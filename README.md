### ZillyonWeb

- Sistema de Gestão para Academias
- Modulos: Administrativo, financeiro, CRM

# Status 

[![pipeline status](https://gitlab.com/Plataformazw/zw/badges/master/pipeline.svg)](https://gitlab.com/Plataformazw/zw/commits/master)

### Como iniciar  o zw com docker

- Pré-requisitos:
> **Docker**: veja os passos de instalação para seu S.O: [Linux Ubuntu](https://docs.docker.com/install/linux/docker-ce/ubuntu/ "Linux Ubuntu"), [Windows](https://docs.docker.com/docker-for-windows/install/ "Windows"), [Mac](https://docs.docker.com/docker-for-mac/install/ "Mac")  [ou veja outras plataformas](https://docs.docker.com/install/ "ou veja outras plataformas")

> Faça login com sua conta gitlab para ter permissão para baixar containers
```console
docker login registry.gitlab.com
```

- Execute
```console
docker-compose up
```

- Você pode acessar aqui: [http://localhost:8081/ZillyonWeb](http://localhost:8081/ZillyonWeb) utilizando a chave ```teste``` usuário ```PACTOBR``` e senha ```123```

### Como executar rotinas "robo" que executam a partir de agendamentos:

> Substitua teste pela chave da empresa

```console
docker-compose exec zw acesso.sh teste
```
```console 
docker-compose exec zw adm.sh teste
```
```console
docker-compose exec zw atualizarbd.sh teste
```
```console
docker-compose exec zw dw.sh teste
```
```console
docker-compose exec zw extrato.sh teste
```
```console
docker-compose exec zw gerenciais.sh teste
```
```console
docker-compose exec zw remessa.sh teste
```
```console
docker-compose exec zw robo.sh teste
```

### Como executar testes:

- Pré-requisitos: 
> **nodejs**: veja os passos de instalação para seu S.O: https://nodejs.org/en/download/

```console
cd test/e2e
npm install
```

## Para executar em modo visual:

```console
cd test/e2e
npm run cy
```

## Para executar com docker em modo de linha de comando:

```console
cd test/e2e
docker-compose up
```

#### Restaurar um banco
```console
docker-compose exec postgres restore.sh 810f691394b9cf79d33459a08861f3ed
```
> Este comando vai restaurar um banco definindo a chave como wpfitness

#### Restaurar uma base de dados definindo o nome da chave

```console
docker-compose exec postgres restore.sh 810f691394b9cf79d33459a08861f3ed true wpfitness
```
> Este comando vai restaurar um banco definindo a chave como wpfitness


#### Qual IP do postgre?

```console
docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' postgres
```

ou 
```console
docker inspect postgres | grep IPAddress
```

