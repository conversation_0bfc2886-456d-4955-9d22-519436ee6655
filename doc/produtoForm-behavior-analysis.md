# Análise Detalhada do Comportamento do produtoForm.jsp

## Visão Geral

O arquivo `produtoForm.jsp` é um formulário JSF complexo para cadastro e edição de produtos, organizado em múltiplas abas com funcionalidades específicas. Este documento detalha cada componente e comportamento para facilitar a migração para um frontend moderno.

## Estrutura Geral

### Tecnologias Utilizadas
- **JSF (JavaServer Faces)** com RichFaces
- **AJAX** para interações dinâmicas
- **JavaScript/jQuery** para validações e máscaras
- **CSS** customizado para estilização

### Organização em Abas
O formulário é organizado em um `rich:tabPanel` com as seguintes abas:
1. **Dados** - Informações básicas do produto
2. **Comissão** - Configurações de comissão
3. **Nota Fiscal** - Configurações fiscais
4. **Vendas Online** - Configurações para vendas online
5. **Mesclagem** - Funcionalidade de mesclagem de produtos
6. **Replicar Empresas** - Replicação entre empresas da rede

## ABA 1: DADOS (Informações Básicas)

### Campos Principais

#### 1. Código do Produto
- **Campo**: `h:inputText` readonly
- **Binding**: `#{ProdutoControle.produtoVO.codigo}`
- **Comportamento**: Somente leitura, gerado automaticamente

#### 2. Importação de Produtos
- **Componente**: `a4j:commandLink`
- **Ação**: Abre popup de importação de produtos
- **Funcionalidade**: Permite importar produtos via arquivo

#### 3. Descrição
- **Campo**: `h:inputText` com 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.descricao}`
- **Validações**: Máximo 50 caracteres
- **Eventos**: `onblur` e `onfocus` para estilização
- **Comportamento**: Desabilitado para "Crédito Personal"

#### 4. Tipo de Produto
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.tipoProduto}`
- **Opções**: Lista dinâmica `#{ProdutoControle.listaSelectItemTipoProdutoProduto}`
- **Eventos AJAX**: `onchange` recarrega o formulário
- **Tipos Disponíveis**:
  - MA (Matrícula)
  - RE (Rematrícula) 
  - RN (Renovação)
  - PE (Produto Estoque)
  - PM (Mês de Referência Plano)
  - SE (Serviço)
  - CD (Convênio de Desconto)
  - TR (Trancamento)
  - RT (Retorno Trancamento)
  - SS (Serviço com Capacidade)
  - DI (Diária)

#### 5. Capacidade (Condicional)
- **Visibilidade**: Apenas para tipo "SS"
- **Campo**: `h:inputText` com 3 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.capacidade}`

#### 6. Unidade de Medida
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.unidadeMedida}`
- **Opções**: Lista dinâmica `#{ProdutoControle.listaSelectItemUnidadeMedida}`
- **Comportamento**: Pode ser desabilitado condicionalmente

#### 7. Código de Barras (Condicional)
- **Visibilidade**: Apenas para tipo "PE" (Produto Estoque)
- **Campo**: `h:inputText` com 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.codigoBarras}`
- **Tooltip**: Dinâmico via `#{ProdutoControle.produtoVO.codigoBarrasTitle}`

#### 8. Status do Produto
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.desativado}`
- **Comportamento**: Indica se o produto está desativado

#### 9. Aparecer Aula Cheia (Condicional)
- **Visibilidade**: Apenas se `#{ProdutoControle.usaAulaCheia}`
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.aparecerAulaCheia}`

#### 10. Categoria do Produto
- **Visibilidade**: Controlada por `#{ProdutoControle.produtoVO.desenhartela}`
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.categoriaProduto.codigo}`
- **Botão Atualizar**: Recarrega lista de categorias

#### 11. Tamanho do Armário (Condicional)
- **Visibilidade**: Apenas se `#{ProdutoControle.produtoVO.armario}`
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.tamanhoArmario.codigo}`

### Campos de Preço e Custo

#### 12. Preço de Custo (Condicional)
- **Visibilidade**: `#{ProdutoControle.apresentarMargemLucroPrecoCusto}`
- **Campo**: `h:inputText` com formatação monetária
- **Binding**: `#{ProdutoControle.produtoVO.precoCusto}`
- **Eventos**: `onkeyup` recalcula valor final

#### 13. Margem de Lucro (Condicional)
- **Campo**: `h:inputText` com formatação monetária
- **Binding**: `#{ProdutoControle.produtoVO.margemLucro}`
- **Formato**: Porcentagem
- **Eventos**: `onkeyup` recalcula valor final

#### 14. Valor do Produto
- **Visibilidade**: `#{ProdutoControle.produtoVO.desenharValorProduto}`
- **Campo**: `h:inputText` com formatação monetária
- **Binding**: `#{ProdutoControle.produtoVO.valorFinal}`
- **Tooltip**: Explicação da unidade de medida

### Campos de Vigência

#### 15. Tipo de Vigência
- **Visibilidade**: `#{ProdutoControle.produtoVO.desenharTipoVigencia}`
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.tipoVigencia}`
- **Eventos**: `onchange` recarrega formulário

#### 16. Data Início Vigência
- **Visibilidade**: `#{ProdutoControle.produtoVO.desenharDataVigencia}`
- **Campo**: `rich:calendar`
- **Binding**: `#{ProdutoControle.produtoVO.dataInicioVigencia}`
- **Formato**: dd/MM/yyyy

#### 17. Data Final Vigência
- **Campo**: `rich:calendar`
- **Binding**: `#{ProdutoControle.produtoVO.dataFinalVigencia}`
- **Formato**: dd/MM/yyyy

#### 18. Número de Dias de Vigência
- **Visibilidade**: `#{ProdutoControle.produtoVO.desenharPeriodoVigencia}`
- **Campo**: `h:inputText` numérico
- **Binding**: `#{ProdutoControle.produtoVO.nrDiasVigencia}`

#### 19. Renovável Automaticamente
- **Visibilidade**: `#{ProdutoControle.produtoVO.podeSerRenovadoAutomatica}`
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.renovavelAutomaticamente}`
- **Eventos**: `onchange` executa validação

### Campos Adicionais

#### 20. Prefixo
- **Campo**: `h:inputText` com 4 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.prefixo}`
- **Formato**: Maiúsculo

#### 21. Bloqueia pela Vigência
- **Visibilidade**: Para tipos SE ou AT com vigência
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.bloqueiaPelaVigencia}`

#### 22. Prevalecer Vigência do Contrato
- **Visibilidade**: Com vigência e não renovável automaticamente
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.prevalecerVigenciaContrato}`

#### 23. Quantidade de Pontos
- **Campo**: `h:inputText` numérico
- **Binding**: `#{ProdutoControle.produtoVO.qtdePontos}`

#### 24. Texto Contrato do Produto
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.contratoTextoPadrao}`
- **Botão Atualizar**: Recarrega lista de textos

### Campos SESI (Condicionais)
- **Visibilidade**: `#{ProdutoControle.apresentarConfiguracoesSesi}`
- **Código Produto Sesi**: `#{ProdutoControle.produtoVO.codigoProdutoSesi}`
- **CR Sesi**: `#{ProdutoControle.produtoVO.cRSesi}`
- **Projeto Sesi**: `#{ProdutoControle.produtoVO.projetoSesi}`
- **Conta Financeira Sesi**: `#{ProdutoControle.produtoVO.contaFinanceiraSesi}`
- **Negócio Sesi**: `#{ProdutoControle.produtoVO.negocioSesi}`

#### 25. Observação
- **Campo**: `h:inputTextarea` 50x3
- **Binding**: `#{ProdutoControle.produtoVO.observacao}`

#### 26. Quantidade de Convites (Condicional)
- **Visibilidade**: Apenas para tipo "DI" (Diária)
- **Campo**: `h:inputText` numérico
- **Binding**: `#{ProdutoControle.produtoVO.qtdConvites}`

### Seção: Configurações para Empresa

#### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.exibirConfiguracoesParaEmpresas}`
- **Propósito**: Permite configurar valores diferentes por empresa

#### Campos de Adição
- **Empresa**: `h:selectOneMenu` com lista de empresas
- **Valor**: `h:inputText` com formatação monetária
- **Botão Adicionar**: Adiciona configuração à lista

#### Tabela de Configurações
- **Colunas**: Empresa, Valor, Opções (Editar/Excluir)
- **Binding**: `#{ProdutoControle.produtoVO.configuracoesEmpresa}`
- **Ações**: Editar e remover configurações

### Seção: Pacotes de Créditos Personal

#### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.creditoPersonal}`
- **Propósito**: Configurar pacotes para crédito personal

#### Campos de Adição
- **Quantidade**: `rich:inputNumberSpinner`
- **Valor Pré-pago**: `h:inputText` monetário
- **Valor Pós-pago**: `h:inputText` monetário

#### Tabela de Pacotes
- **Colunas**: Quantidade, Valor pré-pago, Valor pós-pago, Opções
- **Binding**: `#{ProdutoControle.produtoVO.pacotesPersonal}`

## Comportamentos JavaScript

### Máscaras e Validações
- **Formatação Monetária**: `formatar_moeda(this,'.',',',event)`
- **Apenas Números**: `Tecla(event)` e `somenteNumeros(this)`
- **Validação de Data**: `validar_Data(this.id)`
- **Máscara de Data**: `mask('99/99/9999')`

### Eventos de Foco
- **focusinput(this)**: Estilização ao focar campo
- **blurinput(this)**: Estilização ao sair do campo

### Tooltips
- **jQuery Tooltipster**: Tooltips informativos
- **Configuração**: Tema light, posição bottom, animação grow

## Validações e Regras de Negócio

### Validações Automáticas
1. **Tipo de Produto**: Altera campos visíveis
2. **Vigência**: Valida datas e períodos
3. **Renovação Automática**: Valida compatibilidade
4. **Valores**: Recalcula baseado em custo e margem

### Regras Condicionais
1. **Capacidade**: Apenas para serviços (SS)
2. **Código de Barras**: Apenas para produtos de estoque (PE)
3. **Convites**: Apenas para diárias (DI)
4. **Configurações SESI**: Baseado em configuração da empresa

## Ações AJAX

### Recarregamento de Formulário
- **Trigger**: Mudança de tipo de produto, vigência
- **Comportamento**: Recarrega campos condicionais

### Atualização de Listas
- **Categorias**: Botão atualizar recarrega combo
- **Textos de Contrato**: Botão atualizar recarrega combo
- **Tamanhos de Armário**: Botão atualizar recarrega combo

### Cálculos Automáticos
- **Valor Final**: Baseado em custo + margem
- **Margem**: Baseada em valor final - custo

## Botões de Ação Principal

### Botões Disponíveis
1. **Novo**: Limpa formulário para novo produto
2. **Gravar**: Salva produto (criar/atualizar)
3. **Excluir**: Remove produto (com confirmação)
4. **Voltar Lista**: Retorna à consulta
5. **Visualizar Log**: Abre popup com histórico

### Teclas de Atalho
- **Alt+1**: Novo
- **Alt+2**: Gravar  
- **Alt+3**: Excluir
- **Alt+4**: Voltar Lista
- **Alt+7**: Adicionar (em seções específicas)

## Mensagens e Feedback

### Sistema de Mensagens
- **Sucesso**: Ícone verde + mensagem
- **Erro**: Ícone vermelho + mensagem
- **Detalhada**: Mensagem adicional com detalhes

### Binding de Mensagens
- **Sucesso**: `#{ProdutoControle.sucesso}`
- **Erro**: `#{ProdutoControle.erro}`
- **Mensagem**: `#{ProdutoControle.mensagem}`
- **Detalhada**: `#{ProdutoControle.mensagemDetalhada}`

## Considerações para Migração

### Funcionalidades Críticas
1. **Validações em Tempo Real**: Replicar validações JavaScript
2. **Campos Condicionais**: Implementar lógica de visibilidade
3. **Formatação Monetária**: Manter formatação de valores
4. **Máscaras de Input**: Implementar máscaras equivalentes
5. **Tooltips**: Manter sistema de ajuda contextual

### Integrações Necessárias
1. **Upload de Arquivos**: Para importação de produtos
2. **Autocomplete**: Para busca de produtos na mesclagem
3. **Calendários**: Para seleção de datas
4. **Spinners**: Para campos numéricos

### Estados de Interface
1. **Loading**: Durante operações AJAX
2. **Disabled**: Para campos condicionalmente desabilitados
3. **Readonly**: Para campos não editáveis
4. **Required**: Para campos obrigatórios

## ABA 2: COMISSÃO

### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.apresentarAbaComissao}`
- **Propósito**: Configurar comissões por produto e período

### Campos de Configuração

#### 1. Empresa (Condicional)
- **Visibilidade**: `#{ProdutoControle.apresentarEmpresaComissao}`
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.comissaoVO.empresa.codigo}`

#### 2. Vigência Inicial
- **Campo**: `rich:calendar`
- **Binding**: `#{ProdutoControle.comissaoVO.vigenciaInicio}`
- **Formato**: MM/yyyy (mês/ano)

#### 3. Vigência Final
- **Campo**: `rich:calendar`
- **Binding**: `#{ProdutoControle.comissaoVO.vigenciaFinal}`
- **Formato**: MM/yyyy (mês/ano)

#### 4. Porcentagem
- **Campo**: `h:inputText` numérico
- **Binding**: `#{ProdutoControle.comissaoVO.porcentagem}`
- **Formato**: Decimal com formatador numérico

#### 5. Valor Fixo
- **Campo**: `h:inputText` numérico
- **Binding**: `#{ProdutoControle.comissaoVO.valorFixo}`
- **Formato**: Decimal com formatador numérico

### Tabela de Comissões
- **Binding**: `#{ProdutoControle.produtoVO.comissaoProdutos}`
- **Colunas**: Código, Empresa, Vigência Inicial, Vigência Final, Porcentagem, Valor Fixo, Opções
- **Ações**: Editar e Remover comissões

## ABA 3: NOTA FISCAL

### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.exibirConfiguracoesNFSe || ProdutoControle.exibirConfiguracoesNFCe}`
- **Propósito**: Configurações fiscais para emissão de notas

### Seção NFS-e
- **Visibilidade**: `#{ProdutoControle.exibirConfiguracoesNFSe}`
- **Configuração Emissão**: `#{ProdutoControle.produtoVO.configuracaoNotaFiscalNFSe.codigo}`

### Seção NFC-e
- **Visibilidade**: `#{ProdutoControle.exibirConfiguracoesNFCe}`
- **Configuração Emissão**: `#{ProdutoControle.produtoVO.configuracaoNotaFiscalNFCe.codigo}`

### Campos Fiscais Gerais

#### 1. CFOP
- **Campo**: `h:inputText` 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.cfop}`

#### 2. NCM (NFS-e)
- **Visibilidade**: `#{ProdutoControle.exibirConfiguracoesNFSe}`
- **Campo**: `h:inputText` 10 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.ncm}`

#### 3. NCM NFC-e
- **Visibilidade**: `#{ProdutoControle.exibirConfiguracoesNFCe}`
- **Campo**: `h:inputText` 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.ncmNFCe}`

#### 4. CEST
- **Campo**: `h:inputText` 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.cest}`

#### 5. Código Lista Serviço
- **Campo**: `h:inputText` 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.codigoListaServico}`

#### 6. Código Tributação Município
- **Campo**: `h:inputText` 50 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.codigoTributacaoMunicipio}`

#### 7. Descrição Serviço Município
- **Campo**: `h:inputText` 200 caracteres
- **Binding**: `#{ProdutoControle.produtoVO.descricaoServicoMunicipio}`

### Percentuais de Impostos (Condicional)

#### Controle de Exibição
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.enviarPercentualImposto}`
- **Comportamento**: Controla visibilidade dos campos de percentual

#### Campos de Percentual (se habilitado)
1. **Percentual Federal**: `#{ProdutoControle.produtoVO.percentualFederal}`
2. **Percentual Estadual**: `#{ProdutoControle.produtoVO.percentualEstadual}`
3. **Percentual Municipal**: `#{ProdutoControle.produtoVO.percentualMunicipal}`

### Seção ICMS
- **Visibilidade**: `#{ProdutoControle.produtoVO.produtoApresentarIcms}`

#### Campos ICMS
1. **Situação Tributária**: `#{ProdutoControle.produtoVO.situacaoTributariaICMS}`
2. **Isento ICMS**: `#{ProdutoControle.produtoVO.isentoICMS}`
3. **Enviar Alíquota NFe**: `#{ProdutoControle.produtoVO.enviaAliquotaNFeICMS}`
4. **Alíquota ICMS**: `#{ProdutoControle.produtoVO.aliquotaICMS}`

### Seção CBNEF
- **Visibilidade**: `#{ProdutoControle.produtoVO.produtoEstoque}`
- **Código Benefício Fiscal**: `#{ProdutoControle.produtoVO.codigoBeneficioFiscal}`

### Seção ISSQN
- **Visibilidade**: `#{!ProdutoControle.produtoVO.produtoEstoque}`

#### Campos ISSQN
1. **Situação Tributária**: `#{ProdutoControle.produtoVO.situacaoTributariaISSQN}`
2. **Alíquota ISSQN**: `#{ProdutoControle.produtoVO.aliquotaISSQN}`

### Seção PIS

#### Campos PIS
1. **Situação Tributária**: `#{ProdutoControle.produtoVO.situacaoTributariaPIS}`
2. **Isento PIS**: `#{ProdutoControle.produtoVO.isentoPIS}`
3. **Enviar Alíquota NFe**: `#{ProdutoControle.produtoVO.enviaAliquotaNFePIS}`
4. **Alíquota PIS**: `#{ProdutoControle.produtoVO.aliquotaPIS}`

### Seção COFINS

#### Campos COFINS
1. **Situação Tributária**: `#{ProdutoControle.produtoVO.situacaoTributariaCOFINS}`
2. **Isento COFINS**: `#{ProdutoControle.produtoVO.isentoCOFINS}`
3. **Enviar Alíquota NFe**: `#{ProdutoControle.produtoVO.enviaAliquotaNFeCOFINS}`
4. **Alíquota COFINS**: `#{ProdutoControle.produtoVO.aliquotaCOFINS}`

## ABA 4: VENDAS ONLINE

### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.exibirAbaVendasOnline && LoginControle.apresentarVendas && LoginControle.permissaoAcessoMenuVO.vendasOnline}`
- **Propósito**: Configurações para vendas online e aplicativos

### Campos de Configuração

#### 1. Apresentar no Pacto App
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.apresentarPactoApp}`

#### 2. Apresentar no Vendas Online
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.apresentarVendasOnline}`
- **Eventos**: `onchange` recarrega formulário

#### 3. Apresentar no Pacto Flow
- **Campo**: `h:selectBooleanCheckbox`
- **Binding**: `#{ProdutoControle.produtoVO.apresentarPactoFlow}`

#### 4. Quantidade Máxima de Parcelas (Condicional)
- **Visibilidade**: `#{ProdutoControle.produtoVO.apresentarVendasOnline}`
- **Campo**: `h:inputText` numérico
- **Binding**: `#{ProdutoControle.produtoVO.maxDivisao}`
- **Máscara**: Apenas números
- **Observação**: "À vista" = 1

#### 5. Modalidade (Condicional)
- **Visibilidade**: `#{ProdutoControle.produtoVO.apresentarVendasOnline and ProdutoControle.diaria}`
- **Campo**: `h:selectOneMenu`
- **Binding**: `#{ProdutoControle.produtoVO.modalidadeVendasOnline}`

### Seção de Imagens

#### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.produtoVO.apresentarVendasOnline}`
- **Propósito**: Upload e gerenciamento de imagens do produto

#### Upload de Imagens
- **Componente**: `rich:fileUpload`
- **Tipos Aceitos**: png, gif, jpg, jpeg, ico, bmp
- **Tamanho Máximo**: 10 MB
- **Quantidade**: 1 arquivo por vez
- **Resolução Recomendada**: 250x200 pixels

#### Galeria de Imagens
- **Binding**: `#{ProdutoControle.listaImagens}`
- **Exibição**: Grid de imagens com preview
- **Ações**: Remover imagem (ícone lixeira)
- **Comportamento**: Atualização automática após upload/remoção

## ABA 5: MESCLAGEM

### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.getProdutoVO().getCodigo()!=null && ProdutoControle.getProdutoVO().getCodigo()>0}`
- **Propósito**: Mesclar produtos relacionados

### Campo de Busca

#### Produto para Mesclagem
- **Campo**: `h:inputText` com autocomplete
- **Binding**: `#{ProdutoControle.produtoMescladoSelecionado}`
- **Autocomplete**: `rich:suggestionbox`
- **Busca**: Por código ou nome do produto
- **Colunas Sugestão**: Nome, Categoria

### Tabela de Produtos Mesclados
- **Binding**: `#{ProdutoControle.listaProdutoMescladoVO}`
- **Colunas**: Produto, Valor, Estoque, Opções
- **Ações**: Remover produto da mesclagem
- **Botão Salvar**: Confirma mesclagem dos produtos

### Mensagem de Atenção
- **Texto**: `#{msg.msg_atencao_mesclagem}`
- **Estilo**: Mensagem de aviso pequena

## ABA 6: REPLICAR EMPRESAS

### Funcionalidade
- **Visibilidade**: `#{ProdutoControle.exibirReplicarRedeEmpresa}`
- **Propósito**: Replicar produto entre empresas da rede

### Contador de Status
- **Unidades**: Total de empresas na rede
- **Replicadas**: Empresas que já possuem o produto
- **Não Replicadas**: Empresas que ainda não possuem o produto

### Botões de Ação
1. **Replicar Todas**: Replica para todas as empresas
2. **Replicar Selecionadas**: Replica apenas para empresas marcadas
3. **Limpar Selecionadas**: Desmarca todas as seleções

### Tabela de Empresas
- **Binding**: `#{ProdutoControle.listaProdutoRedeEmpresa}`
- **Colunas**:
  - Checkbox (para seleção)
  - Nome da Unidade
  - Chave da Empresa
  - Botão Replicar Individual
  - Mensagem de Situação
  - Botão Retirar Vínculo

### Estados de Replicação
- **Não Replicado**: Checkbox e botão "Replicar" disponíveis
- **Replicado**: Ícone de check e botão "Retirar" disponíveis

Este documento serve como base completa para implementar um frontend moderno que replique fielmente o comportamento do formulário JSF original, incluindo todas as abas e funcionalidades específicas.
