# =================== DEPENDENCIES ===================
FROM registry.gitlab.com/plataformazw/docker-pacto/maven:3-jdk-8 AS dependencies

WORKDIR /app

COPY pom.xml .
# COPY docker/mvn/settings.xml /root/.m2/settings.xml  # se tiver config custom

RUN mvn dependency:go-offline


# =================== BUILD ===================
FROM dependencies AS build

COPY . .

RUN mvn clean package -P docker -DskipTests


# =================== RUNTIME ===================
FROM openjdk:8-slim

WORKDIR /app

# Instala cron
RUN apt-get update && apt-get install -y cron \
    && rm -rf /var/lib/apt/lists/*

# Define diretório do app
ENV APP_DIR="/usr/local/tomcat/webapps/ZillyonWeb"

# Copia app do build
COPY --from=build /app/target/ZillyonWeb $APP_DIR
COPY target/ZillyonWeb.jar .

# Remove libs específicas (unifica o comando)
RUN rm -rf $APP_DIR/WEB-INF/lib/*jdk14-1.38.jar $APP_DIR/WEB-INF/lib/*jdk14-138.jar

# Cria diretórios e adiciona scripts
RUN mkdir -p /opt/robo /var/log/robo

COPY docker/tomcat/crontab/robo/*.sh /opt/robo/
COPY docker/tomcat/crontab/crontab /etc/crontab
COPY docker/tomcat/keys/* /root/.ssh/
COPY docker/springboot/bin/* /bin/

RUN chmod +x /opt/robo/*.sh /bin/*.sh && ln -s /opt/robo/*.sh /usr/local/bin

# =================== ENV ===================
ENV JAVA_OPTS="-Xms128m -Xmx1g -Xss256k -Duser.timezone=America/Sao_Paulo -Duser.language=pt -Duser.region=BR -Dpacto.service=zw"

ENV CONTEXT="ZillyonWeb"
ENV URL_DATABASE_OAMD="************************************"
ENV DEBUG_JDBC="false"
ENV DISCOVERY_URL="http://dev.pactosolucoes.com.br:8087"

ENV URL_HTTPS_PLATAFORMA_PACTO="https://dev.pactosolucoes.com.br:8094"
ENV URL_HTTP_PLATAFORMA_PACTO="http://dev.pactosolucoes.com.br:8094"
ENV URL_OAMD="http://app.pactosolucoes.com.br/oamd"
ENV URL_OAMD_SEGURA="http://app.pactosolucoes.com.br/oamd"
ENV SMTP_EMAIL_ROBO="<EMAIL>"
ENV SMTP_EMAIL_NOREPLY="<EMAIL>"
ENV SMTP_LOGIN_ROBO="pactosolucoes2017"
# RECOMENDADO: Passar a senha via secret, não no Dockerfile
ENV SMTP_SENHA_ROBO="RlOHgUGw8905"
ENV SMTP_SERVER_ROBO="smtplw.com.br"
ENV SMTP_SERVER_ROBO_CONEXAO_SEGURA="true"
ENV SMTP_SERVER_ROBO_INICIAR_TLS="true"
ENV URL_VENDAS_ONLINE="http://devi9.pactosolucoes.com.br:8189"
ENV URL_BASE_OPTIN="https://vendas.online.sistemapacto.com.br"
ENV MY_URL_UP_BASE="https://app.pactosolucoes.com.br/ucp"
ENV URL_JENKINS="http://devzw.pactosolucoes.com.br:8782/jk"
ENV URL_MAILING="http://devzw.pactosolucoes.com.br:8781/app/ms"
ENV URL_APLICACAO="http://zw:8481/ZillyonWeb"
ENV URL_NOTIFICAR="http://zw:8481/ZillyonWeb"
ENV URL_ZW_AUTO="http://swarm3.pactosolucoes.com.br:8080/zw-auto"
ENV URL_RECURSO_EMPRESA="http://dev.pactosolucoes.com.br:8202"
ENV USAR_URL_RECURSO_EMPRESA="false"
ENV HABILITAR_NICHO="true"
ENV HABILITAR_CACHE_INIT_NICHO="false"
ENV VALIDADE_CACHE_NICHO_EM_MINUTOS="2880"
ENV URL_API="http://swarm3.pactosolucoes.com.br:8010/API-ZillyonWeb"
ENV URL_BI_MS="http://bi-ms:28091/bi-ms"
ENV URL_WEBHOOK_DISCORD_ENOTAS_HABILITAR="false"
ENV URL_SERV_NOTA_FISCAL="http://naoTemHomologacao/servicoNotaFiscal"
ENV ENABLE_MENU_ZW_UI="false"
ENV TOKENS_ACESSO_API_CLIENTE="gP6pV2pS6lC8sY7nH6vG8tN4xT0vR9tU,teste,1"
ENV PERMITIR_ENVIO_NOTIFICACAO_PUSH_AULA="true"
ENV TOKEN_MAIL_GUN="Xzhjdnmdendumdh"
ENV HABILITA_MARKETING="false"
ENV HABILITA_CLUBE_DE_BENEFICIOS="false"
ENV TEMPO_SEGUNDOS_EXPIRAR_CACHE_BANNERS="432000"
ENV SERVIDOR_MEMCACHED="DISABLED"
ENV URL_MARKETING_MS="https://ms1.pactosolucoes.com.br/marketing-ms"
ENV DOMAIN_MAIL="teste.con.br"
ENV VALIDAR_VERSAO_BD="true"
ENV UTEIS_EMAIL_SEND="true"
ENV ZAW_URL_NOTF_ACESSO="http://zw:8080/ZillyonWeb/UpdateServlet"
ENV CANCELAR_BOLETO_AO_CANCELAR_OU_ESTORNAR_CONTRATO="false"
ENV TIPO_MIDIA="ZW_INTERNAL"
ENV URL_FOTOS_NUVEM="zw-photos"
ENV TEMPO_SEGUNDOS_EXPIRACAO_TOKEN_OPERACAO="60"
ENV HABILITAR_FUNCIONALIDADES_BETA="true"

#Aplication.properties
ENV SERVER_PORT=${SERVER_PORT:-"8080"}
ENV SERVER_CONTEXT_PATH=${SERVER_CONTEXT_PATH:-"/"}
ENV AUTH_SECRET_PATH=${AUTH_SECRET_PATH:-"/root/.ssh/auth-secrect"}
ENV AUTH_SECRET_ZW_PATH=${AUTH_SECRET_ZW_PATH:-"/root/.ssh/auth-secrect"}
ENV AUTH_SECRET_PERSONA_PATH=${AUTH_SECRET_PERSONA_PATH:-"/root/.ssh/auth-secrect-persona"}


COPY src/main/resources/application.properties /app/application.properties
COPY target/ZillyonWeb/WEB-INF/classes/application.properties /app/application.build.properties
COPY target/ZillyonWeb/WEB-INF/classes/servicos/propriedades/SuperControle.properties /opt/ZW_ARQ/props.override

# =================== ENTRY ===================
ENTRYPOINT ["bash", "/bin/entrypoint.sh"]
