package controle.acesso.coletor;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.enumerador.ModeloColetorEnum;

public abstract class ValidaColetorImpl implements IValidaColetor {


    public void validar(ColetorVO coletor) throws Exception {
        if (coletor.getModelo() != ModeloColetorEnum.MODELO_LEITOR_NITGEN &&
                coletor.getModelo() != ModeloColetorEnum.MODELO_COLETOR_DESCONHECIDO &&
                coletor.getModelo() != ModeloColetorEnum.MODELO_CAMERA_ACIONAMENTO &&
                coletor.getSensorSaida() == coletor.getSensorEntrada()) {
            throw new Exception("Sensor de saída e entrada não podem ter valores equivalentes.("+coletor.getModelo().getDescricao()+")");
        }
        if (coletor.getModelo() != ModeloColetorEnum.MODELO_LEITOR_NITGEN &&
                coletor.getModelo() != ModeloColetorEnum.MODELO_COLETOR_DESCONHECIDO &&
                coletor.getModelo() != ModeloColetorEnum.MODELO_CAMERA_ACIONAMENTO &&
                coletor.getReleSaida() == coletor.getReleEntrada()) {
            throw new Exception("Rele de saída e entrada não podem ter valores equivalentes.("+coletor.getModelo().getDescricao()+")");
        }
        if (coletor.getSensorEntrada() != null && coletor.getSensorEntrada() == -1) {
            throw new Exception("Sensor de entrada não pode assumir o valor -1.("+coletor.getModelo().getDescricao()+")");
        }

        valida(coletor);
    }
}
