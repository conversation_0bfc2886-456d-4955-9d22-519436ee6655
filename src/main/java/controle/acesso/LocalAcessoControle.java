/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.acesso;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.acesso.coletor.ValidaColetorFactory;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import controle.plano.ProdutoControle;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.TipoHorarioEnum;
import negocio.comuns.acesso.TipoValidacaoEnum;
import negocio.comuns.acesso.ValidacaoLocalAcessoVO;
import negocio.comuns.acesso.enumerador.CategoriaLocalAcessoEnum;
import negocio.comuns.acesso.enumerador.DispositivoAlternativoEnum;
import negocio.comuns.acesso.enumerador.ModeloColetorEnum;
import negocio.comuns.acesso.enumerador.ModoTransmissaoEnum;
import negocio.comuns.acesso.enumerador.SentidoAcessoEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeEmpresaVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class LocalAcessoControle extends SuperControle {

    private String NOME_ENTIDADE = "LOCALACESSO";
    private LocalAcessoVO localAcesso;
    private ColetorVO coletor;
    private ValidacaoLocalAcessoVO validacao;
    protected List listaSelectItemModeloColetor;
    protected List listaSelectItemTipoComunicacao;
    protected List listaSelectItemModoTransmissao;
    protected List listaSelectItemSentidoAcesso;
    protected List<SelectItem> listaSelectItemEmpresa = new ArrayList();
    private List<SelectItem> listaSelectItemCategoriaLocalAcesso;
    private Boolean mostrarEsconderFormColetor = false;
    private Boolean mostrarEsconderFormValidacao = false;
    private List listaPortaCom;
    private List listaPortaComSerial;
    private List listaPortaGertec;
    // Atributos para a tela de cadastro de validações
    private List listaSelectItemTipoValidacao;
    private List listaSelectItemTipoHorario = new ArrayList();
    private List<SelectItem> listaSelectItemHorario = new ArrayList();
    private int codigoTipoValidacao = 0;
    private int codigoTipoHorario = 0;
    // Atributos para mostrar após ter escolhido o tipo de validação
    private List<SelectItem> listaSelectItemModalidade = new ArrayList();
    private List<SelectItem> listaSelectItemProdutoGymPass = new ArrayList();
    private String nomeTipoValidacaoEscolhida = "";
    // Atributos para richModal de pesquisa de Coletores.
    private String nomeColetorEscolhido = "";
    private Boolean mostrarRichModalPesqColetor = false;
    // Atributo para explicar ao usuário como o sistema irá realizar a validação da regra de "Validação de Acesso".
    private String explicacaoRegra = "";
    //Tipos de Dispositivos: Codigo de Barras, Aproximacao, MiFare, etc
    private List<SelectItem> listaSelectItemDispAlternativos =
            JSFUtilities.getSelectItemListFromEnum(DispositivoAlternativoEnum.class,
            "descricao", false);
    private Boolean mostrarBotaoGerarDadosOff = false;
    private String descricaoProcessoRealizado;
    private List<SelectItem> itemsReleEntrada;
    private List<SelectItem> itemsReleSaida;
    private List<SelectItem> itemsSensorEntrada;
    private List<SelectItem> itemsSensorSaida;
    private Boolean utilizarMatriculaComoSenha;
    private ModeloColetorEnum modeloColetorIntegraFacil = ModeloColetorEnum.MODELO_COLETOR_INTEGRA_FACIL;
    private String msgAlert;

    public LocalAcessoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        inicializarEmpresaLogado();
        montarListaSelectItemEmpresa();
        inicializarUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
        validacao = new ValidacaoLocalAcessoVO();
    }

    private String retornarDescricaoItemLista(Integer codigo, List<SelectItem> lista) {
        for (SelectItem obj : lista) {
            if (codigo.intValue() == ((Integer) obj.getValue()).intValue()) {
                return obj.getLabel();
            }
        }
        return "";
    }

    private String retornarTextoExplicativoDaRegra() {
        StringBuilder sb = new StringBuilder();
        sb.append("Validação: ");

        // Montar descrição inicial para todas as validações.
        sb.append("Todos os clientes cadastrados na empresa \"");
        sb.append(retornarDescricaoItemLista(validacao.getEmpresa().getCodigo(), listaSelectItemEmpresa));
        sb.append("\", e que tenham permissão de acesso, ");
        // Selecionou tipo validação:Todos.
        if (this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Todos.getId().intValue()) {
            sb.append("pode passar por esta catraca, independente do dia e horário se não tiver contrato; ");
            sb.append("Caso o aluno tenha um contrato, para passar pela catraca, tem que está no horário do Plano ou no horário da Turma.");
            return sb.toString();
        }
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_Todos.getId().intValue())) {
            sb.append("e tiver adquirido o passe gym pass com o produto \"");
            sb.append(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemProdutoGymPass));
            sb.append("\" estando dentro da validade, pode passar por esta catraca, independente do dia e horário, respeitando o período de acesso do token.");
            return sb.toString();
        }
        // Gympass com acesso restrito a horarios especificos
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico.getId().intValue())) {
            sb.append("e tiver adquirido o passe gym pass com o produto \"");
            sb.append(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemProdutoGymPass));
            sb.append("\", pode passar por esta catraca, somente no horário ");
            sb.append(retornarDescricaoItemLista(validacao.getHorario().getCodigo(), listaSelectItemHorario));
            return sb.toString();
        }
        // Selecionou somente empresa e Tipo Validação igual a Modalidade.
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_Todos.getId().intValue())) {
            sb.append("e estiverem matriculados na modalidade \"");
            sb.append(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemModalidade));
            sb.append("\", pode passar por esta catraca, independente do dia e horário.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Modalidade e Tipo Horário:Horario Contrato
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioContrato.getId().intValue())) {
            sb.append("e estiverem matriculados na modalidade \"");
            sb.append(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemModalidade));
            sb.append("\", pode passar por esta catraca, somente no horário do Plano ou no horário da Turma.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Modalidade e Tipo Horário:Horario Turma
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioTurma.getId().intValue())) {
            sb.append("e estiverem matriculados na modalidade \"");
            sb.append(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemModalidade));
            sb.append("\", pode passar por esta catraca, somente no horário da Turma em que está matriculado.");
            return sb.toString();
        }
        // Selecionou empresa e Tipo Validação:Modalidade e Tipo Horário:Horario Específico
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico.getId().intValue())) {
            sb.append("e estiverem matriculados na modalidade \"");
            sb.append(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemModalidade));
            sb.append("\", pode passar por esta catraca, somente no horário \"");
            sb.append(retornarDescricaoItemLista(validacao.getHorario().getCodigo(), listaSelectItemHorario));
            sb.append("\".");
            return sb.toString();
        }
        // Selecionou somente empresa e Tipo Validação igual a Produto.
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_Todos.getId().intValue())) {
            sb.append("e tiver comprado o produto \"");
            sb.append(validacao.getProduto().getDescricao());
            sb.append("\" estando dentro da validade, pode passar por esta catraca, independente do dia e horário.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Produto e Tipo Horário:Horario Contrato
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioContrato.getId().intValue())) {
            sb.append("e tiver comprado o produto \"");
            sb.append(validacao.getProduto().getDescricao());
            sb.append("\" estando dentro da validade, pode passar por esta catraca, somente no horário do Plano ou no horário da Turma.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Produto e Tipo Horário:Horario Turma
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioTurma.getId().intValue())) {
            sb.append("e tiver comprado o produto \"");
            sb.append(validacao.getProduto().getDescricao());
            sb.append("\" estando dentro da validade, pode passar por esta catraca, somente no horário da Turma em que está matriculado.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Produto e Tipo Horário:Horario Específico
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico.getId().intValue())) {
            sb.append("e tiver comprado o produto \"");
            sb.append(validacao.getProduto().getDescricao());
            sb.append("\" estando dentro da validade, pode passar por esta catraca, somente no horário \"");
            sb.append(retornarDescricaoItemLista(validacao.getHorario().getCodigo(), listaSelectItemHorario));
            sb.append("\".");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Horario e Tipo Horário:Horario Contrato
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Horario.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioContrato.getId().intValue())) {
            sb.append("\", pode passar por esta catraca, somente no horário do Plano ou no horário da Turma.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Horario e Tipo Horário:Horario Turma
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Horario.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioTurma.getId().intValue())) {
            sb.append("\", pode passar por esta catraca, somente no horário da Turma em que está matriculado.");
            return sb.toString();
        }
        // Selecionou empresa, Tipo Validação:Horario e Tipo Horário:Horario Específico
        if ((this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Horario.getId().intValue())
                && (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico.getId().intValue())) {
            sb.append("pode passar por esta catraca, somente no horário \"");
            sb.append(retornarDescricaoItemLista(validacao.getHorario().getCodigo(), listaSelectItemHorario));
            sb.append("\".");
            return sb.toString();
        }

        return sb.toString();
    }

    public String getExplicacaoRegra() {
        setExplicacaoRegra(retornarTextoExplicativoDaRegra());
        return explicacaoRegra;
    }

    public void setExplicacaoRegra(String explicacaoRegra) {
        this.explicacaoRegra = explicacaoRegra;
    }

    public Boolean getMostrarRichModalPesqColetor() {
        return mostrarRichModalPesqColetor;
    }

    public void setMostrarRichModalPesqColetor(Boolean mostrarRichModalPesqColetor) {
        this.mostrarRichModalPesqColetor = mostrarRichModalPesqColetor;
    }

    public String getNomeColetorEscolhido() {
        return nomeColetorEscolhido;
    }

    public void setNomeColetorEscolhido(String nomeColetorEscolhido) {
        this.nomeColetorEscolhido = nomeColetorEscolhido;
    }

    public String getNomeTipoValidacaoEscolhida() {
        return nomeTipoValidacaoEscolhida;
    }

    public void setNomeTipoValidacaoEscolhida(String nomeTipoValidacaoEscolhida) {
        this.nomeTipoValidacaoEscolhida = nomeTipoValidacaoEscolhida;
    }

    public Integer getCodigoTipoHorario() {
        return codigoTipoHorario;
    }

    public void setCodigoTipoHorario(Integer codigoTipoHorario) {
        this.codigoTipoHorario = codigoTipoHorario;
    }

    public int getCodigoTipoValidacao() {
        return codigoTipoValidacao;
    }

    public void setCodigoTipoValidacao(int codigoTipoValidacao) {
        this.codigoTipoValidacao = codigoTipoValidacao;
    }

    public ValidacaoLocalAcessoVO getValidacao() {
        return validacao;
    }

    public void setValidacao(ValidacaoLocalAcessoVO validacao) {
        this.validacao = validacao;
    }

    public Boolean getMostrarEsconderFormValidacao() {
        return mostrarEsconderFormValidacao;
    }

    public void setMostrarEsconderFormValidacao(Boolean mostrarEsconderFormValidacao) {
        this.mostrarEsconderFormValidacao = mostrarEsconderFormValidacao;
    }

    public Boolean getMostrarEsconderFormColetor() {
        return mostrarEsconderFormColetor;
    }

    public void setMostrarEsconderFormColetor(Boolean mostrarEsconderFormColetor) {
        this.mostrarEsconderFormColetor = mostrarEsconderFormColetor;
    }

    public void inicializarUsuarioLogado() {
        try {
            localAcesso.setUsuarioVO(getUsuarioLogado());
        } catch (Exception exception) {
        }
    }

    public void inicializarEmpresaLogado() {
        try {
            setLocalAcesso(new LocalAcessoVO());
            localAcesso.setEmpresa(getEmpresaLogado());
        } catch (Exception exception) {
            System.out.println("Erro LocalAcessoControle.inicializarEmpresaLogado() - " + exception.getMessage());
        }
    }

    public String novo() {
        try {
            setMostrarEsconderFormColetor(false);
            setMostrarEsconderFormValidacao(false);
            inicializarEmpresaLogado();
            inicializarUsuarioLogado();
            getFacade().getLocalAcesso().novo();
            getLocalAcesso().registrarObjetoVOAntesDaAlteracao();
            limparMsg();
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "";
    }

    public void setarValoresDefaultColetor() throws Exception {
        try {
//            pegarColetorDaTela();
            montarTiposEntradaSaida();
            coletor.inicializarDadosDefault();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inverterGiroCatraca() throws Exception {
        try {
            int releEntrada = getColetor().getReleEntrada();
            int sensorEntrada = getColetor().getSensorEntrada();
            getColetor().setReleEntrada(getColetor().getReleSaida());
            getColetor().setSensorEntrada(getColetor().getSensorSaida());
            getColetor().setReleSaida(releEntrada);
            getColetor().setSensorSaida(sensorEntrada);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void adicionarColetor() throws Exception {
        try {
            setMostrarEsconderFormColetor(true);
            setMostrarEsconderFormValidacao(false);
            coletor = new ColetorVO();
            coletor.setLocalAcesso(localAcesso.getCodigo());
            getLocalAcesso().getListaColetores().add(this.getColetor());
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void atualizarDescricaoValidacao() {
        // Atualizar as descrições das validações de (Produto, Modalidade e Horário)
        // Isto é necessário para mostrar a descrição das validações na tela, após alterar uma regra.
        switch (validacao.getTipoValidacao()) {
            case TIPOVALIDACAO_Modalidade:
                validacao.getModalidade().setNome(retornarDescricaoItemLista(validacao.getChave(), listaSelectItemModalidade));
            case TIPOVALIDACAO_Horario: {
                if (validacao.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico) {
                    validacao.getHorario().setDescricao(retornarDescricaoItemLista(validacao.getHorario().getCodigo(), listaSelectItemHorario));
                }
            }
        }
    }

    private void gravarValidacaoEmMemoria() {
        try {
            setMostrarEsconderFormValidacao(true);
            if (validacao != null) {
                if (validacao.getUsuario().getCodigo() <= 0) {
                    validacao.setUsuario(getUsuarioLogado());
                }
                validacao.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(validacao.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                validacao.setTipoHorario(TipoHorarioEnum.valueOf(codigoTipoHorario));
                validacao.setTipoValidacao(TipoValidacaoEnum.valueOf(codigoTipoValidacao));
                if (codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId()) {
                    if (ProdutoControle.selecionouProdutoNoSuggestionBox()) {
                        validacao.setProduto(ProdutoControle.pegarProdutoSelecionadoSuggestionBox());
                        validacao.setChave(validacao.getProduto().getCodigo());
                    } else {
                        validacao.setChave(0);
                    }
                }
                atualizarDescricaoValidacao();
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = localAcesso.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(NOME_ENTIDADE, localAcesso.getCodigo(), null);
    }

    public void adicionarValidacao() throws Exception {
        try {
            gravarValidacaoEmMemoria();
            ProdutoControle.limparSuggestionBoxProduto();
            this.codigoTipoValidacao = 0;
            this.codigoTipoHorario = 0;
            validacao = new ValidacaoLocalAcessoVO();
            coletor.getPermissoes().add(validacao);
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void pegarColetorDaTela() throws Exception {
        ColetorVO obj = (ColetorVO) context().getExternalContext().getRequestMap().get("coletor");
        setColetor(obj);
        setMostrarEsconderFormColetor(true);
        montarTiposEntradaSaida();
    }

    public void editarColetor() throws Exception {
        pegarColetorDaTela();
    }

    public void editarValidacao() throws Exception {
        // Atualiza os dados da validação que foi alterada, antes de clicar no botão editar.
        gravarValidacaoEmMemoria();

        ValidacaoLocalAcessoVO obj = (ValidacaoLocalAcessoVO) context().getExternalContext().getRequestMap().get("validacao");
        setValidacao(obj);
        codigoTipoHorario = validacao.getTipoHorario().getId();
        codigoTipoValidacao = validacao.getTipoValidacao().getId();
        montarListaModalidadeOuProduto();
        if ((obj.getTipoValidacao() == TipoValidacaoEnum.TIPOVALIDACAO_Produto) && (obj.getChave() > 0)) {
            ProdutoControle.setProdutoSuggestionBox(obj.getProduto());
        }
        if (obj.getTipoHorario() == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico) {
            montarListaHorario();
        }
    }

    public void removerColetor() throws Exception {
        setMostrarEsconderFormColetor(false);
        ColetorVO obj = (ColetorVO) context().getExternalContext().getRequestMap().get("coletor");
        getLocalAcesso().excluirObjColetorVOs(obj);
        setMensagemID("msg_dados_excluidos");
    }

    public void removerValidacao() throws Exception {
        setMostrarEsconderFormValidacao(false);
        ValidacaoLocalAcessoVO obj = (ValidacaoLocalAcessoVO) context().getExternalContext().getRequestMap().get("validacao");
        coletor.getPermissoes().remove(obj);
        setMensagemID("msg_dados_excluidos");
    }

    public LocalAcessoVO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoVO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public boolean isPortaServidor(){
        switch (getColetor().getModelo()){
            case MODELO_COLETOR_INTEGRA_FACIL:
                return true;
        }
        return false;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        return itens;
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void clonar() {
        localAcesso.setCodigo(new Integer(0));
        localAcesso.setNovoObj(true);
        // Zerar os códigos dos coletores
        for (ColetorVO obj : localAcesso.getListaColetores()) {
            obj.setCodigo(0);
            obj.setNovoObj(true);
        }
        localAcesso.setUtilizarModoOffline(false);
        localAcesso.setDataBaseOffline(null);
        localAcesso.setDataDownloadBase(null);
        localAcesso.setDescricao("Cópia " + localAcesso.getDescricao());
        setMensagemID("msg_entre_dados");
        setSucesso(true);
        setErro(false);
    }

    private void verificarColetorPadraoCadastro() throws Exception {
        List<ColetorVO> coletores = localAcesso.getListaColetores();
        boolean temPadraoCadastro = false;
        for (ColetorVO coletorVO : coletores) {
            if (!coletorVO.getDesativado() && coletorVO.getPadraoCadastro()) {
                if (temPadraoCadastro) {
                    throw new Exception("Somente um coletor em cada local de acesso pode ser marcado como padrão para cadastro.");
                }
                temPadraoCadastro = true;
            }
        }
    }

    private void verificarColetorPadraoCadastroFacial() throws Exception {
        List<ColetorVO> coletores = localAcesso.getListaColetores();
        boolean temPadraoCadastroFacial = false;
        for (ColetorVO coletorVO : coletores) {
            if (!coletorVO.getDesativado() && coletorVO.getPadraoCadastroFacial()) {
                if (temPadraoCadastroFacial) {
                    throw new Exception("Somente um coletor em cada local de acesso pode ser marcado como padrão para cadastro facial.");
                }
                temPadraoCadastroFacial = true;
            }
        }
    }
    public void validarPortasEntradaSaida() throws Exception{
        int indexLista = 1;
        for(ColetorVO coletor : getLocalAcesso().getListaColetores()) {
            if(coletor.getModelo() != null){
                ValidaColetorFactory.validaColetor(coletor);
            } else {
                throw new Exception("O "+indexLista+"º coletor da lista não teve o modelo informado. Informe um modelo a esse coletor!");
            }
            indexLista++;
        }
    }

    public void verificarColetorUnico() throws Exception{
        List<ColetorVO> coletores = localAcesso.getListaColetores();
        for (ColetorVO coletorvo : coletores){
            coletor = coletorvo;
            if(UteisValidacao.emptyString(coletor.getCodigoNFC())){
                continue;
            }
            if(getFacade().getLocalAcesso().consultarExisteNFC(coletor.getCodigoNFC(), coletor.getCodigo()) ){
                throw new Exception("O codigo de NFC não pode se repetir entre os coletores.");
            }
        }

    }

    public void validarCadastro() throws Exception{
        verificarColetorPadraoCadastro();
        verificarColetorPadraoCadastroFacial();
        validarPortasEntradaSaida();
        verificarColetorUnico();
        verificarColetorComBiometria();
    }

    private void verificarColetorComBiometria() throws Exception {
        List<ColetorVO> coletores = localAcesso.getListaColetores();
        for (ColetorVO coletorVO : coletores) {
            if (coletorVO.getBiometrico() && !UteisValidacao.emptyString(coletorVO.getNumSerie())) {
                throw new Exception("Ao marcar 'Usa biometria' no coletor (Descrição: " + coletorVO.getDescricao() + ") não pode ser informado o 'Número de série' em Impressões digitais.");
            }
        }
    }

    public String gravar() {
        try {
            validarCadastro();
            gravarValidacaoEmMemoria();
            if (localAcesso.isNovoObj()) {
                getFacade().getLocalAcesso().incluir(localAcesso);
                incluirLogInclusao();
            } else {
                getFacade().getLocalAcesso().alterar(localAcesso);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
//            for(ColetorVO coletor : localAcesso.getListaColetores()){
//                registrarLogObjetoVO(coletor,localAcesso.getCodigo(),NOME_ENTIDADE,0);
//            }
            setMostrarBotaoGerarDadosOff(getUsuarioLogado().getAdministrador() && getLocalAcesso().getUtilizarModoOffline());
            setMostrarEsconderFormColetor(false);
            setMostrarEsconderFormValidacao(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String excluir() {
        try {
            setMostrarEsconderFormColetor(false);
            setMostrarEsconderFormValidacao(false);
            getFacade().getLocalAcesso().excluir(localAcesso);
            incluirLogExclusao();
            setLocalAcesso(new LocalAcessoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"coletor\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"coletor\" violates foreign key")){
                setMensagemDetalhada("Este Local de Acesso não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    public List getListaPortaCom() {
        listaPortaCom = new ArrayList();
        for (int i = 1; i < 10; i++) {
            String valor = String.valueOf(i);
            listaPortaCom.add(new SelectItem(valor, "COM" + valor));
        }
        return listaPortaCom;
    }
    public List getListaPortaSerial() {
        listaPortaGertec = new ArrayList();
        for (int i = 0; i < 9; i++) {
            String valor = String.valueOf(i);
            listaPortaGertec.add(new SelectItem(valor, i == 0 ? " " : ("COM" + valor)));
        }
        return listaPortaGertec;
    }

    public void setListaPortaCom(List listaPortaCom) {
        this.listaPortaCom = listaPortaCom;
    }

    public List getListaPortaComSerial() {
        listaPortaComSerial = new ArrayList();
        listaPortaComSerial.add(new SelectItem(null, "(nenhuma)"));
        for (int i = 1; i < 10; i++) {
            String valor = String.valueOf(i);
            listaPortaComSerial.add(new SelectItem(valor, "COM" + valor));
        }
        return listaPortaComSerial;
    }

    public void setListaPortaComSerial(List listaPortaComSerial) {
        this.listaPortaComSerial = listaPortaComSerial;
    }

    public List getListaSelectItemTipoHorario() {
        return montarListaItemTipoHorario();
    }

    public List getListaSelectItemTipoValidacao() {

        listaSelectItemTipoValidacao = new ArrayList();
        for (TipoValidacaoEnum tipoValidacao : TipoValidacaoEnum.values()) {
            listaSelectItemTipoValidacao.add(new SelectItem(tipoValidacao.getId(), tipoValidacao.getDescricao()));
        }
        return listaSelectItemTipoValidacao;
    }

    public List getListaSelectItemModeloColetor() {
        listaSelectItemModeloColetor = new ArrayList();

        for (ModeloColetorEnum modelo : ModeloColetorEnum.values()) {
            listaSelectItemModeloColetor.add(new SelectItem(modelo, modelo.getDescricao()));

        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) listaSelectItemModeloColetor, ordenador);
        return listaSelectItemModeloColetor;
    }

    public void setListaSelectItemModeloColetor(List listaSelectItemModeloColetor) {
        this.listaSelectItemModeloColetor = listaSelectItemModeloColetor;
    }

    public List getListaSelectItemModoTransmissao() {
        listaSelectItemModoTransmissao = new ArrayList();
        listaSelectItemModoTransmissao.add(new SelectItem("", ""));

        for (ModoTransmissaoEnum modoTrans : ModoTransmissaoEnum.values()) {
            listaSelectItemModoTransmissao.add(new SelectItem(modoTrans, modoTrans.getDescricao()));

        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) listaSelectItemModoTransmissao, ordenador);
        return listaSelectItemModoTransmissao;
    }

    public void setListaSelectItemModoTransmissao(List listaSelectItemModoTransmissao) {
        this.listaSelectItemModoTransmissao = listaSelectItemModoTransmissao;
    }

    public List getListaSelectItemSentidoAcesso() {
        listaSelectItemSentidoAcesso = new ArrayList();
        listaSelectItemSentidoAcesso.add(new SelectItem("", ""));

        for (SentidoAcessoEnum sentido : SentidoAcessoEnum.values()) {
            listaSelectItemSentidoAcesso.add(new SelectItem(sentido, sentido.getDescricao()));

        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) listaSelectItemSentidoAcesso, ordenador);
        return listaSelectItemSentidoAcesso;
    }

    public void setListaSelectItemSentidoAcesso(List listaSelectItemSentidoAcesso) {
        this.listaSelectItemSentidoAcesso = listaSelectItemSentidoAcesso;
    }

    public void setListaSelectItemTipoComunicacao(List listaSelectItemTipoComunicacao) {
        this.listaSelectItemTipoComunicacao = listaSelectItemTipoComunicacao;
    }

    @Override
    public String consultar() {
        try {
            super.consultar();
            List<LocalAcessoVO> objs = new ArrayList();
            if (super.getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (super.getControleConsulta().getValorConsulta().equals("")) {
                    super.getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getLocalAcesso().consultarPorCodigo(new Integer(valorInt), getEmpresaLogado().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }

            if (super.getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getLocalAcesso().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }


            objs = getFacade().getLocalAcesso().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            setMostrarEsconderFormColetor(false);
            setMostrarEsconderFormValidacao(false);
            LocalAcessoVO obj = getFacade().getLocalAcesso().consultarPorCodigo(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(false);
            obj.setListaColetores(consultarColetores(obj.getCodigo()));
            setLocalAcesso(obj);
            setMostrarBotaoGerarDadosOff(getUsuarioLogado().getAdministrador() && getLocalAcesso().getUtilizarModoOffline());
            obj.registrarObjetoVOAntesDaAlteracao();
            //localAcesso.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public ArrayList consultarColetores(Integer localAcesso) throws Exception {
        ArrayList<ColetorVO> lista = getFacade().getColetor().consultarColetores(localAcesso, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        return lista;
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public void montarListaModalidadeOuProduto() {
        try {
            if (validacao.getChave() == null) {
                validacao.setChave(0);
            }
            if (this.codigoTipoValidacao != TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId().intValue()) {
                ProdutoControle.limparSuggestionBoxProduto();
            }

            if (this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Modalidade.getId().intValue()) {
                setNomeTipoValidacaoEscolhida("* Modalidade:");
                // Pesquisar todas as modalidades e as empresas que poderão visualizá-las
                List<ModalidadeVO> listaModalidades = getFacade().getModalidade().consultarPorNomeModalidadeSemDesativado("", 0, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
                listaSelectItemModalidade.clear();
                for (ModalidadeVO obj : listaModalidades) {
                    List<ModalidadeEmpresaVO> listaModalidadesEmpresa = obj.getModalidadeEmpresaVOs();
                    if (listaModalidadesEmpresa.size() > 0) {
                        // Neste caso, somente algumas empresas podem visualizar esta modalidade.
                        for (ModalidadeEmpresaVO objModEmp : listaModalidadesEmpresa) {
                            if (objModEmp.getEmpresa().getCodigo().intValue() == validacao.getEmpresa().getCodigo().intValue()) {
                                listaSelectItemModalidade.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                                break;
                            }
                        }

                    } else {
                        // Neste caso, todas as empresas podem visualizar esta modalidade.
                        listaSelectItemModalidade.add(new SelectItem(obj.getCodigo(), obj.getNome()));
                    }
                }
            } else if (this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Produto.getId().intValue()) {
                setNomeTipoValidacaoEscolhida("* Produto:");
            } else if (this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_ProdutoGymPass.getId().intValue()) {
                setNomeTipoValidacaoEscolhida("* Produto Gympass:");
                this.codigoTipoHorario = 3;
                validacao.setTipoHorario(TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico);
                montarListaHorario();
                String gymID = getFacade().getEmpresa().obterGymIDPorCodEmpresa(validacao.getEmpresa().getCodigo());
                if (!UteisValidacao.emptyString(gymID)) {
                    JSONArray listaProdutos = getFacade().getProduto().obterProdutosGymPass(gymID);
                    listaSelectItemProdutoGymPass.clear();
                    if (listaProdutos != null && listaProdutos.length() > 0) {
                        for (int i = 0; i < listaProdutos.length(); i++) {
                            JSONObject obj = listaProdutos.getJSONObject(i);
                            listaSelectItemProdutoGymPass.add(new SelectItem(obj.getInt("product_id"), obj.getString("name")));
                        }
                    }
                }
            } else if (this.codigoTipoValidacao == TipoValidacaoEnum.TIPOVALIDACAO_Horario.getId().intValue()) {
                montarListaItemTipoHorario();
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        montarDescricaoRegra();
    }

    public void montarDescricaoRegra() {
        setExplicacaoRegra(retornarTextoExplicativoDaRegra());
    }

    private List montarListaItemTipoHorario() {
        listaSelectItemTipoHorario.clear();
        for (TipoHorarioEnum tipoHorario : TipoHorarioEnum.values()) {
            if (tipoHorario == TipoHorarioEnum.TIPOHORARIO_Todos) {
                // Se o tipo validação for igual a "Horário", então o usuário não poderá informar um Tipo Horário igual a "Todos"
                if (this.codigoTipoValidacao != TipoValidacaoEnum.TIPOVALIDACAO_Horario.getId().intValue()) {
                    listaSelectItemTipoHorario.add(new SelectItem(tipoHorario.getId(), tipoHorario.getDescricao()));
                }
            } else {
                listaSelectItemTipoHorario.add(new SelectItem(tipoHorario.getId(), tipoHorario.getDescricao()));
            }
        }
        return listaSelectItemTipoHorario;
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        String prm = "";
        this.listaSelectItemEmpresa.clear();
        List resultadoConsulta = consultarEmpresaPorNome(prm);
        Iterator i = resultadoConsulta.iterator();
        while (i.hasNext()) {
            EmpresaVO obj = (EmpresaVO) i.next();
            this.listaSelectItemEmpresa.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        if (getEmpresaLogado().getCodigo() != 0) {
            this.getLocalAcesso().setEmpresa(getEmpresaLogado());
        }
    }

    public List consultarEmpresaPorNome(
            String nomePrm) throws Exception {
        List lista = getFacade().getEmpresa().consultarPorNome(nomePrm,true, false, Uteis.NIVELMONTARDADOS_TODOS);
        return lista;
    }

    public List getListaSelectItemHorario() {
        return montarListaHorario();
    }

    public void setListaSelectItemHorario(List listaSelectItemHorario) {
        this.listaSelectItemHorario = listaSelectItemHorario;
    }

    public List montarListaHorario() {
        listaSelectItemHorario.clear();
        try {
            if (this.codigoTipoHorario == TipoHorarioEnum.TIPOHORARIO_HorarioEspecifico.getId()) {
                this.listaSelectItemHorario.clear();
                List<HorarioVO> listaHorario = getFacade().getHorario().consultarPorCodigo(1, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (HorarioVO obj : listaHorario) {
                    this.listaSelectItemHorario.add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaSelectItemHorario;
    }

    public List getListaSelectItemModalidade() {
        return listaSelectItemModalidade;
    }

    public void setListaSelectItemModalidade(List listaSelectItemModalidade) {
        this.listaSelectItemModalidade = listaSelectItemModalidade;
    }

    public List<SelectItem> getListaSelectItemProdutoGymPass() {
        return listaSelectItemProdutoGymPass;
    }

    public void setListaSelectItemProdutoGymPass(List<SelectItem> listaSelectItemProdutoGymPass) {
        this.listaSelectItemProdutoGymPass = listaSelectItemProdutoGymPass;
    }

    /*
    02/02/11 Ulisses...
    Objetivo: Pesquisar os coletores no banco de dados a partir de uma descrição.
     * Quando o método é acionado: Este método é acionado toda vez que o
    usuário digitar uma letra no suggestionBox da pesquisa de coletores.
     */
    public List<ColetorVO> executarAutocompletePesqColetor(Object suggest) {
        List<ColetorVO> listaColetores = null;
        try {
            String nomePesq = (String) suggest;
            listaColetores = getFacade().getColetor().consultarPorDescricao(nomePesq, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaColetores;
    }

    /*
    02/02/11 Ulisses...
     * Objetivo: Selecionar o Coletor a partir do suggestionBox da pesquisa de coletores,
     *           e copiar todas as informações do coletor consultado, para o Coletor que
     *           está sendo visualizado na tela.
     * Quando o método é acionado: Este método é acionado quando o usuário
    seleciona o coletor apartir do suggestionBox
     */
    public void selecionarColetor() {
        ColetorVO copiaColetor = (ColetorVO) context().getExternalContext().getRequestMap().get("resultColetor");
        // Pegar o índice do coletor na lista
        int indice = -1;
        for (int i = 0; i < localAcesso.getListaColetores().size(); i++) {
            if (localAcesso.getListaColetores().get(i).getCodigo().intValue() == coletor.getCodigo().intValue()) {
                indice = i;
                break;
            }
        }
        if (indice >= 0) {
            // Zerar os códigos dos objetos, para que sejam incluidos no banco de dados.
            copiaColetor.setNovoObj(true);
            copiaColetor.setCodigo(0);
            for (ValidacaoLocalAcessoVO obj : copiaColetor.getPermissoes()) {
                obj.setNovoObj(true);
                obj.setCodigo(0);
            }
            // Atualizar o objeto na lista.
            localAcesso.getListaColetores().remove(indice);
            localAcesso.getListaColetores().add(indice, copiaColetor);
            // Atualizar os dados que estão sendo mostrados na tela.
            coletor = copiaColetor;
        }
        fecharRichModalPesqColetor();
    }

    public void fecharRichModalPesqColetor() {
        mostrarRichModalPesqColetor = false;
    }

    public void copiarColetor() {
        mostrarRichModalPesqColetor = true;
    }

    public List<SelectItem> getListaSelectItemDispAlternativos() {
        return listaSelectItemDispAlternativos;
    }

    public void setListaSelectItemDispAlternativos(List<SelectItem> listaSelectItemDispAlternativos) {
        this.listaSelectItemDispAlternativos = listaSelectItemDispAlternativos;
    }
    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getLocalAcesso().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro,null );

    }

    public Boolean getMostrarBotaoGerarDadosOff() {
        return mostrarBotaoGerarDadosOff;
    }

    public void setMostrarBotaoGerarDadosOff(Boolean mostrarBotaoGerarDadosOff) {
        this.mostrarBotaoGerarDadosOff = mostrarBotaoGerarDadosOff;
    }

    public void atualizarDadosOffLocalAcesso(){
        try{
            getFacade().getLocalAcesso().atualizarDadosOffLocalAcesso(getLocalAcesso());
            setMensagemDetalhada("");
            setErro(false);
            setSucesso(true);
            setMensagem("Dados offline atualizados para esse local de acesso!");
        }catch(Exception e) {
            setMensagem("");
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("problemas na geração dos dados: " + e.getMessage());
        }

    }

    public void montarTiposEntradaSaida(){

        getItemsSensorSaida().clear();
        getItemsSensorEntrada().clear();
        getItemsReleSaida().clear();
        getItemsReleEntrada().clear();
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ALMITEC ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ALMITECMAC400 ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ALMITECTCPIPSECDS) {

            getItemsReleEntrada().add(new SelectItem(0,"0"));
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(0,"0"));
            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(0,"0"));
            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));



            getItemsSensorSaida().add(new SelectItem(-1,"-1"));
            getItemsSensorSaida().add(new SelectItem(0,"0"));
            getItemsSensorSaida().add(new SelectItem(1,"1"));
            getItemsSensorSaida().add(new SelectItem(2,"2"));

        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_SERIALPACTO ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_USBSERIAL ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TRIXSTANDARD ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_SERIALFOCA){

            getItemsReleEntrada().add(new SelectItem(0,"0"));
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(0,"0"));
            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));

            getItemsSensorSaida().add(new SelectItem(-1,"-1"));
            getItemsSensorSaida().add(new SelectItem(0,"0"));
            getItemsSensorSaida().add(new SelectItem(1,"1"));
            getItemsSensorSaida().add(new SelectItem(2,"2"));

        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSFP730 ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSNKFP2 ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_NEOKOROSNKFP3){

            getItemsReleEntrada().add(new SelectItem(4,"4"));
            getItemsReleEntrada().add(new SelectItem(5,"5"));

            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(4,"4"));
            getItemsReleSaida().add(new SelectItem(5,"5"));

            getItemsSensorSaida().add(new SelectItem(-1,"-1"));
            getItemsSensorSaida().add(new SelectItem(1,"1"));
            getItemsSensorSaida().add(new SelectItem(2,"2"));
        }

        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INNERTOPDATA
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TCPINNERTOPDATA
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INNER_EVENTOS_LC) {

            getItemsReleEntrada().add(new SelectItem(0,"0"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));
            getItemsReleEntrada().add(new SelectItem(3,"3"));
            getItemsReleEntrada().add(new SelectItem(6,"6"));
            getItemsReleEntrada().add(new SelectItem(7,"7"));
            getItemsReleEntrada().add(new SelectItem(8,"8"));

            getItemsReleSaida().add(new SelectItem(0,"0"));
            getItemsReleSaida().add(new SelectItem(2,"2"));
            getItemsReleSaida().add(new SelectItem(3,"3"));
            getItemsReleSaida().add(new SelectItem(6,"6"));
            getItemsReleSaida().add(new SelectItem(7,"7"));
            getItemsReleSaida().add(new SelectItem(8,"8"));
        }

        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TCA_SERIAL ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ACTUAR_LITENET2) {
            getItemsReleEntrada().add(new SelectItem(1, "1"));
            getItemsReleEntrada().add(new SelectItem(2, "2"));

            getItemsSensorEntrada().add(new SelectItem(1, "1"));
            getItemsSensorEntrada().add(new SelectItem(2, "2"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(1, "1"));
            getItemsSensorSaida().add(new SelectItem(2, "2"));
            getItemsSensorSaida().add(new SelectItem(-1, "-1"));

        }

        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INTELBRAS_SS3530MF){
            getItemsReleEntrada().add(new SelectItem(1, "1"));
            getItemsReleEntrada().add(new SelectItem(2, "2"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorEntrada().add(new SelectItem(1, "1"));
            getItemsSensorEntrada().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(1, "1"));
            getItemsSensorSaida().add(new SelectItem(2, "2"));
            getItemsSensorSaida().add(new SelectItem(-1, "-1"));
        }

        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_LEITOR_NITGEN ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_DESCONHECIDO ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRYTCPIP ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRYSERIAL ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_PARALELAHENRY ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_PARALELATECNIBRA ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_SERIALTECNIBRA ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TCPTECNIBRA ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_SERIALACTUAR ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRY7xV2 ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ACTUARTCPIP ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_CAMERA_ACIONAMENTO ) {

            getItemsReleEntrada().add(new SelectItem(0, "0"));
            getItemsReleEntrada().add(new SelectItem(1, "1"));

            getItemsSensorEntrada().add(new SelectItem(0, "0"));
            getItemsSensorEntrada().add(new SelectItem(1, "1"));

            getItemsReleSaida().add(new SelectItem(0, "0"));
            getItemsReleSaida().add(new SelectItem(1, "1"));

            getItemsSensorSaida().add(new SelectItem(-1, "-1"));
            getItemsSensorSaida().add(new SelectItem(0, "0"));
            getItemsSensorSaida().add(new SelectItem(1, "1"));
        }
        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRY8x
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRY8xPRIMME
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRY8xLV
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRY8XFS
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_HENRY8xFLAP) {

            getItemsReleEntrada().add(new SelectItem(0,"0"));
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));
            getItemsReleEntrada().add(new SelectItem(3,"3"));
            getItemsReleEntrada().add(new SelectItem(4,"4"));
            getItemsReleEntrada().add(new SelectItem(5,"5"));
            getItemsReleEntrada().add(new SelectItem(6,"6"));
            getItemsReleEntrada().add(new SelectItem(7,"7"));
            getItemsReleEntrada().add(new SelectItem(8,"8"));

            getItemsReleSaida().add(new SelectItem(0,"0"));
            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));
            getItemsReleSaida().add(new SelectItem(3,"3"));
            getItemsReleSaida().add(new SelectItem(4,"4"));
            getItemsReleSaida().add(new SelectItem(5,"5"));
            getItemsReleSaida().add(new SelectItem(6,"6"));
            getItemsReleSaida().add(new SelectItem(7,"7"));
            getItemsReleSaida().add(new SelectItem(8,"8"));

            getItemsSensorEntrada().add(new SelectItem(0,"0"));
            getItemsSensorEntrada().add(new SelectItem(1,"1"));

            getItemsSensorSaida().add(new SelectItem(-1,"-1"));
            getItemsSensorSaida().add(new SelectItem(0,"0"));
            getItemsSensorSaida().add(new SelectItem(1,"1"));

        }
        if (getColetor().getModelo().equals(ModeloColetorEnum.MODELO_COLETOR_HOLLEMAX)){
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));
            getItemsReleEntrada().add(new SelectItem(3,"3"));
            getItemsReleEntrada().add(new SelectItem(4,"4"));

            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));
            getItemsReleSaida().add(new SelectItem(3,"3"));
            getItemsReleSaida().add(new SelectItem(4,"4"));

            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));
            getItemsSensorEntrada().add(new SelectItem(3,"3"));
            getItemsSensorEntrada().add(new SelectItem(4,"4"));

            getItemsSensorSaida().add(new SelectItem(1,"1"));
            getItemsSensorSaida().add(new SelectItem(2,"2"));
            getItemsSensorSaida().add(new SelectItem(3,"3"));
            getItemsSensorSaida().add(new SelectItem(4,"4"));
        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TRIXXPBLOCK){

            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(0,"0"));
            getItemsSensorEntrada().add(new SelectItem(1,"1"));

            getItemsSensorSaida().add(new SelectItem(0,"0"));
            getItemsSensorSaida().add(new SelectItem(1,"1"));
            getItemsSensorSaida().add(new SelectItem(-1,"-1"));

        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_MA100){

            getItemsReleEntrada().add(new SelectItem(0,"0"));
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));
            getItemsReleEntrada().add(new SelectItem(3,"3"));
            getItemsReleEntrada().add(new SelectItem(4,"4"));
            getItemsReleEntrada().add(new SelectItem(5,"5"));
            getItemsReleEntrada().add(new SelectItem(6,"6"));
            getItemsReleEntrada().add(new SelectItem(7,"7"));
            getItemsReleEntrada().add(new SelectItem(8,"8"));

            getItemsReleSaida().add(new SelectItem(0,"0"));
            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));
            getItemsReleSaida().add(new SelectItem(3,"3"));
            getItemsReleSaida().add(new SelectItem(4,"4"));
            getItemsReleSaida().add(new SelectItem(5,"5"));
            getItemsReleSaida().add(new SelectItem(6,"6"));
            getItemsReleSaida().add(new SelectItem(7,"7"));
            getItemsReleSaida().add(new SelectItem(8,"8"));

            getItemsSensorSaida().add(new SelectItem(0,"0"));
            getItemsSensorSaida().add(new SelectItem(1,"1"));
            getItemsSensorSaida().add(new SelectItem(2,"2"));
            getItemsSensorSaida().add(new SelectItem(3,"3"));
            getItemsSensorSaida().add(new SelectItem(4,"4"));
            getItemsSensorSaida().add(new SelectItem(5,"5"));
            getItemsSensorSaida().add(new SelectItem(6,"6"));
            getItemsSensorSaida().add(new SelectItem(7,"7"));
            getItemsSensorSaida().add(new SelectItem(8,"8"));

            getItemsSensorEntrada().add(new SelectItem(0,"0"));
            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));
            getItemsSensorEntrada().add(new SelectItem(3,"3"));
            getItemsSensorEntrada().add(new SelectItem(4,"4"));
            getItemsSensorEntrada().add(new SelectItem(5,"5"));
            getItemsSensorEntrada().add(new SelectItem(6,"6"));
            getItemsSensorEntrada().add(new SelectItem(7,"7"));
            getItemsSensorEntrada().add(new SelectItem(8,"8"));

        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INOVACESSO){
            getItemsReleEntrada().add(new SelectItem(0, "0"));
            getItemsReleEntrada().add(new SelectItem(1, "1"));

            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(0, "0"));
            getItemsReleSaida().add(new SelectItem(1, "1"));

            getItemsSensorSaida().add(new SelectItem(-1 , "-1"));
            getItemsSensorSaida().add(new SelectItem(1 , "1"));
            getItemsSensorSaida().add(new SelectItem(2 , "2"));

        }

        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ZK_TECO){
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(2,"2"));
            getItemsReleSaida().add(new SelectItem(1,"1"));

            getItemsSensorSaida().add(new SelectItem(1 , "1"));
            getItemsSensorSaida().add(new SelectItem(2 , "2"));
            getItemsSensorSaida().add(new SelectItem(-1 , "-1"));


        }

        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ZUCHIMZ4
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_DIMEP_DGATE) {
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(3,"3"));
            getItemsSensorEntrada().add(new SelectItem(4,"4"));

            getItemsReleSaida().add(new SelectItem(2,"2"));
            getItemsReleSaida().add(new SelectItem(1,"1"));

            getItemsSensorSaida().add(new SelectItem(4 , "4"));
            getItemsSensorSaida().add(new SelectItem(3 , "3"));
            if (getColetor().getModelo() != ModeloColetorEnum.MODELO_COLETOR_DIMEP_MICROPOINT) {
                getItemsSensorSaida().add(new SelectItem(-1, "-1"));
            }
        }

        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_SERIALNATSO
                || getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TUPA){
            getItemsReleEntrada().add(new SelectItem(1, "1"));
            getItemsReleEntrada().add(new SelectItem(2, "2"));

            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(-1 , "-1"));
            getItemsSensorSaida().add(new SelectItem(1 , "1"));
            getItemsSensorSaida().add(new SelectItem(2 , "2"));
        }

        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INTEGRA_FACIL){
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(3,"3"));
            getItemsSensorEntrada().add(new SelectItem(4,"4"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(-1, "-1"));
            getItemsSensorSaida().add(new SelectItem(3, "3"));
            getItemsSensorSaida().add(new SelectItem(4, "4"));
        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_SYSTEMTECV4){
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(1,"1"));
            getItemsSensorEntrada().add(new SelectItem(2,"2"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(1, "1"));
            getItemsSensorSaida().add(new SelectItem(2, "2"));
        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INTERLAKEN){
            getItemsReleEntrada().add(new SelectItem(1,"1"));
            getItemsReleEntrada().add(new SelectItem(2,"2"));

            getItemsSensorEntrada().add(new SelectItem(3,"3"));
            getItemsSensorEntrada().add(new SelectItem(4,"4"));

            getItemsReleSaida().add(new SelectItem(1,"1"));
            getItemsReleSaida().add(new SelectItem(2,"2"));

            getItemsSensorSaida().add(new SelectItem(3,"3"));
            getItemsSensorSaida().add(new SelectItem(4,"4"));
            getItemsSensorSaida().add(new SelectItem(-1,"-1"));


        }
        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_ZK_TF1700) {
            getItemsReleEntrada().add(new SelectItem(1, "1"));
        }
        if (getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_IDBLOCK ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_IDFLEX ||
                getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_IDBLOCKNEXT) {
            getItemsReleEntrada().add(new SelectItem(1, "1"));
            getItemsReleEntrada().add(new SelectItem(2, "2"));

            getItemsSensorEntrada().add(new SelectItem(3, "3"));
            getItemsSensorEntrada().add(new SelectItem(4, "4"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(3, "3"));
            getItemsSensorSaida().add(new SelectItem(4, "4"));
            getItemsSensorSaida().add(new SelectItem(-1, "-1"));

        }
        if(getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_TECNEW_SERIAL){
            getItemsReleEntrada().add(new SelectItem(1, "1"));
            getItemsReleEntrada().add(new SelectItem(2, "2"));

            getItemsSensorEntrada().add(new SelectItem(1, "1"));
            getItemsSensorEntrada().add(new SelectItem(2, "2"));

            getItemsReleSaida().add(new SelectItem(1, "1"));
            getItemsReleSaida().add(new SelectItem(2, "2"));

            getItemsSensorSaida().add(new SelectItem(1, "1"));
            getItemsSensorSaida().add(new SelectItem(2, "2"));
            getItemsSensorSaida().add(new SelectItem(-1, "-1"));
        }
        if( getColetor().getModelo() == ModeloColetorEnum.MODELO_COLETOR_INNERACESSO3) {
            getItemsReleEntrada().add(new SelectItem(2,"2"));
            getItemsReleEntrada().add(new SelectItem(3,"3"));
            getItemsReleEntrada().add(new SelectItem(6,"6"));

            getItemsReleSaida().add(new SelectItem(2, "2"));
            getItemsReleSaida().add(new SelectItem(3, "3"));
            getItemsReleSaida().add(new SelectItem(6, "6"));
        }
    }

    public List<SelectItem> getItemsReleEntrada() {
        if(UteisValidacao.emptyList(itemsReleEntrada)){
            itemsReleEntrada = new ArrayList<SelectItem>();
        }
        return itemsReleEntrada;
    }

    public void setItemsReleEntrada(List<SelectItem> itemsReleEntrada) {
        this.itemsReleEntrada = itemsReleEntrada;
    }

    public List<SelectItem> getItemsReleSaida() {
        if(UteisValidacao.emptyList(itemsReleSaida)){
            itemsReleSaida = new ArrayList<SelectItem>();
        }
        return itemsReleSaida;
    }

    public void setItemsReleSaida(List<SelectItem> itemsReleSaida) {
        this.itemsReleSaida = itemsReleSaida;
    }

    public List<SelectItem> getItemsSensorEntrada() {
        if(UteisValidacao.emptyList(itemsSensorEntrada)){
            itemsSensorEntrada = new ArrayList<SelectItem>();
        }
        return itemsSensorEntrada;
    }

    public void setItemsSensorEntrada(List<SelectItem> itemsSensorEntrada) {
        this.itemsSensorEntrada = itemsSensorEntrada;
    }

    public List<SelectItem> getItemsSensorSaida() {
        if(UteisValidacao.emptyList(itemsSensorSaida)){
            itemsSensorSaida = new ArrayList<SelectItem>();
        }
        return itemsSensorSaida;
    }

    public void setItemsSensorSaida(List<SelectItem> itemsSensorSaida) {
        this.itemsSensorSaida = itemsSensorSaida;
    }

    public String getDescricaoProcessoRealizado() {
        return descricaoProcessoRealizado;
    }

    public void setDescricaoProcessoRealizado(String descricaoProcessoRealizado) {
        this.descricaoProcessoRealizado = descricaoProcessoRealizado;
    }


    public List getListaPortaGertec() {
        return listaPortaGertec;
    }

    public void setListaPortaGertec(List listaPortaGertec) {
        this.listaPortaGertec = listaPortaGertec;
    }

    public void incluirLogInclusao() throws Exception {
        try {
            localAcesso.setObjetoVOAntesAlteracao(new LocalAcessoVO());
            localAcesso.setNovoObj(true);
            registrarLogObjetoVO(localAcesso, localAcesso.getCodigo(), NOME_ENTIDADE, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, localAcesso.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        localAcesso.setNovoObj(new Boolean(false));
        localAcesso.registrarObjetoVOAntesDaAlteracao();
    }

    public void incluirLogExclusao() throws Exception {
        try {
            localAcesso.setObjetoVOAntesAlteracao(new LocalAcessoVO());
            localAcesso.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(localAcesso, localAcesso.getCodigo(), NOME_ENTIDADE, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, localAcesso.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(localAcesso, localAcesso.getCodigo(), NOME_ENTIDADE, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(NOME_ENTIDADE, localAcesso.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE "+NOME_ENTIDADE, this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        localAcesso.registrarObjetoVOAntesDaAlteracao();
    }

    public void realizarConsultaLogObjetoGeral() {
        localAcesso = new LocalAcessoVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public List<SelectItem> getListaSelectItemCategoriaLocalAcesso() {
        if (listaSelectItemCategoriaLocalAcesso == null) {
            listaSelectItemCategoriaLocalAcesso = new ArrayList<SelectItem>();
            listaSelectItemCategoriaLocalAcesso.add(new SelectItem(null, ""));
            for (CategoriaLocalAcessoEnum categoriaLocalAcessoEnum : CategoriaLocalAcessoEnum.values()) {
                listaSelectItemCategoriaLocalAcesso.add(new SelectItem(categoriaLocalAcessoEnum.toString(), categoriaLocalAcessoEnum.getDescricao()));
            }
        }
        return listaSelectItemCategoriaLocalAcesso;
    }

    public void setListaSelectItemCategoriaLocalAcesso(List<SelectItem> listaSelectItemCategoriaLocalAcesso) {
        this.listaSelectItemCategoriaLocalAcesso = listaSelectItemCategoriaLocalAcesso;
    }

    public Boolean getUtilizarMatriculaComoSenha() {
        return utilizarMatriculaComoSenha;
    }

    public void setUtilizarMatriculaComoSenha(Boolean utilizarMatriculaComoSenha) {
        this.utilizarMatriculaComoSenha = utilizarMatriculaComoSenha;
    }

    public ModeloColetorEnum getModeloColetorIntegraFacil() {
        return modeloColetorIntegraFacil;
    }

    public void setModeloColetorIntegraFacil(ModeloColetorEnum modeloColetorIntegraFacil) {
        this.modeloColetorIntegraFacil = modeloColetorIntegraFacil;
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Local de Acesso",
                "Deseja excluir o Local de Acesso?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}
