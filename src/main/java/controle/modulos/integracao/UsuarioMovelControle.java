/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.modulos.integracao;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.modulos.integracao.usuariomovel.UsuarioMovelVO;
import negocio.oamd.EmpresaFinanceiroVO;
import servicos.impl.oamd.OAMDService;
import servicos.integracao.TreinoWSConsumer;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.faces.event.ActionEvent;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class UsuarioMovelControle extends SuperControle {

    private UsuarioMovelVO uMovel = new UsuarioMovelVO();
    private Boolean campoNomeDesabilitado;
    private Boolean campoSenhaDesabilitado;
    private Boolean campoAtivoDesabilitado;

    public UsuarioMovelVO getuMovel() {
        return uMovel;
    }

    public void setuMovel(UsuarioMovelVO uMovel) {
        this.uMovel = uMovel;
    }

    public void init(final ClienteVO cliente) throws Exception {
        setMensagemDetalhada("", "");
        uMovel = getFacade().getUsuarioMovel().consultarPorCliente(cliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        uMovel.setEmpresa(cliente.getEmpresa().getCodigo());
        uMovel.setCliente(cliente);
        uMovel.setOrigem("ZW");
        uMovel.setKey(getKey());
    }

    public void init(final ColaboradorVO colaborador) throws Exception {
        setMensagemDetalhada("", "");
        uMovel = getFacade().getUsuarioMovel().consultarPorColaborador(colaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        uMovel.setColaborador(colaborador);
        uMovel.setEmpresa(colaborador.getEmpresa().getCodigo());
        uMovel.setOrigem("ZW");
        uMovel.setUsuarioZW((Integer) SuperFacadeJDBC.consultarValorColunaTop(
                "select codigo from usuario where colaborador = "
                        + colaborador.getCodigo(),
                Conexao.getFromSession()));
    }

    public void preencherUsuarioMovel(ActionEvent evt) {
        try {
            if (evt.getComponent().getAttributes().get("cliente") != null) {
                init((ClienteVO) evt.getComponent().getAttributes().get("cliente"));
            } else if (evt.getComponent().getAttributes().get("colaborador") != null) {
                setCampoAtivoDesabilitado(true);
                setCampoNomeDesabilitado(true);
                setCampoSenhaDesabilitado(true);
                init((ColaboradorVO) evt.getComponent().getAttributes().get("colaborador"));
            }
        } catch (Exception e) {
            setMensagemDetalhada(e);
            Uteis.logar(e, getClass());
        }
    }
    public void gravar(ClienteVO clienteVO) throws Exception{
        gravar(false,clienteVO);
    }
    public void gravar(ColaboradorVO colaboradorVO) throws Exception {
        gravar(false,colaboradorVO);
    }
    public void gravar() throws Exception {
        gravar(false);
    }
    public void gravar(boolean validarEmailUsuario) throws Exception {
        gravar(validarEmailUsuario, getKey());
    }

    public void gravar(boolean validarEmailUsuario, String key) throws Exception {
        limparMsg();
        try {
            if (key == null) {
                key = getKey();
            }

            validarChaveComSessao(key);
            if (uMovel != null && !uMovel.getNome().isEmpty()) {

                //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
                if (!Uteis.removerEspacosInicioFimString(uMovel.getSenha()).equals(uMovel.getSenha())) {
                    throw new Exception("A senha não pode conter espaços no início ou no final");
                }
                //SOLICITACAO MAXIMILIANO NÃO ALTERAR -- TICKET #3319!!!
                if (!Uteis.removerEspacosInicioFimString(uMovel.getNome()).equals(uMovel.getNome())) {
                    throw new Exception("O email não pode conter espaços no início ou no final");
                }

                if(!UteisValidacao.validaEmail(uMovel.getNome()) && validarEmailUsuario){
                    throw new Exception("O formato de email digitado para o usuário móvel é inválido");
                }

                if (uMovel.getCodigo() == 0) {
                    getFacade().getUsuarioMovel().incluir(uMovel);
                } else {
                    getFacade().getUsuarioMovel().alterar(uMovel);
                }
                gerarLoginSiteRedeEmpresa(uMovel.getNome(), uMovel.getCpf());

                boolean apresentarModuloTreino = TreinoWSConsumer.empresaTemIntegracaoTW(key);

                if (apresentarModuloTreino &&
                        ((uMovel.getColaborador() != null
                                && uMovel.getColaborador().getCodigo() > 0)
                                || (uMovel.getCliente().getCodigo() > 0))) {
                    TreinoWSConsumer.sincronizarUsuario(key, uMovel);
                    adicionarUsuarioServicoDescobrir(key, uMovel);
                    sincronizarUsuarioTreinoWeb(key, uMovel);
                }
            } else if(getuMovel().getCliente() != null &&
                    !UteisValidacao.emptyNumber(getuMovel().getCliente().getCodigo())){
                getFacade().getUsuarioMovel().verificarEnvioAlunoParaTW(getKey(), getuMovel().getCliente());
            }
        } catch (Exception e) {
            if(uMovel.getPropagarExcessao()){
                if(e.getMessage() != null &&
                        !e.getMessage().contains("Parâmetros não definidos para chave: ")){
                    throw e;
                }

            }
            setMensagemDetalhada(e.getMessage() != null
                    && e.getMessage().contains("username_ukey")
                    ? new Exception("Já existe um usuário com este nome!")
                    : e);
        }
    }

    private void sincronizarUsuarioTreinoWeb(String key, UsuarioMovelVO usuarioMovelVO) {
        try {
            if (UteisValidacao.emptyNumber(usuarioMovelVO.getUsuarioZW())) {
                return;
            }
            Integer codTreino = getFacade().getUsuarioMovel().obterUsuarioTW(key, usuarioMovelVO.getUsuarioZW(), usuarioMovelVO.getEmpresa());
            if (!UteisValidacao.emptyNumber(codTreino)) {
                usuarioMovelVO.setUsuarioTW(codTreino);
                if (!UteisValidacao.emptyNumber(usuarioMovelVO.getCodigo())) {
                    getFacade().getUsuarioMovel().alterarUsuarioTW(usuarioMovelVO);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void gravar(boolean validarEmailUsuario,ColaboradorVO colaboradorVO) throws Exception{
        uMovel.setColaborador(colaboradorVO);
        gravar(validarEmailUsuario);
    }
    public void gravar(boolean validarEmailUsuario,ClienteVO clienteVO) throws Exception{
        uMovel.setCliente(clienteVO);
        gravar(validarEmailUsuario);
    }

    private void gerarLoginSiteRedeEmpresa(String email, String cpf){
        try{
            OAMDService oamdService = new OAMDService();
            try{
                EmpresaFinanceiroVO empresaFinanceiroVO = oamdService.consultarEmpresaFinanceiro((String) JSFUtilities.getFromSession("key"));
                if (UtilReflection.objetoMaiorQueZero(empresaFinanceiroVO, "getCodigo()")){
                    oamdService = new OAMDService();
                    oamdService.incluirLoginSiteRedeEmpresa(email, empresaFinanceiroVO.getCodigo(), cpf);
                }
            }finally {
                oamdService = null;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void adicionarUsuarioServicoDescobrir(String ctx, UsuarioMovelVO usuarioMovelVO) {
        try {
            String email = "";
            if(usuarioMovelVO.getNome().contains("@")){
                email = usuarioMovelVO.getNome();
            }

            if(!usuarioMovelVO.getUsuarioEmailVO().getEmail().isEmpty()){
                email = usuarioMovelVO.getUsuarioEmailVO().getEmail();
            }

            if(!email.isEmpty()){

                PessoaVO pessoaVO = obterPessoaUsuarioMovel(usuarioMovelVO);

                String url = String.format("%s/prest/empresa/%s/v3/inserirUsuario", new Object[]{
                    PropsService.getPropertyValue(PropsService.urlOamd),
                    ctx
                });
                Map<String, String> params = new HashMap<String, String>();
                params.put("email", email);
                params.put("cpf", pessoaVO.getCfp());
                params.put("telefone", pessoaVO.getTelefones());
                params.put("dataNascimento", Uteis.getDataAplicandoFormatacao(pessoaVO.getDataNasc(), "dd/MM/yyyy"));
                params.put("senha", usuarioMovelVO.getSenha());
                ExecuteRequestHttpService.executeRequest(url, params);
            }
        } catch (Exception e) {
            Uteis.logar(e, UsuarioMovelControle.class);
        }
    }

    private static PessoaVO obterPessoaUsuarioMovel(UsuarioMovelVO usuarioMovelVO) {
        try {
            if (usuarioMovelVO.getCliente() != null && !UteisValidacao.emptyNumber(usuarioMovelVO.getCliente().getCodigo())) {
                return usuarioMovelVO.getCliente().getPessoa();
            } else if (usuarioMovelVO.getColaborador() != null && !UteisValidacao.emptyNumber(usuarioMovelVO.getColaborador().getCodigo())) {
                return usuarioMovelVO.getColaborador().getPessoa();
            } else {
                return new PessoaVO();
            }
        } catch (Exception ex) {
            return new PessoaVO();
        }
    }

    public Boolean getCampoAtivoDesabilitado() {
        if (campoAtivoDesabilitado == null){
            campoAtivoDesabilitado = false;
        }
        return campoAtivoDesabilitado;
    }

    public void setCampoAtivoDesabilitado(Boolean campoAtivoDesabilitado) {
        this.campoAtivoDesabilitado = campoAtivoDesabilitado;
    }

    public Boolean getCampoSenhaDesabilitado() {
        if (campoSenhaDesabilitado == null){
            campoSenhaDesabilitado = false;
        }
        return campoSenhaDesabilitado;
    }

    public void setCampoSenhaDesabilitado(Boolean campoSenhaDesabilitado) {
        this.campoSenhaDesabilitado = campoSenhaDesabilitado;
    }

    public Boolean getCampoNomeDesabilitado() {
        if (campoNomeDesabilitado == null){
            campoNomeDesabilitado = false;
        }
        return campoNomeDesabilitado;
    }

    public void setCampoNomeDesabilitado(Boolean campoNomeDesabilitado) {
        this.campoNomeDesabilitado = campoNomeDesabilitado;
    }

    public static StringBuffer montarMensagemEmailSenha(String nomeCliente, String usuario, String senha, boolean recuperacao, EmpresaVO empresaVO, String urlFotoEmpresa, boolean app_personalizado, String app_personalizado_nome, String app_personalizado_url, String app_url_email) {
        return montarEmail(empresaVO, nomeCliente, senha, usuario, recuperacao, urlFotoEmpresa, app_personalizado, app_personalizado_nome, app_personalizado_url, app_url_email);
    }

    public static StringBuffer montarEmail(EmpresaVO empresaVO, String nomeCliente, String senha, String usuario, boolean recuperacao, String urlFotoEmpresa, boolean app_personalizado, String app_personalizado_nome, String app_personalizado_url, String app_url_email) {
        String urlAppPlay = "", urlAppApple = "", nomeApp = "";
        if(!UteisValidacao.emptyString(app_url_email)) {
            switch(app_url_email){

                case "MINHA_ACADEMIA" :
                    urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto.zwacademia&hl=pt_BR&gl=US";
                    urlAppApple = "https://apps.apple.com/br/app/minha-academia/id1388309741";
                    nomeApp = "Minha Academia";
                    break;

                case "MEU_BOX" :
                    urlAppPlay = "https://play.google.com/store/apps/details?id=com.pactosolucoes.meubox&hl=pt_BR&gl=US";
                    urlAppApple = "https://apps.apple.com/pt/app/meu-box/id1342274240";
                    nomeApp = "Meu Box";
                    break;
                default:
                    urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto&hl=pt_BR&gl=US";
                    urlAppApple = "https://apps.apple.com/br/app/treino/id862662527";
                    nomeApp = "App Treino";
                    break;
            }
        } else {
            urlAppPlay = "https://play.google.com/store/apps/details?id=com.pacto&hl=pt_BR&gl=US";
            urlAppApple = "https://apps.apple.com/br/app/treino/id862662527";
            nomeApp = "App Treino";
        }

        // ---------------------------------- Corpo Email ------------------------------ //
        StringBuffer texto = new StringBuffer();
        texto.append("<html lang=\"pt\">");
        texto.append("  <head>");
        texto.append("    <title>App Pacto Treino</title>");
        texto.append("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=iso-8859-1\">");
        texto.append("  </head>");
        texto.append("  <body bgcolor=\"#FFFFFF\" leftmargin=\"0\" topmargin=\"0\" marginwidth=\"0\" marginheight=\"0\">");
        texto.append("    <div style=\"font-family: Poppins; color: #2e3133; width:600px; background-color: #FFFFFF;margin: auto;\">");
        texto.append("      <div style=\"background: #2b59f1; padding: 72px 72px 0\">");
        texto.append("        <div style=\"background: #ffffff;\">");
        texto.append("          <div style=\"logoPacto\">");
        texto.append("            <img style=\"height: 80px; width: 200px; top: 150px; border-radius: 0px; padding: 72px 128px;\" src=\"" + urlFotoEmpresa + "\">");
        texto.append("          </div>");
        texto.append("        </div>");
        texto.append("      </div>");
        texto.append("      <div style=\"background: #ffffff;\">");
        texto.append("        <div style=\"margin: 0px 72px;\">");
        texto.append("          <p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">Olá, " + nomeCliente + "!</p>");
        if (recuperacao) {
            texto.append("<p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">Foi solicitada uma recuperação de senha.<p>");
        } else {
            texto.append("          <p style=\"font-size: 14px; font-weight: 400; line-height: 21px; text-align: left;\">A partir de agora você é um aluno da " + empresaVO.getNome().toUpperCase() + " e poderá acessar seus treinos, agendar aulas e muito mais, tudo isso em um aplicativo moderno e simples.</p>");
            texto.append("          <div style=\"padding: 24px 0px 24px 0px; background: #fafafa;\">");
            texto.append("            <div style=\"padding: 24px 32px;\">");
            texto.append("              <p style=\"text-align: center; font-size: 14px; font-weight: 400; line-height: 21px;\">Utilize as informações abaixo para acessar o app " + nomeApp + ":</p>");
            texto.append("              <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">1 - Baixe o aplicativo para o seu celular</p>");
            texto.append("              <div style=\"text-align: center;\">");
            texto.append("                <a href=\"" + urlAppPlay + "\"><img style=\"height: 40px; width: 135px;\" src=\"https://app.pactosolucoes.com.br/midias/email_app/baixar_androidP4CT0.png\"/></a>");
            texto.append("                <a href=\"" + urlAppApple + "\"><img style=\"height: 40px; width: 135px;\" src=\"https://app.pactosolucoes.com.br/midias/email_app/baixar_appleP4CT0.png\"/></a>");
            texto.append("              </div>");
            texto.append("              <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">2 - Acesse o aplicativo com o usuário e senha abaixo</p>");
            texto.append("              <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">Usuário</p>");
            texto.append("              <p style=\"text-align: center; font-size: 14px; font-weight: 400; line-height: 21px;\">" + usuario + "</p>");
            texto.append("              <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">Senha</p>");
            texto.append("              <p style=\"text-align: center; font-size: 14px; font-weight: 400; line-height: 21px;\">" + senha + "</p>");
            texto.append("            </div>");
            texto.append("          </div>");
        }
        texto.append("          <hr style=\"background: #c7c9cc;\">");
        texto.append("          <p style=\"font-size: 12px; font-weight: 600; line-height: 18px; text-align: center;\">Enviado por</p>");
        texto.append("          <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">" + empresaVO.getNome().toUpperCase() + "</p>");
        texto.append("          <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\"> " + empresaVO.getEndereco() + "</p>");
        texto.append("          <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">" + empresaVO.getCidade().getNome() + "-" + empresaVO.getCidade().getEstado().getSigla() + "</p>");
        texto.append("          <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">");
        texto.append("            <img style=\"background: url('https://app.pactosolucoes.com.br/midias/email_app/pct-phone-2.png'); height: 15px; width: 15px;border: 0;\">");
        texto.append("            " + empresaVO.getPrimeiroTelefoneNaoNulo() +" ");
        texto.append("            <img style=\"background: url('https://app.pactosolucoes.com.br/midias/email_app/pct-mail-2.png'); height: 15px; width: 15px;border: 0;\">");
        texto.append("            " + empresaVO.getEmail());
        texto.append("          </p>");
        texto.append("          <p style=\"font-size: 10px; font-weight: 300; line-height: 15px; text-align: center;\">Desenvolvido com <span style=\"color: red;\">&hearts;</span> pela Pacto Soluções Tecnológicas<br>");
        texto.append("            Copyright &copy; 1999 - 2022 | Tech. People. Wellness.</p>");
        texto.append("        </div>");
        texto.append("      </div>");
        texto.append("    </div>");
        texto.append("  </body>");
        texto.append("</html>");
        return texto;
    }

    public static void montarEstiloEmail(StringBuffer texto) {
        texto.append("<html>")
                .append("<head>")
                .append("<meta charset=\"utf-8\"> <!-- utf-8 works for most cases -->")
                .append("<link href=\"https://fonts.googleapis.com/css?family=Nunito+Sans:400,600,700&display=swap\" rel=\"stylesheet\">")
                .append("<style>")
                .append("html,body {")
                .append("margin: 0 auto !important;")
                .append("padding: 0 !important;")
                .append("height: 100% !important;")
                .append("width: 100% !important;")
                .append("background: #f1f1f1;")
                .append("}")
                .append("* {")
                .append("-ms-text-size-adjust: 100%;")
                .append("-webkit-text-size-adjust: 100%;")
                .append("}")
                .append("div[style*=\"margin: 16px 0\"] {")
                .append("margin: 0 !important;")
                .append("}")
                .append("img {")
                .append("-ms-interpolation-mode:bicubic;")
                .append("}")
                .append("a {")
                .append("text-decoration: none;")
                .append("color: inherit;")
                .append("}")
                .append("</style>")
                .append("<style>")
                .append("body{")
                .append("font-family: Nunito;")
                .append("font-style: normal;")
                .append("color: rgba(0,0,0,.4);")
                .append("}")
                .append(".topo-email {")
                .append("height: 125px;")
                .append("background-image: linear-gradient(270deg, #FF3931 0%, #FF2F62 87.75%);")
                .append("box-shadow: 2px 11px 9px -5px rgba(0, 0, 0, 0.15);")
                .append("border-radius: 0px 0px 10px 10px;")
                .append("}")
                .append(".img-circle {")
                .append("border-radius: 50%;")
                .append("}")
                .append(".img-topo {")
                .append("padding-left: 20px;")
                .append("}")
                .append(".title-topo {")
                .append("padding-left: -20px;")
                .append("}")
                .append("h1 {")
                .append("width: 100%;")
                .append("height: 38px;")
                .append("margin: 0;")
                .append("font-weight: 900;")
                .append("font-size: 28px;")
                .append("line-height: 38px;")
                .append("color: #FFFFFF;")
                .append("}")
                .append("h2 {")
                .append("width: 100%;")
                .append("margin: 0;")
                .append("font-weight: bold;")
                .append("font-size: 26px;")
                .append("line-height: 35px;")
                .append("color: #000000;")
                .append("padding-left: 13px;")
                .append("padding-top: 20px;")
                .append("}")
                .append("h4 {")
                .append("font-weight: normal;")
                .append("font-size: 17px;")
                .append("line-height: 23px;")
                .append("color: #000000;")
                .append("margin: 0;")
                .append("}")
                .append("h5 {")
                .append("font-weight: bold;")
                .append("font-size: 15px;")
                .append("line-height: 20px;")
                .append("color: #000000;")
                .append("margin: 0;")
                .append("}")
                .append(".body_email {")
                .append("background: #EFF2F7;")
                .append("width: 600px;")
                .append("height: 700px;")
                .append("left: 0px;")
                .append("top: 100px;")
                .append("}")
                .append(".conteudo_email {")
                .append("height: 545px;")
                .append("margin: 0 auto;")
                .append("}")
                .append(".conteudo-box {")
                .append("width: 525px;")
                .append("height: 266px;")
                .append("background: #DCDDDF;")
                .append("border-radius: 15px;")
                .append("}")
                .append(".text-box {")
                .append("padding-top: 30px;")
                .append("padding-left: 37px;")
                .append("padding-right: 34px;")
                .append("}")
                .append(".btn.btn-primary{")
                .append("display: block;")
                .append("border-radius: 30px;")
                .append("background: #FF2F62;")
                .append("width: 140px;")
                .append("height: 45px;")
                .append("text-align: center;")
                .append("line-height: 45px;")
                .append("}")
                .append(".conteudo-btn {")
                .append("padding-left: 37px;")
                .append("}")
                .append("span {")
                .append("font-weight: 800;")
                .append("font-size: 14px;")
                .append("color: #ffffff;")
                .append("}")
                .append(".footer {")
                .append("text-align: center;")
                .append("padding-top: 31px;")
                .append("}")
                .append(".bold {")
                .append("font-weight: bold;")
                .append("}")
                .append(".conteudo-p {")
                .append("padding-left: 13px;")
                .append("}")
                .append("</style>")
                .append("</head>");
    }
}
