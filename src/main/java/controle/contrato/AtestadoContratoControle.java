/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.contrato;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.basico.ClienteControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.AtestadoContratoVO;
import negocio.comuns.contrato.ContratoOperacaoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.JustificativaOperacaoVO;
import negocio.comuns.contrato.ValidacaoContratoOperacao;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ContadorTempo;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Usuario;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AtestadoContratoControle extends SuperControle {

    private AtestadoContratoVO atestadoContratoVO;
    private List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs;
    private List<ContratoVO> listaContratoVOs;
    private List<ContratoOperacaoVO> listaContratoOperacaoVOs;
    private Boolean apresentarBotoes;
    private Boolean abrirRichModalDeConfirmacao;
    private Boolean apresentarPeriodoRetorno;
    private UsuarioVO autorizacaoAcesso;
    private boolean apresentarAcesso;
    private String aviso;
    private String nomeArquivoComprovanteOperacao;
    private Boolean operacaoRealizada;
    private File arquivoAtestado;
    private String extensaoArquivoAtestado;
    private Boolean existeArquivo = false;

    public AtestadoContratoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        //novo();
    }

    public void inicializarUsuarioLogado() {
        try {
            getAtestadoContratoVO().getResponsavelOperacao().setCodigo(getUsuarioLogado().getCodigo());
            getAtestadoContratoVO().getResponsavelOperacao().setUsername(getUsuarioLogado().getUsername());
            getAtestadoContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
            getAutorizacaoAcesso().setCodigo(getUsuarioLogado().getCodigo());
            getAutorizacaoAcesso().setUsername(getUsuarioLogado().getUsername());
            getAutorizacaoAcesso().setUserOamd(getUsuarioLogado().getUserOamd());
        } catch (Exception ignored) {
        }
    }

    public String novo() throws Exception {
        ContadorTempo.limparCronometro();
        ContadorTempo.iniciarContagem();
        notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_ATESTADO);
        try {
            setOperacaoRealizada(false);
            setProcessandoOperacao(false);
            setAviso("");
            setApresentarAcesso(false);
            setAutorizacaoAcesso(new UsuarioVO());
            setAtestadoContratoVO(new AtestadoContratoVO());
            setListaContratoVOs(new ArrayList<>());
            setListaContratoOperacaoVOs(new ArrayList<>());
            inicializarUsuarioLogado();
            inicializarListasSelectItemTodosComboBox();
            //validar se o contrato possui um trancamento sem retorno
            ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getAtestadoContratoVO().getContratoVO());
            obterNomeCliente();
            setMensagemID("msg_entre_dados");
            setSucesso(false);
            setErro(false);
            setApresentarPeriodoRetorno(false);
            setApresentarBotoes(true);
            setExisteArquivo(false);
            return "atestado";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erroAtestado";
        }

    }

    public void validarDadosAtestado() throws Exception {
        getAtestadoContratoVO().validarPeriodoAtestado(getListaContratoOperacaoVOs());
        if (atestadoContratoVO.getDataInicio() != null && atestadoContratoVO.getDataTermino() != null) {
            if (!atestadoContratoVO.getContratoVO().isVendaCreditoTreino()) {
                Integer nrDiasContratos = getFacade().getZWFacade().obterNrDiasContrato(atestadoContratoVO.getContratoVO());
                Date dataAnterior = Uteis.obterDataAnterior(atestadoContratoVO.getDataInicio(), 1);
                Integer diasUtilizados = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(atestadoContratoVO.getContratoVO(), dataAnterior);
                Integer diasRestante = atestadoContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(nrDiasContratos, diasUtilizados);
                //validação de data de início do atestado quando o usuário escolher uma data após o fim do contrato
                if (diasRestante <= 0) {
                    throw new ConsistirException("O número de dias utilizados pelo cliente é superior a quantidade de dias do contrato atual. Não é possível realizar esta operação. ");
                }
            }
            //validação de data de início do atestado quando o usuário escolher uma data antes do início do contrato.
            if (atestadoContratoVO.getDataInicio().compareTo(atestadoContratoVO.getContratoVO().getVigenciaDe()) < 0) {
                throw new ConsistirException("Para lançar um atestado a data de início do mesmo deve ser maior ou igual a data de início do contrato");
            }
        }
        setMensagem("");
        setMensagemDetalhada("");
        atestadoContratoVO.setMensagemErro(false);
        montarSucesso("");

    }

    public void confirmar() {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                setProcessandoOperacao(true);
                //validar se o contrato possui um trancamento sem retorno
                ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(getAtestadoContratoVO().getContratoVO());
                getFacade().getContratoOperacao().incluirOperacaoAtestado(atestadoContratoVO, getUsuarioLogado());

                String chaveArquivo = "";
                if (getArquivoAtestado() != null) {
                    chaveArquivo = MidiaService.getInstance().uploadObjectWithExtension(getKey(),
                            MidiaEntidadeEnum.ANEXO_ATESTADO_OPERACAO_CONTRATO,
                            atestadoContratoVO.getContratoOperacaoVO().getCodigo().toString(),
                            getArquivoAtestado(),
                            getExtensaoArquivoAtestado());
                    setExisteArquivo(true);
                }
                atestadoContratoVO.setChaveArquivo(chaveArquivo);
                getFacade().getContratoOperacao().alterarSomenteChaveArquivo(atestadoContratoVO);
                notificarRecursoEmpresa(RecursoSistema.AFASTAMENTO_ATESTADO_SUCESSO, ContadorTempo.encerraContagem());
                setArquivoAtestado(null);

                montarSucessoDadosGravados();
                setOperacaoRealizada(true);
                setApresentarBotoes(false);
                setExecutarAoCompletar("try { fireElementFromParent('form:btnAtualizaCliente'); } catch(e) {}; executePostMessage({reloadContractPage: true});" + getMensagemNotificar());

                //LOG - INICIO
                try {
                    atestadoContratoVO.setObjetoVOAntesAlteracao(new AtestadoContratoVO());
                    atestadoContratoVO.setNovoObj(true);
                    registrarLogObjetoVO(atestadoContratoVO, atestadoContratoVO.getCodigo(), "ATESTADOCONTRATO", atestadoContratoVO.getContratoVO().getPessoa().getCodigo());
                } catch (Exception e) {
                    registrarLogErroObjetoVO("ATESTADOCONTRATO", atestadoContratoVO.getContratoVO().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE ATESTADO CONTRATO", getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }

                setProcessandoOperacao(false);

            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                setProcessandoOperacao(false);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };

        limparMsg();
        try {
            validarDadosAtestado();
            auto.autorizar("Confirmação de Atestado", "Atestado_Autorizar",
                    "Você precisa da permissão \"3.12 - Atestado para Contrato - Autorizar\"",
                    "form,atestadoArquivo,panelBotoes", listener);
        } catch (Exception e) {
            atestadoContratoVO.setMensagemErro(true);
            montarErro(e.getMessage());
        }
    }

    public void gerarPeriodoRetornoAtestado() throws Exception {
        setApresentarAcesso(false);
        if (operacaoRealizada) {
            return;
        }
        limparMsg();
        setApresentarBotoes(true);
        ValidacaoContratoOperacao.validarSeExisteTrancamentoSemRetorno(atestadoContratoVO.getContratoVO());
        if (atestadoContratoVO.getDataInicio() != null && atestadoContratoVO.getNrDiasASomar() != 0) {
            Date novaData = Uteis.somarDias(atestadoContratoVO.getDataInicio(), (getAtestadoContratoVO().getNrDiasASomar() - 1)); // -1 porque o primeiro dia dev
            getAtestadoContratoVO().setDataTermino(novaData);
        }
        if (atestadoContratoVO.getDataInicio() != null && atestadoContratoVO.getDataTermino() != null) {
            if (Calendario.maior(atestadoContratoVO.getDataInicio(), atestadoContratoVO.getDataTermino())) {
                throw new Exception("O campo ATÉ não pode ser antes do campo INÍCIO");
            }

            Integer diasRestante = 0;
            if (atestadoContratoVO.getContratoVO().isVendaCreditoTreino()) {
                diasRestante = ((Long) Uteis.nrDiasEntreDatas(getAtestadoContratoVO().getDataInicio(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())).intValue() + 1;
            } else {
                Integer nrDiasContratos = getFacade().getZWFacade().obterNrDiasContrato(atestadoContratoVO.getContratoVO());
                Date dataAnterior = Uteis.obterDataAnterior(atestadoContratoVO.getDataInicio(), 1);
                Integer diasUtilizados = getFacade().getZWFacade().obterNrDiasUtilizadoAcademiaAteDataEspecifica(atestadoContratoVO.getContratoVO(), dataAnterior);
                diasRestante = atestadoContratoVO.getContratoVO().obterNrDiasRestantesProFinalDoContrato(nrDiasContratos, diasUtilizados);
                if (diasRestante <= 0) {
                    setApresentarBotoes(false);
                    throw new Exception(String.format("Não é possível lançar "
                                    + "um atestado nesse período, pois o número de dias "
                                    + "utilizados (%s) é maior ou igual ao número de dias do contrato (%s).",
                            new Object[]{
                                    diasUtilizados,
                                    nrDiasContratos

                            }));
                }
            }

            getAtestadoContratoVO().setNrDiasASomar(((Long) Uteis.nrDiasEntreDatas(getAtestadoContratoVO().getDataInicio(), getAtestadoContratoVO().getDataTermino())).intValue() + 1);
            //atestado retroativo
            //assume o atestado no passado, como informado pelo usuário, porém,
            //o retorno de atestado iniciará um dia após o atestado e terminará N dias conforme quantidade de dias restante do contrato ou do atestado
            if (Calendario.menor(atestadoContratoVO.getDataInicio(), negocio.comuns.utilitarias.Calendario.hoje())) {
                //atestadoContratoVO.setDataInicioRetorno(Calendario.hoje());
                atestadoContratoVO.setDataInicioRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), 1));
                Long dias = Uteis.nrDiasEntreDatas(atestadoContratoVO.getDataInicio(), atestadoContratoVO.getDataTermino());
                dias = dias + 1;
                Date data = negocio.comuns.utilitarias.Calendario.hoje();
                //contrato está vencido
                if (Calendario.maior(Calendario.hoje(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {

                    if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(), negocio.comuns.utilitarias.Calendario.hoje())) {
                        atestadoContratoVO.setDataInicioRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), 1));
                    } else {
                        atestadoContratoVO.setDataInicioRetorno(negocio.comuns.utilitarias.Calendario.hoje());
                    }

                    //valido se os dias do atestado sao maior que os dias restante da academia
                    //se a quantidade de dias do atestado é maior que a quantidade de dias restante no contrato na época do atestado
                    //então, gerar o retorno de atestado com validade somada os dias restante do contrato a partir de Hoje.
                    //senão, gerar o retorno de atestado com validade somada os dias do próprio atestado a partir de Hoje.
                    if (dias > diasRestante) {
                        atestadoContratoVO.setQtdDiasAtestadoMaiorQueContrato(true);
                        data = Uteis.somarDias(atestadoContratoVO.getDataInicioRetorno(), (diasRestante - 1));
                            /*if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(),
                            atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), diasRestante);
                            } else {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), diasRestante);
                            }*/
                        atestadoContratoVO.setDataTerminoRetorno(data);
                        atestadoContratoVO.setNrDias(diasRestante);
                        atestadoContratoVO.setNrDiasAtestado(dias.intValue());

                    } else {
                        atestadoContratoVO.setQtdDiasAtestadoMaiorQueContrato(false);
                        data = Uteis.somarDias(atestadoContratoVO.getDataInicioRetorno(), (dias.intValue() - 1));
                            /*if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(),
                            atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), dias.intValue());
                            } else {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), dias.intValue());
                            }*/
                        atestadoContratoVO.setDataTerminoRetorno(data);
                        atestadoContratoVO.setNrDias(dias.intValue());
                    }
                    if (dias.longValue() == 1) {
                        atestadoContratoVO.setDataTerminoRetorno(atestadoContratoVO.getDataInicioRetorno());
                    }
                } else {//contrato não está vencido
                    atestadoContratoVO.setDataInicioRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), 1));

                    if (dias > diasRestante) {
                        atestadoContratoVO.setQtdDiasAtestadoMaiorQueContrato(true);

                        if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(),
                                atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), diasRestante);
                        } else {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), diasRestante);
                        }
                        atestadoContratoVO.setDataTerminoRetorno(data);
                        atestadoContratoVO.setNrDias(diasRestante);
                        atestadoContratoVO.setNrDiasAtestado(dias.intValue());

                    } else {
                        atestadoContratoVO.setQtdDiasAtestadoMaiorQueContrato(false);
                        if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(),
                                atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), dias.intValue());
                        } else {
                            data = Uteis.obterDataFutura2(atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), dias.intValue());
                        }
                        atestadoContratoVO.setDataTerminoRetorno(data);
                        atestadoContratoVO.setNrDias(dias.intValue());
                    }

                }


            } else {//não é retroativo
                Long nrDias = Uteis.nrDiasEntreDatas(atestadoContratoVO.getDataInicio(), atestadoContratoVO.getDataTermino());
                nrDias++;
                // a data inicio do retorno do atestado e um dia depois que terminou o atestado pois seu lancamento nao é retroativo
                //entao eu junto os dias que restam ainda do contrato com o do atestado.
                atestadoContratoVO.setDataInicioRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), 1));
                if (nrDias > diasRestante) {
                    atestadoContratoVO.setQtdDiasAtestadoMaiorQueContrato(true);
                    if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                        atestadoContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), diasRestante));
                    } else {
                        atestadoContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), diasRestante));
                    }
                    atestadoContratoVO.setNrDias(diasRestante);
                    atestadoContratoVO.setNrDiasAtestado(nrDias.intValue());
                } else {
                    atestadoContratoVO.setQtdDiasAtestadoMaiorQueContrato(false);
                    if (Calendario.maiorOuIgual(atestadoContratoVO.getDataTermino(), atestadoContratoVO.getContratoVO().getVigenciaAteAjustada())) {
                        atestadoContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getDataTermino(), nrDias.intValue()));
                    } else {
                        atestadoContratoVO.setDataTerminoRetorno(Uteis.obterDataFutura2(atestadoContratoVO.getContratoVO().getVigenciaAteAjustada(), nrDias.intValue()));
                    }
                    atestadoContratoVO.setNrDias(nrDias.intValue());
                }
            }


            setApresentarPeriodoRetorno(true);
            setMensagem("");
            setMensagemID("");
            setMensagemDetalhada("");
            atestadoContratoVO.setMensagemErro(false);
        } else {
            setApresentarPeriodoRetorno(false);
        }
    }

    public void confirmarPermissaoAtestadoComAcesso() {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() throws Exception {
                AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
                setAutorizacaoAcesso(auto.getUsuario());
                gerarPeriodoRetornoAtestado();
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
                // limpa a data termino para obrigar o usuario a informar de novo
                atestadoContratoVO.setDataTermino(null);
            }

            @Override
            public void onFecharModalAutorizacao() {
                // limpa a data termino para obrigar o usuario a informar de novo
                atestadoContratoVO.setDataTermino(null);
            }
        };

        limparMsg();
        try {
            ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(atestadoContratoVO.getContratoVO().getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

            int acessos = getFacade().getAcessoCliente().consultarQtdAcessosEntreDatas(
                    cliente, atestadoContratoVO.getDataInicio(),
                    atestadoContratoVO.getDataTermino(), false);
            // se cliente teve algum acesso neste periodo
            if (acessos > 0) {
                auto.autorizar("Confirmação de Atestado com Acesso", "AtestadoCarencia_Autorizar",
                        "Aluno Possui " + acessos + " Acessos no Período Informado. " +
                                "Você precisa da permissão \"3.18 - Atestado e Férias com Frequência - Autorizar\"",
                        "datas,panelPeridoRetorno", listener);

            } else {
                gerarPeriodoRetornoAtestado();
                setMensagemDetalhada("", "");
            }

        } catch (Exception e) {
            setApresentarPeriodoRetorno(false);
            atestadoContratoVO.setMensagemErro(true);
            montarErro(e);
        }
    }

    public void limparMensagem() {
        setMensagem("");
        setMensagemID("");
        setMensagemDetalhada("");
    }

    public void consultarResponsavel() {
        try {
            getAtestadoContratoVO().setResponsavelOperacao(new Usuario().consultarPorChavePrimaria(getAtestadoContratoVO().getResponsavelOperacao().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getAtestadoContratoVO().getResponsavelOperacao().setUserOamd(getUsuarioLogado().getUserOamd());
            limparMensagem();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void inicializarListasSelectItemTodosComboBox() throws Exception {
        try {
            montarDadosContratoParaAtestado();
            montarDadosListaJustificativaOperacaoVOs();
        } catch (Exception e) {
            throw e;
        }
    }

    public void obterNomeCliente() {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                getAtestadoContratoVO().getContratoVO().getPessoa().setNome(clienteControle.getClienteVO().getPessoa().getNome());
            } else {
                throw new Exception("Não foi possível inicializar o nome do cliente.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void montarDadosContratoParaAtestado() throws Exception {
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get("ClienteControle");
            if (clienteControle != null) {
                getAtestadoContratoVO().setContratoVO(clienteControle.getContratoVO());
                if (clienteControle.getContratoVO().getSituacao().equals("IN")) {
                    getAtestadoContratoVO().setContratoVencido(true);
                } else {
                    getAtestadoContratoVO().setContratoVencido(false);
                }
                getAtestadoContratoVO().setEmpresa(clienteControle.getClienteVO().getEmpresa().getCodigo().intValue());
                getListaContratoVOs().add(clienteControle.getContratoVO());
                setListaContratoOperacaoVOs(clienteControle.getListaSelectItemContratoOperacao());
            } else {
                throw new Exception("Não foi possível inicializar os dados do Contrato.");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs() {
        try {
            montarDadosListaJustificativaOperacaoVOs("AT");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarDadosListaJustificativaOperacaoVOs(String prm) throws Exception {
        try {
            List resultadoConsulta = consultarTipoJustificativaOperacaoPorTipo(prm);
            Iterator i = resultadoConsulta.iterator();
            List objs = new ArrayList();
            objs.add(new SelectItem(new Integer(0), ""));
            while (i.hasNext()) {
                JustificativaOperacaoVO obj = (JustificativaOperacaoVO) i.next();
                objs.add(new SelectItem(obj.getCodigo(), obj.getDescricao().toString()));
            }
            setListaJustificativaOperacaoVOs(objs);
        } catch (Exception e) {
            throw e;
        }
    }

    public List consultarTipoJustificativaOperacaoPorTipo(String prm) throws Exception {
        return getFacade().getJustificativaOperacao().consultarPorTipoOperacao(prm, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public Boolean getApresentarBotoes() {
        return apresentarBotoes;
    }

    public void setApresentarBotoes(Boolean apresentarBotoes) {
        this.apresentarBotoes = apresentarBotoes;
    }

    public AtestadoContratoVO getAtestadoContratoVO() {
        if (atestadoContratoVO == null) {
            atestadoContratoVO = new AtestadoContratoVO();
        }
        return atestadoContratoVO;
    }

    public void setAtestadoContratoVO(AtestadoContratoVO atestadoContratoVO) {
        this.atestadoContratoVO = atestadoContratoVO;
    }

    public List<ContratoVO> getListaContratoVOs() {
        return listaContratoVOs;
    }

    public void setListaContratoVOs(List<ContratoVO> listaContratoVOs) {
        this.listaContratoVOs = listaContratoVOs;
    }

    public List<JustificativaOperacaoVO> getListaJustificativaOperacaoVOs() {
        return listaJustificativaOperacaoVOs;
    }

    public void setListaJustificativaOperacaoVOs(List<JustificativaOperacaoVO> listaJustificativaOperacaoVOs) {
        this.listaJustificativaOperacaoVOs = listaJustificativaOperacaoVOs;
    }

    public List<ContratoOperacaoVO> getListaContratoOperacaoVOs() {
        return listaContratoOperacaoVOs;
    }

    public void setListaContratoOperacaoVOs(List<ContratoOperacaoVO> listaContratoOperacaoVOs) {
        this.listaContratoOperacaoVOs = listaContratoOperacaoVOs;
    }

    public Boolean getApresentarPeriodoRetorno() {
        return apresentarPeriodoRetorno;
    }

    public void setApresentarPeriodoRetorno(Boolean apresentarPeriodoRetorno) {
        this.apresentarPeriodoRetorno = apresentarPeriodoRetorno;
    }

    @Override
    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        atestadoContratoVO = null;
        listaContratoVOs = new ArrayList<>();
        abrirRichModalDeConfirmacao = null;
        apresentarBotoes = null;
        apresentarPeriodoRetorno = null;
        listaJustificativaOperacaoVOs = new ArrayList<>();
        listaContratoOperacaoVOs = new ArrayList<>();

    }

    public UsuarioVO getAutorizacaoAcesso() {
        if (autorizacaoAcesso == null) {
            autorizacaoAcesso = new UsuarioVO();
        }
        return autorizacaoAcesso;
    }

    public void setAutorizacaoAcesso(UsuarioVO autorizacaoAcesso) {
        this.autorizacaoAcesso = autorizacaoAcesso;
    }

    public boolean isApresentarAcesso() {
        return apresentarAcesso;
    }

    public void setApresentarAcesso(boolean apresentarAcesso) {
        this.apresentarAcesso = apresentarAcesso;
    }

    public String getAviso() {
        return aviso;
    }

    public void setAviso(String aviso) {
        this.aviso = aviso;
    }

    public String getNomeArquivoComprovanteOperacao() {
        if (nomeArquivoComprovanteOperacao == null) {
            nomeArquivoComprovanteOperacao = "";
        }
        return nomeArquivoComprovanteOperacao;
    }

    public void setNomeArquivoComprovanteOperacao(String nomeArquivoComprovanteOperacao) {
        this.nomeArquivoComprovanteOperacao = nomeArquivoComprovanteOperacao;
    }

    public void imprimirComprovanteOperacao() {
        try {
            if (getAtestadoContratoVO().getContratoOperacaoVO().getCodigo() != 0) {
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorChavePrimaria(getAtestadoContratoVO().getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                setNomeArquivoComprovanteOperacao(new SuperControleRelatorio().imprimirComprovanteOperacao(getAtestadoContratoVO().getContratoOperacaoVO(), empresaVO));
            } else {
                throw new Exception("Não foi possível imprimir o comprovante da operação.");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public Boolean getOperacaoRealizada() {
        return operacaoRealizada;
    }

    public void setOperacaoRealizada(Boolean operacaoRealizada) {
        this.operacaoRealizada = operacaoRealizada;
    }

    public void upload(UploadEvent upload) throws Exception {
        UploadItem item = upload.getUploadItem();
        if (item.getFile().length() > 512000) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("operacoes.arquivo.upload.tamanhoLimiteExcedido", "Arquivo tem tamanho superior a 512KB");
            throw new ConsistirException("Tamanho Superior a 512KB");
        }

        setArquivoAtestado(item.getFile());
        String[] partes = item.getFileName().split("\\.");
        setExtensaoArquivoAtestado("." + partes[partes.length - 1]);

        setSucesso(true);
        setErro(false);
        setMensagem("Arquivo enviado com sucesso");
        setMensagemDetalhada("", "");
    }

    public File getArquivoAtestado() {
        return arquivoAtestado;
    }

    public void setArquivoAtestado(File arquivoAtestado) {
        this.arquivoAtestado = arquivoAtestado;
    }

    public String getExtensaoArquivoAtestado() {
        return extensaoArquivoAtestado;
    }

    public void setExtensaoArquivoAtestado(String extensaoArquivoAtestado) {
        this.extensaoArquivoAtestado = extensaoArquivoAtestado;
    }

    public Boolean getExisteArquivo() {
        return existeArquivo;
    }

    public void setExisteArquivo(Boolean existeArquivo) {
        this.existeArquivo = existeArquivo;
    }

    public void downloadAtestadoListener(ActionEvent actionEvent) {
        try {
            byte[] b = MidiaService.getInstance().downloadObjectWithExtensionAsByteArray(getKey(),
                    MidiaEntidadeEnum.ANEXO_ATESTADO_OPERACAO_CONTRATO,
                    atestadoContratoVO.getContratoOperacaoVO().getCodigo().toString(),
                    getExtensaoArquivoAtestado(), null);
            if (b == null || b.length == 0) {
                throw new ConsistirException("Não foi possível realizar o download do arquivo");
            }
            HttpServletResponse res = (HttpServletResponse) context().getExternalContext().getResponse();
            ServletOutputStream out = res.getOutputStream();

            String nomeArquivo = atestadoContratoVO.getContratoOperacaoVO().getCodigo() + getExtensaoArquivoAtestado();
            res.setHeader("Content-disposition", "attachment;filename=\"" + nomeArquivo + "\"");
            res.setContentLength(b.length);
            res.setContentType("application/octet-stream");

            out.write(b);
            out.flush();
            out.close();

            FacesContext.getCurrentInstance().responseComplete();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }
}
