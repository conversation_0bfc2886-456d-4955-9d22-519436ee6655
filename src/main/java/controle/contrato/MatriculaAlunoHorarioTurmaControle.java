package controle.contrato;

import negocio.comuns.plano.TurmaVO;
import negocio.comuns.basico.PessoaVO;
import java.util.Iterator;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;

public class MatriculaAlunoHorarioTurmaControle extends SuperControle {
    private MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO;
    protected List listaSelectItemContrato;
    protected List listaSelectItemPessoa;
    protected List listaSelectItemTurma;

    public MatriculaAlunoHorarioTurmaControle() throws Exception {
        obterUsuarioLogado();
        setControleConsulta(new ControleConsulta());
        setMensagemID("msg_entre_prmconsulta");
    }

    public String novo() throws Exception {
        setMatriculaAlunoHorarioTurmaVO(new MatriculaAlunoHorarioTurmaVO());
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_entre_dados");
        return "editar";
    }

    public String editar() throws Exception {
        MatriculaAlunoHorarioTurmaVO obj = (MatriculaAlunoHorarioTurmaVO)context().getExternalContext().getRequestMap().get("matriculaAlunoHorarioTurma");
        obj.setNovoObj(false);
        setMatriculaAlunoHorarioTurmaVO(obj);
        inicializarListasSelectItemTodosComboBox();
        setMensagemID("msg_dados_editar");
        return "editar";
    }

    public String gravar() {
        try {
            if (matriculaAlunoHorarioTurmaVO.isNovoObj().booleanValue()) {
                getFacade().getMatriculaAlunoHorarioTurma().incluir(matriculaAlunoHorarioTurmaVO);
            } else {
                getFacade().getMatriculaAlunoHorarioTurma().alterar(matriculaAlunoHorarioTurmaVO);
            }
            setMensagemID("msg_dados_gravados");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMatriculaAlunoHorarioTurma().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("situacaoContrato")) {
                objs = getFacade().getMatriculaAlunoHorarioTurma().consultarPorSituacaoContrato(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePessoa")) {
                objs = getFacade().getMatriculaAlunoHorarioTurma().consultarPorNomePessoa(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("identificadorTurma")) {
                objs = getFacade().getMatriculaAlunoHorarioTurma().consultarPorIdentificadorTurma(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("horarioTurma")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getMatriculaAlunoHorarioTurma().consultarPorHorarioTurma(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
    }

    public String excluir() {
        try {
            getFacade().getMatriculaAlunoHorarioTurma().excluir(matriculaAlunoHorarioTurmaVO);
            setMatriculaAlunoHorarioTurmaVO(new MatriculaAlunoHorarioTurmaVO());
            setMensagemID("msg_dados_excluidos");
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception{
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception{
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    public void montarListaSelectItemTurma(String prm) throws Exception {
        List resultadoConsulta = getFacade().getTurma().consultarPorIdentificador(prm, this.getMatriculaAlunoHorarioTurmaVO().getContrato().getEmpresa().getCodigo() ,false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            TurmaVO obj = (TurmaVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getIdentificador().toString()));
        }
        setListaSelectItemTurma(objs);
    }

    public void montarListaSelectItemPessoa(String prm) throws Exception {
        List resultadoConsulta = getFacade().getPessoa().consultarPorNome(prm, false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            PessoaVO obj = (PessoaVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getNome().toString()));
        }
        setListaSelectItemPessoa(objs);
    }

    public void montarListaSelectItemContrato(String prm) throws Exception {
        List resultadoConsulta = getFacade().getContrato().consultarPorSituacao(prm, false, Uteis.NIVELMONTARDADOS_TODOS);
        Iterator i = resultadoConsulta.iterator();
        List objs = new ArrayList();
        objs.add(new SelectItem(new Integer(0), ""));
        while (i.hasNext()) {
            ContratoVO obj = (ContratoVO)i.next();
            objs.add(new SelectItem(obj.getCodigo(), obj.getSituacao().toString()));
        }
        setListaSelectItemContrato(objs);
    }

    public void inicializarListasSelectItemTodosComboBox() throws Exception {
        montarListaSelectItemContrato("");
        montarListaSelectItemPessoa("");
        montarListaSelectItemTurma("");
    }

    public List getTipoConsultaCombo() {
        List itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("situacaoContrato", "Contrato"));
        itens.add(new SelectItem("nomePessoa", "Pessoa"));
        itens.add(new SelectItem("identificadorTurma", "Turma"));
        itens.add(new SelectItem("horarioTurma", "Horário Turma"));
        return itens;
    }

    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    public List getListaSelectItemTurma() {
        return (listaSelectItemTurma);
    }
     
    public void setListaSelectItemTurma( List listaSelectItemTurma ) {
        this.listaSelectItemTurma = listaSelectItemTurma;
    }

    public List getListaSelectItemPessoa() {
        return (listaSelectItemPessoa);
    }
     
    public void setListaSelectItemPessoa( List listaSelectItemPessoa ) {
        this.listaSelectItemPessoa = listaSelectItemPessoa;
    }

    public List getListaSelectItemContrato() {
        return (listaSelectItemContrato);
    }
     
    public void setListaSelectItemContrato( List listaSelectItemContrato ) {
        this.listaSelectItemContrato = listaSelectItemContrato;
    }

    public MatriculaAlunoHorarioTurmaVO getMatriculaAlunoHorarioTurmaVO() {
        return matriculaAlunoHorarioTurmaVO;
    }
     
    public void setMatriculaAlunoHorarioTurmaVO(MatriculaAlunoHorarioTurmaVO matriculaAlunoHorarioTurmaVO) {
        this.matriculaAlunoHorarioTurmaVO = matriculaAlunoHorarioTurmaVO;
    }
}