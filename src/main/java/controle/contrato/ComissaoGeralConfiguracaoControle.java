package controle.contrato;

import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.utilitarias.UteisValidacao;

public class ComissaoGeralConfiguracaoControle extends SuperControle {

    private ComissaoGeralConfiguracaoVO comissaoVO = new ComissaoGeralConfiguracaoVO();
    private List<ContratoDuracaoVO> duracoesPossiveis = new ArrayList<ContratoDuracaoVO>();
    private String[] situacaoCadastrar = new String[0];
    private boolean outraDuracao = false;
    private boolean mostrarComissaoPorMeta= false;
    private String msgAlert;


    public ComissaoGeralConfiguracaoControle() throws Exception {
        setDuracoesPossiveis(getListaDuracoesContratos());
        montarListaEmpresas();
    }

    public List<ContratoDuracaoVO> getListaDuracoesContratos() throws Exception {
        return getFacade().getContratoDuracao().consultarNumeroMeses();
    }

    public Object novo() throws Exception{
        setComissaoVO(new ComissaoGeralConfiguracaoVO());
        getComissaoVO().setNovoObj(true);
        situacaoCadastrar = new String[0];
        limparSelecaoduracoes();
        limparMsg();
        if (getEmpresaLogado() != null){
            mostrarComissaoPorMeta = getEmpresaLogado().isPagarComissaoSeAtingirMetaFinanceira();
            if (mostrarComissaoPorMeta){
               getComissaoVO().setListaComissaoMeta(ComissaoMetaFinananceiraVO.criarListaComissaoMeta());
            }
        }
        return "editar";
    }

    public void verificarComissaoMeta(){
        try{
            if ((this.comissaoVO.getEmpresa().getCodigo() != null) && (this.comissaoVO.getEmpresa().getCodigo() > 0)){
                EmpresaVO empresaVO = getFacade().getEmpresa().consultarPorCodigo(this.comissaoVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                mostrarComissaoPorMeta = empresaVO.isPagarComissaoSeAtingirMetaFinanceira();
                if ((mostrarComissaoPorMeta) && ((getComissaoVO().getListaComissaoMeta() == null) || (getComissaoVO().getListaComissaoMeta().size() <= 0))){
                    getComissaoVO().setListaComissaoMeta(ComissaoMetaFinananceiraVO.criarListaComissaoMeta());
                }
            }else{
                if (getEmpresaLogado().isPagarComissaoSeAtingirMetaFinanceira()){
                    mostrarComissaoPorMeta = true;
                    getComissaoVO().setListaComissaoMeta(ComissaoMetaFinananceiraVO.criarListaComissaoMeta());
                }else{
                    mostrarComissaoPorMeta = false;
                }
            }
            limparMsg();
        }catch (Exception e){
            montarErro(e);
        }
    }

    public void excluirTelaLista() {
        try {
            Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimariaexc"));
            ComissaoGeralConfiguracaoVO obj = getFacade().getComissaoGeralConfiguracao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            getFacade().getComissaoGeralConfiguracao().excluir(obj);
            try {
                obj.setObjetoVOAntesAlteracao(new ComissaoGeralConfiguracaoVO());
                obj.setNovoObj(true);
                registrarLogExclusaoTodosDadosObjetoVO(obj, obj.getCodigo(), "COMISSAOGERALCONFIGURACAO", 0);
            } catch (Exception e) {
                registrarLogErroObjetoVO("COMISSAOGERALCONFIGURACAO", comissaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE COMISSAOGERALCONFIGURACAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                e.printStackTrace();
            }
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void excluirTodasAsTaxas() {
        try {
            List<ComissaoGeralConfiguracaoVO> objs = getFacade().getComissaoGeralConfiguracao().consultarPorEmpresa(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for(ComissaoGeralConfiguracaoVO obj : objs){
                try {
                    getFacade().getComissaoGeralConfiguracao().excluir(obj);
                    obj.setObjetoVOAntesAlteracao(new ComissaoGeralConfiguracaoVO());
                    obj.setNovoObj(true);
                    registrarLogExclusaoTodosDadosObjetoVO(obj, obj.getCodigo(), "COMISSAOGERALCONFIGURACAO", 0);
                } catch (Exception e) {
                    registrarLogErroObjetoVO("COMISSAOGERALCONFIGURACAO", comissaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE COMISSAOGERALCONFIGURACAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
                    e.printStackTrace();
                }
            }
            montarSucesso("msg_dados_excluidos");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public Object editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ComissaoGeralConfiguracaoVO obj = getFacade().getComissaoGeralConfiguracao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            mostrarComissaoPorMeta = obj.getEmpresa().isPagarComissaoSeAtingirMetaFinanceira();
            if ((mostrarComissaoPorMeta) && (obj.getListaComissaoMeta().isEmpty())){
                obj.setListaComissaoMeta(ComissaoMetaFinananceiraVO.criarListaComissaoMeta());
            }
            obj.setNovoObj(false);
            setComissaoVO(obj);
            comissaoVO.registrarObjetoVOAntesDaAlteracao();

            setMensagemDetalhada("", "");
            setMensagemID("msg_dados_editar");
            setErro(false);
            setSucesso(true);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    public String gravar() {
        try {
            if(UteisValidacao.emptyNumber(getComissaoVO().getEmpresa().getCodigo())){
                throw new ConsistirException("Informe uma empresa!");
            }
            if((situacaoCadastrar.length == 0 && getComissaoVO().isNovoObj()) || (!getComissaoVO().isNovoObj() && UteisValidacao.emptyString(comissaoVO.getSituacao()))){
                throw new ConsistirException("Informe ao menos uma situação!");
            }
            if (getComissaoVO().getVigenciaFinal() == null){
                throw new Exception("Informe a Vigência Inicial.");
            }
            if (getComissaoVO().getVigenciaInicio() == null){
                throw new Exception("Informe a Vigência Final.");
            }

            getComissaoVO().setEmpresa(getFacade().getEmpresa().consultarPorCodigo(getComissaoVO().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (getComissaoVO().isNovoObj()) {
                List<ComissaoGeralConfiguracaoVO> listaInserir = new ArrayList<ComissaoGeralConfiguracaoVO>();
                List<ComissaoGeralConfiguracaoVO> listaErros = new ArrayList<ComissaoGeralConfiguracaoVO>();
                boolean algumaDuracaoSelecionada = false;
                for (String situacao : situacaoCadastrar) {
                    if (!outraDuracao) {
                        for (ContratoDuracaoVO duracao : getDuracoesPossiveis()) {
                            if (duracao.getSelecionado()) {
                                prepararComissao(listaInserir, listaErros, situacao, duracao.getNumeroMeses());
                                algumaDuracaoSelecionada = true;
                            }
                        }
                    } else {
                        prepararComissao(listaInserir, listaErros, situacao, getComissaoVO().getDuracao());
                        algumaDuracaoSelecionada = true;
                    }
                }
                if(!algumaDuracaoSelecionada){
                    throw new ConsistirException("Informe ao menos uma opção de Duração!");
                }
                if (listaErros.isEmpty()) {
                    for (ComissaoGeralConfiguracaoVO comissao : listaInserir) {
                        getFacade().getComissaoGeralConfiguracao().incluir(comissao);
                        incluirLogInclusao(comissao);
                    }
                } else {
                    StringBuilder errosParaMostrar = new StringBuilder();
                    for (ComissaoGeralConfiguracaoVO comissao : listaErros) {
                        errosParaMostrar.append(" ").append(comissao.getSituacaoApresentar()).append(" - ");
                        errosParaMostrar.append(comissao.getDuracao()).append(": de ");
                        errosParaMostrar.append(Uteis.getDataAplicandoFormatacao(comissao.getVigenciaInicio(), "MM/yyyy")).append(" até ");
                        errosParaMostrar.append(Uteis.getDataAplicandoFormatacao(comissao.getVigenciaFinal(), "MM/yyyy")).append(",");
                    }
                    errosParaMostrar.deleteCharAt(errosParaMostrar.length() - 1);
                    throw new ConsistirException("Conflitos em: " + errosParaMostrar.toString());
                }
            } else {
                if (!getFacade().getComissaoGeralConfiguracao().existe(getComissaoVO())) {
                    getFacade().getComissaoGeralConfiguracao().alterar(getComissaoVO());
                    incluirLogAlteracao();
                } else {
                    throw new ConsistirException("Não é possível editar, pois já existe uma configuração que conflita com essa.");
                }
            }
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_gravados");
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "editar";
        }
    }

    private void prepararComissao(List<ComissaoGeralConfiguracaoVO> listaInserir, List<ComissaoGeralConfiguracaoVO> listaErros, String situacao, Integer nrMeses) throws Exception {
        ComissaoGeralConfiguracaoVO comissao = new ComissaoGeralConfiguracaoVO();
        comissao.setDuracao(nrMeses);
        comissao.setSituacao(situacao);
        comissao.setValorFixoEspontaneo(getComissaoVO().getValorFixoEspontaneo());
        comissao.setPorcentagemEspontaneo(getComissaoVO().getPorcentagemEspontaneo());
        comissao.setValorFixoAgendado(getComissaoVO().getValorFixoAgendado());
        comissao.setPorcentagemAgendado(getComissaoVO().getPorcentagemAgendado());
        comissao.setVigenciaInicio(getComissaoVO().getVigenciaInicio());
        comissao.setVigenciaFinal(getComissaoVO().getVigenciaFinal());
        comissao.setEmpresa(getComissaoVO().getEmpresa());
        if (comissao.getEmpresa().isPagarComissaoSeAtingirMetaFinanceira()){
            comissao.setListaComissaoMeta(getComissaoVO().getListaComissaoMeta());
        }
        if (!getFacade().getComissaoGeralConfiguracao().existe(comissao)) {
            listaInserir.add(comissao);
        } else {
            listaErros.add(comissao);
        }
    }

    public ComissaoGeralConfiguracaoVO getComissaoVO() {
        return comissaoVO;
    }

    public void setComissaoVO(ComissaoGeralConfiguracaoVO comissaoVO) {
        this.comissaoVO = comissaoVO;
    }

    public String excluir() {
        try {
            getFacade().getComissaoGeralConfiguracao().excluir(getComissaoVO());
            incluirLogExclusao();
            novo();
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_excluidos");
            return "consultar";
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"comissaogeralconfiguracao\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"comissaogeralconfiguracao\" violates foreign key")){
                setMensagemDetalhada("Esta comissão não pode ser excluída, pois já foi utilizada!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            return "editar";
        }
    }

    public List<ContratoDuracaoVO> getDuracoesPossiveis() {
        return duracoesPossiveis;
    }

    public void setDuracoesPossiveis(List<ContratoDuracaoVO> duracoesPossiveis) {
        this.duracoesPossiveis = duracoesPossiveis;
    }

    public String[] getSituacaoCadastrar() {
        return situacaoCadastrar;
    }

    public void setSituacaoCadastrar(String[] situacaoCadastrar) {
        this.situacaoCadastrar = situacaoCadastrar;
    }

    public boolean isOutraDuracao() {
        return outraDuracao;
    }

    public void setOutraDuracao(boolean outraDuracao) {
        this.outraDuracao = outraDuracao;
    }


    public boolean isMostrarComissaoPorMeta() {
        return mostrarComissaoPorMeta;
    }

    public void setMostrarComissaoPorMeta(boolean mostrarComissaoPorMeta) {
        this.mostrarComissaoPorMeta = mostrarComissaoPorMeta;
    }
    
    public void incluirLogInclusao(ComissaoGeralConfiguracaoVO comissao) throws Exception {
        try {
            comissao.setObjetoVOAntesAlteracao(new ComissaoGeralConfiguracaoVO());
            comissao.setNovoObj(true);
            registrarLogObjetoVO(comissao, comissao.getCodigo(), "COMISSAOGERALCONFIGURACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMISSAOGERALCONFIGURACAO", comissao.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE COMISSAOGERALCONFIGURACAO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        comissao.setNovoObj(new Boolean(false));
        comissao.registrarObjetoVOAntesDaAlteracao();
    }
    
    public void incluirLogExclusao() throws Exception {
        try {
            comissaoVO.setObjetoVOAntesAlteracao(new ComissaoGeralConfiguracaoVO());
            comissaoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(comissaoVO, comissaoVO.getCodigo(), "COMISSAOGERALCONFIGURACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMISSAOGERALCONFIGURACAO", comissaoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE COMISSAOGERALCONFIGURACAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    /**
     * Inclui o log de alteração de categoria
     * @throws Exception
     */
    public void incluirLogAlteracao() throws Exception {
        try {
            registrarLogObjetoVO(comissaoVO, comissaoVO.getCodigo(), "COMISSAOGERALCONFIGURACAO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("COMISSAOGERALCONFIGURACAO", comissaoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE COMISSAOGERALCONFIGURACAO ", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
       comissaoVO.registrarObjetoVOAntesDaAlteracao();
    }
    
     public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = comissaoVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(nomeClasse.toUpperCase(), comissaoVO.getCodigo(), 0);
    }
    public void realizarConsultaLogObjetoGeral() {
       comissaoVO = new ComissaoGeralConfiguracaoVO();
       realizarConsultaLogObjetoSelecionado();
    }

    private void limparSelecaoduracoes() {
        for(ContratoDuracaoVO duracaoVO : duracoesPossiveis){
            duracaoVO.setSelecionado(false);
        }
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Comissão",
                "Deseja excluir a Comissão?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}
