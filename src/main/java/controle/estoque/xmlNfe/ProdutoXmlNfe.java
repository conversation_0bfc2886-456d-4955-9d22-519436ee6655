package controle.estoque.xmlNfe;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ProdutoXmlNfe {

    @JacksonXmlProperty(localName = "cProd")
    private String cProd;

    @JacksonXmlProperty(localName = "xProd")
    private String descricao;

    @JacksonXmlProperty(localName = "qCom")
    private String quantidade;

    @JacksonXmlProperty(localName = "vUnCom")
    private String valorUnitario;

    @JacksonXmlProperty(localName = "cEAN")
    private String codigoDeBarras;

    @JacksonXmlProperty(localName = "vDesc")
    private Double desconto = 0d;

    @JacksonXmlProperty(localName = "CFOP")
    private String cfop;

    @JacksonXmlProperty(localName = "NCM")
    private String ncm;

    @JacksonXmlProperty(localName = "cEANTrib")
    private String cEANTrib;

    @JacksonXmlProperty(localName = "CEST")
    private String cest = "";

    public String getcProd() {
        return cProd;
    }

    public void setcProd(String cProd) {
        this.cProd = cProd;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(String quantidade) {
        this.quantidade = quantidade;
    }

    public String getValorUnitario() {
        return valorUnitario;
    }

    public void setValorUnitario(String valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public String getCodigoDeBarras() {
        return codigoDeBarras;
    }

    public void setCodigoDeBarras(String codigoDeBarras) {
        this.codigoDeBarras = codigoDeBarras;
    }

    public Double getDesconto() {
        return desconto;
    }

    public void setDesconto(Double desconto) {
        this.desconto = desconto;
    }

    public String getCfop() {
        return cfop;
    }

    public void setCfop(String cfop) {
        this.cfop = cfop;
    }

    public String getNcm() {
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public String getCest() {
        return cest;
    }

    public void setCest(String cest) {
        this.cest = cest;
    }

    public String getcEANTrib() {
        return cEANTrib;
    }

    public void setcEANTrib(String cEANTrib) {
        this.cEANTrib = cEANTrib;
    }
}