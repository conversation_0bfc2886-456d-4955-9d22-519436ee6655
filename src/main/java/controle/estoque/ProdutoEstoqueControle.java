/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.estoque;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.estoque.ProdutoEstoqueVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Uteis;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.faces.event.ActionEvent;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class ProdutoEstoqueControle extends SuperControle {

    private String NOME_ENTIDADE = "PRODUTO_ESTOQUE";
    protected List listaSelectItemEmpresa;
    protected List listaSelectItemCategoria;
    protected List listaSelectItemProduto;
    private ProdutoEstoqueVO produtoEstoqueVO;
    private List<SelectItem> listaSelectItemTodasCategoria = new ArrayList<SelectItem>();
    // Atributos para pesquisa.
    private Integer codigoCategoria;
    private Integer codigoEmpresa;
    private Integer codigoProduto;
    private String situacao = "A";
    private int tipoAdicionar = 1;
    private Integer estoqueMinimo;
    private boolean permiteGravar = true;
    private Set<ProdutoEstoqueVO> listaProdutoEstoqueVO;
    // Atributos para o suggestion Box de Produto.
    private String produtoSelecionado = "";

    public ProdutoEstoqueControle() throws Exception {
        inicializarDados();
    }

    private void montarListaSelectItemTodasCategoria() throws Exception {
        Map<Integer, String> simplificado = getFacade().getCategoriaProduto().consultarCategoriaSimplificado();
        listaSelectItemTodasCategoria = new ArrayList<SelectItem>();
        listaSelectItemTodasCategoria.add(new SelectItem(0, ""));
        Set<Integer> keySet = simplificado.keySet();
        for (Integer key : keySet) {
            listaSelectItemTodasCategoria.add(new SelectItem(key, simplificado.get(key)));
        }
    }

    private void inicializarDados() throws Exception {
        obterUsuarioLogado();
        this.codigoEmpresa = getEmpresaLogado().getCodigo();
        montarListaSelectItemEmpresa();
        preencherCombosProdutoECategoria();
        setControleConsulta(new ControleConsulta());
    }

    public void preencherCombosProdutoECategoria() throws Exception {
        montarListaSelectItemProduto();
        montarListaSelectItemCategoria();
        montarListaSelectItemTodasCategoria();
    }

    public String alterarSituacaoProdutoEstoque() {
        try {
            validarPermissaoAlterarSituacaoProdutoEstoque();
            ProdutoEstoqueVO obj = (ProdutoEstoqueVO) context().getExternalContext().getRequestMap().get("produtoEstoque");
            if (obj == null) {
                ProdutoEstoqueControle produtoCon = (ProdutoEstoqueControle) JSFUtilities.getFromSession(ProdutoEstoqueControle.class.getSimpleName());
                obj = produtoCon.getProdutoEstoqueVO();
            }
            getFacade().getProdutoEstoque().alterarSituacao(obj, getUsuarioLogado());
            registrarLogObjetoVO(obj,obj.getCodigo(),NOME_ENTIDADE,0);
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
        return "consultar";
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        List listaEmpresa = new ArrayList();
        listaEmpresa.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getEmpresa().consultarPorNome("",true, false, Uteis.NIVELMONTARDADOS_TODOS);
        for (Object obj : listaConsulta) {
            EmpresaVO empresa = (EmpresaVO) obj;
            listaEmpresa.add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
        setListaSelectItemEmpresa(listaEmpresa);
    }

    public void montarListaSelectItemProduto() throws Exception {
        List listaProduto = new ArrayList();
        listaProduto.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getProduto().consultarProdutosComControleEstoque(this.codigoEmpresa, null, "", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (Object obj : listaConsulta) {
            ProdutoVO produtoVO = (ProdutoVO) obj;
            listaProduto.add(new SelectItem(produtoVO.getCodigo(), produtoVO.getDescricao()));
        }
        setListaSelectItemProduto(listaProduto);
    }

    public void montarListaSelectItemCategoria() throws Exception {
        List listaCategoria = new ArrayList();
        listaCategoria.add(new SelectItem(new Integer(0), ""));
        List listaConsulta = getFacade().getCategoriaProduto().consultarCategoriaComControleEstoque(this.codigoEmpresa, "", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (Object obj : listaConsulta) {
            CategoriaProdutoVO categoriaProdutoVO = (CategoriaProdutoVO) obj;
            listaCategoria.add(new SelectItem(categoriaProdutoVO.getCodigo(), categoriaProdutoVO.getDescricao()));
        }
        setListaSelectItemCategoria(listaCategoria);
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ProdutoEstoqueCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            if (!getUsuarioLogado().getAdministrador()) {
                this.codigoEmpresa = getEmpresaLogado().getCodigo();
            }
            List<ProdutoEstoqueVO> lista = getFacade().getProdutoEstoque().consultar(codigoEmpresa, codigoProduto, codigoCategoria, this.situacao, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            objs.addAll(lista);

            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void abrirTelaProdutoEstoque() {
        try {
            notificarRecursoEmpresa(RecursoSistema.fromDescricao(FuncionalidadeSistemaEnum.CONFIGURAR_PRODUTO_ESTOQUE.toString()));
            validarPermissaoAdicionarProdutoEstoque();
            validarPermissaoAlterarSituacaoProdutoEstoque();
            setMsgAlert("abrirPopup('produtoEstoqueCons.jsp', 'ProdutoEstoque', 1000, 650);");
            abrirTelaConsulta();
        } catch (Exception e) {
            montarMsgAlert(e.getMessage());
        }
    }

    public void abrirTelaConsulta() {
        try {
            inicializarDados();
            setSucesso(true);
            setErro(false);
            this.produtoSelecionado = "";
            setListaConsulta(null);
            limparMsg();
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        limparMsg();
        return "consultar";
    }

    public void removerProdutoEstoqueDaLista() throws Exception {
        try {
            ProdutoEstoqueVO objProdutoEstoqueVO = (ProdutoEstoqueVO) context().getExternalContext().getRequestMap().get("produtoEstoqueItem");
            this.listaProdutoEstoqueVO.remove(objProdutoEstoqueVO);
            setMensagemID("msg_dados_excluidos");
            setMensagem("");
            setMensagemDetalhada("");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>ProdutoEstoqueVO</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        try {
            validarPermissaoAdicionarProdutoEstoque();
            this.estoqueMinimo = 0;
            this.codigoCategoria = 0;
            this.tipoAdicionar = 1;
            this.permiteGravar = true;
            this.produtoEstoqueVO = new ProdutoEstoqueVO();
            povoarProdutoEstoque();
            this.listaProdutoEstoqueVO = new HashSet<ProdutoEstoqueVO>();
            this.produtoSelecionado = "";
            produtoEstoqueVO.registrarObjetoVOAntesDaAlteracao();
            limparMsg();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>ProdutoEstoqueVO</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (listaProdutoEstoqueVO != null) {
                if (listaProdutoEstoqueVO.isEmpty()) {
                    throw new Exception("Antes de gravar, adicione ao menos um produto.");
                }
                getFacade().getProdutoEstoque().incluir(getListaProdutoEstoqueVOList(), getUsuarioLogado());
                for(ProdutoEstoqueVO prod : getListaProdutoEstoqueVO()){
                    registrarLogObjetoVO(prod,prod.getCodigo(),NOME_ENTIDADE,0, true);
                }
            } else {
                produtoEstoqueVO.setPontos(produtoEstoqueVO.getProduto().getQtdePontos());
                getFacade().getProdutoEstoque().alterar(produtoEstoqueVO);
                registrarLogObjetoVO(produtoEstoqueVO,produtoEstoqueVO.getCodigo(),NOME_ENTIDADE,0);
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            this.permiteGravar = false;
            preencherCombosProdutoECategoria();
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>ProdutoEstoqueVO</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            this.listaProdutoEstoqueVO = null;
            validarPermissaoAdicionarProdutoEstoque();
            setProdutoEstoqueVO(getFacade().getProdutoEstoque().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS));
            getProdutoEstoqueVO().setNovoObj(new Boolean(false));
            getProdutoEstoqueVO().registrarObjetoVOAntesDaAlteracao();
            this.permiteGravar = true;
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        }
        return "editar";
    }

    public void editarProdutoEstoqueDaLista() throws Exception {
        try {
            this.produtoEstoqueVO = (ProdutoEstoqueVO) context().getExternalContext().getRequestMap().get("produtoEstoqueItem");
            this.estoqueMinimo = this.produtoEstoqueVO.getEstoqueMinimo();
            this.produtoSelecionado = produtoEstoqueVO.getProduto().getDescricao();
            this.tipoAdicionar = 1;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public List getListaSelectItemEmpresa() {
        return listaSelectItemEmpresa;
    }

    public void setListaSelectItemEmpresa(List listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        String nomeClasse = produtoEstoqueVO.getClass().getSimpleName();
        nomeClasse = nomeClasse.substring(0, nomeClasse.length() - 2);
        loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + nomeClasse + "_tituloForm"));
        loginControle.consultarLogObjetoSelecionado(NOME_ENTIDADE, produtoEstoqueVO.getCodigo(), null);
    }
    public List<ProdutoVO> executarAutocompletePesqProduto(Object suggest) {
        List<ProdutoVO> listaProdutos = null;
        try {
            String nomePesq = (String) suggest;
            listaProdutos = getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(nomePesq, "PE", false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            listaProdutos.addAll(getFacade().getProduto().consultarPorDescricaoTipoProdutoAtivo(nomePesq, "OC", false, Uteis.NIVELMONTARDADOS_TELACONSULTA));
            setSucesso(true);
            setErro(false);
            setMensagem("");
            setMensagemDetalhada("");
            setMensagemID("msg_entre_dados");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return listaProdutos;
    }

    public void selecionarProduto() {
        ProdutoVO obj = (ProdutoVO) context().getExternalContext().getRequestMap().get("result");
        this.produtoEstoqueVO.setProduto(obj);
    }

    public String getProdutoSelecionado() {
        return produtoSelecionado;
    }

    public void setProdutoSelecionado(String produtoSelecionado) {
        this.produtoSelecionado = produtoSelecionado;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void validarPermissaoAdicionarProdutoEstoque() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "ConfigurarProdutoEstoque", "12.06 - Adicionar Produto ao Controle de Estoque");
            }
        }
    }

    public void validarPermissaoAlterarSituacaoProdutoEstoque() throws Exception {
        if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (getUsuarioLogado().getAdministrador()) {
                setMensagemDetalhada("");
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        for (Object o : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        getUsuarioLogado(), "AlterarSituacaoProdutoEstoque", "12.07 - Alterar Situação do Produto Estoque");
            }
        }
    }

    public Integer getCodigoCategoria() {
        return codigoCategoria;
    }

    public void setCodigoCategoria(Integer codigoCategoria) {
        this.codigoCategoria = codigoCategoria;
    }

    public Integer getCodigoProduto() {
        return codigoProduto;
    }

    public void setCodigoProduto(Integer codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public List getListaSelectItemCategoria() {
        return listaSelectItemCategoria;
    }

    public void setListaSelectItemCategoria(List listaSelectItemCategoria) {
        this.listaSelectItemCategoria = listaSelectItemCategoria;
    }

    public List getListaSelectItemProduto() {
        return listaSelectItemProduto;
    }

    public void setListaSelectItemProduto(List listaSelectItemProduto) {
        this.listaSelectItemProduto = listaSelectItemProduto;
    }

    public ProdutoEstoqueVO getProdutoEstoqueVO() {
        return produtoEstoqueVO;
    }

    public void setProdutoEstoqueVO(ProdutoEstoqueVO produtoEstoqueVO) {
        this.produtoEstoqueVO = produtoEstoqueVO;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public void adicionarProdutoEstoque() {
        try {
            if ((this.codigoEmpresa == null) || (this.codigoEmpresa.intValue() <= 0)) {
                throw new ConsistirException("O campo Empresa deve ser informado.");
            }
            if (this.tipoAdicionar == 1) {
                // Adicionar um produto ao controle de estoque
                povoarProdutoEstoque();
                ProdutoEstoqueVO.validarDados(this.produtoEstoqueVO);
                this.produtoEstoqueVO.setPontos(this.produtoEstoqueVO.getProduto().getQtdePontos());
                this.listaProdutoEstoqueVO.add(this.produtoEstoqueVO);

                this.produtoEstoqueVO = new ProdutoEstoqueVO();
                produtoEstoqueVO.registrarObjetoVOAntesDaAlteracao();
            } else {
                // Adicionar todos os produtos de uma categoria cujo o tipo do produto seja "PE"
                List<ProdutoVO> listaProduto = getFacade().getProduto().consultarPorDescricaoCategoriaTipoProdutoAtivo("", this.codigoCategoria, "PE", Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                for (ProdutoVO obj : listaProduto) {
                    this.produtoEstoqueVO = new ProdutoEstoqueVO();
                    povoarProdutoEstoque();
                    this.produtoEstoqueVO.setProduto(obj);
                    produtoEstoqueVO.registrarObjetoVOAntesDaAlteracao();
                    this.listaProdutoEstoqueVO.add(this.produtoEstoqueVO);
                }
            }
            produtoSelecionado = "";
            setSucesso(true);
            setErro(false);
            setMensagemID("msg_dados_adicionados");
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    private void povoarProdutoEstoque() throws Exception {
        // Consultar a empresa novamente para exibir o nome da empresa na datatable
        if ((this.codigoEmpresa != null) && (this.codigoEmpresa > 0)) {
            produtoEstoqueVO.setEmpresa(getFacade().getEmpresa().consultarPorChavePrimaria(this.codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }
        this.produtoEstoqueVO.setEstoqueMinimo(this.estoqueMinimo);
    }

    public Set<ProdutoEstoqueVO> getListaProdutoEstoqueVO() {
        return listaProdutoEstoqueVO;
    }

    public void setListaProdutoEstoqueVO(Set<ProdutoEstoqueVO> listaProdutoEstoqueVO) {
        this.listaProdutoEstoqueVO = listaProdutoEstoqueVO;
    }

    public int getTipoAdicionar() {
        return tipoAdicionar;
    }

    public void setTipoAdicionar(int tipoAdicionar) {
        this.tipoAdicionar = tipoAdicionar;
    }

    public Integer getEstoqueMinimo() {
        return estoqueMinimo;
    }

    public void setEstoqueMinimo(Integer estoqueMinimo) {
        this.estoqueMinimo = estoqueMinimo;
    }

    public List<ProdutoEstoqueVO> getListaProdutoEstoqueVOList() {
        return new ArrayList<ProdutoEstoqueVO>(this.listaProdutoEstoqueVO);
    }

    public boolean isPermiteGravar() {
        return permiteGravar;
    }

    public void setPermiteGravar(boolean permiteGravar) {
        this.permiteGravar = permiteGravar;
    }

    public List getListaSelectItemTodasCategoria() {
        return listaSelectItemTodasCategoria;
    }

    public void setListaSelectItemTodasCategoria(List listaSelectItemTodasCategoria) {
        this.listaSelectItemTodasCategoria = listaSelectItemTodasCategoria;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getProdutoEstoque().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }
    
    public void realizarConsultaLogObjetoGeral(){
        produtoEstoqueVO = new ProdutoEstoqueVO();
        produtoEstoqueVO.setCodigo(0);
        realizarConsultaLogObjetoSelecionado();
    }

    public void confirmarExcluir(){
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String obj = request.getParameter("metodochamar");

        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        if (obj.equals("alterarSituacaoProdutoEstoque")){
            control.init("Remoção de Produto",
                    "Deseja Remover Produto do Controle de Estoque?",
                    this, obj, "", "", "", "grupoLinkCancelarProdutoEstoque,form");
        }else {
            control.init("Adição de Produto",
                    "Confirma adicionar o produto ao controle de estoque?",
                    this, "alterarSituacaoProdutoEstoque", "", "", "", "grupoLinkCancelarProdutoEstoque,form");

        }

    }

}
