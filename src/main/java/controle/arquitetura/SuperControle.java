package controle.arquitetura;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.aas.authorization.entity.Autorizacao;
import br.com.pactosolucoes.aas.authorization.rules.AutorizacaoRegras;
import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import br.com.pactosolucoes.comuns.notificacao.EnfileiradorNotificadorRecursoSistemaSingleton;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.OrigemCadEAlteracaoCliente;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.itextpdf.html2pdf.HtmlConverter;
import com.pacto.config.dto.UsuarioSimplesDTO;
import controle.arquitetura.exceptions.SessaoExpiradaException;
import controle.arquitetura.security.AlgoritmoCriptoEnum;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.security.LogoutControle;
import controle.basico.ClienteControle;
import controle.basico.ColaboradorControle;
import controle.basico.FuncionalidadeControle;
import controle.basico.MenuAcessoFacilControle;
import controle.contrato.ContratoControle;
import controle.financeiro.AulaAvulsaDiariaControle;
import controle.financeiro.MovPagamentoControle;
import controle.financeiro.MovParcelaControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.PermissaoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.ConfiguracaoUsuarioEnum;
import negocio.comuns.basico.enumerador.FiltrosEnum;
import negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum;
import negocio.comuns.basico.enumerador.TimeZoneEnum;
import negocio.comuns.basico.enumerador.TipoEmpresaFinanceiro;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.plano.ModeloOrcamentoVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.utilitarias.AcessoException;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConfiguracaoSistemaUsuarioVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.ImageEmailHtml;
import negocio.comuns.utilitarias.OpcaoPerfilAcesso;
import negocio.comuns.utilitarias.OpcoesPerfilAcesso;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.oamd.CustomerSuccessTO;
import negocio.oamd.RedeEmpresaVO;
import org.json.JSONObject;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.notificador.NotificadorServiceControle;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.util.ThreadRequest;

import javax.faces.application.FacesMessage;
import javax.faces.application.ViewHandler;
import javax.faces.component.UICommand;
import javax.faces.component.UIPanel;
import javax.faces.component.UIViewRoot;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.imageio.ImageIO;
import javax.servlet.ServletContext;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.image.BufferedImage;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URISyntaxException;
import java.net.URL;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.TimeZone;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum.values;
import static org.apache.commons.lang.StringUtils.isBlank;

public class SuperControle extends FacadeManager implements Serializable {

    private Integer versaoBD = 2178;
    protected final String MSG_ERRO = "msg_erro";

    private String LINK_FACEBOOK_PACTO = "https://www.facebook.com/pactosolucoes";
    private String LINK_YOUTUBE = "https://www.youtube.com/pactosolucoes";
    private String LINK_TWITTER = "https://twitter.com/pactosolucoes";
    private String LINK_INSTAGRAM = "https://www.instagram.com/pactosolucoes";
    private String LINK_SITE = "https://www.pactosolucoes.com.br/?utm_source=sistema-zw&utm_medium=link-rodape";
    private String TEXTO_LINK_COMPARTILHAR;

    protected List listaConsulta;
    protected ControleConsulta controleConsulta;
    protected String mensagem = null;
    private String mensagemDetalhada = null;
    private Boolean atencao = false;
    private Boolean warning = false;
    private Boolean sucesso = false;
    private Boolean erro = false;
    private String mensagemID = null;
    protected UICommand apresentarPrimeiro = null;
    protected UICommand apresentarUltimo = null;
    protected UICommand apresentarAnterior = null;
    protected UICommand apresentarPosterior = null;
    private UIPanel apresentarLinha = null;
    private String paginaAtualDeTodas = "0/0";
    private UsuarioVO usuario = null;
    private EmpresaVO empresa = null;
    private PerfilAcessoVO perfilAcesso = null;
    //
    public static String Instance = System.getProperty("com.sun.aas.instanceName", "");

    protected static final String nomeInicialChaveEmbaralhamento = "PACTOAPRESENTACAO";
    //
    private Date dataVersao;
    private String dataVersaoComoString;
    private String msgAlert = "";
    private String msgAlertAuxiliar;
    private Boolean apresentarModalPanelSucesso = false;
    private String nothingLabel = "Pressione TAB para usar o Nome Informado.";
    public static final String Crypt_KEY = "P@cT0zIlLy0nW3b1";
    public static final AlgoritmoCriptoEnum Crypt_ALGORITM = AlgoritmoCriptoEnum.ALGORITMO_AES;
    public static final String Crypt_KEY_Contrato = "bmn6rjq1ehm1npv@ZYC";
    private static final String LOG_CHAVE_DIFERENTE_SESSAO = "LogChaveDiferenteSessao";
    private ConfPaginacao confPaginacao = new ConfPaginacao();
    protected int aasEntidade;
    protected String aasOperacao;
    protected int aasFuncionalidade;
    private Boolean apresentarSucessoEmail;
    private String ip = "";
    private String browser = "";
    private String urlBrowser;
    private Integer widthScreenClient = 0;
    private Integer heightScreenClient = 0;
    private String protocol = "";
    private String key = "";
    public static final int nrLimiteDestinatariosEmail = 100;
    private List<String> listaAtributosLista;
    //Find Key List
    private String valorPesquisa;
    private ArrayList listaManipulavel;
    private ArrayList listaOriginal;
    private String navigationCase;
    private boolean exibirMenuLateralEstudio = true;
    //
    private String versaoSistemaNFSe = "1.2.0";
    private List<SelectItem> listaEmpresas = new ArrayList<SelectItem>();
    private List<SelectItem> listaCategorias;
    private Boolean exibirModalFotoPendente = null;
    private String usandoMC = null;
    public static Integer quantidadePadraoDiasEnviarEmailParcelasVencidas = 5;
    public Map<String, String> mapaLog = null;
    public static String SUBDIRETORIO_ERRO_ABAS = "ErroAbas";
    public static String SUBDIRETORIO_UTILIZACAO_TELA = "UtilizacaoTela";
    private Boolean processandoOperacao;
    private List<EmpresaVO> listaEmpresasEstado = new ArrayList<EmpresaVO>();

    public static String chaveBuzzLead = "chIntegracaoBuzz";
    public static String LABEL_TODAS_EMPRESAS = "TODAS AS EMPRESAS";
    private HttpSession session;
    private String url;
    private Boolean enableSetLastActionTime = null;
    private String usernameContrato;
    private String cpf;
    public static final int MAXIMO_DIAS_EXTRA_CONCEDIDOS = 30;
    public String idioma = "";
    private Boolean pontoInterrogacaoHabilitado = null;
    private String zona = null;
    private boolean maxGpt = true;

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public SuperControle() {
        super();
        File arq = new File(this.getClass().getResource(this.getClass().getSimpleName() + ".class").getFile());
        dataVersao = negocio.comuns.utilitarias.Calendario.hoje();
        dataVersao.setTime(arq.lastModified());
        DateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", new Locale("pt", "BR"));
        dataVersaoComoString = sdf.format(dataVersao);
        maxGpt = PropsService.isTrue(PropsService.maxGpt);
    }

    public void redirect(String destino) throws IOException {
        ExternalContext ec = FacesContext.getCurrentInstance().getExternalContext();
        FacesContext.getCurrentInstance().getExternalContext().redirect(ec.getRequestContextPath() + destino);
    }

    public void redirectUrl(String url) throws IOException {
        ExternalContext ec = FacesContext.getCurrentInstance().getExternalContext();
        FacesContext.getCurrentInstance().getExternalContext().redirect(url);
    }


    protected FuncionalidadeControle getFuncionalidadeControle() {
        return (FuncionalidadeControle) obtenhaObjetoSessionMap(FuncionalidadeControle.class.getSimpleName());
    }

    protected Object obtenhaObjetoSessionMap(String chave) {
        return context().getExternalContext().getSessionMap().get(chave);
    }

    protected Object obtenhaObjetoRequestMap(String chave) {
        return context().getExternalContext().getRequestMap().get(chave);
    }

    protected boolean inicializarFacades() {
        return true;
    }

    protected void limparRecursosMemoria() {
        try {
            if (this.getListaConsulta() != null) {
                this.getListaConsulta().clear();
            }
            this.mensagem = null;
            this.mensagemDetalhada = null;
            this.mensagemID = null;
            this.apresentarPrimeiro = null;
            this.apresentarUltimo = null;
            this.apresentarAnterior = null;
            this.apresentarPosterior = null;
            this.apresentarLinha = null;
            this.paginaAtualDeTodas = "0/0";
            this.usuario = null;
            this.perfilAcesso = null;

            System.out.println("BACKING....: " + this.getClass().getSimpleName() + " RECURSOS LIBERADOS DA MEMÓRIA.");
        } catch (Exception ignored) {
        }
    }

    public void irParaTelaCliente(ClienteVO cliente) throws Exception {
        ClienteControle clienteControle = (ClienteControle) getControlador(ClienteControle.class.getSimpleName());
        clienteControle.prepararTelaCliente(cliente, true);
    }

    public void abrirTelaClienteRedirect(String matricula) throws Exception {
        redirect("/faces/cliente.jsp?matricula=" + matricula);
    }

    /**
     * Método que garante que o colaborador correto será mostrado na tela de
     * colaborador
     *
     * @param colaborador ColaboradorVO
     * @throws Exception
     */
    protected void irParaTelaColaborador(ColaboradorVO colaborador) throws Exception {
        ColaboradorControle colaboradorControle = (ColaboradorControle) context().getExternalContext().getSessionMap().get("ColaboradorControle");
        if (colaboradorControle == null) {
            colaboradorControle = new ColaboradorControle();
        }

        colaboradorControle.setColaboradorVO(colaborador);
        colaboradorControle.setPessoaVO(colaborador.getPessoa());
        context().getExternalContext().getSessionMap().put("ColaboradorControle", colaboradorControle);
    }

    protected HttpServletRequest request() {
        return (HttpServletRequest) context().getExternalContext().getRequest();
    }

    public Boolean isHttps() {
        if (getUrl() != null) {
            return getUrl().contains("https://");
        } else {
            return false;
        }
    }

    protected String url() {
        if (request() != null) {
            return request().getRequestURL().substring(0, request().getRequestURL().length() - request().getRequestURI().length()) + request().getContextPath();
        } else {
            return null;
        }
    }

    public String getUrl() {
        if (url == null || url.toLowerCase().contains("null")) {
            url = JSFUtilities.getFromSession("urlBrowser") + request().getContextPath();
        }
        return url;
    }

    public String getUrlImagemAlertaTodosOsModulos() {
        return (null != getUrl() && !getUrl().toLowerCase().contains("null")) ? getUrl() + "/faces/images/icon_alerta_vermelho.svg" : "/images/icon_alerta_vermelho.svg";
    }

    public void setUrl(String url) {
        this.url = url;
    }

    protected HttpServletResponse response() {
        return (HttpServletResponse) context().getExternalContext().getResponse();
    }

    public Object getControlador(String nomeControlador) {
        return JSFUtilities.getManagedBean(nomeControlador);
    }

    public <T> T getControlador(Class<? extends T> classControlador) {
        return (T) JSFUtilities.getManagedBean(classControlador.getSimpleName());
    }

    /**
     * Retorna o contralor já tipado na classe que se esta passando como par?metro
     *
     * @param classControlador {@link Class} que será pesquisada.
     * @param <T>              Tipo genérico da {@link Class}
     * @return
     */
    public <T> T getControladorTipado(Class<T> classControlador) {
        return (T) JSFUtilities.getManagedBean(classControlador.getSimpleName());
    }

    public void liberarBackingBeanMemoria(String nomeBackingBean) {
        try {
            this.limparRecursosMemoria();
            removerManagedBean(nomeBackingBean);
            System.out.println("BACKING....: " + this.getClass().getSimpleName() + " REMOVIDO DA MEMÓRIA");
        } catch (Exception e) {
            System.out.println("Nao conseguimos remover o Backing da Memória (" + e.getMessage() + ") " + this.getClass().getSimpleName());
            Uteis.logarDebug("#### CONTRATOCONTROLE liberarBackingBeanMemoria: " + e.getMessage());
            e.printStackTrace();
        }
    }

    protected void removerManagedBean(String nomeManagedBean) {
        Object obj = context().getExternalContext().getSessionMap().remove(nomeManagedBean);
        if (obj != null) {
            obj = null;
        }
    }

    protected void removerManagedRequest(String nomeManagedBean) {
        context().getExternalContext().getRequestMap().remove(nomeManagedBean);
    }

    protected FacesContext context() {
        return (FacesContext.getCurrentInstance());
    }

    protected ServletContext getServletContext() {
        return (ServletContext) context().getExternalContext().getContext();
    }

    public UsuarioVO getUsuarioLogado() throws Exception {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getUsuario();
        } catch (Exception e) {
            return null;
        }
    }

    public Boolean getApresentarTrocaDeUnidades() throws Exception {
        return !getUsuarioLogado().getAdministrador() && (getUsuarioLogado().getUsuarioPerfilAcessoVOs().size() > 1 || getUsuarioLogado().isUsuarioMultiChave());
    }

    public String getEmailUsuarioLogado() throws Exception {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if(loginControle != null && loginControle.getUsuario().getEmail() == null) {
                loginControle.getUsuario().setEmail(getFacade().getUsuario().consultarEmailUsuarioEmail(loginControle.getUsuario().getCodigo()));
            }
            if (loginControle != null && loginControle.getUsuario() != null) {
                return loginControle.getUsuario().getEmail();
            }
            return "";
        } catch (Exception e) {
            return null;
        }
    }

    public UsuarioVO getColaboradorResponsavel() throws Exception {
        return (UsuarioVO) context().getExternalContext().getSessionMap().get("ColaboradorResponsavel");
    }

    public void setColaboradorResponsavel(UsuarioVO usuario) throws Exception {
        context().getExternalContext().getSessionMap().put("ColaboradorResponsavel", usuario);
    }

    public AberturaMetaVO getAberturaMeta() throws Exception {
        return (AberturaMetaVO) context().getExternalContext().getSessionMap().get("AberturaMeta");
    }

    public void setAberturaMeta(AberturaMetaVO abertura) throws Exception {
        context().getExternalContext().getSessionMap().put("AberturaMeta", abertura);
    }

    public String getNomeEmpresaLogada() {
        try {
            return context() != null && getEmpresaLogado() != null && getEmpresaLogado().getCodigo() > 0 ? getEmpresaLogado().getNome() : "";
        } catch (Exception ignored) {
        }
        return "";
    }

    public String getNomeEmpresaLogadaMinusculo() {
        try {
            return getNomeEmpresaLogada().toLowerCase();
        } catch (Exception ignored) {
        }
        return "";
    }

    public EmpresaVO getEmpresaLogado() throws Exception {
        if(context() == null && getEmpresa() != null){
            return getEmpresa();
        }
        if (context() != null) {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getEmpresa();
        } else {
            return new EmpresaVO();
        }
    }

    public String getEmpresaLogadoTrocaEmpresa() throws Exception {
        return this.getEmpresaLogado().getNome() + (UteisValidacao.emptyString(this.getEmpresaLogado().getCodigoRede()) ? "" : (" - " + this.getEmpresaLogado().getCodigoRede()));
    }

    public boolean estahDeslogado() throws Exception {
        return getEmpresaLogado().getCodigo().equals(0);
    }

    public UsuarioVO obterUsuarioLogado() throws Exception {
        if (context() != null) {
            UsuarioVO u = (UsuarioVO) JSFUtilities.getFromSession(LoginControle.NomeChaveUsuarioSessao);
            //verificar cookie
            if (u == null || isBlank(u.getSenha()) || isBlank(u.getUsername())) {
                Cookie c = JSFUtilities.recuperarCookie(JSFUtilities.COOKIE_CREDENTIALS_FAILOVER);
                if (c != null) {
                    JSONObject json = JSFUtilities.recuperarSessaoFailOver(c, request(), response());
                    if (json != null) {
                        u = (UsuarioVO) JSFUtilities.getFromSession(LoginControle.NomeChaveUsuarioSessao);
                    }
                }
            }
            if ((u == null) || isBlank(u.getUsername())) {
                throw new SessaoExpiradaException("Houve uma falha em suas credenciais e por segurança, sua sessão foi finalizada.");
            }
            return u;
        }
        return null;
    }

    public String obterNomeUsuarioLogado() throws Exception {
        try {
            return obterUsuarioLogado().getNome();
        } catch (Exception e) {
            return null;
        }
    }

    public String obterCaminhoWebAplicacao() throws Exception {
        ServletContext servletContext = (ServletContext) this.context().getExternalContext().getContext();
        String caminhoBaseAplicacao = servletContext.getRealPath(this.context().getExternalContext().getRequestContextPath());
        File caminhoBaseAplicacaoFile = new File(caminhoBaseAplicacao);
        caminhoBaseAplicacao = caminhoBaseAplicacaoFile.getParent();
        return caminhoBaseAplicacao + File.separator + "relatorio";
    }

    public String obterCaminhoWebAplicacaoFoto() throws Exception {
        return ((ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext()).getRealPath("");
    }

    public String alterarLingua_En() {
        Locale local = new Locale("en", "US");
        context().getApplication().setDefaultLocale(local);
        context().getViewRoot().setLocale(local);
        return "alterarLingua";
    }

    public String alterarLingua_Pt() {
        Locale local = new Locale("pt", "BR");
        context().getApplication().setDefaultLocale(local);
        context().getViewRoot().setLocale(local);
        return "alterarLingua";
    }

    protected static ClassLoader getCurrentLoader(Object fallbackClass) {
        ClassLoader loader = Thread.currentThread().getContextClassLoader();
        if (loader == null) {
            loader = fallbackClass.getClass().getClassLoader();
        }
        return loader;
    }

    protected String getMensagemInternalizacao(String mensagemID) {
        String mensagem = "(" + mensagemID + ") Mensagem não localizada nas propriedades de internalização.";
        ResourceBundle bundle;
        Locale locale;
        String nomeBundle = context().getApplication().getMessageBundle();
        if (nomeBundle != null) {
            locale = context().getViewRoot().getLocale();
            bundle = ResourceBundle.getBundle(nomeBundle, locale, getCurrentLoader(nomeBundle));
            try {
                mensagem = bundle.getString(mensagemID);
                return mensagem;
            } catch (MissingResourceException e) {
                return mensagem;
            }
        }
        return mensagem;
    }

    protected String consultar() throws Exception {
        if (getControleConsulta() == null) {
            setControleConsulta(new ControleConsulta());
        }
        getControleConsulta().definirProximaPaginaApresentar(getControleConsulta().getPaginaAtual());
        return "consultar";
    }

    public void definirVisibilidadeLinksNavegacao(int paginaAtual, int nrTotalPaginas) {
        setPaginaAtualDeTodas(paginaAtual + "/" + nrTotalPaginas);
        if (apresentarPrimeiro == null || apresentarAnterior == null || apresentarPosterior == null || apresentarUltimo == null) {
            return;
        }
        if (paginaAtual == 1 && nrTotalPaginas > 1) {
            apresentarPrimeiro.setRendered(false);
            apresentarAnterior.setRendered(false);
            apresentarPosterior.setRendered(true);
            apresentarUltimo.setRendered(true);
        } else if (paginaAtual == 1 && nrTotalPaginas == 1) {
            apresentarPrimeiro.setRendered(false);
            apresentarAnterior.setRendered(false);
            apresentarPosterior.setRendered(false);
            apresentarUltimo.setRendered(false);
        } else if (paginaAtual > 1 && paginaAtual < nrTotalPaginas) {
            apresentarPrimeiro.setRendered(true);
            apresentarAnterior.setRendered(true);
            apresentarPosterior.setRendered(true);
            apresentarUltimo.setRendered(true);
        } else if (paginaAtual == nrTotalPaginas && paginaAtual != 0) {
            apresentarPrimeiro.setRendered(true);
            apresentarAnterior.setRendered(true);
            apresentarPosterior.setRendered(false);
            apresentarUltimo.setRendered(false);
        } else if (nrTotalPaginas == 0) {
            setPaginaAtualDeTodas("0/0");
            apresentarPrimeiro.setRendered(false);
            apresentarAnterior.setRendered(false);
            apresentarPosterior.setRendered(false);
            apresentarUltimo.setRendered(false);
        }
    }

    public String getMensagem() {
        if ((getMensagemID() != null) && (!getMensagemID().equals(""))) {
            mensagem = getMensagemInternalizacao(getMensagemID());
        }
        return mensagem;
    }

    public void montarMensagemExceptionControle(String mensagemID, Exception ex) {
        MensagemExceptionControle mensagemExceptionControle = (MensagemExceptionControle) JSFUtilities.getManagedBean("MensagemExceptionControle");
        if (mensagemExceptionControle == null) {
            mensagemExceptionControle = new MensagemExceptionControle();
        }
        mensagemExceptionControle.init(ex);
        mensagemExceptionControle.setMensagemID(mensagemID);
    }

    public void montarMensagemExceptionControle(String mensagemID) {
        montarMensagemExceptionControle(mensagemID, null);
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String tratarMensagemErroDetalhada(String mensagemDetalhada) {
        String novaMensagem = mensagemDetalhada;
        if (novaMensagem == null) {
            return "";
        }
        if ((novaMensagem.indexOf("duplicar chave viola") != -1)
                || (novaMensagem.indexOf("duplicate key") != -1)) {
            if (novaMensagem.lastIndexOf("_") != -1) {
                String campo = novaMensagem.substring(novaMensagem.lastIndexOf("_") + 1, novaMensagem.length() - 1);
                novaMensagem = "Já existe um registro com este valor para o campo " + campo.toUpperCase() + ".";
            } else {
                novaMensagem = "Já existe um registro gravados com estes valores (" + mensagemDetalhada + ")";
            }
        }
        if ((novaMensagem.indexOf("violates foreign key constraint") != -1)
                || (novaMensagem.indexOf("de chave estrangeira") != -1)) {
            novaMensagem = "Esse registro está sendo utilizado por outro cadastro e não pode ser excluído ou modificado!";// + mensagemDetalhada;
        }
        if (mensagemDetalhada.indexOf("planocomposicao") != -1 || mensagemDetalhada.indexOf("fk_planocomposicao_composicao") != -1) {
            novaMensagem = "Esse pacote está sendo utilizado em um ou mais contratos e não pode ser excluído!";
        }
        return novaMensagem;
    }

    public void setMensagemDetalhada(String mensagemID, String mensagemDetalhada) {
        this.mensagemID = mensagemID;
        this.mensagemDetalhada = tratarMensagemErroDetalhada(mensagemDetalhada);
    }

    public String getMensagemDetalhada() {
        if (mensagemDetalhada == null)
            mensagemDetalhada = "";
        return mensagemDetalhada;
    }

    public void setMensagemDetalhada(String mensagemDetalhada) {
        this.mensagemDetalhada = mensagemDetalhada;
    }

    public void setMensagemDetalhada(Exception ex) {
        if (ex == null || ex.getMessage() == null) {
            this.mensagemDetalhada = NullPointerException.class.getName();
        } else {
            this.mensagemDetalhada = ex.getMessage();
        }
    }

    public String getMensagemID() {
        if (mensagemID == null)
            mensagemID = "";
        return mensagemID;
    }

    public void setMensagemID(String mensagemID) {
        this.mensagemID = mensagemID == null || mensagemID.isEmpty() ? "" : mensagemID;
        this.mensagemDetalhada = "";
    }

    public List getListaConsulta() {
        if (listaConsulta == null) {
            listaConsulta = new ArrayList<>();
        }
        return listaConsulta;
    }

    public void setListaConsulta(List listaConsulta) {
        this.listaConsulta = listaConsulta;
    }

    public ControleConsulta getControleConsulta() {
        return controleConsulta;
    }

    public void setControleConsulta(ControleConsulta controleConsulta) {
        this.controleConsulta = controleConsulta;
    }

    public UICommand getApresentarPrimeiro() {
        return apresentarPrimeiro;
    }

    public void setApresentarPrimeiro(UICommand apresentarPrimeiro) {
        this.apresentarPrimeiro = apresentarPrimeiro;
    }

    public UICommand getApresentarUltimo() {
        return apresentarUltimo;
    }

    public void setApresentarUltimo(UICommand apresentarUltimo) {
        this.apresentarUltimo = apresentarUltimo;
    }

    public UICommand getApresentarAnterior() {
        return apresentarAnterior;
    }

    public void setApresentarAnterior(UICommand apresentarAnterior) {
        this.apresentarAnterior = apresentarAnterior;
    }

    public UICommand getApresentarPosterior() {
        return apresentarPosterior;
    }

    public void setApresentarPosterior(UICommand apresentarPosterior) {
        this.apresentarPosterior = apresentarPosterior;
    }

    public String getPaginaAtualDeTodas() {
        return paginaAtualDeTodas;
    }

    public void setPaginaAtualDeTodas(String paginaAtualDeTodas) {
        this.paginaAtualDeTodas = paginaAtualDeTodas;
    }

    public UIPanel getApresentarLinha() {
        return apresentarLinha;
    }

    public void setApresentarLinha(UIPanel apresentarLinha) {
        this.apresentarLinha = apresentarLinha;
    }

    public UsuarioVO getUsuario() throws Exception {
        if (usuario == null) {
            return getUsuarioLogado();
        }
        return usuario;
    }

    private void pesquisarColaboradorEmpresaLogada() throws Exception {
        ColaboradorVO colaboradorVO = getUsuario().getColaboradorVO();
        if ((getUsuario().getColaboradorVO() != null) && (getUsuario().getColaboradorVO().getCodigo() != null) &&
                (getUsuario().getColaboradorVO().getCodigo() > 0)) {
            // O colaborador tem somente um cadastro de usu?rio e um cadastro de colaborador para cada empresa.
            colaboradorVO = getFacade().getColaborador().consultarPorCodigoPessoa(getUsuario().getColaboradorVO().getPessoa().getCodigo(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN);
        }
        if (!UteisValidacao.emptyNumber(colaboradorVO.getCodigo())) {
            getUsuario().setColaboradorVO(colaboradorVO);
            if (getUsuario().getColaboradorVO().getPessoa() != null && getUsuario().getColaboradorVO().getPessoa().getCodigo() >0) {
                getUsuario().getColaboradorVO().setPessoa(getFacade().getPessoa().consultarPorChavePrimaria(getUsuario().getColaboradorVO().getPessoa().getCodigo(),Uteis.NIVELMONTARDADOS_LOGIN));
            }
        }
    }

    public void setUsuario(UsuarioVO aUsuario) throws Exception {
        usuario = aUsuario;
        pesquisarColaboradorEmpresaLogada();
    }

    public PerfilAcessoVO getPerfilAcesso() {
        return perfilAcesso;
    }

    public void setPerfilAcesso(PerfilAcessoVO aPerfilAcesso) {
        perfilAcesso = aPerfilAcesso;
    }

    public String getVersaoSistema() {
        return PropsService.getPropertyValue(PropsService.VERSAO_SISTEMA);
    }

    public String getInstance() {
        if (Instance != null && !Instance.isEmpty()) {
            return Instance.replaceAll("[^0-9]", "");
        }
        return Instance;
    }

    public String getVersaoSistemaNFSe() {
        return versaoSistemaNFSe;
    }

    public void setVersaoSistemaNFSe(String versaoSistemaNFSe) {
        this.versaoSistemaNFSe = versaoSistemaNFSe;
    }

    public Integer getVersaoBD() {
        return versaoBD;
    }

    public void setVersaoBD(Integer versaoBD) {
        this.versaoBD = versaoBD;
    }

    public boolean getApresentarResultadoConsulta() {
        return (this.getListaConsulta() != null);
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Boolean getErro() {
        return erro;
    }

    public void setErro(Boolean erro) {
        this.erro = erro;
    }

    public Boolean getSucesso() {
        return sucesso;
    }

    public void setSucesso(Boolean sucesso) {
        this.sucesso = sucesso;
    }

    public Boolean getAtencao() {
        return atencao;
    }

    public void setAtencao(Boolean atencao) {
        this.atencao = atencao;
    }

    public Boolean getWarning() {
        return warning;
    }

    public void setWarning(Boolean warning) {
        this.warning = warning;
    }

    public long getTimeStamp() {
        return System.currentTimeMillis();
    }

    public boolean isFotosNaNuvem() {
        return PropsService.isTrue(PropsService.fotosParaNuvem);
    }

    public boolean isAtivarGoogleAnalytics() {
        return PropsService.isTrue(PropsService.ativarGoogleAnalytics);
    }

    public boolean isAtivarWeHelp() {
        return PropsService.isTrue(PropsService.ativarWeHelp);
    }

    public boolean isAtivarAnuncioVitio() {
        return PropsService.isTrue(PropsService.ativarAnuncioVitio);
    }
    protected void removerTodosManagedBean() {
        Set<String> identificadores = context().getExternalContext().getSessionMap().keySet();
        Iterator i = identificadores.iterator();
        while (i.hasNext()) {
            String identificadorMB = (String) i.next();
            if ((!identificadorMB.equals("LoginControle"))
                    && (identificadorMB.endsWith("Controle") || identificadorMB.endsWith("ControleRel"))) {
                SuperControle controle = (SuperControle) context().getExternalContext().getSessionMap().get(identificadorMB);
                controle.limparRecursosMemoria();
                context().getExternalContext().getSessionMap().remove(identificadorMB);
            }
        }
    }

    public String getUrlWikiRaiz() {
        return PropsService.getPropertyValue(PropsService.urlWikiRaiz);
    }

    public static String getUrlLoginExterno() {
        return PropsService.getPropertyValue(PropsService.urlLogin);
    }

    public static String getEmpresasPermitidasAlterarDataBase() {
        return PropsService.getPropertyValue(PropsService.empresasPermitidasAlterarDataBase);
    }

    public String getUrlWiki() {
        return PropsService.getPropertyValue(PropsService.urlWiki);
    }

    public String getUrlTreino() {
        try {
            return PropsService.getPropertyValue(getKey(), PropsService.urlTreinoWeb);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getUrlNovaPlataformaTreino() {
        try {
            return PropsService.getPropertyValue(getKey(), PropsService.treinoFront);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getUrlNovaPlataformaZW() {
        try {
            return PropsService.getPropertyValue(getKey(), PropsService.zwFront);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getUrlPactoPayFront() {
        try {
            //utiliza a url do novo treino que é a url base
            return PropsService.getPropertyValue(getKey(), PropsService.treinoFront).replace("novotreino", "pactopay");
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getUrlPactoCrmFront() {
        try {
            //utiliza a url do novo treino que é a url base
            return PropsService.getPropertyValue(getKey(), PropsService.urlcrmFront);
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public String getUrlFotoEmpresa() {
        try {
            return String.format("%s/imagens/foto-%s-%s.jpg", Uteis.getUrlOamdSegura(),
                    getKey(), getEmpresaLogado().getCodigo());
        } catch (Exception ex) {
            return Uteis.getUrlOamd() + File.separator + "imagens" + getKey() + " .jpg";
        }
    }

    public String getUrlWikiCE() {
        return PropsService.getPropertyValue(PropsService.urlWikiCE);
    }

    public String getUrlWikiCRM() {
        return PropsService.getPropertyValue(PropsService.urlWikiCRM);
    }

    public String getUrlWikiFIN() {

        return PropsService.getPropertyValue(PropsService.urlWikiFIN);
    }

    public String getUrlWikiGST() {
        return PropsService.getPropertyValue(PropsService.urlWikiGST);
    }

    public String getUrlWikiVersaoAtual() {
        return ("https://pactosolucoes.com.br/versoes/categoria/novidades-pacto?utm_source=sistema-zw&utm_medium=link-rodape");
    }

    public void notificarOAMDDRoodape() {
        notificarRecursoEmpresa(RecursoSistema.O_QUE_HA_DE_NOVO_RODAPE);
    }

    public String getUrlWikiVersoes() {
        return PropsService.getPropertyValue(PropsService.urlWikiVersoes);
    }

    public String getDiretorioArquivos() {
        return PropsService.getPropertyValue(PropsService.diretorioArquivos);
    }

    public Boolean getApresentarHotjar() {
        return "true".equalsIgnoreCase(PropsService.getPropertyValue(PropsService.apresentarHotjar));
    }

    public Boolean getUtilizarSinteticoMs() {
        return "true".equalsIgnoreCase(PropsService.getPropertyValue(PropsService.utilizarSinteticoMs));
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void montarMsgAlert(String texto) {
        setSucesso(false);
        setErro(false);
        setAtencao(true);
        setMsgAlert("alert('" + texto + "');");
    }

    public void montarMsgAlertID(String texto) {

        setMsgAlert("alert('" + getMensagemInternalizacao(texto) + "');");
    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlertAuxiliar() {
        return msgAlertAuxiliar;
    }

    public void setMsgAlertAuxiliar(String msgAlertAuxiliar) {
        this.msgAlertAuxiliar = msgAlertAuxiliar;
    }

    public Boolean getApresentarModalPanelSucesso() {
        return apresentarModalPanelSucesso;
    }

    public void setApresentarModalPanelSucesso(Boolean apresentarModalPanelSucesso) {
        this.apresentarModalPanelSucesso = apresentarModalPanelSucesso;
    }

    public void setDataVersaoComoString(String dataVersaoComoString) {
        this.dataVersaoComoString = dataVersaoComoString;
    }

    public String getDataVersaoComoString() {
        return dataVersaoComoString;
    }

    public String getNothingLabel() {
        return nothingLabel;
    }

    public String getNomeContextoReal() {
        if ((context() != null) && (context().getExternalContext() != null)
                && (context().getExternalContext().getRequestContextPath() != null)) {
            return context().getExternalContext().getRequestContextPath().substring(1);
        } else {
            return "";
        }
    }

    public String getLocaleDefault() {
        return "pt";
    }

    public String getTimeZoneDefault() {
        String ret = (String) JSFUtilities.getManagedBeanValue("LoginControle.empresa.timeZoneDefault");
        if (ret == null) {
            return TimeZoneEnum.Brazil_East.getId();
        } else {
            return ret;
        }
    }

    protected String getValorCampoConfiguracaoSistema(String campo, String key) {
        try {
            Class classTricks = Class.forName("br.com.pacto.priv.utils.Tricks");

            if (classTricks != null) {
                Class[] partypes = new Class[2];
                partypes[0] = String.class;
                partypes[1] = String.class;
                String[] parvalues = new String[2];
                parvalues[0] = campo;
                parvalues[1] = key;

                Method metodo = null;
                try {
                    metodo = classTricks.getMethod("obterValorCampoConfiguracaoSistema",
                            partypes);
                    if (metodo != null) {
                        try {
                            String retorno = (String) metodo.invoke(classTricks,
                                    parvalues);
                            return retorno;
                        } catch (IllegalAccessException ex) {
                            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
                        } catch (IllegalArgumentException ex) {
                            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
                        } catch (InvocationTargetException ex) {
                            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
                        }
                    }
                } catch (NoSuchMethodException ex) {
                    Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
                } catch (SecurityException ex) {
                    Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        } catch (ClassNotFoundException ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "";
    }

    public void poll() {
        NotificadorServiceControle.verificarZW();
    }

    public void pollCompleto() {
        //TODO método não pode ser removido ainda.
    }

    public ConfPaginacao getConfPaginacao() {
        return this.confPaginacao;
    }

    public void setConfPaginacao(ConfPaginacao confPaginacao) {
        this.confPaginacao = confPaginacao;
    }

    //==============INICIO - METODOS RESPONSAVEIS PELA GERACAO DE LOGS DO SISTEMA=============================

    /**
     * Método responsável pelo registro do LOG do objeto selecionado, esse
     * registro ? feito de acordo com as annotations que foram adicionadas no VO
     * do objeto. No método é gerado uma lista com objetos do tipo LogVO, onde
     * cada objeto desta lista ? incluida na tabela Log no BD (Banco de Dados)
     * com a sua opera??o.
     * <p>
     * Autor: Pedro Y. Saito Criado em 26/01/2011
     */
    @SuppressWarnings("unchecked")
    public static void registrarLogObjetoVO(LogVO logVO, int codPessoa) throws Exception {
        LogInterfaceFacade logFacade = getFacade().getLog();
        if (logVO != null) {
            logVO.setPessoa(codPessoa);
            logFacade.incluirSemCommit(logVO);
        }
    }

    /**
     * Método responsável pelo registro do LOG de alteração do objeto
     * selecionado, esse registro é feito de acordo com as annotations que foram
     * adicionadas no VO do objeto. No método é gerado uma lista com objetos do
     * tipo LogVO, onde cada objeto desta lista ? incluida na tabela Log no BD
     * (Banco de Dados) com a sua opera??o.
     *
     * @param ObjetoVO Objeto a ser validado para geração o LOG.
     * <AUTHOR> Cantarelli dos Santos
     */
    @SuppressWarnings("unchecked")
    public static void registrarLogObjetoVO(SuperVO ObjetoVO, int codPessoa) throws Exception {
        registrarLogObjetoVO(ObjetoVO, codPessoa, getFacade().getLog().getCon());
    }

    public static void registrarLogObjetoVO(SuperVO ObjetoVO, int codPessoa, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);
            List lista = ObjetoVO.gerarLogAlteracaoObjetoVO();
            Iterator i = lista.iterator();
            while (i.hasNext()) {
                LogVO log = (LogVO) i.next();
                log.setPessoa(codPessoa);
                log.setOrigem(OrigemCadEAlteracaoCliente.ZW.getDescricao());
                logDAO.incluirSemCommit(log);
            }
        } finally {
            logDAO = null;
        }
    }

    /**
     * Método responsável pelo registro do LOG de alteração do objeto
     * selecionado, esse registro é feito de acordo com as annotations que foram
     * adicionadas no VO do objeto. No método é gerado uma lista com objetos do
     * tipo LogVO, onde cada objeto desta lista é incluida na tabela Log no BD
     * (Banco de Dados) com a sua opera??o.
     *
     * @param ObjetoVO Objeto a ser validado para gera??o o LOG.
     * <AUTHOR> Cantarelli dos Santos
     */
    @SuppressWarnings("unchecked")
    public static void registrarLogObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        registrarLogObjetoVOGeral(ObjetoVO, codigoCliente, nomeEntidade, codPessoa, false);
    }

    public static void registrarLogObjetoVOGeral(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa, boolean validarValorNull) throws Exception {
        registrarLogObjetoVOGeralAlterandoResponsavel(ObjetoVO, codigoCliente, nomeEntidade, codPessoa, validarValorNull, "", null, getFacade().getLog().getCon());
    }

    public static void registrarLogObjetoVOGeralAlterandoResponsavel(SuperVO ObjetoVO, Integer codigoCliente,
                                                                     String nomeEntidade, int codPessoa,
                                                                     boolean validarValorNull, UsuarioSimplesDTO usuarioSimplesDTO) throws Exception{
        UsuarioVO usuarioVO = new UsuarioVO();
        usuarioVO.setNome(usuarioSimplesDTO.getUsername());
        usuarioVO.setCodigo(usuarioSimplesDTO.getCodZw());
        registrarLogObjetoVOGeralAlterandoResponsavel(ObjetoVO, codigoCliente, nomeEntidade, codPessoa, validarValorNull,
                usuarioSimplesDTO.getUsername(), usuarioVO, getFacade().getLog().getCon());
    }


    public static void registrarLogObjetoVOGeralAlterandoResponsavel(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa, boolean validarValorNull, String responsavel) throws Exception{
        registrarLogObjetoVOGeralAlterandoResponsavel(ObjetoVO, codigoCliente, nomeEntidade, codPessoa, validarValorNull, responsavel, null, getFacade().getLog().getCon());
    }

    public static void registrarLogObjetoVOGeralComConexao(SuperVO objetoVO, String nomeEntidade, Integer chavePrimaria, UsuarioVO usuarioVO, Connection con) throws Exception{
        registrarLogObjetoVOGeralComConexao(objetoVO, nomeEntidade, chavePrimaria, 0, false, usuarioVO, con);
    }

    public static void registrarLogObjetoVOGeralComConexao(SuperVO objetoVO, String nomeEntidade, Integer chavePrimaria, int codPessoa,
                                                 boolean validarValorNull, UsuarioVO usuarioVO, Connection con) throws Exception{
        registrarLogObjetoVOGeralAlterandoResponsavel(objetoVO, chavePrimaria, nomeEntidade, codPessoa, validarValorNull, null, usuarioVO, con);
    }

    public static void registrarLogObjetoVOGeralAlterandoResponsavel(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa,
                                                                     boolean validarValorNull, String responsavel, UsuarioVO usuarioVO, Connection con) throws Exception{
        Log lodDAO;
        try {
            lodDAO = new Log(con);

            List lista = ObjetoVO.gerarLogAlteracaoObjetoVO(validarValorNull, usuarioVO);
            for (Object aLista : lista) {
                LogVO log = (LogVO) aLista;
                log.setChavePrimaria(codigoCliente.toString());
                log.setNomeEntidade(nomeEntidade);
                log.setPessoa(codPessoa);
                log.setOrigem(OrigemCadEAlteracaoCliente.ZW.getDescricao());
                if (usuarioVO != null) {
                    log.setUsuarioVO(usuarioVO);
                    log.setResponsavelAlteracao(usuarioVO.getNome());
                    log.setUserOAMD(usuarioVO.getUserOamd());
                } else if (responsavel != null && !responsavel.equals("")) {
                    log.setResponsavelAlteracao(responsavel);
                }
                if (log.getNomeEntidade().equals("PRODUTO_ESTOQUE")) {
                    if (log.getNomeCampo().equals("situacao")) {
                        if (log.getValorCampoAnterior().equals("A") && log.getValorCampoAlterado().equals("C")) {
                            log.setOperacao("CANCELAMENTO PRODUTO");
                            log.setNomeEntidadeDescricao("PRODUTO");
                            log.setNomeCampo("*Situação Produto:");
                            log.setValorCampoAnterior("Ativo");
                            log.setValorCampoAlterado("Cancelado");
                        } else if (log.getValorCampoAnterior().equals("C") && log.getValorCampoAlterado().equals("A")) {
                            log.setOperacao("ALTERAÇÃO");
                            log.setNomeCampo("*Situação Produto:");
                            log.setValorCampoAnterior("Cancelado");
                            log.setValorCampoAlterado("Ativo");
                        }
                    }
                }
                if (log.getNomeEntidade().equals("BALANCO")) {
                    if (log.getNomeCampo().equals("cancelado")) {
                        log.setOperacao("CANCELAMENTO BALANÇO");
                        log.setNomeEntidadeDescricao("BALANÇO");
                        log.setNomeCampo("*Situação Balanço:");
                        log.setValorCampoAnterior("Ativo");
                        log.setValorCampoAlterado("Cancelado");
                        lodDAO.incluirSemCommit(log);
                    }
                }
                if (!log.getNomeEntidade().equals("BALANCO")) {
                    lodDAO.incluirSemCommit(log);
                }
            }
        } finally {
            lodDAO = null;
        }
    }

    @SuppressWarnings("unchecked")
    public static void registrarLogExclusaoTodosDadosObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        LogInterfaceFacade logFacade = getFacade().getLog();
        List lista = ObjetoVO.gerarLogAlteracaoObjetoVO();
        for (Object aLista : lista) {
            LogVO log = (LogVO) aLista;
            log.setOperacao("EXCLUSÃO");
            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            logFacade.incluirSemCommit(log);
        }
    }

    public static void registrarLogObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa, boolean inclusao) throws Exception {
        LogInterfaceFacade logFacade = getFacade().getLog();
        List lista = ObjetoVO.gerarLogAlteracaoObjetoVO();
        for (Object aLista : lista) {
            LogVO log = (LogVO) aLista;

            if (inclusao)
                log.setOperacao("INCLUSÃO");
            else
                log.setOperacao("ALTERAÇÃO");

            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            logFacade.incluirSemCommit(log);
        }
    }


    /**
     * Método responsável pelo registro do LOG de exclusao do objeto
     * selecionado, esse registro é feito de acordo com as annotations que foram
     * adicionadas no VO do objeto. No método é gerado uma lista com objetos do
     * tipo LogVO, onde cada objeto desta lista é incluida na tabela Log no BD
     * (Banco de Dados) com a sua opera??o.
     * <p>
     * Autor: Pedro Y. Saito Criado em 26/01/2011
     */
    @SuppressWarnings("unchecked")
    public static void registrarLogExclusaoObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        LogInterfaceFacade logFacade = getFacade().getLog();
        List lista = ObjetoVO.gerarLogExclusaoObjetoVO();
        Iterator i = lista.iterator();
        while (i.hasNext()) {
            LogVO log = (LogVO) i.next();
            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            logFacade.incluirSemCommit(log);
        }
    }

    public static void registrarLogObjetoVO(String nomeEntidade, String msg, int codPessoa, String responsavel, String userOamd, String operacao) throws Exception {
        try {
            LogInterfaceFacade logFacade = getFacade().getLog();

            LogVO log = new LogVO();
            log.setNomeCampo("");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao(operacao);
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logFacade.incluir(log);


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd) throws Exception {
        registrarLogErroObjetoVO(nomeEntidade, codPessoa, msg, responsavel, userOamd, getFacade().getLog().getCon());
    }

    public static void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, String responsavel, String userOamd, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    public static void registrarLogErroObjetoVO(String nomeEntidade, String operacao, String chavePrimaria, int codPessoa, String msg, String responsavel, String userOamd) throws Exception {
        try {
            LogInterfaceFacade logFacade = getFacade().getLog();

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria(chavePrimaria);
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAlterado(msg);
            log.setOperacao(operacao);
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logFacade.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável pelo registro do LOG de altera??o do objeto
     * selecionado, esse registro ? feito de acordo com as annotations que foram
     * adicionadas no VO do objeto. No método ? gerado uma lista com objetos do
     * tipo LogVO, onde cada objeto desta lista ? incluida na tabela Log no BD
     * (Banco de Dados) com a sua opera??o.
     *
     * <AUTHOR> Cantarelli dos Santos
     */
    public static void registrarLogObjetoVO(List<LogVO> listaLog, int codPessoa) throws Exception {
        registrarLogObjetoVO(listaLog, codPessoa, getFacade().getLog().getCon());
    }

    public static void registrarLogObjetoVO(List<LogVO> listaLog, int codPessoa, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            Iterator i = listaLog.iterator();
            while (i.hasNext()) {
                LogVO log = (LogVO) i.next();
                log.setPessoa(codPessoa);
                logDAO.incluirSemCommit(log);
            }
        } finally {
            logDAO = null;
        }
    }

    public static void registrarLogObjetoVO(List<LogVO> listaLog, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        LogInterfaceFacade logFacade = getFacade().getLog();
        Iterator i = listaLog.iterator();
        while (i.hasNext()) {
            LogVO log = (LogVO) i.next();
            log.setChavePrimaria(codigoCliente.toString());
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            logFacade.incluirSemCommit(log);
        }
    }

    public String getKey(String key) {
        if (context() != null) {
            return getKey();
        }else{
            return key;
        }
    }
    public String getKey() {
        String retorno = "";
        if (context() != null) {
            if (JSFUtilities.getFromSession("key") != null) {
                return JSFUtilities.getFromSession("key").toString();
            }
        }
        return retorno;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKeyComURLConexao() throws SQLException {
        String retorno = "";
        if (context() != null) {
            if (JSFUtilities.getFromSession("key") != null) {
                Connection con = Conexao.getFromSession();
                return JSFUtilities.getFromSession("key").toString() + "/"
                        + con.getMetaData().getURL();
            }
        }
        return retorno;
    }

    public void vazio() {
        //so para invocar um método para ser executado o evento "oncomplete" de um componente ajax qualquer
    }

    /**
     * Metodo que limpa as mensagens na tela
     * <p>
     * Autor: Pedro Y. Saito Criado em 04/03/2011
     */
    public void limparMsg() {
        this.setMensagemDetalhada("");
        this.setMensagemID("");
        this.setMensagem("");
        this.setErro(false);
        this.setSucesso(false);
        this.setWarning(false);
        this.setAtencao(false);
    }

    protected final void clsMessages() {
        this.mensagemID = "";
        this.mensagem = "";
        this.mensagemDetalhada = "";
    }

    public void montarErro(Exception e) {
        setMensagemID("msg_erro");
        setMensagemDetalhada(e);
        montarErro();
    }

    public void montarErro(String e) {
        setMensagemID("msg_erro");
        setMensagemDetalhada(new Exception(e));
        setMensagem(e);
        montarErro();
    }

    private void montarErro() {
        setSucesso(false);
        setAtencao(false);
        setErro(true);
    }

    public void montarSucesso(String msg) {
        setMensagemID(msg);
        setSucesso(true);
        setAtencao(false);
        setWarning(false);
        setErro(false);
    }

    public void montarSucessoGrowl(String msg) {
        montarSucesso("msg_operacao_sucesso");
        setMensagemDetalhada("");
        if (!UteisValidacao.emptyString(msg)) {
            setMensagemID("");
            setMensagem(msg);
        }
    }

    public void montarSucessoDadosGravados() {
        montarSucesso("msg_operacao_sucesso");
        setMensagemDetalhada("");
    }

    public void montarInfo(String info) {
        setMensagemDetalhada(info);
        setWarning(false);
        setSucesso(false);
        setAtencao(true);
        setErro(false);
    }

    public void montarAviso(String info) {
        setMensagemDetalhada("", info);
        setSucesso(false);
        setWarning(true);
        setAtencao(false);
        setErro(false);
    }

    public void montarAviso(String titulo, String info) {
        setMensagem(titulo);
        setMensagemDetalhada(info);
        setSucesso(false);
        setWarning(true);
        setAtencao(false);
        setErro(false);
    }

    public void montarInfo(String titulo, String info) {
        setMensagem(titulo);
        setMensagemDetalhada(info);
        setWarning(false);
        setSucesso(false);
        setAtencao(true);
        setErro(false);
    }

    /**
     * Handler responsável por capturar os atributos de autoriza??o dos bot?es
     * da tela. Exemplo.: <h:commandButton ...
     * actionListener="#{Controle.autorizacao}"> <f:attribute name="entidade"
     * value="101" /> <f:attribute name="operacao" value="104" />
     * </h:commandButton>
     *
     * @param evt ActionEvent
     */
    public void autorizacao(ActionEvent evt) {
        Object op = evt.getComponent().getAttributes().get("operacao");
        Object ent = evt.getComponent().getAttributes().get("entidade");
        Object func = evt.getComponent().getAttributes().get("funcao");
        aasEntidade = (ent == null ? 0 : new Integer(ent.toString()));
        aasOperacao = (op == null ? "" : op.toString());
        aasFuncionalidade = (func == null ? 0 : new Integer(func.toString()));
    }

    /**
     * Responsável por verificarZW se o usuário possui permissao para executar a
     * ação.
     *
     * @throws Exception caso o usuário não possua permissao
     */
    public void verificarAutorizacao() throws Exception {
        //obtem o usuario logado
        UsuarioVO usuarioLogado = this.getUsuarioLogado();
        if (usuarioLogado.getAdministrador()) {
            //usuario administrador possui permissao total
            return;
        }
        //MBean que contem as regras de autorizacao da aplicacao
        AutorizacaoRegras mb = (AutorizacaoRegras) JSFUtilities.getManagedBean("Regras");
        //Obtem as regras para a chave da empresa do usuario logado
        Object obj = JSFUtilities.getFromSession("key");
        String key = "DEFAULT";
        if (obj != null) {
            key = UteisValidacao.emptyString(obj.toString()) ? key : obj.toString();
        }
        Map<String, Autorizacao> regras = mb.getRegras(key);
        //verifica se é uma funcao ou uma entidade
        String funcOuEnt = aasEntidade == 0 ? "F" + aasFuncionalidade : "E" + aasEntidade;
        //obtem os perfis do usuario logado
        List perfis = usuarioLogado.getUsuarioPerfilAcessoVOs();
        for (Object perfilObj : perfis) {
            //essa entidade relaciona usuario x perfil
            UsuarioPerfilAcessoVO perfil = (UsuarioPerfilAcessoVO) perfilObj;
            //codigo do perfil
            Integer codgPerfil = perfil.getPerfilAcesso().getCodigo();
            String perfilStr = codgPerfil.toString();
            //obtem as autorizacoes de um determinado perfil para aquela fun??o ou entidade
            Autorizacao autorizacao = regras.get(perfilStr + funcOuEnt);
            //verifica se possui autoriza??o para a operacao especifica
            boolean permiteOperacao = autorizacao != null && autorizacao.getOperacao().indexOf(aasOperacao) > -1;
            //TODO internacionalizar mensagem da excecao.
            if (autorizacao == null) {
                //nao existe autoriza??o
                throw new Exception("Não há permissao para essa ação.");
            } else if (aasFuncionalidade == 0 && !permiteOperacao) {
                //possui autoriza??o para a entidade mas n?o para a opera??o.
                throw new Exception("Não há permissão para essa ação nessa entidade.");
            } else {
                //autorizacao concedida
                return;
            }
        }
    }

    private String inserirUrlAssinaturaDigital(String texto) {
        String urlSistema = "";
        try {
            if (JSFUtilities.isJSFContext()) {
                urlSistema = getUrl();
            }
        } catch (Exception ignored) {
        }

        if (UteisValidacao.emptyString(urlSistema)) {
            try {
                String key = DAO.resolveKeyFromConnection(getFacade().getEmpresa().getCon());
                ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave(key);
                urlSistema = dataDTO.getServiceUrls().getZwUrl();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        String url = urlSistema + "/faces/assinaturaDigital.jsp?token=";
        return texto.replaceAll("#URL_PAGINA_ASSINATURA", url);
    }

    /**
     * Responsavel por enviar o contrato por email para cliente
     *
     * @throws Exception
     * <AUTHOR> 06/05/2011
     */
    public void enviarContrato(String[] emails, ContratoVO contrato, List<MovPagamentoVO> pagamentos,
                               ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO,boolean enviarParaAssinar,
                               HttpServletRequest request, UsuarioVO usuarioVO) throws Exception {
        if (contrato.getCodigo() != 0 && getFacade().getConfiguracaoSistema().verificarAssinaturaDigital() && enviarParaAssinar) {
            if(getFacade().getContrato().verificaSeContratoFoiAssinado(contrato.getCodigo())) {
                throw new Exception("O contrato já está assinado! Selecione um contrato que ainda não foi assinado.");
            }
            if (JSFUtilities.isJSFContext()) {
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", contrato.getEmpresa());
            }
            String texto = getFacade().getCliente().montarEmailAssinaturaContrato(contrato);
            texto = inserirUrlAssinaturaDigital(texto);
            String textoAnexo = getFacade().getContratoTextoPadrao().consultarHtmlContrato(contrato.getCodigo(), false);
            UteisEmail email = new UteisEmail();
            texto = arranjarImagens(texto, contrato, request);

            email.novo(contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            substituiImagensRodape(email);

            String chave = "";
            if (JSFUtilities.isJSFContext()) {
                email.setRemetente(getUsuarioLogado());
                chave = (String) JSFUtilities.getFromSession("key");
            } else {
                email.setRemetente(usuarioVO);
                chave = DAO.resolveKeyFromConnection(getFacade().getEmpresa().getCon());
            }

            File anexo = File.createTempFile("contrato", ".pdf");
            anexo.deleteOnExit();

            try {
                try (OutputStream out = new FileOutputStream(anexo)) {
                    HtmlConverter.convertToPdf(textoAnexo, out);

                    email.addAnexo("Contrato de Prestação de Serviços.pdf", anexo);
                }

                boolean existeEmailValido = false;
                for (String emailEnviar : emails) {
                    if (UteisValidacao.validaEmail(emailEnviar)) {
                        existeEmailValido = true;
                    }
                }
                if (existeEmailValido) {
                    String token = "codigoContrato:" + contrato.getCodigo() + "; chave:" + chave;
                    token = Criptografia.encrypt(token, Crypt_KEY_Contrato, Crypt_ALGORITM);
                    texto = texto.replaceAll("#TOKEN", token);
                    contrato.setEmailRecebimento(String.join(";",emails));
                    getFacade().getContrato().gravarInformacoesAssinaturaContratoEmail(contrato);
                    email.enviarEmailN(emails, texto, contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
                } else {
                    throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
                }
            } finally {
                anexo.delete();
            }
        } else if(contrato.getCodigo() != 0) {
            if (JSFUtilities.isJSFContext()) {
                JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", contrato.getEmpresa());
            }
            Boolean telaNova = false;
            if (request != null) {
                telaNova = UteisValidacao.converterBooleano(request.getParameter("telaNova"));
            }
            String texto = getFacade().getContratoTextoPadrao().consultarHtmlContrato(contrato.getCodigo(),false, false, telaNova);
            UteisEmail email = new UteisEmail();
            texto = arranjarImagens(texto, contrato, request);
            email.novo(contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            email.setRemetente(usuarioVO != null ? usuarioVO : getUsuarioLogado());

            boolean existeEmailValido = false;
            for (String emailEnviar : emails) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                email.enviarEmailN(emails, texto, contrato.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
            } else {
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            }

        } else {
            throw new Exception("Não foi possível enviar o contrato. Dados não encontrados!");
        }
    }

    public void enviarContratoProduto(String[] emails, VendaAvulsaVO vendaAvulsa,
                               ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO,
                               HttpServletRequest request, UsuarioVO usuarioVO) throws Exception {
        if(vendaAvulsa.getCodigo() != 0) {
            String texto = (String) request.getSession().getAttribute("textoRelatorio");
            UteisEmail email = new UteisEmail();
            texto = arranjarImagensContratoProduto(texto, vendaAvulsa, request);
            email.novo(vendaAvulsa.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", configuracaoSistemaCRMVO);
            email.setRemetente(usuarioVO != null ? usuarioVO : getUsuarioLogado());

            boolean existeEmailValido = false;
            for (String emailEnviar : emails) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                email.enviarEmailN(emails, texto, vendaAvulsa.getEmpresa().getNome() + " - CONTRATO DE PRESTAÇÃO DE SERVIÇOS ", "");
            } else {
                throw new Exception("Não foi possível enviar o contrato pois o cliente não possui um email válido.");
            }

        } else {
            throw new Exception("Não foi possível enviar o contrato. Dados não encontrados!");
        }
    }

    public void substituiImagensRodape(UteisEmail email) throws URISyntaxException {
        File imagemEmail = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/imagemEmail.png").toURI());
        File imagemTelefone = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/imagemTelefoneEmail.png").toURI());

        email.addImagem(new ImageEmailHtml("#IMAGEM_TELEFONE", imagemTelefone));
        email.addImagem(new ImageEmailHtml("#IMAGEM_EMAIL", imagemEmail));
    }

    public void enviarOrcamento(OrcamentoVO orcamento, String[] emails, ModeloOrcamentoVO modeloOrcamento, ClienteVO cliente, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) throws Exception {
        EmpresaVO emp = new EmpresaVO();
        emp = getFacade().getEmpresa().consultarPorChavePrimaria(cliente.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        if (modeloOrcamento.getCodigo() != 0) {
            JSFUtilities.setManagedBeanValue("EmpresaControle.empresaVO", cliente.getEmpresa());
            modeloOrcamento.substituirTagsTextoEmBranco(orcamento, emp, Conexao.getFromSession(), true);
            String texto = modeloOrcamento.getTexto();
            texto = arranjarImagensOrcamento(texto, emp);

            UteisEmail email = new UteisEmail();
            email.novo(emp.getNome() + " - ORCAMENTO ", configuracaoSistemaCRMVO);
            email.setRemetente(getUsuarioLogado());

            boolean existeEmailValido = false;
            for (String emailEnviar : emails) {
                if (UteisValidacao.validaEmail(emailEnviar)) {
                    existeEmailValido = true;
                }
            }
            if (existeEmailValido) {
                email.enviarEmailN(emails, texto, emp.getNome() + " - ORCAMENTO ", "");
            } else {
                throw new Exception("Não foi possível enviar o orçamento pois o cliente não possui um email válido.");
            }
        } else {
            throw new Exception("Não foi possível enviar o orçamento. Dados não encontrados!");
        }
    }

    /**
     * <AUTHOR> 06/05/2011
     */
    public String arranjarImagens(String texto, ContratoVO contrato, HttpServletRequest request) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        String pathFull = "";
        if (request != null) {
            pathFull = request.getRealPath("").substring(0, request.getRealPath("").length() -1) + path;
        } else {
            pathFull = Uteis.obterCaminhoWeb() + path;
        }
        String nomeImagem = Uteis.retirarAcentuacaoRegex(contrato.getEmpresa().getNome().replaceAll(" ", ""));
        contrato.getEmpresa().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(),
                contrato.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        UteisEmail.criarImagem(pathFull, contrato.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");

        if (request != null) {
            path = (request.getRequestURL().toString().replace(request.getServletPath(), "")) + path + nomeImagem + ".jpg";
        } else {
            path = getUrl() + path + nomeImagem + ".jpg";
        }
        texto = texto.replaceAll("#LOGO_EMPRESA", path);
        return texto;
    }

    public String arranjarImagensContratoProduto(String texto, VendaAvulsaVO vendaAvulsa, HttpServletRequest request) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        String pathFull = "";
        if (request != null) {
            pathFull = request.getRealPath("").substring(0, request.getRealPath("").length() -1) + path;
        } else {
            pathFull = Uteis.obterCaminhoWeb() + path;
        }
        String nomeImagem = Uteis.retirarAcentuacaoRegex(vendaAvulsa.getEmpresa().getNome().replaceAll(" ", ""));
        vendaAvulsa.getEmpresa().setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(),
                vendaAvulsa.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        UteisEmail.criarImagem(pathFull, vendaAvulsa.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");

        if (request != null) {
            path = (request.getRequestURL().toString().replace(request.getServletPath(), "")) + path + nomeImagem + ".jpg";
        } else {
            path = getUrl() + path + nomeImagem + ".jpg";
        }
        texto = texto.replaceAll("#LOGO_EMPRESA", path);
        return texto;
    }

    public String arranjarImagens(String texto, ContratoVO contrato, Connection con) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        String pathFull = Uteis.obterCaminhoWeb() + path;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(contrato.getEmpresa().getNome().replaceAll(" ", ""));
        Empresa empresaDAO = new Empresa(con);
        contrato.getEmpresa().setFotoRelatorio(empresaDAO.obterFoto(getKey(),
                contrato.getEmpresa().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        empresaDAO = null;
        UteisEmail.criarImagem(pathFull, contrato.getEmpresa().getFotoRelatorio(), nomeImagem + ".jpg");

        path = getUrl() + path + nomeImagem + ".jpg";
        texto = texto.replaceAll("#LOGO_EMPRESA", path);
        return texto;
    }

    public String arranjarImagensOrcamento(String texto, EmpresaVO emp) throws Exception {
        //criar a imagem temporaria
        String path = "/imagensCRM/email/tmp/";
        String pathFull = Uteis.obterCaminhoWeb() + path;
        String nomeImagem = Uteis.retirarAcentuacaoRegex(emp.getNome().replaceAll(" ", ""));
        emp.setFotoRelatorio(getFacade().getEmpresa().obterFoto(getKey(),
                emp.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO));
        UteisEmail.criarImagem(pathFull, emp.getFotoRelatorio(), nomeImagem + ".jpg");
        path = "." + path + nomeImagem + ".jpg";
        texto = texto.replaceAll("<img[^>]+src\\s*=\\s*['\"]acesso\\?emp*([^'\"]+)['\"][^>]*>", "<img src=\"" + path + "\" />");
        return texto;
    }

    public void setApresentarSucessoEmail(Boolean apresentarSucessoEmail) {
        this.apresentarSucessoEmail = apresentarSucessoEmail;
    }

    public Boolean getApresentarSucessoEmail() {
        if (apresentarSucessoEmail == null) {
            apresentarSucessoEmail = Boolean.FALSE;
        }
        return apresentarSucessoEmail;
    }

    public void fecharConfirmacao() {
        apresentarSucessoEmail(false);
    }

    public void apresentarSucessoEmail(boolean valor) {
        JSFUtilities.setManagedBeanValue("SuperControle.apresentarSucessoEmail",
                valor);
    }

    public final String getIpCliente() {
        String temp = (String) JSFUtilities.getFromSession("ip");
        if (temp != null) {
            return temp;
        } else {
            return "";
        }
    }

    public String getIp() {
        if (ip == null) {
            ip = "";
        }
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void enviar() {
        if (this.ip.isEmpty()) {
            String ipAux = (String) JSFUtilities.getFromSession("RemoteAddr");
            if (ipAux == null) {
                this.ip = "SEM_IP";
            } else {
                this.ip = ipAux;
            }
        }
        JSFUtilities.storeOnSession("ip", getIp());
        System.out.println(getIp());

    }

    public void enviarBrowser() {
        JSFUtilities.storeOnSession("browser", getBrowser());
        JSFUtilities.storeOnSession("screenW", getWidthScreenClient());
        JSFUtilities.storeOnSession("screenH", getHeightScreenClient());
        JSFUtilities.storeOnSession("protocol", getProtocol());
        JSFUtilities.storeOnSession("urlBrowser", getUrlBrowser());
        System.out.println(getBrowser());
        System.out.println(getUrlBrowser());
    }

    public Integer getScreenWidthCliente() {
        Integer w = (Integer) JSFUtilities.getFromSession("screenW");
        if (w == null) {
            return 0;
        } else {
            return w;
        }
    }

    public static String getApplicationProtocol() {
        String p = (String) JSFUtilities.getFromSession("protocol");
        return p != null ? p : "";
    }

    public UsuarioVO getUsuarioRecorrencia() throws Exception {
        return getFacade().getZWFacade().getUsuarioRecorrencia();
    }

    public boolean isInternetExplorer() {
        String temp = (String) JSFUtilities.getFromSession("browser");
        return (temp != null && (temp.contains("MSIE") && !temp.contains("Chrome")));
    }

    public boolean isSuportaFlash() {
        String temp = (String) JSFUtilities.getFromSession("browser");
        return (temp != null && (!temp.toUpperCase().contains("IPAD") && (!temp.toUpperCase().contains("IPHONE"))));
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getUrlBrowser() {
        return urlBrowser;
    }

    public void setUrlBrowser(String urlBrowser) {
        this.urlBrowser = urlBrowser;
    }

    public Integer getHeightScreenClient() {
        return heightScreenClient;
    }

    public void setHeightScreenClient(Integer heightScreenClient) {
        this.heightScreenClient = heightScreenClient;
    }

    public Integer getWidthScreenClient() {
        return widthScreenClient;
    }

    public void setWidthScreenClient(Integer widthScreenClient) {
        this.widthScreenClient = widthScreenClient;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public static void paintFotoInterno(OutputStream out, final byte[] byteArrayFoto, String caminhoResourceDefault,
                                        Integer largura, Integer altura) throws Exception {
        InputStream in = null;
        try {
            if (byteArrayFoto != null && byteArrayFoto.length > 0) {
                in = new ByteArrayInputStream(byteArrayFoto);
            } else {
                in = SuperControle.class.getResourceAsStream(caminhoResourceDefault);
            }
            BufferedImage img = ImageIO.read(in);
            if (largura != null && altura != null) {
                Graphics2D graphics2D = img.createGraphics();
                /**
                 * Aqui esta o "pulo do gato": redimensiona antes de setar o
                 * graphics, com o algoritmo de calculo de media da area em pixels
                 * da imagem.
                 */
                Image scaledImage = img.getScaledInstance(largura, altura, Image.SCALE_AREA_AVERAGING);
                graphics2D.drawImage(scaledImage, 0, 0, largura, altura, null);

                graphics2D.dispose();
                img = null;
                img = Uteis.toBufferedImage(scaledImage, BufferedImage.TYPE_INT_RGB);
            }
            ImageIO.write(Uteis.toBufferedImage(img, BufferedImage.TYPE_INT_RGB), "PNG", out);
            img = null;
        } finally {
            if (in != null) {
                in.close();
            }
            out.close();
        }
    }

    public static void paintFoto(OutputStream out, byte[] byteArrayFoto) throws Exception {
        HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String l = request.getParameter("largura");
        String a = request.getParameter("altura");

        Integer largura = null;
        Integer altura = null;
        if (l != null && a != null) {
            largura = Integer.valueOf(l);
            altura = Integer.valueOf(a);
        }
        SuperControle.paintFotoInterno(out, byteArrayFoto, "/br/com/pactosolucoes/comuns/util/resources/fotoPadrao.jpg", largura, altura);
    }

    public static void paintFotoEmpresa(OutputStream out, byte[] byteArrayFoto) throws Exception {
        SuperControle.paintFotoInterno(out, byteArrayFoto, "/br/com/pactosolucoes/comuns/util/resources/logoPadraoRelatorio.jpg", null, null);
    }

    public static void paintFotoRelatorio(OutputStream out, byte[] byteArrayFoto) throws Exception {
        SuperControle.paintFotoInterno(out, byteArrayFoto, "/br/com/pactosolucoes/comuns/util/resources/logoPadraoRelatorio.jpg", null, null);
    }

    public String getContextPath() {
        return request().getContextPath();
    }

    public boolean isDesv() {
        return JSFUtilities.getFromSession("desv") != null
                && JSFUtilities.getFromSession("desv").equals("true");
    }

    public boolean isEnableCountDown() {
        return PropsService.isTrue(PropsService.enableCountdown);
    }

    public ArrayList getListaManipulavel() {
        return listaManipulavel;
    }

    public void setListaManipulavel(ArrayList listaManipulavel) {
        this.valorPesquisa = "";
        this.listaManipulavel = listaManipulavel;
        this.listaOriginal = (ArrayList) listaManipulavel.clone();
        if (!listaManipulavel.isEmpty()) {
            this.listaAtributosLista = UtilReflection.getListAttributes(
                    listaManipulavel.get(0).getClass());
        }
    }

    public ArrayList getListaOriginal() {
        return listaOriginal;
    }

    public void setListaOriginal(ArrayList listaOriginal) {
        this.listaOriginal = listaOriginal;
    }

    public String getValorPesquisa() {
        return valorPesquisa;
    }

    public void setValorPesquisa(String valorPesquisa) {
        this.valorPesquisa = valorPesquisa;
    }

    public String getNavigationCase() {
        return navigationCase;
    }

    public void setNavigationCase(String navigationCase) {
        this.navigationCase = navigationCase;
    }

    public boolean isExibirMenuLateralEstudio() {
        return exibirMenuLateralEstudio;
    }

    public void setExibirMenuLateralEstudio(boolean exibirMenuLateralEstudio) {
        this.exibirMenuLateralEstudio = exibirMenuLateralEstudio;
    }

    protected boolean isUsarEstudio() throws Exception {
        LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
        return loginControle.isApresentarLinkEstudio();
    }

    private StringBuilder getValoresConcatenados(Object obj) {
        StringBuilder valores = new StringBuilder();
        for (String nomeAtributo : listaAtributosLista) {
            try {
                Object valor = UtilReflection.getValor(obj, nomeAtributo);
                valores.append(valor != null ? valor.toString() : "" + "|");
            } catch (Exception ex) {
                Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, null, ex);
            }
        }
        return valores;

    }

    public boolean isPageFromModuloEstudio() {
        String p = request().getParameter("modulo");
        return p != null && p.equals("4bf2add2267962ea87f029fef8f75a2f");
    }

    public void filtrarPorTexto() {
        if (valorPesquisa.isEmpty() && listaOriginal != null && listaManipulavel != null && (listaOriginal.size() == listaManipulavel.size())) {
            return;
        }
        if (valorPesquisa.isEmpty()) {
            listaManipulavel = null;
            listaManipulavel = listaOriginal != null ? (ArrayList) listaOriginal.clone() : null;
            return;
        }
        listaManipulavel = listaOriginal != null ? (ArrayList) listaOriginal.clone() : new ArrayList();
        List listaAssociada = new ArrayList(listaManipulavel);
        for (Object object : listaAssociada) {
            StringBuilder valores = getValoresConcatenados(object);
            if (valores.toString().toUpperCase().indexOf(valorPesquisa.toUpperCase()) == -1) {
                listaManipulavel.remove(object);
            }
        }
    }

    public String getPropertyValue(final String property) {
        return PropsService.getPropertyValue(property);
    }

    public void setPropertyValue(final String property, final String value) {
        PropsService.setPropertyValue(property, value);
    }

    public String tratarNavigationCaseIntegracaoModulo(String navegacaoPadrao,
                                                       String navegacaoModulo, ActionEvent evt) {

        return (evt == null || evt.getComponent().getAttributes().get("modulo") == null)
                ? navegacaoPadrao : navegacaoModulo + "?modulo="
                + evt.getComponent().getAttributes().get("modulo");
    }

    public String tratarNavigationCaseIntegracaoModulo(String navegacaoPadrao,
                                                       String navegacaoModulo) {

        return (request().getParameter("modulo") == null || request().getParameter("modulo").equals(""))
                ? navegacaoPadrao : navegacaoModulo + "?modulo="
                + request().getParameter("modulo");
    }

    public void toggleMenuLateralEstudio(ActionEvent evt) {
        this.setExibirMenuLateralEstudio(!this.isExibirMenuLateralEstudio());
    }

    public String getDataAtual() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        return sdf.format(Calendario.hoje());
    }

    public String getNomeUsuarioLogado() {
        String nomeUsuario = "";
        try {
            nomeUsuario = getUsuarioLogado().getNome();
        } catch (Exception ignored) {
        }
        return nomeUsuario;
    }

    public PerfilAcessoVO getPerfilUsuarioLogado() throws Exception {
        for (UsuarioPerfilAcessoVO uPerfil : getUsuarioLogado().getUsuarioPerfilAcessoVOs()) {
            if (uPerfil.getEmpresa().getCodigo().equals(getEmpresaLogado().getCodigo())) {
                return uPerfil.getPerfilAcesso();
            }
        }
        return new PerfilAcessoVO();
    }

    public PerfilAcessoVO getPerfilUsuarioLogado(UsuarioVO NomeEmpresa, EmpresaVO empresaLogada) throws Exception {

        for (Object uPerfil : NomeEmpresa.getUsuarioPerfilAcessoVOs()) {

            if (((UsuarioPerfilAcessoVO) uPerfil).getEmpresa().getCodigo().equals(empresaLogada.getCodigo()))
                return ((UsuarioPerfilAcessoVO) uPerfil).getPerfilAcesso();
        }
        return new PerfilAcessoVO();
    }

    /**
     * Verifica se o usuario logado possue permiss?o de acesso
     *
     * @param nomePermissao
     * @return
     */
    public boolean permissao(String nomePermissao) {
        boolean permissao;
        try {
            permissao = getPerfilUsuarioLogado().consultarObjPermissaoVO(nomePermissao) != null || getUsuarioLogado().getAdministrador();
        } catch (Exception e) {
            permissao = false;
        }

        return permissao;
    }

    public boolean permissao(String nomePermissao, UsuarioVO NomeEmpresa, EmpresaVO empresaLogada) {
        boolean permissao;
        try {
            permissao = getPerfilUsuarioLogado(NomeEmpresa, empresaLogada).consultarObjPermissaoVO(nomePermissao) != null || getUsuarioLogado().getAdministrador();
        } catch (Exception e) {
            permissao = false;
        }

        return permissao;
    }

    public void validarPermissaoEntidade(EmpresaVO empresaVO, UsuarioVO usuarioVO, String idEntidade, Integer constantesAcessoID) throws Exception {
        if (usuarioVO == null || empresaVO == null) {
            return;
        }
        if (usuarioVO.getAdministrador()) {
            return;
        }

        PerfilAcessoVO perfilAcesso = obterPerfilAcessoUsuarioLogado(empresaVO, usuarioVO);
        if (!verificarPermissaoUsuario(usuarioVO, idEntidade, constantesAcessoID, perfilAcesso)) {
            String nomeUsuario = usuarioVO.getNome().toUpperCase();

            OpcoesPerfilAcesso opcoesPerfilAcesso = new OpcoesPerfilAcesso();
            String permissao = opcoesPerfilAcesso.getPermissaoPorEntidade(idEntidade);
            String msgErro = "Este Usuário (" + nomeUsuario + " - " + perfilAcesso.getNome().toUpperCase()
                    + ") não possui permissão para esta operação, \"INCLUIR " + permissao.toUpperCase() + "\"";
            throw new AcessoException(msgErro);
        }
    }

    private PerfilAcessoVO obterPerfilAcessoUsuarioLogado(EmpresaVO empresaVO, UsuarioVO usuarioVO) throws Exception {
        PerfilAcessoVO p = null;
        if (!UteisValidacao.emptyList(usuarioVO.getUsuarioPerfilAcessoVOs())) {
            Iterator i = usuarioVO.getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = (UsuarioPerfilAcessoVO) i.next();
                if (usuarioPerfilAcessoVO.getEmpresa().getCodigo().equals(empresaVO.getCodigo())) {
                    p = getFacade().getPerfilAcesso().consultarPorChavePrimaria(usuarioPerfilAcessoVO.getPerfilAcesso().getCodigo(),
                            Uteis.NIVELMONTARDADOS_TODOS);
                    break;
                }
            }

        }
        return p;
    }

    private boolean verificarPermissaoUsuario(UsuarioVO usuarioVO, String nomeEntidade, int operacao, PerfilAcessoVO perfilAcesso) {
        if (usuarioVO.isPseudo()) {
            return true;
        }
        if (nomeEntidade.equals("")) {
            return false;
        }
        if ((usuarioVO == null) || (usuarioVO.getUsername().equalsIgnoreCase(""))) {
            return false;
        }
        if (perfilAcesso == null) {
            return false;
        }
        return verificarPermissaoOperacao(perfilAcesso.getPermissaoVOs(),
                nomeEntidade, operacao);
    }

    private boolean verificarPermissaoOperacao(List permissoes, String nomeEntidade, int operacao) {
        Iterator i = permissoes.iterator();
        while (i.hasNext()) {
            PermissaoVO obj = (PermissaoVO) i.next();
            if (obj.getNomeEntidade().equals(nomeEntidade)) {
                return !obj.getTipoPermissao().equals(OpcaoPerfilAcesso.TP_ENTIDADE) || verificarPermissaoOperacao(obj, operacao);
            }
        }
        return false;
    }

    private boolean verificarPermissaoOperacao(PermissaoVO permissaoVO,
                                               int operacao) {
        String operStr = "(" + operacao + ")";
        if (permissaoVO == null) {
            return false;
        }
        int resultado = permissaoVO.getPermissoes().indexOf(operStr);
        return resultado != -1;
    }

    public void permissaoFuncionalidade(UsuarioVO usuario, String permissao, String descricao) throws Exception {
        if (usuario.getAdministrador()) {
            setMensagemDetalhada("", "");
            usuario.setPorcetagemDescontoContrato(100.0);
            return;
        }
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            throw new Exception("O usuário informado não possui perfil de acesso.");
        }

        if (getEmpresaLogado().getCodigo() == 0) {
            throw new Exception("Não há nenhuma empresa logada.");
        }
        Iterator i = usuario.getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(), usuario, permissao, descricao);
                usuario.setPorcetagemDescontoContrato(usuarioPerfilAcesso.getPerfilAcesso().getPorcetagemDescontoContrato());
            }
        }
    }

    public void notificarOuvintes(final String descNotf) {
        String url = getPropertyValue("urlNotificacaoAcesso");
        String timeZone = null;
        try {
            timeZone = getFacade().getEmpresa().obterTimeZoneDefault(getEmpresaLogado().getCodigo());
        } catch (Exception ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        notificarOuvintes(descNotf, url, getKey(), timeZone);
    }

    public static void notificarOuvintes(final String descNotf, String urlNotificacao, final String key) {
        notificarOuvintes(descNotf, urlNotificacao, key, null);
    }

    public static void notificarOuvintes(final String descNotf, String urlNotificacao, final String key, String timeZone) {
        try {
            URL u = new URL(urlNotificacao);
            Map<String, String> mapa = new HashMap<String, String>();
            //notificar todas as instancias por 2 minutos que precisa executar ALGUMA COISA
            mapa.put("op", "createNOTF");
            mapa.put("desc", descNotf);
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss",
                    Calendario.getDefaultLocale());
            if (!UteisValidacao.emptyString(timeZone)) {
                df.setTimeZone(TimeZone.getTimeZone(timeZone));
            }
            mapa.put("dti", df.format(Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, -1)));
            mapa.put("dtf", df.format(Uteis.somarCampoData(Calendario.hoje(), Calendar.MINUTE, 20)));
            mapa.put("tipoNOTF", "WS");
            mapa.put("chave", key);
            mapa.put("localAcesso", "");
            mapa.put("propagable", "s");
            mapa.put("timeZone", timeZone);
            if (urlNotificacao == null)
                urlNotificacao = PropsService.getPropertyValue("urlNotificacaoAcesso");
            //String url = getPropertyValue("urlNotificacaoAcesso");
            new ThreadRequest(urlNotificacao, mapa).start();

        } catch (Exception e) {
            Uteis.logarDebug("Erro ao notificar ouvintes... " + e.getMessage());
        }
    }

    public <T extends SuperVO> void registrarLogListas(List<T> listaAntes, List<T> listaDepois, String nomeEntidade,
                                                       int codPessoa, int codEntidadePrinc, T novo) throws Exception {
        Map<Integer, T> mapAntes = obterMapaDaListaLog(listaAntes);
        Map<Integer, T> mapDepois = obterMapaDaListaLog(listaDepois);
        Set<Integer> keySet = mapDepois.keySet();
        for (Integer chaveDepois : keySet) {
            T objAntes = mapAntes.get(chaveDepois);
            T objDepois = mapDepois.get(chaveDepois);
            if (objAntes == null) {
                objDepois.setNovoObj(true);
                objDepois.setObjetoVOAntesAlteracao(novo);
                registrarLogObjetoVO(objDepois, codEntidadePrinc, nomeEntidade, codPessoa);
            } else {
                objDepois.setNovoObj(false);
                objDepois.setObjetoVOAntesAlteracao(objAntes);
                registrarLogObjetoVO(objDepois, codEntidadePrinc, nomeEntidade, codPessoa);
            }
        }

    }

    public <T extends SuperVO> Map<Integer, T> obterMapaDaListaLog(List<T> lista) throws Exception {
        Map<Integer, T> map = new HashMap<Integer, T>();
        for (T obj : lista) {
            Field PK = obterCampoChavePrimaria(obj);
            map.put(PK.getInt(obj), obj);
        }
        return map;
    }

    public <T extends SuperVO> Field obterCampoChavePrimaria(T obj) {
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(ChavePrimaria.class)) {
                return field;
            }
        }
        return null;
    }

    public void validarPermissao(String permissao, String descricao, UsuarioVO usuario) throws Exception {
        if (usuario.getUsuarioPerfilAcessoVOs().isEmpty()) {
            if (usuario.getAdministrador()) {
                limparMsg();
                setSucesso(true);
                return;
            }
            throw new Exception("O usuário informado não tem nenhum perfil acesso informado.");
        }
        Iterator i = usuario.getUsuarioPerfilAcessoVOs().iterator();
        while (i.hasNext()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
            if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(
                        usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                        usuario, permissao, descricao);
            }
        }
    }

    public List<SelectItem> obterListaEmpresas(List<EmpresaVO> resultadoConsulta) {
        return obterListaEmpresas(resultadoConsulta, "");
    }

    public List<SelectItem> obterListaEmpresas(List<EmpresaVO> resultadoConsulta, String labelItemTodas) {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(0, labelItemTodas));
        for (EmpresaVO obj : resultadoConsulta) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getNome()));
        }
        return lista;
    }

    public List<SelectItem> obterListaEmpresasEstado(List<EmpresaVO> resultadoConsulta) {
        List<SelectItem> lista = new ArrayList<SelectItem>();
        lista.add(new SelectItem(0, ""));
        for (EmpresaVO obj : resultadoConsulta) {
            lista.add(new SelectItem(obj.getCodigo(), obj.getNome(), obj.getEstado().getCodigo().toString()));
        }
        return lista;
    }


    public void setListaEmpresas(List<SelectItem> listaEmpresasLocais) {
        this.listaEmpresas = listaEmpresasLocais;
    }

    public List<SelectItem> getListaEmpresas() {
        return listaEmpresas;
    }

    public void montarListaEmpresasComItemTodas(String label) throws Exception {
        addTodasEmpresas();
        montarListaEmpresas();
    }

    public void montarListaEmpresasComItemTodas() throws Exception {
        addTodasEmpresas();
        montarListaEmpresas();
    }

    private void addTodasEmpresas() {
        listaEmpresas.add(new SelectItem(0));
        listaEmpresas.get(0).setLabel(LABEL_TODAS_EMPRESAS);
    }

    public void montarListaEmpresasComItemTodasClubeVantagens() throws Exception {
        addTodasEmpresas();
        montarListaEmpresasCV();
    }

    public void montarListaEmpresasCV() throws Exception {
        for (Object usuarioPerfilAcessoVO : getUsuario().getUsuarioPerfilAcessoVOs()) {
            if (((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO).getEmpresa().isTrabalharComPontuacao()) {
                SelectItem item = new SelectItem();
                item.setValue(((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO).getEmpresa().getCodigo());
                item.setLabel(((UsuarioPerfilAcessoVO) usuarioPerfilAcessoVO).getEmpresa().getNome());
                listaEmpresas.add(item);
            }
        }
    }

    public void montarListaEmpresas() throws Exception {
        listaEmpresas = obterListaEmpresas(getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_MINIMOS));
    }

    public List<EmpresaVO> getListaEmpresasEstado() {
        return listaEmpresasEstado;
    }

    public void setListaEmpresasEstado(List<EmpresaVO> listaEmpresasEstado) {
        this.listaEmpresasEstado = listaEmpresasEstado;
    }

    public void montarListaEmpresasEstado() throws Exception {
        listaEmpresasEstado = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public boolean getPermitirAlterarDataSistema() {
        try {
            boolean desv = isDesv()
                    || (!UteisValidacao.emptyString(getKey())
                    && PropsService.getPropertyValue(PropsService.empresasPermitidasAlterarDataBase) != null
                    && PropsService.getPropertyValue(PropsService.empresasPermitidasAlterarDataBase).contains(getKey())
                    && PropsService.getPropertyValue(PropsService.dataLimiteEmpresaAlterarDataBase) != null
                    && Calendario.maiorOuIgual(Uteis.getDate(PropsService.getPropertyValue(PropsService.dataLimiteEmpresaAlterarDataBase)), Calendario.hoje()));
            if (desv) {
                JSFUtilities.storeOnSession("desv", "true");
            } else {
                JSFUtilities.storeOnSession("desv", "");
            }
            return desv;
        } catch (Exception e) {
            Uteis.logar(e, SuperControle.class);
            return isDesv();
        }

    }

    public static boolean isCMYK(File imagem) {
        boolean result = false;
        BufferedImage img = null;
        try {
            img = ImageIO.read(imagem);
        } catch (IOException e) {
            return true;
        }
        if (img != null) {
            int colorSpaceType = img.getColorModel().getColorSpace().getType();
            result = colorSpaceType == ColorSpace.TYPE_CMYK;
        }
        return result;
    }

    public String getPaintFotoDaNuvem(final String fotokey) {
        return Uteis.getPaintFotoDaNuvem(fotokey);
    }

    public String getFotoKeyUsuarioLogado() {
        try {
            return getPaintFotoDaNuvem(getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey());
        } catch (Exception ex) {
            return "";
        }
    }

    public void paintFotoUsuario(OutputStream out, Object data) throws Exception {
        SuperControle.paintFoto(out, getUsuarioLogado().getColaboradorVO().getPessoa().getFoto());
    }

    public void mensagensTela(Boolean sucesso, String msgId, String mensagemDetalhada) {
        setMensagemDetalhada(msgId, mensagemDetalhada);
        setErro(!sucesso);
        setSucesso(sucesso);
    }

    public void paintFotoSemPesquisarNoBanco(OutputStream out, Object data) throws Exception {
        byte[] foto = null;
        try {

            foto = getFacade().getEmpresa().obterFoto(getKey(), getEmpresaLogado().getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA);
            paintFotoEmpresa(out, foto);
        } catch (Exception ignored) {

        }


    }

    public void limparControladoresVenda() {
        MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get("MovParcelaControle");
        if (movParcelaControle != null) {
            movParcelaControle.liberarBackingBeanMemoria("MovParcelaControle");
            movParcelaControle = null;
        }
        MovPagamentoControle movPagamentoControle = (MovPagamentoControle) context().getExternalContext().getSessionMap().get("MovPagamentoControle");
        if (movPagamentoControle != null) {
            movPagamentoControle.liberarBackingBeanMemoria("MovPagamentoControle");
            movPagamentoControle = null;
        }
        ContratoControle contratoControle = (ContratoControle) context().getExternalContext().getSessionMap().get("ContratoControle");
        if (contratoControle != null) {
            contratoControle.liberarBackingBeanMemoria("ContratoControle");
            contratoControle = null;
        }
        AulaAvulsaDiariaControle aulaAvulsaDiariaControle = (AulaAvulsaDiariaControle) context().getExternalContext().getSessionMap().get("AulaAvulsaDiariaControle");
        if (aulaAvulsaDiariaControle != null) {
            aulaAvulsaDiariaControle.liberarBackingBeanMemoria("AulaAvulsaDiariaControle");
            aulaAvulsaDiariaControle = null;
        }
    }

    public List<SelectItem> getListaCategorias() {
        if (listaCategorias == null) {
            montarListaCategorias();
        }
        return listaCategorias;
    }

    public void setListaCategorias(List<SelectItem> listaCategorias) {
        this.listaCategorias = listaCategorias;
    }

    public void montarListaCategorias() {
        try {
            List categorias = getFacade().getCategoria().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_TODOS);
            listaCategorias = JSFUtilities.getSelectItemListFrom(categorias, "nome", "codigo", true, false);
        } catch (Exception err) {
            montarErro(err);
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.SEVERE, err.getMessage(), err);
        }
    }

    public static boolean getRedirectLoginServidorLocal() {
        return PropsService.isTrue(PropsService.redirectLoginServidorLocal);
    }

    public static String getValidarUsuarioOAMD() {
        return PropsService.getPropertyValue(PropsService.validarUsuarioOAMD);
    }

    public String getVersaoSistema_Apresentar() {
        return getVersaoSistema().replace("-SNAPSHOT", "");
    }

    public String getUsandoMC() {
        try {
            if (usandoMC == null) {
                if (getFacade().getMemCachedManager().getMemcachedOn()) {
                    usandoMC = " MC";
                } else {
                    usandoMC = "";
                }
            }

        } catch (Exception e) {
            usandoMC = "";
        }
        return usandoMC;
    }

    public String getUrloamdrecursomigracao() {
        return PropsService.getPropertyValue(PropsService.urlOamdSegura);
    }

    public static String getUrlOamd() {
        return PropsService.getPropertyValue(PropsService.urlOamd);
    }
    public static String getUrlOamdEmpresas() {
        return PropsService.getPropertyValue(PropsService.urlOamdEmpresas);
    }

    public static String getUrlIntegradorOamd() {
        return PropsService.getPropertyValue(PropsService.urlIntegradorOamd);
    }

    public static String getUrlModuloNFSe() {
        return PropsService.getPropertyValue(PropsService.urlModuloNFSe);
    }

    public static String getUrlMidiaSocial() {
        return PropsService.getPropertyValue(PropsService.urlMidiaSocial);
    }

    public static  String getUrlApiApp(){
        return  PropsService.getPropertyValue(PropsService.urlApiApp);
    }
    public String getLinkFacebook() {
        return LINK_FACEBOOK_PACTO;
    }

    public String getLinkYouTube() {
        return LINK_YOUTUBE;
    }

    public String getLinkTwitter() {
        return LINK_TWITTER;
    }

    public String getLinkSite() {
        return LINK_SITE;
    }

    public String getLinkInstagram() {
        return LINK_INSTAGRAM;
    }

    public void voltarParaTelaCliente() {
        try {
            ClienteControle clienteControle = getControlador(ClienteControle.class);
            if (clienteControle != null) {
                clienteControle.prepararTelaCliente(clienteControle.getClienteVO(), true);
            }
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("", "");
            redirect("/faces/cliente.jsp?matricula=" + clienteControle.getClienteVO().getMatricula());
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada(e);
        }
    }

    public boolean isApresentarVoltarParaCliente() {
        ClienteControle clienteControle = getControlador(ClienteControle.class);
        return (clienteControle != null) && (clienteControle.getClienteVO().getCodigo() > 0);
    }

    public Date getHoje() {
        return Calendario.hoje();
    }

    public boolean isFotoUsuarioPendente() {
        try {
            if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(0)) {
                return false;
            } else if (getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey() != null && !getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey().isEmpty()) {
                return false;
            } else if (getUsuarioLogado().getColaboradorVO().getPessoa().getFoto() != null && getUsuarioLogado().getColaboradorVO().getPessoa().getFoto().length != 0) {
                return false;
            } else if (isExibirModalFotoPendente()) {
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
        return false;
    }

    public String getStyleUsuario() {
        try {
            if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(0)) {
                return "width:50px;height:50px; border:1px solid #094771;border-radius:50%;";
            } else if (getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey() != null && !getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey().isEmpty()) {
                return "width:50px;height:50px; border:1px solid #094771;border-radius:50%;";
            } else if (getUsuarioLogado().getColaboradorVO().getPessoa().getFoto() != null && getUsuarioLogado().getColaboradorVO().getPessoa().getFoto().length != 0) {
                return "width:50px;height:50px; border:1px solid #094771;border-radius:50%;";
            } else {
                return "width:40px;height:40px; border:1px solid red;border-radius:50%;";
            }
        } catch (Exception ex) {
            return "width:40px;height:40px; border:1px solid red;border-radius:50%;";
        }
    }

    public String getTitleUsuario() {
        try {
            if (getUsuarioLogado().getColaboradorVO().getCodigo().equals(0)) {
                return "Usuário Logado: " + getUsuarioLogado().getUsername();
            } else if (getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey() != null && !getUsuarioLogado().getColaboradorVO().getPessoa().getFotoKey().isEmpty()) {
                return "Usuário Logado: " + getUsuarioLogado().getUsername();
            } else if (getUsuarioLogado().getColaboradorVO().getPessoa().getFoto() != null && getUsuarioLogado().getColaboradorVO().getPessoa().getFoto().length != 0) {
                return "Usuário Logado: " + getUsuarioLogado().getUsername();
            } else {
                return "Adicione uma foto ao seu usuário!";
            }
        } catch (Exception ex) {
            return "";
        }
    }

    public void montarConfiguracoesUsuario() {
        try {
            ConfiguracaoSistemaUsuarioVO obj = getFacade().getConfiguracaoSistemaUsuario().consultarUltimoPorCodigoUsuario(getUsuarioLogado().getCodigo(), ConfiguracaoUsuarioEnum.MODAL_FOTO_PENDENTE);
            if (obj != null && !UteisValidacao.emptyNumber(obj.getCodigo())) {
                exibirModalFotoPendente = Boolean.parseBoolean(obj.getValor());
            } else {
                exibirModalFotoPendente = true;
            }
        } catch (Exception ex) {
            montarErro(ex);
            exibirModalFotoPendente = false;
        }
    }

    public void salvarExibirModalFotoPendente() {
        try {
            ConfiguracaoSistemaUsuarioVO obj = getFacade().getConfiguracaoSistemaUsuario().consultarUltimoPorCodigoUsuario(getUsuarioLogado().getCodigo(), ConfiguracaoUsuarioEnum.MODAL_FOTO_PENDENTE);
            exibirModalFotoPendente = !exibirModalFotoPendente;
            obj.setValor(exibirModalFotoPendente.toString());
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                obj.setValor(exibirModalFotoPendente.toString());
                getFacade().getConfiguracaoSistemaUsuario().alterar(obj);
            } else {
                obj.setTipo(ConfiguracaoUsuarioEnum.MODAL_FOTO_PENDENTE);
                obj.setUsuario(getUsuarioLogado());
                obj.setDataRegistro(Calendario.hoje());
                obj.setValor(exibirModalFotoPendente.toString());
                getFacade().getConfiguracaoSistemaUsuario().incluir(obj);
            }
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isExibirModalFotoPendente() {
        if (exibirModalFotoPendente == null) {
            montarConfiguracoesUsuario();
        }
        return exibirModalFotoPendente;
    }

    public void setExibirModalFotoPendente(boolean exibirModalFotoPendente) {
        this.exibirModalFotoPendente = exibirModalFotoPendente;
    }

    public void recarregarFotoUsuario() throws Exception {
        JSFUtilities.getResponse().addHeader("Expires", Calendario.anterior(Calendar.DATE, Calendario.hoje()).toString());
        JSFUtilities.getResponse().addDateHeader("Last-Modified", new Date().getTime());
        JSFUtilities.getResponse().addHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0, post-check=0, pre-check=0");
        JSFUtilities.getResponse().addHeader("Pragma", "no-cache");
        if (isFotosNaNuvem()) {
            final String fotoKey = getFacade().getPessoa().obterFotoKey(
                    getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo());
            if (!UteisValidacao.emptyString(fotoKey)) {
                getUsuarioLogado().getColaboradorVO().getPessoa().setFotoKey(fotoKey + "?time=" + getTimeStamp());
            }
        } else {
            getUsuarioLogado().getColaboradorVO().getPessoa().setFoto(getFacade().getPessoa().obterFoto(
                    getKey(),
                    getUsuarioLogado().getColaboradorVO().getPessoa().getCodigo()));
        }
        sincronizarFotoLoginNovo();
    }

    private void sincronizarFotoLoginNovo() throws Exception {
        if (UteisValidacao.emptyString(getUsuarioLogado().getUsuarioGeral())) {
            getUsuarioLogado().setUsuarioGeral(getFacade().getUsuario().consultarUsuarioGeral(getUsuarioLogado()));
        }
        if (!UteisValidacao.emptyString(getUsuarioLogado().getUsuarioGeral())) {
            SincronizarUsuarioNovoLogin.atualizarUsuarioGeral(getUsuarioLogado().getCodigo(), Conexao.getFromSession(),
                    getEmpresaLogado().getCodigo(), getUsuarioLogado(), getIpCliente(), getIntegracaoNovoLogin());
        }
    }

    public String getMensagemNotificar() {
        return getMensagemNotificar(false);
    }

    public String getMensagemNotificar(boolean errorAutoremove) {
        if (!UteisValidacao.emptyString(getMensagemDetalhada())) {
            String msgDetalhada = getMensagemDetalhada().replaceAll("\"", "\\\\\"");
            if (getErro()) {
                return "try{ Notifier." + (errorAutoremove ? "errorRemove" : "error") + "(\"" + msgDetalhada + "\",\"" + getMensagem() + "\"); } catch(e){}";
            } else if (getAtencao()) {
                return "try{ Notifier.info(\"" + msgDetalhada + "\",\"" + getMensagem() + "\");} catch(e){}";
            } else if (getWarning()) {
                return "try{ Notifier.warning(\"" + msgDetalhada + "\",\"" + getMensagem() + "\");} catch(e){}";
            }
        } else if (!UteisValidacao.emptyString(getMensagem()) && (UteisValidacao.emptyString(getMensagemID()) || !getMensagemID().equals("msg_dados_consultados"))) {
            if (getSucesso()) {
                return "try{ Notifier.success(\"" + getMensagem().replaceAll("\"", "\\\\\"") + "\");} catch(e){}";
            }
        }
        return "";
    }

    public static boolean isBuscarConhecimentoUCP() {
//        IN-499 - UCP desativada!
//        return PropsService.isTrue(PropsService.buscarConhecimentoUCP);
        return false;
    }

    public static boolean isAtualizacaoCadastral() {
        return PropsService.isTrue(PropsService.atualizacaoCadastral);
    }

    public static boolean isUsarUrlRecursoEmpresa() {
        return PropsService.isTrue(PropsService.usarUrlRecursoEmpresa);
    }

    public void validarChaveComSessao(String key) throws Exception {
        if (context() != null && !key.equals(getKey()) && !UteisValidacao.emptyString(key)) {
            salvarArquivoLOG(key, Thread.currentThread().getStackTrace()[3].getMethodName() + "=>" + Thread.currentThread().getStackTrace()[2].getMethodName());
            throw new Exception("Foi detectado duas abas em aberto, favor feche e tente novamente.");
        }
    }

    public void salvarArquivoLOG(String key, String metodo) throws Exception {
        String path = PropsService.getPropertyValue(PropsService.diretorioArquivos);
        String url = path + LOG_CHAVE_DIFERENTE_SESSAO + ".txt";
        File file = new File(url);
        StringBuilder textoArquivo = new StringBuilder(Uteis.obterStringArquivoTexto(file));
        textoArquivo.append("\n");
        textoArquivo.append("CHAVE SESSÃO " + getKey() + " / CHAVE PARAMETRO " + key + " ; METODO " + metodo + " ; " + getUsuario().getNome() + " ; " + Uteis.getDataComHHMM(Calendario.hoje()));
        Uteis.salvarArquivo(LOG_CHAVE_DIFERENTE_SESSAO, textoArquivo.toString(), PropsService.getPropertyValue(PropsService.diretorioArquivos));
    }

    public void setMessageInInput(String identificador, String mensagem) {
        context().addMessage(identificador, new FacesMessage(mensagem));
    }

    public static boolean getHabilitarLembreteSolicitacoes() {
        return PropsService.isTrue(PropsService.habilitarLembreteSolicitacoes);
    }

    public static boolean getValidarVersaoBD() {
        return PropsService.isTrue(PropsService.validarVersaoBD);
    }

    public static boolean getValidarBloqueioOutros() {
        return PropsService.isTrue(PropsService.validarBloqueioOutros);
    }


    private String getNomeArquivoErroAbasGerado(String key) {
        return SUBDIRETORIO_ERRO_ABAS + File.separator + "erroAbas_" + key + ".txt";
    }

    private String getNomeArquivoUtilizacaoTela(String key) {
        return SUBDIRETORIO_UTILIZACAO_TELA + File.separator + "utilizacaoTela_" + key + ".txt";
    }


    private File getArquivoErroAbas(String key) {
        if (((key == null) || (key.trim().equals("")))) {
            return null;
        }
        try {
            criarDiretorioLogErroAbas();
            File file = new File(getDiretorioArquivos() + File.separator + getNomeArquivoErroAbasGerado(key));
            if (!file.exists()) {
                file.createNewFile();
            }
            return file;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CRIAR ARQUIVO ERRO_ABAS. Erro:" + e.getMessage());
        }
        return null;
    }

    private File getArquivoUtilizacaoTela(String key) {
        if (((key == null) || (key.trim().equals("")))) {
            return null;
        }
        try {
            criarDiretorioLogUtilizacaoTela();
            File file = new File(getDiretorioArquivos() + File.separator + getNomeArquivoUtilizacaoTela(key));
            if (!file.exists()) {
                file.createNewFile();
            }
            return file;
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO CRIAR ARQUIVO UTILIZACAO TELAS. Erro:" + e.getMessage());
        }
        return null;
    }


    public void gerarLogErroAbasOuPopup(String msgLog, String nomeCliente) {
        try {
            String key = (JSFUtilities.getFromSession("key") != null) ? ((String) (JSFUtilities.getFromSession("key"))) : "";
            File file = getArquivoErroAbas(key);
            if (file != null) {
                StringBuilder msg = new StringBuilder();
                SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
                msg.append(sdf.format(Calendario.hoje()));
                msg.append(" ").append(getClass().getSimpleName());
                msg.append("-Usuario:").append(getUsuarioLogado().getNome());
                if (!getUsuarioLogado().getUserOamd().equals("")) {
                    msg.append("-UserOamd:" + getUsuarioLogado().getUserOamd());
                }
                msg.append("-Cliente:").append(nomeCliente);
                msg.append(" - ").append(msgLog);

                FileWriter fileWriter = new FileWriter(file, true);
                BufferedWriter bw = new BufferedWriter(fileWriter);
                bw.write(msg.toString());
                bw.newLine();
                bw.close();
                fileWriter.close();
            }
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO GERAR LOG ERRO_ABAS. Erro:" + e.getMessage());
        }
    }

    public void gerarLogUtilizacaoTela(String tela) {
        try {
            String key = (JSFUtilities.getFromSession("key") != null) ? ((String) (JSFUtilities.getFromSession("key"))) : "";
            File file = getArquivoUtilizacaoTela(key);
            if (file != null) {
                StringBuilder msg = new StringBuilder();
                SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
                msg.append(sdf.format(Calendario.hoje()));
                msg.append(" ").append(getClass().getSimpleName());
                msg.append("-Usuario:").append(getUsuarioLogado().getNome());
                if (!getUsuarioLogado().getUserOamd().equals("")) {
                    msg.append("-UserOamd:" + getUsuarioLogado().getUserOamd());
                }
                msg.append("-Tela:").append(tela);
                FileWriter fileWriter = new FileWriter(file, true);
                BufferedWriter bw = new BufferedWriter(fileWriter);
                bw.write(msg.toString());
                bw.newLine();
                bw.close();
                fileWriter.close();
            }
        } catch (Exception e) {
            Logger.getLogger(this.getClass().getSimpleName()).log(Level.SEVERE, "#### ERRO AO EXECUTAR gerarLogUtilizacaoTela. Erro:" + e.getMessage());
        }
    }


    private void criarDiretorioLogErroAbas() {
        File arquivo = new File(getDiretorioArquivos());
        if (!arquivo.exists()) {
            boolean criou = arquivo.mkdir();
            if (criou)
                Uteis.logar(null, "Criou com sucesso o diret?rio de arquivos.");
            else
                Uteis.logar(null, "N?o Criou diret?rio de arquivos.");
        }
        arquivo = new File(getDiretorioArquivos() + File.separator + SUBDIRETORIO_ERRO_ABAS);
        if (!arquivo.exists()) {
            boolean criou = arquivo.mkdir();
            if (criou)
                Uteis.logar(null, "Criou com sucesso o diretório de arquivos.");
            else
                Uteis.logar(null, "Não Criou diretório de arquivos.");
        }
    }

    private void criarDiretorioLogUtilizacaoTela() {
        File arquivo = new File(getDiretorioArquivos());
        if (!arquivo.exists()) {
            boolean criou = arquivo.mkdir();
            if (criou)
                Uteis.logar(null, "Criou com sucesso o diretório de arquivos.");
            else
                Uteis.logar(null, "Não Criou diretório de arquivos.");
        }
        arquivo = new File(getDiretorioArquivos() + File.separator + SUBDIRETORIO_UTILIZACAO_TELA);
        if (!arquivo.exists()) {
            boolean criou = arquivo.mkdir();
            if (criou)
                Uteis.logar(null, "Criou com sucesso o diretório de arquivos.");
            else
                Uteis.logar(null, "Não Criou diretório de arquivos.");
        }
    }

    public static ConfiguracaoSistemaCRMVO getConfiguracaoSMTPRobo() {
        ConfiguracaoSistemaCRMVO config = new ConfiguracaoSistemaCRMVO();
        config.setLogin(PropsService.getPropertyValue(PropsService.smtpLoginRobo));
        config.setEmailPadrao(PropsService.getPropertyValue(PropsService.smtpEmailRobo));
        config.setSenha(PropsService.getPropertyValue(PropsService.smtpSenhaRobo));
        config.setConexaoSegura(PropsService.isTrue(PropsService.smtpConexaoSeguraRobo));
        config.setIniciarTLS(PropsService.isTrue(PropsService.iniciarTLS));
        config.setMailServer(PropsService.getPropertyValue(PropsService.smtpServerRobo));
        return config;
    }

    public Boolean getProcessandoOperacao() {
        return processandoOperacao;
    }

    public void setProcessandoOperacao(Boolean processandoOperacao) {
        this.processandoOperacao = processandoOperacao;
    }

    public static ConfiguracaoSistemaCRMVO getConfiguracaoSMTPNoReply() {
        ConfiguracaoSistemaCRMVO config = getConfiguracaoSMTPRobo();
        config.setEmailPadrao(PropsService.getPropertyValue(PropsService.smtpEmailNoReply));
        return config;
    }

    public boolean isFotoTempBVExiste() throws Exception {
        File fotoTemp = Uteis.repurarFotoPessoaTemp(getKey(), getUsuarioLogado().getCodigo());
        return fotoTemp != null;
    }

    public static String getUserAgent() {
        ExternalContext externalContext = FacesContext.getCurrentInstance().getExternalContext();
        return externalContext.getRequestHeaderMap().get("User-Agent");
    }

    public String getAuthenticationKeyCappta() {
        try {
            return PropsService.getPropertyValue(PropsService.authenticationKeyCappta);
        } catch (Exception e) {
            return "";
        }
    }

    public boolean validarPermissaoEmpresas() {
        try {
            if (getUsuarioLogado().getUsuarioPerfilAcessoVOs().isEmpty()) {
                if (getUsuarioLogado().getAdministrador()) {
                    setMensagemDetalhada("");
                    setSucesso(true);
                    return true;
                }
                throw new Exception("O usu?rio informado n?o tem nenhum perfil acesso informado.");
            }
            Iterator i = getUsuarioLogado().getUsuarioPerfilAcessoVOs().iterator();
            while (i.hasNext()) {
                UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) i.next();
                if (getEmpresaLogado().getCodigo().equals(usuarioPerfilAcesso.getEmpresa().getCodigo().intValue())) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(getFacade().getPermissao().consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    getFacade().getControleAcesso().verificarPermissaoUsuarioFuncionalidade(usuarioPerfilAcesso.getPerfilAcesso(),
                            getUsuarioLogado(), "LancarVisualizarLancamentosEmpresas", "9.23 - Lançar/Visualizar Lançamentos Financeiros para todas as Empresas");
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    public void gravarFiltro(FiltrosEnum filtro, String valor) throws Exception {
        getFacade().getConfiguracaoSistema().incluirFiltro(getUsuarioLogado().getCodigo(),
                filtro, valor);
    }

    public String obterFiltro(FiltrosEnum filtro) throws Exception {
        return getFacade().getConfiguracaoSistema().obterFiltro(getUsuarioLogado().getCodigo(), filtro);
    }

    public void notificarRecursoEmpresa(final RecursoSistema recurso, Long tempoRequisicao, int quantidadeDiasPesquisa) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    getKey(),
                    getUsuarioLogado().getAdministrador(),
                    isUsarUrlRecursoEmpresa(),
                    getEmpresaLogado().getCodigo(),
                    getUsuarioLogado().getUsername(),
                    getEmpresaLogado().getNome(),
                    getEmpresaLogado().getCidade().getNome(),
                    getEmpresaLogado().getEstado().getSigla(),
                    getEmpresaLogado().getPais().getNome(),
                    tempoRequisicao,
                    quantidadeDiasPesquisa,
                    context()
            );
        } catch (Exception e) {
            if (JSFUtilities.isJSFContext()) {
                Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
            }
        }
    }

    public void notificarRecursoEmpresa(final RecursoSistema recurso, Long tempoRequisicao) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    getKey(),
                    getUsuarioLogado().getAdministrador(),
                    isUsarUrlRecursoEmpresa(),
                    getEmpresaLogado().getCodigo(),
                    getUsuarioLogado().getUsername(),
                    getEmpresaLogado().getNome(),
                    getEmpresaLogado().getCidade().getNome(),
                    getEmpresaLogado().getEstado().getSigla(),
                    getEmpresaLogado().getPais().getNome(),
                    tempoRequisicao,
                    context()
            );
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public void notificarRecursoEmpresa(final RecursoSistema recurso) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    getKey(),
                    getUsuarioLogado().getAdministrador(),
                    isUsarUrlRecursoEmpresa(),
                    getEmpresaLogado().getCodigo(),
                    getUsuarioLogado().getUsername(),
                    getEmpresaLogado().getNome(),
                    getEmpresaLogado().getCidade().getNome(),
                    getEmpresaLogado().getEstado().getSigla(),
                    getEmpresaLogado().getPais().getNome(),
                    context()
            );
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static void notificarRecursoEmpresa(Connection con, final String chave, final RecursoSistema recurso, String empresa, String usuario) {
        try {
            Empresa daoEmpresa = new Empresa(con);
            EmpresaVO empresaVO = daoEmpresa.consultarPorCodigo(Integer.valueOf(empresa), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            daoEmpresa = null;

            UsuarioVO usuarioVO;
            if (UteisValidacao.emptyString(usuario)) {
                Usuario daoUsuario = new Usuario(con);
                usuarioVO = daoUsuario.consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                daoUsuario = null;
            } else {
                usuarioVO = new UsuarioVO();
                usuarioVO.setUsername(usuario);
                usuarioVO.setAdministrador(false);
            }

            notificarRecursoEmpresa(chave, recurso, usuarioVO, empresaVO);
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static void notificarRecursoEmpresa(Connection con, final String chave, final RecursoSistema recurso, String empresa) {
        notificarRecursoEmpresa(con, chave, recurso, empresa, null);
    }

    public static void notificarRecursoEmpresa(final String chave, final RecursoSistema recurso, UsuarioVO usuarioVO, EmpresaVO empresaVO) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    chave,
                    usuarioVO.getAdministrador(),
                    isUsarUrlRecursoEmpresa(),
                    empresaVO.getCodigo(),
                    usuarioVO.getUsername(),
                    empresaVO.getNome(),
                    empresaVO.getCidade().getNome(),
                    empresaVO.getEstado().getSigla(),
                    empresaVO.getPais().getNome(),
                    null
            );
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static void notificarRecursoEmpresa(final String chave, final RecursoSistema recurso, UsuarioVO usuarioVO, EmpresaVO empresaVO, Long tempoRequisicao) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    chave,
                    usuarioVO.getAdministrador(),
                    isUsarUrlRecursoEmpresa(),
                    empresaVO.getCodigo(),
                    usuarioVO.getUsername(),
                    empresaVO.getNome(),
                    empresaVO.getCidade().getNome(),
                    empresaVO.getEstado().getSigla(),
                    empresaVO.getPais().getNome(),
                    tempoRequisicao,
                    null
            );
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    public static void notificarRecursoEmpresa(final String chave, final RecursoSistema recurso, UsuarioVO usuarioVO, EmpresaVO empresaVO, Long tempoRequisicao, int intervaloTempoPesquisado) {
        try {
            EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
                    recurso,
                    chave,
                    usuarioVO.getAdministrador(),
                    isUsarUrlRecursoEmpresa(),
                    empresaVO.getCodigo(),
                    usuarioVO.getUsername(),
                    empresaVO.getNome(),
                    empresaVO.getCidade().getNome(),
                    empresaVO.getEstado().getSigla(),
                    empresaVO.getPais().getNome(),
                    tempoRequisicao,
                    intervaloTempoPesquisado,
                    null
            );
        } catch (Exception e) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, e);
        }
    }

    protected final <V extends SuperVO> void criarLogInclusao(V vo) throws Exception {
        String tabela = vo.getClass().getSimpleName().replace("VO", "").toUpperCase();
        try {
            vo.setObjetoVOAntesAlteracao(vo.getClass().newInstance());
            vo.setNovoObj(true);
            registrarLogObjetoVO(vo, vo.getCodigo(), tabela, 0);
            vo.setNovoObj(false);
            vo.registrarObjetoVOAntesDaAlteracao();
        } catch (Exception e) {
            registrarLogErroObjetoVO(tabela, vo.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE " + tabela, this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            throw e;
        }
    }

    protected final <V extends SuperVO> void criarLogAlteracao(V vo) throws Exception {
        String tabela = vo.getClass().getSimpleName().replace("VO", "").toUpperCase();
        try {
            registrarLogObjetoVO(vo, vo.getCodigo(), tabela, 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO(tabela, vo.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE " + tabela, this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            throw e;
        }
    }

    protected final <V extends SuperVO> void consultaLogs(V vo) {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.limparListaLog();
        if (vo.getCodigo() != null && vo.getCodigo() > 0) {
            String tabela = vo.getClass().getSimpleName().replace("VO", "").toUpperCase();
            loginControle.setNomeClasse(loginControle.getNomeEntidadeCamposLog("prt_" + tabela + "_tituloForm"));
            loginControle.consultarLogObjetoSelecionado(tabela, vo.getCodigo(), 0);
        }
    }

    public Boolean getValidarInadimplencia() {
        try {
            return Boolean.valueOf(PropsService.getPropertyValue(PropsService.validarInadimplencia));
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean getIntegraProtheus() {
        try {
            return Boolean.valueOf(PropsService.getPropertyValue(PropsService.integraProtheus));
        } catch (Exception e) {
            return false;
        }
    }

    public String obterURLAplicacao() {
        String url = request().getRequestURL().toString();
        return url.substring(0, url.indexOf("/faces/"));
    }

    public void montarErroComLog(Exception e) {
        Uteis.logar(e, SuperControle.class);
        setListaConsulta(new ArrayList());
        setMensagemDetalhada("msg_erro", e.getMessage());
        setSucesso(false);
        setErro(true);
    }

    protected Connection criarEGerenciarConexaoNaSessao(String key) throws Exception {
        Connection con = null;

        if (!isJaExisteUmaConexaoIniciada()) {
            con = new DAO().obterConexaoEspecifica(key);
            Conexao.storeOnSession(con);
            JSFUtilities.storeOnSession("key", key);
        }

        return con;
    }

    public boolean isJaExisteUmaConexaoIniciada() {
        boolean existe = false;
        try {
            Connection con = Conexao.getFromSession();
            existe = (con != null) && (!con.isClosed());
        } catch (SQLException ex) {
            Logger.getLogger(SuperControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return existe;
    }

    public Boolean getBannerEmergencia() {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return getUsuarioLogado().getPossuiPerfilAcessoAdministrador() && !UteisValidacao.emptyString(loginControle.getUrlBannerEmergencia());
        } catch (Exception e) {
            return false;
        }
    }

    public Boolean getBannerRetro() {
        try {
            String chavesdesconsiderarretro = PropsService.getPropertyValue("chavesdesconsiderarretro");
            return Boolean.valueOf(PropsService.getPropertyValue("bannerretro")) &&
                    (chavesdesconsiderarretro == null || !chavesdesconsiderarretro.contains(getKey()));
        } catch (Exception e) {
            return false;
        }
    }

    public boolean memcachedHabilidado() {
        boolean habilitado = false;
        try {
            String xml = Uteis.getXMLDocumentCFG(Uteis.nomeArqCFG);
            String ipServidorMemcached = Uteis.getValorTAG(xml, "ipServidoresMemCached");
            habilitado = !(UteisValidacao.emptyString(ipServidorMemcached) || ipServidorMemcached.equalsIgnoreCase("DISABLED"));
        } catch (Exception e) {
            Uteis.logar(e, SuperControle.class);
        }

        return habilitado;
    }

    public Map<Integer, EmpresaVO> obterMapaEmpresas(int nivelMontarDados) throws Exception {
        return getFacade().getEmpresa().obterMapaEmpresas();
    }

    public String getNomeEmpresaSelecionada(Integer filtroEmpresa) {
        String retorno = "";

        if (getListaEmpresas().isEmpty()) {
            try {
                return getEmpresaLogado().getNome();
            } catch (Exception e) {
                e.getStackTrace();
            }
        }

        for (SelectItem item : getListaEmpresas()) {
            if (item.getValue().equals(filtroEmpresa)) {
                return item.getLabel();
            }
        }
        return retorno;
    }

    public void notificarEmpresaNoBack() {
        notificarRecursoEmpresa(RecursoSistema.NAO_VOLTAR_PAGINA);
    }

    public void notificarEmpresaClickChat() {
        notificarRecursoEmpresa(RecursoSistema.PRE_MUDANCA_CHAT);
    }

    public void putObjectInSession(Object obj, String name) {
        getSession().setAttribute(name, obj);
    }

    public void removeObjectInSession(String name) {
        getSession().removeAttribute(name);
    }

    public Object getObjectInSession(String name) {
        return getSession().getAttribute(name);
    }

    public HttpSession getSession() {
        if (this.session == null) {
            FacesContext facesContext = FacesContext.getCurrentInstance();
            HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
            this.session = request.getSession();
        }
        return this.session;
    }

    public boolean isNFe() {
        if (JSFUtilities.isJSFContext()) {
            return JSFUtilities.getFromSession("isNFE") != null ? (Boolean) JSFUtilities.getFromSession("isNFE") : false;
        } else {
            return false;
        }
    }

    public String[] identificadorPessoaInternacional(String paisEmpresa) {
        String[] displayIdentificadorFront = new String[3];

        for (IdentificadorInternacionalEnum identificadorInternacionalEnum : values()) {
            if (paisEmpresa.equalsIgnoreCase(identificadorInternacionalEnum.getNomePais())) {
                displayIdentificadorFront[0] = identificadorInternacionalEnum.getIdentificadorPrimario();
                displayIdentificadorFront[1] = identificadorInternacionalEnum.getIdentificadorSecundadrio();
                displayIdentificadorFront[2] = identificadorInternacionalEnum.getIdentificadorTerciario();
                break;
            }
        }

        return displayIdentificadorFront;
    }

    protected boolean isAdmin() {
        try {
            return getUsuarioLogado() != null && getUsuarioLogado().getUsername() != null && getUsuarioLogado().getUsername().equalsIgnoreCase("admin");
        } catch (Exception e) {
            return false;
        }
    }

    protected boolean isPactobr(){
        try {
            return getUsuarioLogado() != null && getUsuarioLogado().getUsername() != null && getUsuarioLogado().getUsername().equalsIgnoreCase("pactobr");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isUsuarioPacto(){
        return isAdmin() || isPactobr();
    }

    public String getShortCommitHash() {
        return PropsService.getGitProperty(PropsService.GIT_SHORT_COMMIT_HASH);
    }

    public String getCommitHash() {
        return PropsService.getGitProperty(PropsService.GIT_COMMIT_HASH);
    }

    public String getCommitTime() {
        return PropsService.getGitProperty(PropsService.GIT_COMMIT_TIME);
    }

    public String getUrlServicoIntegracaoSendy() {
        return PropsService.getUrlServicoIntegracaoSendy();
    }

    public String getEmailIntegracaoSendy() {
        return PropsService.getEmailIntegracaoSendy();
    }

    public String getRedeEmpresas(){
        RedeEmpresaVO redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
        return redeEmpresaVO == null ? "": redeEmpresaVO.getNome();
    }

    public String getIsRedeEmpresas() {
        RedeEmpresaVO redeEmpresaVO = (RedeEmpresaVO) JSFUtilities.getFromSession(JSFUtilities.REDE_EMPRESA);
        return redeEmpresaVO == null ? "Não": "Sim";
    }

    public String getNomeResponsavelPacto(){
        CustomerSuccessTO customerSuccessTO = (CustomerSuccessTO) JSFUtilities.getFromSession(JSFUtilities.RESPONSAVEL_PACTO);
        return customerSuccessTO == null ? "": customerSuccessTO.getNome();
    }

    public Boolean isBloquearEmpresasNaoTipifcadas() {
        return PropsService.isTrue(PropsService.bloquearEmpresasNaoTipificadas);
    }

    public boolean isEmpresaProducao() throws Exception {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                return true;
            } else {
                return TipoEmpresaFinanceiro.PRODUCAO.equals(getEmpresaLogado().getTipoEmpresa());
            }
        } catch (Exception e) {
            Uteis.logarDebug(String.format("Erro %s ao verificar se empresa de chave %s isEmpresaProducao ", e.getMessage(), getKey()));
        }
        return false;
    }

    public String getMensagemTipoEmpresa() throws Exception {
        return getEmpresaLogado().getTipoEmpresa().getMensagem();
    }

    public Boolean getEnableSetLastActionTime() {
        if (enableSetLastActionTime == null)
            enableSetLastActionTime = PropsService.isTrue(PropsService.enableLastActionTime);
        return enableSetLastActionTime;
    }

    public static String getIpServidor() {
        String ipServidor = (String) JSFUtilities.getFromSession("ipServidor");
        if (UteisValidacao.emptyString(ipServidor)) {
            try {
                String urlRequest = "https://app.pactosolucoes.com.br/ip/v2.php";
                String retorno = ExecuteRequestHttpService.executeHttpRequest(urlRequest, null);
                if (retorno != null && !retorno.isEmpty()) {
                    ipServidor = retorno.replace("\n", "");
                    JSFUtilities.storeOnSession("ipServidor", ipServidor);
                    return ipServidor;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return ipServidor;
    }

    public boolean isAmbienteDesenvolvimentoTeste() {
        return Uteis.isAmbienteDesenvolvimentoTeste();
    }

    public Boolean getMenuZwUi() {
        try {
            Object menuZwUi = JSFUtilities.getFromSession("menuZwUi");
            if(menuZwUi == null){
                menuZwUi = isApresentarMenuZWUI();
            }
            if(menuZwUi == null){
                JSFUtilities.storeOnSession("menuZwUi", Boolean.TRUE);
                return true;
            }
            return (Boolean) menuZwUi;
        } catch (Exception e) {
            return false;
        }
    }

    public String mudarMenu(){
        JSFUtilities.storeOnSession("menuZwUi", !getMenuZwUi());
        try {
            MenuAcessoFacilControle menuAcessoFacilControle = JSFUtilities.getControlador(MenuAcessoFacilControle.class);
            menuAcessoFacilControle.setInicializado(false);
            menuAcessoFacilControle.inicializar();
            MenuControle menuControle = JSFUtilities.getControlador(MenuControle.class);
            menuControle.processarMenuExplorar();
            menuControle.processarMenus();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    public Boolean isApresentarMenuZWUI() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        if(loginControle.getModuloAtualCe()){
            return false;
        }
        if(loginControle.getModuloAtualEst()){
            return false;
        }
        return PropsService.isTrue(PropsService.enableMenuZwUI);
    }

    public Boolean getIntegracaoNovoLogin() {
        try {
            Object integNewLogin = JSFUtilities.getFromSession("integNewLogin");
            if (integNewLogin == null) {
                JSFUtilities.storeOnSession("integNewLogin", false);
            }
            Boolean boole = (Boolean) integNewLogin;
            return boole != null && boole;
        } catch (Exception e) {
            return false;
        }
    }

    public String mudarIntegracaoNovoLogin(){
        JSFUtilities.storeOnSession("integNewLogin", !getIntegracaoNovoLogin());
        if (getIntegracaoNovoLogin()) {
            try {
                ClientDiscoveryDataDTO dataDTO = DiscoveryMsService.urlsChave((String) JSFUtilities.getFromSession("key"));
                JSFUtilities.storeOnSession("urlLoginDeslogar", dataDTO.getServiceUrls().getLoginFrontUrl() + "/" + getUsuarioLogado().getLinguagem() + "/logout");
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return "";
    }

    public Boolean existeMaisDeUmaUnidade() throws Exception {
        return getFacade().getEmpresa().quantidadeEmpresas(true) > 1;
    }

    public String getIdioma() throws Exception {
        selecionaIdioma();
        return idioma;
    }

    public void setIdioma(String idioma) {
        this.idioma = idioma;
    }

    public void selecionaIdioma() throws Exception {
        LocaleEnum localeEnum = LocaleEnum.obterLocale(getUsuario().getLinguagem());
        FacesContext.getCurrentInstance().getViewRoot().setLocale(new Locale(localeEnum.getLocale()));
        idioma = localeEnum.getLocale();
    }

    public void recarregarPagina() throws IOException {
      final FacesContext context = FacesContext.getCurrentInstance();
        String refreshpage = context.getViewRoot().getViewId();
        ViewHandler handler = context.getApplication().getViewHandler();
        UIViewRoot root = handler.createView(context, refreshpage);
        root.setViewId(refreshpage);
        context.setViewRoot(root);
    }

    public void setarModuloAberto(ModuloAberto moduloAberto) {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null) {
                loginControle.setModuloAberto(moduloAberto);
            }
        } catch (Exception e) {
            Uteis.logar(e, SuperControle.class);
        }
    }

    public String getIdConhecimentoBusca(){
        try {
            return context().getViewRoot().getViewId().substring(1);
        }catch (Exception e){
            return "";
        }
    }

    public String getZona(){
        try {
            if(zona == null){
                String roboControle = PropsService.getPropertyValue(getKey(), PropsService.robocontrole);
                Pattern pattern = Pattern.compile("zw(\\d+)\\.");
                Matcher matcher = pattern.matcher(roboControle);
                if (matcher.find()) {
                    zona = matcher.group(1);
                } else {
                    zona = "-1";
                }
            }
        }catch (Exception e){
            zona = "-1";
        }
        return zona;
    }

    public String getPontoInterrogacaoMs(){
        return PropsService.getPropertyValue(PropsService.pontoInterrogacaoMs);
    }

    public Boolean getPontoInterrogacaoHabilitado(){
        return true;
    }

    public Boolean getNotificacoesVersoesHabilitado(){
        return true;
    }

    public String getUrlBaseConhecimento(){
        return PropsService.getPropertyValue(PropsService.urlBaseConhecimento);
    }

    protected LogoutControle getLogoutControle(){
        return (LogoutControle) obtenhaObjetoSessionMap(LogoutControle.class.getSimpleName());
    }

    public void validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum itemExportar, Integer tamanhoLista, String filtro, String tipo, String atributos, String prefixoArquivo)  throws  Exception {
        validarPermissaoExportacao(itemExportar);
        try {
            try {
                String msg = (UteisValidacao.emptyNumber(tamanhoLista) ? "" : "Tamanho Lista Exportada: " + tamanhoLista )+ (UteisValidacao.emptyString(filtro) ? " " : "\nFiltro: " + filtro) + "\nTipo: " + (UteisValidacao.emptyString(tipo) ? "não informado" : tipo) + (UteisValidacao.emptyString(atributos) ? " " : "\nAtributos: " + atributos);
                registrarLogObjetoVO((itemExportar == null ? prefixoArquivo : itemExportar.getDescricaoLog()), msg, getUsuarioLogado().getCodigo(), getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd(), "EXPORTAÇÃO");
            } catch (Exception e ){
                registrarLogErroObjetoVO((itemExportar == null ? prefixoArquivo : itemExportar.getDescricaoLog()), getUsuarioLogado().getCodigo(), "ERRO AO GERAR LOG DO EXPORTAÇÃO", getUsuarioLogado().getNome(), getUsuarioLogado().getUserOamd());
            }
        } catch (Exception e){
            Uteis.logarDebug("Erro ao gerar log Exportacao:" + e.getMessage());
        }
    }

    public void validarPermissaoExportacao(ItemExportacaoEnum  item) throws Exception {
        if(item==null){
            return;
        }
        if(!permissao(item.getPermissao())){
            throw new Exception("Usuário não tem a permissão "+ (UteisValidacao.emptyString(item.getDescricaoPermissao()) ? item.getPermissao() : item.getDescricaoPermissao()));
        }
    }

    public boolean isMaxGpt() {
        return maxGpt;
    }

    public void setMaxGpt(boolean maxGpt) {
        this.maxGpt = maxGpt;
    }

    public String getUrlImagemRetrospectiva() {
        try {
            if(!UteisValidacao.emptyObject(JSFUtilities.getRequest().getRequestURL()) && JSFUtilities.getRequest().getRequestURL().toString().contains("/finan/")){
                return "../../images/RETROSPECTIVA-2024.png";
            }
            return "images/RETROSPECTIVA-2024.png";
        }catch (Exception e) {
            return "images/RETROSPECTIVA-2024.png";
        }
    }

    public String getUrlImagemBannerRespiracao() {
        try {
            if(!UteisValidacao.emptyObject(JSFUtilities.getRequest().getRequestURL()) && JSFUtilities.getRequest().getRequestURL().toString().contains("/finan/")){
                return "../../images/banner-respiracao.png";
            }
            return "images/banner-respiracao.png";
        }catch (Exception e) {
            return "images/banner-respiracao.png";
        }
    }
}
