package controle.arquitetura.exceptions;

public class TokenInvalidoException extends ServiceException {

    private static final String CHAVE_EXCECAO = "token_invalido";
    private static final String MENSAGEM_EXCECAO = "O token informado não está válido";

    public TokenInvalidoException() {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO);
    }

    public TokenInvalidoException(Throwable causa) {
        super(CHAVE_EXCECAO, MENSAGEM_EXCECAO, causa);
    }

}
