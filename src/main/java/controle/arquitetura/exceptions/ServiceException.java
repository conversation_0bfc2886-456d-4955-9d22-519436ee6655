package controle.arquitetura.exceptions;


@SuppressWarnings("serial")
public class ServiceException extends Exception {

    private String chaveExcecao;
    private Integer codigoError = 0;

    public ServiceException(String mensagem, int... codigoErros) {
        super(mensagem);
        if (codigoErros.length > 0) {
            this.codigoError = codigoErros[0];
        } else {
            this.codigoError = 0;
        }
    }

    /**
     * @param chaveExcecao Chave da exceção mapeada pela implementação de {@link }
     * @param mensagem     Mensagem detalhada da exceção lançada
     */
    public ServiceException(String chaveExcecao, String mensagem) {
        this(mensagem);
        this.chaveExcecao = chaveExcecao;
    }

    public ServiceException(Throwable causa, int... codigoErros) {
        super(causa.getMessage());
        if (codigoErros.length > 0) {
            this.codigoError = codigoErros[0];
        } else {
            this.codigoError = 0;
        }
    }

    public ServiceException(String mensagem, Throwable causa) {
        super(mensagem, causa);
    }

    /**
     * @param chaveExcecao Chave da exceção mapeada pela implementação de {@link }
     * @param mensagem     Mensagem detalhada da exceção lançada
     * @param causa        A causa, o objeto lançado
     */
    public ServiceException(String chaveExcecao, String mensagem, Throwable causa) {
        this(mensagem, causa);
        this.chaveExcecao = chaveExcecao;
    }

    public String getChaveExcecao() {
        return chaveExcecao;
    }

    public Integer getCodigoError() {
        return codigoError;
    }

}
