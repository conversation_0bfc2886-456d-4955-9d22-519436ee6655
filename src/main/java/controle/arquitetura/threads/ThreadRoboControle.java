package controle.arquitetura.threads;

import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.Calendario;

public class ThreadRoboControle extends SuperControle {

    private ThreadRobo threadRobo;

    public ThreadRoboControle() throws Exception {
        threadRobo = new ThreadRobo();
        threadRobo.setName("Robo_" + super.getNomeContextoReal() + Calendario.hoje().getTime());
    }

    synchronized public void iniciarRobo() {
        try {
            threadRobo.setRealizar(true);            
            if (!threadRobo.isAlive()) {
                threadRobo.start();
            }

        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    synchronized public void finalizarRobo() {
        try {
            threadRobo.setRealizar(false);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    public ThreadRobo getThreadRobo() {
        return threadRobo;
    }

    public void setThreadRobo(ThreadRobo threadRobo) {
        this.threadRobo = threadRobo;
    }    
}
