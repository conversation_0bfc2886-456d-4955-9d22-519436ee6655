package controle.arquitetura.filter;

import br.com.pactosolucoes.oamd.controle.basico.BetaTestersService;
import controle.arquitetura.exceptions.SecretException;
import controle.arquitetura.servico.impl.LeituraTokenServiceImpl;
import controle.arquitetura.servico.impl.PersonaDTO;
import controle.arquitetura.servico.interfaces.LeituraTokenServico;
import negocio.comuns.arquitetura.UsuarioVO;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class RequisicaoFilter implements Filter {

    private static final String HEADER_AUTENTICACAO = "Authorization";
    private LeituraTokenServico tokenService;
    private String permissao = "ZW";

    public RequisicaoFilter() {
        tokenService = new LeituraTokenServiceImpl();
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        String chave = extractKeyRequest((HttpServletRequest) request);

        if (!BetaTestersService.isBetaTester(chave)
                 || ((HttpServletRequest) request).getRequestURI().contains("auth")
                 || ((HttpServletRequest) request).getRequestURI().contains("health")
                || ((HttpServletRequest) request).getRequestURI().contains("pix")
                || ((HttpServletRequest) request).getRequestURI().contains("testers")) {
            filterChain.doFilter(request, response);
            return;
        }
        PersonaDTO persona;
        String token = ((HttpServletRequest) request).getHeader(HEADER_AUTENTICACAO);
        UsuarioVO usuarioLogado = (UsuarioVO) ((HttpServletRequest) request).getSession().getAttribute("logado");
        boolean logado = usuarioLogado != null && usuarioLogado.getCodigo() > 0 ? true : false;
        try {

            if(logado){
                filterChain.doFilter(request, response);
                return;
            }

            if(token == null){
                ((HttpServletResponse) response).sendError(
                        401,
                        "Token de autenticacao nao encontrado");
                return;
            }

            persona = tokenService.validarRecuperandoPersona(token.contains(" ") ? token.split(" ")[1] : token);
            if(!persona.getPermissoes().contains(permissao)){
                ((HttpServletResponse) response).sendError(
                        401,
                        "Suas permissoes nao te dao acesso ao ZW");
                return;
            }
        } catch (SecretException s) {
            ((HttpServletResponse) response).sendError(
                    401,
                    s.getMessage());
            return;
        } catch (Exception e) {
            ((HttpServletResponse) response).sendError(
                    401,
                    e.getMessage());
            return;
        }

        if (persona == null) {
            ((HttpServletResponse) response).sendError(
                    401,
                    "Não foi possível verificar suas credenciais");
            return;
        }

        filterChain.doFilter(request, response);
    }

    private String extractKeyRequest(HttpServletRequest request){
        String chave;
        try {
            chave = obterParametroString(request, "key");
            if(chave == null){
                chave = obterParametroString(request, "chave");
            }
        }catch (Exception e){
            chave = null;
        }
        return chave;
    }

    private String obterParametroString(HttpServletRequest request, String chave) {
        String parameter = request.getParameter(chave);
        if (parameter != null) {
            return parameter.toString();
        }
        return null;
    }

    @Override
    public void destroy() {

    }
}
