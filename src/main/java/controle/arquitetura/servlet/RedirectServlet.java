/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package controle.arquitetura.servlet;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import org.json.JSONObject;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Criptografia;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;
/**
 *
 * <AUTHOR>
 */
public class RedirectServlet extends HttpServlet {

    private static final String PARAMETRO_CHAVE = "key";
    private static final String PARAMETRO_CODIGO_EMPRESA = "codigoEmpresa";
    private static final String PARAMETRO_NOME_FANTASIA = "nomeFantasia";
    private static final String PARAMETRO_RAZAO_SOCIAL = "razaoSocial";
    private static final String PARAMETRO_CPF_CNPJ = "cpfCnpj";
    private static final String PARAMETRO_ESTADO = "estado";
    private static final String PARAMETRO_CIDADE = "cidade";
    private static final String PARAMETRO_CODIGO_FINANCEIRO = "codigoFinanceiro";

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        try {
            if(request.getParameter("suporte") != null){
                response.sendRedirect(PropsService.getPropertyValue(PropsService.urlSuporte));
            }
            if (request.getParameter("up") != null) {
                UsuarioVO user = (UsuarioVO) request.getSession().getAttribute(JSFUtilities.LOGGED);
                if (user != null) {
                    LoginControle loginControle = (LoginControle) request.getSession().getAttribute("LoginControle");
                    try {
                        final EmpresaVO empresa = loginControle.getEmpresa();
                        if (empresa != null && empresa.getCodigo() > 0) {
                            final String key = (String) request.getSession().getAttribute(JSFUtilities.KEY);
                            final String nomeEmpresa;
                            nomeEmpresa = empresa.getNome();
                            JSONObject json = new JSONObject();
                            json.put(PARAMETRO_CHAVE, key)
                                    .put("nomeEmpresa", nomeEmpresa)
                                    .put("userName", user.getUsername())
                                    .put("codigoUsuarioZW", user.getCodigo())
                                    .put("nomeCompleto", user.getNome())
                                    .put("telefones", user.getColaboradorVO().getPessoa().getTelefones())
                                    .put("origemSistema", 0) //ENUM DA UCP PARA IDENTIFICAR QUE SUA ORIGEM É ZILLYONWEB
                                    .put("codigoUsuario", user.getCodigo()) //PARA UCP
                                    .put("timevld", Calendario.getInstance(Calendario.hoje().getTime() + ((60 * 30) * 1000)).getTimeInMillis());

                            if (!UteisValidacao.emptyString(user.getColaboradorVO().getPessoa().getFotoKey())) {
                                json.put("urlFoto", String.format("%s/%s",
                                        PropsService.getPropertyValue(PropsService.urlFotosNuvem),
                                        user.getColaboradorVO().getPessoa().getFotoKey()));
                            }

                            String paginaAtualZW = request.getParameter("paginaAtualZW");
                            if (paginaAtualZW != null && !paginaAtualZW.isEmpty()) {
                                json.put("paginaAtualZW", paginaAtualZW);
                            } else {
                                json.put("paginaAtualZW", "");
                            }

                            String codPerguntaUCP = request.getParameter("codPerguntaUCP");
                            if (codPerguntaUCP != null && !codPerguntaUCP.isEmpty()) {
                                json.put("codPerguntaUCP", codPerguntaUCP);
                            } else {
                                json.put("codPerguntaUCP", "");
                            }

                            String visualizacaoUCP = request.getParameter("visualizacaoUCP");
                            if (visualizacaoUCP != null && !visualizacaoUCP.isEmpty()) {
                                json.put("visualizacaoUCP", visualizacaoUCP);
                            } else {
                                json.put("visualizacaoUCP", "");
                            }

                            String solicitacao = request.getParameter("solicitacao");
                            if (solicitacao != null && !solicitacao.isEmpty()) {
                                json.put("solicitacao", solicitacao);
                            } else {
                                json.put("solicitacao", "false");
                            }

                            String ranking = request.getParameter("ranking");
                            if (ranking != null && !ranking.isEmpty()) {
                                json.put("ranking", ranking);
                            } else {
                                json.put("ranking", "false");
                            }

                            String duvida = request.getParameter("duvida");
                            if (duvida != null && !duvida.isEmpty()) {
                                try {
                                    duvida = Criptografia.decrypt(duvida, SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
                                } catch (Exception ex) {

                                }
                                json.put("duvida", duvida);
                            } else {
                                json.put("duvida", "");
                            }

                            json.put("SolicitacaoSuporte", "movidesk")
                                    .put(PARAMETRO_CODIGO_EMPRESA, empresa.getCodigo())
                                    .put(PARAMETRO_NOME_FANTASIA, empresa.getNome())
                                    .put(PARAMETRO_RAZAO_SOCIAL, empresa.getRazaoSocial())
                                    .put(PARAMETRO_CPF_CNPJ, empresa.getCNPJ())
                                    .put(PARAMETRO_CIDADE, empresa.getCidadeNome())
                                    .put(PARAMETRO_ESTADO, empresa.getEstadoSigla())
                                    .put(PARAMETRO_CODIGO_FINANCEIRO, empresa.getCodEmpresaFinanceiro())
                            ;

                            prepararInformacoesService(user, loginControle, json, request);

                            final String lgn = Uteis.encriptar(json.toString(), "chave_login_unificado");
                            response.sendRedirect(String.format("%s/oid?lgn=%s",
                                    PropsService.getPropertyValue(PropsService.myUpUrlBase),
                                    lgn));
                        } else {
                            out.println("<!DOCTYPE html>");
                            out.println("<html>");
                            out.println("<head>");
                            out.println("<title>Universidade Pacto</title>");
                            out.println("</head>");
                            out.println("<body>");
                            out.println("<h2>Seu usuário não pôde logar na UCP (Universidade Corporativa Pacto) porque não está alocado em nenhuma empresa do ZW</h2>");
                            out.println("</body>");
                            out.println("</html>");
                        }
                    } catch (Exception ex) {
                        Logger.getLogger(RedirectServlet.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
            }
        } finally {
            out.close();
        }
    }

    private void prepararInformacoesService(UsuarioVO user, LoginControle loginControle, JSONObject jsonObject, HttpServletRequest request) throws Exception {
        String email = user.getColaboradorVO().getPessoa().getEmail();
        String usuarioService = user.getServiceUsuario();
        String senhaService = user.getServiceSenha();
        String telefoneEmpresa = loginControle.getEmpresa().getTelComercial1();
        Boolean utilizarMoviDesk = utilizarMoviDesk(request);
        boolean serviceEmpresa = false;

        if (UteisValidacao.emptyString(usuarioService)) {
            serviceEmpresa = true;
            usuarioService = loginControle.getEmpresa().getServiceUsuario();
            senhaService = loginControle.getEmpresa().getServiceSenha();
            email = loginControle.getEmpresa().getEmail();
        }

        if (!UteisValidacao.emptyString(senhaService)) {
            senhaService = Criptografia.decrypt(senhaService, SuperControle.Crypt_KEY, SuperControle.Crypt_ALGORITM);
        }

        if (UteisValidacao.emptyString(email)) {
            email = loginControle.getEmpresa().getEmail();
        }

        jsonObject.put("email", email);
        jsonObject.put("telefoneEmpresa", telefoneEmpresa);
        jsonObject.put("usuarioService", usuarioService);
        jsonObject.put("senhaService", senhaService);
        jsonObject.put("serviceEmpresa", serviceEmpresa);
        jsonObject.put("SolicitacaoSuporte", utilizarMoviDesk ? "movidesk" : "bee");

    }

    private Boolean utilizarMoviDesk(final HttpServletRequest request) {
        final Object utilizarMoviDesk = request.getSession().getAttribute(JSFUtilities.UTILIZA_MOVIDESK);
        return utilizarMoviDesk != null && (Boolean) utilizarMoviDesk;
    }

// <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
