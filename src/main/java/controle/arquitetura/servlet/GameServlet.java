package controle.arquitetura.servlet;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.security.LoginControle;
import controle.crm.MalaDiretaControle;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONException;
import servicos.propriedades.PropsService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

public class GameServlet extends SuperServlet {
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        super.processRequest(request, response);
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        LoginControle loginControl = null;
        Connection c = null;
        try {
            String mailing = request.getParameter("mailing");
            if(!UteisValidacao.emptyString(mailing)){
                MalaDiretaControle control = (MalaDiretaControle) request.getSession().getAttribute(MalaDiretaControle.class.getSimpleName());
                if (control == null) {
                    control = new MalaDiretaControle();
                }
                control.novo();
                JSFUtilities.storeOnSession(MalaDiretaControle.class.getSimpleName(), control);
                response.sendRedirect(request.getContextPath() + "/faces/mailing.jsp");
            }
        } catch (Exception ex) {
            Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
        } finally {
            if (c != null) {
                try {
                    c.close();
                } catch (SQLException ex) {
                    Logger.getLogger(OIDServlet.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
            out.close();
        }
    }
}
