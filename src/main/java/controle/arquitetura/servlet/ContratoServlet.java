package controle.arquitetura.servlet;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import br.com.pactosolucoes.ce.comuns.enumerador.EnumTipoAmbiente;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoCaucaoCredito;
import br.com.pactosolucoes.ce.comuns.to.NegEvCaucaoCreditoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoAmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoProdutoLocacaoTO;
import br.com.pactosolucoes.ce.comuns.to.NegEvPerfilEventoServicoTO;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoTO;
import br.com.pactosolucoes.ce.comuns.to.PerfilEventoAmbienteLayoutTO;
import br.com.pactosolucoes.ce.comuns.to.TagsTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CentralEventosFacade;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.perfil.PerfilEventoAmbienteLayout;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
/**
 * Servlet simples para exibição de imagens.
 * 
 * Caminho do diretório é 'hard coded' nesta classe.
 * 
 * O nome do arquivo é passado como parâmetro.
 * 
 * Responde requisições do tipo get
 * 
 * <AUTHOR>
 * <AUTHOR>
 * 
 */
public class ContratoServlet extends HttpServlet {
	/**
	 * Serial gerado automaticamente
	 */
	private static final long serialVersionUID = 458704839020430050L;

	private static final String ENCODING = "ISO-8859-1";

	private PrintWriter out;
	private NegociacaoEventoTO evento;
	private CentralEventosFacade centralEventosFacade;
	private String contexto;
	private String documentoModelo;
	private TagsTO tagsTo;

	private HttpSession session;
	// 1 -> Contrato
	// 2 -> Orçamento
	private Integer tipoDocumento;

	/**
	 * Imprime o contrato requisitado
	 * 
	 * @param request
	 *            <code>HttpServletRequest</code> - Solicitação do cliente
	 * @param response
	 *            <code>HttpServletResponse</code> - Resposta do servidor
	 * @exception e
	 */
	@Override
	public void doGet(final HttpServletRequest request, final HttpServletResponse response) throws ServletException, IOException {
		this.session = request.getSession();
		// Determina o contetType HTML
		response.setContentType("text/html");
		// Determinar a codificação do documento iso 8859-1
		response.setCharacterEncoding(ContratoServlet.ENCODING);
		// obter o stream de saida
		this.out = response.getWriter();
		// Determinar a partir do parametro "q" qual o tipo de documento a ser impresso (q=1 -> contrato ], q=2 -> orçamento)
		this.tipoDocumento = Integer.valueOf(request.getParameter("q"));
		// retorna o caminho do cotexto
		this.contexto = request.getContextPath();

		this.evento = (NegociacaoEventoTO) request.getSession().getAttribute("negociacao");
		this.documentoModelo = (String)request.getSession().getAttribute("documentoModelo");
		if (this.documentoModelo == null) {
			this.escrever();
		} else {
			//mostrar modelo 
			escreverModelo(this.documentoModelo);
		}
	}

	private void escrever() {
		StringBuilder texto = this.obterModelo();

		montarTexto(texto);
	}
	/**
	 * Método que imprime na tela o modelo pré-configurado com todas as tags 
	 * @param modelo
	 */
	private void escreverModelo(String modelo) {
		StringBuilder texto = new StringBuilder(modelo);

		montarTexto(texto);
	}

	/**
	 * @param texto
	 */
	private void montarTexto(StringBuilder texto) {
		this.replaceTags(texto);
		String body = "<body";

		int posBody = texto.indexOf(body);
		int posFinalBody = texto.indexOf(">", posBody);

		texto.insert(posFinalBody + 1, "<input type='button' onclick='window.print();' value='Imprimir'/>");

		if (this.tipoDocumento.equals(1)) {
			this.replaceImg(texto, this.evento.getModeloContrato().getCodigo());
		} else {
			this.replaceImg(texto, this.evento.getModeloOrcamento().getCodigo());
		}

		this.out.println(texto);
	}

	private StringBuilder obterModelo() {
		StringBuilder modelo;
		try {
			File arquivo;
			if (this.tipoDocumento.equals(1)) {
				arquivo = this.evento.getModeloContrato().getArquivo();
			} else {
				arquivo = this.evento.getModeloOrcamento().getArquivo();
			}
			byte[] bytesArquivo = FileUtilities.obterBytesArquivo(arquivo);
			modelo = new StringBuilder(new String(bytesArquivo, ContratoServlet.ENCODING));
		} catch (Exception e) {
			modelo = null;
			e.printStackTrace();
		}
		return modelo;
	}

	private void replaceTags(StringBuilder texto) {
		try {
			Integer contadorListas;
			String tags[] = { "#nomeevento", "#ambiente", "#layout", "#servicoslistavertical", "#bensdeconsumolistavertical", "#utensilioslistavertical", "#brinquedoslistavertical",
					"#condicaopagamento", "#data", "#horario", "#texto", "#observacao", "#total", "#descontos", "#valorfinal",
					"#valorparcela", "#nomecliente", "#rg", "#cpf", "#endereco", "#bairro", "#cidade", "#telefones", "#tempoduracaoevento",
				"#qtdconvidados", "#brinquedoslistahorizontal", "#parcelas", "#vencimentoparcelas", "#formapagamento", "#cheque", "#compensacaodata", 
				"#eventohorainicial", "#eventohorafinal", "#servicoslistahorizontal", "#bensdeconsumolistahorizontal", "#utensilioslistahorizontal",
				"#quadra_ambiente","#quadra_layout","#quadra_horario", "#quadra_tempoduracaoevento","#quadra_eventohorainicial",
				"#quadra_eventohorafinal", "#salao_ambiente","#salao_layout","#salao_horario", "#salao_tempoduracaoevento","#salao_eventohorainicial",
				"#salao_eventohorafinal", "#valor_ambientes", "#nr_segurancas", "#nr_auxiliarLimpeza", "#valor_individual_seguranca", "#valor_total_servicos",
				"#valor_credito","#valor_caucao", "#hoje", "#valor_individual_limpeza", "#valor_individual_estacionamento", "#qtd_estacionamento"};

			this.centralEventosFacade = FacadeManager.getFacade().getCentralEventosFacade(this.session);
			TagsTO consultarTags = this.centralEventosFacade.consultarTags(this.session, evento.getCodigoEventoInteresse());
			this.setTagsTo(consultarTags);
			Ordenacao.ordenarLista(getTagsTo().getParcelas(), "dataVencimento");	
			EnderecoVO endereco = null;
			for (Object obj : this.getTagsTo().getEnderecos()) {
				if (obj instanceof EnderecoVO) {
					EnderecoVO enderecoPessoa = (EnderecoVO) obj;
					if (enderecoPessoa.getEnderecoCorrespondencia()) {
						endereco = enderecoPessoa;
					}
				}
			}
			if ((endereco == null) && !this.getTagsTo().getEnderecos().isEmpty()) {
				endereco = (EnderecoVO) this.getTagsTo().getEnderecos().get(0);
			}

			for (int i = 0; i < tags.length; i++) {
				String tag = tags[i];

				if (texto.indexOf(tag) > 0) {

					switch (i) {
					// #nomeevento
					case 0:
						try {
							StringUtilities.substituirTodos(texto, tag, this.getTagsTo()
									.getNomeEvento());
						} catch (Exception e) {

							String textoNovo = texto.toString().replaceAll(tag,
									tag + ", não substituiu : " + e.getMessage());
							 
						}
						break;

					// #ambiente
					case 1:
						
						try{
							StringBuilder descAmbientes = new StringBuilder();
							contadorListas = new Integer(1);
							//percorrer lista de ambientes
							for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
								//adicionar a descricao do ambiente
								descAmbientes.append(ambiente.getDescricaoAmbiente());
								//caso o contador não esteja com valor igual ao tamanho da lista de ambientes
								//adicionar vírgula
								if(contadorListas != this.evento.getAmbientes().size()){
									descAmbientes.append(", ");
								}
								contadorListas++;
							}
								
							StringUtilities.substituirTodos(texto, tag, descAmbientes.toString());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
							 
						}
						
						
						break;

					// #layout
					case 2:
						try{
							StringBuilder descLayouts = new StringBuilder();
							contadorListas = new Integer(1);
							//percorrer lista de ambientes
							PerfilEventoAmbienteLayout  peal = new PerfilEventoAmbienteLayout(this.session);
							List<PerfilEventoAmbienteLayoutTO> layoutsNegociacao = peal.obterLayoutsPorNegociacao(this.evento.getCodigo());
							
							for (PerfilEventoAmbienteLayoutTO layout : layoutsNegociacao) {
								//verificar existencia de layout
								if(layout.getDescricao() != null){
									//se o contador indicar que é o primeiro item da lista, não colocar vírgula
									if(contadorListas != 1){
										descLayouts.append(", ");
									}
									//concatenar a descricao do ambiente mais : e a descricao do layout
									descLayouts.append(layout.getNomeAmbiente()+": "+ layout.getNomeArquivo());
									contadorListas++;
								}
							}
							if(contadorListas == 1){
								descLayouts.append("(NÃO HÁ LAYOUTS)");
							}
							StringUtilities.substituirTodos(texto, tag, descLayouts.toString());
							
							
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #servicoslistavertical
					case 3:
						
						try{

							StringBuilder descServicos = new StringBuilder();
							if (this.evento.getServicos().isEmpty()) {
								descServicos.append("(NÃO HÁ SERVIÇOS)");
							} else {
								descServicos.append("<ul><table style=\"{border-style: none;\">");
								for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()) {
									descServicos.append("<tr><td style=\"{border-style: none;\"><li>" + servico.getDescricaoServico() + "</li></td><td style=\"{border-style: none;\">"
											+ servico.getValorMonetario() + "</td></tr>");
								}
								descServicos.append("</table></ul>");
							}
							StringUtilities.substituirTodos(texto, tag, descServicos.toString());	
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #bensdeconsumolistavertical
					case 4:
						
						try{
							StringBuilder descBensConsumo = new StringBuilder();
							if (this.evento.getBensConsumo().isEmpty()) {
								descBensConsumo.append("(NÃO HÁ BENS DE CONSUMO)");
							} else {
								descBensConsumo.append("<ul><table style=\"{border-style: none;\" >");
								for (NegEvPerfilEventoProdutoLocacaoTO bemConsumo : this.evento.getBensConsumo()) {
									descBensConsumo.append("<tr><td style=\"{border-style: none;\"><li>" + bemConsumo.getDescricaoProdutoLocacao() + "</li></td><td style=\"{border-style: none;\">"
											+ bemConsumo.getValorUnitarioMonetario() + "</td></tr>");
								}
								descBensConsumo.append("</table></ul>");
							}
							StringUtilities.substituirTodos(texto, tag, descBensConsumo.toString());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						break;

					// #utensilioslistavertical
					case 5:
						
						try{
							StringBuilder descUtensilios = new StringBuilder();
							if (this.evento.getUtensilios().isEmpty()) {
								descUtensilios.append("(NÃO HÁ UTENSÍLIOS)");
							} else {
								descUtensilios.append("<ul><table style=\"{border-style: none;\">");
								for (NegEvPerfilEventoProdutoLocacaoTO utensilio : this.evento.getUtensilios()) {
									descUtensilios.append("<tr><td style=\"{border-style: none;\"><li>" + utensilio.getDescricaoProdutoLocacao() + "</li></td><td style=\"{border-style: none;\">"
											+ utensilio.getValorUnitarioMonetario() + "</td></tr>");
								}
								descUtensilios.append("</table></ul>");
							}
							StringUtilities.substituirTodos(texto, tag, descUtensilios.toString());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						break;
						
						

					// #brinquedoslistavertical
					case 6:
						
						try{
							StringBuilder descBrinquedos = new StringBuilder();
							if (this.evento.getBrinquedos().isEmpty()) {
								descBrinquedos.append("(NÃO HÁ BRINQUEDOS)");
							} else {
								descBrinquedos.append("<ul><table style=\"{border-style: none;\">");
								for (NegEvPerfilEventoProdutoLocacaoTO brinquedo : this.evento.getBrinquedos()) {
									descBrinquedos.append("<tr><td style=\"{border-style: none;\"><li>" + brinquedo.getDescricaoProdutoLocacao() + "</li></td><td style=\"{border-style: none;\">"
											+ brinquedo.getValorUnitarioMonetario() + "</td></tr>");
								}
								descBrinquedos.append("</table></ul>");
							}
							StringUtilities.substituirTodos(texto, tag, descBrinquedos.toString());
							
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						break;
						
						
						

					// #condicaopagamento
					case 7:
						
						try{
							StringUtilities.substituirTodos(texto, tag, this.evento.getCondicaoPagamento().getDescricaoCondicaoPagamento());
							
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						
						
						break;

					// #data
					case 8:
						try{
							StringUtilities.substituirTodos(texto, tag, Formatador.formatarDataPadrao(this.evento.getDataEvento()));
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
							 
						}
						
						break;

					// #horario
					case 9:
						try{
							StringUtilities.substituirTodos(texto, tag, Formatador.formatarDataPadrao(this.evento.getDataEvento()));
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #texto
					case 10:
						try{
							StringUtilities.substituirTodos(texto, tag, this.evento.getTextoPredefinido());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #observacao
					case 11:
						try{
							StringUtilities.substituirTodos(texto, tag, this.evento.getTextoLivre());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #total
					case 12:
						try{
							StringUtilities.substituirTodos(texto, tag, this.evento.getValorTotalMonetario());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #descontos
					case 13:
						try{
							StringUtilities.substituirTodos(texto, tag, this.evento.getDescontoMonetario());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #valorfinal
					case 14:
						try{
							StringUtilities.substituirTodos(texto, tag, this.evento.getValorFinalMonetario());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #valorparcela
					case 15:
						try{
							StringBuilder tabelaValores = new StringBuilder();
							if(this.getTagsTo().getParcelas() == null || this.getTagsTo().getParcelas().isEmpty()){
								tabelaValores.append("(NENHUMA PARCELA RELACIONADA)");
							}else{
								tabelaValores.append("<table cellspacing=5><tr>");
								tabelaValores.append("<th>&nbsp; Parcela &nbsp;</th>");
								tabelaValores.append("<th>&nbsp; Valor &nbsp; </th>");
							for(MovParcelaVO parcela : this.getTagsTo().getParcelas()){
								tabelaValores.append("<tr><td align=\"center\">"+parcela.getDescricao()+"</td>");
								tabelaValores.append("<td align=\"center\">"+parcela.getValorParcelaNumerico()+"</td></tr>");
							}
							tabelaValores.append("</table>");
							}
							StringUtilities.substituirTodos(texto, tag, tabelaValores.toString());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						
						break;
						
					// #nomecliente
					case 16:
						try{
							StringUtilities.substituirTodos(texto, tag, this.getTagsTo().getNomeCliente());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						
						break;

					// #rg
					case 17:
						
						try{
							StringUtilities.substituirTodos(texto, tag, this.getTagsTo().getRg());
						}catch (Exception e) {
							
							StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						}
						
						break;

					// #cpf
					case 18:
					
					try{
						StringUtilities.substituirTodos(texto, tag, this.getTagsTo().getCpf());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;

					// #endereco
					case 19:
						
					try{
						StringUtilities.substituirTodos(texto, tag, endereco == null ? "(NENHUM ENDEREÇO DISPONÍVEL)" : endereco
								.getEndereco());
					}catch (Exception e) {
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;

					// #bairro
					case 20:
					try{
						StringUtilities.substituirTodos(texto, tag, endereco == null ? "(NENHUM ENDEREÇO DISPONÍVEL)" : endereco
								.getBairro());
					}catch (Exception e) {
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;

					// #cidade
					case 21:
					try{
						StringUtilities.substituirTodos(texto, tag,
								(this.getTagsTo().getCidade() == null || this.getTagsTo().getCidade().equals("")) ? "(CIDADE NÃO DISPONÍVEL)" : 
										this.getTagsTo().getCidade());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;

					// #telefones
					case 22:
					
					try{
						StringBuilder telefones = new StringBuilder();
						if (this.getTagsTo().getTelefones().isEmpty()) {
							telefones.append("(NENHUM TELEFONE DISPONÍVEL)");
						} else {
							for (int x = 0; x <this.getTagsTo().getTelefones().size(); x++) {
								if (x == this.getTagsTo().getTelefones().size() - 1) {
									telefones.append(this.getTagsTo().getTelefones().get(x).getNumero());
								} else {
									telefones.append(this.getTagsTo().getTelefones().get(x).getNumero() + " / ");
								}
							}
						}
						StringUtilities.substituirTodos(texto, tag, telefones.toString());
					}catch (Exception e) {
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;

					// #tempoduracaoevento
					case 23:
					try{
						int mins;
						if(this.evento.getHorarioFinalExibicao().before(this.evento.getHorarioInicial())){
							mins =  (int) (Uteis.minutosEntreDatas(this.evento.getHorarioInicial(),
											this.evento.getHorarioFinal()) + Uteis.minutosEntreDatas(
													Uteis.getDataComHoraZerada(this.evento.getHorarioInicial()), 
															this.evento.getHorarioFinalExibicao()));
							mins +=1;
							
						}else{
							mins  = (int) Uteis.minutosEntreDatas(this.evento.getHorarioInicial(), this.evento.getHorarioFinalExibicao());
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorNumerico(new Double(mins / 60)));
						break;
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						

					// #qtdconvidados
					case 24:
					
					try{
						Integer qtdConvidados = 0;
						if(!this.evento.getAmbientes().isEmpty()){
							qtdConvidados = this.evento.getAmbientes().get(0).getNrMaximoConvidado();
						}
						StringUtilities.substituirTodos(texto, tag, qtdConvidados.toString());
						break;
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						
					// #brinquedoslistahorizontal
					case 25:
					
					try{
						contadorListas = new Integer(1);
						StringBuilder descBrinquedoshori = new StringBuilder();
						if (this.evento.getBrinquedos().isEmpty()) {
							descBrinquedoshori.append("(NÃO HÁ BRINQUEDOS)");
						} else {
							for (NegEvPerfilEventoProdutoLocacaoTO brinquedo : this.evento.getBrinquedos()) {
								
								descBrinquedoshori.append(brinquedo.getDescricaoProdutoLocacao());
								if(contadorListas != this.evento.getBrinquedos().size()){
									descBrinquedoshori.append(", ");
								}
								contadorListas++;
							}
						}
						StringUtilities.substituirTodos(texto, tag, descBrinquedoshori.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;
					
					//#parcelas
					case 26:
					try{
						StringBuilder tabelaParcelas = new StringBuilder();
						if(this.getTagsTo().getParcelas() == null ||this.getTagsTo().getParcelas().isEmpty()){
							tabelaParcelas.append("(NENHUMA PARCELA RELACIONADA)");
						}else{
						tabelaParcelas.append("<table cellspacing=5><tr>");
						tabelaParcelas.append("<th>&nbsp; Parcela &nbsp;</th>");
						tabelaParcelas.append("<th>&nbsp; Data Lançamento &nbsp;</th><th>&nbsp; Vencimento &nbsp; </th>");
						tabelaParcelas.append("<th> &nbsp;Valor &nbsp;</th>");
						for(MovParcelaVO parcela : this.getTagsTo().getParcelas()){
							tabelaParcelas.append("<tr><td align=\"center\">"+parcela.getDescricao()+"</td>");
							tabelaParcelas.append("<td align=\"center\">"+parcela.getDataRegistro_Apresentar()+"</td>");
							tabelaParcelas.append("<td align=\"center\">"+parcela.getDataVencimento_Apresentar()+"</td>");
							tabelaParcelas.append("<td align=\"center\">"+parcela.getValorParcelaNumerico()+"</td></tr>");
						}
						tabelaParcelas.append("</table>");
						}
						StringUtilities.substituirTodos(texto, tag, tabelaParcelas.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
					break;
					//#vencimentoparcelas
					case 27:
						
						try{
						StringBuilder tabelaVencimentos = new StringBuilder();
						if(this.getTagsTo().getParcelas() == null || this.getTagsTo().getParcelas().isEmpty()){
							tabelaVencimentos.append("(NENHUMA PARCELA RELACIONADA)");
						}else{
							tabelaVencimentos.append("<table cellspacing=5><tr>");
							tabelaVencimentos.append("<th>&nbsp; Parcela &nbsp;</th>");
							tabelaVencimentos.append("<th>&nbsp; Vencimento &nbsp; </th>");
						for(MovParcelaVO parcela : this.getTagsTo().getParcelas()){
							tabelaVencimentos.append("<tr><td align=\"center\">"+parcela.getDescricao()+"</td>");
							tabelaVencimentos.append("<td align=\"center\">"+parcela.getDataVencimento_Apresentar()+"</td></tr>");
						}
						tabelaVencimentos.append("</table>");
						}
						StringUtilities.substituirTodos(texto, tag, tabelaVencimentos.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;
					//#formapagamento
					case 28:
					try{
						String formaPagamento;
						if(this.getTagsTo().getFormaPagamento()==null || this.getTagsTo().getFormaPagamento().equals(""))
							formaPagamento = "(PAGAMENTO AINDA NÃO REALIZADO)";
						else
							formaPagamento = this.getTagsTo().getFormaPagamento();
						StringUtilities.substituirTodos(texto, tag, formaPagamento);
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;
					//#cheque
					case 29:try{
						StringBuilder tabelaCheques = new StringBuilder();
						if(this.getTagsTo().getCheque()==null || this.getTagsTo().getCheque().isEmpty()){
							tabelaCheques.append("(PAGAMENTO AINDA NÃO REALIZADO OU FORMA DE PAGAMENTO DIFERENTE DE CHEQUE)");
						}else{
							tabelaCheques.append("<table cellspacing=5><tr>");
							tabelaCheques.append("<th>&nbsp; Banco &nbsp;</th>");
							tabelaCheques.append("<th>&nbsp; Agência &nbsp;</th><th>&nbsp; Conta &nbsp; </th>");
							tabelaCheques.append("<th> &nbsp;Número do Cheque &nbsp;</th>");
							for(ChequeVO cheque : this.getTagsTo().getCheque()){
								tabelaCheques.append("<tr><td align=\"center\">"+cheque.getBanco().getNome()+"</td>");
								tabelaCheques.append("<td align=\"center\">"+cheque.getAgencia()+"</td>");
								tabelaCheques.append("<td align=\"center\">"+cheque.getConta()+"</td>");
								tabelaCheques.append("<td align=\"center\">"+cheque.getNumero()+"</td></tr>");
							}
							tabelaCheques.append("</table>");
							
						}
						StringUtilities.substituirTodos(texto, tag, tabelaCheques.toString());
					}catch (Exception e) {
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;
					//#datacompensacao
					case 30:
					try{
						String compensacao;
						if(this.getTagsTo().getParcelas() == null || this.getTagsTo().getParcelas().isEmpty()){
							compensacao = "(NENHUMA PARCELA RELACIONADA)";
						}else
							compensacao = this.getTagsTo().getParcelas().get(0).getDataVencimento_Apresentar();
						StringUtilities.substituirTodos(texto, tag, compensacao);
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;
					//#eventohorainicial
					case 31:try{
						StringUtilities.substituirTodos(texto, tag, this.evento.getHorarioInicialString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
					}
						
						break;
					//#eventohorafinal	
					case 32:try{
						StringUtilities.substituirTodos(texto, tag, this.evento.getHorarioFinalExibicaoString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						// #servicoslistahorizontal
					case 33:try{
						contadorListas = new Integer(1);
						StringBuilder descServicoshori = new StringBuilder();
						if (this.evento.getServicos().isEmpty()) {
							descServicoshori.append("(NÃO HÁ SERVIÇOS)");
						} else {
							for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()) {
								descServicoshori.append(servico.getDescricaoServico());
								if(contadorListas != this.evento.getServicos().size()){
									descServicoshori.append(", ");
								}
								contadorListas++;
							}
							
						}
						StringUtilities.substituirTodos(texto, tag, descServicoshori.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;

					// #bensdeconsumolistahorizontal
					case 34:try{
						contadorListas = new Integer(1);
						StringBuilder descBensConsumohori = new StringBuilder();
						if (this.evento.getBensConsumo().isEmpty()) {
							descBensConsumohori.append("(NÃO HÁ BENS DE CONSUMO)");
						} else {
							for (NegEvPerfilEventoProdutoLocacaoTO bemConsumo : this.evento.getBensConsumo()) {
								descBensConsumohori.append(bemConsumo.getDescricaoProdutoLocacao());
								if(contadorListas != this.evento.getBensConsumo().size()){
									descBensConsumohori.append(", ");
								}
								contadorListas++;
							}
						}
						StringUtilities.substituirTodos(texto, tag, descBensConsumohori.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;

					// #utensilioslistahorizontal
					case 35:try{
						contadorListas = new Integer(1);
						StringBuilder descUtensilioshori = new StringBuilder();
						if (this.evento.getUtensilios().isEmpty()) {
							descUtensilioshori.append("(NÃO HÁ UTENSÍLIOS)");
						} else {
							for (NegEvPerfilEventoProdutoLocacaoTO utensilio : this.evento.getUtensilios()) {
								descUtensilioshori.append(utensilio.getDescricaoProdutoLocacao());
								if(contadorListas != this.evento.getUtensilios().size()){
									descUtensilioshori.append(", ");
								}
								contadorListas++;
							}
						}
						StringUtilities.substituirTodos(texto, tag, descUtensilioshori.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
					//#quadra_ambiente
					case 36 : try{
						StringBuilder descQdAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer lista de ambientes quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							if(ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())){
								//caso o contador indique que o ambiente é o primeiro da lista
								//adicionar vírgula
								if(contadorListas != 1){
									descQdAmbientes.append(", ");
								}
								//adicionar a descricao do ambiente quadra
								descQdAmbientes.append(ambiente.getDescricaoAmbiente());
								contadorListas++;	
							}
						}
						if(contadorListas == 1){
						descQdAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
						}
						StringUtilities.substituirTodos(texto, tag, descQdAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
							
						break;
						//#quadra_layout
					case 37 : try{
						StringBuilder descQdLayouts = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer lista de ambientes
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							if(ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())){
							//verificar existencia de layout
							if(ambiente.getTipoLayout().getDescricao() != null){
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									descQdLayouts.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a descricao do layout
								descQdLayouts.append(ambiente.getDescricaoAmbiente()+": "+ ambiente.getTipoLayout().getDescricao());
								contadorListas++;
							}
							}
						}
						if(contadorListas == 1){
							descQdLayouts.append("(NÃO HÁ LAYOUTS DE AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, descQdLayouts.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						
						break;
					//#quadra_horario
					case 38:try{
						StringBuilder horarioQdAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer lista de ambientes quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							if(ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())){
							//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									horarioQdAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e os horarios
								horarioQdAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ 
											ambiente.getHorarioInicialString()+"-"+ambiente.getHorarioInicialString());
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							horarioQdAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, horarioQdAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;	
//					#quadra_tempoduracaoevento
					case 39 : try{
						StringBuilder duracaoQdAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer ambientes do tipo quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							if (ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())) {
								//tempo em minutos
								int minsQuadra;
								if (ambiente.getHorarioFinalExibicao().before(ambiente.getHorarioInicial())) {
									minsQuadra = (int) (Uteis.minutosEntreDatas(ambiente.getHorarioInicial(), ambiente
											.getHorarioFinal()) + Uteis.minutosEntreDatas(Uteis.getDataComHoraZerada(ambiente
											.getHorarioInicial()), ambiente.getHorarioFinalExibicao()));
									minsQuadra += 1;

								} else {
									minsQuadra = (int) Uteis.minutosEntreDatas(ambiente.getHorarioInicial(), ambiente
											.getHorarioFinalExibicao());
								}
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									duracaoQdAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a duracao
								duracaoQdAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ minsQuadra);
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							duracaoQdAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, duracaoQdAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
//					#quadra_eventohorainicial
					case 40 : try{
						StringBuilder inicialQdAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer ambientes do tipo quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							if (ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())) {
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									inicialQdAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a hora inicial
								inicialQdAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ ambiente.getHorarioInicialString());
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							inicialQdAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, inicialQdAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
//					#quadra_eventohorafinal
					case 41 : try{
						StringBuilder finalQdAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer ambientes do tipo quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							if (ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())) {
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									finalQdAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a hora inicial
								finalQdAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ ambiente.getHorarioFinalString());
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							finalQdAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, finalQdAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						
					//#salao_ambiente
					case 42 :try{
						StringBuilder descSaAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer lista de ambientes quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							if(ambiente.getTipoambiente().equals(EnumTipoAmbiente.SALAO.getCodigo())){
								//caso o contador indique que o ambiente é o primeiro da lista
								//adicionar vírgula
								if(contadorListas != 1){
									descSaAmbientes.append(", ");
								}
								//adicionar a descricao do ambiente quadra
								descSaAmbientes.append(ambiente.getDescricaoAmbiente());
								contadorListas++;	
							}
						}
						if(contadorListas == 1){
							descSaAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO SALÃO RELACIONADOS)");
						}
						StringUtilities.substituirTodos(texto, tag, descSaAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					} 
							
						break;
						//#salao_layout
					case 43 : try{
						StringBuilder descSaLayouts = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer lista de ambientes
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							if(ambiente.getTipoambiente().equals(EnumTipoAmbiente.SALAO.getCodigo())){
							//verificar existencia de layout
							if(ambiente.getTipoLayout().getDescricao() != null){
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									descSaLayouts.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a descricao do layout
								descSaLayouts.append(ambiente.getDescricaoAmbiente()+": "+ ambiente.getTipoLayout().getDescricao());
								contadorListas++;
							}
							}
						}
						if(contadorListas == 1){
							descSaLayouts.append("(NÃO HÁ LAYOUTS DE AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, descSaLayouts.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
					//#salao_horario
					case 44:try{
						StringBuilder horarioSaAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer lista de ambientes quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							if(ambiente.getTipoambiente().equals(EnumTipoAmbiente.SALAO.getCodigo())){
							//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									horarioSaAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e os horarios
								horarioSaAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ 
											ambiente.getHorarioInicialString()+"-"+ambiente.getHorarioInicialString());
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							horarioSaAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, horarioSaAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;	
//					#salao_tempoduracaoevento
					case 45 : try{
						StringBuilder duracaoSaAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer ambientes do tipo quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							if (ambiente.getTipoambiente().equals(EnumTipoAmbiente.SALAO.getCodigo())) {
								//tempo em minutos
								int minsQuadra;
								if (ambiente.getHorarioFinalExibicao().before(ambiente.getHorarioInicial())) {
									minsQuadra = (int) (Uteis.minutosEntreDatas(ambiente.getHorarioInicial(), ambiente
											.getHorarioFinal()) + Uteis.minutosEntreDatas(Uteis.getDataComHoraZerada(ambiente
											.getHorarioInicial()), ambiente.getHorarioFinalExibicao()));
									minsQuadra += 1;

								} else {
									minsQuadra = (int) Uteis.minutosEntreDatas(ambiente.getHorarioInicial(), ambiente
											.getHorarioFinalExibicao());
								}
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									duracaoSaAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a duracao
								duracaoSaAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ minsQuadra);
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							duracaoSaAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, duracaoSaAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
//					#quadra_eventohorainicial
					case 46 : try{
						StringBuilder inicialSaAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer ambientes do tipo quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							if (ambiente.getTipoambiente().equals(EnumTipoAmbiente.SALAO.getCodigo())) {
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									inicialSaAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a hora inicial
								inicialSaAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ ambiente.getHorarioInicialString());
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							inicialSaAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, inicialSaAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
//					#salao_eventohorafinal
					case 47 : try{
						StringBuilder finalSaAmbientes = new StringBuilder();
						contadorListas = new Integer(1);
						//percorrer ambientes do tipo quadra
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							if (ambiente.getTipoambiente().equals(EnumTipoAmbiente.QUADRA.getCodigo())) {
								//se o contador indicar que é o primeiro item da lista, não colocar vírgula
								if(contadorListas != 1){
									finalSaAmbientes.append(", ");
								}
								//concatenar a descricao do ambiente mais : e a hora inicial
								finalSaAmbientes.append(ambiente.getDescricaoAmbiente()+": "+ ambiente.getHorarioFinalString());
								contadorListas++;
							}
						}
						if(contadorListas == 1){
							finalSaAmbientes.append("(NÃO HÁ AMBIENTES DO TIPO QUADRA RELACIONADOS)");
							}
						StringUtilities.substituirTodos(texto, tag, finalSaAmbientes.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						
//					valor_ambientes
					case 48:try{
						double valor = 0.0;
						//percorrer ambientes 
						for (NegEvPerfilEventoAmbienteTO ambiente : this.evento.getAmbientes()) {
							
							valor += ambiente.getValorDescontadoComSazonalidadeDouble();
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valor));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						
//					nr_segurancas
					case 49:try{
						Integer nrSegurancas = 0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
							if(servico.getDescricaoServico().equals("Segurança"))
								nrSegurancas += servico.getQuantidade();
						}
						StringUtilities.substituirTodos(texto, tag, nrSegurancas.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						
//					nr_auxiliarLimpeza
					case 50:try{
						Integer nrLimp = 0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
							if(servico.getDescricaoServico().equals("Auxiliar de Limpeza"))
								nrLimp += servico.getQuantidade();
						}
						StringUtilities.substituirTodos(texto, tag, nrLimp.toString());
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
					
//					valor_individual_seguranca
					case 51:try{
						double valorSegurancas = 0.0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
							if(servico.getDescricaoServico().equals("Segurança")){
								valorSegurancas = servico.getValorUnitario();
								break;
							}
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valorSegurancas));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						
//					valor_total_servicos
					case 52:
					try{
						double valorLimpezaSegurancas = 0.0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
								valorLimpezaSegurancas += servico.getValor();
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valorLimpezaSegurancas));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						
						break;
//					valor_credito
					case 53:
					
						try{
						double valorCredito = 0.0;
						for(NegEvCaucaoCreditoTO creditoCaucao : this.evento.getListaNegEvCaucaoECreditoTO()){
							if(creditoCaucao.getTipoCaucaoECredito().equals(TipoCaucaoCredito.CREDITO)){
								valorCredito += creditoCaucao.getValor();
							}
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valorCredito));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
						
//					valor_caucao
					case 54:
					
						try{
						double valorCaucao = 0.0;
						for(NegEvCaucaoCreditoTO creditoCaucao : this.evento.getListaNegEvCaucaoECreditoTO()){
							if(creditoCaucao.getTipoCaucaoECredito().equals(TipoCaucaoCredito.CHEQUE_CAUCAO)){
								valorCaucao += creditoCaucao.getValor();
							}
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valorCaucao));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
					break;
						
//					data_atual
					case 55:
						
					try{
						StringUtilities.substituirTodos(texto, tag, Uteis.getData(Calendario.hoje()));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;
//				   #valor_individual_limpeza"
					case 56:
						
					try{
						double valorLimpeza = 0.0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
							if(servico.getDescricaoServico().equals("Auxiliar de Limpeza")){
								valorLimpeza += servico.getValorUnitario();
							}
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valorLimpeza));
					}catch (Exception e) {
						
						StringUtilities.substituirTodos(texto, tag, "tag "+tag.replace("#", "")+" com problemas : "+e.getMessage());
						 
					}
						
						break;	
//				#estacionamento
					case 57:
						double valorEstacionamento = 0.0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
							if(servico.getDescricaoServico().toLowerCase().contains("estacionamento")){
								valorEstacionamento += servico.getValorUnitario();
							}
						}
						StringUtilities.substituirTodos(texto, tag, Formatador.formatarValorMonetarioSemMoeda(valorEstacionamento));

						break;
//						#qtd_estacionamento
					case 58:
						Integer qtdestacionamento = 0;
						for (NegEvPerfilEventoServicoTO servico : this.evento.getServicos()){
							if(servico.getDescricaoServico().toLowerCase().contains("estacionamento")){
								qtdestacionamento += servico.getQuantidade();
								break;
							}
						}
						StringUtilities.substituirTodos(texto, tag, qtdestacionamento.toString());
						break;
				}
			}
		}
			} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * Altera o src das imagens no HTML.
	 * 
	 * @param texto
	 * @return
	 * <AUTHOR>
	 */
	private void replaceImg(final StringBuilder texto, final Integer codigoModelo) {
		Pattern imagem = Pattern.compile("<\\s*[iI][mM][gG][^>]*[sS][rR][cC]\\s*=\\s*[\\\"']{0,1}([^\\\"'\\s>]*)");
		Matcher matcher = imagem.matcher(texto.toString());

		while (matcher.find()) {
			String img = matcher.group(1);
			int fileInit = img.lastIndexOf("/") + 1;
			String imgFile = img.substring(fileInit, img.length());
			String newImg = this.contexto + "/ce/img?arquivo=" + imgFile + "&modelo=" + codigoModelo + "&q=" + this.tipoDocumento;
			StringUtilities.substituirTodos(texto, img, newImg);
		}
	}

	/**
	 * @param tagsTo the tagsTo to set
	 */
	public void setTagsTo(TagsTO tagsTo) {
		this.tagsTo = tagsTo;
	}

	/**
	 * @return the tagsTo
	 */
	public TagsTO getTagsTo() {
		return tagsTo;
	}

}