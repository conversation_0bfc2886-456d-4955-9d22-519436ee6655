package controle.arquitetura;

public class GrupoFuncionalidadeSistemaEnumAux implements Cloneable {

    private GrupoFuncionalidadeSistemaEnum grupoFuncionalidadeSistemaEnum;
    private String descricao;
    private String iconeClass;
    private FuncionalidadeSistemaEnumAux[] funcionalidades;
    private FuncionalidadeSistemaEnumAux funcionalidade;
    private String menu;
    private boolean renderizar = true;
    private String reRend = "true";
    private GrupoFuncionalidadeSistemaEnumAux[] subMenu;
    private boolean menuSolto = false;

    private boolean renderizarGrupo;

    @Override
    public GrupoFuncionalidadeSistemaEnumAux clone(){
        try {
            return (GrupoFuncionalidadeSistemaEnumAux) super.clone();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    public GrupoFuncionalidadeSistemaEnum getGrupoFuncionalidadeSistemaEnum() {
        return grupoFuncionalidadeSistemaEnum;
    }

    public void setGrupoFuncionalidadeSistemaEnum(GrupoFuncionalidadeSistemaEnum grupoFuncionalidadeSistemaEnum) {
        this.grupoFuncionalidadeSistemaEnum = grupoFuncionalidadeSistemaEnum;
    }

    public String getDescricao() {
        descricao = descricao == null || descricao.trim().length() == 0 ? getGrupoFuncionalidadeSistemaEnum().getDescricao() : descricao;
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getIconeClass() {
        iconeClass = iconeClass == null || iconeClass.trim().length() == 0 ? getGrupoFuncionalidadeSistemaEnum().getIconeClass() : iconeClass;
        return iconeClass;
    }

    public void setIconeClass(String iconeClass) {
        this.iconeClass = iconeClass;
    }

    public FuncionalidadeSistemaEnumAux[] getFuncionalidades() {
        return funcionalidades;
    }

    public void setFuncionalidades(FuncionalidadeSistemaEnumAux[] funcionalidades) {
        this.funcionalidades = funcionalidades;
    }

    public boolean isPossuiFuncionalidades() {
        return getFuncionalidades() != null && getFuncionalidades().length > 0;
    }

    public String getMenu() {
        menu = menu == null || menu.trim().length() == 0 ? getGrupoFuncionalidadeSistemaEnum().getMenu().getName() : menu;
        return menu;
    }

    public void setMenu(String menu) {
        this.menu = menu;
    }

    public boolean isRenderizar() {
        return renderizar;
    }

    public void setRenderizar(boolean renderizar) {
        this.renderizar = renderizar;
    }

    public String getReRend() {
        reRend = reRend == null || reRend.trim().length() == 0 ? getGrupoFuncionalidadeSistemaEnum().getReRend() : reRend;
        return reRend;
    }

    public void setReRend(String reRend) {
        this.reRend = reRend;
    }

    public GrupoFuncionalidadeSistemaEnumAux[] getSubMenu() {
        return subMenu;
    }

    public void setSubMenu(GrupoFuncionalidadeSistemaEnumAux[] subMenu) {
        this.subMenu = subMenu;
    }

    public boolean isPossuiSubMenu() {
        return getSubMenu() != null && getSubMenu().length > 0;
    }

    public boolean getRenderizarGrupo() {
        return renderizarGrupo;
    }

    public void setRenderizarGrupo(boolean renderizarGrupo) {
        this.renderizarGrupo = renderizarGrupo;
    }

    public boolean isMenuSolto() {
        return menuSolto;
    }

    public void setMenuSolto(boolean menuSolto) {
        this.menuSolto = menuSolto;
    }

    public FuncionalidadeSistemaEnumAux getFuncionalidade() {
        return funcionalidade;
    }

    public void setFuncionalidade(FuncionalidadeSistemaEnumAux funcionalidade) {
        this.funcionalidade = funcionalidade;
    }
}
