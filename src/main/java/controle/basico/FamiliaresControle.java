package controle.basico;

import br.com.pactosolucoes.ce.comuns.to.PessoaTO;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.FamiliarTO;
import negocio.comuns.basico.FamiliarVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.List;

public class FamiliaresControle extends SuperControle {

    private List<FamiliarTO> familiares = new ArrayList<FamiliarTO>();
    private String nome = "";
    private ClienteVO clienteVO;

    public FamiliaresControle(){
        //TODO: 14/06/19 - Recurso desativado até o J Alcides concluir
//        try {
//            String matricula = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("matricula");
//            clienteVO = getFacade().getCliente().consultarPorMatricula(matricula, false, Uteis.NIVELMONTARDADOS_MINIMOS);
//            familiares = getFacade().getFamiliar().consultarFamiliaresMatricula(matricula, false);
//        }catch (Exception e){
//            Uteis.logar(e, FamiliaresControle.class);
//        }

    }

    public void removerFamiliar(){
        try {
            FamiliarTO f = (FamiliarTO) context().getExternalContext().getRequestMap().get("f");
            familiares.remove(f);
            montarSucessoDadosGravados();
            montarInfo("Familiar removido.");
            setMsgAlert(getMensagemNotificar());
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public List<PessoaTO> executarAutocompleteConsultaPessoa(Object suggest) {
        String pref = (String) suggest;
        List<PessoaTO> result;
        try {
            result = getFacade().getPessoa().consultarNomeSimples(getEmpresaLogado().getCodigo(), pref.equals("%") ? "" : pref);
        } catch (Exception ex) {
            result = (new ArrayList<PessoaTO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarPessoaSuggestionBox() throws Exception {
        PessoaTO pessoa = (PessoaTO) request().getAttribute("result");
        FamiliarTO familiarTO = getFacade().getFamiliar().montarFamiliar(pessoa.getCodigo());
        familiares.add(familiarTO);
        nome = "";
    }

    public void gravar(){
        try {
            getFacade().getFamiliar().gravarEdicaoFamilia(familiares, clienteVO, getEmpresaLogado().getCodigo());
            montarSucessoDadosGravados();
            setMsgAlert("Richfaces.hideModalPanel('mdlNovoMembroFamilia');"+getMensagemNotificar());
        }catch (Exception e){
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
        }
    }

    public List<FamiliarTO> getFamiliares() {
        return familiares;
    }

    public void setFamiliares(List<FamiliarTO> familiares) {
        this.familiares = familiares;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
