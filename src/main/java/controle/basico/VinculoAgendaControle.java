package controle.basico;

import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.AgendaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.UtilReflection;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by johnys on 02/09/2016.
 */
public class VinculoAgendaControle extends SuperControle{

    private List<AgendaVO> agendas;

    private List<UsuarioVO> colaboradores;

    private List<SelectItem> selectItensColaboradores;

    private Integer usuarioVOSelecionado;

    private String onCompleteBotaoGravar;

    private String onCompleteBotaoVoltar;

    private String metodoChamarGravar;

    private String metodoChamarVoltar;

    private Object control;

    private String titulo;

    private String  reRenderComponents;

    private ColaboradorVO colaborador;

    private String mensagemSuperiorTela;

    private String mensagemComboColaboradores;

    private TipoColaboradorEnum tipoColaborador;

    /**
     * Realiza o carregamento das agendas vinculadas ao {@link ColaboradorVO} pelo campo <code>AgendaVo.colaboradorResponsavel</code>.
     * Realiza o carregamento de todos os colaboradores que estão ativos para a determinada empresa.
     * @param colaborador {@link ColaboradorVO} para qual sera baseado para carregar os outros {@link ColaboradorVO} da empresa.
     * @throws Exception
     */
    public void carregarDadosVinculo(ColaboradorVO colaborador) throws  Exception{
        this.agendas = getFacade().getAgenda().consultarPorColaboradorResponsavel(colaborador.getCodigo(), Calendario.hoje(), negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_MINIMOS);
        carregarDadosVinculo(colaborador, this.agendas);
    }

    /**
     * Realiza o carregamento de todos os colaboradores que estão ativos para a determinada empresa.
     * Vincula os dados das {@link AgendaVO} aos dados que serão exibidos na tela.
     * @param colaborador {@link ColaboradorVO} para qual sera baseado para carregar os outros {@link ColaboradorVO} da empresa.
     * @param agendas {@link AgendaVO} para quais serão mostrados os clientes na interface. Caso seja null, a listagem de agendas não aparecera no modal.
     * @throws Exception
     */
    public void carregarDadosVinculo(ColaboradorVO colaborador, List<AgendaVO> agendas) throws  Exception{
        this.agendas = agendas;
        this.colaborador = colaborador;
        initValoresPadroes();
        setColaboradores(getFacade().getUsuario().consultarTodosUsuariosColaboradorAtivosPorEmpresaEPassivelAbrirMetaAgendamento(colaborador.getEmpresa().getCodigo(), this.tipoColaborador, negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_MINIMOS));
    }

    /**
     * Realiza o carregamento de todos os colaboradores que estão ativos para a determinada empresa, para um determinado {@link TipoColaboradorEnum}
     * @param colaborador {@link ColaboradorVO} para qual sera baseado para carregar os outros {@link ColaboradorVO} da empresa.
     * @param agendas {@link AgendaVO} para quais serão mostrados os clientes na interface. Caso seja null, a listagem de agendas não aparecera no modal.
     * @param tipoColaborador Define o {@link TipoColaboradorEnum} que os {@link ColaboradorVO} devem possuir para serem carregados na combo de seleção.
     * @throws Exception
     */
    public void carregarDadosVinculo(ColaboradorVO colaborador, List<AgendaVO> agendas, TipoColaboradorEnum tipoColaborador) throws  Exception{
        this.carregarDadosVinculo(colaborador, agendas);
        this.tipoColaborador = tipoColaborador;
    }

    /**
     * Informa as propriedades básicas para o {@link VinculoAgendaControle}.
     * @param titulo Título da página.
     * @param  control Objeto em que serão invocados os métodos metodoBotaoGravar, metodoBotaoFechar.
     * @param metodoBotaoGravar Método invocado em <code>this.control</code> ao clicar na ação 'Gravar'.
     * @param onCompleteBotaoGravar Javascript invocado no botão 'Gravar'.
     * @param metodoBotaoFechar Método invocado em <code>this.control</code> ao clicar na ação 'Fechar'.
     * @param onCompleteBotaoVoltar Javascript invocado no botão 'Voltar'.
     * @param reRenderComponents Componentes que serão renderizados após as ações dos botões 'Gravar' e 'Voltar'.
     */
    public void setarPropriedades(String titulo, String mensagemSuperiorTela, Object control, String metodoBotaoGravar, String onCompleteBotaoGravar, String metodoBotaoFechar, String onCompleteBotaoVoltar, String reRenderComponents){
        setTitulo(titulo);
        setControl(control);
        setMensagemSuperiorTela(mensagemSuperiorTela);
        setMetodoChamarGravar(metodoBotaoGravar);
        setMetodoChamarVoltar(metodoBotaoFechar);
        setOnCompleteBotaoGravar(onCompleteBotaoGravar);
        setOnCompleteBotaoVoltar(onCompleteBotaoVoltar);
        setReRenderComponents(reRenderComponents);
    }

    /**
     * Informa as propriedades básicas para o {@link VinculoAgendaControle}.
     * @param titulo Título da página.
     * @param  control Objeto em que serão invocados os métodos metodoBotaoGravar, metodoBotaoFechar.
     * @param metodoBotaoGravar Método invocado em <code>this.control</code> ao clicar na ação 'Gravar'.
     * @param onCompleteBotaoGravar Javascript invocado no botão 'Gravar'.
     * @param metodoBotaoFechar Método invocado em <code>this.control</code> ao clicar na ação 'Fechar'.
     * @param onCompleteBotaoVoltar Javascript invocado no botão 'Voltar'.
     * @param reRenderComponents Componentes que serão renderizados após as ações dos botões 'Gravar' e 'Voltar'.
     * @param  mensagemComboColaboradores Mentagem monstrada no campo superior dos combos.
     */
    public void setarPropriedades(String titulo, String mensagemSuperiorTela, Object control, String metodoBotaoGravar, String onCompleteBotaoGravar, String metodoBotaoFechar, String onCompleteBotaoVoltar, String reRenderComponents, String mensagemComboColaboradores){
        setMensagemComboColaboradores(mensagemComboColaboradores);
        this.setarPropriedades(titulo, mensagemSuperiorTela, control, metodoBotaoGravar,  onCompleteBotaoGravar,  metodoBotaoFechar,  onCompleteBotaoVoltar,  reRenderComponents);
    }

    /**
     * Método chamado quando o botão fechar da interface e invocado.
     * Ele invoca o metodo fechar no <code>this.control</code> caso exista.
     * @return
     * @throws Exception
     */
    public String fechar() throws Exception {
        try {
            UtilReflection.invoke(control, "fechar");
        } catch (Exception ignored) {
        }

        return "";
    }

    /**
     * Realiza a invocação do método <code>this.metodoChamarGravar</code> sobre o objeto <code>this.control</code>.
     * Caso o retorno do metodo seja {@link String} sera retornado como resultado da chamada.
     * @return
     */
    public String invokeBotaoGravar() {
        setMensagemDetalhada("", "");
        try {
            if (this.metodoChamarGravar != null){
                Object objRetorno = UtilReflection.invoke(control, metodoChamarGravar);
                if ((objRetorno != null) && (objRetorno instanceof String)){
                    return (String)objRetorno;
                }
            }

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(VinculoAgendaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    /**
     * Realiza a invocação do método <code>this.metodoChamarGravar</code> sobre o objeto <code>this.control</code>.
     * Caso o retorno do metodo seja {@link String} sera retornado como resultado da chamada.
     * @return
     */
    public String invokeBotaoVoltar() {
        setMensagemDetalhada("", "");
        try {
            if (this.metodoChamarVoltar != null){
                Object objRetorno = UtilReflection.invoke(control, metodoChamarVoltar);
                if ((objRetorno != null) && (objRetorno instanceof String)){
                    return (String)objRetorno;
                }
            }

        } catch (Exception ex) {
            setMensagemDetalhada(ex);
            Logger.getLogger(VinculoAgendaControle.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    /**
     * Realiza o carregamento dos valores padrões das propriedades do {@link VinculoAgendaControle}
     */
    private void initValoresPadroes() {
        setMensagemComboColaboradores(null);
        setTipoColaborador(null);
        setMensagemSuperiorTela(null);
        this.usuarioVOSelecionado = 0;
    }

    public Object getControl() {
        return control;
    }

    public void setControl(Object control) {
        this.control = control;
    }

    public String getMetodoChamarGravar() {
        return metodoChamarGravar;
    }

    public void setMetodoChamarGravar(String metodoChamarGravar) {
        this.metodoChamarGravar = metodoChamarGravar;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getOnCompleteBotaoGravar() {
        if (onCompleteBotaoGravar == null) {
            onCompleteBotaoGravar = "";
        }
        return onCompleteBotaoGravar;
    }

    public void setOnCompleteBotaoGravar(String onCompleteBotaoGravar) {
        this.onCompleteBotaoGravar = onCompleteBotaoGravar;
    }

    public String getOnCompleteBotaoVoltar() {
        if (onCompleteBotaoVoltar == null) {
            onCompleteBotaoVoltar = "";
        }
        return onCompleteBotaoVoltar;
    }

    public void setOnCompleteBotaoVoltar(String onCompleteBotaoVoltar) {
        this.onCompleteBotaoVoltar = onCompleteBotaoVoltar;
    }

    public String getMetodoChamarVoltar() {
        return metodoChamarVoltar;
    }

    public void setMetodoChamarVoltar(String metodoChamarVoltar) {
        this.metodoChamarVoltar = metodoChamarVoltar;
    }

    public String getReRenderComponents() {
        return reRenderComponents;
    }

    public void setReRenderComponents(String reRenderComponents) {
        this.reRenderComponents = reRenderComponents;
    }

    public Integer getUsuarioVOSelecionado() {
        return usuarioVOSelecionado;
    }

    public void setUsuarioVOSelecionado(Integer usuarioVOSelecionado) {
        this.usuarioVOSelecionado = usuarioVOSelecionado;
    }

    public List<AgendaVO> getAgendas() {
        return agendas;
    }

    public void setAgendas(List<AgendaVO> agendas) {
        if(this.agendas == null){
            this.agendas = new ArrayList<AgendaVO>();
        }
        this.agendas = agendas;
    }

    public List<UsuarioVO> getColaboradores() {
        if(this.colaboradores == null)
            this.colaboradores = new ArrayList<UsuarioVO>();
        return colaboradores;
    }

    public void setColaboradores(List<UsuarioVO> colaboradores) {
        this.colaboradores = colaboradores;
        this.selectItensColaboradores = new ArrayList<SelectItem>(this.colaboradores.size());
        if(this.colaboradores != null)
        for(UsuarioVO c : this.colaboradores){
            if(c.getColaboradorVO() != null && !UteisValidacao.emptyNumber(c.getColaboradorVO().getCodigo()) && !c.getColaboradorVO().getCodigo().equals(this.colaborador.getCodigo()))
                this.selectItensColaboradores.add(new SelectItem(c.getCodigo(), c.getNome()));
        }
        Ordenacao.ordenarLista(this.selectItensColaboradores, "label");
    }

    public List<SelectItem> getSelectItensColaboradores(){
        if(this.selectItensColaboradores == null)
            this.selectItensColaboradores = new ArrayList<SelectItem>(0);
        return this.selectItensColaboradores;
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public String getMensagemSuperiorTela() {
        return mensagemSuperiorTela;
    }

    public void setMensagemSuperiorTela(String mensagemSuperiorTela) {
        this.mensagemSuperiorTela = mensagemSuperiorTela;
    }

    public String getMensagemComboColaboradores() {
        return mensagemComboColaboradores;
    }

    public void setMensagemComboColaboradores(String mensagemComboColaboradores) {
        this.mensagemComboColaboradores = mensagemComboColaboradores;
    }

    public TipoColaboradorEnum getTipoColaborador() {
        return tipoColaborador;
    }

    public void setTipoColaborador(TipoColaboradorEnum tipoColaborador) {
        this.tipoColaborador = tipoColaborador;
    }
}
