package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.EstadoVO;
import negocio.comuns.basico.PaisVO;
import negocio.comuns.utilitarias.ControleConsulta;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.SelectItemOrdemValor;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Estado;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.List;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas
 * cidadeForm.jsp cidadeCons.jsp) com as funcionalidades da classe <code>Cidade</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Cidade
 * @see CidadeVO
 */
public class CidadeControle extends SuperControle {

    protected List listaSelectItemPais;
    private CidadeVO cidadeVO;
    private EstadoVO estadoVO;
    private PaisVO paisVO;
    private Boolean campoConsultaSelectItem = false;
    /**
     * Interface <code>CidadeInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */
    private List listaSelectItemEstado;
    private String oncompleteLog;
    private String onCompleteModal;

    public CidadeControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        inicializarListasSelectItemTodosComboBox();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Cidade</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setCidadeVO(new CidadeVO());
        inicializarAtributosRelacionados(cidadeVO);
        inicializarListasSelectItemTodosComboBox();
        cidadeVO.setObjetoVOAntesAlteracao(new CidadeVO());
        setSucesso(false);
        setErro(false);
        setMensagemDetalhada("", "");
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Cidade</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        limparMsg();
        try {
            CidadeVO obj = getFacade().getCidade().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(false);
            obj.registrarObjetoVOAntesDaAlteracao();
            setCidadeVO(new CidadeVO());
            setCidadeVO(obj);
            inicializarListasSelectItemTodosComboBox();
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Método responsável inicializar objetos relacionados a classe <code>CidadeVO</code>.
     * Esta inicialização é necessária por exigência da tecnologia JSF, que não trabalha com valores nulos para estes atributos.
     */
    public void inicializarAtributosRelacionados(CidadeVO obj) {
        if (obj.getPais() == null) {
            obj.setPais(new PaisVO());
        }
        if (obj.getEstado() == null) {
            obj.setEstado(new EstadoVO());
        }
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    public String gravar() {
        return gravar(false);
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    public String gravarCE() {
        return gravar(true);
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Cidade</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar(boolean centralEventos) {
        String retorno = "";
        try {

            if (centralEventos) {
                this.verificarAutorizacao();
                if (cidadeVO.isNovoObj()) {
                    getFacade().getCidade().incluir(cidadeVO, true);
                    incluirLogInclusao();
                    retorno = sucesso();

                } else {
                    getFacade().getCidade().alterar(cidadeVO, true);
                    retorno = sucesso();
                    incluirLogAlteracao();
                }
            } else {
                if (cidadeVO.isNovoObj()) {
                    getFacade().getCidade().incluir(cidadeVO);
                    incluirLogInclusao();
                    retorno = sucesso();

                } else {
                    getFacade().getCidade().alterar(cidadeVO);
                    retorno = sucesso();
                    incluirLogAlteracao();
                }
            }

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            retorno = "editar";
        }
        return retorno;
    }

    public void gravarSemRetorno() {
        gravarSemRetorno(false);
    }

    public void gravarSemRetorno(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                if (cidadeVO.isNovoObj()) {
                    getFacade().getCidade().incluir(cidadeVO, true);
                    incluirLogInclusao();
                } else {
                    getFacade().getCidade().alterar(cidadeVO, true);
                    incluirLogAlteracao();
                }
            } else {
                if (cidadeVO.isNovoObj()) {
                    getFacade().getCidade().incluir(cidadeVO);
                    incluirLogInclusao();

                } else {
                    getFacade().getCidade().alterar(cidadeVO);
                    incluirLogAlteracao();
                }
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void incluirLogInclusao() throws Exception {
        //LOG - INICIO
        try {
            cidadeVO.setObjetoVOAntesAlteracao(new CidadeVO());
            cidadeVO.setNovoObj(true);
            registrarLogObjetoVO(cidadeVO, cidadeVO.getCodigo(), "CIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CIDADE", cidadeVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE CIDADE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        cidadeVO.setNovoObj(false);
        cidadeVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    public void incluirLogExlusao() throws Exception {
        //LOG - INICIO
        try {
            cidadeVO.setObjetoVOAntesAlteracao(new CidadeVO());
            cidadeVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(cidadeVO, cidadeVO.getCodigo(), "CIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CIDADE", cidadeVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE CIDADE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        //LOG - FIM
    }

    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(cidadeVO, cidadeVO.getCodigo(), "CIDADE", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("CIDADE", cidadeVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE CIDADE", this.getUsuarioLogado().getNome(), this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        cidadeVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }

    /**
     * <AUTHOR>
     * 24/03/2011
     */
    private String sucesso() {
        setMensagemID("msg_dados_gravados");
        setSucesso(true);
        setErro(false);
        return "editar";
    }

    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP CidadeCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();

            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());

            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    objs = getFacade().getCidade().consultarPorCodigo(0, true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
                    int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                    CidadeVO cidade = getFacade().getCidade().consultarPorCodigoExato(valorInt, true, Uteis.NIVELMONTARDADOS_TODOS);
                    if (cidade != null) {
                        objs.add(cidade);
                    }
                }

            }
            if (getControleConsulta().getCampoConsulta().equals("nome")) {
                objs = getFacade().getCidade().consultarPorNome(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("estado")) {
                objs = getFacade().getCidade().consultarPorEstado(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomePais")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getCidade().consultarPorCodigoPais(valorInt, Uteis.NIVELMONTARDADOS_TODOS);
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            /**
             * Rotina usada para corrigir erro ao salvar cidade com o campo nomsemacento com acento
             * interferindo na validação de cidades já cadastradas
             */
            //Inicio
            try (ResultSet consulta = getFacade().getCidade().consultar()) {
                while (consulta.next()) {
                    CidadeVO cidade = new CidadeVO();
                    cidade.setCodigo(consulta.getInt("codigo"));
                    cidade.setNome(consulta.getString("nome"));
                    cidade.getEstado().setCodigo(consulta.getInt("estado"));
                    cidade.getPais().setCodigo(consulta.getInt("pais"));
                    cidade.setNomeSemAcento(Uteis.retirarAcentuacao(consulta.getString("nome")));
                    getFacade().getCidade().alterar(cidade);
                }
            }
            //Fim
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public void confirmarExcluir() {
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Cidade",
                "Deseja excluir a cidade?",
                this, "excluir", "", "", "", "form");
    }

    public String excluir() {
        return this.excluir(false);
    }

    public void excluirCE() {
        this.excluir(true);
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>CidadeVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir(boolean centralEventos) {
        try {
            if (centralEventos) {
                this.verificarAutorizacao();
                getFacade().getCidade().excluir(cidadeVO, true);
                //registrar log
                registrarLogExclusaoObjetoVO(cidadeVO, cidadeVO.getCodigo(), "CIDADE", 0);
            } else {
                getFacade().getCidade().excluir(cidadeVO);
                incluirLogExlusao();
            }
            setCidadeVO(new CidadeVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /* Método responsável por inicializar List<SelectItem> de valores do
     * ComboBox correspondente ao atributo <code>estado</code>
     */
    public List getListaSelectItemEstadoCidade() {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("", ""));
        Hashtable estados = Dominios.getEstado();
        Enumeration keys = estados.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) estados.get(value);
            objs.add(new SelectItem(value, label));
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort(objs, ordenador);
        return objs;
    }

    /**
     * Método responsável por gerar uma lista de objetos do tipo <code>SelectItem</code> para preencher
     * o comboBox relativo ao atributo <code>Pais</code>.
     */
    public void montarListaSelectItemPais(String prm) throws Exception {
        List<SelectItem> itens = new ArrayList<>();
        itens.add(new SelectItem(0, ""));
        for (PaisVO pais : (ArrayList<PaisVO>) consultarPaisPorNome(prm)) {
            itens.add(new SelectItem(pais.getCodigo(), pais.getNome().toString()));
        }
        Ordenacao.ordenarLista(itens, "label");
        setListaSelectItemPais(itens);
    }

    public void montarListaSelectItemEstado(String prm) throws Exception {
        List<SelectItem> itens = new ArrayList<>();
        itens.add(new SelectItem(0, ""));
        if (this.getCidadeVO() != null) {
            List<EstadoVO> estados = consultarEstadoPorCodigoPais(this.getCidadeVO().getPais().getCodigo());
            for (EstadoVO estado : estados) {
                itens.add(new SelectItem(estado.getCodigo(), estado.getDescricao()));
            }
            Ordenacao.ordenarLista(itens, "label");
        }
        setListaSelectItemEstado(itens);
    }

    /**
     * Método responsável por atualizar o ComboBox relativo ao atributo <code>Pais</code>.
     * Buscando todos os objetos correspondentes a entidade <code>Pais</code>. Esta rotina não recebe parâmetros para filtragem de dados, isto é
     * importante para a inicialização dos dados da tela para o acionamento por meio requisições Ajax.
     */
    public void montarListaSelectItemPais() {
        try {
            montarListaSelectItemPais("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void montarListaSelectItemEstado() {
        try {
            montarListaSelectItemEstado("");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por consultar dados da entidade <code><code> e montar o atributo <code>nome</code>
     * Este atributo é uma lista (<code>List</code>) utilizada para definir os valores a serem apresentados no ComboBox correspondente
     */
    public List consultarPaisPorNome(String nomePrm) throws Exception {
        return getFacade().getPais().consultarPorNome(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public List<EstadoVO> consultarEstadoPorCodigoPais(Integer nomePrm) throws Exception {
        return getFacade().getPais().consultarEstadoPorPais(nomePrm, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public List consultarEstadoPorNome(String nomePrm) throws Exception {
        return new Estado().consultarPorDescricao(nomePrm, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public void atualizarEstado() {
        try {
            getCidadeVO().setEstado(getFacade().getEstado().consultarPorCodigo(getCidadeVO().getEstado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Método responsável por inicializar a lista de valores (<code>SelectItem</code>) para todos os ComboBox's.
     */
    public void inicializarListasSelectItemTodosComboBox() {
        montarListaSelectItemEstado();
        montarListaSelectItemPais();

    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<>();
        itens.add(new SelectItem("nome", "Nome"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("estado", "Estado"));
        itens.add(new SelectItem("nomePais", "País"));
        Ordenacao.ordenarLista(itens, "label");
        return itens;
    }

    public void alterarCampoConsulta() {
        String campoSelecionado = getControleConsulta().getCampoConsulta();
        getControleConsulta().setValorConsulta("");
        if (campoSelecionado.equals("nomePais")) {
            setCampoConsultaSelectItem(Boolean.TRUE);
        } else {
            setCampoConsultaSelectItem(Boolean.FALSE);
        }
    }

    public List getListaSelectItemConsulta() {
        List<SelectItem> itens = new ArrayList<>();
        itens.add(new SelectItem("", ""));

        try {
            if (getControleConsulta().getCampoConsulta().equals("nomePais")) {
                return getListaSelectItemPais();
            }
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
            e.printStackTrace();
        }

        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("msg_entre_prmconsulta");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados.
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public List getListaSelectItemPais() {
        return (listaSelectItemPais);
    }

    public void setListaSelectItemPais(List listaSelectItemPais) {
        this.listaSelectItemPais = listaSelectItemPais;
    }

    public CidadeVO getCidadeVO() {
        return cidadeVO;
    }

    public void setCidadeVO(CidadeVO cidadeVO) {
        this.cidadeVO = cidadeVO;
    }

    public List getListaSelectItemEstado() {
        return listaSelectItemEstado;
    }

    public void setListaSelectItemEstado(List listaSelectItemEstado) {
        this.listaSelectItemEstado = listaSelectItemEstado;
    }

    public EstadoVO getEstadoVO() {
        return estadoVO;
    }

    public void setEstadoVO(EstadoVO estadoVO) {
        this.estadoVO = estadoVO;
    }

    public PaisVO getPaisVO() {
        return paisVO;
    }

    public void setPaisVO(PaisVO paisVO) {
        this.paisVO = paisVO;
    }

    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Cidade");
        loginControle.consultarLogObjetoSelecionado("CIDADE", cidadeVO.getCodigo(), null);
    }

    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        cidadeVO = new CidadeVO();
        realizarConsultaLogObjetoSelecionado();
    }

    public String getOncompleteLog() {
        return oncompleteLog;
    }

    public void setOncompleteLog(String oncompleteLog) {
        this.oncompleteLog = oncompleteLog;
    }

    public Boolean getCampoConsultaSelectItem() {
        return campoConsultaSelectItem;
    }

    public void setCampoConsultaSelectItem(Boolean campoConsultaSelectItem) {
        this.campoConsultaSelectItem = campoConsultaSelectItem;
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getCidade().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public String getOnCompleteModal() {
        return onCompleteModal;
    }

    public void setOnCompleteModal(String onCompleteModal) {
        this.onCompleteModal = onCompleteModal;
    }

    public void prepararCadastro(ActionEvent actionEvent) {
        try {
            setCidadeVO(new CidadeVO());
            inicializarAtributosRelacionados(cidadeVO);

            Integer pais = Integer.parseInt(request().getParameter("pais"));
            Integer estado = Integer.parseInt(request().getParameter("estado"));
            getCidadeVO().getPais().setCodigo(pais);
            getCidadeVO().getEstado().setCodigo(estado);

            setOnCompleteModal( request().getParameter("onCompleteModal"));

            inicializarListasSelectItemTodosComboBox();
            for (Object obj : getListaSelectItemEstado()) {
                SelectItem item = (SelectItem) obj;
                if (item.getValue().equals(getCidadeVO().getEstado().getCodigo())) {
                    getCidadeVO().getEstado().setDescricao(item.getLabel());
                    break;
                }
            }
            cidadeVO.setObjetoVOAntesAlteracao(new CidadeVO());
            limparMsg();
        } catch (Exception ex) {
            montarErro(ex);
        }
    }
}
