package controle.basico;

import negocio.comuns.basico.FamiliaTO;

import java.util.Date;

public class BIFamiliaTO {

    private Integer cadastradas = 0;
    private Integer ativas = 0;
    private Integer risco = 0;
    private Integer comInativo = 0;
    private FamiliaTO maiorValor;
    private Double valor;
    private FamiliaTO maisAcessos;
    private Integer acessos;
    private Date acessosDesde;
    private FamiliaTO maisTempo;
    private Date desde;
    private FamiliaTO maisRecente;
    private Date recente;

    public Integer getCadastradas() {
        return cadastradas;
    }

    public void setCadastradas(Integer cadastradas) {
        this.cadastradas = cadastradas;
    }

    public Integer getAtivas() {
        return ativas;
    }

    public void setAtivas(Integer ativas) {
        this.ativas = ativas;
    }

    public Integer getRisco() {
        return risco;
    }

    public void setRisco(Integer risco) {
        this.risco = risco;
    }

    public Integer getComInativo() {
        return comInativo;
    }

    public void setComInativo(Integer comInativo) {
        this.comInativo = comInativo;
    }

    public FamiliaTO getMaiorValor() {
        return maiorValor;
    }

    public void setMaiorValor(FamiliaTO maiorValor) {
        this.maiorValor = maiorValor;
    }

    public FamiliaTO getMaisAcessos() {
        return maisAcessos;
    }

    public void setMaisAcessos(FamiliaTO maisAcessos) {
        this.maisAcessos = maisAcessos;
    }

    public FamiliaTO getMaisTempo() {
        return maisTempo;
    }

    public void setMaisTempo(FamiliaTO maisTempo) {
        this.maisTempo = maisTempo;
    }

    public FamiliaTO getMaisRecente() {
        return maisRecente;
    }

    public void setMaisRecente(FamiliaTO maisRecente) {
        this.maisRecente = maisRecente;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }

    public Date getAcessosDesde() {
        return acessosDesde;
    }

    public void setAcessosDesde(Date acessosDesde) {
        this.acessosDesde = acessosDesde;
    }

    public Date getDesde() {
        return desde;
    }

    public void setDesde(Date desde) {
        this.desde = desde;
    }

    public Date getRecente() {
        return recente;
    }

    public void setRecente(Date recente) {
        this.recente = recente;
    }
}
