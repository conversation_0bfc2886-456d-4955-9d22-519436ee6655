package controle.basico;

import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import importador.ExportadorExcel;
import importador.ImportacaoConfigTO;
import importador.LeitorExcel2010;
import importador.UteisImportacaoExcel;
import importador.enumerador.ImportacaoParcelasSituacaoEnum;
import importador.json.*;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.acesso.integracao.member.MemberShipApivViewTO;
import negocio.comuns.acesso.integracao.member.enums.TipoOperacaoIntegracaoMembersEnum;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.MascaraDataEnum;
import negocio.comuns.basico.enumerador.TipoImportacaoEnum;
import negocio.comuns.basico.enumerador.TipoLogEnum;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.acesso.IntegracaoMember;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.ProcessoImportacao;
import negocio.facade.jdbc.basico.ProcessoImportacaoLog;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.poifs.filesystem.OfficeXmlFileException;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.json.JSONArray;
import org.json.JSONObject;
import org.richfaces.event.UploadEvent;
import org.richfaces.model.UploadItem;
import relatorio.negocio.comuns.basico.ItemRelatorioTO;
import servicos.acesso.IntegracaoMemberService;

import javax.faces.model.SelectItem;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

/**
 * Created by Luiz Felipe on 20/12/2019.
 */
public class ImportacaoControle extends SuperControle {

    private String emailAdicionar;
    private List<String> listaEmails;

    private String onComplete;
    private int scrollerPage;

    private String abaAtual = "abaCliente";
    private String abaTreinoAtual = "abaAtividades";

    //select item
    private List<SelectItem> listaPlano;
    private List<SelectItem> listaModalidade;
    private List<SelectItem> listaHorarios;
    private List<SelectItem> listaConsultor;
    private List<SelectItem> listaProfessorTreinoWeb;
    private List<SelectItem> listaCategoria;

    //aba parcelas / pagamentos
    private File arquivoParcelasPagamentos;
    private ConfigExcelImportacaoTO configParcelasPagamentosTO;
    List<ParcelaPagamentoJSON> listaParcelasPagamentosJSON;
    List<TreinoAtividadeJSON> listaTreinoAtividadesJSON;
    List<ProgramaFichaJSON> listaTreinoProgramasJSON;
    List<AtividadeFichaJSON> listaTreinoAtividadeFichaJSON;

    //aba cliente / contrato
    private File arquivoCliente;
    private File arquivoContrato;
    private ConfigExcelImportacaoTO configClienteTO;

    private List<ClienteImportacaoJSON> listaClientesJSON;
    private List<ContratoImportacaoJSON> listaContratosJSON;
    private boolean configSesc = false;

    //aba colaborador
    private File arquivoColaborador;
    private ConfigExcelImportacaoTO configColaboradorTO;
    private List<ColaboradorImportacaoJSON> listaColaboradorJSON;

    //aba fornecedor
    private File arquivoFornecedor;
    private ConfigExcelImportacaoTO configFornecedorTO;
    private List<FornecedorImportacaoJSON> listaFornecedorJSON;

    //aba produtos
    private File arquivoProduto;
    private ConfigExcelImportacaoTO configProdutoTO;
    private List<ProdutoImportacaoJSON> listaProdutosJSON;

    //aba contas
    private File arquivoConta;
    private ConfigExcelImportacaoTO configContaTO;
    private List<ContaImportacaoJSON> listaContasJSON;

    //aba histórico
    private List<ProcessoImportacaoLogVO> logImportacao;
    private List<ProcessoImportacaoItemVO> itensImportacao;
    private List<ProcessoImportacaoVO> historico;

    // aba importação Members
    private IntegracaoMemberVO integracaoMember;
    private String idsMembers;
    private Integer tipoOperacaoIntegracaoMembers;
    private Integer codigoEmpresa;
    private File arquivoCorrespondencia;

    //aba Aluno Turma
    private File arquivoAlunoTurma;
    private ConfigExcelImportacaoTO configAlunoTurmaTO;
    private List<AlunoTurmaImportacaoJSON> listaAlunoTurmaJSON;

    //aba Turma
    private File arquivoTurma;
    private ConfigExcelImportacaoTO configTurmaTO;
    private List<TurmaImportacaoJSON> listaTurmaJSON;

    //aba importadorNextFit
    private File arquivoNextFit;
    private ConfigExcelImportacaoTO configNextFit;
    private List<AlunoTurmaImportacaoJSON> listaNextFitJSON;

    //aba Importador Cloud Gyn
    private File arquivoCloudGyn;
    private ConfigExcelImportacaoTO configCloudgyn;
    private List<AlunoTurmaImportacaoJSON> listaCloudgynJSON;


    public ImportacaoControle() {
        novo();
    }

    public void novo() {
        limparMsg();
        setOnComplete("");
        setEmailAdicionar("");
        setAbaAtual("abaCliente");

        setLogImportacao(new ArrayList<>());
        setItensImportacao(new ArrayList<>());
        setHistorico(new ArrayList<>());

        setArquivoParcelasPagamentos(null);
        setArquivoCliente(null);
        setArquivoContrato(null);
        setArquivoFornecedor(null);
        setArquivoProduto(null);
        setArquivoConta(null);
        setArquivoColaborador(null);
        setArquivoAlunoTurma(null);

        setListaParcelasPagamentosJSON(new ArrayList<>());
        setListaClientesJSON(new ArrayList<>());
        setListaContratosJSON(new ArrayList<>());
        setListaContasJSON(new ArrayList<>());
        setListaFornecedorJSON(new ArrayList<>());
        setListaProdutosJSON(new ArrayList<>());
        setListaColaboradorJSON(new ArrayList<>());
        setListaAlunoTurmaJSON(new ArrayList<>());
        setListaTurmaJSON(new ArrayList<>());
        setListaTreinoAtividadesJSON(new ArrayList<>());
        setListaTreinoProgramasJSON(new ArrayList<>());
        setListaTreinoAtividadeFichaJSON(new ArrayList<>());


        setConfigParcelasPagamentosTO(new ConfigExcelImportacaoTO());
        setConfigClienteTO(new ConfigExcelImportacaoTO());
        setConfigProdutoTO(new ConfigExcelImportacaoTO());
        setConfigContaTO(new ConfigExcelImportacaoTO());
        setConfigAlunoTurmaTO(new ConfigExcelImportacaoTO());
        setConfigTurmaTO(new ConfigExcelImportacaoTO());
        setConfigFornecedorTO(new ConfigExcelImportacaoTO());
        setConfigColaboradorTO(new ConfigExcelImportacaoTO());

        montarComboConsultor();
        montarComboProfessorTreinoWeb();
        montarComboPlano();
        montarComboModalidade();
        montarComboHorario();
        montarComboCategoria();
        consultarConfiguracaoIntegracaoMember();
        consultarConfiguracaoSesc();
    }

    public void removerArquivo() {
        if (getAbaAtual().equalsIgnoreCase("abaCliente")) {
            setArquivoCliente(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaContrato")) {
            setArquivoContrato(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaColaborador")) {
            setArquivoColaborador(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaFornecedor")) {
            setArquivoFornecedor(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaConta")) {
            setArquivoConta(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaProduto")) {
            setArquivoProduto(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaMembers")) {
            setArquivoCorrespondencia(null);
        }
        else if (getAbaAtual().equalsIgnoreCase("abaImportacaoAlunoTurmar")) {
            setArquivoAlunoTurma(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaParcelasPagamentos")) {
            setArquivoParcelasPagamentos(null);
        }
        else if (getAbaAtual().equalsIgnoreCase("abaImportacaoNextFit")) {
            setArquivoAlunoTurma(null);
        }
        else if (getAbaAtual().equalsIgnoreCase("abaImportacaoCloudGyn")) {
            setArquivoAlunoTurma(null);
        }
        else if (getAbaAtual().equalsIgnoreCase("abaImportacaoTurma")) {
            setArquivoTurma(null);
        } else if (getAbaAtual().equalsIgnoreCase("abaTreino")) {
            if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividades")) {
                setArquivoParcelasPagamentos(null);
            } else if (getAbaTreinoAtual().equalsIgnoreCase("abaProgramas")) {
                setArquivoParcelasPagamentos(null);
            } else if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividadeFicha")) {
                setArquivoParcelasPagamentos(null);
            }
        }
    }

    public boolean isApresentarImportar() {
        if (getAbaAtual().equalsIgnoreCase("abaCliente")) {
            return getArquivoCliente() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaContrato")) {
            return getArquivoContrato() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaColaborador")) {
            return getArquivoColaborador() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaFornecedor")) {
            return getArquivoFornecedor() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaConta")) {
            return getArquivoConta() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaProduto")) {
            return getArquivoProduto() != null;
        }
        else if (getAbaAtual().equalsIgnoreCase("abaImportacaoAlunoTurmar")) {
            return getArquivoAlunoTurma() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaParcelasPagamentos")) {
            return getArquivoParcelasPagamentos() != null;
        }
        else if (getAbaAtual().equalsIgnoreCase("abaImportacaoTurma")) {
            return getArquivoTurma() != null;
        } else if (getAbaAtual().equalsIgnoreCase("abaTreino")) {
            if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividades")) {
                return getArquivoParcelasPagamentos() != null;
            } else if (getAbaTreinoAtual().equalsIgnoreCase("abaProgramas")) {
                return getArquivoParcelasPagamentos() != null;
            } else if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividadeFicha")) {
                return getArquivoParcelasPagamentos() != null;
            } else {
                return false;
            }
        }
        return false;
    }

    public void uploadArquivoParcelasPagamentos(final UploadEvent event) throws Exception {
        // Obter o arquivo a partir do evento
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        // cria um novo arquivo de imagem
        arquivoParcelasPagamentos = new File(item.getFile().getParent() + File.separator + item.getFileName());
        // caso exista o arquivo ele é deletado
        arquivoParcelasPagamentos.delete();
        final FileOutputStream out = new FileOutputStream(arquivoParcelasPagamentos);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        // Limpa a memória assim que o arquivo e carregado
        out.flush();
        out.close();
    }

    public void uploadArquivoCliente(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoCliente = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoCliente.delete();
        final FileOutputStream out = new FileOutputStream(arquivoCliente);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoContrato(final UploadEvent event) throws Exception {
        // Obter o arquivo a partir do evento
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        // cria um novo arquivo de imagem
        arquivoContrato = new File(item.getFile().getParent() + File.separator + item.getFileName());
        // caso exista o arquivo ele é deletado
        arquivoContrato.delete();
        final FileOutputStream out = new FileOutputStream(arquivoContrato);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        // Limpa a memória assim que o arquivo e carregado
        out.flush();
        out.close();
    }

    public void uploadArquivoAlunoTurma(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoAlunoTurma = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoAlunoTurma.delete();
        final FileOutputStream out = new FileOutputStream(arquivoAlunoTurma);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoTurma(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoTurma = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoTurma.delete();
        final FileOutputStream out = new FileOutputStream(arquivoTurma);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoCorrepondencia(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoCorrespondencia = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoCorrespondencia.delete();
        final FileOutputStream out = new FileOutputStream(arquivoCorrespondencia);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoColaborador(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoColaborador = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoColaborador.delete();
        final FileOutputStream out = new FileOutputStream(arquivoColaborador);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoFornecedor(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoFornecedor = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoFornecedor.delete();
        final FileOutputStream out = new FileOutputStream(arquivoFornecedor);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoProduto(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoProduto = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoProduto.delete();
        final FileOutputStream out = new FileOutputStream(arquivoProduto);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void uploadArquivoConta(final UploadEvent event) throws Exception {
        final UploadItem item = event.getUploadItem();
        final File arquivoUploaded = item.getFile();
        arquivoConta = new File(item.getFile().getParent() + File.separator + item.getFileName());
        arquivoConta.delete();
        final FileOutputStream out = new FileOutputStream(arquivoConta);
        out.write(FileUtilities.obterBytesArquivo(arquivoUploaded));
        out.flush();
        out.close();
    }

    public void processarArquivoParcelasPagamentos() {
        try {
            limparMsg();
            setOnComplete("");
            setListaParcelasPagamentosJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigClienteTO(), getArquivoParcelasPagamentos(), TipoImportacaoEnum.PARCELAS_PAGAMENTOS);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoParcelasPagamentos().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaParcelasPagamentosJSONExcel(linhas, getConfigParcelasPagamentosTO());
            setListaParcelasPagamentosJSON(uteisImportacaoExcel.getListaParcelasPagamentos());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaParcelasPagamentosJSON())) {
                throw new Exception("Nenhuma parcela para ser importada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoTreinoAtividades() {
        try {
            limparMsg();
            setOnComplete("");
            setListaTreinoAtividadesJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigClienteTO(), getArquivoParcelasPagamentos(), TipoImportacaoEnum.TREINO_ATIVIDADES);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoParcelasPagamentos().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaTreinoAtividadesJSONExcel(linhas);
            setListaTreinoAtividadesJSON(uteisImportacaoExcel.getListaTreinoAtividades());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaTreinoAtividadesJSON())) {
                throw new Exception("Nenhuma atividade para ser importada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoTreinoProgramas() {
        try {
            limparMsg();
            setOnComplete("");
            setListaTreinoProgramasJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigClienteTO(), getArquivoParcelasPagamentos(), TipoImportacaoEnum.TREINO_PROGRAMAS);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoParcelasPagamentos().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaTreinoProgramasJSONExcel(linhas);
            setListaTreinoProgramasJSON(uteisImportacaoExcel.getListaTreinoProgramas());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaTreinoProgramasJSON())) {
                throw new Exception("Nenhuma atividade para ser importada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoTreinoAtividadeFicha() {
        try {
            limparMsg();
            setOnComplete("");
            setListaTreinoAtividadeFichaJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigClienteTO(), getArquivoParcelasPagamentos(), TipoImportacaoEnum.TREINO_ATIVIDADE_FICHA);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoParcelasPagamentos().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaTreinoAtividadeFichaJSONExcel(linhas);
            setListaTreinoAtividadeFichaJSON(uteisImportacaoExcel.getListaTreinoAtividadeFicha());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaTreinoAtividadeFichaJSON())) {
                throw new Exception("Nenhuma atividade para ser importada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoCliente() {
        try {
            limparMsg();
            setOnComplete("");
            setListaClientesJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigClienteTO(), getArquivoCliente(), TipoImportacaoEnum.CLIENTE);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoCliente().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaClienteJSONExcel(linhas, getConfigClienteTO());
            setListaClientesJSON(uteisImportacaoExcel.getListaClientes());
            setListaContratosJSON(uteisImportacaoExcel.getListaContratos());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaClientesJSON())) {
                throw new Exception("Nenhum aluno para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoContrato() {
        try {
            limparMsg();
            setOnComplete("");
            setListaContratosJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigClienteTO(), getArquivoContrato(), TipoImportacaoEnum.CONTRATO);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoContrato().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaContratoJSONExcel(linhas, getConfigClienteTO());
            setListaContratosJSON(uteisImportacaoExcel.getListaContratos());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaContratosJSON())) {
                throw new Exception("Nenhum contrato para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoAlunoTurma() {
        try {
            limparMsg();
            setOnComplete("");
            setListaAlunoTurmaJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigAlunoTurmaTO(), getArquivoAlunoTurma(), TipoImportacaoEnum.ALUNOS_TURMAS);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoAlunoTurma().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaAlunoTurmaJSONExcel(linhas, getConfigAlunoTurmaTO());
            setListaAlunoTurmaJSON(uteisImportacaoExcel.getListaAlunoTurma());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaAlunoTurmaJSON())) {
                throw new Exception("Nenhum aluno para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoTurma() {
        try {
            limparMsg();
            setOnComplete("");
            setListaTurmaJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigTurmaTO(), getArquivoTurma(), TipoImportacaoEnum.TURMAS);

            List<XSSFRow> rows = LeitorExcel2010.lerLinhas(getArquivoTurma().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            uteisImportacaoExcel.obterListaTurmaJSONExcel(rows, getConfigTurmaTO().getEmpresaVO().getCodigo());
            setListaTurmaJSON(uteisImportacaoExcel.getListaTurma());
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaTurmaJSON())) {
                throw new Exception("Nenhum aluno para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void gravarConfiguracaoIntegracaoMember() throws Exception {
        limparMsg();
        setOnComplete("");

        try {
            integracaoMember.validarDados();
            IntegracaoMember integracaoMemberDAO = new IntegracaoMember(Conexao.getFromSession());
//            integracaoMember.setSincronizacaoPosImportacao(true);
            if (UteisValidacao.emptyNumber(integracaoMember.getCodigo())) {
                integracaoMember.setEmpresa(getEmpresaLogado());
                integracaoMemberDAO.incluir(integracaoMember);
            } else {
                integracaoMemberDAO.alterar(integracaoMember);
            }
            integracaoMemberDAO = null;
            montarSucessoGrowl("Dados gravados com sucesso");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public void gravarConfiguracaoCloudGyn() throws Exception {
        limparMsg();
        setOnComplete("");

        try {
            integracaoMember.validarDados();
            IntegracaoMember integracaoMemberDAO = new IntegracaoMember(Conexao.getFromSession());
//            integracaoMember.setSincronizacaoPosImportacao(true);
            if (UteisValidacao.emptyNumber(integracaoMember.getCodigo())) {
                integracaoMember.setEmpresa(getEmpresaLogado());
                integracaoMemberDAO.incluir(integracaoMember);
            } else {
                integracaoMemberDAO.alterar(integracaoMember);
            }
            integracaoMemberDAO = null;
            montarSucessoGrowl("Dados gravados com sucesso");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    public void processarImportacaoMembers() {
        limparMsg();
        setOnComplete("");
        try {
            integracaoMember.validarDados();
            Thread threadImportacao = obterThreadImportacaoEmAndamento();
            if (threadImportacao != null) {
                throw new Exception("Já existe uma importação em andamento, por favor aguarde até que ela seja finalizada.");
            }

            if (UteisValidacao.emptyString(idsMembers)) {
                throw new Exception("Matriculas não informadas!");
            }

            getFacade().getProcessoImportacao().iniciarThreadMembers(TipoImportacaoEnum.MEMBERS_EVO, getKey(),
                    getUsuarioLogado().getCodigo(), integracaoMember, idsMembers, TipoOperacaoIntegracaoMembersEnum.obterPorId(tipoOperacaoIntegracaoMembers));

            montarSucessoGrowl("A importação foi iniciada, acompanhe o processo na aba \"Histórico\".");
        } catch (Exception e) {
            montarErro(e);
        }
    }


    private void consultarConfiguracaoIntegracaoMember() {
        try {
            IntegracaoMember integracaoMemberDAO = new IntegracaoMember(Conexao.getFromSession());
            this.integracaoMember = integracaoMemberDAO.consultarIntegracaoPorEmpresa(getEmpresaLogado().getCodigo());
            integracaoMemberDAO = null;
        } catch (Exception e) {
            this.integracaoMember = new IntegracaoMemberVO();
            e.printStackTrace();
        }
    }

    public void processarArquivoColaborador() {
        try {
            limparMsg();
            setOnComplete("");
            setListaColaboradorJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigColaboradorTO(), getArquivoColaborador(), TipoImportacaoEnum.COLABORADOR);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoColaborador().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            setListaColaboradorJSON(uteisImportacaoExcel.obterListaColaboradorExcel(linhas, getConfigColaboradorTO()));
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaColaboradorJSON())) {
                throw new Exception("Nenhum colaborador para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoFornecedor() {
        try {
            limparMsg();
            setOnComplete("");
            setListaFornecedorJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigFornecedorTO(), getArquivoFornecedor(), TipoImportacaoEnum.FORNECEDOR);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoFornecedor().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            setListaFornecedorJSON(uteisImportacaoExcel.obterListaFornecedorExcel(linhas, getConfigFornecedorTO()));
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaFornecedorJSON())) {
                throw new Exception("Nenhum fornecedor para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoConta() {
        try {
            limparMsg();
            setOnComplete("");
            setListaContasJSON(new ArrayList<>());
            validaPreparaConfiguracaoImportacao(getConfigContaTO(), getArquivoConta(), TipoImportacaoEnum.CONTA_FINANCEIRO);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoConta().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            setListaContasJSON(uteisImportacaoExcel.obterListaContaExcel(linhas, getConfigContaTO()));
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaContasJSON())) {
                throw new Exception("Nenhuma conta para ser importada.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    public void processarArquivoProduto() {
        try {
            limparMsg();
            setOnComplete("");
            setListaProdutosJSON(new ArrayList<>());

            validaPreparaConfiguracaoImportacao(getConfigProdutoTO(), getArquivoProduto(), TipoImportacaoEnum.PRODUTO);

            List<XSSFRow> linhas = LeitorExcel2010.lerLinhas(getArquivoProduto().getPath());
            UteisImportacaoExcel uteisImportacaoExcel = new UteisImportacaoExcel();
            setListaProdutosJSON(uteisImportacaoExcel.obterListaProdutosJSONExcel(linhas, getConfigProdutoTO()));
            uteisImportacaoExcel = null;

            if (UteisValidacao.emptyList(getListaProdutosJSON())) {
                throw new Exception("Nenhum produto para ser importado.");
            }

            setOnComplete("Richfaces.showModalPanel('modalConfirmarImportacao');");
        } catch (Exception e) {
            if (e instanceof OfficeXmlFileException) {
                montarErro("Sua planilha precisa estar no formato .XLSX");
            } else {
                montarErro(e);
            }
        }
    }

    private void validaPreparaConfiguracaoImportacao(ConfigExcelImportacaoTO configuracaoTO,
                                                     File arquivo, TipoImportacaoEnum tipoImportacaoEnum) throws Exception {
        Thread threadImportacao = obterThreadImportacaoEmAndamento();
        if (threadImportacao != null) {
            throw new Exception("Já existe uma importação em andamento, por favor aguarde até que ela seja finalizada.");
        }

        if (arquivo == null) {
            throw new Exception("Faça o upload modelo da planilha para realizar a importação.");
        }

        configuracaoTO.setChave(getKey());
        configuracaoTO.setUsuarioResponsavelImportacao(getUsuarioLogado());
        configuracaoTO.setEmpresaVO(getEmpresaLogado());

        if (tipoImportacaoEnum.equals(TipoImportacaoEnum.CONTRATO)) {
            getConfigClienteTO().validarDados(TipoImportacaoEnum.CONTRATO);
        }

        if (!tipoImportacaoEnum.equals(TipoImportacaoEnum.ALUNOS_TURMAS)
                && !tipoImportacaoEnum.equals(TipoImportacaoEnum.TURMAS)) {
            configuracaoTO.validarDados(tipoImportacaoEnum);
        }
    }

    public void iniciarImportacao() {
        try {
            limparMsg();
            setOnComplete("");

            if (!UteisValidacao.emptyString(getEmailAdicionar())) {
                adicionarEmail();
            }

            if (UteisValidacao.emptyList(getListaEmails())) {
                throw new Exception("Informe um email para receber o resultado da importação.");
            }

            if (getAbaAtual().equalsIgnoreCase("abaCliente")) {
                getFacade().getProcessoImportacao().iniciarThreadClientesContratos(TipoImportacaoEnum.CLIENTE, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaClientesJSON(), getListaContratosJSON(), getConfigClienteTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaContrato")) {
                getFacade().getProcessoImportacao().iniciarThreadClientesContratos(TipoImportacaoEnum.CONTRATO, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), null, getListaContratosJSON(), getConfigClienteTO());

            } else if (getAbaAtual().equalsIgnoreCase("abaColaborador")) {
                getFacade().getProcessoImportacao().iniciarThreadColaborador(TipoImportacaoEnum.COLABORADOR, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaColaboradorJSON(), getConfigColaboradorTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaFornecedor")) {
                getFacade().getProcessoImportacao().iniciarThreadFornecedor(TipoImportacaoEnum.FORNECEDOR, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaFornecedorJSON(), getConfigFornecedorTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaConta")) {
                getFacade().getProcessoImportacao().iniciarThreadContasFinanceiro(TipoImportacaoEnum.CONTA_FINANCEIRO, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaContasJSON(), getConfigContaTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaProduto")) {
                getFacade().getProcessoImportacao().iniciarThreadProdutos(TipoImportacaoEnum.PRODUTO, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaProdutosJSON(), getConfigProdutoTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaImportacaoAlunoTurma")) {
                getFacade().getProcessoImportacao().iniciarThreadAlunoTurma(TipoImportacaoEnum.ALUNOS_TURMAS, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaAlunoTurmaJSON(), getConfigAlunoTurmaTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaImportacaoTurma")) {
                getFacade().getProcessoImportacao().iniciarThreadTurma(TipoImportacaoEnum.TURMAS, getKey(),
                        getUsuarioLogado().getCodigo(), getListaEmails(), getListaTurmaJSON(), getConfigTurmaTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaParcelasPagamentos")) {
                getFacade().getProcessoImportacao().iniciarThreadParcelasPagamentos(TipoImportacaoEnum.PARCELAS_PAGAMENTOS, getKey(),
                        getUsuario().getCodigo(), getListaEmails(), getListaParcelasPagamentosJSON(), getConfigParcelasPagamentosTO());
            } else if (getAbaAtual().equalsIgnoreCase("abaTreino")) {
                if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividades")) {
                    getFacade().getProcessoImportacao().iniciarThreadTreinoAtividades(TipoImportacaoEnum.TREINO_ATIVIDADES, getKey(),
                            getUsuario().getCodigo(), getListaEmails(), getListaTreinoAtividadesJSON(), getConfigParcelasPagamentosTO());
                } else if (getAbaTreinoAtual().equalsIgnoreCase("abaProgramas")) {
                    getFacade().getProcessoImportacao().iniciarThreadTreinoProgramas(TipoImportacaoEnum.TREINO_PROGRAMAS, getKey(),
                            getUsuario().getCodigo(), getListaEmails(), getListaTreinoProgramasJSON(), getConfigParcelasPagamentosTO());
                } else if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividadeFicha")) {
                    getFacade().getProcessoImportacao().iniciarThreadTreinoAtividadeFicha(TipoImportacaoEnum.TREINO_ATIVIDADE_FICHA, getKey(),
                            getUsuario().getCodigo(), getListaEmails(), getListaTreinoAtividadeFichaJSON(), getConfigParcelasPagamentosTO());
                }
            }

            removerArquivo();
            setOnComplete("Richfaces.hideModalPanel('modalConfirmarImportacao');");
            montarSucessoGrowl("A importação foi iniciada, acompanhe o processo na aba \"Histórico\".");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public List<SelectItem> getListaSelectItemMascaraData() {
        return MascaraDataEnum.obterListaSelectItem(true);
    }

    public List<SelectItem> getListaSelectItemTipoOperacaoIntegracaoMembers() {
        List<SelectItem> lista = new ArrayList<>();
        lista.add(new SelectItem(TipoOperacaoIntegracaoMembersEnum.MEMBERS.getId(), TipoOperacaoIntegracaoMembersEnum.MEMBERS.getNome()));
        lista.add(new SelectItem(TipoOperacaoIntegracaoMembersEnum.RECEBIMENTOS_PARCELAS.getId(), TipoOperacaoIntegracaoMembersEnum.RECEBIMENTOS_PARCELAS.getNome()));
        return lista;
    }

    public String getEmailAdicionar() {
        if (emailAdicionar == null) {
            emailAdicionar = "";
        }
        return emailAdicionar;
    }

    public void setEmailAdicionar(String emailAdicionar) {
        this.emailAdicionar = emailAdicionar;
    }

    public List<SelectItem> getListaPlano() {
        if (listaPlano == null) {
            listaPlano = new ArrayList<>();
        }
        return listaPlano;
    }

    public void setListaPlano(List<SelectItem> listaPlano) {
        this.listaPlano = listaPlano;
    }

    public List<SelectItem> getListaModalidade() {
        if (listaModalidade == null) {
            listaModalidade = new ArrayList<>();
        }
        return listaModalidade;
    }

    public void setListaModalidade(List<SelectItem> listaModalidade) {
        this.listaModalidade = listaModalidade;
    }

    public List<SelectItem> getListaConsultor() {
        if (listaConsultor == null) {
            listaConsultor = new ArrayList<>();
        }
        return listaConsultor;
    }

    public void setListaConsultor(List<SelectItem> listaConsultor) {
        this.listaConsultor = listaConsultor;
    }

    public void removerEmail() {
        try {
            limparMsg();
            List<String> listaAtual = new ArrayList<>(getListaEmails());
            String emailRemover = (String) context().getExternalContext().getRequestMap().get("email");
            Iterator i = listaAtual.iterator();
            while (i.hasNext()) {
                String email = (String) i.next();
                if (email.hashCode() == emailRemover.hashCode()) {
                    getListaEmails().remove(email);
                }
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public void adicionarEmail() {
        try {
            limparMsg();
            setOnComplete("");
            if (UteisEmail.getValidEmail(getEmailAdicionar())) {
                boolean existe = false;
                for (String email : getListaEmails()) {
                    if (email.equalsIgnoreCase(getEmailAdicionar())) {
                        existe = true;
                    }
                }
                if (!existe) {
                    getListaEmails().add(getEmailAdicionar());
                }
                setEmailAdicionar("");
            } else {
                throw new Exception("O email não é válido.");
            }
        } catch (Exception ex) {
            montarErro(ex.getMessage());
        }
    }

    public void montarComboPlano() {
        try {
            setOnComplete("");
            setListaPlano(new ArrayList<>());
            getListaPlano().add(new SelectItem(0, ""));
            List<PlanoVO> lista = getFacade().getPlano().consultarPorCodigoEmpresa(getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "codigo");
            for (PlanoVO obj : lista) {
                String descricaoComCodigo = obj.getCodigo() + " - " + obj.getDescricao();
                getListaPlano().add(new SelectItem(obj.getCodigo(), descricaoComCodigo));

                //selecionar plano importação
                if (obj.getDescricao().toUpperCase().contains("IMPORTACAO") ||
                        obj.getDescricao().toUpperCase().contains("IMPORTAÇÃO")) {
                    getConfigClienteTO().setPlano(obj.getCodigo());
                }
            }
        } catch (Exception e) {
            setListaPlano(new ArrayList<>());
        }
    }

    public void montarComboConsultor() {
        try {
            setOnComplete("");
            setListaConsultor(new ArrayList<>());
            getListaConsultor().add(new SelectItem(0, ""));
            List<ColaboradorVO> lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "pessoa_Apresentar");
            for (ColaboradorVO obj : lista) {
                getListaConsultor().add(new SelectItem(obj.getCodigo(), obj.getPessoa_Apresentar()));
            }
        } catch (Exception e) {
            setListaConsultor(new ArrayList<>());
        }
    }

    public void montarComboProfessorTreinoWeb() {
        try {
            setOnComplete("");
            setListaProfessorTreinoWeb(new ArrayList<>());
            getListaProfessorTreinoWeb().add(new SelectItem(0, ""));
            List<ColaboradorVO> lista = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.PROFESSOR_TREINO, "AT", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "pessoa_Apresentar");
            for (ColaboradorVO obj : lista) {
                getListaProfessorTreinoWeb().add(new SelectItem(obj.getCodigo(), obj.getPessoa_Apresentar()));
            }
        } catch (Exception e) {
            setListaProfessorTreinoWeb(new ArrayList<>());
        }
    }

    public void montarComboModalidade() {
        try {
            setOnComplete("");
            setListaModalidade(new ArrayList<>());
            getListaModalidade().add(new SelectItem(0, ""));
            List<ModalidadeVO> lista = getFacade().getModalidade().consultarPorNome("", getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "codigo");
            for (ModalidadeVO obj : lista) {
                if (!obj.isUtilizarTurma()) {
                    String descricaoComCodigo = obj.getCodigo() + " - " + obj.getNome();
                    getListaModalidade().add(new SelectItem(obj.getCodigo(), descricaoComCodigo));
                }
            }
        } catch (Exception e) {
            setListaModalidade(new ArrayList<>());
        }
    }

    public void montarComboHorario() {
        try {
            setOnComplete("");
            setListaHorarios(new ArrayList<>());
            getListaHorarios().add(new SelectItem(0, ""));
            List<HorarioVO> lista = getFacade().getHorario().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "codigo");
            for (HorarioVO obj : lista) {
                if (obj.getDescricao().contains("LIVRE")) {
                    getConfigClienteTO().setHorario(obj.getCodigo());
                }
                String descricaoComCodigo = obj.getCodigo() + " - " + obj.getDescricao();
                getListaHorarios().add(new SelectItem(obj.getCodigo(), descricaoComCodigo));
            }
        } catch (Exception e) {
            setListaHorarios(new ArrayList<>());
        }
    }

    private Thread obterThreadImportacaoEmAndamento() {
        String threadName = "IMPORTACAO_" + getKey();
        for (Thread t : Thread.getAllStackTraces().keySet()) {
            if (t.getName().toUpperCase().startsWith(threadName.toUpperCase())) {
                return t;
            }
        }
        return null;
    }

    public void consultarImportacoes() {
        try {
            limparMsg();
            setOnComplete("");
            setHistorico(new ArrayList<>());
            setHistorico(getFacade().getProcessoImportacao().consultarTodos(0, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public boolean isExisteImportacaoEmAndamento() {
        return obterThreadImportacaoEmAndamento() != null;
    }


    public void visualizarLogImportacao() {
        try {
            limparMsg();
            setOnComplete("");
            setLogImportacao(new ArrayList<>());
            ProcessoImportacaoVO processoVO = (ProcessoImportacaoVO) context().getExternalContext().getRequestMap().get("item");
            setLogImportacao(getFacade().getProcessoImportacaoLog().consultarPorProcessoImportacao(processoVO.getCodigo(), 0, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setOnComplete("Richfaces.showModalPanel('modalImportacaoLog');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void visualizarDetalhesImportacao() {
        try {
            limparMsg();
            setOnComplete("");
            setItensImportacao(new ArrayList<>());
            ProcessoImportacaoVO processoVO = (ProcessoImportacaoVO) context().getExternalContext().getRequestMap().get("item");
            setItensImportacao(getFacade().getProcessoImportacaoItem().consultarPorProcessoImportacao(processoVO.getCodigo(), 0, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setOnComplete("Richfaces.showModalPanel('modalImportacaoDetalhe');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void parar() throws Exception {
        limparMsg();
        setOnComplete("");
        try {
            Thread threadImportacao = obterThreadImportacaoEmAndamento();
            if (threadImportacao == null) {
                throw new Exception("Não existe nenhum processo em andamento.");
            }
            if (threadImportacao.isInterrupted()) {
                throw new Exception("Já existe uma solicitação de parada, aguarde alguns instantes...");
            }

            threadImportacao.interrupt();

            montarSucessoGrowl("O processo será parado em alguns instantes...");
        } catch (Exception e) {
            montarErro(e);
        }
    }

    public Integer getLogImportacaoSize() {
        return getLogImportacao().size();
    }

    public List<ProcessoImportacaoLogVO> getLogImportacao() {
        if (logImportacao == null) {
            logImportacao = new ArrayList<>();
        }
        return logImportacao;
    }

    public void setLogImportacao(List<ProcessoImportacaoLogVO> logImportacao) {
        this.logImportacao = logImportacao;
    }

    public String getOnComplete() {
        if (onComplete == null) {
            onComplete = "";
        }
        return onComplete;
    }

    public void setOnComplete(String onComplete) {
        this.onComplete = onComplete;
    }

    public Integer getHistoricoSize() {
        return getHistorico().size();
    }

    public List<ProcessoImportacaoVO> getHistorico() {
        if (historico == null) {
            historico = new ArrayList<>();
        }
        return historico;
    }

    public void setHistorico(List<ProcessoImportacaoVO> historico) {
        this.historico = historico;
    }

    public Integer getItensImportacaoSize() {
        return getItensImportacao().size();
    }

    public List<ProcessoImportacaoItemVO> getItensImportacao() {
        if (itensImportacao == null) {
            itensImportacao = new ArrayList<>();
        }
        return itensImportacao;
    }

    public void setItensImportacao(List<ProcessoImportacaoItemVO> itensImportacao) {
        this.itensImportacao = itensImportacao;
    }

    public int getScrollerPage() {
        return scrollerPage;
    }

    public void setScrollerPage(int scrollerPage) {
        this.scrollerPage = scrollerPage;
    }

    public List<ParcelaPagamentoJSON> getListaParcelasPagamentosJSON() {
        return listaParcelasPagamentosJSON;
    }

    public void setListaParcelasPagamentosJSON(List<ParcelaPagamentoJSON> listaParcelasPagamentosJSON) {
        this.listaParcelasPagamentosJSON = listaParcelasPagamentosJSON;
    }

    public List<ClienteImportacaoJSON> getListaClientesJSON() {
        if (listaClientesJSON == null) {
            listaClientesJSON = new ArrayList<>();
        }
        return listaClientesJSON;
    }

    public void setListaClientesJSON(List<ClienteImportacaoJSON> listaClientesJSON) {
        this.listaClientesJSON = listaClientesJSON;
    }

    public List<SelectItem> getListaProfessorTreinoWeb() {
        if (listaProfessorTreinoWeb == null) {
            listaProfessorTreinoWeb = new ArrayList<>();
        }
        return listaProfessorTreinoWeb;
    }

    public void setListaProfessorTreinoWeb(List<SelectItem> listaProfessorTreinoWeb) {
        this.listaProfessorTreinoWeb = listaProfessorTreinoWeb;
    }

    public List<SelectItem> getListaHorarios() {
        if (listaHorarios == null) {
            listaHorarios = new ArrayList<>();
        }
        return listaHorarios;
    }

    public void setListaHorarios(List<SelectItem> listaHorarios) {
        this.listaHorarios = listaHorarios;
    }

    public List<ContratoImportacaoJSON> getListaContratosJSON() {
        if (listaContratosJSON == null) {
            listaContratosJSON = new ArrayList<>();
        }
        return listaContratosJSON;
    }

    public void setListaContratosJSON(List<ContratoImportacaoJSON> listaContratosJSON) {
        this.listaContratosJSON = listaContratosJSON;
    }

    public List<SelectItem> getListaImportacaoParcelasSituacao() {
        return ImportacaoParcelasSituacaoEnum.obterListaSelectItem(true, false);
    }

    public List<SelectItem> getListaCategoria() {
        if (listaCategoria == null) {
            listaCategoria = new ArrayList<>();
        }
        return listaCategoria;
    }

    public void setListaCategoria(List<SelectItem> listaCategoria) {
        this.listaCategoria = listaCategoria;
    }

    public List<SelectItem> getListaSelectItemTipoProduto() {
        List<SelectItem> objs = new ArrayList<>();
        objs.add(new SelectItem("", ""));
        Hashtable tipoProdutos = (Hashtable) Dominios.getTipoProduto();
        Enumeration keys = tipoProdutos.keys();
        while (keys.hasMoreElements()) {
            String value = (String) keys.nextElement();
            String label = (String) tipoProdutos.get(value);
            if (!value.equals("AA")) {
                objs.add(new SelectItem(value, label));
            }
        }
        SelectItemOrdemValor ordenador = new SelectItemOrdemValor();
        Collections.sort((List) objs, ordenador);
        return objs;
    }

    public void montarComboCategoria() {
        try {
            setOnComplete("");
            setListaCategoria(new ArrayList<>());
            getListaCategoria().add(new SelectItem(0, ""));
            List<CategoriaProdutoVO> lista = getFacade().getCategoriaProduto().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Ordenacao.ordenarLista(lista, "descricao");
            for (CategoriaProdutoVO obj : lista) {
                getListaCategoria().add(new SelectItem(obj.getCodigo(), obj.getDescricao()));
            }
        } catch (Exception e) {
            setListaCategoria(new ArrayList<>());
        }
    }

    public List<ProdutoImportacaoJSON> getListaProdutosJSON() {
        if (listaProdutosJSON == null) {
            listaProdutosJSON = new ArrayList<>();
        }
        return listaProdutosJSON;
    }

    public void setListaProdutosJSON(List<ProdutoImportacaoJSON> listaProdutosJSON) {
        this.listaProdutosJSON = listaProdutosJSON;
    }

    public List<ContaImportacaoJSON> getListaContasJSON() {
        if (listaContasJSON == null) {
            listaContasJSON = new ArrayList<>();
        }
        return listaContasJSON;
    }

    public void setListaContasJSON(List<ContaImportacaoJSON> listaContasJSON) {
        this.listaContasJSON = listaContasJSON;
    }

    public ConfigExcelImportacaoTO getConfigParcelasPagamentosTO() {
        if (configParcelasPagamentosTO == null) {
            configParcelasPagamentosTO = new ConfigExcelImportacaoTO();
        }
        return configParcelasPagamentosTO;
    }

    public void setConfigParcelasPagamentosTO(ConfigExcelImportacaoTO configParcelasPagamentosTO) {
        this.configParcelasPagamentosTO = configParcelasPagamentosTO;
    }

    public ConfigExcelImportacaoTO getConfigClienteTO() {
        if (configClienteTO == null) {
            configClienteTO = new ConfigExcelImportacaoTO();
        }
        return configClienteTO;
    }

    public void setConfigClienteTO(ConfigExcelImportacaoTO configClienteTO) {
        this.configClienteTO = configClienteTO;
    }

    public ConfigExcelImportacaoTO getConfigProdutoTO() {
        if (configProdutoTO == null) {
            configProdutoTO = new ConfigExcelImportacaoTO();
        }
        return configProdutoTO;
    }

    public void setConfigProdutoTO(ConfigExcelImportacaoTO configProdutoTO) {
        this.configProdutoTO = configProdutoTO;
    }

    public ConfigExcelImportacaoTO getConfigContaTO() {
        if (configContaTO == null) {
            configContaTO = new ConfigExcelImportacaoTO();
        }
        return configContaTO;
    }

    public void setConfigContaTO(ConfigExcelImportacaoTO configContaTO) {
        this.configContaTO = configContaTO;
    }

    public ConfigExcelImportacaoTO getConfigFornecedorTO() {
        if (configFornecedorTO == null) {
            configFornecedorTO = new ConfigExcelImportacaoTO();
        }
        return configFornecedorTO;
    }

    public void setConfigFornecedorTO(ConfigExcelImportacaoTO configFornecedorTO) {
        this.configFornecedorTO = configFornecedorTO;
    }

    public File getArquivoParcelasPagamentos() {
        return arquivoParcelasPagamentos;
    }

    public void setArquivoParcelasPagamentos(File arquivoParcelasPagamentos) {
        this.arquivoParcelasPagamentos = arquivoParcelasPagamentos;
    }

    public File getArquivoCliente() {
        return arquivoCliente;
    }

    public void setArquivoCliente(File arquivoCliente) {
        this.arquivoCliente = arquivoCliente;
    }

    public File getArquivoContrato() {
        return arquivoContrato;
    }

    public void setArquivoContrato(File arquivoContrato) {
        this.arquivoContrato = arquivoContrato;
    }

    public File getArquivoFornecedor() {
        return arquivoFornecedor;
    }

    public void setArquivoFornecedor(File arquivoFornecedor) {
        this.arquivoFornecedor = arquivoFornecedor;
    }

    public List<FornecedorImportacaoJSON> getListaFornecedorJSON() {
        if (listaFornecedorJSON == null) {
            listaFornecedorJSON = new ArrayList<>();
        }
        return listaFornecedorJSON;
    }

    public void setListaFornecedorJSON(List<FornecedorImportacaoJSON> listaFornecedorJSON) {
        this.listaFornecedorJSON = listaFornecedorJSON;
    }

    public File getArquivoProduto() {
        return arquivoProduto;
    }

    public File getArquivoAlunoTurma() {
        return arquivoAlunoTurma;
    }

    public void setArquivoProduto(File arquivoProduto) {
        this.arquivoProduto = arquivoProduto;
    }

    public File getArquivoTurma() {
        return arquivoTurma;
    }

    public void setArquivoTurma(File arquivoTurma) {
        this.arquivoTurma = arquivoTurma;
    }

    public ConfigExcelImportacaoTO getConfigTurmaTO() {
        return configTurmaTO;
    }

    public void setConfigTurmaTO(ConfigExcelImportacaoTO configTurmaTO) {
        this.configTurmaTO = configTurmaTO;
    }

    public List<TurmaImportacaoJSON> getListaTurmaJSON() {
        return listaTurmaJSON;
    }

    public void setListaTurmaJSON(List<TurmaImportacaoJSON> listaTurmaJSON) {
        this.listaTurmaJSON = listaTurmaJSON;
    }

    public File getArquivoConta() {
        return arquivoConta;
    }

    public void setArquivoAlunoTurma(File arquivoAlunoTurma) {
        this.arquivoAlunoTurma = arquivoAlunoTurma;
    }

    public void setArquivoConta(File arquivoConta) {
        this.arquivoConta = arquivoConta;
    }

    public File getArquivoColaborador() {
        return arquivoColaborador;
    }

    public void setArquivoColaborador(File arquivoColaborador) {
        this.arquivoColaborador = arquivoColaborador;
    }

    public List<ColaboradorImportacaoJSON> getListaColaboradorJSON() {
        if (listaColaboradorJSON == null) {
            listaColaboradorJSON = new ArrayList<>();
        }
        return listaColaboradorJSON;
    }

    public void setListaColaboradorJSON(List<ColaboradorImportacaoJSON> listaColaboradorJSON) {
        this.listaColaboradorJSON = listaColaboradorJSON;
    }

    public ConfigExcelImportacaoTO getConfigColaboradorTO() {
        if (configColaboradorTO == null) {
            configColaboradorTO = new ConfigExcelImportacaoTO();
        }
        return configColaboradorTO;
    }

    public void setConfigColaboradorTO(ConfigExcelImportacaoTO configColaboradorTO) {
        this.configColaboradorTO = configColaboradorTO;
    }

    public List<String> getListaEmails() {
        if (listaEmails == null) {
            listaEmails = new ArrayList<>();
        }
        return listaEmails;
    }

    public void setListaEmails(List<String> listaEmails) {
        this.listaEmails = listaEmails;
    }

    public String getAbaAtual() {
        return abaAtual;
    }

    public void setAbaAtual(String abaAtual) {
        this.abaAtual = abaAtual;
    }

    public ConfigExcelImportacaoTO getConfigAlunoTurmaTO() {
        return configAlunoTurmaTO;
    }

    public void setConfigAlunoTurmaTO(ConfigExcelImportacaoTO configAlunoTurmaTO) {
        this.configAlunoTurmaTO = configAlunoTurmaTO;
    }

    public List<AlunoTurmaImportacaoJSON> getListaAlunoTurmaJSON() {
        return listaAlunoTurmaJSON;
    }

    public void setListaAlunoTurmaJSON(List<AlunoTurmaImportacaoJSON> listaAlunoTurmaJSON) {
        this.listaAlunoTurmaJSON = listaAlunoTurmaJSON;
    }

    public List<ItemRelatorioTO> getListaTotalImportacao() {
        List<ItemRelatorioTO> lista = new ArrayList<>();

        if (getAbaAtual().equalsIgnoreCase("abaCliente")) {

            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Clientes");
            item.setQuantidade(String.valueOf(getListaClientesJSON().size()));
            lista.add(item);

            if (getConfigClienteTO().isImportarContratos()) {
                ItemRelatorioTO item1 = new ItemRelatorioTO();
                item1.setNome("Contratos");
                item1.setQuantidade(String.valueOf(getListaContratosJSON().size()));
                lista.add(item1);
            }

        } else if (getAbaAtual().equalsIgnoreCase("abaContrato")) {
            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Contratos");
            item.setQuantidade(String.valueOf(getListaContratosJSON().size()));
            lista.add(item);

        } else if (getAbaAtual().equalsIgnoreCase("abaParcelasPagamentos")) {
            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Parcelas e Pagamentos");
            item.setQuantidade(String.valueOf(getListaParcelasPagamentosJSON().size()));
            lista.add(item);


        } else if (getAbaAtual().equalsIgnoreCase("abaColaborador")) {

            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Colaboradores");
            item.setQuantidade(String.valueOf(getListaColaboradorJSON().size()));
            lista.add(item);

        } else if (getAbaAtual().equalsIgnoreCase("abaFornecedor")) {

            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Fornecedores");
            item.setQuantidade(String.valueOf(getListaFornecedorJSON().size()));
            lista.add(item);

        } else if (getAbaAtual().equalsIgnoreCase("abaConta")) {

            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Contas do Financeiro");
            item.setQuantidade(String.valueOf(getListaContasJSON().size()));
            lista.add(item);

        } else if (getAbaAtual().equalsIgnoreCase("abaProduto")) {

            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Produtos");
            item.setQuantidade(String.valueOf(getListaProdutosJSON().size()));
            lista.add(item);

        }else if (getAbaAtual().equalsIgnoreCase("abaImportacaoTurma")) {

            ItemRelatorioTO item = new ItemRelatorioTO();
            item.setNome("Turma");
            item.setQuantidade(String.valueOf(getListaTurmaJSON().size()));
            lista.add(item);

        }
        return lista;
    }

    public String getNomeAbaAtual() {
        if (getAbaAtual().equalsIgnoreCase("abaCliente")) {
            return "Clientes";
        } else if (getAbaAtual().equalsIgnoreCase("abaContrato")) {
          return "Contratos";
        } else if (getAbaAtual().equalsIgnoreCase("abaParcelasPagamentos")) {
            return "Parcelas e Pagamentos";
        } else if (getAbaAtual().equalsIgnoreCase("abaColaborador")) {
            return "Colaboradores";
        } else if (getAbaAtual().equalsIgnoreCase("abaFornecedor")) {
            return "Fornecedores";
        } else if (getAbaAtual().equalsIgnoreCase("abaConta")) {
            return "Contas do Financeiro";
        } else if (getAbaAtual().equalsIgnoreCase("abaProduto")) {
            return "Produtos";
        } else if (getAbaAtual().equalsIgnoreCase("abaImportacaoTurma")) {
            return "Turmas";
        } else if (getAbaAtual().equalsIgnoreCase("abaTreino")) {
            if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividades")) {
                return "Atividades";
            } else if (getAbaTreinoAtual().equalsIgnoreCase("abaProgramas")) {
                return "Programas";
            } else if (getAbaTreinoAtual().equalsIgnoreCase("abaAtividadeFicha")) {
                return "AtividadeFicha/Series";
            } else {
                return "";
            }
        } else {
            return "";
        }
    }

    public void excluirItemImportacao() {
        try {
            limparMsg();
            setOnComplete("");
            setItensImportacao(new ArrayList<>());
            ProcessoImportacaoVO processoVO = (ProcessoImportacaoVO) context().getExternalContext().getRequestMap().get("item");
            setItensImportacao(getFacade().getProcessoImportacaoItem().consultarPorProcessoImportacao(processoVO.getCodigo(), 0, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setOnComplete("Richfaces.showModalPanel('modalImportacaoDetalhe');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public void excluirTodosImportacao() {
        try {
            limparMsg();
            setOnComplete("");
            setItensImportacao(new ArrayList<>());
            ProcessoImportacaoVO processoVO = (ProcessoImportacaoVO) context().getExternalContext().getRequestMap().get("item");
            setItensImportacao(getFacade().getProcessoImportacaoItem().consultarPorProcessoImportacao(processoVO.getCodigo(), 0, 0, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            setOnComplete("Richfaces.showModalPanel('modalImportacaoDetalhe');");
        } catch (Exception ex) {
            montarErro(ex);
        }
    }

    public IntegracaoMemberVO getIntegracaoMember() {
        return integracaoMember;
    }

    public void setIntegracaoMember(IntegracaoMemberVO integracaoMember) {
        this.integracaoMember = integracaoMember;
    }

    public String getIdsMembers() {
        return idsMembers;
    }

    public void setIdsMembers(String idsMembers) {
        this.idsMembers = idsMembers;
    }

    public File getArquivoCorrespondencia() {
        return arquivoCorrespondencia;
    }

    public void setArquivoCorrespondencia(File arquivoCorrespondencia) {
        this.arquivoCorrespondencia = arquivoCorrespondencia;
    }

    public Integer getTipoOperacaoIntegracaoMembers() {
        return tipoOperacaoIntegracaoMembers;
    }

    public void setTipoOperacaoIntegracaoMembers(Integer tipoOperacaoIntegracaoMembers) {
        this.tipoOperacaoIntegracaoMembers = tipoOperacaoIntegracaoMembers;
    }

    public void gerarPlanilhaPlanosParaCorrespondencia() throws Exception {
        IntegracaoMemberService integracaoMemberService = new IntegracaoMemberService();

        List<MemberShipApivViewTO> memberShips = integracaoMemberService.obterMemberShips(integracaoMember);

        JSONArray jsonArray = new JSONArray();
        memberShips.forEach(m -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("Codigo", m.getIdMembership());
            jsonObject.put("Descricao", m.getNameMembership());
            jsonObject.put("CodigoPlanoPacto", "");
            jsonArray.put(jsonObject);
        });

        ExportadorExcel exportadorExcel = new ExportadorExcel();
        String colunas = "Codigo,Descricao,CodigoPlanoPacto";
        File file = exportadorExcel.gerarArquivoExcel("planos-correspondencia", jsonArray, DAO.resolveKeyFromConnection(Conexao.getFromSession()), colunas);

        setOnComplete("location.href='../DownloadSV?mimeType=application/vnd.ms-excel&relatorio=" + file.getName() + "'");
    }


    public void consultarConfiguracaoSesc() {
        try {
            ConfiguracaoSistema configuracaoSistemaDao = new ConfiguracaoSistema(Conexao.getFromSession());
            this.configSesc = configuracaoSistemaDao.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS).getSesc();
        } catch (Exception e) {
            this.configSesc = false;
            e.printStackTrace();
        }

    }

    public boolean getConfigSesc() {
        return configSesc;
    }

    public String getAbaTreinoAtual() {
        return abaTreinoAtual;
    }

    public void setAbaTreinoAtual(String abaTreinoAtual) {
        this.abaTreinoAtual = abaTreinoAtual;
    }

    public List<TreinoAtividadeJSON> getListaTreinoAtividadesJSON() {
        return listaTreinoAtividadesJSON;
    }

    public void setListaTreinoAtividadesJSON(List<TreinoAtividadeJSON> listaTreinoAtividadesJSON) {
        this.listaTreinoAtividadesJSON = listaTreinoAtividadesJSON;
    }

    public List<ProgramaFichaJSON> getListaTreinoProgramasJSON() {
        return listaTreinoProgramasJSON;
    }

    public void setListaTreinoProgramasJSON(List<ProgramaFichaJSON> listaTreinoProgramasJSON) {
        this.listaTreinoProgramasJSON = listaTreinoProgramasJSON;
    }

    public List<AtividadeFichaJSON> getListaTreinoAtividadeFichaJSON() {
        return listaTreinoAtividadeFichaJSON;
    }

    public void setListaTreinoAtividadeFichaJSON(List<AtividadeFichaJSON> listaTreinoAtividadeFichaJSON) {
        this.listaTreinoAtividadeFichaJSON = listaTreinoAtividadeFichaJSON;
    }
}
