package controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.security.LoginControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.facade.jdbc.basico.Parentesco;
import negocio.comuns.utilitarias.*;
import negocio.comuns.basico.*;
import javax.faces.model.SelectItem;
import java.util.List;
import java.util.ArrayList;
import controle.arquitetura.SuperControle;
import javax.faces.event.ActionEvent;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das páginas 
 * parentescoForm.jsp parentescoCons.jsp) com as funcionalidades da classe <code>Parentesco</code>.
 * Implemtação da camada controle (Backing Bean).
 * @see SuperControle
 * @see Parentesco
 * @see ParentescoVO
 */
public class ParentescoControle extends SuperControle {

    private ParentescoVO parentescoVO;
    private String msgAlert;

    /**
     * Interface <code>ParentescoInterfaceFacade</code> responsável pela interconexão da camada de controle com a camada de negócio.
     * Criando uma independência da camada de controle com relação a tenologia de persistência dos dados (DesignPatter: Façade).
     */

    public ParentescoControle() throws Exception {
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        setMensagemID("");
    }

    /**
     * Rotina responsável por disponibilizar um novo objeto da classe <code>Parentesco</code>
     * para edição pelo usuário da aplicação.
     */
    public String novo() {
        setParentescoVO(new ParentescoVO());
        limparMsg();
        return "editar";
    }

    /**
     * Rotina responsável por disponibilizar os dados de um objeto da classe <code>Parentesco</code> para alteração.
     * O objeto desta classe é disponibilizado na session da página (request) para que o JSP correspondente possa disponibilizá-lo para edição.
     */
    public String editar() {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            ParentescoVO obj = getFacade().getParentesco().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            obj.setNovoObj(new Boolean(false));
            obj.registrarObjetoVOAntesDaAlteracao();
            setParentescoVO(new ParentescoVO());
            setParentescoVO(obj);
        } catch (Exception e) {
            setErro(true);
            setSucesso(false);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
        return "editar";
    }

    /**
     * Rotina responsável por gravar no BD os dados editados de um novo objeto da classe <code>Parentesco</code>.
     * Caso o objeto seja novo (ainda não gravado no BD) é acionado a operação <code>incluir()</code>. Caso contrário é acionado o <code>alterar()</code>.
     * Se houver alguma inconsistência o objeto não é gravado, sendo re-apresentado para o usuário juntamente com uma mensagem de erro.
     */
    public String gravar() {
        try {
            if (parentescoVO.isNovoObj().booleanValue()) {
                getFacade().getParentesco().incluir(parentescoVO);
                incluirLogInclusao();
            } else {
                getFacade().getParentesco().alterar(parentescoVO);
                incluirLogAlteracao();
            }
            setMensagemID("msg_dados_gravados");
            setSucesso(true);
            setErro(false);
            return "editar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

      public void incluirLogInclusao() throws Exception {
        //LOG - INICIO
        try {
            parentescoVO.setObjetoVOAntesAlteracao(new ParentescoVO());
            parentescoVO.setNovoObj(true);
            registrarLogObjetoVO(parentescoVO, parentescoVO.getCodigo(), "PARENTESCO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PARENTESCO", parentescoVO.getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO DE PARENTESCO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        parentescoVO.setNovoObj(false);
        parentescoVO.registrarObjetoVOAntesDaAlteracao();
    }
      
    public void incluirLogExclusao() throws Exception {
        //LOG - INICIO
        try {
            parentescoVO.setObjetoVOAntesAlteracao(new ParentescoVO());
            parentescoVO.setNovoObj(true);
            registrarLogExclusaoTodosDadosObjetoVO(parentescoVO, parentescoVO.getCodigo(), "PARENTESCO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PARENTESCO", parentescoVO.getCodigo(), "ERRO AO GERAR LOG DE EXCLUSÃO DE PARENTESCO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
    }

    public void incluirLogAlteracao() throws Exception {
        //LOG - INICIO
        try {
            registrarLogObjetoVO(parentescoVO, parentescoVO.getCodigo(), "PARENTESCO", 0);
        } catch (Exception e) {
            registrarLogErroObjetoVO("PARENTESCO", parentescoVO.getCodigo(), "ERRO AO GERAR LOG DE ALTERAÇÃO DE PARENTESCO", this.getUsuarioLogado().getNome(),this.getUsuarioLogado().getUserOamd());
            e.printStackTrace();
        }
        parentescoVO.registrarObjetoVOAntesDaAlteracao();
        //LOG - FIM
    }
    /**
     * Rotina responsavel por executar as consultas disponiveis no JSP ParentescoCons.jsp.
     * Define o tipo de consulta a ser executada, por meio de ComboBox denominado campoConsulta, disponivel neste mesmo JSP.
     * Como resultado, disponibiliza um List com os objetos selecionados na sessao da pagina.
     */
    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            
            //limpar espaços em branco antes de realizar a consulta
            getControleConsulta().setValorConsulta(getControleConsulta().getValorConsulta().trim());
            
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                	objs = getFacade().getParentesco().consultarPorCodigo(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
	                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
	                ParentescoVO parentesco = getFacade().getParentesco().consultarPorCodigoExato(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
	                if(parentesco != null) {
	                	objs.add(parentesco);
	                }
                }
            }
            if (getControleConsulta().getCampoConsulta().equals("descricao")) {
                objs = getFacade().getParentesco().consultarPorDescricao(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TODOS);
            }
            if (getControleConsulta().getCampoConsulta().equals("idadeLimiteDependencia")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                	objs = getFacade().getParentesco().consultarPorIdadeLimiteDependencia(new Integer(0), true, Uteis.NIVELMONTARDADOS_TODOS);
                } else {
	                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
	                ParentescoVO parentesco = getFacade().getParentesco().consultarPorIdadeLimiteDependenciaExata(new Integer(valorInt), true, Uteis.NIVELMONTARDADOS_TODOS);
	                if(parentesco != null) {
	                	objs.add(parentesco);
	                }
                }
            }
            objs = ControleConsulta.obterSubListPaginaApresentar(objs, controleConsulta);
            definirVisibilidadeLinksNavegacao(controleConsulta.getPaginaAtual(), controleConsulta.getNrTotalPaginas());
            setListaConsulta(objs);
            limparMsg();
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    /**
     * Operação responsável por processar a exclusão um objeto da classe <code>ParentescoVO</code>
     * Após a exclusão ela automaticamente aciona a rotina para uma nova inclusão.
     */
    public String excluir() {
        try {
            getFacade().getParentesco().excluir(parentescoVO);
            incluirLogExclusao();
            setParentescoVO(new ParentescoVO());
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"parentesco\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"parentesco\" violates foreign key")){
                setMensagemDetalhada("Este parentesco não pode ser excluído, pois está sendo utilizado!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }

    /**
     * Rotina responsável por preencher a combo de consulta da telas.
     */
    public List getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList();
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("descricao", "Descrição"));
        itens.add(new SelectItem("idadeLimiteDependencia", "Idade Limite Dependência"));
        return itens;
    }

    /**
     * Rotina responsável por organizar a paginação entre as páginas resultantes de uma consulta.
     */
    public String inicializarConsultar() {
        setPaginaAtualDeTodas("0/0");
        setListaConsulta(new ArrayList());
        definirVisibilidadeLinksNavegacao(0, 0);
        setMensagemID("");
        setSucesso(false);
        setErro(false);
        return "consultar";
    }

    /**
     * Operação que inicializa as Interfaces Façades com os respectivos objetos de
     * persistência dos dados no banco de dados. 
     */
    protected boolean inicializarFacades() {
        try {
            super.inicializarFacades();

            return true;
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro_conectarBD", e.getMessage());
            return false;
        }
    }

    public ParentescoVO getParentescoVO() {
        return parentescoVO;
    }

    public void setParentescoVO(ParentescoVO parentescoVO) {
        this.parentescoVO = parentescoVO;
    }

       /**
     * Consulta de logs de parentesco
     */
    @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoSelecionado() {
        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        loginControle.setNomeClasse("Parentesco");
        loginControle.consultarLogObjetoSelecionado("PARENTESCO", parentescoVO.getCodigo(), null);
    }
    
        @SuppressWarnings("unchecked")
    public void realizarConsultaLogObjetoGeral() {
        parentescoVO = new ParentescoVO();
        realizarConsultaLogObjetoSelecionado();
    }
 public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getParentesco().consultarParaImpressao(filtro, ordem, campoOrdenacao, getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo());
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);

    }

    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Parentesco",
                "Deseja excluir o Parentesco?",
                this, "excluir", "", "", "", "grupoBtnExcluir");
    }

}
