/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package controle.crm;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.faces.model.SelectItem;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 *
 * <AUTHOR>
 */
public class RelatorioContatoAppControle extends SuperControle {

    private Date inicio;
    private Date fim;
    private Integer malaDireta;
    private Integer codigoEmpresa = null;
    private List<SelectItem> listaSelectItemEmpresa;
    private List<HistoricoContatoVO> lista;
    private UsuarioVO responsavel = new UsuarioVO();
    private String filtroResposta = "";
    private String filtroMensagem = "";
    private String filtroNomePessoa = "";

    public RelatorioContatoAppControle() {
        novo();
    }

    public void limparPeriodo() {
        inicio = null;
        fim = null;
    }

    public void novo() {
        try {
            codigoEmpresa = getUsuarioLogado().getAdministrador() ? null : getEmpresaLogado().getCodigo();
            inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(Calendario.hoje()));
            fim = Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje());
            consultarContatos();
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public List<UsuarioVO> executarAutocompleteConsultaUsuario(Object suggest) {
        String pref = (String) suggest;
        ArrayList<UsuarioVO> result = new ArrayList<UsuarioVO>();
        try {
            result = (ArrayList<UsuarioVO>) getFacade().getUsuario().
                    consultarPorNomeUsuarioComLimite(pref, codigoEmpresa, false, Uteis.NIVELMONTARDADOS_ROBO);

            setMensagemID("msg_dados_consultados");
        } catch (Exception ex) {
            result = (new ArrayList<UsuarioVO>());
            setMensagemDetalhada("msg_erro", ex.getMessage());
        }
        return result;
    }

    public void selecionarUsuarioSuggestionBox() throws Exception {
        UsuarioVO usuarioVO = (UsuarioVO) request().getAttribute("result");
        if (usuarioVO != null) {
            setResponsavel(usuarioVO);
        }
    }

    public void consultarContatos() {
        try {
            setMensagemDetalhada("","");
            lista = getFacade().getHistoricoContato().relatorioContatoApp(filtroNomePessoa,filtroResposta, filtroMensagem, inicio, fim, responsavel, malaDireta, codigoEmpresa);
            if (lista.isEmpty()) {
                setMensagemDetalhada("msg_erro", "Nenhum registro foi encontrado! Altere suas opções de filtro e tente novamente.");
                setAtencao(true);
            }
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Integer getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(Integer codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public List<SelectItem> getListaSelectItemEmpresa() throws Exception {
        if (listaSelectItemEmpresa == null) {
            montarListaSelectItemEmpresa();
        }
        return listaSelectItemEmpresa;
    }

    /**
     * @param listaSelectItemEmpresa the listaSelectItemEmpresa to set
     */
    public void setListaSelectItemEmpresa(List<SelectItem> listaSelectItemEmpresa) {
        this.listaSelectItemEmpresa = listaSelectItemEmpresa;
    }

    public void montarListaSelectItemEmpresa() throws Exception {
        this.setListaSelectItemEmpresa(new ArrayList<SelectItem>());
        List<EmpresaVO> empresas = getFacade().getEmpresa().consultarPorNome("", true, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (EmpresaVO empresa : empresas) {
            this.getListaSelectItemEmpresa().add(new SelectItem(empresa.getCodigo(), empresa.getNome()));
        }
    }

    public UsuarioVO getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
    }

    public List<HistoricoContatoVO> getLista() {
        return lista;
    }

    public void setLista(List<HistoricoContatoVO> lista) {
        this.lista = lista;
    }

    public String getFiltroResposta() {
        return filtroResposta;
    }

    public void setFiltroResposta(String filtroResposta) {
        this.filtroResposta = filtroResposta;
    }

    public String getFiltroMensagem() {
        return filtroMensagem;
    }

    public void setFiltroMensagem(String filtroMensagem) {
        this.filtroMensagem = filtroMensagem;
    }
    
    public void irParaTelaCliente() throws Exception {
        setMsgAlert("");
        HistoricoContatoVO amostra = (HistoricoContatoVO) context().getExternalContext().getRequestMap().get("contato");
        if(UteisValidacao.emptyString(amostra.getClienteVO().getMatricula())){
            return;
        }
        try {
            ClienteControle clienteControle = (ClienteControle) context().getExternalContext().getSessionMap().get(ClienteControle.class.getSimpleName());
            if (clienteControle == null) {
                clienteControle = new ClienteControle();
                JSFUtilities.storeOnSession(ClienteControle.class.getSimpleName(), clienteControle);
            }
            clienteControle.setClienteVO(getFacade().getCliente().consultarPorMatricula(amostra.getClienteVO().getMatricula(), false, Uteis.NIVELMONTARDADOS_TODOS));
            clienteControle.validarTela();
            HistoricoContatoControle historicoContatos = (HistoricoContatoControle) JSFUtilities.getManagedBean("HistoricoContatoControle");
            historicoContatos.inicializarHistoricoContato();
            if(UteisValidacao.emptyNumber(clienteControle.getClienteVO().getCodigo())){
            	montarMsgAlert("Cliente não encontrado.");
            }else{
            	setMsgAlert("abrirPopup('historicoContatoClienteForm.jsp', 'Questionario', 780, 595);");
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }
     
    
    public String getFiltros() throws Exception {
        String filtros = "";
        if (inicio != null) {
            filtros += " | Início: " + Uteis.getDataAplicandoFormatacao(inicio, "dd/MM/yyyy");
        }
        if (inicio != null) {
            filtros += " | Fim: " + Uteis.getDataAplicandoFormatacao(fim, "dd/MM/yyyy");
        }
        if(!UteisValidacao.emptyString(filtroNomePessoa)){
            filtros += " | Cliente: "+filtroNomePessoa;
        }
        if(!UteisValidacao.emptyString(filtroMensagem)){
            filtros += " | Mensagem: "+filtroMensagem;
        }
        if(!UteisValidacao.emptyString(filtroResposta)){
            filtros += " | Respostas: "+filtroResposta;
        }
        if(!UteisValidacao.emptyString(responsavel.getNome())){
            filtros += " | Resp. Contato: "+responsavel.getNome();
        }
        
        if(!UteisValidacao.emptyNumber(getMalaDireta())){
            filtros += " | Mala direta: "+malaDireta;
        }
        return filtros.replaceFirst("[|]", "");
    }

    public String getFiltroNomePessoa() {
        return filtroNomePessoa;
    }

    public void setFiltroNomePessoa(String filtroNomePessoa) {
        this.filtroNomePessoa = filtroNomePessoa;
    }
    
    

}
