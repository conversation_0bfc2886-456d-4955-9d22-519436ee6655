package controle.crm;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.AberturaMetaVO;
import negocio.comuns.crm.EventoVO;
import negocio.comuns.crm.FecharMetaDetalhadoVO;
import negocio.comuns.crm.IndicacaoVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.crm.Indicacao;
import controle.arquitetura.SuperControle;
import controle.basico.ClienteControle;
import org.apache.commons.beanutils.BeanUtils;

/**
 * Classe responsável por implementar a interação entre os componentes JSF das
 * páginas
 * indicacaoForm.jsp indicacaoCons.jsp) com as funcionalidades da classe
 * <code>Indicacao</code>.
 * Implemtação da camada controle (Backing Bean).
 *
 * @see SuperControle
 * @see Indicacao
 * @see IndicacaoVO
 */
public class IndicacaoControle extends SuperControle {

    private IndicadoVO indicadoVO;
    private IndicacaoVO indicacaoVO;
    private Date dataConsulta;
    private ClienteVO cliente;
    private Boolean apresentarCliente = true;
    private Boolean apresentarPassivo = false;
    private Boolean apresentarColaborador = false;
    private Boolean apresentarAluno = false;
    private Boolean apresentarTelefoneAluno = false;
    private Boolean apresentarDadosPassivo = false;
    private Boolean apresentarDadosColaborador = false;
    private Boolean apresentarCampoClienteQueRealizouIndicacao = true;
    private Boolean apresentarCampoColaboradorQueRealizouIndicacao = true;
    private Boolean apresentarBotaoAdicionar = true;
    private List listaclienteIndicado;
    private List<IndicacaoVO> listaIndicacoes;
    private List<Integer> listaContatoRealizado = null;

    //Filto
    private IndicacaoFiltro indicacaoFiltro = new IndicacaoFiltro();
    private Date dataInicioConsulta;
    private Date dataFimConsulta;
    private String filtroConsulta;
    private String msgAlert;
    private Integer totalIndicados;
    private Integer indicadosConvertidos;
    private String totalResultados;

    public Date getDataInicioConsulta() {
        return dataInicioConsulta;
    }

    public void setDataInicioConsulta(Date dataInicioConsulta) {
        this.dataInicioConsulta = dataInicioConsulta;
    }

    public Date getDataFimConsulta() {
        return dataFimConsulta;
    }

    public void setDataFimConsulta(Date dataFimConsulta) {
        this.dataFimConsulta = dataFimConsulta;
    }

    public String getFiltroConsulta() {
        return filtroConsulta;
    }

    public void setFiltroConsulta(String filtroConsulta) {
        this.filtroConsulta = filtroConsulta;
    }

    public IndicacaoControle() throws Exception {
        setDataConsulta(negocio.comuns.utilitarias.Calendario.hoje());
        obterUsuarioLogado();
        inicializarFacades();
        setControleConsulta(new ControleConsulta());
        realizarlimpezaCamposMensagem();
        setMensagemID("msg_entre_prmconsulta");
        montarResultadosIndicacao();
    }

    public void realizarlimpezaCamposMensagem() {

        try {
            setDataInicioConsulta(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(Calendario.hoje())));
            setDataFimConsulta(Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje()));
        } catch (Exception e) {
            setMensagemID("msg_erro");
            setMensagemDetalhada(e);
        }

        setMensagemID("msg_entre_dados");
        setMensagem("");
        setMensagemDetalhada("");
        setSucesso(true);
        setErro(false);
    }

    public String novo() throws Exception {
        try {
            setIndicacaoVO(new IndicacaoVO());
            setIndicadoVO(new IndicadoVO());
            setApresentarAluno(false);
            setApresentarCliente(true);
            setApresentarColaborador(false);
            setApresentarPassivo(false);
            setApresentarCampoClienteQueRealizouIndicacao(true);
            setApresentarCampoColaboradorQueRealizouIndicacao(true);
            setApresentarTelefoneAluno(false);
            setApresentarDadosPassivo(false);
            setApresentarDadosColaborador(false);
            setApresentarBotaoAdicionar(true);

            inicializarUsuarioLogado();
            setMsgAlert("");
            setMensagemID("msg_entre_dados");
            setMensagem("");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
        return "editar";
    }

    public void validarTelaRealizarContato() throws Exception {
        HistoricoContatoControle his = (HistoricoContatoControle) getControlador("HistoricoContatoControle");
        if (his != null && his.getHistoricoContatoVO().getClienteVO().getCodigo().intValue() != 0) {
            if (getColaboradorResponsavel() == null || getColaboradorResponsavel().getCodigo() == 0) {
                setColaboradorResponsavel(getUsuarioLogado());
            }
            novo();
            getIndicacaoVO().setClienteQueIndicou(getFacade().getCliente().consultarPorChavePrimaria(his.getHistoricoContatoVO().getClienteVO().getCodigo().intValue(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
            setCliente(getIndicacaoVO().getClienteQueIndicou());
        }
    }

    public void historicoIndicacao() throws Exception {
        notificarRecursoEmpresa(RecursoSistema.HISTORICO_INDICACAO_CLIENTE);
        ClienteControle his = (ClienteControle) getControlador("ClienteControle");
        if (his != null) {
            his.pegarClienteTelaCliente();
            if (his.getClienteVO().getCodigo() != 0) {
                if (getColaboradorResponsavel() == null || getColaboradorResponsavel().getCodigo() == 0) {
                    setColaboradorResponsavel(getUsuarioLogado());
                }
                novo();
                getIndicacaoVO().setClienteQueIndicou(his.getClienteVO());
                setCliente(his.getClienteVO());
            } else {
                throw new Exception("Não Foi Possível Posicionar o Cliente. Contate o Suporte Técnico.");
            }
            consultarIndicacoes();
        }
    }

    public Boolean getApresentarCalendarDia() {
        if (getControleConsulta().getCampoConsulta().equals("dia")) {
            return true;
        }
        return false;
    }

    public void inicializarUsuarioLogado() throws Exception {
        try {
            AberturaMetaVO aberturaMeta = getFacade().getAberturaMeta().consultarAberturaPorCodigoUsuario(getUsuarioLogado().getCodigo(), Calendario.hoje(), getEmpresaLogado().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (aberturaMeta == null || aberturaMeta.getCodigo() == null || aberturaMeta.getCodigo() == 0) {
                throw new ConsistirException("Ainda não foi registrado nenhuma abertura de meta para realizar esse cadastro.");
            }
            if (getUsuarioLogado().getCodigo() != null && getUsuarioLogado().getCodigo() != 0) {
                getIndicacaoVO().getResponsavelCadastro().setCodigo(getUsuarioLogado().getCodigo());
                getIndicacaoVO().getResponsavelCadastro().setNome(getUsuarioLogado().getNome());
                getIndicacaoVO().getResponsavelCadastro().setAdministrador(getUsuarioLogado().getAdministrador());
            }
            if (getColaboradorResponsavel() != null && getColaboradorResponsavel().getCodigo().intValue() != 0 && aberturaMeta != null && aberturaMeta.getCodigo() != 0) {
                getIndicacaoVO().getColaboradorResponsavel().setCodigo(getColaboradorResponsavel().getCodigo());
                getIndicacaoVO().getColaboradorResponsavel().setNome(getColaboradorResponsavel().getNome());
                getIndicacaoVO().getColaboradorResponsavel().setAdministrador(getColaboradorResponsavel().getAdministrador());
                getIndicacaoVO().setDiaAbertura(aberturaMeta.getDia());
                if (!aberturaMeta.getMetaEmAberto()) {
                    throw new ConsistirException("Não poderá ser lançando mais nenhum cadastro de indicação para essa abertura pois ela já está fechada.");
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void consultarIndicacoes() {
        try {
            setListaIndicacoes(new ArrayList<IndicacaoVO>());
            if (getIndicacaoVO().getClienteQueIndicou().getCodigo() != 0
                    && !getIndicacaoVO().getClienteQueIndicou().getPessoa().getNome().isEmpty()) {
                setListaIndicacoes(getFacade().getIndicacao().
                        consultarPorClienteQueIndicou(getIndicacaoVO().getClienteQueIndicou().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            } else if (getIndicacaoVO().getColaboradorQueIndicou().getCodigo() != 0
                    && !getIndicacaoVO().getColaboradorQueIndicou().getPessoa().getNome().isEmpty()) {
                setListaIndicacoes(getFacade().getIndicacao().
                        consultarPorColaboradorQueIndicou(
                                getIndicacaoVO().getColaboradorQueIndicou().getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS));
            }
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void montarContatosRealizadosIndicados() throws Exception {
        for (IndicadoVO indicado : getIndicacaoVO().getIndicadoVOs())
            indicado.setContatoRealizado(getFacade().getHistoricoContato().consultarHistoricoPorCodigoIndicado(indicado.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
    }

    public String editar() throws Exception {
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        try {
            IndicacaoVO obj = getFacade().getIndicacao().consultarPorChavePrimaria(codigoConsulta, Uteis.NIVELMONTARDADOS_TODOS);
            inicializarAtributosRelacionados(obj);
            obj.setNovoObj(new Boolean(false));
            setMsgAlert("");
            setIndicacaoVO(obj);
            montarContatosRealizadosIndicados();
            setIndicadoVO(new IndicadoVO());
            setMensagemID("msg_dados_editar");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
        return "editar";
    }

    public void inicializarAtributosRelacionados(IndicacaoVO obj) throws Exception {
        if (obj.getResponsavelCadastro() == null) {
            obj.setResponsavelCadastro(new UsuarioVO());
        }
        if (obj.getColaboradorQueIndicou() == null) {
            obj.setColaboradorQueIndicou(new ColaboradorVO());
        }

        if (obj.getClienteQueIndicou() == null) {
            obj.setClienteQueIndicou(new ClienteVO());
            setCliente(new ClienteVO());
        }
        if (obj.getColaboradorResponsavel() == null) {
            obj.setColaboradorResponsavel(new UsuarioVO());
        }
    }

    public void gravar() {
        try {
            indicacaoVO.setEmpresa(getEmpresaLogado());
            if (indicacaoVO.isNovoObj().booleanValue()) {
                getFacade().getIndicacao().incluir(indicacaoVO);
            } else {
                getFacade().getIndicacao().alterar(indicacaoVO);
            }
            setMensagemID("msg_dados_gravados");
            montarMsgAlert(getMensagem());
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            montarMsgAlert(getMensagemDetalhada());
            setSucesso(false);
            setErro(true);
        }
    }

    public void limparCampoClienteSuggestion() {
        getIndicacaoVO().setClienteQueIndicou(new ClienteVO());
    }

    public void limparCampoColaboradorSuggestion() {
        getIndicacaoVO().setColaboradorQueIndicou(new ColaboradorVO());
    }

    public String consultar() {
        try {
            super.consultar();
            List objs = new ArrayList();
            if (getControleConsulta().getCampoConsulta().equals("codigo")) {
                if (getControleConsulta().getValorConsulta().equals("")) {
                    getControleConsulta().setValorConsulta("0");
                }
                int valorInt = Integer.parseInt(getControleConsulta().getValorConsulta());
                objs = getFacade().getIndicacao().consultarPorCodigo(valorInt, true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeIndicado")) {
                objs = getFacade().getIndicacao().consultarPorNomeIndicado(getControleConsulta().getValorConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("responsavelCadastro")) {
                objs = getFacade().getIndicacao().consultarPorNomeResponsavelCadastro(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("colaboradorResponsavel")) {
                objs = getFacade().getIndicacao().consultarPorNomeColaboradorResponsavel(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("nomeIndicador")) {
                objs = getFacade().getIndicacao().consultarPorNomeIndicador(getControleConsulta().getValorConsulta(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (getControleConsulta().getCampoConsulta().equals("dia")) {
                objs = getFacade().getIndicacao().consultarPorDia(getDataConsulta(), getDataConsulta(), true, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            setListaConsulta(objs);
            setMensagemID("msg_dados_consultados");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
            return "consultar";
        }
    }

    public List<SelectItem> getTipoConsultaCombo() {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("nomeIndicador", "Quem indicou"));
        itens.add(new SelectItem("codigo", "Código"));
        itens.add(new SelectItem("responsavelCadastro", "Responsável Cadastro"));
        itens.add(new SelectItem("colaboradorResponsavel", "Colaborador Responsável"));
        itens.add(new SelectItem("dia", "Dia"));
        return itens;
    }

    public String excutarExclusaoDependentesIndicacao() {
        try {
            getFacade().getIndicacao().excutarExclusaoDependentesIndicacao(indicacaoVO);
            novo();
            setMensagemID("msg_dados_excluidos");
            setSucesso(true);
            setErro(false);
            return "consultar";
        } catch (Exception e) {
            if(e.getMessage().contains("ERRO: atualização ou exclusão em tabela \"indicado\" viola restrição de chave estrangeira") || e.getMessage().contains("ERROR: update or delete on table \"indicado\" violates foreign key")){
                setMensagemDetalhada("Esta indicação não pode ser excluída!");
            }else {
                setMensagemDetalhada("msg_erro", e.getMessage());
            }
            setSucesso(false);
            setErro(true);
            return "editar";
        }
    }

    public void validarEmpresaLogado() throws Exception {
        if ((estahDeslogado()) || (getEmpresaLogado() == null)) {
            setMensagemDetalhada("msg_erro");
        }
    }

    public void editarIndicadosLista() throws Exception {
        IndicadoVO obj = (IndicadoVO) context().getExternalContext().getRequestMap().get("indicados");
        setIndicadoVO(obj);

    }

    public String removerIndicadosLista() throws Exception {
        IndicadoVO obj = (IndicadoVO) context().getExternalContext().getRequestMap().get("indicados");
        getIndicacaoVO().excluirObjIndicadoVOs(obj);
        setMensagemID("msg_dados_excluidos");
        return "editar";
    }

    public String getMascaraConsulta() {
        return "";
    }

    public void selecionarClienteSuggestionBox() throws Exception {
        ClienteVO clienteVO = (ClienteVO) request().getAttribute("result");
        if (clienteVO != null) {
            getIndicacaoVO().setClienteQueIndicou(clienteVO);
        }
    }

    public void selecionarColaboradorSuggestionBox() throws Exception {
        ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("result");
        if (colaboradorVO != null) {
            getIndicacaoVO().setColaboradorQueIndicou(colaboradorVO);

        }
    }

    public void selecionarEventoSuggestionBox() throws Exception {
        EventoVO eventoVO = (EventoVO) request().getAttribute("result");
        if (eventoVO != null) {
            getIndicacaoVO().setEvento(eventoVO);
        }
    }

    public List<ClienteVO> executarAutocompleteCliente(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ClienteVO> result = new ArrayList<ClienteVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarTodosClienteComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
            } else {
                result = (ArrayList<ClienteVO>) getFacade().getCliente().consultarPorNomeCliente(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS, 50);
            }
            if (null != getIndicacaoVO()) {
                getIndicacaoVO().setColaboradorQueIndicou(new ColaboradorVO());
            }
        } catch (Exception ex) {
            result = (new ArrayList<ClienteVO>());
        }
        return result;
    }

    public List<ColaboradorVO> executarAutocompleteColaborador(Object suggest) {
        String pref = (String) suggest;
        ArrayList<ColaboradorVO> result = new ArrayList<ColaboradorVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarTodosColaboradorComLimite(getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                result = (ArrayList<ColaboradorVO>) getFacade().getColaborador().consultarPorNomeColaboradorComLimite(pref, getEmpresaLogado().getCodigo(), false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }
            if (null != getIndicacaoVO()) {
                getIndicacaoVO().setClienteQueIndicou(new ClienteVO());
            }
        } catch (Exception ex) {
            result = (new ArrayList<ColaboradorVO>());
        }
        return result;
    }

    public List<EventoVO> executarAutocompleteEvento(Object suggest) {
        String pref = (String) suggest;
        ArrayList<EventoVO> result = new ArrayList<EventoVO>();
        try {
            if (pref.equals("%")) {
                result = (ArrayList<EventoVO>) getFacade().getEvento().consultarTodosEventosComLimite(false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            } else {
                result = (ArrayList<EventoVO>) getFacade().getEvento().consultarPorNomeEventoComLimite(pref, false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            }

        } catch (Exception ex) {
            result = (new ArrayList<EventoVO>());
        }
        return result;
    }

    public void limparCampoEventoSuggestion() {
        getIndicacaoVO().setEvento(new EventoVO());
    }

    public String inicializarConsultar() {
        setMensagemID("msg_entre_prmconsulta");
        return "consultar";
    }

    protected void limparRecursosMemoria() {
        super.limparRecursosMemoria();
        indicacaoVO = null;
    }

    public void adicionarIndicados() throws Exception {
        try {
            if (!getIndicacaoVO().getCodigo().equals(new Integer(0))) {
                indicadoVO.setIndicacaoVO(getIndicacaoVO());
            }
            getIndicacaoVO().adicionarObjIndicadoVOs(getIndicadoVO());
            this.setIndicadoVO(new IndicadoVO());
            setMensagemID("msg_dados_adicionados");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setSucesso(false);
            setErro(true);
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public void editarIndicadosListaFecharMetaDetalhado() throws Exception {
        FecharMetaDetalhadoVO obj = (FecharMetaDetalhadoVO) context().getExternalContext().getRequestMap().get("fecharMetaDetalhado");
        setIndicacaoVO(getFacade().getIndicacao().consultarPorChavePrimaria(obj.getIndicado().getIndicacaoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        setIndicadoVO(obj.getIndicado());
    }

    public int getIdadeCliente() {
        return Uteis.calcularIdadePessoa(
                negocio.comuns.utilitarias.Calendario.hoje(), getIndicacaoVO().getClienteQueIndicou().getPessoa().getDataNasc());
    }

    public int getIdadeColaborador() {
        return Uteis.calcularIdadePessoa(
                negocio.comuns.utilitarias.Calendario.hoje(), getIndicacaoVO().getColaboradorQueIndicou().getPessoa().getDataNasc());
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");

        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "").replace("span", "").replace("/", "").replace("<>", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getIndicacao().consultarParaImpressao(filtro, ordem, campoOrdenacao, getDataInicioConsulta(), getDataFimConsulta(), getEmpresaLogado() == null ? 0 : getEmpresaLogado().getCodigo(), filtro);
        exportadorListaControle.exportar(evt, listaParaImpressao, filtro, null);
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public IndicacaoVO getIndicacaoVO() {
        return indicacaoVO;
    }

    public void setIndicacaoVO(IndicacaoVO indicacaoVO) {
        this.indicacaoVO = indicacaoVO;
    }

    public Date getDataConsulta() {
        return dataConsulta;
    }

    public void setDataConsulta(Date dataConsulta) {
        this.dataConsulta = dataConsulta;
    }

    public Boolean getApresentarCliente() {
        return apresentarCliente;
    }

    public void setApresentarCliente(Boolean apresentarCliente) {
        this.apresentarCliente = apresentarCliente;
    }

    public Boolean getApresentarPassivo() {
        return apresentarPassivo;
    }

    public void setApresentarPassivo(Boolean apresentarPassivo) {
        this.apresentarPassivo = apresentarPassivo;
    }

    public Boolean getApresentarColaborador() {
        return apresentarColaborador;
    }

    public void setApresentarColaborador(Boolean apresentarColaborador) {
        this.apresentarColaborador = apresentarColaborador;
    }

    public Boolean getApresentarAluno() {
        return apresentarAluno;
    }

    public void setApresentarAluno(Boolean apresentarAluno) {
        this.apresentarAluno = apresentarAluno;
    }

    public Boolean getApresentarTelefoneAluno() {
        return apresentarTelefoneAluno;
    }

    public void setApresentarTelefoneAluno(Boolean apresentarTelefoneAluno) {
        this.apresentarTelefoneAluno = apresentarTelefoneAluno;
    }

    public Boolean getApresentarDadosPassivo() {
        return apresentarDadosPassivo;
    }

    public void setApresentarDadosPassivo(Boolean apresentarDadosPassivo) {
        this.apresentarDadosPassivo = apresentarDadosPassivo;
    }

    public Boolean getApresentarDadosColaborador() {
        return apresentarDadosColaborador;
    }

    public void setApresentarDadosColaborador(Boolean apresentarDadosColaborador) {
        this.apresentarDadosColaborador = apresentarDadosColaborador;
    }

    public Boolean getApresentarCampoClienteQueRealizouIndicacao() {
        return apresentarCampoClienteQueRealizouIndicacao;
    }

    public void setApresentarCampoClienteQueRealizouIndicacao(Boolean apresentarCampoClienteQueRealizouIndicacao) {
        this.apresentarCampoClienteQueRealizouIndicacao = apresentarCampoClienteQueRealizouIndicacao;
    }

    public Boolean getApresentarCampoColaboradorQueRealizouIndicacao() {
        return apresentarCampoColaboradorQueRealizouIndicacao;
    }

    public void setApresentarCampoColaboradorQueRealizouIndicacao(Boolean apresentarCampoColaboradorQueRealizouIndicacao) {
        this.apresentarCampoColaboradorQueRealizouIndicacao = apresentarCampoColaboradorQueRealizouIndicacao;
    }

    public IndicadoVO getIndicadoVO() {
        return indicadoVO;
    }

    public void setIndicadoVO(IndicadoVO indicadoVO) {
        this.indicadoVO = indicadoVO;
    }

    public List getListaclienteIndicado() {
        return listaclienteIndicado;
    }

    public void setListaclienteIndicado(List listaclienteIndicado) {
        this.listaclienteIndicado = listaclienteIndicado;
    }

    public Boolean getApresentarBotaoAdicionar() {
        return apresentarBotaoAdicionar;
    }

    public void setApresentarBotaoAdicionar(Boolean apresentarBotaoAdicionar) {
        this.apresentarBotaoAdicionar = apresentarBotaoAdicionar;
    }

    public void setListaIndicacoes(List<IndicacaoVO> listaIndicacoes) {
        this.listaIndicacoes = listaIndicacoes;
    }

    public List<IndicacaoVO> getListaIndicacoes() {
        if(null == listaIndicacoes){
            listaIndicacoes = new ArrayList<IndicacaoVO>();
        }
        return listaIndicacoes;
    }


    public void setMsgAlert(String msgAlert) {
        this.msgAlert = msgAlert;
    }

    public String getMsgAlert() {
        if (msgAlert == null) {
            return "";
        }
        return msgAlert;
    }


    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Indicação",
                "Deseja excluir a Indicação?",
                this, "excutarExclusaoDependentesIndicacao", "", "", "", "grupoBtnExcluir");
    }

    //<editor-fold defaultstate="collapsed" desc="Codigo para criacao de filtro para consulta">

    public IndicacaoFiltro getIndicacaoFiltro() {
        return indicacaoFiltro;
    }

    public void setIndicacaoFiltro(IndicacaoFiltro indicacaoFiltro) {
        this.indicacaoFiltro = indicacaoFiltro;
    }

    public void limparPeriodo() {
        indicacaoFiltro = new IndicacaoFiltro();
    }

    public List<IndicacaoVO> getListaIndicacoesByIndicados() {
        List<IndicacaoVO> r = new ArrayList<IndicacaoVO>();

        for(IndicacaoVO ic : getListaIndicacoes()){
            for (IndicadoVO id  : ic.getIndicadoVOs()){

                try {
                    IndicacaoVO clone = (IndicacaoVO)  BeanUtils.cloneBean(ic);
                    clone.setIndicadoVOs(new ArrayList<IndicadoVO>());
                    clone.setIndicadoMobile(id);
                    r.add(clone);

                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

        return r;
    }

    public void consultarIndicacoesByFiltro() {
        try {

            setMensagemDetalhada("", "");
            setSucesso(true);
            setErro(false);

            setListaIndicacoes(new ArrayList<IndicacaoVO>());

            setListaIndicacoes(getFacade().getIndicacao()
                    .consultarPorPeriodoColaboradorCliente(indicacaoFiltro.inicio,
                            indicacaoFiltro.fim,
                            indicacaoFiltro.colaboradorIndicou.getCodigo(),
                            indicacaoFiltro.clienteIndicou.getCodigo(),
                            false,
                            Uteis.NIVELMONTARDADOS_TODOS));

        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public static class IndicacaoFiltro {
        private Date inicio;
        private Date fim;
        private ColaboradorVO colaboradorIndicou;
        private ClienteVO clienteIndicou;


        private HttpServletRequest request() {
            return (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        }

        public void selectClienteSuggestionBox() throws Exception {

            ClienteVO clienteVO = (ClienteVO) request().getAttribute("cliente");
            if (clienteVO != null) {
                setClienteIndicou(clienteVO);
            }
        }

        public void selectColaboradorSuggestionBox() throws Exception {
            ColaboradorVO colaboradorVO = (ColaboradorVO) request().getAttribute("colaborador");
            if (colaboradorVO != null) {
                setColaboradorIndicou(colaboradorVO);
            }

        }

        public Date getInicio() {
            return inicio;
        }

        public void setInicio(Date inicio) {
            this.inicio = inicio;
        }

        public Date getFim() {
            return fim;
        }

        public void setFim(Date fim) {
            this.fim = fim;
        }

        public ColaboradorVO getColaboradorIndicou() {
            if (null == colaboradorIndicou) {
                colaboradorIndicou = new ColaboradorVO();
            }

            return colaboradorIndicou;
        }

        public void setColaboradorIndicou(ColaboradorVO colaboradorIndicou) {
            this.colaboradorIndicou = colaboradorIndicou;
        }

        public ClienteVO getClienteIndicou() {
            if (null == clienteIndicou) {
                clienteIndicou = new ClienteVO();
            }
            return clienteIndicou;
        }

        public void setClienteIndicou(ClienteVO clienteIndicou) {
            this.clienteIndicou = clienteIndicou;
        }
    }

    public Integer getTotalIndicados() {
        if(totalIndicados == null){
            return totalIndicados = 0;
        }
        return totalIndicados;
    }

    public void setTotalIndicados(Integer totalIndicados) {
        this.totalIndicados = totalIndicados;
    }

    public Integer getIndicadosConvertidos() {
        if(indicadosConvertidos == null){
            return indicadosConvertidos = 0;
        }
        return indicadosConvertidos;
    }

    public void setIndicadosConvertidos(Integer indicadosConvertidos) {
        this.indicadosConvertidos = indicadosConvertidos;
    }

    public String getTotalResultados() {
        if(totalResultados == null){
            return totalResultados = "0%";
        }
        return totalResultados;
    }

    public void setTotalResultados(String totalResultados) {
        this.totalResultados = totalResultados;
    }

    public void montarResultadosIndicacao() throws Exception {
        Map<String, Number> resultado = getFacade().getIndicacao().countResultadosIndicacao(getEmpresaLogado().getCodigo(), getDataInicioConsulta(), getDataFimConsulta());
        setTotalIndicados(resultado.get("totalRegistros").intValue());
        setIndicadosConvertidos(resultado.get("convertidos").intValue());
        setTotalResultados(resultado.get("porcentagem").toString() + "%");
    }
}
