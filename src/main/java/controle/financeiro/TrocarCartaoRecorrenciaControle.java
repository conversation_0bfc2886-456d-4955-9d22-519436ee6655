package controle.financeiro;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import java.util.ArrayList;
import java.util.List;
import javax.faces.event.ActionEvent;

import br.com.pactosolucoes.comuns.util.JSFUtilities;

import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;
import controle.arquitetura.SuperControle;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import servicos.impl.dcc.base.DCCAttEnum;

/**
 * <AUTHOR>
 *
 */
public class TrocarCartaoRecorrenciaControle extends SuperControle {

    private ContratoRecorrenciaVO contratoRecorrencia;
    private MovParcelaVO proximaParcelaEA;
    private Boolean existeParcela;
    private Boolean existeParcelaAnteriorEmRemessa = false;
    private boolean trocarCartaoSemCobrarParcela = false;
    private PlanoVO plano;

    /**
     * Responsável por receber o codigo de um contrato em regime de recorrencia que terá o cartão trocado
     * <AUTHOR>
     * 03/08/2011
     */
    public void listenerCodigoContrato(ActionEvent event) throws Exception {
        this.proximaParcelaEA = null;
        this.existeParcela = false;

        JSFUtilities.setManagedBeanValue(
                PagamentoCartaoCreditoControle.class.getSimpleName()
                + ".dadosPagamento",
                new CartaoCreditoTO());
        JSFUtilities.setManagedBeanValue(
                PagamentoCartaoCreditoControle.class.getSimpleName()
                + ".trocarCartao",
                true);
        
        PagamentoCartaoCreditoControle control = (PagamentoCartaoCreditoControle) 
                JSFUtilities.getFromSession(PagamentoCartaoCreditoControle.class);

        control.setStyleClassCartao("white");
        control.setStyleClassCartaoRodape("white");
        control.setStyleClassCartaoTitles("titleGrey");

        if (control != null){
            control.setMovPagamentoSelecionado(null);
            JSFUtilities.storeOnSession(PagamentoCartaoCreditoControle.class.getSimpleName(), control);
        }
        JSFUtilities.setManagedBeanValue(
                PagamentoCartaoCreditoControle.class.getSimpleName()
                + ".mensagem",
                "");
        JSFUtilities.setManagedBeanValue(
                PagamentoCartaoCreditoControle.class.getSimpleName()
                + ".mensagemDetalhada",
                "");
        
        JSFUtilities.setManagedBeanValue(
                PagamentoCartaoCreditoControle.class.getSimpleName()
                + ".dadosPagamento.parcelas",
                1);
        
        this.trocarCartaoSemCobrarParcela = true;

        Integer codigoContrato = Integer.valueOf(event.getComponent().getAttributes().get("codigoContrato").toString());
        plano = getFacade().getPlano().consultarPorCodigoContrato(codigoContrato, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        prepararTroca(getFacade().getContratoRecorrencia().consultarPorContrato(codigoContrato, Uteis.NIVELMONTARDADOS_MINIMOS));
    }

    /**
     * Responsável por receber o contrato de recorrencia que terá o cartão trocado
     * <AUTHOR>
     * 03/08/2011
     */
    public void listenerContratoRecorrencia(ActionEvent event) throws Exception {
        this.trocarCartaoSemCobrarParcela = false;
        ContratoRecorrenciaVO contratoRecorrencia = (ContratoRecorrenciaVO) event.getComponent().getAttributes().get("contratoRecorrencia");
        prepararTroca(contratoRecorrencia);
    }

    /**
     * Pesquisa qual a proxima parcela em aberto para que seja efetuado o pagamento
     * <AUTHOR>
     * 04/08/2011
     */
    public void prepararTroca(ContratoRecorrenciaVO contratoRecorrencia) throws Exception {
        setContratoRecorrencia(contratoRecorrencia);
        setExisteParcela(false);
        setExisteParcelaAnteriorEmRemessa(false);
        //consultar parcelas abertas deste contrato
        List<MovParcelaVO> parcelas = getFacade().getMovParcela().consultarParcelas(contratoRecorrencia.getContrato().getCodigo(), null,null,Uteis.NIVELMONTARDADOS_MINIMOS);
        for(MovParcelaVO parcela: parcelas){
            if (!parcelaEmRemessa(parcela)) {
                setProximaParcelaEA(parcela);
                setExisteParcela(true);
                break;
            } else {
                setExisteParcelaAnteriorEmRemessa(true);
            }
        }
    }

    /**
     * Prepara a parcela para a tela de pagamento
     * <AUTHOR>
     * 04/08/2011
     */
    public String avancarPagamento() {
        try {
            MovParcelaControle movParcelaControle = (MovParcelaControle) context().getExternalContext().getSessionMap().get(MovParcelaControle.class.getSimpleName());
            movParcelaControle = new MovParcelaControle();
            movParcelaControle.setMovParcelaVO(this.getProximaParcelaEA());
            movParcelaControle.setListaParcelasPagar(new ArrayList<MovParcelaVO>());
            movParcelaControle.getListaParcelasPagar().add(this.getProximaParcelaEA());
            movParcelaControle.setEmpresa(this.getProximaParcelaEA().getEmpresa());
            movParcelaControle.setValorTotalParcela(this.getProximaParcelaEA().getValorParcela());
            
            movParcelaControle.validarListaParcelasPagar();
            JSFUtilities.storeOnSession(MovParcelaControle.class.getSimpleName(), movParcelaControle);
            JSFUtilities.storeOnSession("trocarCartao", Boolean.TRUE);
            if(!UteisValidacao.emptyNumber(this.getProximaParcelaEA().getContrato().getCodigo())){
                setContratoRecorrencia(getFacade().getContratoRecorrencia().consultarPorContrato(this.getProximaParcelaEA().getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            }
            return "pagamento";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "erro";
        }
    }

    //-------------------------------------- GETTERS AND SETTERS------------------------------//
    /**
     * @param contratoRecorrencia the contratoRecorrencia to set
     */
    public void setContratoRecorrencia(ContratoRecorrenciaVO contratoRecorrencia) {
        this.contratoRecorrencia = contratoRecorrencia;
    }

    /**
     * @return the contratoRecorrencia
     */
    public ContratoRecorrenciaVO getContratoRecorrencia() {
        return contratoRecorrencia;
    }

    /**
     * @param proximaParcelaEA the proximaParcelaEA to set
     */
    public void setProximaParcelaEA(MovParcelaVO proximaParcelaEA) {
        this.proximaParcelaEA = proximaParcelaEA;
    }

    /**
     * @return the proximaParcelaEA
     */
    public MovParcelaVO getProximaParcelaEA() {
        return proximaParcelaEA;
    }

    /**
     * @param existeParcela the existeParcela to set
     */
    public void setExisteParcela(Boolean existeParcela) {
        this.existeParcela = existeParcela;
    }

    /**
     * @return the existeParcela
     */
    public Boolean getExisteParcela() {
        if (existeParcela == null) {
            existeParcela = false;
        }
        return existeParcela;
    }

    public boolean isTrocarCartaoSemCobrarParcela() {
        return trocarCartaoSemCobrarParcela;
    }

    public void setTrocarCartaoSemCobrarParcela(boolean trocarCartaoSemCobrarParcela) {
        this.trocarCartaoSemCobrarParcela = trocarCartaoSemCobrarParcela;
    }
    
    public boolean parcelaEmRemessa(final MovParcelaVO mp) throws Exception {
        List<RemessaItemVO> itensRemessa = getFacade().getZWFacade().getRemessaItem().consultarPorCodigoParcela(mp.getCodigo(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!itensRemessa.isEmpty()) {
            for (RemessaItemVO item : itensRemessa) {
                if (item.getRemessa().isAguardandoRetorno() && !item.getProps().containsKey(DCCAttEnum.StatusVenda.name())) {
                    return true;
                }
            }
        } 
        return false;
    }

    public Boolean getExisteParcelaAnteriorEmRemessa() {
        return existeParcelaAnteriorEmRemessa;
    }

    public void setExisteParcelaAnteriorEmRemessa(Boolean existeParcelaAnteriorEmRemessa) {
        this.existeParcelaAnteriorEmRemessa = existeParcelaAnteriorEmRemessa;
    }

    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }
}
