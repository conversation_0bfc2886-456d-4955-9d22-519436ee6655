package controle.financeiro;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import controle.arquitetura.SuperControle;
import controle.basico.clube.MensagemGenericaControle;
import negocio.comuns.financeiro.ContaContabilVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.FacadeManager;

import javax.faces.event.ActionEvent;
import java.util.ArrayList;
import java.util.List;

/**
  *
 * <AUTHOR>
 */
public class ContaContabilControle extends SuperControle {
    // objeto do formulário de cadastro

    private ContaContabilVO contaContabilVO = new ContaContabilVO();
    private String filtroConsulta;
    // consulta dos dados
    private List listaConsulta;
    private boolean mostrarContaContabil = false;

    public ContaContabilControle() throws Exception {
        validarMostrarTelaContaContabil();
        setMensagemID("msg_entre_prmconsulta");
    }

    /**
     * Chamado para criar uma nova conta
     *
     * @return navigation-case
     */
    public String novo() {
        this.contaContabilVO = new ContaContabilVO();
        montarSucesso("msg_entre_dados");
        return "editarContaContabil";
    }

    public void abrirContaContabil() {
        try {
            validarPermissao("ContaContabil", "4.37 - Conta contábil", getUsuarioLogado());
            setMsgAlert("abrirPopup('"+request().getContextPath() + "/faces/finanContaContabilCons.jsp?modulo=financeiroWeb', 'ContaContabil', 800, 595);");
            //montarSucesso("msg_entre_prmconsulta");
            montarSucesso("");
        } catch (Exception e) {
            setMsgAlert("alert('" + e.getMessage() + "');");
        }
    }

    public void validarMostrarTelaContaContabil(){
        this.mostrarContaContabil = false;
        try{
            validarPermissao("ContaContabil", "4.37 - Conta contábil", getUsuarioLogado());
            if (FacadeManager.getFacade().getFinanceiro().getConfiguracaoFinanceiro().consultar().isHabilitarExportacaoAlterData()){
                this.mostrarContaContabil = true;
            }

        }catch (Exception e){
            //
        }
    }

    public void exportar(ActionEvent evt) throws Exception {
        ExportadorListaControle exportadorListaControle = (ExportadorListaControle) JSFUtilities.getFromRequest(ExportadorListaControle.class.getSimpleName());
        String paramsTableFiltrada = context().getExternalContext().getRequestParameterMap().get("paramsTableFiltrada");
        String[] split = paramsTableFiltrada.split(",");
        String campoOrdenacao = split[0].replace("[", "");
        String ordem = split[1];
        String filtro = split[2].replace("''", "");
        filtro = filtro.replace("]", "");
        List listaParaImpressao = getFacade().getFinanceiro().getContaContabil().consultarParaImpressao(filtro, ordem, campoOrdenacao);
        exportadorListaControle.exportar(evt, listaParaImpressao,filtro, null);
    }
    public String editar() throws Exception{
        Integer codigoConsulta = Integer.parseInt(request().getParameter("chavePrimaria"));
        ContaContabilVO obj = getFacade().getFinanceiro().getContaContabil().consultarPorChavePrimaria(codigoConsulta,Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
        obj.setNovoObj(new Boolean(false));
        this.contaContabilVO = obj;
        montarSucesso("msg_dados_editar");
        return "editarContaContabil";
    }

    // /CONSULTA PAGINADA
    public String consultar() {
        try {
            super.consultar();
            List<ContaContabilVO> lista = getFacade().getFinanceiro().getContaContabil().consultarPorDescricao(getControleConsulta().getValorConsulta());
            setListaConsulta(lista);
            montarSucesso("msg_dados_consultados");
        } catch (Exception e) {
            setListaConsulta(new ArrayList());
            montarErro(e);
        }
        return "consultarContaContabil";
    }

    public void irPaginaInicial() throws Exception {
        controleConsulta.setPaginaAtual(1);
        this.consultar();
    }

    public void irPaginaAnterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() - 1);
        this.consultar();
    }

    public void irPaginaPosterior() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getPaginaAtual() + 1);
        this.consultar();
    }

    public void irPaginaFinal() throws Exception {
        controleConsulta.setPaginaAtual(controleConsulta.getNrTotalPaginas());
        this.consultar();
    }


    public String gravar() {
        try {
            if (this.contaContabilVO.isNovoObj().booleanValue()) {
                getFacade().getFinanceiro().getContaContabil().incluir(this.contaContabilVO);
            } else {
                getFacade().getFinanceiro().getContaContabil().alterar(this.contaContabilVO);
            }
            montarSucesso("msg_dados_gravados");
        } catch (Exception e) {
            montarErro(e);
        }
        return "editarContaContabil";
    }

    public void confirmarExcluir(){
        MensagemGenericaControle control = (MensagemGenericaControle) getControlador(MensagemGenericaControle.class);
        control.setMensagemDetalhada("", "");
        setMsgAlert("Richfaces.showModalPanel('mdlMensagemGenerica');");
        control.init("Exclusão de Conta Contábil",
                "Confirma a exclusão da conta contábil?",
                this, "excluir", "", "", "", "form");
    }

    public String excluir() {
        try {
            getFacade().getFinanceiro().getContaContabil().excluir(this.contaContabilVO);
            this.contaContabilVO = new ContaContabilVO();
            montarSucesso("msg_dados_excluidos");
            return "consultarContaContabil";
        } catch (Exception e) {
            montarErro(e);
            return "editarContaContabil";
        }
    }


    public List getListaConsulta() {
        return listaConsulta;
    }

    public void setListaConsulta(List listaConsulta) {
        this.listaConsulta = listaConsulta;
    }

    public ContaContabilVO getContaContabilVO() {
        return contaContabilVO;
    }

    public void setContaContabilVO(ContaContabilVO contaContabilVO) {
        this.contaContabilVO = contaContabilVO;
    }

    public String getFiltroConsulta() {
        return filtroConsulta;
    }

    public void setFiltroConsulta(String filtroConsulta) {
        this.filtroConsulta = filtroConsulta;
    }

    public boolean isMostrarContaContabil() {
        return mostrarContaContabil;
    }

    public void setMostrarContaContabil(boolean mostrarContaContabil) {
        this.mostrarContaContabil = mostrarContaContabil;
    }
}
