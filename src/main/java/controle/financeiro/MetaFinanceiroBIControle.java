package controle.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import br.com.pactosolucoes.estrutura.ItemExportacaoEnum;
import controle.arquitetura.security.LoginControle;
import controle.arquitetura.view.ComboBoxEmpresaControle;
import controle.arquitetura.view.TreeViewControle;
import controle.arquitetura.view.TreeViewNode;
import controle.modulos.SmartBoxControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoConsultaMetaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.DiasDaSemana;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ControleAcesso;
import org.richfaces.model.TreeNode;

import javax.faces.model.SelectItem;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MetaFinanceiroBIControle extends MetaFinanceiroControle {

    private String mesAtual;
    private String corTexto;
    private String cor;
    private List<DetalhamentoMetaFinanceiroVO> listaDetalhamento;
    private Integer consultarPor;
    private String dataListaFaturamentoRealizado;
    private String dataListaFaturamentoAcumulado;
    private List<DetalhamentoDiarioMetaFinanceiroVO> listaDiaDetalhada;
    private ConfiguracaoSistemaCRMVO config;
    private Integer numeroDiasAcademiaAberta = 0;
    private Double valorMetaAtingida;
    private String filtros;

    private boolean consultarMatricula = true;
    private boolean consultarRematricula = true;
    private boolean consultarRenovacao = true;
    private boolean consultarAtual = false;
    List<ColaboradorVO> listColaboradores = new ArrayList<>();

    public MetaFinanceiroBIControle() throws Exception {
        inicializarDados();
    }

    public void inicializarDados() throws Exception {
        inicializarDados(true);
        //obter configurações do sistema
        if(config == null) {
            config = getFacade().getConfiguracaoSistemaCRM().consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_TODOS);
        }
    }
    public List getListaSelectItemEmpresa() {
        return (List)JSFUtilities.getManagedBean("BIControle.listaSelectItemEmpresa");
    }
    public EmpresaVO getEmpresaFiltroBI(){
        return (EmpresaVO)JSFUtilities.getManagedBean("BIControle.empresaFiltro");
    }
    public List<ColaboradorVO> getListaColaboradorSessao(){
        return (List)JSFUtilities.getManagedBean("BIControle.listaColaboradorVOs");
    }

    public void inicializarMeta() throws Exception {
        Date dataPesquisa = Calendario.hoje();
        try{
            dataPesquisa = (Date) JSFUtilities.getManagedBean("BIControle.dataBaseFiltro");
        } catch (Exception e){
            dataPesquisa = Calendario.hoje();
        }
        if(getUsuarioLogado().getAdministrador() || permissao("ConsultarInfoTodasEmpresas")){
            super.setEmpresa(getEmpresaFiltroBI());
        } else {
            super.setEmpresa(getEmpresaLogado());
        }

        this.setMeta(new MetaFinanceiraEmpresaVO());
        Calendar dataCalendar = Calendario.getInstance();
        dataCalendar.setTime(dataPesquisa);
        this.setMesAtual(Uteis.getMesNomeReferencia(dataPesquisa) + "/" + dataCalendar.get(Calendar.YEAR));
        List<MetaFinanceiraEmpresaVO> listaMetas =  getFacade().getMetaFinanceiraEmpresa().consultarPorEmpresaAnoMesDescricao(getEmpresaFiltroBI().getCodigo(),
                dataCalendar.get(Calendar.YEAR), (dataCalendar.get(Calendar.MONTH) + 1), "", Uteis.NIVELMONTARDADOS_RESULTADOS_BI);
        if (!listaMetas.isEmpty() && getEmpresaFiltroBI().getCodigo() != 0) {
            //obtenho a primeira posição da lista porque com os filtros usados não irá existir mais de um resultado para a consulta
            this.setMeta(listaMetas.get(0));
            this.getMeta().setEmpresa(getEmpresaFiltroBI());
        }
        verificarApresentarMetaGeral();
        identificarGrupoColaboradorResponsavelFiltroBonecoTodos();
        atualizarMetaAtingida(listColaboradores, null, false, false);
        verificarValorQualMeta();
        setCor(getMeta().getCor());
        setCorTexto(getMeta().getCorTexto());
        verificarExistenciaValores();
        definirCorMetaAtingida(getMeta());
    }

    private void identificarGrupoColaboradorResponsavelFiltroBonecoTodos() {
        listColaboradores = new ArrayList<>();
        try {
            for (ColaboradorVO colab : getListaColaboradorFiltroBI()) {
                if(colab.getColaboradorEscolhido()){
                    listColaboradores.add(colab);
                }
            }
        } catch (Exception ignore) {
        }
    }

    public void atualizarMetaAtingida(List<ColaboradorVO> colaboradores, List<UsuarioVO> responsaveis, boolean usarFiltros, boolean atualizar) throws Exception {
        getMeta().restaurarValores();
        if (getApresentarApenasMetaConsultor() && !usarFiltros) {
            colaboradores.add(getUsuarioLogado().getColaboradorVO());
        }

        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            if (loginControle == null) {
                loginControle = new LoginControle();
            }

            if ((ControleAcesso.isPermiteMultiEmpresas() && !loginControle.getPermissaoAcessoMenuVO().getVisualizarMetasFinanceirasTodasEmpresas()) && !loginControle.getPermissaoAcessoMenuVO().getVisualizaRMetaFinanceiraEmpresaBI()) {
                aplicarPorcentagemColaborador(getMeta(), getUsuarioLogado().getColaboradorVO());
            } else if (colaboradores.size() == 1) {
                aplicarPorcentagemColaborador(getMeta(), colaboradores.get(0));
            }
        } catch (Exception ignore){}
        atualizarValorMeta(getMeta(), colaboradores, responsaveis, isConsultarRematricula(), isConsultarRenovacao(), isConsultarMatricula(), consultarAtual, atualizar);
        Date dataFim = Uteis.getDate("01/" + getMeta().getMes().getCodigo() + "/" + getMeta().getAno());
        if (UteisValidacao.dataMenorDataAtual(Uteis.obterUltimoDiaMesUltimaHora(dataFim))) {
            this.setDataMetaAtualizada(Uteis.obterUltimoDiaMesUltimaHora(dataFim));
        } else {
            this.setDataMetaAtualizada(Calendario.hoje());
        }
        this.setValorMetaAtingida(getMeta().getMetaAtingida());
        verificarValorQualMeta();

    }

    public void atualizarMetaAtingida() throws Exception {
        try{
            atualizarMetaAtingida(true);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    public void atualizarMetaAtingida(boolean atualizar) throws Exception {
        identificarGrupoColaboradorResponsavelFiltroBonecoTodos();
        atualizarMetaAtingida(listColaboradores, null, false, atualizar);
    }
    /* 
     Sobrescrita do metódo de consulta para verificar se o usuario tem permissão de ver a meta total da empresa
     */

    public void consultarMetas() {
        super.consultarMetas();
        try {
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            if (loginControle == null) {
                loginControle = new LoginControle();
            }

            if(loginControle.getPermissaoAcessoMenuVO().getVisualizarMetasFinanceirasTodasEmpresas()){
                return;
            } else if (loginControle.getPermissaoAcessoMenuVO().getVisualizaRMetaFinanceiraEmpresaBI()){
                carregarMetasEmpresaLogada();
            } else {
                carregarMetasConsultorLogado();
            }
        } catch (Exception e) {
            this.setMensagem(e.getMessage());
        }

    }

    public void carregarMetasEmpresaLogada() throws Exception {
        int i = 0;
        while (i <= getMetas().size()) {
            MetaFinanceiraEmpresaVO meta = getMetas().get(i);
            if(meta.getEmpresa().getCodigo() != getEmpresaLogado().getCodigo()){
                getMetas().remove(i);
                continue;
            }
            i++;
        }
    }

    public void carregarMetasConsultorLogado() throws Exception {
        Iterator i = getMetas().iterator();
        while (i.hasNext()) {
            MetaFinanceiraEmpresaVO meta = (MetaFinanceiraEmpresaVO) i.next();
            if (!aplicarPorcentagemColaborador(meta)) {
                getMetas().remove(i);
                continue;
            }
            meta.setDescricao(meta.getDescricao() + " - " + getNomeUsuarioLogado());
        }
    }

    private void verificarApresentarMetaGeral() throws Exception {
        if (!getApresentarApenasMetaConsultor()) {
            return;
        }
        if (!aplicarPorcentagemColaborador(getMeta())) {
            setMeta(new MetaFinanceiraEmpresaVO());
        }
    }

    private Boolean aplicarPorcentagemColaborador(MetaFinanceiraEmpresaVO meta) throws Exception {
        return aplicarPorcentagemColaborador(meta, getUsuarioLogado().getColaboradorVO());
    }
    private Boolean aplicarPorcentagemColaborador(MetaFinanceiraEmpresaVO meta, ColaboradorVO colaboradorVO) throws Exception {
        MetaFinanceiraConsultorVO metaDoConsultor = getFacade().getMetaFinanceiraConsultor().consultarPorColaboradorMetaDaEmpresa(
                colaboradorVO.getCodigo(), meta.getCodigo());
        if (UteisValidacao.emptyNumber(metaDoConsultor.getCodigo())) {
            return false;
        } else {
            for (MetaFinanceiraEmpresaValoresVO metaValor : meta.getValores()) {
                Double valor = metaValor.getValor() * (metaDoConsultor.getPercentagem() / 100);
                metaValor.setValor(valor);
            }
            return true;
        }
    }

    /**
     * Joao Alcides 22/02/2012
     *
     */
    public void selecionarMeta() throws Exception {
        MetaFinanceiraEmpresaVO obj = (MetaFinanceiraEmpresaVO) context().getExternalContext().getRequestMap().get("meta");
        this.setMeta(obj);
        verificarExistenciaValores();
        this.setMesAtual(this.getMeta().getMes().getDescricao() + "/" + this.getMeta().getAno());
        atualizarMetaAtingida(listColaboradores, null, false, false);
        verificarValorQualMeta();
//        setCor(getMeta().getCor());
//        setCorTexto(getMeta().getCorTexto());
        definirCorMetaAtingida(getMeta());
    }

    /**
     * retira os valores não cadastrados Joao Alcides 10/04/2012
     */
    private void verificarExistenciaValores() {
        List<MetaFinanceiraEmpresaValoresVO> valores = new ArrayList<MetaFinanceiraEmpresaValoresVO>();
        for (MetaFinanceiraEmpresaValoresVO valor : getMeta().getValores()) {
            if (!UteisValidacao.emptyNumber(valor.getCodigo())) {
                valores.add(valor);
            }
        }
        getMeta().setValores(valores);
    }

    public List<SelectItem> getConsultarPorItens() {
        return TipoConsultaMetaEnum.getSelectItens();
    }

    public void atualizarDetalhamentoMetaFinan() {
        limparMsg();
        setMsgAlert("");
        try {
            List<ColaboradorVO> colaboradoresSelecionados = obterColaboradores();
            if(colaboradoresSelecionados != null && !colaboradoresSelecionados.isEmpty()){
                aplicarAtualizandoFiltros();
            }else {
                montarDetalhamento(true);
            }
        }catch (Exception ex){
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }
    public void montarDetalhamento() {
        montarDetalhamento(false);
        montarArvoreColaboradores();
        selecionarColaboradoresFiltroBInaArvore();
    }

    private void selecionarColaboradoresFiltroBInaArvore() {
        if(!UteisValidacao.emptyList(listColaboradores)) {
            for (ColaboradorVO colaboradorVO : listColaboradores) {
                TreeNode nodePai =(TreeNode) JSFUtilities.getManagedBeanValue(TreeViewControle.class.getSimpleName() + ".treeNode");
                marcarColaboradorNaArvore(nodePai, colaboradorVO);

            }
            TreeViewControle treeView = (TreeViewControle) getControlador(TreeViewControle.class.getSimpleName());
            if (treeView != null) {
                treeView.atualizarSelecionados();
            }
        }
    }

    private void marcarColaboradorNaArvore(TreeNode nodePai, ColaboradorVO colaboradorVO) {
        if (nodePai != null) {
            Iterator<Map.Entry<TreeViewNode, TreeNode>> it = nodePai.getChildren();
            while (nodePai != null && it != null && it.hasNext()) {
                Map.Entry<TreeViewNode, TreeNode> entry = it.next();
                TreeViewNode treeViewNode = (TreeViewNode) entry.getValue().getData();
                if (treeViewNode.getObjeto() instanceof ColaboradorVO) {
                    ColaboradorVO colab = (ColaboradorVO) treeViewNode.getObjeto();
                    if (colab != null && colab.getCodigo().equals(colaboradorVO.getCodigo())) {
                        colab.setColaboradorEscolhidoRenovacao(true);
                        treeViewNode.setMarcado(true);
                    }
                }
                marcarColaboradorNaArvore(entry.getValue(), colaboradorVO);
            }
        }
    }

    public void montarDetalhamento(boolean atualizar) {
        try {
            if (UteisValidacao.emptyNumber(getMeta().getCodigo())) {
                throw new Exception("Selecione uma meta.");
            }
            limparMsg();
            setMsgAlert("");
            consultarAtual = false;
            getMeta().restaurarValores();
            consultarPor = TipoConsultaMetaEnum.FATURAMENTO_RECEBIDO.getCodigo();
            aplicarPorcentagemColaborador(getMeta());
            montarDetalhamento(false, !verificarMostrarTodaEmpresa(), atualizar);
            this.setFiltros(atualizarFiltros());
            atualizarMetaAtingida(listColaboradores, new ArrayList<>(), false, false);
            limparMsg();
            setMsgAlert("abrirPopup('detalhamentoMetaAtualFinanceiro.jsp', 'MetaAtualFinanceiro', 850, 700);");
        } catch (Exception ex) {
            montarErro(ex);
            setMsgAlert(getMensagemNotificar());
        }
    }

    /**
     * Joao Alcides 05/03/2012
     *
     * @param usarFiltros
     * @throws Exception
     */
    public void montarDetalhamento(boolean usarFiltros, boolean mostrarApenasDonoDaMeta, boolean atualizar) throws Exception {
        listaDetalhamento = new ArrayList<DetalhamentoMetaFinanceiroVO>();
        Date data = obterDataMesAno();
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(data);

        List<ColaboradorVO> colaboradores = listColaboradores; // colaboradores selecionados no card do BI
        List<UsuarioVO> responsaveis = new ArrayList<UsuarioVO>();
        if (usarFiltros) { // colaboradores selecionados dentro o popup de Detalhes do Cumprimento da Meta
            colaboradores = obterColaboradores();
            responsaveis.addAll(obterUsuariosResponsaveis());
        }

        verificarMetasColaboradoresSelecionados(colaboradores);

        if (mostrarApenasDonoDaMeta) {
            colaboradores.add(getUsuarioLogado().getColaboradorVO());
        }
        ConfiguracaoFinanceiroVO configFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
        Map<Object, Double> faturamentoPorDia = getFacade().getMetaFinanceiroBI().obterFaturamentoRecebidoPorDia(getKey(),
                inicio, fim, colaboradores,responsaveis, this.getMeta().getEmpresa().getCodigo(), isConsultarRematricula(), isConsultarRenovacao(),
                isConsultarMatricula(), consultarAtual, atualizar, configFinanceiro.isMetaFinanceiraPorFaturamento());
        
        construirListaPorDia(faturamentoPorDia, usarFiltros, inicio, fim);
        atualizarMetaAtingida(colaboradores, responsaveis, false, atualizar);
    }

    public boolean isMetaFinanceiraPorFaturamento() {
        try {
            ConfiguracaoFinanceiroVO configFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
            return configFinanceiro.isMetaFinanceiraPorFaturamento();
        } catch (Exception exception) {
            return false;
        }
    }

    public boolean verificarMostrarTodaEmpresa() {
        try {
            if (getUsuarioLogado().getAdministrador()) {
                return true;
            }
            LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
            return loginControle.getPermissaoAcessoMenuVO().getVisualizaRMetaFinanceiraEmpresaBI();
        } catch (Exception e) {
            return false;
        }

    }

    /**
     * Joao Alcides 02/03/2012
     */
    private void construirListaPorDia(Map<Object, Double> faturamentoPorDia, boolean usarFiltros, Date inicio, Date fim) throws Exception {
        EmpresaVO empresa = getFacade().getEmpresa().consultarPorChavePrimaria(this.getMeta().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        List<Date> feriados = getFacade().getFeriado().consultarDiasFeriados(inicio, fim, empresa);
        Double faturamentoAcumulado = 0.0;
        //lista de dias do mes
        List<Date> diasMes = Uteis.getDiasEntreDatas(inicio, fim);
        //contabilizar os dias em que a academia é aberta no mes da meta
        Integer totalDiasAcademiaAberta = 0;
        for (Date key : diasMes) {
            if (academiaAberta(key, inicio, fim, feriados)) {
                totalDiasAcademiaAberta++;
            }
        }
        setNumeroDiasAcademiaAberta(totalDiasAcademiaAberta);
        //usar index para os dias
        Integer indexDia = 1;
        //iterar nos dias do mês
        for (Date key : diasMes) {
            Double valor = 0.0;
            DetalhamentoMetaFinanceiroVO diaMeta = new DetalhamentoMetaFinanceiroVO();
            diaMeta.setDia(key);
            // para consulta por competencia, a chave é um integer que representa o dia
            if (getCompetencia()) {
                Integer diaMes = Uteis.getDiaMesData(key);
                valor = faturamentoPorDia.get(diaMes) != null ? faturamentoPorDia.get(diaMes) : 0.0;
                if (Uteis.getDiaMesData(fim) == diaMes) {
                    //para meses menores do que 31 dias, verificar se existem valores para
                    //os dias 29,30,31 se for o caso
                    while (diaMes < 31) {
                        diaMes++;
                        valor += faturamentoPorDia.get(diaMes) != null ? faturamentoPorDia.get(diaMes) : 0.0;
                    }
                }
            } else {
                //obter valor referente ao dia
                valor = faturamentoPorDia.get(new SimpleDateFormat("yyyy-MM-dd").format(key)) != null ? faturamentoPorDia.get(new SimpleDateFormat("yyyy-MM-dd").format(key)) : 0.0;
            }
            valor = Uteis.arredondarForcando2CasasDecimais(valor);
            diaMeta.setFaturamentoRealizado(valor);
            //acumular o valor e setar no dia o acumulado até o momento
            faturamentoAcumulado += valor;
            diaMeta.setFaturamentoAcumulado(faturamentoAcumulado);
            //verifcar academia aberta
            if (academiaAberta(key, inicio, fim, feriados)) {
                //se academia aberta setar valores de meta 
                separarValoresPorDia(totalDiasAcademiaAberta, indexDia, diaMeta);
                indexDia++;
            } else {
                montarDiaNaoCalculado(diaMeta);
            }
            listaDetalhamento.add(diaMeta);
        }
        Ordenacao.ordenarLista(listaDetalhamento, "dia");
        //inicializar arvore de colaboradores se não for usar os filtros
        if (!usarFiltros) {
            TreeViewControle treeView = (TreeViewControle) getControlador(
                    TreeViewControle.class.getSimpleName());
            treeView.getNodesMarcados().clear();
            JSFUtilities.storeOnSession(TreeViewControle.class.getSimpleName(), treeView);
            montarArvoreColaboradores();
        }
    }

    /**
     * Joao Alcides 05/03/2012
     *
     * @param diaMeta
     */
    private void montarDiaNaoCalculado(DetalhamentoMetaFinanceiroVO diaMeta) {
        for (int i = 0; i < this.getMeta().getValores().size(); i++) {
            MetaFinanceiraEmpresaValoresVO meta = new MetaFinanceiraEmpresaValoresVO();
            diaMeta.getMetas().add(meta);
        }
        diaMeta.setDiaCalculado(false);
        diaMeta.setCor("#F5F5F5");
        diaMeta.setCorTexto("#000000");
    }

    public void  aplicarAtualizandoFiltros() throws Exception {
        aplicarFiltros(true);
    }

    public void  aplicarFiltros() throws Exception {
        aplicarFiltros(false);
    }

    public void  aplicarFiltros(boolean atualizar) throws Exception {
        List<ColaboradorVO> colaboradoresSelecionados = obterColaboradores();
        verificarMetasColaboradoresSelecionados(colaboradoresSelecionados);
        switch (TipoConsultaMetaEnum.getTipoPorCodigo(consultarPor)) {
            case FATURAMENTO:
                detalharFaturamento();
                break;
            case FATURAMENTO_RECEBIDO:
                montarDetalhamento(true, false, atualizar);
                break;
            case COMPETENCIA:
                detalharCompetencia();
                break;
            case RECEITA:
                detalharReceita();
                break;
        }
        Date dataFim = Uteis.getDate("01/" + getMeta().getMes().getCodigo() + "/" + getMeta().getAno());
        if (UteisValidacao.dataMenorDataAtual(Uteis.obterUltimoDiaMesUltimaHora(dataFim))) {
            this.setDataMetaAtualizada(Uteis.obterUltimoDiaMesUltimaHora(dataFim));
        } else {
            this.setDataMetaAtualizada(Calendario.hoje());
        }
        verificarValorQualMeta();
        this.setFiltros(atualizarFiltros());
    }

    private void verificarMetasColaboradoresSelecionados(List<ColaboradorVO> colaboradoresSelecionados) throws Exception {
        if (!colaboradoresSelecionados.isEmpty()) {
            for (MetaFinanceiraEmpresaValoresVO valor : getMeta().getValores()) {
                //zerar valor de metas
                valor.setValor(0.0);
            }
            //iterar colaboradores selecionados
            for (ColaboradorVO colaborador : colaboradoresSelecionados) {
                MetaFinanceiraConsultorVO metaDoConsultor = getFacade().getMetaFinanceiraConsultor().consultarPorColaboradorMetaDaEmpresa(colaborador.getCodigo(), getMeta().getCodigo());
                //verificar se o mesmo possui meta
                if (metaDoConsultor != null && !UteisValidacao.emptyNumber(metaDoConsultor.getCodigo())) {
                    colaborador.setPossuiMetaFinanceira(true);
                    //montar as metas do consultor de acordo com a porcentagem
                    metaDoConsultor.montarValores(getMeta());
                    //iterar mos valores da meta
                    for (MetaFinanceiraEmpresaValoresVO valor : getMeta().getValores()) {
                        //iterar nas metas do consultor
                        CONS:
                        for (MetaFinanceiraEmpresaValoresVO valorConsultor : metaDoConsultor.getValores()) {
                            //caso seja a meta da empresa, somar
                            if (valorConsultor.getCodigo().equals(valor.getCodigo())) {
                                valor.setValor(valorConsultor.getValor() + valor.getValor());
                                break CONS;
                            }

                        }
                    }
                } else {
                    colaborador.setPossuiMetaFinanceira(false);
                }
            }
        } else {
            getMeta().restaurarValores();
        }
    }

    /**
     * Joao Alcides 05/03/2012
     *
     * @throws Exception
     */
    private void detalharCompetencia() throws Exception {
        listaDetalhamento = new ArrayList<DetalhamentoMetaFinanceiroVO>();
        Date data = obterDataMesAno();
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(data);

        this.getMeta().setMetaAtingida(getFacade().getMetaFinanceiroBI().obterCompetencia(inicio, fim, this.getMeta().getMesAno(),
                obterColaboradores(), this.getMeta().getEmpresa().getCodigo()));
        Map<Object, Double> competenciaPorDia = getFacade().getMetaFinanceiroBI().obterCompetenciaPorDia(inicio, fim, this.getMeta().getMesAno(),
                obterColaboradores(), this.getMeta().getEmpresa().getCodigo());
        construirListaPorDia(competenciaPorDia, true, inicio, fim);
    }

    /**
     * Joao Alcides 05/03/2012
     *
     * @throws Exception
     */
    private void detalharReceita() throws Exception {
        listaDetalhamento = new ArrayList<DetalhamentoMetaFinanceiroVO>();
        Date data = obterDataMesAno();
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(data);

        this.getMeta().setMetaAtingida(getFacade().getMetaFinanceiroBI().obterReceita(inicio, fim, obterColaboradores(),
                this.getMeta().getEmpresa().getCodigo()));
        Map<Object, Double> receitaPorDia = getFacade().getMetaFinanceiroBI().obterReceitaPorDia(inicio, fim,
                obterColaboradores(), this.getMeta().getEmpresa().getCodigo());
        construirListaPorDia(receitaPorDia, true, inicio, fim);
    }

    /**
     * Joao Alcides 02/03/2012
     *
     * @throws Exception
     */
    private void detalharFaturamento() throws Exception {
        listaDetalhamento = new ArrayList<DetalhamentoMetaFinanceiroVO>();
        Date data = obterDataMesAno();
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(data);

        this.getMeta().setMetaAtingida(getFacade().getMetaFinanceiroBI().obterFaturamento(inicio, fim, obterColaboradores(),
                this.getMeta().getEmpresa().getCodigo()));
        Map<Object, Double> faturamentoPorDia = getFacade().getMetaFinanceiroBI().obterFaturamentoPorDia(inicio, fim,
                obterColaboradores(), this.getMeta().getEmpresa().getCodigo());
        construirListaPorDia(faturamentoPorDia, true, inicio, fim);
    }

    /**
     * Joao Alcides
     *
     * @param nrDias
     * @param dia
     * @param diaMeta
     */
    private void separarValoresPorDia(Integer nrDias, Integer dia, DetalhamentoMetaFinanceiroVO diaMeta) {
        for (MetaFinanceiraEmpresaValoresVO valorMeta : this.getMeta().getValores()) {
            MetaFinanceiraEmpresaValoresVO meta = new MetaFinanceiraEmpresaValoresVO();
            meta.setCor(valorMeta.getCor());
            meta.setValor((valorMeta.getValor() / nrDias) * dia);
            meta.setObservacao(valorMeta.getObservacao());
            diaMeta.getMetas().add(meta);
        }
        MetaFinanceiraEmpresaValoresVO metaValores = MetaFinanceiraEmpresaValoresVO.verificarValorQualMetaFoiAtingida(diaMeta.getFaturamentoAcumulado(), diaMeta.getMetas());
        diaMeta.setCor(metaValores.getCor());
        diaMeta.setCorTexto(metaValores.getCorTexto());
        diaMeta.setObservacao(metaValores.getObservacao());
        diaMeta.setDiaCalculado(true);
    }

    /**
     * Joao Alcides 29/02/2012
     *
     * @throws Exception
     */
    public void zerarDatasRealizadoAcumulado() throws Exception {
        this.setDataListaFaturamentoRealizado("");
        this.setDataListaFaturamentoAcumulado("");
    }

    public void atualizarListaFaturamentoRealizadoAcumulado() throws Exception {
        atualizarDetalhamentoMetaFinan();
        if(!UteisValidacao.emptyString(this.getDataListaFaturamentoRealizado())) {
            consultarListaFaturamentoRealizado(true);
        }
        if(!UteisValidacao.emptyString(this.getDataListaFaturamentoAcumulado())) {
            consultarListaFaturamentoAcumulado(true);
        }
    }

    public void consultarListaFaturamentoRealizado() throws Exception {
        consultarListaFaturamentoRealizado(false);
    }
    public void consultarListaFaturamentoRealizado(boolean atualizar) throws Exception {
        setListaDiaDetalhada(new ArrayList<DetalhamentoDiarioMetaFinanceiroVO>());
        Date inicio = Calendario.getDataComHoraZerada(Uteis.getDate(this.getDataListaFaturamentoRealizado()));
        Date fim = Calendario.getDataComUltimaHora(Uteis.getDate(this.getDataListaFaturamentoRealizado()));
        switch (TipoConsultaMetaEnum.getTipoPorCodigo(consultarPor)) {
            case FATURAMENTO:
                setListaDiaDetalhada(getFacade().getMetaFinanceiroBI().obterListaSimplificadaFaturamento(
                        inicio,
                        fim,
                        obterColaboradores(), getMeta().getEmpresa().getCodigo()));
                break;
            case FATURAMENTO_RECEBIDO:
                ConfiguracaoFinanceiroVO configFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
                montarDetalhamentoDiario(getFacade().getMetaFinanceiroBI().obterListaSimplificadaFaturamentoRecebido(getKey(),
                        inicio, fim, obterColaboradores(),obterUsuariosResponsaveis(), getMeta().getEmpresa().getCodigo(), isConsultarRematricula(),
                        isConsultarRenovacao(), isConsultarMatricula(), consultarAtual, atualizar, configFinanceiro.isMetaFinanceiraPorFaturamento()));
                break;
            case COMPETENCIA:
                setListaDiaDetalhada(getFacade().getMetaFinanceiroBI().obterListaSimplificadaCompetencia(
                        Uteis.getDiaMesData(inicio), Uteis.getDiaMesData(fim), getMeta().getMesAno(),
                        obterColaboradores(), getMeta().getEmpresa().getCodigo()));
                break;
            case RECEITA:
                setListaDiaDetalhada(getFacade().getMetaFinanceiroBI().obterListaSimplificadaReceita(
                        inicio,
                        fim,
                        obterColaboradores(), getMeta().getEmpresa().getCodigo()));
                break;
        }
        Ordenacao.ordenarLista(getListaDiaDetalhada(), "dia");

    }

    /**
     * Joao Alcides 02/03/2012 Glauco T. Camargo 07/02/2013
     */
    private void montarDetalhamentoDiario(List<ReciboClienteConsultorVO> listaSimplificada) {
        for (ReciboClienteConsultorVO item : listaSimplificada) {
            DetalhamentoDiarioMetaFinanceiroVO ddMF = new DetalhamentoDiarioMetaFinanceiroVO();
            ddMF.setDia(item.getRecibo().getData());
            ddMF.setValor(item.getValor());
            ddMF.setContrato(item.getRecibo().getContrato().getCodigo());
            ddMF.setCliente(item.getCliente().getPessoa().getNome());
            ddMF.setResponsavelParcela(item.getResponsavelPagamento().getNome());
            if(consultarAtual){
                ddMF.setResponsavel(item.getRecibo().getUsuarioVO().getNome());
            }else{
                ddMF.setResponsavel(item.getConsultor().getPessoa().getNome());
            }
            ddMF.setFormaPagamento(item.getFormaPagamento().getDescricao());
            ddMF.setDuracaoContrato(item.getRecibo().getContrato().getDuracaoContratoApresentar());
            getListaDiaDetalhada().add(ddMF);
        }
    }

    /**
     * Joao Alcides 29/02/2012
     *
     * @throws Exception
     */
    public void consultarListaFaturamentoAcumulado() throws Exception {
        consultarListaFaturamentoAcumulado(false);
    }

    public void consultarListaFaturamentoAcumulado(boolean atualizar) throws Exception {
        setListaDiaDetalhada(new ArrayList<DetalhamentoDiarioMetaFinanceiroVO>());
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(Uteis.getDate(this.getDataListaFaturamentoAcumulado())));
        Date fim = Calendario.getDataComUltimaHora(Uteis.getDate(this.getDataListaFaturamentoAcumulado()));
        switch (TipoConsultaMetaEnum.getTipoPorCodigo(consultarPor)) {
            case FATURAMENTO:
                setListaDiaDetalhada(getFacade().getMetaFinanceiroBI().obterListaSimplificadaFaturamento(
                        inicio,
                        fim,
                        obterColaboradores(), getMeta().getEmpresa().getCodigo()));
                break;
            case FATURAMENTO_RECEBIDO:
                ConfiguracaoFinanceiroVO configFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
                montarDetalhamentoDiario(getFacade().getMetaFinanceiroBI().obterListaSimplificadaFaturamentoRecebido(getKey(), inicio,
                        fim, obterColaboradores() ,obterUsuariosResponsaveis(), getMeta().getEmpresa().getCodigo(), isConsultarRematricula(),
                        isConsultarRenovacao(), isConsultarMatricula(), consultarAtual, atualizar, configFinanceiro.isMetaFinanceiraPorFaturamento()));
                break;
            case COMPETENCIA:
                setListaDiaDetalhada(getFacade().getMetaFinanceiroBI().obterListaSimplificadaCompetencia(
                        Uteis.getDiaMesData(inicio), Uteis.getDiaMesData(fim), getMeta().getMesAno(),
                        obterColaboradores(), getMeta().getEmpresa().getCodigo()));
                break;
            case RECEITA:
                setListaDiaDetalhada(getFacade().getMetaFinanceiroBI().obterListaSimplificadaReceita(
                        inicio,
                        fim,
                        obterColaboradores(), getMeta().getEmpresa().getCodigo()));
                break;
        }
        Ordenacao.ordenarLista(getListaDiaDetalhada(), "dia");
    }

    public void setMesAtual(String mesAtual) {
        this.mesAtual = mesAtual;
    }

    public String getMesAtual() {
        return mesAtual;
    }

    public void setCorTexto(String corTexto) {
        this.corTexto = corTexto;
    }

    public String getCorTexto() {
        if (corTexto == null) {
            corTexto = "";
        }
        return corTexto;
    }

    public void setListaDetalhamento(List<DetalhamentoMetaFinanceiroVO> listaDetalhamento) {
        this.listaDetalhamento = listaDetalhamento;
    }

    public List<DetalhamentoMetaFinanceiroVO> getListaDetalhamento() {
        if (listaDetalhamento == null) {
            listaDetalhamento = new ArrayList<DetalhamentoMetaFinanceiroVO>();
        }
        return listaDetalhamento;
    }

    public void setConsultarPor(Integer consultarPor) {
        this.consultarPor = consultarPor;
    }

    public Integer getConsultarPor() {
        if (consultarPor == null) {
            consultarPor = TipoConsultaMetaEnum.FATURAMENTO_RECEBIDO.getCodigo();
        }
        return consultarPor;
    }

    public String atualizarFiltros() throws Exception {
        ConfiguracaoFinanceiroVO configFinanceiro = getFacade().getConfiguracaoFinanceiro().consultar();
        Date data = obterDataMesAno();
        Date inicio = Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(data));
        Date fim = Uteis.obterUltimoDiaMesUltimaHora(data);
        TipoConsultaMetaEnum consultaMetaEnum = TipoConsultaMetaEnum.getTipoPorCodigo(this.getConsultarPor());
        StringBuilder filtros = new StringBuilder();
        filtros.append("<b>Consultar por:</b> " + consultaMetaEnum.getDescricao());
        if (configFinanceiro.isMetaFinanceiraPorFaturamento()) {
            filtros = new StringBuilder(filtros.toString().replace("Faturamento Recebido", "Faturamento"));
        }
        List<ColaboradorVO> colaboradores = obterColaboradores();
        if (!colaboradores.isEmpty()) {
            String nomes = "";
            for (ColaboradorVO colaborador : colaboradores) {
                if (!UteisValidacao.emptyString(colaborador.getPessoa().getNome())) {
                    nomes += ", " + colaborador.getPessoa().getNome() + (colaborador.getPossuiMetaFinanceira() ? "(TEM META)" : "(NÃO TEM META)");
                }
            }
            filtros.append(" | <b>Colaborador:</b> " + nomes.replaceFirst(", ", ""));
        }
        if (configFinanceiro.isMetaFinanceiraPorFaturamento()) {
            filtros.append(" | <b>Período Faturamento:</b> " + Uteis.getData(inicio) + " a " + Uteis.getData(fim));
        }else {
            filtros.append(" | <b>Período " + consultaMetaEnum.getDescricao() + ":</b> " + Uteis.getData(inicio) + " a " + Uteis.getData(fim));
        }

        filtros.append(" | <b> Tipos de Produto:</b>");
        if (isConsultarMatricula() && isConsultarRematricula() && isConsultarRenovacao()
                || !isConsultarMatricula() && !isConsultarRematricula() && !isConsultarRenovacao()) {
            filtros.append(" Todos");
        } else {
            StringBuilder filtrosProdutos = new StringBuilder();
            if (isConsultarRenovacao()) {
                filtrosProdutos.append(", Renovação");
            }

            if (isConsultarRematricula()) {
                filtrosProdutos.append(", Rematrícula");
            }

            if (isConsultarMatricula()) {
                filtrosProdutos.append(", Matrícula");
            }
            filtrosProdutos.deleteCharAt(0);
            filtros.append(filtrosProdutos);
        }

        if (!configFinanceiro.isMetaFinanceiraPorFaturamento()) {
            if (consultarAtual) {
                filtros.append(" | <b> Consultor atual</b>");
            } else {
                filtros.append(" | <b> Consultor da época da venda</b>");
            }

            if(getFacade().getEmpresa().consultarPorChavePrimaria(this.getMeta().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS).getConsultorVendaAvulsa().getCodigo() == 0){
                filtros.append(" </br> <b>Importante:</b> Há Pagamentos de venda avulsa sem consultor vinculado. Informe um Consultor responsável (Venda Avulsa) na empresa e solicite ao nosso suporte que atualize seus recibos!");
            }

            if(getFacade().getReciboClienteConsultor().existeReciboVendaAvulsaConsumidorNaData(this.getMeta().getEmpresa().getCodigo(), inicio, fim)) {
                String valorReciboVendaAvulsaConsumidor = Uteis.arrendondarForcando2CadasDecimaisComVirgula(getFacade().getReciboClienteConsultor().consultarValoresReciboVendaAvulsaNaData(this.getMeta().getEmpresa().getCodigo(), inicio, fim));
                filtros.append(" </br> <b>Importante:</b> Há Recibos de Pagamento sem consultores provenientes de vendas ao consumidor, cujo o comprador não tem cadastro  no sistema, totalizando um valor de: " + this.getMeta().getEmpresa().getMoeda() + valorReciboVendaAvulsaConsumidor);
            }
        }
        
        return filtros.toString();
    }

    public Integer getNumeroDiasDetalhamento() {
        return this.getListaDetalhamento().size();
    }

    public void setDataListaFaturamentoRealizado(String dataListaFaturamentoRealizado) throws Exception {
        this.dataListaFaturamentoRealizado = dataListaFaturamentoRealizado;
    }

    public String getDataListaFaturamentoRealizado() {
        return dataListaFaturamentoRealizado;
    }

    public void setDataListaFaturamentoAcumulado(String dataListaFaturamentoAcumulado) {
        this.dataListaFaturamentoAcumulado = dataListaFaturamentoAcumulado;
    }

    public String getDataListaFaturamentoAcumulado() {
        return dataListaFaturamentoAcumulado;
    }

    public void setListaDiaDetalhada(List<DetalhamentoDiarioMetaFinanceiroVO> listaDiaDetalhada) {
        this.listaDiaDetalhada = listaDiaDetalhada;
    }

    public List<DetalhamentoDiarioMetaFinanceiroVO> getListaDiaDetalhada() {
        if (listaDiaDetalhada == null) {
            listaDiaDetalhada = new ArrayList<DetalhamentoDiarioMetaFinanceiroVO>();
        }
        return listaDiaDetalhada;
    }

    public String getValorDiaSelecionado() {
        for (DetalhamentoMetaFinanceiroVO l : getListaDetalhamento()) {
            if (l.getDiaComAno().equals(this.dataListaFaturamentoAcumulado)) {
                return l.getFaturamentoAcumulado_Apresentar();
            } else if (l.getDiaComAno().equals(this.dataListaFaturamentoRealizado)) {
                return l.getFaturamentoRealizado_Apresentar();
            }
        }
        return "0.0";
    }

    public String getDataAtual() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        return sdf.format(Calendario.hoje());
    }

    public String getNomeUsuarioLogado() {
        String nomeUsuario = "";
        try {
            nomeUsuario = getUsuarioLogado().getNome();
        } catch (Exception e) {

        }
        return nomeUsuario;
    }

    public Boolean getApresentarApenasMetaConsultor() throws Exception {
        if (getUsuarioLogado().getAdministrador()) {
            return false;
        }
        LoginControle loginControle = (LoginControle) JSFUtilities.getFromSession(LoginControle.class.getSimpleName());
        if (loginControle == null) {
            loginControle = new LoginControle();
        }
        if (loginControle.getPermissaoAcessoMenuVO().getVisualizaRMetaFinanceiraEmpresaBI() || loginControle.getPermissaoAcessoMenuVO().getVisualizarMetasFinanceirasTodasEmpresas()) {
            return false;
        }
        return true;
    }

    public Boolean getSemMetasApresentar() throws Exception {
        if (getUsuarioLogado().getAdministrador()
                || getUsuarioLogado().getPermissaoAcessoMenuVO().getVisualizaRMetaFinanceiraEmpresaBI()
                || !UteisValidacao.emptyNumber(getMeta().getCodigo())) {
            return false;
        }
        return true;
    }

    public Boolean getCompetencia() {
        return (TipoConsultaMetaEnum.COMPETENCIA.getCodigo().equals(consultarPor));
    }

    public Boolean getReceita() {
        return (TipoConsultaMetaEnum.RECEITA.getCodigo().equals(consultarPor));
    }

    /*-------------------------- AQUI SERÁ REVISTO ASSIM QUE FOR FEITO O MERGE DESTE RAMO COM O TRONCO--------------------------------------------- */
    private List<ColaboradorVO> obterColaboradores() throws Exception {
        List<TreeViewNode> listaNodes = (List<TreeViewNode>) JSFUtilities.getManagedBeanValue(TreeViewControle.class.getSimpleName() + ".nodesMarcados");
        List<ColaboradorVO> listaColaboradores = new ArrayList();
        if (listaNodes != null && !listaNodes.isEmpty()) {
            for (TreeViewNode treeViewNode : listaNodes) {
                if(treeViewNode.getObjeto() instanceof ColaboradorVO) {
                    ColaboradorVO colab = (ColaboradorVO) treeViewNode.getObjeto();
                    if (colab != null) {
                        colab.setColaboradorEscolhidoRenovacao(true);
                        listaColaboradores.add(colab);
                    }
                }
            }
        }
        if (!verificarMostrarTodaEmpresa() && !listaColaboradores.contains(getUsuarioLogado().getColaboradorVO())) {
            listaColaboradores.add(getUsuarioLogado().getColaboradorVO());
        }
        return listaColaboradores;
    }
    private List<UsuarioVO> obterUsuariosResponsaveis() throws Exception {
        List<TreeViewNode> listaNodes = (List<TreeViewNode>) JSFUtilities.getManagedBeanValue(TreeViewControle.class.getSimpleName() + ".nodesMarcados");
        List<UsuarioVO> listaColaboradores = new ArrayList();
        if (listaNodes != null && !listaNodes.isEmpty()) {
            for (TreeViewNode treeViewNode : listaNodes) {
                if(treeViewNode.getObjeto() instanceof UsuarioVO) {
                    UsuarioVO colab = (UsuarioVO) treeViewNode.getObjeto();
                    if (colab != null) {

                        listaColaboradores.add(colab);
                    }
                }
            }
        }
        return listaColaboradores;
    }

    public void montarResposanveisLancamentoArvore(Map<String, TreeViewNode> props,int grupo) throws Exception{
            List<UsuarioVO> usuarioVOs = getFacade().getUsuario().consultarPorNome("", false, Uteis.NIVELMONTARDADOS_TELACONSULTA);
            TreeViewNode node = new TreeViewNode(null, "USUÁRIO LANÇAMENTO PAGAMENTO");
            props.put(String.valueOf(grupo), node);
            int usuario = 1;
            usuarioVOs = Ordenacao.ordenarLista(usuarioVOs, "nome");
            for (UsuarioVO usuarioVO : usuarioVOs) {
                node = new TreeViewNode(usuarioVO, usuarioVO.getNome());
                props.put(String.valueOf(grupo) + "." + String.valueOf(usuario),
                        node);
                usuario++;
            }
    }
    public void montarArvoreColaboradores() {
        setMensagemDetalhada("");
        ComboBoxEmpresaControle comboEmpresas = (ComboBoxEmpresaControle) getControlador(
                ComboBoxEmpresaControle.class.getSimpleName());
        comboEmpresas.registrarGatilhos(new String[]{"gatilhoAtualizarArvoreEmpresaSelecionada"});
        try {
            EmpresaVO empresa = this.getMeta().getEmpresa();
            //
            List<GrupoColaboradorVO> listaGrupos = getFacade().
                    getGrupoColaborador().consultarPorCodigo(0,
                            false, Uteis.NIVELMONTARDADOS_TODOS, empresa.getCodigo());
            listaGrupos = Ordenacao.ordenarLista(listaGrupos, "descricao");
            List<Integer> colaboradoresEmGrupo = new ArrayList<>();
            Map<String, TreeViewNode> props = new HashMap<String, TreeViewNode>();
            int grupo = 1;
            Logger.getLogger(MetaFinanceiroBIControle.class.getName()).log(Level.SEVERE, "##------ MetaFinanceiroBIControle.java - INICIO montarArvoreColaboradores ------##");
            for (GrupoColaboradorVO grupoColaboradorVO : listaGrupos) {
                //apenas adicionar grupos que possuam colaboradores relacionados
                if (grupoColaboradorVO.getGrupoColaboradorParticipanteVOs().isEmpty()) {
                    continue;
                }
                TreeViewNode node = new TreeViewNode(null, grupoColaboradorVO.getDescricao());
                props.put(String.valueOf(grupo), node);

                Logger.getLogger(MetaFinanceiroBIControle.class.getName()).log(Level.SEVERE, "GRUPO COLABORADOR - " + grupo);
                Logger.getLogger(MetaFinanceiroBIControle.class.getName()).log(Level.SEVERE, grupo + " - " + grupoColaboradorVO.toString());

                List<GrupoColaboradorParticipanteVO> listaParticipantes
                        = grupoColaboradorVO.getGrupoColaboradorParticipanteVOs();
                int participante = 1;
                List<ColaboradorVO> listaColaboradorGrupo = new ArrayList();

                for (GrupoColaboradorParticipanteVO grupoColaboradorParticipanteVO : listaParticipantes) {
                    Logger.getLogger(MetaFinanceiroBIControle.class.getName()).log(Level.SEVERE, "COLABORADOR PARTICIPANTE - " + grupoColaboradorParticipanteVO.toString());
                    listaColaboradorGrupo.add(grupoColaboradorParticipanteVO.getColaboradorParticipante());
                }
                listaColaboradorGrupo = Ordenacao.ordenarLista(listaColaboradorGrupo, "pessoa");
                for (ColaboradorVO colaborador : listaColaboradorGrupo) {
                    node = new TreeViewNode(colaborador, colaborador.getPessoa().getNome());
                    props.put(String.valueOf(grupo) + "." + String.valueOf(participante),
                            node);
                    colaboradoresEmGrupo.add(colaborador.getCodigo());
                    participante++;
                }
                Logger.getLogger(MetaFinanceiroBIControle.class.getName()).log(Level.SEVERE, "-----");
                grupo++;
            }
            if(montarConsultoresSemGrupoArvore(props,grupo, empresa.getCodigo(), colaboradoresEmGrupo)){
                grupo++;
            }
            montarResposanveisLancamentoArvore(props,grupo);
            TreeViewControle treeView = (TreeViewControle) getControlador(TreeViewControle.class.getSimpleName());
            if (treeView != null) {
                treeView.limpar();
                treeView.setProps(props);
            }
            Logger.getLogger(MetaFinanceiroBIControle.class.getName()).log(Level.SEVERE, "##------ MetaFinanceiroBIControle.java - FIM montarArvoreColaboradores ------##");
            //JSFUtilities.setManagedBeanValue(TreeViewControle.class.getSimpleName()+"props", props);

        } catch (Exception ex) {
            Logger.getLogger(SmartBoxControle.class.getName()).log(Level.SEVERE, null, ex);
            setMensagemDetalhada(ex.getMessage());
        }

    }

    private boolean montarConsultoresSemGrupoArvore(Map<String, TreeViewNode> props, int grupo, Integer empresa, List<Integer> colaboradoresEmGrupo) throws Exception {
        List<ColaboradorVO> colaboradorVOS = getFacade().getColaborador().consultarPorTipoColaboradorAtivo(TipoColaboradorEnum.CONSULTOR, "AT",empresa, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        TreeViewNode node = new TreeViewNode(null, "CONSULTORES SEM GRUPO");
        props.put(String.valueOf(grupo), node);
        int consultor = 1;
        boolean grupoAdicionado = false;
        colaboradorVOS = Ordenacao.ordenarLista(colaboradorVOS, "pessoa_Apresentar");
        for (ColaboradorVO colaboradorVO : colaboradorVOS) {
            if(!colaboradoresEmGrupo.contains(colaboradorVO.getCodigo())) {
                node = new TreeViewNode(colaboradorVO, colaboradorVO.getPessoa_Apresentar());
                props.put(String.valueOf(grupo) + "." + String.valueOf(consultor),
                        node);
                consultor++;
                grupoAdicionado = true;
            }
        }
        return grupoAdicionado;
    }

    public Integer getTotalRegistrosDiaSelecionado() {
        return getListaDiaDetalhada().size();
    }

    private Boolean academiaAberta(Date dia, Date inicio, Date fim, List<Date> feriados) throws Exception {
        return (!feriados.contains(Uteis.getDataComHoraZerada(dia))
                && !((Uteis.getDiaDaSemana(dia, DiasDaSemana.SABADO)
                && !config.getAbertoSabado())
                || (Uteis.getDiaDaSemana(dia, DiasDaSemana.DOMINGO)
                && !config.getAbertoDomingo())));
    }

    public void setNumeroDiasAcademiaAberta(Integer numeroDiasAcademiaAberta) {
        this.numeroDiasAcademiaAberta = numeroDiasAcademiaAberta;
    }

    public Integer getNumeroDiasAcademiaAberta() {
        return numeroDiasAcademiaAberta;
    }

    public void setValorMetaAtingida(Double valorMetaAtingida) {
        this.valorMetaAtingida = valorMetaAtingida;
    }

    public Double getValorMetaAtingida() {
        if (valorMetaAtingida == null) {
            valorMetaAtingida = 0.0;
        }
        return valorMetaAtingida;
    }

    public String getMetaAtingida_Apresentar() {
        return Formatador.formatarValorMonetario(this.getValorMetaAtingida());
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public String getCor() {
        return cor;
    }

    public void limparConsulta() throws Exception {
        setMetas(new ArrayList<MetaFinanceiraEmpresaVO>());
        super.inicializarDados(false);
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public String getFiltros() {
        if (filtros == null) {
            filtros = "";
        }
        return filtros;
    }

    public boolean getUsuarioTemMeta() {
        try {
            Date dataPesquisa = Calendario.hoje();
            try{
                dataPesquisa = (Date) JSFUtilities.getManagedBean("BIControle.dataBaseFiltro");
            } catch (Exception e){
                dataPesquisa = Calendario.hoje();
            }
            if (UteisValidacao.emptyNumber(getEmpresaFiltroBI().getCodigo()) || !getFacade().getMetaFinanceiraConsultor().existeMetaColaboradorPorEmpresaData(getUsuarioLogado().getColaboradorVO().getCodigo(), getEmpresaFiltroBI().getCodigo(), dataPesquisa)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    public boolean isConsultarMatricula() {
        return consultarMatricula;
    }

    public void setConsultarMatricula(boolean consultarMatricula) {
        this.consultarMatricula = consultarMatricula;
    }

    public boolean isConsultarRematricula() {
        return consultarRematricula;
    }

    public void setConsultarRematricula(boolean consultarRematricula) {
        this.consultarRematricula = consultarRematricula;
    }

    public boolean isConsultarRenovacao() {
        return consultarRenovacao;
    }

    public void setConsultarRenovacao(boolean consultarRenovacao) {
        this.consultarRenovacao = consultarRenovacao;
    }

    public boolean isConsultarAtual() {
        return consultarAtual;
    }

    public void setConsultarAtual(boolean consultarAtual) {
        this.consultarAtual = consultarAtual;
    }
    
    public String getTituloConsultor(){
        return consultarAtual ? "prt_Finan_ConsultorAtual" : "prt_Finan_ConsultorResponsavel";
    }

    public void validarPermissaoImpressao(){
        try{
            limparMsg();
            setMsgAlert("");
            validarPermissaoEIncluirLogExportacao(ItemExportacaoEnum.BI_META_FINANCEIRA, null, getFiltros(), "html", "","");
            setMsgAlert("abrirPopup('impressaoRelatorioMetas.jsp', 'RelatorioMetas', 780, 595);");

        }catch (Exception e) {
            montarErro(e);
        }
    }
}
