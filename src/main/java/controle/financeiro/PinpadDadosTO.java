package controle.financeiro;

import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import org.json.JSONObject;

public class PinpadDadosTO {

    private Integer convenioCobranca;
    private Integer pinpadPedido;
    private String pinpadTipo;
    private String respostaRequisicao;

    public PinpadDadosTO() {

    }

    public PinpadDadosTO(JSONObject json) {
        this.convenioCobranca = json.optInt("convenioCobranca");
        this.pinpadPedido = json.optInt("pinpadPedido");
        this.pinpadTipo = json.optString("pinpadTipo");
        this.respostaRequisicao = json.optString("respostaRequisicao");
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public Integer getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Integer convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Integer getPinpadPedido() {
        return pinpadPedido;
    }

    public void setPinpadPedido(Integer pinpadPedido) {
        this.pinpadPedido = pinpadPedido;
    }

    public String getPinpadTipo() {
        return pinpadTipo;
    }

    public void setPinpadTipo(String pinpadTipo) {
        this.pinpadTipo = pinpadTipo;
    }

    public String getRespostaRequisicao() {
        return respostaRequisicao;
    }

    public void setRespostaRequisicao(String respostaRequisicao) {
        this.respostaRequisicao = respostaRequisicao;
    }

    public OpcoesPinpadEnum getPinpadEnum() {
        try {
            return OpcoesPinpadEnum.valueOf(this.getPinpadTipo());
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }
}
