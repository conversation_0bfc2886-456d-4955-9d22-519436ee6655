package controle.clubevantagens;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import controle.arquitetura.security.AutorizacaoFuncionalidadeControle;
import controle.arquitetura.security.AutorizacaoFuncionalidadeListener;
import controle.vendasonline.AdquiraVendasOnlineControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import relatorio.controle.arquitetura.SuperControleRelatorio;

import java.util.Date;

public class AtivarClubeVantagensControle extends SuperControleRelatorio {

    private UsuarioVO usuarioResponsavel;
    private EmpresaVO empresaLogada;
    private boolean podeAtivar;

    public AtivarClubeVantagensControle() {
        init();
    }

    public void init() {
        try {
            setUsuarioResponsavel(getUsuarioLogado() != null ? getUsuarioLogado() : new UsuarioVO());
            setEmpresaLogada(getEmpresaLogado() != null ? getEmpresaLogado() : new EmpresaVO());
        } catch (Exception e) {
            montarErro(e);
            setMsgAlert(getMensagemNotificar());
            Uteis.logar(e, AdquiraVendasOnlineControle.class);
        }
    }

    public void consultarResponsavel() {
        try {
            setUsuarioResponsavel(getFacade().getUsuario().consultarPorChavePrimaria(
                    getUsuarioResponsavel().getCodigo().intValue(),
                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            getUsuarioResponsavel().setUserOamd(getUsuarioLogado().getUserOamd());
            setMensagemID("msg_dados_consultados");
            setMensagemDetalhada("");
            setSucesso(true);
            setErro(false);
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            setSucesso(false);
            setErro(true);
        }
    }

    public void abrilModalAtivacao() {
        setMsgAlert("");
        try {
            notificarRecursoEmpresa(RecursoSistema.CLICOU_ABRIR_MODAL_ATIVAR_CLUBE_VANTAGENS);
        } catch (Exception ignore) {
        }
        setMsgAlert("Richfaces.showModalPanel('modalAtivarClube')");
    }

    public void permisaoAtivar() {

        AutorizacaoFuncionalidadeControle auto = getControlador(AutorizacaoFuncionalidadeControle.class);
        auto.setPedirPermissao(false);
        AutorizacaoFuncionalidadeListener listener = new AutorizacaoFuncionalidadeListener() {

            @Override
            public void onAutorizacaoComSucesso() {
                try {
                    setProcessandoOperacao(true);
                    acrescentarPermissoesClubeVatangens();
                    montarSucessoGrowl("");
                    notificarRecursoEmpresa(RecursoSistema.CONFIRMOU_MODAL_ATIVAR_CLUBE_VANTAGENS);
                    //registra Log
                    registrarLog("ATIVACLUBEVANTAGENS", getUsuarioResponsavel().getClienteVO().getPessoa().getCodigo(), getUsuarioResponsavel().getNome(), getUsuarioResponsavel().getUserOamd());
                    setExecutarAoCompletar("Richfaces.hideModalPanel('modalAtivarClube');Richfaces.showModalPanel('modalFinalizaClubeVantagens')");
                    setMsgAlert("Richfaces.hideModalPanel('modalAtivarClube');Richfaces.showModalPanel('modalFinalizaClubeVantagens')");
                    enviarEmailConteudoUCP();
                    setProcessandoOperacao(false);
                } catch (Exception e) {
                    setPodeAtivar(false);
                    setProcessandoOperacao(false);
                    montarErro(e);
                }
            }

            @Override
            public void onAutorizacaoComErro(Exception e) {
                montarErro(e);
            }

            @Override
            public void onFecharModalAutorizacao() {

            }
        };
        limparMsg();

        auto.autorizar("Confirmação ativação clube de vantagens", "Empresa",
                "Você precisa da permissão \"2.10 - Empresa\"",
                "modalAtivarClube,mensagem,mensagemDetalhada,modalFinalizaClubeVantagens", listener);

    }

    private void acrescentarPermissoesClubeVatangens() throws Exception {
        boolean achouPermissao = false;
        for (Object o : getUsuarioResponsavel().getUsuarioPerfilAcessoVOs()) {
            UsuarioPerfilAcessoVO usuarioPerfilAcesso = (UsuarioPerfilAcessoVO) o;
            if (usuarioPerfilAcesso.getPerfilAcesso().getNome().toUpperCase().contains("ADMINISTRADOR") ||
                    usuarioPerfilAcesso.getPerfilAcesso().getNome().toUpperCase().contains("ADMIN") ||
                    usuarioPerfilAcesso.getPerfilAcesso().getNome().toUpperCase().contains("MASTER") ||
                    usuarioPerfilAcesso.getPerfilAcesso().getNome().toUpperCase().contains("ADM") ||
                    usuarioPerfilAcesso.getPerfilAcesso().getNome().toUpperCase().contains("DONO") ||
                    usuarioPerfilAcesso.getPerfilAcesso().getNome().toUpperCase().contains("CEO") ||
                    usuarioPerfilAcesso.getPerfilAcesso().getTipo().equals(PerfilUsuarioEnum.ADMINISTRADOR)) {
                achouPermissao = true;
                setPodeAtivar(true);
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("UPDATE empresa SET trabalharcompontuacao = true WHERE codigo = " + getEmpresaLogada().getCodigo(), getFacade().getPerfilAcesso().getCon());
                } catch (Exception ignore) {
                }
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '9.60 - Configurar Clube de Vantagens','(0)(1)(2)(3)(9)(12)', "
                            + " 'ConfigurarClubeDeVantagens', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());
                } catch (Exception ignore) {
                }
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '5.66 - Cadastro de brinde','(0)(1)(2)(3)(9)(12)', "
                            + " 'Brinde', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());
                } catch (Exception ignore) {
                }
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '5.68 - Permitir lançar brinde para aluno','(0)(1)(2)(3)(9)(12)', "
                            + " 'LancamentoBrindeAluno', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());
                } catch (Exception ignore) {
                }
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '5.69 - Permitir ajuste manual de pontos','(0)(1)(2)(3)(9)(12)', "
                            + " 'AjusteManualPontos', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());
                } catch (Exception ignore) {
                }
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '5.70 - Cadastro de campanha pontuação','(0)(1)(2)(3)(9)(12)', "
                            + " 'CampanhaDuracao', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());
                } catch (Exception ignore) {
                }
                try {
                    SuperFacadeJDBC.executarConsultaUpdate("INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                            + " VALUES (2, '5.71 - Cadastro de item campanha','(0)(1)(2)(3)(9)(12)', "
                            + " 'ItemCampanha', " + usuarioPerfilAcesso.getPerfilAcesso().getCodigo() + ")", getFacade().getPerfilAcesso().getCon());

                } catch (Exception ignore) {
                }
                break;
            }
        }
        if (!achouPermissao){
            setPodeAtivar(false);
            throw new Exception("Para ativar o Clube de Vantagens é necessário que seu usuário possua um Perfil de Acesso do tipo ADMINISTRADOR.");
        }
    }

    private void enviarEmailConteudoUCP() {
        if (!UteisValidacao.emptyString(getUsuarioResponsavel().getEmail())) {
            try {
                Uteis.logar(null, "Enviando email com link para videos de aulas na ucp do clube de vantagens para " + getUsuarioResponsavel().getEmail() + " da empresa " + getEmpresaLogada().getNome());
                ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
                String[] emails = new String[1];
                emails[0] = getUsuarioResponsavel().getEmail();
                UteisEmail email = new UteisEmail();
                String assunto = "[Sistema Pacto - Recurso Ativado]";
                String html = textoEmail(getEmpresaLogada().getNome(),
                        getUsuarioResponsavel().getNome(),
                        Calendario.hoje()).toString();
                email.novo(assunto, configuracaoSistemaCRMVO);
                try {
                    email.enviarEmail(getUsuarioResponsavel().getEmail(), getUsuarioResponsavel().getNome(), html, getEmpresaLogada().getNome());
                } catch (Exception e) {
                    Uteis.logar(e.getMessage(), AtivarClubeVantagensControle.class);
                    e.getStackTrace();
                }
            } catch (Exception ex) {
                Uteis.logar(null, ex.getMessage());
                ex.getStackTrace();
            }
        }
    }

    private void registrarLog(String nomeEntidade, int codPessoa, String responsavel, String userOamd) {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("ATIVAÇÃO CLUBE DE VANTAGENS");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior("-");
            log.setValorCampoAlterado("Ativação do clube de vantagens realizada pelo click no banner de apresentação.");
            log.setOperacao("Ativação");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            getFacade().getLog().incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private StringBuffer textoEmail(String nomeEmpresa,
                                    String nomeUsuario,
                                    Date dataCadastro) {

        String dataEnvio = Uteis.getDataAtualAplicandoFormatacao("dd/MM/yyyy");
        String horaEnvio = Uteis.getDataAtualAplicandoFormatacao("HH:mm");
        StringBuffer email = new StringBuffer();
        email.append("<html>\n");
        email.append("<head>\n");
        email.append("    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n");
        // TITLE
        email.append("    <title><b>SISTEMA PACTO</b> - Ativação de recurso</title>\n");

        email.append("</head>\n");
        email.append("\n");
        email.append("<body style=\"padding: 40px 30px; font-family: Arial;\">\n");
        email.append("<div id=\"cabecalho\">\n");
        // SUBTITLE
        email.append("    <h1 style=\"font-size: 16px; text-transform: uppercase; display: inline-block; width: 50%;\">Ativação recurso Clube de Vantagens - Sistema Pacto ADM</h1>\n");

        email.append("    <div style=\"display: inline-block; width: 40%; float: right; font-size: 16px; text-align: right; padding-top: 10px;\">").append(dataEnvio).append(" <span style=\"font-size:12px\">").append(horaEnvio).append("</span></div>\n");
        email.append("    <hr>\n");
        email.append("    <div id=\"dadosRel\">\n");
        email.append("            <div><span style=\"font-weight: normal\">Olá, ").append(nomeUsuario).append("</span></div>\n");
        email.append("            <div><span style=\"font-weight: normal\">O Clube de Vantagens foi ativado em seu sistema.</span></div>\n");
        email.append("            <div><span style=\"font-weight: normal\">Agora, é só definir as melhores ações para engajar seus alunos e aumentar sua fidelização!</span></div>\n");
        email.append("            <div><span style=\"font-weight: normal\">Para configurar, assista o tutorial com o passo a passo em nossa Central de Ajuda clicando no link abaixo:</span></div>\n");
        email.append("            <div id=\"colunaA\" style=\"width:49.5%; display: inline-block; font-weight:bold\">\n");
        email.append("            <div>Link: <span style=\"font-weight: normal\">https://pactosolucoes.com.br/ajuda/conhecimento/como-trabalhar-com-o-clube-de-vantagens/</span></div>\n");
        email.append("            <div><span style=\"font-weight: normal\"Aproveite e acesse também o curso sobre o Clube de Vantagens:</span></div>\n");
        email.append("            <div id=\"colunaA\" style=\"width:49.5%; display: inline-block; font-weight:bold\">\n");
        email.append("            <div>Link: <span style=\"font-weight: normal\">https://universidade.sistemapacto.com.br/courses/enrolled/1325432</span></div>\n");
        email.append("            <div>Usuário responsável pela ativação: <span style=\"font-weight: normal\">").append(nomeUsuario).append("</span></div>\n");
        email.append("            <div>Empresa em que foi realizada a ativação: <span style=\"font-weight: normal\">").append(nomeEmpresa).append("</span></div>\n");
        email.append("            <div>Data da ativação: <span style=\"font-weight: normal\">").append(Calendario.getDataAplicandoFormatacao(dataCadastro, "dd/MM/yyyy HH:mm")).append("</span></div>\n");
        email.append("        </div>\n");
        email.append("    </div>\n");
        email.append("    <hr>\n");
        email.append("</div>\n");
        email.append("</body>\n");
        email.append("</html>");
        return email;
    }

    public UsuarioVO getUsuarioResponsavel() {
        return usuarioResponsavel;
    }

    public void setUsuarioResponsavel(UsuarioVO usuarioResponsavel) {
        this.usuarioResponsavel = usuarioResponsavel;
    }

    public EmpresaVO getEmpresaLogada() {
        return empresaLogada;
    }

    public void setEmpresaLogada(EmpresaVO empresaLogada) {
        this.empresaLogada = empresaLogada;
    }

    public boolean isPodeAtivar() {
        return podeAtivar;
    }

    public void setPodeAtivar(boolean podeAtivar) {
        this.podeAtivar = podeAtivar;
    }
}
