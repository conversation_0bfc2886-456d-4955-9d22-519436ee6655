/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package acesso.webservice.retorno;

/**
 *
 * <AUTHOR>
 */
public class RetornoRequisicaoRegistrarAcesso extends RetornoRequisicaoWS {

    Boolean acessoRegistrado = false;

    public Boolean getAcessoRegistrado() {
        return acessoRegistrado;
    }

    public void setAcessoRegistrado(Boolean acessoRegistrado) {
        this.acessoRegistrado = acessoRegistrado;
    }

        public void fromClient(negocio.comuns.acesso.webservice.client.RetornoRequisicaoRegistrarAcesso retorno) {
        try {
            setAcessoRegistrado(retorno.isAcessoRegistrado());
            ResultadoWSEnum resultadoWS = ResultadoWSEnum.valueOf(retorno.getResultado().toString());
            setResultado(resultadoWS);
            setMsgErro(retorno.getMsgErro());
            setTerminal(retorno.getTerminal());
        } catch (Exception e) {
        }

    }



}
