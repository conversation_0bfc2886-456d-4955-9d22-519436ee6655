/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.basico;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import relatorio.negocio.comuns.financeiro.CompetenciaSinteticoProdutoMesVO;
import relatorio.negocio.comuns.financeiro.FaturamentoSinteticoProdutoMesVO;

/**
 *
 * <AUTHOR>
 */
public class TicketMedioVO extends SuperVO {

    private Integer ativosVencidosInicioMes = 0;
    private Integer ativosVencidosFimMes = 0;
    private Integer dependentesInicioMes = 0;
    private Integer dependentesFimMes = 0;
    private Integer mediaAtivosVencidos = 0;
    private Integer bolsas = 0;
    private Integer ativos = 0;
    private Integer pagantes = 0;
    private Double caixaCompetencia = 0.0;
    private Double ticketCompetencia = 0.0;
    private Double caixaFaturamentoRecebido = 0.0;
    private Double ticketFaturamentoRecebido = 0.0;
    private Double ticketReceita = 0.0;
    private Double caixaReceita = 0.0;
    private Double despesaTotalMes = 0.0;
    private Double despesaPorAluno = 0.0;
    private CompetenciaSinteticoProdutoMesVO competenciaSinteticoProdutoMesVO;
    private FaturamentoSinteticoProdutoMesVO faturamentoSinteticoProdutoMesVO;
    private Date ultimaExecucaoDespesa;
    private String corDespesa = "";
    private Integer mes;
    private Integer ano;
    private Date ultimaAtualizacao;
    private String ticketReceitaFormatado = "";
    private String ticketCompetenciaFormatado = "";
    private String despesaPorAlunoFormatado = "";

    public TicketMedioVO(JSONObject json) {
        this.ativosVencidosInicioMes = json.optInt("ativosVencidosInicioMes");
        this.ativosVencidosFimMes = json.optInt("ativosVencidosFimMes");
        this.mediaAtivosVencidos = json.optInt("mediaAtivosVencidos");
        this.bolsas = json.optInt("bolsas");
        this.ativos = json.optInt("ativos");
        this.pagantes = json.optInt("pagantes");
        this.caixaCompetencia = json.optDouble("caixaCompetencia");
        this.ticketCompetencia = json.optDouble("ticketCompetencia");
        this.caixaFaturamentoRecebido = json.optDouble("caixaFaturamentoRecebido");
        this.ticketFaturamentoRecebido = json.optDouble("ticketFaturamentoRecebido");
        this.ticketReceita = json.optDouble("ticketReceita");
        this.caixaReceita = json.optDouble("caixaReceita");
        this.despesaTotalMes = json.optDouble("despesaTotalMes");
        this.despesaPorAluno = json.optDouble("despesaPorAluno");
        this.corDespesa = json.optString("corDespesa");
        this.mes = json.optInt("mes");
        this.ano = json.optInt("ano");
        this.ultimaAtualizacao = new Date(json.optLong("ultimaAtualizacao"));
        this.ticketReceitaFormatado = json.optString("ticketReceitaFormatado");
        this.ticketCompetenciaFormatado = json.optString("ticketCompetenciaFormatado");
        this.despesaPorAlunoFormatado = json.optString("despesaPorAlunoFormatado");
        this.dependentesInicioMes = json.optInt("dependentesInicioMes");
        this.dependentesFimMes = json.getInt("dependentesFimMes");
    }

    public TicketMedioVO() {
    }

    public TicketMedioVO(Integer mes, Integer ano) {
        this.ano = ano;
        this.mes = mes;
    }

    public Date getUltimaAtualizacao() {
        return ultimaAtualizacao;
    }

    public void setUltimaAtualizacao(Date ultimaAtualizacao) {
        this.ultimaAtualizacao = ultimaAtualizacao;
    }
    
    public String getUltimaAtualizacaoApresentar(){
        try {
            return Uteis.getDataComHora(ultimaAtualizacao);
        } catch (Exception e) {
            return "";
        }
    }

    public Integer getBolsas() {
        return bolsas;
    }

    public void setBolsas(Integer bolsas) {
        this.bolsas = bolsas;
    }

    public Double getCaixaCompetencia() {
        return caixaCompetencia;
    }

    public void setCaixaCompetencia(Double caixaCompetencia) {
        this.caixaCompetencia = caixaCompetencia;
    }

    public Double getCaixaFaturamentoRecebido() {
        return caixaFaturamentoRecebido;
    }

    public void setCaixaFaturamentoRecebido(Double caixaFaturamentoRecebido) {
        this.caixaFaturamentoRecebido = caixaFaturamentoRecebido;
    }

    public Double getCaixaReceita() {
        return caixaReceita;
    }

    public void setCaixaReceita(Double caixaReceita) {
        this.caixaReceita = caixaReceita;
    }

    public Double getDespesaPorAluno() {
        return despesaPorAluno;
    }

    public void setDespesaPorAluno(Double despesaPorAluno) {
        this.despesaPorAluno = despesaPorAluno;
    }

    public Double getDespesaTotalMes() {
        return despesaTotalMes;
    }

    public void setDespesaTotalMes(Double despesaTotalMes) {
        this.despesaTotalMes = despesaTotalMes;
    }

    public Integer getMediaAtivosVencidos() {
        return mediaAtivosVencidos;
    }

    public void setMediaAtivosVencidos(Integer mediaAtivosVencidos) {
        this.mediaAtivosVencidos = mediaAtivosVencidos;
    }

    public Integer getPagantes() {
        return pagantes;
    }

    public void setPagantes(Integer pagantes) {
        this.pagantes = pagantes;
    }

    public Double getTicketCompetencia() {
        return ticketCompetencia;
    }

    public void setTicketCompetencia(Double ticketCompetencia) {
        this.ticketCompetencia = ticketCompetencia;
    }

    public Double getTicketFaturamentoRecebido() {
        return ticketFaturamentoRecebido;
    }

    public void setTicketFaturamentoRecebido(Double ticketFaturamentoRecebido) {
        this.ticketFaturamentoRecebido = ticketFaturamentoRecebido;
    }

    public Double getTicketReceita() {
        return ticketReceita;
    }

    public void setTicketReceita(Double ticketReceita) {
        this.ticketReceita = ticketReceita;
    }

    public Integer getAtivosVencidosFimMes() {
        return ativosVencidosFimMes;
    }

    public void setAtivosVencidosFimMes(Integer ativosVencidosFimMes) {
        this.ativosVencidosFimMes = ativosVencidosFimMes;
    }

    public Integer getAtivosVencidosInicioMes() {
        return ativosVencidosInicioMes;
    }

    public void setAtivosVencidosInicioMes(Integer ativosVencidosInicioMes) {
        this.ativosVencidosInicioMes = ativosVencidosInicioMes;
    }

    public Integer getDependentesInicioMes() {
        return dependentesInicioMes;
    }

    public void setDependentesInicioMes(Integer dependentesInicioMes) {
        this.dependentesInicioMes = dependentesInicioMes;
    }

    public Integer getDependentesFimMes() {
        return dependentesFimMes;
    }

    public void setDependentesFimMes(Integer dependentesFimMes) {
        this.dependentesFimMes = dependentesFimMes;
    }

    public CompetenciaSinteticoProdutoMesVO getCompetenciaSinteticoProdutoMesVO() {
        return competenciaSinteticoProdutoMesVO;
    }

    public void setCompetenciaSinteticoProdutoMesVO(CompetenciaSinteticoProdutoMesVO competenciaSinteticoProdutoMesVO) {
        this.competenciaSinteticoProdutoMesVO = competenciaSinteticoProdutoMesVO;
    }

    public Date getUltimaExecucaoDespesa() {
        return ultimaExecucaoDespesa;
    }

    public void setUltimaExecucaoDespesa(Date ultimaExecucaoDespesa) {
        this.ultimaExecucaoDespesa = ultimaExecucaoDespesa;
    }

    public String getUltimaExecucaoApresentar(){
        try {
            return Uteis.getDataComHora(ultimaExecucaoDespesa);
        } catch (Exception e) {
            return "";
        }
        
    }

    public FaturamentoSinteticoProdutoMesVO getFaturamentoSinteticoProdutoMesVO() {
        return faturamentoSinteticoProdutoMesVO;
    }

    public void setFaturamentoSinteticoProdutoMesVO(FaturamentoSinteticoProdutoMesVO faturamentoSinteticoProdutoMesVO) {
        this.faturamentoSinteticoProdutoMesVO = faturamentoSinteticoProdutoMesVO;
    }

    public String getCorDespesa() {
        return corDespesa;
    }

    public void setCorDespesa(String corDespesa) {
        this.corDespesa = corDespesa;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getAtivos() {
        return ativos;
    }

    public void setAtivos(Integer ativos) {
        this.ativos = ativos;
    }

    public static String formataMoeda(double vlr){
        DecimalFormat nf = new DecimalFormat("#,##0.00");
        return nf.format(vlr);
    }

    public String getTicketReceitaFormatado() {
        return formataMoeda(ticketReceita);
    }

    public void setTicketReceitaFormatado(String ticketReceitaFormatado) {
        this.ticketReceitaFormatado = ticketReceitaFormatado;
    }

    public String getTicketCompetenciaFormatado() {
        return formataMoeda(ticketCompetencia);
    }

    public void setTicketCompetenciaFormatado(String ticketCompetenciaFormatado) {
        this.ticketCompetenciaFormatado = ticketCompetenciaFormatado;
    }

    public String getDespesaPorAlunoFormatado() {
        return formataMoeda(despesaPorAluno);
    }

    public void setDespesaPorAlunoFormatado(String despesaPorAlunoFormatado) {
        this.despesaPorAlunoFormatado = despesaPorAlunoFormatado;
    }

    public String toJSON() {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(this);
        } catch (Exception e) {
            Logger.getLogger(SuperJSON.class.getName()).log(Level.SEVERE, e.getMessage());
            return null;
        }
    }
}
