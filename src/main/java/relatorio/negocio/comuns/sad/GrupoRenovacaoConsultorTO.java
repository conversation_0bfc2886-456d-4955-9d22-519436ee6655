
package relatorio.negocio.comuns.sad;

import java.util.ArrayList;
import java.util.List;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR>
 */
public class GrupoRenovacaoConsultorTO extends SuperTO {
    private String descricao = "";
    private int qtdePrevista = 0;
    private int qtdeRenovados = 0;
    private List<RenovacaoConsultorTO> listaConsultores = new ArrayList<RenovacaoConsultorTO>();

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<RenovacaoConsultorTO> getListaConsultores() {
        return listaConsultores;
    }

    public void setListaConsultores(List<RenovacaoConsultorTO> listaConsultores) {
        this.listaConsultores = listaConsultores;
    }

    public int getQtdePrevista() {
        return qtdePrevista;
    }

    public void setQtdePrevista(int qtdePrevista) {
        this.qtdePrevista = qtdePrevista;
    }

    public int getQtdeRenovados() {
        return qtdeRenovados;
    }

    public void setQtdeRenovados(int qtdeRenovados) {
        this.qtdeRenovados = qtdeRenovados;
    }

    public boolean getAlert() {
        return (getPercentualRenovados() < 50.0);
    }

    public double getPercentualRenovados() {
        double total = getQtdePrevista();
        double renovados = getQtdeRenovados();
        if(total == 0) return 0;
        return Uteis.arredondarForcando2CasasDecimais(renovados*100/total);
    }

    public boolean equals(Object obj) {
        if(obj instanceof GrupoRenovacaoConsultorTO && obj != null) {
            GrupoRenovacaoConsultorTO aux = (GrupoRenovacaoConsultorTO)obj;
            return this.getDescricao().equals(aux.getDescricao());
        }
        return false;
    }
}
