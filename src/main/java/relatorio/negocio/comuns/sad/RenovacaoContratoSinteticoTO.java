package relatorio.negocio.comuns.sad;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.HistoricoVinculoVO;
import negocio.comuns.contrato.ContratoVO;

import java.util.Date;
import java.util.List;

/**
 * Transfer Object com todos os dados do relatório sintético de renovação de contrato
 *
 * <AUTHOR>
 * @since 06/08/2018
 */
public class RenovacaoContratoSinteticoTO extends SuperTO {

    private ContratoVO contratoVO;
    private List<HistoricoVinculoVO> historicosVinculoVO;
    private Integer diasSemAcesso = 0;
    private Date ultimoContatoCRM;
    private Double mediaAcessos4semanas = 0.0;
    private Integer checkin4semanas = 0;
    private String professores ="";

    public Integer getDiasSemAcesso() {
        return diasSemAcesso;
    }

    public void setDiasSemAcesso(Integer diasSemAcesso) {
        this.diasSemAcesso = diasSemAcesso;
    }

    public Date getUltimoContatoCRM() {
        return ultimoContatoCRM;
    }

    public void setUltimoContatoCRM(Date ultimoContatoCRM) {
        this.ultimoContatoCRM = ultimoContatoCRM;
    }

    public Double getMediaAcessos4semanas() {
        return mediaAcessos4semanas;
    }

    public void setMediaAcessos4semanas(Double mediaAcessos4semanas) {
        this.mediaAcessos4semanas = mediaAcessos4semanas;
    }

    public Integer getCheckin4semanas() {
        return checkin4semanas;
    }

    public void setCheckin4semanas(Integer checkin4semanas) {
        this.checkin4semanas = checkin4semanas;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public List<HistoricoVinculoVO> getHistoricosVinculoVO() {
        return historicosVinculoVO;
    }

    public void setHistoricosVinculoVO(List<HistoricoVinculoVO> historicosVinculoVO) {
        this.historicosVinculoVO = historicosVinculoVO;
    }

    public String getProfessores() {
        return professores;
    }

    public void setProfessores(String professores) {
        this.professores = professores;
    }
}
