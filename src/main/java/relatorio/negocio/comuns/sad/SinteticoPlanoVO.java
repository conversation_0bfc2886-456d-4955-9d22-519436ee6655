/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package relatorio.negocio.comuns.sad;

import java.util.Iterator;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class SinteticoPlanoVO extends SuperVO {
    
    protected String nome;
    protected Boolean marcado;
    protected Integer chavePrimaria;

    public SinteticoPlanoVO() {
        super();
        inicializarDados();
    }

    public void inicializarDados(){
        setNome("");
        setMarcado(new Boolean(false));
        setChavePrimaria(new Integer(0));
    }

     public void adicionarObjClienteSituacaoVOs(List listaPlano, SinteticoPlanoVO obj) throws Exception {
        int index = 0;
        Iterator i = listaPlano.iterator();
        while (i.hasNext()) {
            SinteticoPlanoVO objExistente = (SinteticoPlanoVO) i.next();
            if (objExistente.getChavePrimaria().equals(obj.getChavePrimaria())) {
                listaPlano.set(index, obj);
                return;
            }
            index++;
        }
        listaPlano.add(obj);
    }

    public Integer getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(Integer chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public Boolean getMarcado() {
        return marcado;
    }

    public void setMarcado(Boolean marcado) {
        this.marcado = marcado;
    }

    public String getNome() {
        if(nome == null){
            nome = "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
