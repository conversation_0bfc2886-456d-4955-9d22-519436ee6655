/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class FaturamentoSinteticoMesVO extends SuperVO {

    protected List<FaturamentoSinteticoProdutoMesVO> listaProdutoXMes;
    protected Integer mes;
    protected Integer ano;

    public FaturamentoSinteticoMesVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setMes(new Integer(0));
        setAno(new Integer(0));
        setListaProdutoXMes(new ArrayList<FaturamentoSinteticoProdutoMesVO>());
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public List<FaturamentoSinteticoProdutoMesVO> getListaProdutoXMes() {
        return listaProdutoXMes;
    }

    public void setListaProdutoXMes(List<FaturamentoSinteticoProdutoMesVO> listaProdutoXMes) {
        this.listaProdutoXMes = listaProdutoXMes;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

}