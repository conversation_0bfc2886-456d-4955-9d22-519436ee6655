/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.ProdutoVO;

/**
 *
 * <AUTHOR>
 */
public class CompetenciaSinteticoProdutoVO extends SuperVO {

    protected ProdutoVO produto;
    protected String descricao;
    protected Integer duracao;
    protected Integer plano;
    private String nomePlano;
    protected List<CompetenciaSinteticoProdutoMesVO> listaProdutoXMes;

    public CompetenciaSinteticoProdutoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setProduto(new ProdutoVO());
        setDuracao(new Integer(0));
        setPlano(new Integer(0));
        setListaProdutoXMes(new ArrayList<CompetenciaSinteticoProdutoMesVO>());
        setNomePlano("");
    }

    public List<CompetenciaSinteticoProdutoMesVO> getListaProdutoXMes() {
        return listaProdutoXMes;
    }

    public void setListaProdutoXMes(List<CompetenciaSinteticoProdutoMesVO> listaProdutoXMes) {
        this.listaProdutoXMes = listaProdutoXMes;
    }

    public ProdutoVO getProduto() {
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getDuracao() {
        return duracao;
    }

    public void setDuracao(Integer duracao) {
        this.duracao = duracao;
    }

    public Integer getPlano() {
        return plano;
    }

    public void setPlano(Integer plano) {
        this.plano = plano;
    }

    /**
     * @return the nomePlano
     */
    public String getNomePlano() {
        return nomePlano;
    }

    /**
     * @param nomePlano the nomePlano to set
     */
    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    

}