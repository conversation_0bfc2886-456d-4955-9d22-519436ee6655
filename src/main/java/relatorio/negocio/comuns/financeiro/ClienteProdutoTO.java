package relatorio.negocio.comuns.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

/**
 * Created by glauco on 02/04/2014.
 */
public class ClienteProdutoTO extends SuperTO {

    private Integer codigoCliente = 0;
    private String matriculaCliente = "";
    private String nomeCliente = "";
    private String telefonesCliente = "";
    private String descricaoProduto = "";
    private Date validadeProduto = null;
    private Double valorProduto = 0.0;
    private String responsavellancamento = "";
    private String nomeEmpresa;


    public String getMatriculaCliente() {
        return matriculaCliente;
    }

    public void setMatriculaCliente(String matriculaCliente) {
        this.matriculaCliente = matriculaCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getDescricaoProduto() {
        return descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public Date getValidadeProduto() {
        return validadeProduto;
    }

    public void setValidadeProduto(Date validadeProduto) {
        this.validadeProduto = validadeProduto;
    }

    public String getValidadeProduto_Apresentar() {
        return Uteis.getDataAplicandoFormatacao(getValidadeProduto(), "dd/MM/yyyy");
    }

    public Double getValorProduto() {
        return valorProduto;
    }

    public void setValorProduto(Double valorProduto) {
        this.valorProduto = valorProduto;
    }

    public String getValorProduto_Apresentar() {
        return Formatador.formatarValorMonetario(getValorProduto());
    }

    public String getTelefonesCliente() {
        return telefonesCliente.replace("{", "").replace("}", "");
    }

    public void setTelefonesCliente(String telefonesCliente) {
        this.telefonesCliente = telefonesCliente;
    }

    public Integer getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(Integer codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    /**
     * @return the responsavellancamento
     */
    public String getResponsavellancamento() {
        return responsavellancamento;
    }

    /**
     * @param responsavellancamento the responsavellancamento to set
     */
    public void setResponsavellancamento(String responsavellancamento) {
        this.responsavellancamento = responsavellancamento;
    }

    public String getNomeEmpresa() {
        if (nomeEmpresa == null) {
            nomeEmpresa = "";
        }
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }
}
