package relatorio.negocio.jdbc.basico;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.basico.ClientePorDuracaoVO;
import relatorio.negocio.comuns.basico.ResumoClientePorDuracaoVO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

public class ClientePorDuracaoRel extends SuperRelatorio {

    protected Date dataPeriodo;
    protected Boolean situacaoNormal;
    protected Boolean situacaoAvencer;
    protected Boolean situacaoAtestado;
    protected Boolean situacaoCarencia;
    protected Boolean situacaoVencido;
    protected Boolean situacaoTrancado;
    private Boolean semBolsa;
    private ClientePorDuracaoVO clientePorDuracaoVO;
    protected Integer totalizadorPessoa;

    public ClientePorDuracaoRel() throws Exception {
        super();
        setIdEntidade("CaixaPorOperadorRel");
        try {
            inicializar();
            inicializarParametros();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     *Metodo que inicializar os Parametros para o Relatório.
     */
    public void inicializarParametros() {
        setDataPeriodo(negocio.comuns.utilitarias.Calendario.hoje());
        setSituacaoNormal(new Boolean(false));
        setSituacaoAtestado(new Boolean(false));
        setSituacaoAvencer(new Boolean(false));
        setSituacaoCarencia(new Boolean(false));
        setSituacaoVencido(new Boolean(false));
        setSituacaoTrancado(new Boolean(false));
        setSemBolsa(new Boolean(false));
        setTotalizadorPessoa(new Integer(0));
        setClientePorDuracaoVO(new ClientePorDuracaoVO());
    }

    /**
     * Valida os campos obrigatórios Para a emissão do relatório
     * @throws negocio.comuns.utilitarias.ConsistirException Caso algum campo esteja faltando é mostrado o erro.
     */
    public void validarDados() throws ConsistirException {
        if (getDataPeriodo() == null) {
            throw new ConsistirException("Não é possível emitir o relátorio. Informe primeiro o Período De Início da Pesquisa.");
        }
    }

    public List<ResumoClientePorDuracaoVO> consultarDuracaoPorPeriodoSituacaoContratoAnaliticoDW(String situacaoContratoAnalitico, Integer empresa, boolean comBolsa) throws Exception {
        List<ResumoClientePorDuracaoVO> lista = new ArrayList();
        StringBuilder sql = new StringBuilder();

        try {
            if (situacaoContratoAnalitico.equals("CR") //carencia
                    || situacaoContratoAnalitico.equals("AT") //atestado
                    || situacaoContratoAnalitico.equals("TR") //trancamento
                    || situacaoContratoAnalitico.equals("TV"))//trancamento vencido
            {

                sql.append("Select count(o.contrato)as quantidade, contrato.empresa, contratoduracao.numeromeses as numeromeses  \n");
                sql.append("from contratooperacao  as o  \n");
                sql.append("inner join contrato on contrato.codigo = o.contrato ");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and contrato.empresa = ").append(empresa).append(" \n");
                }
                sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo  \n");
                sql.append("where o.tipooperacao in (%s)  \n");
                sql.append("and '%s' between o.datainicioefetivacaooperacao  \n");
                sql.append("and o.datafimefetivacaooperacao ").append(comBolsa ? "" : "and contrato.bolsa = false  \n");
                sql.append("GROUP BY numeromeses,contrato.empresa order by numeromeses");

            } else if (situacaoContratoAnalitico.equals("NO")) { //ativos normais

                sql.append("Select count(c.codigo)as quantidade, c.empresa, contratoduracao.numeromeses as numeromeses  \n");
                sql.append("from contrato c  \n");
                sql.append("inner join contratoduracao on contratoduracao.contrato = c.codigo  \n");
                sql.append("where 1 = 1 \n");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and c.empresa = ").append(empresa).append(" \n");
                }
                sql.append("and '%s' between c.vigenciaDe and c.vigenciaAteAjustada  \n");
                sql.append("and not exists (select h.contrato from historicocontrato h  \n");
                sql.append("where h.contrato = c.codigo   \n");
                sql.append("and '%s' between h.datainiciosituacao and h.datafinalsituacao and h.tipohistorico in ('AV', 'VE', 'CA', 'DE'))  \n");
                sql.append("and not exists (select o.contrato from contratooperacao  o  \n");
                sql.append("where o.contrato = c.codigo and o.tipooperacao in ('AT','CR', 'TR','TV')  \n");
                sql.append("and '%s' >= o.datainicioefetivacaooperacao and '%s' <= o.datafimefetivacaooperacao)  \n");
                sql.append((comBolsa ? "" : "and c.bolsa = false \n"));
                sql.append("GROUP BY numeromeses,c.empresa order by numeromeses \n");


            } else {//vencido, a vencer

                sql.append("Select count(h.contrato)as quantidade, contrato.empresa, contratoduracao.numeromeses as numeromeses  \n");
                sql.append("from historicocontrato  as h  \n");
                sql.append("inner join contrato on contrato.codigo = h.contrato ");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append("and contrato.empresa = ").append(empresa).append(" \n");
                }
                sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo  \n");
                sql.append("where h.tipohistorico = %s  \n");
                sql.append("and '%s' between h.datainiciosituacao  \n");
                sql.append("and h.datafinalsituacao ").append(comBolsa ? "" : "and contrato.bolsa = false  \n");
                sql.append("GROUP BY numeromeses,contrato.empresa order by numeromeses \n");
            }

            String sqlConsulta = "";
            Statement sqlConsultar = con.createStatement();
            if (situacaoContratoAnalitico.equals("NO")) {
                sqlConsulta = String.format(sql.toString(),
                        Uteis.getDataJDBC(getDataPeriodo()),
                        Uteis.getDataJDBC(getDataPeriodo()),
                        Uteis.getDataJDBC(getDataPeriodo()),
                        Uteis.getDataJDBC(getDataPeriodo()));
            } else {
                sqlConsulta = String.format(sql.toString(),
                        situacaoContratoAnalitico.equals("TR") ? "'TR', 'TV'": "'"+situacaoContratoAnalitico+"'",
                        Uteis.getDataJDBC(getDataPeriodo()));
            }

            ResultSet tabelaResultado = sqlConsultar.executeQuery(sqlConsulta);
            while (tabelaResultado.next()) {
                ResumoClientePorDuracaoVO obj = new ResumoClientePorDuracaoVO();
                obj.setNumeroMeses(tabelaResultado.getInt("numeromeses"));
                obj.setQuantidade(tabelaResultado.getInt("quantidade"));
                obj.setSituacao(situacaoContratoAnalitico);
                obj.getEmpresaVO().setCodigo(tabelaResultado.getInt("empresa"));
                setTotalizadorPessoa(getTotalizadorPessoa() + tabelaResultado.getInt("quantidade"));
                lista.add(obj);
                obj = null;
            }
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Método usado para buscar e listar as pessoas de acordo com a situação dos contratos marcados no formulário
     * e considerando todos os tipos de planos (plano, bolsa)
     * @param situacaoContratoAnalitico
     * @param empresa
     * @param objs
     * @return
     * @throws Exception
     */
    public List<ClientePorDuracaoVO> consultarPessoasParametrizado(String situacaoContratoAnalitico, Integer empresa, boolean comBolsa, ResumoClientePorDuracaoVO objs) throws Exception {
        List<ClientePorDuracaoVO> lista = new ArrayList<ClientePorDuracaoVO>();
        try {

            String sql = consultaClientesPorDuracao(situacaoContratoAnalitico, comBolsa, objs, empresa, getDataPeriodo());

            PreparedStatement sqlConsultar = con.prepareStatement(sql);
            ResultSet tabelaResultado = sqlConsultar.executeQuery();
            while (tabelaResultado.next()) {
                ClientePorDuracaoVO obj = new ClientePorDuracaoVO();
                ClienteVO cli = getFacade().getCliente().consultarPorCodigoPessoa(
                        tabelaResultado.getInt("codigopessoa"),
                        Uteis.NIVELMONTARDADOS_MINIMOS);
                obj.setClienteVO(cli);
                obj.getPessoaVO().setNome(tabelaResultado.getString("nome"));
                obj.setDescricao(tabelaResultado.getString("descricao"));// a lista que passo como parametro já possui a quantidade de mês, intaum eu naum consulto esse cara, apenas seto o valor que ja tenho
                obj.getContratoVO().setCodigo(tabelaResultado.getInt("contratocodigo"));
                obj.setNumeroMeses(objs.getNumeroMeses());// a lista que passo como parametro já possui a quantidade de mês, intaum eu naum consulto esse cara, apenas seto o valor que ja tenho
                obj.getContratoVO().setVigenciaDe(tabelaResultado.getTimestamp("datainicial"));
                obj.getContratoVO().setVigenciaAteAjustada(tabelaResultado.getTimestamp("datafinal"));
                obj.getContratoVO().setNomeModalidades(tabelaResultado.getString("modalidade"));
                obj.setSituacao(objs.getSituacao());// a lista que passo como parametro já possui a situacao, intaum eu naum consulto esse cara, apenas seto o valor que ja tenho
                lista.add(obj);
                obj = null;
            }
            return lista;
        } catch (Exception e) {
            throw e;
        }
    }

    public static String consultaClientesPorDuracao(String situacaoContratoAnalitico, boolean comBolsa, ResumoClientePorDuracaoVO objs, int empresa, java.util.Date dataPeriodo) throws Exception {

        if (situacaoContratoAnalitico.equals("CR")//carencia
                || situacaoContratoAnalitico.equals("AT")//atestado
                || situacaoContratoAnalitico.equals("TR")//trancamento
                || situacaoContratoAnalitico.equals("TV"))//trancamento vencido
            {

                StringBuilder sql = new StringBuilder();
                sql.append(" Select ");
                sql.append("pessoa.nome, ");
                sql.append("plano.descricao,");
                sql.append("contrato.codigo as contratocodigo , ");
                sql.append("cast(contrato.vigenciade as date) as datainicial, ");
                sql.append("cast(contrato.vigenciaateajustada as date) as datafinal, ");
                sql.append("contrato.nomemodalidades as modalidade, ");
                sql.append("pessoa.codigo as codigopessoa ");
                sql.append("from contratooperacao as o  ");
                sql.append("inner join contrato on contrato.codigo = o.contrato ");
                sql.append("inner join pessoa on pessoa.codigo = contrato.pessoa ");
                sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo and contratoduracao.numeromeses = ").append(objs.getNumeroMeses()).append(" ");
                sql.append("inner join plano on plano.codigo = contrato.plano and plano.descricao like ('").append(objs.getDescricao()).append("%')  ");
                sql.append("where (o.tipooperacao = '").append(situacaoContratoAnalitico.toUpperCase()).append("' ");
                if (situacaoContratoAnalitico.equals("TR")) {
                    sql.append(" or o.tipooperacao = 'TV' ");
                }
                sql.append(") ");
                if (!UteisValidacao.emptyNumber(empresa)) {
                    sql.append(" and contrato.empresa =  ").append(empresa);
                }
                sql.append(comBolsa ? "" : " and contrato.bolsa = false ");
                sql.append(" and  '").append(Uteis.getDataJDBC(dataPeriodo)).append("' between o.datainicioefetivacaooperacao and o.datafimefetivacaooperacao ");
                sql.append("order by pessoa.nome ");
                return sql.toString();

        } else if (situacaoContratoAnalitico.equals("NO")) {//ativos normais

            StringBuilder sql = new StringBuilder();
            sql.append(" Select distinct ");
            sql.append("pessoa.nome, ");
            sql.append("plano.descricao,");
            sql.append("contrato.codigo as contratocodigo , ");
            sql.append("cast( contrato.vigenciade as date) as datainicial, ");
            sql.append("cast( contrato.vigenciaateajustada as date) as datafinal, ");
            sql.append("contrato.nomemodalidades as modalidade, ");
            sql.append("pessoa.codigo as codigopessoa ");
            sql.append("from historicocontrato  as h  ");
            sql.append("inner join contrato on contrato.codigo = h.contrato ");
            sql.append("inner join pessoa on pessoa.codigo = contrato.pessoa ");
            sql.append("inner join contratoduracao on contratoduracao.contrato = contrato.codigo  and contratoduracao.numeromeses = ").append(objs.getNumeroMeses()).append(" ");
            sql.append("inner join plano on plano.codigo = contrato.plano and plano.descricao like ('").append(objs.getDescricao()).append("%')  ");
            sql.append("where 1 = 1 ");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and contrato.empresa =  ").append(empresa).append(" ");
            }
            sql.append("and '").append(Uteis.getDataJDBC(dataPeriodo)).append("' between contrato.vigenciaDe and contrato.vigenciaAteAjustada ");
            sql.append("and contrato.codigo not in (select h.contrato from historicocontrato h ");
            sql.append("where h.tipohistorico in ('AV','VE','CA','DE') ");
            sql.append("and '").append(Uteis.getDataJDBC(dataPeriodo)).append("' between h.datainiciosituacao and h.datafinalsituacao) ");
            sql.append("and contrato.codigo not in (select o.contrato from contratooperacao  o ");
            sql.append("where o.tipooperacao in ('TR', 'CR', 'AT', 'TV') ");
            sql.append("and '").append(Uteis.getDataJDBC(dataPeriodo)).append("' >= o.datainicioefetivacaooperacao and '").append(Uteis.getDataJDBC(dataPeriodo)).append("' <= o.datafimefetivacaooperacao) ");
            sql.append((comBolsa ? "" : "and contrato.bolsa = false "));
            sql.append("order by pessoa.nome ");
            return sql.toString();

        } else {//vencido, a vencer, Retorno Carência, Retorno Atestado, Retorno Trancamento

            StringBuilder sql = new StringBuilder();
            sql.append(" Select ");
            sql.append("pessoa.nome, ");
            sql.append("plano.descricao,");
            sql.append("contrato.codigo as contratocodigo , ");
            sql.append("cast( contrato.vigenciade as date) as datainicial, ");
            sql.append("cast( contrato.vigenciaateajustada as date) as datafinal, ");
            sql.append("contrato.nomemodalidades as modalidade, ");
            sql.append("pessoa.codigo as codigopessoa ");
            sql.append("from historicocontrato  as h  ");
            sql.append("inner join contrato on contrato.codigo = h.contrato ");
            sql.append("inner join pessoa on pessoa.codigo = contrato.pessoa ");
            sql.append("inner join contratoduracao on contratoduracao.contrato = h.contrato  and contratoduracao.numeromeses = ").append(objs.getNumeroMeses()).append(" ");
            sql.append("inner join plano on plano.codigo = contrato.plano and plano.descricao like ('").append(objs.getDescricao()).append("%')  ");
            sql.append("where h.tipohistorico = '").append(situacaoContratoAnalitico).append("' ").append(comBolsa ? "" : " and contrato.bolsa = false ");
            if (!UteisValidacao.emptyNumber(empresa)) {
                sql.append("and contrato.empresa =  ").append(empresa).append(" ");
            }
            sql.append("and  '").append(Uteis.getDataJDBC(dataPeriodo)).append("' between h.datainiciosituacao and h.datafinalsituacao ");
            sql.append("and contrato.codigo not in (select h.contrato from historicocontrato h ");
            sql.append("where h.tipohistorico in ('CA','DE') and '").append(Uteis.getDataJDBC(dataPeriodo)).append("' between h.datainiciosituacao and h.datafinalsituacao ) ");
            sql.append("order by pessoa.nome ");
            return sql.toString();
        }
    }

    public ClientePorDuracaoVO getClientePorDuracaoVO() {
        return clientePorDuracaoVO;
    }

    public void setClientePorDuracaoVO(ClientePorDuracaoVO clientePorDuracaoVO) {
        this.clientePorDuracaoVO = clientePorDuracaoVO;
    }

    public String getDataPeriodo_Apresentar() {
        return Uteis.getData(dataPeriodo);
    }

    public Date getDataPeriodo() {
        return dataPeriodo;
    }

    public void setDataPeriodo(Date dataPeriodo) {
        this.dataPeriodo = dataPeriodo;
    }

    public Boolean getSituacaoAtestado() {
        return situacaoAtestado;
    }

    public void setSituacaoAtestado(Boolean situacaoAtestado) {
        this.situacaoAtestado = situacaoAtestado;
    }

    public Boolean getSituacaoNormal() {
        return situacaoNormal;
    }

    public void setSituacaoNormal(Boolean situacaoNormal) {
        this.situacaoNormal = situacaoNormal;
    }

    public Boolean getSituacaoAvencer() {
        return situacaoAvencer;
    }

    public void setSituacaoAvencer(Boolean situacaoAvencer) {
        this.situacaoAvencer = situacaoAvencer;
    }

    public Boolean getSituacaoCarencia() {
        return situacaoCarencia;
    }

    public void setSituacaoCarencia(Boolean situacaoCarencia) {
        this.situacaoCarencia = situacaoCarencia;
    }

    public Boolean getSituacaoTrancado() {
        return situacaoTrancado;
    }

    public void setSituacaoTrancado(Boolean situacaoTrancado) {
        this.situacaoTrancado = situacaoTrancado;
    }

    public Boolean getSituacaoVencido() {
        return situacaoVencido;
    }

    public void setSituacaoVencido(Boolean situacaoVencido) {
        this.situacaoVencido = situacaoVencido;
    }

    public Integer getTotalizadorPessoa() {
        return totalizadorPessoa;
    }

    public void setTotalizadorPessoa(Integer totalizadorPessoa) {
        this.totalizadorPessoa = totalizadorPessoa;
    }

    /**
     * @return the semBolsa
     */
    public Boolean getSemBolsa() {
        return semBolsa;
    }

    /**
     * @param semBolsa the semBolsa to set
     */
    public void setSemBolsa(Boolean semBolsa) {
        this.semBolsa = semBolsa;
    }
}
