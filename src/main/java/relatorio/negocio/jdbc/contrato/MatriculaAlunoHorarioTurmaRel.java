/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.contrato;

import java.io.File;
import java.lang.reflect.Method;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import negocio.comuns.basico.AulaDesmarcadaVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.PresencaVO;
import negocio.comuns.contrato.ContratoPlanoProdutoSugeridoVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.comuns.plano.ConsultarTurmaTO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.contrato.MatriculaAlunoHorarioTurma;
import relatorio.negocio.comuns.contrato.ItemMatriAlunoHorarioTurmaVO;
import relatorio.negocio.comuns.contrato.MatriculaAlunoHorarioTurmaRelVO;
import relatorio.negocio.jdbc.arquitetura.SuperRelatorio;

/**
 *
 * <AUTHOR>
 */
public class MatriculaAlunoHorarioTurmaRel extends SuperRelatorio {

    protected Date dataInicio;
    protected Date dataTermino;
    protected TurmaVO turma;
    protected ColaboradorVO professor;
    protected EmpresaVO empresa;
    private ConsultarTurmaTO consultarTurma = new ConsultarTurmaTO();

    public MatriculaAlunoHorarioTurmaRel() throws Exception {
        super();
        setIdEntidade("ListaChamada");
        inicializarParametros();
    }

    private void inicializarParametros() {
        setEmpresa(new EmpresaVO());
        setDataInicio(negocio.comuns.utilitarias.Calendario.hoje());
        setDataTermino(Uteis.obterDataFutura2(dataInicio, 29));
        setProfessor(new ColaboradorVO());
        setTurma(new TurmaVO());

    }

    public void validarDados() throws Exception {
        // datainicio iqual a data termino
        if (getEmpresa().getCodigo() == 0) {
            throw new ConsistirException("Uma Empresa deve ser selecionada !");
        }
        if (dataInicio.compareTo(dataTermino) == 0) {
            throw new ConsistirException("Não é possível emitir o relátorio para o período de datas informado. Data Início igual a Data Final");
        }
        if (dataInicio.compareTo(dataTermino) > 0) {
            throw new ConsistirException("Não é possível emitir o relátorio para o período de datas informado. Data Início maior que Data Final");
        }

        long dias = Uteis.nrDiasEntreDatas(dataInicio, dataTermino);
        dias = dias + 1;
        if (dias > 31) {
            throw new ConsistirException("Não é possível emitir o relatório para o período de datas informado. Intervalo de dias superior a 31 dias.");
        }

        if (getDataInicio().compareTo(getDataTermino()) > 0) {
            throw new Exception("A Data de Início deve ser menor que a Data De Término para pesquisa.");
        }
    }

    public List<MatriculaAlunoHorarioTurmaVO> consultarListaChamada() throws Exception {
        validarDados();
        return consultar(empresa.getCodigo(), turma.getCodigo(), professor.getCodigo(), dataInicio, dataTermino);
    }

    public List<MatriculaAlunoHorarioTurmaRelVO> consultarAlunoHorarioTurma(List lista, boolean presencas) throws Exception {
        // lista de alunos por horario
        List listaObjetosConcat = new ArrayList();
        // lista de horarios
        List<MatriculaAlunoHorarioTurmaRelVO> listaRelVO = new ArrayList<MatriculaAlunoHorarioTurmaRelVO>();
        int sequencial = 1;
        MatriculaAlunoHorarioTurmaVO matricula;

        // percorre todos os elementos da lista
        while (!lista.isEmpty()) {
            int var = 0;
            // pega o primeiro elemento da lista
            matricula = (MatriculaAlunoHorarioTurmaVO) lista.get(var);
            // adiciona a lista de alunos
            listaObjetosConcat.add(matricula);
            // remove da lista principal
            lista.remove(var);

            // percorre a lista procurando por matriculas no mesmo horario
            while (lista.size() != var) {
                MatriculaAlunoHorarioTurmaVO h = (MatriculaAlunoHorarioTurmaVO) lista.get(var++);
                // separa as matriculas de uma mesma turma
                if (matricula.getHorarioTurma().getTurma().intValue() == h.getHorarioTurma().getTurma().intValue()) {
                    HorarioTurmaVO horario1 = matricula.getHorarioTurma();
                    HorarioTurmaVO horario2 = h.getHorarioTurma();
                    // separa as matriculas de um mesmo horario
                    if (horario1.getHoraInicial().equals(horario2.getHoraInicial())
                            && horario1.getHoraFinal().equals(horario2.getHoraFinal())
                            && horario1.getProfessor().getCodigo().equals(horario2.getProfessor().getCodigo())
                            && horario1.getAmbiente().getCodigo().equals(horario2.getAmbiente().getCodigo())
                            && horario1.getNivelTurma().getCodigo().equals(horario2.getNivelTurma().getCodigo())) {
                        var--;
                        listaObjetosConcat.add(h);
                        lista.remove(var);
                    }
                }
            }
            // prepara uma turma para colocar os alunos encontrados
            MatriculaAlunoHorarioTurmaRelVO relVO = new MatriculaAlunoHorarioTurmaRelVO();
            relVO.setContrato(matricula.getContrato());
            relVO.setSequencial(sequencial++);
            relVO.setDataFimMatricula(dataTermino);
            relVO.setDataInicioMatricula(dataInicio);
            relVO.setModalidade(getFacade().getModalidade().consultarPorTurma(matricula.getHorarioTurma().getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            relVO.setTurma(getFacade().getTurma().consultarPorChavePrimaria(matricula.getHorarioTurma().getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            relVO.setHorarioTurma(matricula.getHorarioTurma());

            ItemMatriAlunoHorarioTurmaVO item = null;
            PessoaVO pessoa = new PessoaVO();

            Ordenacao.ordenarLista(listaObjetosConcat, "nomeAluno");

            Iterator o = listaObjetosConcat.iterator();
            // percorre a lista de matriculas separadas por horario
            while (o.hasNext()) {
                MatriculaAlunoHorarioTurmaVO matr = (MatriculaAlunoHorarioTurmaVO) o.next();
                // nao inclui as pessoas repetidas
                if (pessoa.getCodigo().intValue() != matr.getPessoa().getCodigo().intValue()) {
                    pessoa = matr.getPessoa();
                    item = new ItemMatriAlunoHorarioTurmaVO();
                    item.setPessoa(pessoa);
                    item.setCliente(getFacade().getCliente().consultarPorCodigoPessoa(pessoa.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));

                    if(matr.getContrato() != null && !matr.getContrato().getCodigo().equals(0)){
                        item.setContrato(getFacade().getContrato().consultarPorChavePrimaria(matr.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_TIPOPESSOA));
                    }else {
                        item.setContrato(getFacade().getContrato().consultarContratoVigentePorPessoa(pessoa.getCodigo(), false, false, Uteis.NIVELMONTARDADOS_TIPOPESSOA));
                    }
                    relVO.getListaAluno().add(item);
                }

                if (matr.getReposicao() != null && matr.getReposicao().getCodigo().intValue() != 0) {
                    if (reposicaoIsDiaria(matr.getReposicao().getCodigo())) {
                        matr.getPessoa().setNome(matr.getPessoa().getNome() + " (DIÁRIA)");
                    } else {
                        matr.getPessoa().setNome(matr.getPessoa().getNome() + " (REPOS.)");
                    }
                }

                HorarioTurmaVO hor = matr.getHorarioTurma();
                long dias = Uteis.nrDiasEntreDatas(dataInicio, dataTermino) + 1;
                Date dataObtida = dataInicio;
                // marca os dias da semana em que a pessoa tem aula
                for (int cont = 0; cont < dias; cont++) {
                    String diaSemana = Uteis.obterDiaSemanaData(dataObtida);
                    int diaMes = Uteis.obterDiaData(dataObtida);

                    if (matr.getReposicao() != null) {
                        if (Uteis.obterDiaData(matr.getReposicao().getDataReposicao()) != diaMes) {
                            dataObtida = Uteis.obterDataFutura2(dataObtida, 1);
                            continue;
                        }
                    } else if (getFacade().getReposicao().existeDesmarcacaoAlunoNaqueleDia(
                            matr.getPessoa().getCodigo(), hor, dataObtida)) {
                        dataObtida = Uteis.obterDataFutura2(dataObtida, 1);
                        continue;
                    }

                    Class classItemVO = Class.forName(item.getClass().getName());
                    Class classRelVO = Class.forName(relVO.getClass().getName());
                    Method metodoSetDiaSemana = classRelVO.getMethod("setDiaSemana" + String.valueOf(cont + 1), String.class);
                    metodoSetDiaSemana.invoke(relVO, diaSemana);
                    Method metodoSetDia = classRelVO.getMethod("setData" + String.valueOf(cont + 1), String.class);
                    metodoSetDia.invoke(relVO, String.valueOf(diaMes));

                    Method metodoGetListaChamada = classItemVO.getMethod("getListaChamada" + String.valueOf(cont + 1));
                    // se a pessoa tem aula neste dia da semana especifico
                    if (!(Boolean) metodoGetListaChamada.invoke(item)) {
                        Method metodoSetListaChamada = classItemVO.getMethod("setListaChamada" + String.valueOf(cont + 1), Boolean.class);
                        metodoSetListaChamada.invoke(item, hor.getDiaSemana().equalsIgnoreCase(diaSemana) && (Calendario.maiorOuIgual(dataObtida,matr.getDataInicio()) && Calendario.menorOuIgual(dataObtida,matr.getDataFim())));
                        if (presencas) {
                            // verifica se a pessoa tem presença nesse dia
                            PresencaVO presenca = null;
                            PresencaVO presencaRepo = getFacade().getReposicao().preencherPresencaEmFuncaoReposicao(
                                    matr.getPessoa().getCodigo(), hor.getCodigo(),
                                    dataObtida, dataObtida);
                            if (presencaRepo.getRepo() != null) {
                                presenca = presencaRepo;
                            } else {
                                presenca = getFacade().getPresenca().consultarPorPessoaHorarioPeriodoPresenca(matr.getPessoa().getCodigo(), hor.getCodigo(), dataObtida, dataObtida, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            }

                            if (presenca.getCodigo().intValue() > 0 || presenca.getRepo() != null) {

                                boolean valor = presenca.getRepo() != null ? presenca.getDataPresenca() != null : true;
                                metodoSetListaChamada = classItemVO.getMethod("setPresenca" + String.valueOf(cont + 1), Boolean.class);
                                metodoSetListaChamada.invoke(item, valor);

                            }
                        }
                    }
                    dataObtida = Uteis.obterDataFutura2(dataObtida, 1);
                }
            }
            // adiciona a turma na lista
            listaRelVO.add(relVO);
            listaObjetosConcat = new ArrayList();
        }
        return listaRelVO;
    }

    private boolean reposicaoIsDiaria(Integer codReposicao) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select tipoagendamento from agenda a where reposicao = " + codReposicao);
        try (ResultSet rs = criarConsulta(sql.toString(), con)) {
            while (rs.next()) {
                return rs.getString("tipoagendamento").equalsIgnoreCase("DI");
            }
        }
        return false;
    }

    public List<MatriculaAlunoHorarioTurmaVO> consultar(Integer empresa, Integer turma, Integer colaborador, Date dataInicial, Date dataFinal) throws Exception {
        inicializar();
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT matriculaalunohorarioturma.* FROM matriculaalunohorarioturma ");
        if (turma != null && turma != 0) {
            sqlStr.append("INNER JOIN horarioturma ON horarioturma.codigo = matriculaalunohorarioturma.horarioturma AND horarioturma.turma = ").append(turma).append(" ");
        } else {
            sqlStr.append("LEFT OUTER JOIN horarioturma ON horarioturma.codigo = matriculaalunohorarioturma.horarioturma ");
        }
        if (colaborador != null && colaborador != 0) {
            sqlStr.append("INNER JOIN colaborador ON horarioturma.professor = colaborador.codigo AND colaborador.codigo = ").append(colaborador).append(" ");
        }
        sqlStr.append("INNER JOIN contrato ON matriculaalunohorarioturma.contrato = contrato.codigo ");
        sqlStr.append("WHERE matriculaalunohorarioturma.empresa = ").append(empresa).append(" ");
        if (dataInicial != null && dataFinal != null) {
            sqlStr.append("AND ( ( matriculaalunohorarioturma.datainicio <= '").append(Uteis.getDataJDBC(dataInicial)).append("' AND  matriculaalunohorarioturma.dataFim >= '").append(Uteis.getDataJDBC(dataInicial)).append("' ) ");
            sqlStr.append("OR ( matriculaalunohorarioturma.datainicio <= '").append(Uteis.getDataJDBC(dataFinal)).append("' AND matriculaalunohorarioturma.dataFim >= '").append(Uteis.getDataJDBC(dataFinal)).append("') ");
            sqlStr.append("OR ( matriculaalunohorarioturma.datainicio >= '").append(Uteis.getDataJDBC(dataInicial)).append("' AND matriculaalunohorarioturma.dataFim <= '").append(Uteis.getDataJDBC(dataFinal)).append("') ) ");
        } else {
            sqlStr.append("AND CURRENT_DATE between matriculaalunohorarioturma.datainicio AND matriculaalunohorarioturma.dataFim ");
        }

        Integer tempoDeToleranciaOcupacaoTurma = getEmpresaLogado().getToleranciaOcupacaoTurma();
        if (!UteisValidacao.emptyNumber(tempoDeToleranciaOcupacaoTurma)) {
            sqlStr.append(" AND ( contrato.situacao <> 'CA' ");
            sqlStr.append(" OR ( contrato.situacao = 'CA' ");
            sqlStr.append("      AND contrato.dataprevistarenovar >= CURRENT_DATE ");
            sqlStr.append("      AND contrato.dataprevistarenovar <= CURRENT_DATE + interval '")
                    .append(tempoDeToleranciaOcupacaoTurma)
                    .append(" day' ) ) ");
        } else {
            sqlStr.append(" AND contrato.situacao <> 'CA' ");
        }

        boolean temp = true;
        for (String dia : filtrodiasSemana()) {
            if (temp) {
                temp = false;
                sqlStr.append(" AND horarioturma.diasemana IN (");
            } else {
                sqlStr.append(",");
            }
            sqlStr.append(" '").append(dia.toUpperCase()).append("' ");
        }
        if (!temp) {
            temp = true;
            sqlStr.append(") ");
        }
        for (String hora : filtroHorarios()) {
            if (temp) {
                temp = false;
                sqlStr.append(" AND (");
            } else {
                sqlStr.append(" OR ");
            }
            sqlStr.append(" (horarioturma.horainicial >= '").append(hora.substring(0, 5)).append("' ");
            sqlStr.append(" AND horarioturma.horainicial  <= '").append(hora.substring(8, 13)).append("') ");
        }
        if (!temp) {
            sqlStr.append(") ");
        }


        sqlStr.append("ORDER BY horarioturma.turma, matriculaalunohorarioturma.pessoa");
        PreparedStatement sqlConsultar = con.prepareStatement(sqlStr.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();

        List<MatriculaAlunoHorarioTurmaVO> lista = MatriculaAlunoHorarioTurma.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, getCon());
        getFacade().getMatriculaAlunoHorarioTurma().preencherReposicoesFormatoMatriculaAlunoHorarioTurma(empresa, turma, colaborador, lista, dataInicial, dataFinal,filtroHorarios(),filtrodiasSemana());
        return lista;
    }

    private List<String> filtrodiasSemana() {
        List<String> diasSemana = new ArrayList<String>();
        if (consultarTurma.isDomingo()) {
            diasSemana.add("DM");
        }
        if (consultarTurma.isSegunda()) {
            diasSemana.add("SG");
        }
        if (consultarTurma.isTerca()) {
            diasSemana.add("TR");
        }
        if (consultarTurma.isQuarta()) {
            diasSemana.add("QA");
        }
        if (consultarTurma.isQuinta()) {
            diasSemana.add("QI");
        }
        if (consultarTurma.isSexta()) {
            diasSemana.add("SX");
        }
        if (consultarTurma.isSabado()) {
            diasSemana.add("SB");
        }
        if (diasSemana.isEmpty()) {
            consultarTurma.setDomingo(true);
            consultarTurma.setSegunda(true);
            consultarTurma.setTerca(true);
            consultarTurma.setQuarta(true);
            consultarTurma.setQuinta(true);
            consultarTurma.setSexta(true);
            consultarTurma.setSabado(true);
        }
        return diasSemana;
    }

    private List<String> filtroHorarios() {
        List<String> horarios = new ArrayList<String>();
        if (consultarTurma.isH0001as0200()) {
            horarios.add("00:01 - 02:00");
        }
        if (consultarTurma.isH0201as0400()) {
            horarios.add("02:01 - 04:00");
        }
        if (consultarTurma.isH0401as0600()) {
            horarios.add("04:01 - 06:00");
        }
        if (consultarTurma.isH0601as0800()) {
            horarios.add("06:01 - 08:00");
        }
        if (consultarTurma.isH0801as1000()) {
            horarios.add("08:01 - 10:00");
        }
        if (consultarTurma.isH1001as1200()) {
            horarios.add("10:01 - 12:00");
        }
        if (consultarTurma.isH1201as1400()) {
            horarios.add("12:01 - 14:00");
        }
        if (consultarTurma.isH1401as1600()) {
            horarios.add("14:01 - 16:00");
        }
        if (consultarTurma.isH1601as1800()) {
            horarios.add("16:01 - 18:00");
        }
        if (consultarTurma.isH1801as2000()) {
            horarios.add("18:01 - 20:00");
        }
        if (consultarTurma.isH2001as2200()) {
            horarios.add("20:01 - 22:00");
        }
        if (consultarTurma.isH2201as0000()) {
            horarios.add("22:01 - 00:00");
        }
        return horarios;
    }


    public String getDesignIReportRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator + getIdEntidade() + ".jrxml");
    }

    public static String getCaminhoSubRelatorio() {
        return ("relatorio" + File.separator + "designRelatorio" + File.separator + "contrato" + File.separator);
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataTermino() {
        return dataTermino;
    }

    public void setDataTermino(Date dataTermino) {
        this.dataTermino = dataTermino;
    }

    public TurmaVO getTurma() {
        return turma;
    }

    public void setTurma(TurmaVO turma) {
        this.turma = turma;
    }

    public ColaboradorVO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorVO professor) {
        this.professor = professor;
    }

    public EmpresaVO getEmpresa() {
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public ConsultarTurmaTO getConsultarTurma() {
        return consultarTurma;
    }

    public MatriculaAlunoHorarioTurmaRel setConsultarTurma(ConsultarTurmaTO consultarTurma) {
        this.consultarTurma = consultarTurma;
        return this;
    }
}
