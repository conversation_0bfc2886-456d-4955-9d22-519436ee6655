/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import controle.arquitetura.threads.ThreadDemonstrativoFinanceiro;
import negocio.comuns.financeiro.enumerador.TipoFonteDadosDF;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.financeiro.enumerador.TipoVisualizacaoRelatorioDF;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDemonstrativoFinanceiro extends SuperRelatorioFinan {

    public RelatorioDemonstrativoFinanceiro() {
        listaThreads = new ArrayList<ThreadDemonstrativoFinanceiro>();
    }

    public  List<DemonstrativoFinanceiro> gerarDemonstrativo(TipoRelatorioDF tipoRelatorio,
                                                         TipoVisualizacaoRelatorioDF tipoVisualizacao,
                                                         Calendar dataInicial,
                                                         Calendar dataFinal,
                                                         int empresa,
                                                         List<Integer> listaFiltroCentroCusto,
                                                         boolean gerarRelatorioUsandoThread,
                                                         TipoFonteDadosDF tipoFonteDadosDF,
                                                         boolean usarCE,
                                                         boolean agruparValorProdutoMMasModalidades,
                                                         String contas,
                                                         boolean apresentarDevolucoesRel,
                                                         Connection con) throws Exception {
        if (con == null){
            return gerarDemonstrativo(tipoRelatorio,
                    tipoVisualizacao,
                    dataInicial,
                    dataFinal,
                    empresa,
                    listaFiltroCentroCusto,
                    gerarRelatorioUsandoThread,
                    tipoFonteDadosDF,
                    usarCE,
                    agruparValorProdutoMMasModalidades,
                    contas,
                    false, false, apresentarDevolucoesRel, null, true, false);
        } else {
            return gerarDemonstrativo(tipoRelatorio,
                    tipoVisualizacao,
                    dataInicial,
                    dataFinal,
                    empresa,
                    listaFiltroCentroCusto,
                    gerarRelatorioUsandoThread,
                    tipoFonteDadosDF,
                    usarCE,
                    agruparValorProdutoMMasModalidades,
                    contas,
                    false, false, apresentarDevolucoesRel, con, false, false);
        }
    }

    public  List<DemonstrativoFinanceiro> gerarDemonstrativo(TipoRelatorioDF tipoRelatorio,
                                                             TipoVisualizacaoRelatorioDF tipoVisualizacao,
                                                             Calendar dataInicial,
                                                             Calendar dataFinal,
                                                             int empresa,
                                                             List<Integer> listaFiltroCentroCusto,
                                                             boolean gerarRelatorioUsandoThread,
                                                             TipoFonteDadosDF tipoFonteDadosDF,
                                                             boolean usarCE,
                                                             boolean agruparValorProdutoMMasModalidades,
                                                             String contas,
                                                             boolean incluirParcelasRecorrenciaEmRelatorioReceitaProvisao,
                                                             boolean fluxoCaixa, boolean apresentarDevolucoesRel, Connection con, boolean conecFromSession,
                                                             boolean incluirParcelasEmAbertoEmRelatorioReceitaProvisao) throws Exception{
        if (conecFromSession){
            con = Conexao.getFromSession();
            if (con == null) {
                con = Conexao.getConexaoForJ2SE();
            }
        }

        this.gerarRelatorioUsandoThread = gerarRelatorioUsandoThread;
        List<MesProcessar> listaMesesProcessar = montarListaMeses(dataInicial, dataFinal);
        List<DemonstrativoFinanceiro> listaDemonstrativo = montarListaDemonstrativoFinaneiro(tipoVisualizacao, listaMesesProcessar, null, false, con);

        criarThreadsDemonstrativoFinanceiro(con,
                listaDemonstrativo,
                tipoRelatorio,
                tipoVisualizacao,
                listaMesesProcessar,
                empresa,
                listaFiltroCentroCusto,
                tipoFonteDadosDF,
                false,
                null,
                usarCE,
                agruparValorProdutoMMasModalidades,
                contas,
                incluirParcelasRecorrenciaEmRelatorioReceitaProvisao, fluxoCaixa,
                incluirParcelasEmAbertoEmRelatorioReceitaProvisao);
        executarThreads(tipoRelatorio, listaMesesProcessar, apresentarDevolucoesRel);
        // Ficar em loop até que todas as threads terminam o seu trabalho.
        loopVerificarTerminoDasThreads();
        return listaDemonstrativo;
    }
}
