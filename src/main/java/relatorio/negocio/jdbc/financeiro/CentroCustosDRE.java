package relatorio.negocio.jdbc.financeiro;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.financeiro.enumerador.TipoEquivalenciaDRE;

import br.com.pactosolucoes.comuns.util.Formatador;

public class CentroCustosDRE implements Serializable {
    
	private Integer codigo;
	private String codigoCentro;
	private String nomeCentro;
    private double total = 0; // Esta variável servirá para mostrar o total de cada mês
    private double totalNaoAtribuido = 0; // Esta variável servirá para mostrar o total do totalizador "não atribuido"
	private List<LancamentoDF> lancamentos = new ArrayList<LancamentoDF>();
    
	public static List<CentroCustosDRE> getListaClone(List<CentroCustosDRE> centros){
		List<CentroCustosDRE> clones = new ArrayList<CentroCustosDRE>();
		for(CentroCustosDRE ccd : centros){
			clones.add(ccd.getClone());
		}
		return clones;
	}
	
	public static CentroCustosDRE obter(CentroCustosDRE ccd, List<CentroCustosDRE> ccds){
		int indexOf = ccds.indexOf(new CentroCustosDRE(ccd.getCodigo()));
		if(indexOf >= 0){
			return ccds.get(indexOf);
		}else{
			return new CentroCustosDRE();			
		}
	}
	
	public String toString(){
		return nomeCentro;
	}
	
	public CentroCustosDRE getClone(){
		return new CentroCustosDRE(this.codigo, this.codigoCentro, this.nomeCentro);
	}
	
	public CentroCustosDRE(){
    	
    }
    public CentroCustosDRE(Integer centro){
    	this.codigo = centro;
    }
    
    public CentroCustosDRE(Integer centro, String codigoCentro, String nomeCentro){
    	this.codigo = centro;
    	this.nomeCentro = nomeCentro;
    	this.codigoCentro = codigoCentro;
    }
    
    
    public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (!(obj instanceof CentroCustosDRE))
            return false;
        return  (((CentroCustosDRE)obj).getCodigo().equals(this.getCodigo()));
    }

    public String getValorApresentar() {
    	if(this.total == 0.0){
    		return "-";
    	}
        String valor = Formatador.formatarValorMonetarioSemMoeda(this.total);
        return (total < 0.0 ? "(-)" : "") +valor;
    }

    public String getValorNaoAtribuidoApresentar() {
    	if(this.totalNaoAtribuido == 0.0){
    		return "-";
    	}
    	String valor = Formatador.formatarValorMonetarioSemMoeda(this.totalNaoAtribuido);
    	return valor;
    }
    
    public void setNomeCentro(String nomeCentro) {
		this.nomeCentro = nomeCentro;
	}
	public String getNomeCentro() {
		return nomeCentro;
	}
	public void setTotal(double total) {
		this.total = total;
	}
	public double getTotal() {
		return total;
	}
	public double getTotalPositivo() {
		return total < 0.0 ? total *-1 : total;
	}
	public void setTotalNaoAtribuido(double totalNaoAtribuido) {
		this.totalNaoAtribuido = totalNaoAtribuido;
	}
	public double getTotalNaoAtribuido() {
		return totalNaoAtribuido;
	}
	public void setCodigoCentro(String codigoCentro) {
		this.codigoCentro = codigoCentro;
	}
	public String getCodigoCentro() {
		return codigoCentro;
	}

	public void setCodigo(Integer codigo) {
		this.codigo = codigo;
	}

	public Integer getCodigo() {
		return codigo;
	}
	public void setLancamentos(List<LancamentoDF> lancamentos) {
		this.lancamentos = lancamentos;
	}
	public List<LancamentoDF> getLancamentos() {
		return lancamentos;
	}

    public String getCorLinkTotalTodosMeses() {
        if (this.total >= 0)
            return "black";
        else
            return "red";
    }
	
}
