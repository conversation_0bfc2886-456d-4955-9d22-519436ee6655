/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.negocio.jdbc.financeiro;

import annotations.arquitetura.Lista;
import br.com.pactosolucoes.comuns.util.Formatador;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;

/**
 *
 * <AUTHOR>
 * Objetivo da classe: Representar a Modalidade do Plano que foi vendido, e
 *                     o percentual que a mesma representa em relação ao Valor Total do
 *                     Plano vendido.
 */
public class ContratoModalidadePercentual extends SuperVO{

    private int codigoModalidade;
    private int contrato;
    private double valor;
    private double percentagem = 0.0;
    private double valorPago = 0.0;
    private String nomeModalidade;
    private String nomeParcela = "";
    @Lista
    private List<MatriculaAlunoHorarioTurmaVO> matriculasDesteContratoModalidade = new ArrayList();
    
    public boolean equals(Object obj){
        if (obj == null)
            return false;
        if (!(obj instanceof ContratoModalidadePercentual))
            return false;
        ContratoModalidadePercentual cmp =(ContratoModalidadePercentual) obj;
        if(cmp.getNomeParcela().equals(nomeParcela) && cmp.getCodigoModalidade() == codigoModalidade && cmp.getContrato() == contrato)	
        	return true;	
        
        return false;
    }
    
    
    public static List<ContratoModalidadePercentual> getListaClone(List<ContratoModalidadePercentual> lista) throws Exception{
    	List<ContratoModalidadePercentual> clone = new ArrayList<ContratoModalidadePercentual>();
    	for(ContratoModalidadePercentual cm : lista){
    		clone.add((ContratoModalidadePercentual) cm.getClone(true));
    	}
    	return clone;
    	
    }

    public boolean getTemTurma(){
    	return !matriculasDesteContratoModalidade.isEmpty();
    }
    
    
	public JRDataSource getDsTurmas() {
        JRDataSource jr1 = new JRBeanArrayDataSource(getMatriculasDesteContratoModalidade().toArray());
        return jr1;
    }
    
    public int getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(int codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public int getContrato() {
        return contrato;
    }

    public void setContrato(int contrato) {
        this.contrato = contrato;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public double getPercentagem() {
        return percentagem;
    }

    public void setPercentagem(double percentagem) {
        this.percentagem = percentagem;
    }

    public String getNomeModalidade() {
        return nomeModalidade;
    }

    public void setNomeModalidade(String nomeModalidade) {
        this.nomeModalidade = nomeModalidade;
    }

    public double getValorPago() {
        return valorPago;
    }

    public void setValorPago(double valorPago) {
        this.valorPago = valorPago;
    }

    public List<MatriculaAlunoHorarioTurmaVO> getMatriculasDesteContratoModalidade() {
        return matriculasDesteContratoModalidade;
    }

    public void setMatriculasDesteContratoModalidade(List<MatriculaAlunoHorarioTurmaVO> matriculasDesteContratoModalidade) {
        this.matriculasDesteContratoModalidade = matriculasDesteContratoModalidade;
    }

        /*private double percentagem = 0.0;
    private double valorPago = 0.0;*/
    public String getPercentagem_Apresentar(){
        return Formatador.formatarValorPercentual(this.percentagem, 2);
    }

    public String getValorPago_Apresentar(){
        return Formatador.formatarValorMonetario(this.valorPago);
    }


	public void setNomeParcela(String nomeParcela) {
		this.nomeParcela = nomeParcela;
	}


	public String getNomeParcela() {
		return nomeParcela;
	}
}
