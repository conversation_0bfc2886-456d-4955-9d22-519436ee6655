package relatorio.controle.financeiro;

import br.com.pactosolucoes.estrutura.exportador.controle.ExportadorListaControle;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.jdbc.financeiro.TotalizadorFrequenciaRel;

import javax.faces.event.ActionEvent;
import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

public class TotalizadorTicketsControleRel extends SuperControleRelatorio {

    private TotalizadorFrequenciaRel totalizadorFrequenciaRel;
    private List<TotalizadorFrequenciaRel> listaDeTotalizadores;
    private Boolean popUp;
    private String filtros;
    private boolean permissaoConsultaTodasEmpresas = false;
    private Integer filtroEmpresa;

    public TotalizadorTicketsControleRel() {
        inicializarDados();
    }

    public void montarListaSelectItemEmpresa() {
        try {
            permissaoConsultaTodasEmpresas = permissao("ConsultarInfoTodasEmpresas");
            if (permissaoConsultaTodasEmpresas) {
                montarListaEmpresasComItemTodas();
            }
            setFiltroEmpresa(getEmpresaLogado().getCodigo());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void inicializarDados() {
        try {
            setTotalizadorFrequenciaRel(new TotalizadorFrequenciaRel());
            setListaEmpresas(new ArrayList<SelectItem>());
            setListaDeTotalizadores(new ArrayList<TotalizadorFrequenciaRel>());
            setPopUp(false);
            setMensagem("");
            setMensagemDetalhada("", "");
            montarListaSelectItemEmpresa();
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }


    }

    public List<SelectItem> getListaSelectItemFrequencia() {
        List<SelectItem> objs = new ArrayList<SelectItem>();
        objs.add(new SelectItem(TotalizadorFrequenciaRel.TICKET_TOTALIZADOR, "Totalizador"));
        objs.add(new SelectItem(TotalizadorFrequenciaRel.TICKET_TEMPO_PERMANENCIA, "Por tempo de permanência"));
        return objs;
    }

    public void imprimir() {
        try {
            setListaDeTotalizadores(new ArrayList<TotalizadorFrequenciaRel>());

            totalizadorFrequenciaRel.validarDados();
            getListaDeTotalizadores().addAll(getTotalizadorFrequenciaRel().consultaTickets(getFiltroEmpresa()));

            //Monta String que será apresentada na tela do relatório totalizador de acessos.
            montarDescricaoFiltros();

            setMensagemID("msg_relatorio_ok");
            setPopUp(true);
        } catch (ConsistirException e) {
            setPopUp(false);
            setMensagemDetalhada("msg_erro_relatorio", e.getMessage());
        } catch (Exception e) {
            setPopUp(false);
            setMensagemID("");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }

    }

    private void montarDescricaoFiltros() {
        StringBuilder filtros = new StringBuilder();
        filtros.append("<b>Empresa: </b>").append(getNomeEmpresaSelecionada(getFiltroEmpresa())).append(", </br>");

        if (getTotalizadorFrequenciaRel().getDataInicio() != null && getTotalizadorFrequenciaRel().getDataTermino() != null) {
            filtros.append("<b> Período de: </b>").append(Uteis.getDataAplicandoFormatacao(getTotalizadorFrequenciaRel().getDataInicio(), "dd/MM/yyyy"));
            filtros.append(" <b>até: </b>").append(Uteis.getData(getTotalizadorFrequenciaRel().getDataTermino())).append(", </br>");
        }

        if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
            filtros.append("<b>Tipo: </b>Total");
        }

        if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
            filtros.append("<b>Tipo: </b>Por faixa de horário");
        }
        setFiltros(filtros.toString());
    }

    public String getAbrirPopUp() {
        if (getPopUp()) {
            return "abrirPopup('../relatorio/totalizadorTicketsForm.jsp', 'Totalizadorfrequencia', 780, 595);";
        } else {
            return "";
        }
    }

    public List consultarEmpresaPorNome(String nomePrm) throws Exception {
        return new Empresa().consultarPorNome(nomePrm, true, false, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public TotalizadorFrequenciaRel getTotalizadorFrequenciaRel() {
        return totalizadorFrequenciaRel;
    }

    public void setTotalizadorFrequenciaRel(TotalizadorFrequenciaRel totalizadorFrequenciaRel) {
        this.totalizadorFrequenciaRel = totalizadorFrequenciaRel;
    }

    public List<TotalizadorFrequenciaRel> getListaDeTotalizadores() {
        return listaDeTotalizadores;
    }

    public void setListaDeTotalizadores(List<TotalizadorFrequenciaRel> listaDeTotalizadores) {
        this.listaDeTotalizadores = listaDeTotalizadores;
    }

    public Boolean getPopUp() {
        return popUp;
    }

    public void setPopUp(Boolean popUp) {
        this.popUp = popUp;
    }

    public void novo() {

    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public void exportar(ActionEvent eve) throws Exception {
        try {
            ExportadorListaControle exportadorListaControle = (ExportadorListaControle) getControlador(ExportadorListaControle.class.getSimpleName());
            exportadorListaControle.exportar(eve);
        } catch (Exception e) {
            setMensagemID("");
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String getAtributos() {
        if (getTotalizadorFrequenciaRel().getFrequencia() == 1) {
            return "dataInicio=Data,quantidade=Quantidade,porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
        }
        if (getTotalizadorFrequenciaRel().getFrequencia() == 2) {
            if (getTotalizadorFrequenciaRel().getAgrupamento().equals("NENHUM")) {
                return "diaDaSemana=Dia da Semana,quantidade=Quantidade,porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
            } else {
                return "diaDaSemana=Dia da Semana,quantidade=Quantidade,porcentagemRelacionada=Porcent. maior dia (%),porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
            }
        }
        if (getTotalizadorFrequenciaRel().getFrequencia() == 3) {
            return "hora=Hora,quantidade=Quantidade,porcentagem=Porcentagem (%),porcentagemAtivos=Porcentagem Ativos(%)";
        }
        return "";
    }

    public String getFiltrosSemHtml() {
        return Uteis.retiraTags(this.getFiltros(), false);
    }

    public boolean isPermissaoConsultaTodasEmpresas() {
        return permissaoConsultaTodasEmpresas;
    }

    public void setPermissaoConsultaTodasEmpresas(boolean permissaoConsultaTodasEmpresas) {
        this.permissaoConsultaTodasEmpresas = permissaoConsultaTodasEmpresas;
    }

    public Integer getFiltroEmpresa() {
        if (filtroEmpresa == null) {
            filtroEmpresa = 0;
        }
        return filtroEmpresa;
    }

    public void setFiltroEmpresa(Integer filtroEmpresa) {
        this.filtroEmpresa = filtroEmpresa;
    }

}
