/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package relatorio.controle.sad;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.arquitetura.RoboTransientObjectsVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;

/**
 *
 * <AUTHOR>
 */
public class SituacaoClienteSinteticoDWControle extends SuperControle {

    private static final String EXECUCAO_CONCLUIDA = "Execução concluída";

    private static final boolean RESUMIDO = false;
    private boolean debug = false;

    public List<ClienteVO> getListClientesControlado() {
        RoboTransientObjectsVO r = (RoboTransientObjectsVO) JSFUtilities.getFromSession(RoboTransientObjectsVO.class);
        if (r != null) {
            return r.getListaClientes();

        } else {
            return new ArrayList<>();
        }
    }

    public boolean isProcessamentoControlado() {
        return JSFUtilities.isJSFContext() && JSFUtilities.getFromSession(RoboTransientObjectsVO.class) != null;
    }

    public String processarDia(Date data, boolean somenteNovos) throws Exception {
        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
        if (config == null) {
            config = new ConfiguracaoSistemaControle();
        }

        String logInicio = String.format("Iniciando processamento do dia %s em %s", data.toString(), Calendario.hoje());
        Uteis.logarDebug(logInicio);
        config.setInformacaoSituacaoClienteSinteticoDW(logInicio);
        //EXECUÇÃO DE TODOS OS PROCESSOS
        config.setInformacaoExecutarProcessos(logInicio);
        return acaoProcessarDia(data, somenteNovos, config);
    }

    private String acaoProcessarDia(Date data, boolean somenteNovos, ConfiguracaoSistemaControle config) throws Exception {
        String clienteProcessados;
        try {
            if(isProcessamentoControlado()){
                clienteProcessados = processamentoControlado(data, config);
            } else {
                clienteProcessados = processamentoPadrao(data, somenteNovos, config);
            }
            getFacade().getZWFacade().getSituacaoClienteSinteticoDW().atualizarSaldoContaCorrente();
            getFacade().getZWFacade().getSituacaoClienteSinteticoDW().atualizarClassificacaoEnvioSMS();

            Uteis.logarDebug("Terminou processamento do dia " + data.toString() + " em " + Calendario.hoje().toString());
        } catch (Exception ex) {
            setErro(Boolean.TRUE);
            setMensagem(ex.getMessage());
            config.setInformacaoSituacaoClienteSinteticoDW("Falha na execução");

            //EXECUÇÃO DE TODOS OS PROCESSOS
            config.setInformacaoExecutarProcessos("Falha na execução");

            config.setRodandoSituacaoClienteSinteticoDW(false);
            Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
            setMensagem(ex.getMessage());
            throw ex;
        }
        return clienteProcessados;
    }

    private String processamentoPadrao(Date data, boolean somenteNovos, ConfiguracaoSistemaControle config) throws Exception {
        StringBuilder clienteProcessados = new StringBuilder();
        ResultSet rsClientes = getFacade().getSituacaoClienteSinteticoDW().consultarClientesPreparadosResultSet(somenteNovos);
        while (rsClientes.next()) {
            Date d1 = Calendario.hoje();
            ClienteVO clienteVO = getFacade().getSituacaoClienteSinteticoDW().montarDadosPreparados(rsClientes);
            clienteVO.setDeveAtualizarDependentesSintetico(true);
            SituacaoClienteSinteticoDWVO scDWVO = getFacade().getZWFacade().atualizarSintetico(
                    clienteVO, data,
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, RESUMIDO);
            if (somenteNovos) {
                clienteProcessados.append(",").append(clienteVO.getCodigo());
            }
            if (this.debug) {
                Date d2 = Calendario.hoje();

                Uteis.logarDebug(logSinteticoProcessado(d1, scDWVO, d2));
                if (config.isRodandoSituacaoClienteSinteticoDW() && config.isRodandoExecutarProcessos()) {
                    config.setInformacaoSituacaoClienteSinteticoDW(logSinteticoProcessado(d1, scDWVO, d2));

                    //EXECUÇÃO DE TODOS OS PROCESSOS
                    config.setInformacaoExecutarProcessos(logSinteticoProcessado(d1, scDWVO, d2));
                }
            }
        }
        if (config.isRodandoSituacaoClienteSinteticoDW() && config.isRodandoExecutarProcessos()) {
            config.setInformacaoSituacaoClienteSinteticoDW(EXECUCAO_CONCLUIDA);

            //EXUCUÇÃO DE TODOS OS PROCESSOS
            config.setInformacaoExecutarProcessos(EXECUCAO_CONCLUIDA);
        }

        config.setRodandoSituacaoClienteSinteticoDW(false);
        return clienteProcessados.toString();
    }

    private String processamentoControlado(Date data, ConfiguracaoSistemaControle config) throws Exception {
        StringBuilder clienteProcessados = new StringBuilder();
        //percorrer a lista de todos clientes existentes e preencher com as informações respectivas
        for (ClienteVO clienteVO : getListClientesControlado()) {
            Date d1 = Calendario.hoje();

            clienteVO.setDeveAtualizarDependentesSintetico(true);
            SituacaoClienteSinteticoDWVO scDWVO = getFacade().getZWFacade().atualizarSintetico(
                    clienteVO, data,
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, RESUMIDO);
            clienteProcessados.append(",").append(clienteVO.getCodigo());

            if (this.debug) {
                Date d2 = Calendario.hoje();

                String logProcessado = logSinteticoProcessado(d1, scDWVO, d2);
                Uteis.logarDebug(logProcessado);
                config.setInformacaoSituacaoClienteSinteticoDW(logProcessado);
                //EXECUÇÃO DE TODOS OS PROCESSOS
                config.setInformacaoExecutarProcessos(logProcessado);
            }
        }


        config.setInformacaoSituacaoClienteSinteticoDW(EXECUCAO_CONCLUIDA);

        //EXECUÇÃO DE TODOS OS PROCESSOS
        config.setInformacaoExecutarProcessos(EXECUCAO_CONCLUIDA);

        config.setRodandoSituacaoClienteSinteticoDW(false);
        return clienteProcessados.toString();
    }

    private static String logSinteticoProcessado(Date d1, SituacaoClienteSinteticoDWVO scDWVO, Date d2) {
        return String.format("Sintético cliente %s processado (%s ms)", scDWVO.getCodigoCliente(), (d2.getTime() - d1.getTime()));
    }

    public String processarDiaAlunosFrequencia(Date data, String clienteAtualizados, String clientesJaProcessados) throws Exception {
        StringBuilder clienteProcessados = new StringBuilder();
        Uteis.logar(null, "Iniciando processamento (Frequencia) do dia "
                    + data.toString() + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
        String clienteIngnorar = (clienteAtualizados + clientesJaProcessados).equals("") ? "" : (clienteAtualizados + clientesJaProcessados).substring(1);
        if (Calendario.getInstance(data).get(Calendar.DAY_OF_MONTH) == 1) {
            try {
                if(isProcessamentoControlado()){
                    List<ClienteVO> listaClientes = getListClientesControlado();

                    double n = listaClientes.size();
                    Uteis.logar(null, String.format("Atualizar Sintético (Frequencia) de %s alunos", n));
                    int i = 0;
                    //percorrer a lista de todos clientes existentes e preencher com as informações respectivas
                    for (Iterator<ClienteVO> it = listaClientes.iterator(); it.hasNext();) {
                        i++;
                        ClienteVO clienteVO = it.next();
                        clienteVO.setAtualizarTreino(false);
                        SituacaoClienteSinteticoDWVO scDWVO = getFacade().getZWFacade().atualizarSintetico(
                                clienteVO, data,
                                SituacaoClienteSinteticoEnum.GRUPO_FREQUENCIMETRO, RESUMIDO);
                        clienteProcessados.append(",").append(clienteVO.getCodigo());
                        if (this.debug) {
                            Double perc = (i * 100) / n;
                            Uteis.logar(null, String.format("(%1$,.2f)%% - Sintético cliente (Frequencia) %2$s processado ",
                                    perc,
                                    scDWVO.getCodigoCliente()));
                        }
                    }
                    clientesJaProcessados += clienteProcessados;
                } else {
                    getFacade().getSituacaoClienteSinteticoDW().inicializarFrequencimetro(clienteIngnorar);
                    clientesJaProcessados += getFacade().getSituacaoClienteSinteticoDW().processarFrequencimetroDia(Calendario.hoje(), clienteIngnorar);
                }

            } catch (Exception ex) {
                setErro(Boolean.TRUE);
                setMensagem(ex.getMessage());
                Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(
                        Level.SEVERE, ex.getMessage(), ex);
                setMensagem(ex.getMessage());
                throw ex;
            }
        } else {
            getFacade().getZWFacade().atualizarUltimoMesFrequencimetro(data, clienteIngnorar);
        }
        Uteis.logar(null, "Terminou processamento (Frequencia) do dia " + data.toString()
                        + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
        return clientesJaProcessados;
    }


     public void processarDiaAlunosDadosAcesso(String clienteAtualizados) throws Exception {
            Uteis.logar(null, "Iniciando processamento (dados acesso) do dia "
                    + Calendario.hoje().toString() + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            try {
                if(isProcessamentoControlado()){
                    StringBuilder clientes = new StringBuilder();
                    for (ClienteVO cli : getListClientesControlado()){
                        clientes.append(",").append(cli.getCodigo());
                    }
                    getFacade().getSituacaoClienteSinteticoDW().inicializarDadosAcessoProcessamentoContralado(Calendario.hoje(), clientes.substring(1));
                    List<ClienteVO> listaClientes =  getListClientesControlado();
                    double n = listaClientes.size();
                    Uteis.logar(null, String.format("Atualizar Sintético (dados acesso) de %s alunos", n));
                    int i = 0;
                    //percorrer a lista de todos clientes existentes e preencher com as informações respectivas
                    for (Iterator<ClienteVO> it = listaClientes.iterator(); it.hasNext();) {
                        i++;
                        ClienteVO clienteVO = it.next();
                        clienteVO.setAtualizarTreino(false);
                        SituacaoClienteSinteticoDWVO scDWVO = getFacade().getZWFacade().atualizarSintetico(
                                clienteVO, Calendario.hoje(),
                                SituacaoClienteSinteticoEnum.GRUPO_DADOSACESSO, RESUMIDO);
                        if (this.debug) {
                            Double perc = (i * 100) / n;
                            Uteis.logar(null, String.format("(%1$,.2f)%% - Sintético cliente (dados acesso) %2$s processado ",
                                    perc,
                                    scDWVO.getCodigoCliente()));
                        }
                    }
                } else {
                    getFacade().getSituacaoClienteSinteticoDW().processarDadosAcessoDia(Calendario.hoje(), clienteAtualizados.equals("") ? "" : clienteAtualizados.substring(1));
                }
                Uteis.logarDebug("Terminou processamento (dados acesso) do dia " + Calendario.hoje().toString() + " em " + negocio.comuns.utilitarias.Calendario.hoje().toString());
            } catch (Exception ex) {
                setErro(Boolean.TRUE);
                setMensagem(ex.getMessage());
                Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(Level.SEVERE, ex.getMessage(), ex);
                throw ex;
            }
    }

    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    public static void main(String[] args) {
        if (args.length > 0 && args[0].equals("todas")) {
            main(args, true);
        } else {
            try {
                Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "bdzillyonselfit");
                Conexao.guardarConexaoForJ2SE(c);
                if (args.length > 2) {
                    if (args[2].equals("atualizarTreino")) {
                        SituacaoClienteSinteticoDW sitDao = new SituacaoClienteSinteticoDW(c);
                        sitDao.atualizarUsuariosTreino(c);
                    }
                } else {
                    SituacaoClienteSinteticoDWControle control = new SituacaoClienteSinteticoDWControle();
                    control.setDebug(true);
                    control.processarDia(Calendario.hoje(), args.length > 0 && Boolean.parseBoolean(args[1]));
                }
            } catch (Exception ex) {
                Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    public static void executar() throws Exception {
        SituacaoClienteSinteticoDWControle control = new SituacaoClienteSinteticoDWControle();
        control.setDebug(true);
        control.processarDia(Calendario.hoje(), false);
    }

    public static void main(String[] args, boolean processarExcetoVisitante) {
        try {
            List<String>  empresas = new DAO().buscarListaChaves();
            for (String chave : empresas){
                try (Connection c = new DAO().obterConexaoEspecifica(chave)) {
                    Conexao.guardarConexaoForJ2SE(c);
                    SituacaoClienteSinteticoDW stdw = new SituacaoClienteSinteticoDW();
                    List<SituacaoClienteSinteticoDWVO> listaSituacaoClienteSinteticoDWVO = stdw.consultarAlunosSemSituacaoContrato(Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
                    if (processarExcetoVisitante){
                        for (SituacaoClienteSinteticoDWVO sinteticoDWVO : listaSituacaoClienteSinteticoDWVO){
                            Cliente cliente = new Cliente();
                            ClienteVO clienteVO = cliente.consultarPorMatricula(sinteticoDWVO.getMatricula().toString(),false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                            getFacade().getZWFacade().atualizarSintetico(
                                    clienteVO, Calendario.hoje(),
                                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, RESUMIDO);
                        }
                    }
                }catch (Exception ex){
                    Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(Level.SEVERE, null, ex);
                }

            }
        } catch (Exception ex) {
            Logger.getLogger(SituacaoClienteSinteticoDWControle.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}
