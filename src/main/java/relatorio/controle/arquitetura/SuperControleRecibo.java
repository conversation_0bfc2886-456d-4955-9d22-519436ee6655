package relatorio.controle.arquitetura;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ConvenioDescontoConfiguracaoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import relatorio.negocio.comuns.financeiro.DescontoReciboRelTO;
import relatorio.negocio.comuns.financeiro.ReciboPagamentoRelTO;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 14/11/13
 * Time: 09:24
 * To change this template use File | Settings | File Templates.
 */
public class SuperControleRecibo extends SuperControleRelatorio {

    public void calcularConvDescontoMovProduto(ReciboPagamentoRelTO recibo, MovProdutoVO movProduto, ContratoVO contratoVO) throws Exception {
        //Calculo do desconto por convenio
        DescontoReciboRelTO descontoConv = new DescontoReciboRelTO();
        Double porcentagemConvenio = 0.0;
        Double valorConvenio = 0.0;

        List conConfiguracoes = getFacade().getConvenioDescontoConfiguracao().consultarConvenioDescontoConfiguracaos(contratoVO.getConvenioDesconto().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        for (Object conv : conConfiguracoes) {
            ConvenioDescontoConfiguracaoVO descConfiguracaoVO = (ConvenioDescontoConfiguracaoVO) conv;
            if (descConfiguracaoVO.getDuracao().equals(contratoVO.getContratoDuracao().getNumeroMeses())) {
                valorConvenio = descConfiguracaoVO.getValorDesconto();
                porcentagemConvenio = descConfiguracaoVO.getPorcentagemDesconto();
            }
            List convenioDesconto = getFacade().getConvenioDesconto().consultarPorCodigo(contratoVO.getConvenioDesconto().getCodigo(), contratoVO.getEmpresa().getCodigo() , false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (Object convenioDesc : convenioDesconto) {
                ConvenioDescontoVO convenioDescontoVO = (ConvenioDescontoVO) convenioDesc;
                recibo.getConvenioDescontoVO().setCodigo(convenioDescontoVO.getCodigo());
                if (recibo.getConvenioDescontoVO().getCodigo().equals(contratoVO.getConvenioDesconto().getCodigo())) {
                    if (!UteisValidacao.emptyString(contratoVO.getNomeConvenioDesconto())) {
                        recibo.getConvenioDescontoVO().setDescricao(contratoVO.getNomeConvenioDesconto());

                        descontoConv.setDescricaoDesconto(contratoVO.getNomeConvenioDesconto());
                        descontoConv.setValorDesconto(contratoVO.getValorConvenioDesconto());
                        descontoConv.setPorcentagemDesconto(contratoVO.getPercentualConvenioDesconto());
                    } else {
                        recibo.getConvenioDescontoVO().setDescricao(convenioDescontoVO.getDescricao());

                        descontoConv.setDescricaoDesconto(convenioDescontoVO.getDescricao());
                        descontoConv.setValorDesconto(valorConvenio);
                        descontoConv.setPorcentagemDesconto(porcentagemConvenio);
                    }
                    descontoConv.setCodigoContrato(movProduto.getContrato_Apresentar());
                    break;
                }
            }
        }
        if (descontoConv.getValorDesconto() != 0.0 || descontoConv.getPorcentagemDesconto() != 0.0) {
            boolean jaExiste = false;
            for (DescontoReciboRelTO descontoReciboRelTO : recibo.getDescontosRecibo()) {
                if (descontoReciboRelTO.getPorcentagemDesconto().equals(descontoConv.getPorcentagemDesconto())
                        && descontoReciboRelTO.getValorDesconto().equals(descontoConv.getValorDesconto())
                        && descontoReciboRelTO.getCodigoContrato().equals(descontoConv.getCodigoContrato())
                        && descontoReciboRelTO.getDescricaoDesconto().equals(descontoConv.getDescricaoDesconto())) {
                    jaExiste = true;
                }
            }
            if (!jaExiste) {
                recibo.getDescontosRecibo().add(descontoConv);
            }
        }
        recibo.getDescConfiguracaoVO().setPorcentagemDesconto(porcentagemConvenio);
        recibo.getDescConfiguracaoVO().setValorDesconto(valorConvenio);
    }

    public void calcularDescontoExtraMovProduto(ReciboPagamentoRelTO recibo, MovProdutoVO movProduto) throws Exception {
        //Cálculo do desconto extra;
        DescontoReciboRelTO descontoExtr = new DescontoReciboRelTO();
        Double descontoExtra = 0.0;
        List tipoDesconto = getFacade().getMovProduto().consultarPorCodigoContratoTipoProduto(movProduto.getContrato().getCodigo(), "DE", Uteis.NIVELMONTARDADOS_MINIMOS);
        for (Object tipo : tipoDesconto) {
            MovProdutoVO movprodutoVO = (MovProdutoVO) tipo;
            descontoExtra = movprodutoVO.getTotalFinal();
            if (movprodutoVO.getProduto().getTipoProduto().equals(TipoProduto.DESCONTO.getCodigo())) {
                recibo.getMovProduto().setDescricao(movprodutoVO.getDescricao());

                descontoExtr.setDescricaoDesconto(movprodutoVO.getDescricao());
                descontoExtr.setValorDesconto(descontoExtra);
                descontoExtr.setCodigoContrato(movProduto.getContrato_Apresentar());
            }
        }
        if (descontoExtr.getValorDesconto() != 0.0 || descontoExtr.getPorcentagemDesconto() != 0.0) {
            boolean jaExiste = false;
            for (DescontoReciboRelTO descontoReciboRelTO : recibo.getDescontosRecibo()) {
                if (descontoReciboRelTO.getPorcentagemDesconto().equals(descontoExtr.getPorcentagemDesconto())
                        && descontoReciboRelTO.getValorDesconto().equals(descontoExtr.getValorDesconto())
                        && descontoReciboRelTO.getCodigoContrato().equals(descontoExtr.getCodigoContrato())
                        && descontoReciboRelTO.getDescricaoDesconto().equals(descontoExtr.getDescricaoDesconto())) {
                    jaExiste = true;
                }
            }
            if (!jaExiste) {
                recibo.getDescontosRecibo().add(descontoExtr);
            }
        }
        recibo.getMovProduto().setPrecoUnitario(descontoExtra);
    }

}
