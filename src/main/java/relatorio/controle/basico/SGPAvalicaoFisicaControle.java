package relatorio.controle.basico;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.negocio.comuns.basico.SGPAvaliacaoFisicaTO;
import relatorio.negocio.jdbc.basico.SGPAvaliacaoFisicaRel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SGPAvalicaoFisicaControle extends SuperControleRelatorio {
    private List<SGPAvaliacaoFisicaTO> listaUtilizacoes = new ArrayList<>();

    private Date dataInicio;
    private Date dataFim;

    private boolean selecionarEmpresa;
    private String filtros;

    private List<ClienteVO> listaClientes;

    public SGPAvalicaoFisicaControle() {
        inicializarDados();
    }

    public void inicializarDados() {
        try {
            setListaUtilizacoes(new ArrayList<>());
            setDataInicio(new Date());
            setDataFim(new Date());
            setSelecionarEmpresa(false);
            setFiltros("");
            montarListaSelectItemEmpresa();
            limparMsg();
            setMensagemID("msg_entre_prmrelatorio");
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
        }
    }

    public String consultar() {
        try {
            limparMsg();
            setarFiltros();
            SGPAvaliacaoFisicaRel rel = getFacade().getSGPAvaliacaoFisicaRel();
            rel.setarParametrosConsulta(getDataInicio(), getDataFim(), getEmpresa());
            rel.validarDados();
            setListaUtilizacoes(rel.consultar());
            setMensagem("Dados consultados");
            return "relatorio";
        } catch (ConsistirException e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "consultar";
        } catch (Exception e) {
            setMensagemDetalhada("msg_erro", e.getMessage());
            return "";
        }
    }

    private void setarFiltros() {
        String filtros = "<b>Inicio: </b> " + getDataInicio_Apresentar() + "</br>" +
                "<b>Fim: </b> " + getDataFim_Apresentar() + "</br>" +
                "<b>Empresa: </b>" + getEmpresa().getNome() + "</br>";
        setFiltros(filtros);
    }

    public String voltar() {
        return "consultar";
    }

    public void montarListaSelectItemEmpresa() {
        try {
            if (JSFUtilities.isJSFContext()) {
                setListaEmpresas(new ArrayList<>());
                setEmpresa(getEmpresaLogado());
            }
            if (getEmpresa() == null || getEmpresa().getCodigo() == 0) {
                setSelecionarEmpresa(true);
                montarListaEmpresas();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isSelecionarEmpresa() {
        return selecionarEmpresa;
    }

    public void setSelecionarEmpresa(Boolean selecionarEmpresa) {
        this.selecionarEmpresa = selecionarEmpresa;
    }

    public String getFiltros() {
        return filtros;
    }

    public void setFiltros(String filtros) {
        this.filtros = filtros;
    }

    public List<SGPAvaliacaoFisicaTO> getListaUtilizacoes() {
        return listaUtilizacoes;
    }

    public void setListaUtilizacoes(List<SGPAvaliacaoFisicaTO> listaUtilizacoes) {
        this.listaUtilizacoes = listaUtilizacoes;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getDataInicio_Apresentar() {
        return Uteis.getData(dataInicio);
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    private String getDataFim_Apresentar() {
        return Uteis.getData(dataFim);
    }

    public List<ClienteVO> getListaClientes() {
        return listaClientes;
    }

    public void setListaClientes(List<ClienteVO> listaClientes) {
        this.listaClientes = listaClientes;
    }

    public void prepararListaComerciario() {
        SGPAvaliacaoFisicaTO item = (SGPAvaliacaoFisicaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaComerciario());
        }
    }

    public void prepararListaDependente() {
        SGPAvaliacaoFisicaTO item = (SGPAvaliacaoFisicaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaDependentes());
        }
    }

    public void prepararListaUsuario() {
        SGPAvaliacaoFisicaTO item = (SGPAvaliacaoFisicaTO) JSFUtilities.getFromRequest("item");
        setListaClientes(new ArrayList<>());
        if (item != null) {
            setListaClientes(item.getListaUsuarios());
        }
    }
}
