package servicos.treino;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.jdbc.sad.SituacaoClienteSinteticoDW;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by alcides on 26/08/2016.
 */
public class AtualizadorAlunosTreinoService {

    public static void main(String ... args){
        String chave = "quads";
        if (args.length > 0) {
            chave = args[0];
        }
        verificarInconsistencias(chave);
    }

    public static void verificarInconsistencias(String chave){
        try {
            DAO dao = new DAO();
            Connection con = dao.obterConexaoEspecifica(chave);
            JSONArray jsonTR = TreinoWSConsumer.obterTodosAlunosTreino(chave);
            SituacaoClienteSinteticoDW sitDao = new SituacaoClienteSinteticoDW(con);
            JSONArray jsonZW = sitDao.obterDadosAlunosTreinoResumidos();
            Map<Integer, Map<String, JSONObject>> mapa = new HashMap<Integer, Map<String, JSONObject>>();
            for (int i = 0; i < jsonTR.length(); i++) {
                Map<String, JSONObject> jsonObjectMap = mapa.get(jsonTR.getJSONObject(i).getInt("codigopessoa"));
                if(jsonObjectMap == null){
                    jsonObjectMap = new HashMap<String, JSONObject>();
                    mapa.put(jsonTR.getJSONObject(i).getInt("codigopessoa"), jsonObjectMap);
                }
                jsonObjectMap.put("TR", jsonTR.getJSONObject(i));
            }

            for (int i = 0; i < jsonZW.length(); i++) {
                Map<String, JSONObject> jsonObjectMap = mapa.get(jsonZW.getJSONObject(i).getInt("codigopessoa"));
                if(jsonObjectMap == null){
                    jsonObjectMap = new HashMap<String, JSONObject>();
                    mapa.put(jsonZW.getJSONObject(i).getInt("codigopessoa"), jsonObjectMap);
                }
                jsonObjectMap.put("ZW", jsonZW.getJSONObject(i));
            }
            List<Integer> listaCodigosAtualizar = new ArrayList<Integer>();
            for(Integer codigoPessoa : mapa.keySet()){
                Map<String, JSONObject> jsonObjectMap = mapa.get(codigoPessoa);
                JSONObject tr = jsonObjectMap.get("TR");
                JSONObject zw = jsonObjectMap.get("ZW");
                if(tr == null && zw != null){
                    System.out.println("\nAluno com usuario movel mas não consta no TR:"+
                            zw.getString("nome") +" - "+ zw.getString("matricula")+"\n");
                }else if(tr != null && zw == null){
                    System.out.println("\nAluno do treino não encontrado no zw:"+
                            tr.getString("nome") +" - "+ tr.getString("matricula")+"\n");
                }else if((!tr.getString("matricula").equals(zw.getString("matricula")))
                        || (!tr.getString("nome").equals(zw.getString("nome")))
                        || (!tr.getString("situacao").equals(zw.getString("situacao")))
                        || (!tr.getString("professor").equals(zw.getString("professor")))
                        ){
                    listaCodigosAtualizar.add(codigoPessoa);
                    System.out.println("\n--- Aluno difere: ZW - TR");
                    System.out.println("Nome       : "+zw.getString("nome") +" - "+ tr.getString("nome"));
                    System.out.println("Matricula  : "+zw.getString("matricula") +" - "+ tr.getString("matricula"));
                    System.out.println("Professor  : "+zw.getString("professor") +" - "+ tr.getString("professor"));
                    System.out.println("Situação   : "+zw.getString("situacao") +" - "+ tr.getString("situacao"));
                    System.out.println("Cod. Pessoa: "+zw.getString("codigopessoa") +" - "+ tr.getString("codigopessoa"));

                }
            }
            if(listaCodigosAtualizar.isEmpty()){
                System.out.println("Nada para ser atualizado.");
            }else{
                System.out.println("Atualizar "+listaCodigosAtualizar.size()+" alunos...");
                sitDao.atualizarUsuariosTreinoLista(con, listaCodigosAtualizar);
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }


}
