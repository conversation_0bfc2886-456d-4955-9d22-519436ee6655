package servicos.adm;

public class SPCService {

    private int codPessoa;
    private String key;

    private static final String ENDPOINT_CDL = "/cdl-cds";
    private static final String ENDPOINT_INCLUIR_CDL = ENDPOINT_CDL + "/v1/{key}/{codPessoa}/incluirSPC";
    private static final String ENDPOINT_EXCLUIR_CDL = ENDPOINT_CDL + "/v1/{key}/{codPessoa}/excluirSPC";
    private static final String ENDPOINT_CONSULTAR_CDL = ENDPOINT_CDL + "/v1/{key}/{codPessoa}/incluirSPC";


    public SPCService(int codPessoa) {

    }


    public String incluir() {



        return null;
    }

    public String excluir() {


        return null;
    }

    public String consultar() {


        return null;
    }


}
