package servicos.discovery;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DiscoveryMsService {

    private static final Map<String, ClientDiscoveryDataDTO> cache = new ConcurrentHashMap<>();

    private static String baseUrl() throws DiscoveryException {
        String url = PropsService.getPropertyValue(PropsService.URL_DISCOVERY_MS);
        if (url.equals("@DISCOVERY_URL@")) {
            throw new DiscoveryException("A propriedade @DISCOVERY_URL@ não está definida");
        }
        return url;
    }

    public static ClientDiscoveryDataDTO urls() throws Exception {
        String cacheKey = "urls";
        if (cache.containsKey(cacheKey)) {
            return cache.get(cacheKey);
        }

        String url = baseUrl() + "/find";
        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        ClientDiscoveryDataDTO data = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);

        cache.put(cacheKey, data);
        return data;
    }

    public static ClientDiscoveryDataDTO urlsChave(String chave) throws Exception {
        if (cache.containsKey(chave)) {
            return cache.get(chave);
        }

        String url = baseUrl() + "/find/" + chave;
        String response = ExecuteRequestHttpService.get(url, new HashMap<>());
        ClientDiscoveryDataDTO data = JSONMapper.getObject(new JSONObject(response).getJSONObject("content"), ClientDiscoveryDataDTO.class);

        cache.put(chave, data);
        return data;
    }

    public static void clearnCache() {
        cache.clear();
    }
}
