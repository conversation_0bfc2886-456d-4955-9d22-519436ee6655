/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.armario;

import java.io.IOException;
import java.io.PrintWriter;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import servlet.arquitetura.SuperServlet;

/**
 *
 * <AUTHOR>
 */
public class TamanhoArmarioServlet extends SuperServlet {
    
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        String json;
        try {
            json = ff(request).getTamanhoArmario().consultarJSON();

        } catch (Exception e) {
            json = (e.getMessage() == null) ? NullPointerException.class.getName() : e.getMessage();
        }
        out.println(json);
        json = null;
    }
    
}
