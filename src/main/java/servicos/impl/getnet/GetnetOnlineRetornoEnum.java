package servicos.impl.getnet;


import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 05/05/2020
 */
public enum GetnetOnlineRetornoEnum {

    NENHUM("NENHUM", ""),
    PAYMENTS_001 ("PAYMENTS-001", "Valor não informado"),
    PAYMENTS_002 ("PAYMENTS-002", "Cartão inválido", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_003 ("PAYMENTS-003", "Cartão vencido", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_004 ("PAYMENTS-004", "Parâmetro inválido"),
    PAYMENTS_005 ("PAYMENTS-005", "Código de segurança inválido"),
    PAYMENTS_006 ("PAYMENTS-006", "Bandeira inválida"),
    PAYMENTS_007 ("PAYMENTS-007", "Operação não permitida"),
    PAYMENTS_008 ("PAYMENTS-008", "Dados inválidos"),
    PAYMENTS_009 ("PAYMENTS-009", "Valor mínimo da parcela inválido"),
    PAYMENTS_010 ("PAYMENTS-010", "Número de parcelas inválido"),
    PAYMENTS_011 ("PAYMENTS-011", "Número de parcelas excede limite"),
    PAYMENTS_012 ("PAYMENTS-012", "Valor da entrada maior ou igual ao valor da transação"),
    PAYMENTS_013 ("PAYMENTS-013", "Valor da parcela inválido"),
    PAYMENTS_014 ("PAYMENTS-014", "Parcelamento não permitido"),
    PAYMENTS_015 ("PAYMENTS-015", "Contatar emissor"),
    PAYMENTS_016 ("PAYMENTS-016", "NSU inválido"),
    PAYMENTS_019 ("PAYMENTS-019", "Data de emissão do cartão inválida"),
    PAYMENTS_020 ("PAYMENTS-020", "Data de vencimento inválida"),
    PAYMENTS_021 ("PAYMENTS-021", "Sistema do Emissor indisponível - Tente novamente"),
    PAYMENTS_022 ("PAYMENTS-022", "Código de moeda inválido"),
    PAYMENTS_023 ("PAYMENTS-023", "Bandeira não pertece a rede"),
    PAYMENTS_024 ("PAYMENTS-024", "Transação desfeita"),
    PAYMENTS_025 ("PAYMENTS-025", "Autenticação inválida"),
    PAYMENTS_026 ("PAYMENTS-026", "Autorização inválida"),
    PAYMENTS_029 ("PAYMENTS-029", "Pré-autorização inválida"),
    PAYMENTS_030 ("PAYMENTS-030", "Erro na autenticação do usuário"),
    PAYMENTS_031 ("PAYMENTS-031", "Timeout"),
    PAYMENTS_032 ("PAYMENTS-032", "Negada pelo emissor"),
    PAYMENTS_033 ("PAYMENTS-033", "Estabelecimento inválido"),
    PAYMENTS_034 ("PAYMENTS-034", "Solicitação não autorizada"),
    PAYMENTS_036 ("PAYMENTS-036", "Solicite identificação"),
    PAYMENTS_037 ("PAYMENTS-037", "Transação em andamento"),
    PAYMENTS_038 ("PAYMENTS-038", "Transação inválida"),
    PAYMENTS_039 ("PAYMENTS-039", "Valor da transação inválido"),
    PAYMENTS_041 ("PAYMENTS-041", "Refaça a transação"),
    PAYMENTS_042 ("PAYMENTS-042", "Resposta inválida"),
    PAYMENTS_043 ("PAYMENTS-043", "Registro não encontrado"),
    PAYMENTS_044 ("PAYMENTS-044", "Erro de formato"),
    PAYMENTS_045 ("PAYMENTS-045", "Cartão restrito"),
    PAYMENTS_046 ("PAYMENTS-046", "Cartão extraviado", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_047 ("PAYMENTS-047", "Cartão roubado", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_048 ("PAYMENTS-048", "Cartão sem registro"),
    PAYMENTS_049 ("PAYMENTS-049", "Transação não permitida a esse cliente"),
    PAYMENTS_050 ("PAYMENTS-050", "Entrar em contato com a instituição"),
    PAYMENTS_051 ("PAYMENTS-051", "Resposta parametrizada negativa"),
    PAYMENTS_052 ("PAYMENTS-052", "Transação duplicada"),
    PAYMENTS_053 ("PAYMENTS-053", "Cartão bloqueado", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_TEMPORARIO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_054 ("PAYMENTS-054", "Pendente de confirmação"),
    PAYMENTS_055 ("PAYMENTS-055", "Transação cancelada"),
    PAYMENTS_056 ("PAYMENTS-056", "Transação não permitida neste ciclo"),
    PAYMENTS_057 ("PAYMENTS-057", "Transação não existe"),
    PAYMENTS_058 ("PAYMENTS-058", "Transação estornada"),
    PAYMENTS_059 ("PAYMENTS-059", "Problema rede local"),
    PAYMENTS_060 ("PAYMENTS-060", "Cartão obrigatório na transação"),
    PAYMENTS_061 ("PAYMENTS-061", "Rejeição genérica"),
    PAYMENTS_062 ("PAYMENTS-062", "Instituição temporariamente fora de operação"),
    PAYMENTS_063 ("PAYMENTS-063", "Mal funcionamento do sistema"),
    PAYMENTS_064 ("PAYMENTS-064", "Erro banco de dados"),
    PAYMENTS_066 ("PAYMENTS-066", "Forma de pagamento inválido"),
    PAYMENTS_067 ("PAYMENTS-067", "Valor limite excedido"),
    PAYMENTS_068 ("PAYMENTS-068", "Dígito cartão inválido"),
    PAYMENTS_069 ("PAYMENTS-069", "Transação repetida"),
    PAYMENTS_070 ("PAYMENTS-070", "Número do cartão não confere", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_071 ("PAYMENTS-071", "Autorizadora temporariamente bloqueada"),
    PAYMENTS_072 ("PAYMENTS-072", "Transação não cancelável"),
    PAYMENTS_073 ("PAYMENTS-073", "Transação já cancelada"),
    PAYMENTS_076 ("PAYMENTS-076", "Transação não disponível"),
    PAYMENTS_077 ("PAYMENTS-077", "Número do cartão inválido", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_078 ("PAYMENTS-078", "Dados inválidos no cancelamento"),
    PAYMENTS_079 ("PAYMENTS-079", "Valor cancelamento inválido"),
    PAYMENTS_080 ("PAYMENTS-080", "Cartão inválido", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_081 ("PAYMENTS-081", "Excede data"),
    PAYMENTS_082 ("PAYMENTS-082", "Cancelamento inválido"),
    PAYMENTS_083 ("PAYMENTS-083", "Use função débito"),
    PAYMENTS_084 ("PAYMENTS-084", "Use função crédito"),
    PAYMENTS_085 ("PAYMENTS-085", "Transação já efetuada"),
    PAYMENTS_086 ("PAYMENTS-086", "Erro na transação"),
    PAYMENTS_089 ("PAYMENTS-089", "Timeout interno"),
    PAYMENTS_090 ("PAYMENTS-090", "Transação não autorizada pelo cartão"),
    PAYMENTS_091 ("PAYMENTS-091", "Fora do prazo permitido"),
    PAYMENTS_093 ("PAYMENTS-093", "Autorização já encontra-se em processamento"),
    PAYMENTS_094 ("PAYMENTS-094", "Autorização a confirmar o recebimento"),
    PAYMENTS_095 ("PAYMENTS-095", "Autorização não encontrada"),
    PAYMENTS_096 ("PAYMENTS-096", "Crédito excedido"),
    PAYMENTS_098 ("PAYMENTS-098", "Cliente não cadastrado"),
    PAYMENTS_099 ("PAYMENTS-099", "Autorizadora não inicializada"),
    PAYMENTS_100 ("PAYMENTS-100", "Canal desconectado"),
    PAYMENTS_101 ("PAYMENTS-101", "Autorizadora offline"),
    PAYMENTS_102 ("PAYMENTS-102", "Cliente sem limite de crédito", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    PAYMENTS_103 ("PAYMENTS-103", "Transação não autorizada pela rede autorizadora"),
    PAYMENTS_104 ("PAYMENTS-104", "Erro de integração"),
    PAYMENTS_105 ("PAYMENTS-105", "Transação não autorizada"),
    PAYMENTS_107 ("PAYMENTS-107", "Erro de comunicação"),
    PAYMENTS_109 ("PAYMENTS-109", "Existe transação a confirmar"),
    PAYMENTS_112 ("PAYMENTS-112", "Documento inexistente para cancelar"),
    PAYMENTS_113 ("PAYMENTS-113", "Saldo Insuficiente", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    PAYMENTS_114 ("PAYMENTS-114", "Excede o Limite de Valor de Aprovação"),
    PAYMENTS_115 ("PAYMENTS-115", "Excede o Limite de Frequência de Retiradas"),
    PAYMENTS_116 ("PAYMENTS-116", "Autorização recusada"),
    PAYMENTS_117 ("PAYMENTS-117", "Solicite ao portator ligar para o emissor", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    PAYMENTS_118 ("PAYMENTS-118", "Cartao invalido ou produto não habilitado"),
    PAYMENTS_500 ("PAYMENTS-500", "Internal Server Error"),
    PAYMENTS_999 ("PAYMENTS-999", "Transacao nao processada"),
    ERRO_481     ("481", "Transação negada por regra de segurança do sistema de Antifraude");


    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private GetnetOnlineRetornoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private GetnetOnlineRetornoEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private GetnetOnlineRetornoEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static GetnetOnlineRetornoEnum valueOff(String id) {
        for (GetnetOnlineRetornoEnum getnet : GetnetOnlineRetornoEnum.values()) {
            if (getnet.getId().equals(id) || getnet.getId().equals("PAYMENTS-"+id)) {
                return getnet;
            }
        }
        return NENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}
