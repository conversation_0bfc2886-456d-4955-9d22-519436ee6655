package servicos.impl.boleto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.EstornoReciboControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.MovimentoContaCorrenteClienteVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.BoletoMovParcelaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.EstornoReciboVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.ReciboPagamentoVO;
import negocio.comuns.financeiro.VendaAvulsaVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.notaFiscal.TipoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.MovimentoContaCorrenteCliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.BoletoMovParcela;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.FormaPagamento;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.SuperServico;
import servicos.impl.boleto.asaas.BoletoAsaasService;
import servicos.impl.boleto.bancobrasil.BancoBrasilService;
import servicos.impl.boleto.caixa.CaixaService;
import servicos.impl.boleto.itau.ItauService;
import servicos.impl.boleto.pjbank.PJBankService;
import servicos.impl.gatewaypagamento.CobrancaOnlineService;
import servicos.interfaces.BoletoOnlineServiceInterface;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 07/12/2021
 */
public class BoletoOnlineService extends SuperServico {

    public BoletoOnlineService(Connection con) throws Exception {
        super(con);
    }

    public static BoletoOnlineServiceInterface getBoletoOnlineService(TipoBoletoEnum tipoBoletoEnum, Integer empresa,
                                                                      Integer convenioCobranca, Connection con,
                                                                      boolean validarConvenioAtivo) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            if (validarConvenioAtivo) {
                ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                if (!convenioCobrancaVO.getSituacao().equals(SituacaoConvenioCobranca.ATIVO)) {
                    throw new Exception("Convênio de cobrança \"" + convenioCobrancaVO.getDescricao() + "\" não está ativo!");
                }
            }

            if (tipoBoletoEnum.equals(TipoBoletoEnum.PJ_BANK)) {
                return new PJBankService(con, empresa, convenioCobranca);
            } else if (tipoBoletoEnum.equals(TipoBoletoEnum.ITAU)) {
                return new ItauService(con, empresa, convenioCobranca);
            } else if (tipoBoletoEnum.equals(TipoBoletoEnum.ASAAS)) {
                return new BoletoAsaasService(con, empresa, convenioCobranca);
            } else if (tipoBoletoEnum.equals(TipoBoletoEnum.CAIXA)) {
                return new CaixaService(con, empresa, convenioCobranca);
            } else if (tipoBoletoEnum.equals(TipoBoletoEnum.BANCO_BRASIL)) {
                return new BancoBrasilService(con, empresa, convenioCobranca);
            } else {
                throw new Exception("Service Boleto não encontrado");
            }
        } finally {
            convenioCobrancaDAO = null;
        }
    }

    public void gerarPagamentoBoleto(BoletoVO boletoVO, UsuarioVO usuarioVO, boolean controlarTransacao, boolean origemProcesso) throws Exception {
        Cliente clienteDAO;
        Usuario usuarioDAO;
        MovPagamento movPagamentoDAO;
        Empresa empresaDAO;
        Boleto boletoDAO;
        BoletoMovParcela boletoMovParcelaDAO;
        MovParcela movParcelaDAO;
        MovimentoContaCorrenteCliente movimentoDAO;
        Colaborador colaboradorDAO;
        Pessoa pessoaDAO;
        try {
            if (controlarTransacao) {
                this.getCon().setAutoCommit(false);
            }
            clienteDAO = new Cliente(this.getCon());
            usuarioDAO = new Usuario(this.getCon());
            movPagamentoDAO = new MovPagamento(this.getCon());
            empresaDAO = new Empresa(this.getCon());
            boletoDAO = new Boleto(this.getCon());
            boletoMovParcelaDAO = new BoletoMovParcela(this.getCon());
            movParcelaDAO = new MovParcela(this.getCon());
            movimentoDAO = new MovimentoContaCorrenteCliente(this.getCon());
            colaboradorDAO = new Colaborador(this.getCon());
            pessoaDAO = new Pessoa(this.getCon());

            if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.PAGO) ||
                    !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo()) ||
                    !UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo())) {
                return;
            }

            if (UteisValidacao.emptyList(boletoVO.getListaParcelas())) {
                boletoVO.setListaParcelas(boletoMovParcelaDAO.consultarMovParcelasDoBoleto(boletoVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            boletoVO.setPessoaVO(pessoaDAO.consultarPorChavePrimaria(boletoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            FormaPagamentoVO formaPagamentoVO = obterFormaPagamentoBoleto(boletoVO);

            Date dataPagamento = boletoVO.getDataPagamento();
            Date dataCredito = boletoVO.getDataCredito();
            Double valorPago = boletoVO.getValorPago();
            boolean valorPagoMenorQueValorBoleto = Uteis.arredondarForcando2CasasDecimais(boletoVO.getValor()) > Uteis.arredondarForcando2CasasDecimais(boletoVO.getValorPago());

            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setMovPagamentoEscolhida(true);
            movPagamentoVO.setOpcaoPagamentoBoleto(true);
            movPagamentoVO.setCredito(false);
            movPagamentoVO.setFormaPagamento(formaPagamentoVO);
            movPagamentoVO.setValor(valorPago);
            movPagamentoVO.setValorTotal(valorPago);
            movPagamentoVO.setPessoa(boletoVO.getPessoaVO());
            movPagamentoVO.setNomePagador(boletoVO.getPessoaVO().getNome());
            movPagamentoVO.setEmpresa(empresaVO);
            movPagamentoVO.setConvenio(boletoVO.getConvenioCobrancaVO());
            if (usuarioVO == null) {
                movPagamentoVO.setResponsavelPagamento(usuarioDAO.getUsuarioRecorrencia());
            } else {
                movPagamentoVO.setResponsavelPagamento(usuarioVO);
            }

            //O padrão é a Data de Lançamento ser a data efetiva do lançamento no sistema.
            //Mas como isso impacta no Financeiro, foi solicitado alterar para atender clientes que Atualiza o boleto que não deu baixa automática.
            //Foi adicionado validação para só impactar o Pjbank, que o reclamado no ticket e não impactar os demais.
            if (boletoVO != null && boletoVO.getTipo().equals(TipoBoletoEnum.PJ_BANK) && dataPagamento != null) {
                movPagamentoVO.setDataLancamento(dataPagamento);
            } else {
                //ajustado conforme informado pelo Marcos André
                movPagamentoVO.setDataLancamento(Calendario.hoje());
            }
            movPagamentoVO.setDataQuitacao(dataPagamento);
            movPagamentoVO.setDataPagamento(dataCredito);

            List<MovPagamentoVO> listaPagamento = new ArrayList<MovPagamentoVO>();
            listaPagamento.add(movPagamentoVO);

            List<MovParcelaVO> listaParcelasMultaJurosGeradas = new ArrayList<>();
            List<MovParcelaVO> listaParcelasSemMultaJurosGeradas = new ArrayList<>();

            List<BoletoMovParcelaVO> listaBoletoMovParcela = boletoMovParcelaDAO.consultarPorCodigoBoleto(boletoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS);
            List<BoletoMovParcelaVO> listaBoletoMovParcelaCriadas = new ArrayList<>();

            //verificar se tem que gerar parcela de multa e juros para alguma parcela
            for (BoletoMovParcelaVO boletoMovParcelaVO : listaBoletoMovParcela) {
                if (!UteisValidacao.emptyNumber(boletoMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                        boletoMovParcelaVO.getMovParcelaVO().getSituacao().equalsIgnoreCase("EA")) {

                    //criar parcela de multa e juros
                    if (!UteisValidacao.emptyNumber(boletoMovParcelaVO.getValorMulta()) ||
                            !UteisValidacao.emptyNumber(boletoMovParcelaVO.getValorJuros())) {
                        //esse valor já está incluso no valor do boleto porem ainda não foi criado a parcela
                        //criar a parcela e adicionar na lista de parcelas
                        MovParcelaVO movParcelaMultaJuros = movParcelaDAO.criarParcelaMultaJuros(boletoMovParcelaVO.getMovParcelaVO(),
                                boletoMovParcelaVO.getValorMulta(), boletoMovParcelaVO.getValorJuros(), usuarioVO, false);
                        BoletoMovParcelaVO boletoMovParcelaCriado = boletoMovParcelaDAO.incluirBoletoMovParcelaVO(boletoVO, movParcelaMultaJuros);
                        listaBoletoMovParcelaCriadas.add(boletoMovParcelaCriado);
                        listaParcelasMultaJurosGeradas.add(movParcelaMultaJuros);
                    }
                    listaParcelasSemMultaJurosGeradas.add(boletoMovParcelaVO.getMovParcelaVO());
                }
            }

            listaBoletoMovParcela.addAll(listaBoletoMovParcelaCriadas);

            //caso tenha pago a menos do que o valor do boleto
            if (valorPagoMenorQueValorBoleto && deveGerarDesconto(boletoVO, dataPagamento, valorPago)) {
                boletoVO.setPagoUsandoDesconto(true);
                listaParcelasSemMultaJurosGeradas = gerarDescontoBoleto(boletoVO, usuarioVO, listaBoletoMovParcela, listaParcelasSemMultaJurosGeradas);
            }

            //se as parcelas não estiverem em aberto então gerar depósito na conta
            //gerar pagamento conta corrente caso parcela não esteja em aberto
            boolean todasAsParcelasEmAberto = todasAsParcelasEmAberto(listaBoletoMovParcela);

            if (!todasAsParcelasEmAberto){
                double diferencaGerarContaCorrente = 0.0;
                if (origemProcesso) {
                    diferencaGerarContaCorrente = boletoVO.getValorPago();
                } else {
                    diferencaGerarContaCorrente = valorGerarContaCorrente(boletoVO, listaBoletoMovParcela);
                }
                StringBuilder sqlExiste = new StringBuilder();
                sqlExiste.append("select \n");
                sqlExiste.append("mp.codigo \n");
                sqlExiste.append("from movpagamento mp \n");
                sqlExiste.append("inner join pagamentomovparcela pag on pag.movpagamento = mp.codigo \n");
                sqlExiste.append("where mp.observacao ilike '%boleto ").append(boletoVO.getCodigo()).append(" - %' \n");
                sqlExiste.append("limit 1 \n");
                boolean existe = SuperFacadeJDBC.existe(sqlExiste.toString(), this.getCon());

                if (existe) {
                    throw new Exception("Já existe um crédito em conta corrente referente ao boleto " + boletoVO.getCodigo());
                }

                ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(boletoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                ColaboradorVO colaboradorVO = null;
                if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    clienteVO = null;
                    colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(boletoVO.getPessoaVO().getCodigo(), boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                String descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO_PARCELA_NAO_ABERTO;
                VendaAvulsaVO vendaAvulsaVO = movimentoDAO.gerarProdutoPagamentoCredito(diferencaGerarContaCorrente, clienteVO, colaboradorVO, null, usuarioVO, descLancCC,
                        boletoVO.getDataPagamento(), boletoVO.getDataPagamento(), empresaVO);

                MovParcelaVO movParcelaCriada = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                movPagamentoVO.setObservacao("Foi gerado crédito boleto " + boletoVO.getCodigo() + " - Diferença no valor");
                BoletoMovParcelaVO boletoMovParcelaCriado = boletoMovParcelaDAO.incluirBoletoMovParcelaVO(boletoVO, movParcelaCriada);
                listaBoletoMovParcela.add(boletoMovParcelaCriado);
                listaParcelasSemMultaJurosGeradas.add(movParcelaCriada);
            }

            Double valorTotalItens = obterValorTotalItensBoleto(listaBoletoMovParcela);
            boolean valorPagoMaior = Uteis.arredondarForcando2CasasDecimais(valorTotalItens) < Uteis.arredondarForcando2CasasDecimais(valorPago);

            //caso tenha pago a mais do que o valor do boleto
            if (valorPagoMaior) {
                // Se pagou a mais e o boleto não está vencido, então a diferença vai pra conta corrente do aluno
                if (!Calendario.maior(dataPagamento, boletoVO.getDataVencimento())) {
                    double diferencaGerarContaCorrente = valorGerarContaCorrente(boletoVO, listaBoletoMovParcela);
                    StringBuilder sqlExiste = new StringBuilder();
                    sqlExiste.append("select \n");
                    sqlExiste.append("mp.codigo \n");
                    sqlExiste.append("from movpagamento mp \n");
                    sqlExiste.append("inner join pagamentomovparcela pag on pag.movpagamento = mp.codigo \n");
                    sqlExiste.append("where mp.observacao ilike '%boleto ").append(boletoVO.getCodigo()).append(" - %' \n");
                    sqlExiste.append("limit 1 \n");
                    boolean existe = SuperFacadeJDBC.existe(sqlExiste.toString(), this.getCon());

                    if (existe) {
                        throw new Exception("Já existe um crédito em conta corrente referente ao boleto " + boletoVO.getCodigo());
                    }

                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(boletoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    ColaboradorVO colaboradorVO = null;
                    if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        clienteVO = null;
                        colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(boletoVO.getPessoaVO().getCodigo(), boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                    String descLancCC = MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO_BOLETO_EXCEDENTE;
                    VendaAvulsaVO vendaAvulsaVO = movimentoDAO.gerarProdutoPagamentoCredito(diferencaGerarContaCorrente, clienteVO, colaboradorVO, null, usuarioVO, descLancCC,
                            boletoVO.getDataPagamento(), boletoVO.getDataPagamento(), empresaVO);

                    MovParcelaVO movParcelaCriada = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    movPagamentoVO.setObservacao("Foi gerado crédito boleto " + boletoVO.getCodigo() + " - Diferença no valor");
                    BoletoMovParcelaVO boletoMovParcelaCriado = boletoMovParcelaDAO.incluirBoletoMovParcelaVO(boletoVO, movParcelaCriada);
                    listaBoletoMovParcela.add(boletoMovParcelaCriado);
                    listaParcelasSemMultaJurosGeradas.add(movParcelaCriada);
                } else {
                    //Se pagou a mais e o boleto está vencido, então a diferença vai gerar parcela de multa e juros
                    //TODO melhorar para obter o valor correto de multa e juros

                    Double valorDiferenca = Uteis.arredondarForcando2CasasDecimais(Uteis.arredondarForcando2CasasDecimais(valorPago) - Uteis.arredondarForcando2CasasDecimais(valorTotalItens));
                    MovParcelaVO movParcelaMultaJuros = movParcelaDAO.criarParcelaMultaJuros(listaParcelasSemMultaJurosGeradas.get(0), valorDiferenca, 0.0, usuarioVO, false);
                    BoletoMovParcelaVO boletoMovParcelaCriado = boletoMovParcelaDAO.incluirBoletoMovParcelaVO(boletoVO, movParcelaMultaJuros);
                    listaBoletoMovParcela.add(boletoMovParcelaCriado);
                    listaParcelasMultaJurosGeradas.add(movParcelaMultaJuros);
                }
            }

            List<MovParcelaVO> listaParcelasPagar = new ArrayList<>();
            listaParcelasPagar.addAll(listaParcelasMultaJurosGeradas);
            listaParcelasPagar.addAll(listaParcelasSemMultaJurosGeradas);

            ReciboPagamentoVO reciboObj = movPagamentoDAO.incluirListaPagamento(listaPagamento, listaParcelasPagar, null,
                    obterContratoParcelas(listaParcelasPagar), false, 0.0, false,
                    null, null);

            boletoVO.setReciboPagamentoVO(reciboObj);
            boletoVO.setMovPagamentoVO(reciboObj.getPagamentosDesteRecibo().get(0));
            boletoDAO.alterarMovPagamentoReciboPagamento(boletoVO);

            if (controlarTransacao) {
                this.getCon().commit();
            }
            processamentoPosPagamento(reciboObj, empresaVO);
        } catch (Exception ex) {
            if (controlarTransacao) {
                this.getCon().rollback();
            }
            ex.printStackTrace();
            throw ex;
        } finally {
            if (controlarTransacao) {
                this.getCon().setAutoCommit(true);
            }
            clienteDAO = null;
            usuarioDAO = null;
            movPagamentoDAO = null;
            empresaDAO = null;
            boletoDAO = null;
            boletoMovParcelaDAO = null;
            movParcelaDAO = null;
            movimentoDAO = null;
            colaboradorDAO = null;
            pessoaDAO = null;
        }
    }

    private void processamentoPosPagamento(ReciboPagamentoVO reciboPagamentoVO, EmpresaVO empresaVO) {
        NotaFiscal notaFiscalDAO;
        MovPagamento movPagamentoDAO;
        Cliente clienteDAO;
        ZillyonWebFacade zillyonWebFacade;
        try {
            notaFiscalDAO = new NotaFiscal(this.getCon());
            movPagamentoDAO = new MovPagamento(this.getCon());
            clienteDAO = new Cliente(this.getCon());
            zillyonWebFacade = new ZillyonWebFacade(this.getCon());
            try {
                //o próprio método de enviar o nfse já valida se a empresa usa ou não o recurso
                reciboPagamentoVO.getPagamentosDesteRecibo().get(0).setProdutosPagos(movPagamentoDAO.obterProdutosPagosMovPagamento(reciboPagamentoVO.getPagamentosDesteRecibo().get(0).getCodigo()));

                String formasPagamentoEmissaoAutomaticaNfse = (empresaVO.getFormasPagamentoEmissaoNFSe() != null ?
                        reciboPagamentoVO.getEmpresa().getFormasPagamentoEmissaoNFSe() : "");

                if (empresaVO.isEnviarNFSeAutomatico() && empresaVO.getTipoGestaoNFSe() == TipoRelatorioDF.FATURAMENTO_DE_CAIXA.getCodigo()) {
                    boolean emiteBoletoAutomaticamente = reciboPagamentoVO
                                .getPagamentosDesteRecibo()
                                .get(0)
                                .getFormaPagamento()
                                .getTipoFormaPagamento().equals("BB")
                            && formasPagamentoEmissaoAutomaticaNfse.contains("BB");

                    if (emiteBoletoAutomaticamente) {
                        String chave = DAO.resolveKeyFromConnection(this.getCon());
                        notaFiscalDAO.gerarNotaReciboPagamento(TipoNotaFiscalEnum.NFSE, reciboPagamentoVO, reciboPagamentoVO.getPagamentosDesteRecibo().get(0).getUsuarioVO(), chave);
                        Uteis.logarDebug("Nota Fiscal Eletronica gerada e emitida para o Recibo: " + reciboPagamentoVO.getCodigo());
                    } else {
                        Uteis.logarDebug("Nota Fiscal Eletronica não gerada e não emitida para o Recibo: " + reciboPagamentoVO.getCodigo() + ", devido a forma de pagamento BOLETO BANCÁRIO não estar configurada para emissão automática.");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                Uteis.logarDebug("Nota Fiscal Eletronica ignorada para o Recibo: " + reciboPagamentoVO.getCodigo() + ", devido ao erro: " + e.getMessage());
            }

            try {
                if (empresaVO.isNotificarWebhook()) {
                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoPessoa(reciboPagamentoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    zillyonWebFacade.notificarPagamento(clienteVO, reciboPagamentoVO);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            notaFiscalDAO = null;
            movPagamentoDAO = null;
            clienteDAO = null;
            zillyonWebFacade = null;
        }
    }

    private Double valorGerarContaCorrente(BoletoVO boletoVO, List<BoletoMovParcelaVO> listaBoletoMovParcela) {
        Double somaValorParcelas = 0.0;
        for (BoletoMovParcelaVO boletoMovParcelaVO : listaBoletoMovParcela) {
            if (!UteisValidacao.emptyNumber(boletoMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                    boletoMovParcelaVO.getMovParcelaVO().getSituacao().equalsIgnoreCase("EA")) {
                somaValorParcelas += boletoMovParcelaVO.getMovParcelaVO().getValorParcela();
            }
        }
        return boletoVO.getValorPago() - Uteis.arredondarForcando2CasasDecimais(somaValorParcelas);
    }

    private boolean todasAsParcelasEmAberto(List<BoletoMovParcelaVO> listaBoletoMovParcela) {
        for (BoletoMovParcelaVO boletoMovParcelaVO : listaBoletoMovParcela) {
            if ((!UteisValidacao.emptyNumber(boletoMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                    !boletoMovParcelaVO.getMovParcelaVO().getSituacao().equalsIgnoreCase("EA")) ||
                    UteisValidacao.emptyNumber(boletoMovParcelaVO.getMovParcelaVO().getCodigo())) {
              return false;
            }
        }
        return true;
    }

    private Double obterValorTotalItensBoleto(List<BoletoMovParcelaVO> listaBoletoMovParcela) {
        Double soma = 0.0;
        for (BoletoMovParcelaVO boletoMovParcelaVO : listaBoletoMovParcela) {
            if (!UteisValidacao.emptyNumber(boletoMovParcelaVO.getMovParcelaVO().getCodigo()) &&
                    boletoMovParcelaVO.getMovParcelaVO().getSituacao().equalsIgnoreCase("EA")) {
                soma += boletoMovParcelaVO.getMovParcelaVO().getValorParcela();
            }
        }

        return Uteis.arredondarForcando2CasasDecimais(soma);
    }

    private Boolean deveGerarDesconto(BoletoVO boletoVO, Date dataOcorrencia, Double valorPago) throws Exception {
        Feriado feriadoDAO = null;
        Empresa empresaDAO = null;
        try {
            if (((boletoVO.getValor() - valorPago) > 0.0) && //validar se a parcela já não foi renegociada e já está com o valor correto
                    boletoVO.isPossuiDesconto() &&
                    valorPago < boletoVO.getValor() &&
                    (boletoVO.getValor() - valorPago > 0.0)) {
                feriadoDAO = new Feriado(this.getCon());
                empresaDAO = new Empresa(this.getCon());
                boolean darDesconto = true;
                String dataMaximaDescontoAux = boletoVO.getDiaMesDescontoPagAntecipado().toString() + "/" + (Calendario.getData(boletoVO.getDataVencimento(), "MM/yyyy"));
                Date dataMaximaDesconto = Calendario.getDate("dd/MM/yyyy", dataMaximaDescontoAux);
                dataMaximaDesconto = Calendario.fimDoDia(dataMaximaDesconto);

                Calendar cal = Calendario.getInstance();
                cal.setTime(dataMaximaDesconto);
                if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                    int diasSomar = (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) ? 2 : 1;
                    dataMaximaDesconto = Uteis.somarDias(cal.getTime(), diasSomar);
                }
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
                List<Date> dataLimiteFeriado = feriadoDAO.consultarDiasFeriados(dataMaximaDesconto, dataMaximaDesconto, empresaVO);
                if (!dataLimiteFeriado.isEmpty()) {
                    dataMaximaDesconto = Uteis.somarDias(dataMaximaDesconto, 1);
                }
                //pagou com desconto mesmo após a data limite para pagar com desconto
                if (Calendario.maior(dataOcorrencia, dataMaximaDesconto)) {
                    //primeiro validar usando regra da pjbank pra ver se era realmente para dar desconto ou não
                    if (boletoVO.getValorPossivelDesconto().equals(valorPago)) {
                        darDesconto = true;
                    } else {
                        darDesconto = false;
                    }
                }
                return darDesconto;
            }
        } finally {
            feriadoDAO = null;
            empresaDAO = null;
        }
        return false;
    }

    private List<MovParcelaVO> gerarDescontoBoleto(BoletoVO boletoVO, UsuarioVO usuarioVO,
                                                   List<BoletoMovParcelaVO> listaBoletoMovParcela,
                                                   List<MovParcelaVO> listaParcelas) throws Exception {
        MovParcela movParcelaDAO;
        BoletoMovParcela boletoMovParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(this.getCon());
            boletoMovParcelaDAO = new BoletoMovParcela(this.getCon());

            Double valorDesconto= 0.0;
            if (!UteisValidacao.emptyNumber(boletoVO.getValorDescontoPagAntecipado())) {
                valorDesconto = Uteis.arredondarForcando2CasasDecimais(boletoVO.getValorDescontoPagAntecipado());
            } else {
                valorDesconto = Uteis.arredondarForcando2CasasDecimais((boletoVO.getValor() * boletoVO.getPorcentagemDescontoPagAntecipado()) / 100);
            }

            //escolher a parcela com o
            BoletoMovParcelaVO boletoMovParcelaRenegociar = null;
            for (BoletoMovParcelaVO boletoMovParcelaVO1 : listaBoletoMovParcela) {
                if (!UteisValidacao.emptyNumber(boletoMovParcelaVO1.getMovParcelaVO().getCodigo()) &&
                        Uteis.arredondarForcando2CasasDecimais(boletoMovParcelaVO1.getMovParcelaVO().getValorParcela()) >
                                Uteis.arredondarForcando2CasasDecimais(valorDesconto)) {
                    boletoMovParcelaRenegociar = boletoMovParcelaVO1;
                    break;
                }
            }

            if (boletoMovParcelaRenegociar == null) {
                throw new Exception("Não foi encontrado uma parcela para aplicar o desconto");
            }


            MovParcelaVO movParcelaRenegociar = boletoMovParcelaRenegociar.getMovParcelaVO();

            List<MovParcelaVO> parcelasRenegociar = new ArrayList<>();
            parcelasRenegociar.add(movParcelaRenegociar);
            // parcela desconto
            MovParcelaVO parcelaRenegociar = new MovParcelaVO();
            parcelaRenegociar.setDescricao("DESCONTOS");
            parcelaRenegociar.setValorParcela(Uteis.arredondarForcando2CasasDecimais(valorDesconto));
            parcelaRenegociar.setDataVencimento(Calendario.hoje());
            parcelasRenegociar.add(parcelaRenegociar);

            MovParcelaVO parcelaDesconto = new MovParcelaVO();
            parcelaDesconto.setDescricao("");
            parcelaDesconto.setValorParcela(valorDesconto);
            parcelaDesconto.setDataVencimento(Calendario.hoje());

            // Parcelas Renegociadas
            List<MovParcelaVO> parcelasRenegociadas = new ArrayList<>();
            MovParcelaVO novaParcela = (MovParcelaVO) movParcelaRenegociar.getClone(true);
            novaParcela.setDescricao("PARCELA RENEGOCIADA");
            novaParcela.setValorParcela(Uteis.arredondarForcando2CasasDecimais(movParcelaRenegociar.getValorParcela() - valorDesconto));
            novaParcela.setDataRegistro(Calendario.hoje());
            parcelasRenegociadas.add(novaParcela);
            parcelaDesconto.setDescricao("");

            movParcelaDAO.renegociarParcelas(parcelasRenegociar, parcelasRenegociadas, parcelaDesconto, new MovParcelaVO(), "DE", false, null, null, 0.0, false, usuarioVO, true, false, true, null, null);

            List<MovParcelaVO> listaNovaParcelas = new ArrayList<>();
            listaNovaParcelas.addAll(parcelasRenegociadas);

            boletoMovParcelaDAO.alterarMovParcela(boletoMovParcelaRenegociar, parcelasRenegociadas.get(0), "Alteração de MovParcela do boleto devido desconto");

            //alterar parcela
            for (BoletoMovParcelaVO boletoMovParcelaVO2 : listaBoletoMovParcela) {
                if (boletoMovParcelaRenegociar.getCodigo().equals(boletoMovParcelaVO2.getCodigo())) {
                    boletoMovParcelaVO2.setMovParcelaVO(parcelasRenegociadas.get(0));
                    break;
                }
            }

            for (MovParcelaVO movParcelaVO1 : listaParcelas) {
                if (!movParcelaVO1.getCodigo().equals(movParcelaRenegociar.getCodigo())) {
                    listaNovaParcelas.add(movParcelaVO1);
                }
            }

            return listaNovaParcelas;
        } finally {
            movParcelaDAO = null;
            boletoMovParcelaDAO = null;
        }
    }

    private FormaPagamentoVO obterFormaPagamentoBoleto(BoletoVO boletoVO) throws Exception {
        FormaPagamento formaPagamentoDAO;
        try {
            formaPagamentoDAO = new FormaPagamento(this.getCon());

            FormaPagamentoVO formaPagamentoVO = null;
            List<FormaPagamentoVO> formasPgtConvenio = formaPagamentoDAO.consultarFormaPagamentoComConvenioCobranca(true, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            for (FormaPagamentoVO form : formasPgtConvenio) {
                if (form.getConvenioCobrancaVO() != null && form.getConvenioCobrancaVO().getCodigo().equals(boletoVO.getConvenioCobrancaVO().getCodigo())) {
                    formaPagamentoVO = form;
                    break;
                }
            }
            if (formaPagamentoVO == null) {
                formaPagamentoVO = formaPagamentoDAO.consultarFormaPorConvenio(boletoVO.getConvenioCobrancaVO());
                if (UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                    formaPagamentoVO = new FormaPagamentoVO();
                    formaPagamentoVO.setDescricao("BOLETO BANCÁRIO");
                    formaPagamentoVO.setTipoFormaPagamento("BB");
                    formaPagamentoVO = formaPagamentoDAO.criarOuConsultarSeExistePorFlag(formaPagamentoVO);
                }
            }

            if (formaPagamentoVO == null || UteisValidacao.emptyNumber(formaPagamentoVO.getCodigo())) {
                throw new Exception("Forma de pagamento para boleto não encontrada");
            }
            return formaPagamentoVO;
        } finally {
            formaPagamentoDAO = null;
        }
    }

    private ContratoVO obterContratoParcelas(List<MovParcelaVO> listaParcelasPagar) {
        if (UteisValidacao.emptyList(listaParcelasPagar)) {
            return null;
        }

        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = listaParcelasPagar.get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : listaParcelasPagar) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                return null;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        return contratoVO;
    }

    public void marcarBoletoComErro(BoletoVO boletoVO, Exception exception) {
        if (boletoVO != null && !UteisValidacao.emptyNumber(boletoVO.getCodigo())) {
            Boleto boletoDAO = null;
            try {
                boletoDAO = new Boleto(getCon());

                if (boletoVO.getSituacao().equals(SituacaoBoletoEnum.NENHUMA)) {
                    boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.ERRO);
                }

                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, exception.getMessage());
                boletoDAO.alterar(boletoVO);
            } catch (Exception ex) {
                Uteis.logar(ex, CobrancaOnlineService.class);
            } finally {
                boletoDAO = null;
            }
        } else {
            exception.printStackTrace();
            Uteis.logarDebug("Boleto null " + exception.getMessage());
        }
    }

    public void estornarRecibo(BoletoVO boletoVO, UsuarioVO usuarioVO) throws Exception {
        if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.CANCELADO) ||
                UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo())) {
            return;
        }

        BoletoMovParcela boletoMovParcelaDAO;
        ReciboPagamento reciboPagamentoDAO;
        MovPagamento movPagamentoDAO;
        MovProdutoParcela movProdutoParcelaDAO;
        try {
            boletoMovParcelaDAO = new BoletoMovParcela(this.getCon());
            reciboPagamentoDAO = new ReciboPagamento(this.getCon());
            movPagamentoDAO = new MovPagamento(this.getCon());
            movProdutoParcelaDAO = new MovProdutoParcela(this.getCon());

            EstornoReciboControle estorno = new EstornoReciboControle();
            EstornoReciboVO estornoVO = new EstornoReciboVO();
            estornoVO.setExcluirNFSe(true);
            estornoVO.setResponsavelEstornoRecivo(usuarioVO);

            List<MovPagamentoVO> listaMovPagamento = new ArrayList();
            listaMovPagamento.add(movPagamentoDAO.consultarPorChavePrimaria(boletoVO.getMovPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
            estornoVO.setListaMovPagamento(listaMovPagamento);

            if (UteisValidacao.emptyList(boletoVO.getListaParcelas())) {
                Uteis.logarDebug("BoletoOnlineService - Lista de parcelas vazia | Boleto " + boletoVO.getCodigo());
                boletoVO.setListaParcelas(boletoMovParcelaDAO.consultarMovParcelasDoBoleto(boletoVO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }

            estornoVO.setListaMovParcela(boletoVO.getListaParcelas());
            estornoVO.setEstornarOperadora(false);
            estornoVO.setReciboPagamentoVO(reciboPagamentoDAO.consultarPorChavePrimaria(boletoVO.getReciboPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

            estorno.setEstornoReciboVO(estornoVO);

            reciboPagamentoDAO.estornarReciboPagamento(estornoVO, movPagamentoDAO, movProdutoParcelaDAO, null, null);
        } finally {
            boletoMovParcelaDAO = null;
            reciboPagamentoDAO = null;
            movPagamentoDAO = null;
            movProdutoParcelaDAO = null;
        }
    }

    public static void main(String[] args) {
        try {
            String chave = "teste";
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }

            Uteis.debug = true;
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, con);
            BoletoOnlineService boletoOnlineService = new BoletoOnlineService(con);

            Integer codEmpresa = 1;
            Integer codReciboPagamento = 1;

            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
            ReciboPagamentoVO reciboPagamentoVO = reciboPagamentoDAO.consultarPorChavePrimaria(codReciboPagamento, Uteis.NIVELMONTARDADOS_TODOS);

            MovPagamento movPagamentoDAO = new MovPagamento(con);
            List<MovPagamentoVO> movPagamentosVO = movPagamentoDAO.consultarPorCodigoRecibo(reciboPagamentoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS);
            reciboPagamentoVO.setPagamentosDesteRecibo(movPagamentosVO);

            boletoOnlineService.processamentoPosPagamento(reciboPagamentoVO, empresaVO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}