package servicos.impl.boleto;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.MovParcela;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class ThreadGerarBoletosOnlineGrandeVolume implements Runnable {

    private List<MovParcelaVO> parcelas;
    private String emailParaNotificacao;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Connection connection;
    private UsuarioVO usuarioVO;
    private EmpresaVO empresaVO;

    public ThreadGerarBoletosOnlineGrandeVolume(List<MovParcelaVO> parcelas, ConvenioCobrancaVO convenioCobrancaVO, String emailParaNotificacao, UsuarioVO usuarioVO,
                                                Connection con, EmpresaVO empresaVO) {
        this.parcelas = parcelas;
        this.emailParaNotificacao = emailParaNotificacao;
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.connection = con;
        this.usuarioVO = usuarioVO;
        this.empresaVO = empresaVO;
    }

    @Override
    public void run() {
        System.out.println("Iniciando ThreadGerarBoletosOnlineGrandeVolume...");
        System.out.println("Convênio de Envio: " + convenioCobrancaVO.getDescricao());
        Boleto boletoDAO = null;
        Pessoa pessoaDAO = null;
        MovParcela movParcelaDAO = null;
        try {
            StringBuilder corpoEmail = new StringBuilder();
            corpoEmail.append("Resultado ao gerar boletos no Gestão Boletos Online <br><br>");

            boletoDAO = new Boleto(this.connection);
            pessoaDAO = new Pessoa(this.connection);
            movParcelaDAO = new MovParcela(this.connection);
            for (MovParcelaVO parcela : parcelas) {
                System.out.println("Gerando boleto para parcela: " + parcela.getDescricao());

                List<BoletoVO> boletosGerados = new ArrayList<>();
                PessoaVO pessoa = pessoaDAO.consultarPorChavePrimaria(parcela.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                corpoEmail.append("Pessoa: ").append(pessoa.getNome()).append("; Parcela: ").append(parcela.getCodigo()).append("; Resultado: ");

                List<MovParcelaVO> listaTemporaria = new ArrayList<>();
                listaTemporaria.add(parcela);
                try {
                    // Customização SESC para Boleto Caixa Online registar na impressão
                    // O padrão é sempre registar, mas caso configuração marcada, pode mudar comportamento
                    boolean registrarBoletoAgora = true;
                    if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE)) {
                        registrarBoletoAgora = !convenioCobrancaVO.isRegistrarBoletoOnlineSomenteNaImpressao();
                    }

                    boletosGerados.addAll(boletoDAO.gerarBoletoPorParcela(pessoa, this.convenioCobrancaVO, listaTemporaria,
                            this.usuarioVO, OrigemCobrancaEnum.ZW_MANUAL_GESTAO_BOLETOS_ONLINE, true, registrarBoletoAgora));
                    if (!UteisValidacao.emptyList(boletosGerados)) {
                        System.out.println("Sucesso ao gerar boleto para parcela: " + parcela.getCodigo());
                        corpoEmail.append("Sucesso <br>");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("Erro ao gerar boleto para parcela: " + parcela.getCodigo());
                    corpoEmail.append("Erro - ").append(e.getMessage()).append("<br>");
                }
            }

            //Liberar as parcelas para gerar novos boletos
            movParcelaDAO.mudarSituacaoGerandoBoletoGestaoBoletosOnline(false, parcelas);

            enviarNotificacaoDeTermino(corpoEmail.toString());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            boletoDAO = null;
            pessoaDAO = null;
            movParcelaDAO = null;
        }
    }

    private void enviarNotificacaoDeTermino(String corpoEmail) {
        //Envia e-mail utilizando a conta do cliente configurada no CRM.
        System.out.println("Enviando notificação para: " + emailParaNotificacao);
        try {
            //Obter configurações do envio de email
            ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO = getConfiguracaoSMTPNoReply();
            UteisEmail email = new UteisEmail();
            //Assunto do email será "Notificação de geração de boletos"
            email.novo("Notificação de geração de boletos", configuracaoSistemaCRMVO);
            //Remetente é o usuario logado
            email.setRemetente(usuarioVO);
            //Converte o e-mail que está em String, para Array de String
            String[] emailArray = new String[] {emailParaNotificacao};

            email.enviarEmailN(emailArray, corpoEmail, "Notificação de geração de boletos", empresaVO.getNome());
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("Erro ao tentar enviar e-mail notificação para: " + emailParaNotificacao);
        }
    }

    private ConfiguracaoSistemaCRMVO getConfiguracaoSMTPNoReply() {
        ConfiguracaoSistemaCRMVO config = getConfiguracaoSMTPRobo();
        config.setEmailPadrao(PropsService.getPropertyValue(PropsService.smtpEmailNoReply));
        return config;
    }

    private ConfiguracaoSistemaCRMVO getConfiguracaoSMTPRobo() {
        ConfiguracaoSistemaCRMVO config = new ConfiguracaoSistemaCRMVO();
        config.setLogin(PropsService.getPropertyValue(PropsService.smtpLoginRobo));
        config.setEmailPadrao(PropsService.getPropertyValue(PropsService.smtpEmailRobo));
        config.setSenha(PropsService.getPropertyValue(PropsService.smtpSenhaRobo));
        config.setConexaoSegura(PropsService.isTrue(PropsService.smtpConexaoSeguraRobo));
        config.setIniciarTLS(PropsService.isTrue(PropsService.iniciarTLS));
        config.setMailServer(PropsService.getPropertyValue(PropsService.smtpServerRobo));
        return config;
    }

}
