package servicos.impl.boleto.asaas;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.boleto.AbstractBoletoOnlineServiceComum;
import servicos.impl.boleto.AtributoBoletoEnum;
import servicos.interfaces.BoletoOnlineServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Rodrigo Estulano
 * Date: 04/05/2023
 */
public class BoletoAsaasService extends AbstractBoletoOnlineServiceComum implements BoletoOnlineServiceInterface {

    private String chaveBanco;
    private Boleto boletoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Empresa empresaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private String URL_API_ASAAS = "";
    private Usuario usuarioDAO;

    public BoletoAsaasService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        inicializarDAO(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    public BoletoAsaasService(Connection con, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        super(con);
        this.convenioCobrancaVO = convenioCobrancaVO;
        inicializarDAO(con);
        popularInformacoes();
    }

    private void inicializarDAO(Connection con) throws Exception {
        this.chaveBanco = DAO.resolveKeyFromConnection(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.empresaDAO = new Empresa(con);
        this.usuarioDAO = new Usuario(con);
        this.boletoDAO = new Boleto(con);
    }

    @Override
    public BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception {
        BoletoVO boletoVO = null;
        try {
            boletoVO = criarBoletoVO(boletoOnlineTO, this.convenioCobrancaVO, this.getCon());
            if (boletoOnlineTO.isVerificarBoletoExistente()) {
                BoletoVO boletoExistenteVO = verificarBoletoExistente(boletoVO);
                if (boletoExistenteVO != null &&
                        !UteisValidacao.emptyNumber(boletoExistenteVO.getCodigo())) {
                    return boletoExistenteVO;
                }
            }

            this.boletoDAO.incluir(boletoVO);

            EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            PessoaCPFTO pessoaCPFTO = this.boletoDAO.obterDadosPessoaPagador(empresaVO.getCodigo(), boletoVO.getPessoaVO(), true, true);
            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(pessoaCPFTO.getPessoa(), Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_BOLETO);

            validarDados(boletoVO);
            validarCamposObrigatorioPagador(pessoaCPFTO);

            //Objeto cliente
            ClienteAsaasDTO clienteAsaas = new ClienteAsaasDTO();
            clienteAsaas.setNome(pessoaCPFTO.getNomeResponsavel());
            clienteAsaas.setCpfCnpj(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", ""));
            clienteAsaas.setEmail(obterEmailPessoa(pessoaVO));
            clienteAsaas.setTelefone(obterTelefonePessoa(pessoaVO));
            EnderecoVO enderecoVO = obterEnderecoVO(pessoaVO);
            clienteAsaas.setEnderecoVO(enderecoVO);

            //Incluir cliente Asaas
            String idClienteAsaas = "";
            if (UteisValidacao.emptyString(pessoaVO.getIdAsaas())) {
                try {
                    idClienteAsaas = incluirClienteAsaas(clienteAsaas, boletoVO, pessoaVO);
                } catch (Exception ex) {
                    throw new ConsistirException("Falha ao inserir o cliente no portal Asaas: " + ex.getMessage());
                }
            } else {
                idClienteAsaas = pessoaVO.getIdAsaas();
            }
            if (UteisValidacao.emptyString(idClienteAsaas)) {
                throw new ConsistirException("Falha ao inserir o cliente no portal Asaas");
            }

            //Incluir Boleto Asaas
            try {
                incluirBoletoAsaas(idClienteAsaas, boletoVO, empresaVO);
            } catch (Exception ex) {
                throw new ConsistirException("Falha ao gerar o boleto no portal Asaas: " + ex.getMessage());
            }

            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.empresa_nome, empresaVO.getNome());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.empresa_razao_social, empresaVO.getRazaoSocial());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.empresa_cnpj, this.convenioCobrancaVO.getCnpj());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_nome, pessoaCPFTO.getNomeResponsavel());
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_documento, Uteis.formatarCpfCnpj(pessoaCPFTO.getCpfResponsavel(), true));
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.pagador_end_logradouro, enderecoVO.getEndereco());

            this.boletoDAO.alterar(boletoVO);
            return boletoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarBoletoComErro(boletoVO, ex);
            throw ex;
        }
    }

    public String incluirClienteAsaas(ClienteAsaasDTO clienteAsaasDTO, BoletoVO boletoVO, PessoaVO pessoaVO) throws Exception {

        JSONObject jsonCliente = montarClienteJSON(clienteAsaasDTO);
        boletoVO.setParamsEnvio(jsonCliente.toString());

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/customers";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, jsonCliente.toString(), MetodoHttpEnum.POST);

        if (resposta.getHttpStatus() != 200) {
            throw new ConsistirException(resposta.getResponse());
        } else {
            JSONObject jsonObject = new JSONObject(resposta.getResponse());
            if (jsonObject.has("id") && !UteisValidacao.emptyString(jsonObject.optString("id"))) {
                String clientId = jsonObject.optString("id");
                pessoaVO.setIdAsaas(clientId);
                new Pessoa(getCon()).alterarIdAsaas(pessoaVO);
                return clientId;
            }
        }
        return "";
    }

    public JSONObject incluirBoletoAsaas(String idClienteAsaas, BoletoVO boletoVO, EmpresaVO empresaVO) throws Exception {

        JSONObject jsonBoleto = montarBoletoJSON(idClienteAsaas, boletoVO, empresaVO);

        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/payments";
        RequestHttpService service = new RequestHttpService();

        try {
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, jsonBoleto.toString(), MetodoHttpEnum.POST);
            boletoVO.setParamsEnvio(jsonBoleto.toString());
            if (resposta.getHttpStatus() != 200) {
                throw new ConsistirException(tratarMensagemErro(resposta.getResponse()));
            } else {
                JSONObject jsonObject = new JSONObject(resposta.getResponse());
                CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO = new CobrancaAsaasRetornoDTO(jsonObject);
                boletoVO.setIdExterno(cobrancaAsaasRetornoDTO.getId());
                boletoVO.setLinkBoleto(obterLinkBoleto(cobrancaAsaasRetornoDTO.getInvoiceUrl()));
                boletoVO.setNossoNumero(cobrancaAsaasRetornoDTO.getNossoNumero());
                boletoVO.setNumeroExterno(cobrancaAsaasRetornoDTO.getInvoiceNumber());
                boletoVO.setNumeroInterno(cobrancaAsaasRetornoDTO.getExternalReference());
                boletoVO.setParamsResposta(resposta.getResponse());
                boletoVO.setSituacao(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO);
                try {
                    boletoVO.setLinhaDigitavel(obterLinhaDigitavel(cobrancaAsaasRetornoDTO));
                } catch (Exception ignore) {
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
            boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
            boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
        }
        return jsonBoleto;
    }

    public String obterLinkBoleto(String invoiceURL) {
        return invoiceURL.replace("/i/", "/b/preview/") + "?isAbTestBankSlipPreview=true";
    }

    public String obterLinhaDigitavel(CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/payments/" + cobrancaAsaasRetornoDTO.getId() + "/identificationField";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() == 200) {
            JSONObject json = new JSONObject(resposta.getResponse());
            if (!UteisValidacao.emptyString(json.optString("identificationField"))) {
                return json.optString("identificationField");
            }
        }
        return "";
    }

    public String tratarMensagemErro(String resposta) {
        if (!UteisValidacao.emptyString(resposta)) {
            JSONObject jsonObject = new JSONObject(resposta);
            if (jsonObject.has("errors") && jsonObject.optJSONArray("errors") != null) {
                JSONObject jsonErrors = new JSONObject(jsonObject.getJSONArray("errors").get(0).toString());
                if (!UteisValidacao.emptyString(jsonErrors.optString("description"))) {
                    return jsonErrors.optString("description");
                }
            }
            return resposta;
        }
        return "Erro ao realizar operação no Asaas";
    }

    public JSONObject montarClienteJSON(ClienteAsaasDTO clienteAsaasDTO) {
        JSONObject params = new JSONObject();

        //parâmetros obrigatórios
        params.put("name", clienteAsaasDTO.getNome());
        params.put("cpfCnpj", clienteAsaasDTO.getCpfCnpj());
        params.put("notificationDisabled", !this.convenioCobrancaVO.isEnviarNotificacoes());

        //parâmetros opcionais - Importante enviar pois o aluno fica com o cadastro completo lá no portal facilitando conferências e recebendo notificações do Asaas.
        if (!UteisValidacao.emptyString(clienteAsaasDTO.getEmail())) {
            params.put("email", clienteAsaasDTO.getEmail());
        }
        if (!UteisValidacao.emptyString(clienteAsaasDTO.getTelefone())) {
            params.put("mobilePhone", clienteAsaasDTO.getTelefone());
        }

        if (clienteAsaasDTO.getEnderecoVO() != null) {
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getEndereco())) {
                params.put("address", clienteAsaasDTO.getEnderecoVO().getEndereco());
            }
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getNumero())) {
                params.put("addressNumber", clienteAsaasDTO.getEnderecoVO().getNumero());
            }
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getComplemento())) {
                params.put("complement", clienteAsaasDTO.getEnderecoVO().getComplemento());
            }
            if (!UteisValidacao.emptyString(clienteAsaasDTO.getEnderecoVO().getCep())) {
                params.put("postalCode", clienteAsaasDTO.getEnderecoVO().getCep());
            }
        }
        return params;
    }

    public JSONObject montarBoletoJSON(String idClienteAsaas, BoletoVO boletoVO, EmpresaVO empresaVO) {
        try {
            JSONObject params = new JSONObject();
            params.put("customer", idClienteAsaas); //Identificador único do cliente no Asaas
            params.put("billingType", TipoCobrancaAsaasEnum.BOLETO.getDescricao()); //tipo da cobrança (Boleto, Cartão ou Pix)
            params.put("value", boletoVO.getValor()); //Valor da cobrança
            params.put("dueDate", Calendario.getDataAplicandoFormatacao(boletoVO.getDataVencimento(), "yyyy-MM-dd")); //Data de vencimento da cobrança
            params.put("externalReference", gerarNumeroAleatorio()); //Campo livre para busca

            //INSTRUÇÕES
            String instrucoesBoleto = this.convenioCobrancaVO.getInstrucoesBoleto();
            try {
                if (instrucoesBoleto.toUpperCase().contains("TAG_MATRICULA")) {
                    ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(boletoVO.getPessoaVO().getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        instrucoesBoleto = processarInstrucaoMatricula(instrucoesBoleto, clienteVO.getMatricula());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            params.put("description", instrucoesBoleto);

            //MULTA/JUROS
            if (empresaVO.getCobrarAutomaticamenteMultaJuros() && empresaVO.isCobrarMultaJurosAsaas()) {
                if (!UteisValidacao.emptyNumber(empresaVO.getValorMultaAsaas())) {
                    JSONObject multa = new JSONObject();
                    multa.put("value", Uteis.arredondarForcando2CasasDecimais(empresaVO.getValorMultaAsaas()));
                    params.put("fine", multa);
                }
                if (!UteisValidacao.emptyNumber(empresaVO.getValorJurosAsaas())) {
                    JSONObject multa = new JSONObject();
                    multa.put("value", Uteis.arredondarForcando2CasasDecimais(empresaVO.getValorJurosAsaas() * 30)); //juros mensal calculado com base no juros por dia
                    params.put("interest", multa);
                }
            }

            //DESCONTO PORCENTAGEM/VALOR
            if (!UteisValidacao.emptyNumber(boletoVO.getPorcentagemDescontoPagAntecipado()) || !UteisValidacao.emptyNumber(boletoVO.getValorDescontoPagAntecipado())) {
                String tipoDesconto = !UteisValidacao.emptyNumber(boletoVO.getPorcentagemDescontoPagAntecipado()) ? String.valueOf(TipoDescontoAsaasEnum.PERCENTAGE) : String.valueOf(TipoDescontoAsaasEnum.FIXED);
                double valorDesconto = !UteisValidacao.emptyNumber(boletoVO.getPorcentagemDescontoPagAntecipado()) ? boletoVO.getPorcentagemDescontoPagAntecipado() : boletoVO.getValorDescontoPagAntecipado();
                int dataMaximaPagarBoletoComDesconto = Uteis.getDiaMesData(boletoVO.getDataVencimento()) - boletoVO.getDiaMesDescontoPagAntecipado(); //no boleto ele imprime pagamento até o dia x tem desconto. Esse dia X tem que ser o diaMesDescontoPagAntecipado.

                JSONObject desconto = new JSONObject();
                desconto.put("value", valorDesconto); //Valor percentual de desconto a ser aplicado sobre o valor da cobrança
                desconto.put("dueDateLimitDays", dataMaximaPagarBoletoComDesconto);
                desconto.put("type", tipoDesconto); //Tipo de desconto
                params.put("discount", desconto);
            }

            return params;

        } catch (Exception ex) {
        }

        return new JSONObject();
    }

    public CobrancaAsaasRetornoDTO obterDadosCobranca(String idCobranca) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/payments/" + idCobranca;
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);

        if (resposta.getHttpStatus() == 200) {
            JSONObject json = new JSONObject(resposta.getResponse());
            return new CobrancaAsaasRetornoDTO(json);
        }
        return new CobrancaAsaasRetornoDTO();
    }

    public RespostaHttpDTO obterDadosCobrancaRequest(String idCobranca) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        String endpoint = URL_API_ASAAS + "api/v3/payments/" + idCobranca;
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.GET);
        return resposta;
    }

    public RespostaHttpDTO consultarBoletosPagos(String dtInicio, String dtFim) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

        //Filtros
        Map<String, String> params = new HashMap<String, String>();
        params.put("paymentDate[ge]", dtInicio); //yyyy-MM-dd
        params.put("paymentDate[le]", dtFim); //yyyy-MM-dd
        params.put("limit", "50"); //yyyy-MM-dd

        String endpoint = URL_API_ASAAS + "api/v3/payments";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, params, null, MetodoHttpEnum.GET);
        return resposta;
    }


    private void validarCamposObrigatorioPagador(PessoaCPFTO pessoaCPFTO) throws Exception {

        List<String> camposInvalidos = new ArrayList<>();

        if (!SuperVO.verificaCPF(pessoaCPFTO.getCpfResponsavel())) {
            camposInvalidos.add(" CPF");
        }

        String campos = "";
        if (!UteisValidacao.emptyList(camposInvalidos)) {
            for (String campo : camposInvalidos) {
                campos += campo + ",";
            }
            if (camposInvalidos.size() > 1) {
                String mensagem = "Os campos: " + Uteis.removerUltimoCaractere(campos) + " do aluno são obrigatórios!";
                throw new ConsistirException(mensagem);
            } else {
                String mensagem = "O " + Uteis.removerUltimoCaractere(campos) + " do aluno é obrigatório!";
                throw new ConsistirException(mensagem);
            }
        }
    }

    private void validarDados(BoletoVO boletoVO) throws Exception {
        if (UteisValidacao.emptyString(this.chaveBanco)) {
            throw new ConsistirException("Operação não permitida, não foi possível identificar a chaveZW.");
        }
        if (boletoVO.getDataVencimento() == null) {
            throw new ConsistirException("O parâmetro dataVencimento é obrigatório para registrar o boleto no site do itaú.");
        }
        if (UteisValidacao.emptyNumber(boletoVO.getValor())) {
            throw new ConsistirException("O parâmetro valorCobrado é obrigatório para registrar o boleto no site do itaú.");
        }
    }

    @Override
    public void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception {
        Boleto boletoDAO;
        try {
            if (UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                throw new Exception("Não foi possível obter o id externo do boleto para cancelar");
            }
            boletoDAO = new Boleto(this.getCon());
            JSONObject jsonBase = boletoDAO.gerarBaseJSON(usuarioVO, operacao);
            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar", jsonBase.toString());

            Map<String, String> headers = new HashMap<>();
            headers.put("access_token", this.convenioCobrancaVO.getCodigoAutenticacao01());

            String endpoint = URL_API_ASAAS + "api/v3/payments/" + boletoVO.getIdExterno();
            RequestHttpService service = new RequestHttpService();
            RespostaHttpDTO resposta = service.executeRequest(endpoint, headers, null, null, MetodoHttpEnum.DELETE);

            if (resposta.getHttpStatus() == 200) {
                JSONObject json = new JSONObject(resposta.getResponse());
                if (json.has("deleted") && json.optBoolean("deleted")) {
                    String operacaoGravar = ("Cancelamento de Boleto - Cód. " + boletoVO.getCodigo() + " | Id: " + boletoVO.getIdExterno());
                    JSONObject jsonEstorno;
                    if (origemProcessoManutencao) {
                        jsonEstorno = boletoDAO.gerarBaseJSONProcesso(usuarioVO, operacaoGravar);
                    } else {
                        jsonEstorno = boletoDAO.gerarBaseJSON(usuarioVO, operacaoGravar);
                    }
                    boletoVO.setJsonEstorno(jsonEstorno.toString());
                    boletoDAO.alterarJsonEstorno(boletoVO);
                    if (!origemProcessoManutencao) {
                        boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                        estornarRecibo(boletoVO, usuarioVO);
                    }
                }
            } else {
                throw new Exception("Não foi possível cancelar, tente novamente mais tarde. " + tratarMensagemErro(resposta.getResponse()));
            }
        } finally {
            boletoDAO = null;
        }
    }

    @Override
    public void processarWebhook(BoletoVO boletoVO, String json) throws Exception {
        processarBoletoAsaas(boletoVO, new JSONObject(json), this.usuarioDAO.getUsuarioRecorrencia(), "processarWebhook");
    }

    private SituacaoBoletoEnum obterSituacaoBoleto(CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO) {
        SituacaoBoletoEnum situacaoBoletoEnum = null;
        StatusAsaasEnum statusAsaasEnum = cobrancaAsaasRetornoDTO.getStatus();
        if ((cobrancaAsaasRetornoDTO.isDeleted() && UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate())) || statusAsaasEnum.equals(StatusAsaasEnum.REFUNDED)) {
            situacaoBoletoEnum = SituacaoBoletoEnum.CANCELADO;
        } else if ((statusAsaasEnum.equals(StatusAsaasEnum.PENDING) && UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate())) || statusAsaasEnum.equals(StatusAsaasEnum.AWAITING_RISK_ANALYSIS) ||
                statusAsaasEnum.equals(StatusAsaasEnum.OVERDUE)) {
            situacaoBoletoEnum = SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO;
        } else if (statusAsaasEnum.equals(StatusAsaasEnum.RECEIVED) || statusAsaasEnum.equals(StatusAsaasEnum.CONFIRMED) ||
                (!UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate()))) {
            if (cobrancaAsaasRetornoDTO.getValue() > 0) {
                situacaoBoletoEnum = SituacaoBoletoEnum.PAGO;
            } else {
                situacaoBoletoEnum = SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO;
            }
        }
        return situacaoBoletoEnum;
    }

    @Override
    public void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.getCon());

            if (UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                throw new Exception("Boleto não tem idExterno Asaas");
            }

            JSONObject jsonBase = boletoDAO.gerarBaseJSON(usuarioVO, operacao);
            jsonBase.put("idExterno", boletoVO.getIdExterno());
            boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar", jsonBase.toString());

            RespostaHttpDTO resposta = obterDadosCobrancaRequest(boletoVO.getIdExterno());

            if (resposta.getHttpStatus() == 200) {
                JSONObject json = new JSONObject(resposta.getResponse());
                boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", resposta.getResponse());
                processarBoletoAsaas(boletoVO, json, usuarioVO, operacao);
            } else {
                throw new Exception("Erro ao sincronizar boleto: " + tratarMensagemErro(resposta.getResponse()));
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
        }
    }

    private void processarBoletoAsaas(BoletoVO boletoVO, JSONObject json, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO = null;
        BoletoVO boletoVOAnterior = null;
        try {
            boletoDAO = new Boleto(this.getCon());
            boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoAsaas | " + operacao, json.toString());

            CobrancaAsaasRetornoDTO cobrancaAsaasRetornoDTO = new CobrancaAsaasRetornoDTO(json);

            if (boletoVO == null) {
                if (UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getId())) {
                    throw new Exception("Boleto Asaas não tem Id informado");
                }
                boletoVO = boletoDAO.consultarPorIdexternoTipo(cobrancaAsaasRetornoDTO.getId(), TipoBoletoEnum.ASAAS, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado");
            }

            boletoVOAnterior = (BoletoVO) boletoVO.getClone(true);

            //em caso de cancelamento
            if (cobrancaAsaasRetornoDTO.isDeleted() && UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate())) {
                if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.CANCELADO)) {
                    //boleto já está cancelado
                    return;
                }
                if (!UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo())) {
                    throw new Exception("Boleto está pago por isso não é possível alterar para cancelado. Boleto " + boletoVO.getCodigo());
                }
                boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                return;
            }


            //obter situacao conforme a os dados recebidos
            SituacaoBoletoEnum situacaoBoletoEnum = obterSituacaoBoleto(cobrancaAsaasRetornoDTO);
            boletoVO.setSituacao(situacaoBoletoEnum);
            boletoVO.setValorLiquido(cobrancaAsaasRetornoDTO.getNetValue());
            if (!UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate())) {
                boletoVO.setDataPagamento(Uteis.getDate(cobrancaAsaasRetornoDTO.getPaymentDate()));
            }
            if (!UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getCreditDate())) {
                boletoVO.setDataCredito(Uteis.getDate(cobrancaAsaasRetornoDTO.getCreditDate()));
            }

            boletoVO.setTransactionReceiptUrl(cobrancaAsaasRetornoDTO.getTransactionReceiptUrl());

            if (!UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getPaymentDate()) && !UteisValidacao.emptyString(cobrancaAsaasRetornoDTO.getConfirmedDate())) {
                if (!UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                    boletoDAO.incluirBoletoHistorico(boletoVO, operacao, "BOLETO JÁ ESTÁ PAGO");
                } else {
                    boletoVO.setValorPago(cobrancaAsaasRetornoDTO.getValue());
                    boletoVO.setValorPossivelDesconto(cobrancaAsaasRetornoDTO.getValue());
                    //gerar recibo pagamento
                    gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);
                }
            }

            //atualizar dados do boleto
            boletoDAO.alterar(boletoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoAsaas | ERRO", ex.getMessage());
            }
            throw ex;
        } finally {
            if (boletoVOAnterior != null && boletoVO != null) {
                if (!boletoVOAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                    JSONObject jsonSituacao = new JSONObject();
                    jsonSituacao.put("situacao_anterior", boletoVOAnterior.getSituacao().getDescricao());
                    jsonSituacao.put("situacao_atual", boletoVO.getSituacao().getDescricao());
                    boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoAsaas - ALTERAR SITUAÇÃO", jsonSituacao.toString());
                }
            }

            boletoDAO = null;
        }
    }

    private String obterEmailPessoa(PessoaVO pessoaVO) {
        for (EmailVO emailVO : pessoaVO.getEmailVOs()) {
            if (UteisValidacao.validaEmail(emailVO.getEmail())) {
                return emailVO.getEmail();
            }
        }
        return "";
    }

    private String obterTelefonePessoa(PessoaVO pessoaVO) {
        for (TelefoneVO telefoneVO : pessoaVO.getTelefoneVOs()) {
            if (Uteis.validarTelefoneCelular(UteisTelefone.removerCaracteresEspeciais(telefoneVO.getNumero())))
                return UteisTelefone.removerCaracteresEspeciais(telefoneVO.getNumero());
        }
        return "";
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API_ASAAS = PropsService.getPropertyValue(PropsService.urlApiAsaasProducao);
            } else {
                this.URL_API_ASAAS = PropsService.getPropertyValue(PropsService.urlApiAsaasSandbox);

            }
        }
    }

    private Integer gerarNumeroAleatorio() {
        SimpleDateFormat sdf = new SimpleDateFormat("ddHHmmss");
        Double vlrRandom = 0.0;
        vlrRandom = Math.random() * Double.valueOf(sdf.format(Calendario.hoje()));
        return vlrRandom.intValue();
    }

    private String processarInstrucaoMatricula(String instrucao, String matriculaCliente) throws Exception {
        if (instrucao.contains("<matricula>")) {
            int inicioMatricula = instrucao.indexOf("<matricula>");
            int finalMatricula = instrucao.indexOf("</matricula>");
            String aux = instrucao.substring(0, inicioMatricula);
            aux = aux + instrucao.substring((inicioMatricula + 11), finalMatricula);
            aux = aux.replaceAll("TAG_MATRICULA", matriculaCliente != null ? matriculaCliente : "");
            aux += instrucao.substring(finalMatricula + 12);
            instrucao = aux;
        }
        return instrucao;
    }
}
