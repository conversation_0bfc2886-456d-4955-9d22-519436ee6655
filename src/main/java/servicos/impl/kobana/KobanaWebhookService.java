package servicos.impl.kobana;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.StatusLoteKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusPagamentoKobanaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.*;
import org.json.JSONObject;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 08/11/2024.
 */

public class KobanaWebhookService extends SuperEntidade {

    private static final Integer TIMEOUT_REQUEST = 10000;

    public KobanaWebhookService() throws Exception {
    }

    public KobanaWebhookService(Connection con) throws Exception {
        super(con);
    }

    public void processarWebhook(String chaveZW, String dados) throws Exception {

        try {
            Uteis.logarDebug("########### INICIO DE PROCESSAMENTO WEBHOOK DA KOBANA | " + Calendario.hoje());

            JSONObject json = new JSONObject(dados);
            boolean webhookDeLote = !UteisValidacao.emptyString(json.getString("event_code")) && json.getString("event_code").contains("batch");
            KobanaWebhookDTO webhookKobana = new KobanaWebhookDTO(json, webhookDeLote);

            if (webhookDeLote) {
                LoteKobanaVO loteKobanaZW = obterLoteKobanaPorUID(webhookKobana.getUid(), Uteis.NIVELMONTARDADOS_TODOS);
                //para logs
                KobanaWebhookVO kobanaWebhookVO = salvarKobanaWebhookDeLote(webhookKobana, dados, loteKobanaZW);
                if (loteKobanaZW == null || UteisValidacao.emptyNumber(loteKobanaZW.getCodigo())) {
                    throw new Exception("Não foi possível obter o loteKobanaItemVO: " + webhookKobana.getUid());
                }

                boolean precisaAtualizarStatusRegistroLote = !webhookKobana.getRegistrationStatusKobanaEnum().equals(loteKobanaZW.getRegistration_status());
                boolean precisaAtualizarStatusLote = validarSePrecisaAtualizarStatusDoLote(webhookKobana, loteKobanaZW);

                if (!precisaAtualizarStatusRegistroLote && !precisaAtualizarStatusLote) {
                    //Não precisa mudar nada pois o status do registro do lote ainda é o mesmo, então apenas atualizar webhook para logs
                    atualizarKobanaWebhook(kobanaWebhookVO);
                    Uteis.logarDebug("Não mudou o status do registro do lote, não precisa percorrer todo o fluxo...colocando como processado...");
                    Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DA KOBANA | " + " | UID: " +
                            webhookKobana.getUid() + " | " + Calendario.hoje());
                    return;
                }

                try {
                    getCon().setAutoCommit(false);
                    LoteKobanaVO loteKobanaVO = new LoteKobanaVO();
                    loteKobanaVO.setRegistration_status(webhookKobana.getRegistrationStatusKobanaEnum());
                    loteKobanaVO.setUpdated_at(webhookKobana.getUpdated_at());
                    loteKobanaVO.setCodigo(loteKobanaZW.getCodigo());
                    atualizarLoteKobana(loteKobanaVO);
                    if (precisaAtualizarStatusLote) {
                        atualizarStatusLoteKobana(webhookKobana.getStatusLoteKobanaEnum(), loteKobanaZW.getCodigo());
                    }
                    getCon().commit();
                } catch (Exception ex) {
                    getCon().rollback();
                    getCon().setAutoCommit(true);
                    ex.printStackTrace();
                    throw ex;
                } finally {
                    getCon().setAutoCommit(true);
                }

                //Processou todo o fluxo com sucesso , agora atualizar webhook para logs
                atualizarKobanaWebhook(kobanaWebhookVO);

                Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DE LOTE KOBANA " + " | UID: " +
                        webhookKobana.getUid() + " | " + Calendario.hoje());
            } else {
                //webhook de pagamento
                LoteKobanaItemVO itemPagamentoZW = obterLoteKobanaItemPorUID(webhookKobana.getUid(), Uteis.NIVELMONTARDADOS_TODOS);
                //para logs
                KobanaWebhookVO kobanaWebhookVO = salvarKobanaWebhookDeItem(webhookKobana, dados, itemPagamentoZW);
                if (itemPagamentoZW == null || UteisValidacao.emptyNumber(itemPagamentoZW.getCodigo())) {
                    throw new Exception("Não foi possível obter o loteKobanaItemVO: " + webhookKobana.getUid());
                }
                boolean precisaAtualizarStatusItem = !webhookKobana.getStatusPagamentoKobanaEnum().equals(itemPagamentoZW.getStatus());
                boolean precisaAtualizarStatusRegistroItem = !webhookKobana.getRegistrationStatusKobanaEnum().equals(itemPagamentoZW.getRegistration_status());

                //mesmo status não precisa processar novamente
                if (!precisaAtualizarStatusItem && !precisaAtualizarStatusRegistroItem) {
                    //Não precisa mudar nada pois o status ainda é o mesmo, então apenas atualizar webhook para logs
                    atualizarKobanaWebhook(kobanaWebhookVO);
                    Uteis.logarDebug("Não mudou o status do item, não precisa percorrer todo o fluxo...colocando como processado...");
                    Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DA KOBANA | " + " | UID: " +
                            webhookKobana.getUid() + " | " + Calendario.hoje());
                    return;
                }

                // Dar baixa no pagamento quando vier sucesso
                // AQUI PRECISA CONTROLAR A TRANSAÇÃO!
                MovConta movContaDAO;
                try {
                    getCon().setAutoCommit(false);
                    movContaDAO = new MovConta(getCon());
                    if (webhookKobana.getStatusPagamentoKobanaEnum().equals(StatusPagamentoKobanaEnum.CONFIRMED)) {
                        quitarMovContaLoteKobana(itemPagamentoZW.getMovcontaVO(), webhookKobana);
                    } else if (webhookKobana.getStatusPagamentoKobanaEnum().equals(StatusPagamentoKobanaEnum.REPROVED) ||
                            webhookKobana.getStatusPagamentoKobanaEnum().equals(StatusPagamentoKobanaEnum.FAILED) ||
                            webhookKobana.getStatusPagamentoKobanaEnum().equals(StatusPagamentoKobanaEnum.REJECTED) ||
                            webhookKobana.getStatusPagamentoKobanaEnum().equals(StatusPagamentoKobanaEnum.CANCELED)) {
                        //reabrir MovConta em caso de erros
                        movContaDAO.prenderOuLiberarMovContaEmLoteDePagamento(false, null, itemPagamentoZW.getMovcontaVO().getCodigo());
                    }
                    IntegracaoKobanaVO integracaoKobanaVO = obterIntegracaoKobana(itemPagamentoZW.getMovcontaVO().getEmpresaVO().getCodigo());
                    IntegracaoKobanaService service = new IntegracaoKobanaService(integracaoKobanaVO, getCon());

                    //Atualizar items no BD
                    LoteKobanaItemVO loteKobanaItemVO = new LoteKobanaItemVO();
                    loteKobanaItemVO.setStatus(webhookKobana.getStatusPagamentoKobanaEnum());
                    loteKobanaItemVO.setRegistration_status(webhookKobana.getRegistrationStatusKobanaEnum());
                    loteKobanaItemVO.setUpdated_at(webhookKobana.getUpdated_at());
                    loteKobanaItemVO.setCodigo(itemPagamentoZW.getCodigo());
                    loteKobanaItemVO.setRejected_error(webhookKobana.getRejected_error());
                    loteKobanaItemVO.setRejected_at(webhookKobana.getRejected_at());
                    List<LoteKobanaItemVO> listaItems = new ArrayList<>();
                    listaItems.add(loteKobanaItemVO);
                    service.atualizarItemsControlandoTransacao(listaItems, false);
                    getCon().commit();
                } catch (Exception ex) {
                    getCon().rollback();
                    getCon().setAutoCommit(true);
                    ex.printStackTrace();
                    throw ex;
                } finally {
                    getCon().setAutoCommit(true);
                    movContaDAO = null;
                }

                //Processou todo o fluxo com sucesso , agora atualizar webhook para logs
                atualizarKobanaWebhook(kobanaWebhookVO);

                Uteis.logarDebug("########### FIM DE PROCESSAMENTO WEBHOOK DE PAGAMENTO KOBANA " + " | UID: " +
                        webhookKobana.getUid() + " | " + Calendario.hoje());
            }

        } catch (Exception ex) {
            Uteis.logarDebug("Não foi possível processar o pixWebhook: Chave: " + chaveZW +
                    " | Dados: " + dados + " | Exception: " + ex.getMessage());
            ex.printStackTrace();
            throw ex;
        }
    }

    private static boolean validarSePrecisaAtualizarStatusDoLote(KobanaWebhookDTO webhookKobana, LoteKobanaVO loteKobanaZW) {
        if (webhookKobana.getStatusLoteKobanaEnum() != null && webhookKobana.getStatusLoteKobanaEnum().equals(StatusLoteKobanaEnum.REPROVED)) {
            return true;
        }
        if (webhookKobana.getStatusLoteKobanaEnum() != null && webhookKobana.getStatusLoteKobanaEnum().equals(StatusLoteKobanaEnum.AWAITING_APPROVAL)) {
            //chegou aguardando aprovação e já estava aprovado, então colocar como Gerado novamente...
            if (loteKobanaZW.getStatus().equals(StatusLoteKobanaEnum.APPROVED)) {
                webhookKobana.setStatusLoteKobanaEnum(StatusLoteKobanaEnum.GERADO);
                return true;
            }
            for (LoteKobanaItemVO item : webhookKobana.getListaLoteKobanaItemVO()) {
                if (item.getStatus().equals(StatusPagamentoKobanaEnum.PENDING) || item.getStatus().equals(StatusPagamentoKobanaEnum.AWAITING_APPROVAL)) {
                    return true;
                } else if (item.getStatus().equals(StatusPagamentoKobanaEnum.CANCELED)) {
                    webhookKobana.setStatusLoteKobanaEnum(StatusLoteKobanaEnum.GERADO);
                    return true;
                }
            }
        }
        if (loteKobanaZW.getStatus().equals(StatusLoteKobanaEnum.AWAITING_APPROVAL) && webhookKobana.getStatusLoteKobanaEnum().equals(StatusLoteKobanaEnum.PENDING)) {
            //não deixar como pending, deixar como gerado mesmo...
            webhookKobana.setStatusLoteKobanaEnum(StatusLoteKobanaEnum.GERADO);
            return true;
        }
        if (loteKobanaZW.getStatus().equals(StatusLoteKobanaEnum.GERADO) && webhookKobana.getStatusLoteKobanaEnum().equals(StatusLoteKobanaEnum.CONFIRMED)) {
            return true;
        }
        return false;
    }

    private void reabrirMovContasDoLote(LoteKobanaVO loteKobanaZW) throws Exception {
        List<LoteKobanaItemVO> lista = obterTodasMovContasDoLoteByCodigo(loteKobanaZW.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        MovConta movContaDAO;
        try {
            for (LoteKobanaItemVO item : lista) {
                movContaDAO = new MovConta(getCon());
                movContaDAO.prenderOuLiberarMovContaEmLoteDePagamento(false, null, item.getMovcontaVO().getCodigo());
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            movContaDAO = null;
        }
    }

    public KobanaWebhookVO salvarKobanaWebhookDeItem(KobanaWebhookDTO webhookKobana, String dados, LoteKobanaItemVO loteKobanaItemVO) throws Exception {
        KobanaWebhook kobanaWebhookDAO;
        try {
            KobanaWebhookVO kobanaWebhookVO = new KobanaWebhookVO();
            kobanaWebhookVO.setDados(dados);
            kobanaWebhookVO.setEvent_code(webhookKobana.getEvent_code());
            kobanaWebhookVO.setProcessado(false);
            kobanaWebhookVO.setDataProcessamento(null);
            kobanaWebhookVO.setUidLoteKobanaItem(loteKobanaItemVO.getUid());
            kobanaWebhookVO.setCodLoteKobanaItem(loteKobanaItemVO.getCodigo());
            kobanaWebhookVO.setUidLoteKobana(null);
            kobanaWebhookVO.setCodLoteKobana(null);
            kobanaWebhookVO.setMovContaVO(loteKobanaItemVO.getMovcontaVO());
            kobanaWebhookDAO = new KobanaWebhook(getCon());
            return kobanaWebhookDAO.incluir(kobanaWebhookVO);
        } catch (Exception ex) {
            throw new Exception("Não foi possível obter a configuração da integração Kobana: " + ex.getMessage());
        } finally {
            kobanaWebhookDAO = null;
        }
    }

    public KobanaWebhookVO salvarKobanaWebhookDeLote(KobanaWebhookDTO webhookKobana, String dados, LoteKobanaVO loteKobanaVO) throws Exception {
        KobanaWebhook kobanaWebhookDAO;
        try {
            KobanaWebhookVO kobanaWebhookVO = new KobanaWebhookVO();
            kobanaWebhookVO.setDados(dados);
            kobanaWebhookVO.setEvent_code(webhookKobana.getEvent_code());
            kobanaWebhookVO.setProcessado(false);
            kobanaWebhookVO.setDataProcessamento(null);
            kobanaWebhookVO.setUidLoteKobanaItem(null);
            kobanaWebhookVO.setCodLoteKobanaItem(null);
            kobanaWebhookVO.setUidLoteKobana(loteKobanaVO.getUid());
            kobanaWebhookVO.setCodLoteKobana(loteKobanaVO.getCodigo());
            kobanaWebhookVO.setMovContaVO(null);
            kobanaWebhookDAO = new KobanaWebhook(getCon());
            return kobanaWebhookDAO.incluir(kobanaWebhookVO);
        } catch (Exception ex) {
            throw new Exception("Não foi possível obter a configuração da integração Kobana: " + ex.getMessage());
        } finally {
            kobanaWebhookDAO = null;
        }
    }

    public KobanaWebhookVO atualizarKobanaWebhook(KobanaWebhookVO kobanaWebhookVO) throws Exception {
        KobanaWebhook kobanaWebhookDAO;
        try {
            kobanaWebhookVO.setProcessado(true);
            kobanaWebhookVO.setDataProcessamento(Calendario.hoje());
            kobanaWebhookDAO = new KobanaWebhook(getCon());
            kobanaWebhookDAO.alterar(kobanaWebhookVO);
            return kobanaWebhookVO;
        } catch (Exception ex) {
            throw new Exception("Não foi possível obter a configuração da integração Kobana: " + ex.getMessage());
        } finally {
            kobanaWebhookDAO = null;
        }
    }

    public IntegracaoKobanaVO obterIntegracaoKobana(int codEmpresa) throws Exception {
        IntegracaoKobana integracaoKobanaDAO;
        try {
            integracaoKobanaDAO = new IntegracaoKobana(getCon());
            return integracaoKobanaDAO.consultar(codEmpresa, Uteis.NIVELMONTARDADOS_TODOS);
        } catch (Exception ex) {
            throw new Exception("Não foi possível obter a configuração da integração Kobana: " + ex.getMessage());
        } finally {
            integracaoKobanaDAO = null;
        }
    }

    public void quitarMovContaLoteKobana(MovContaVO movContaVO, KobanaWebhookDTO kobanaWebhookDTO) throws Exception {
        MovConta movContaDAO;
        CaixaVO caixaAbertoVO = null;
        try {
            movContaVO.setValorPago(kobanaWebhookDTO.getAmount());
            movContaVO.setDataQuitacao(kobanaWebhookDTO.getConfirmed_at());
            movContaVO.setDataUltimaAlteracao(Calendario.hoje());
            movContaDAO = new MovConta(getCon());
            caixaAbertoVO = abrirCaixa(UsuarioVO.getUsuarioAdmin(), movContaVO.getEmpresaVO(), movContaVO.getDataQuitacao());
            if (caixaAbertoVO != null && !UteisValidacao.emptyNumber(caixaAbertoVO.getCodigo())) {
                //Quitar a conta
                movContaDAO.gravarQuitacao(movContaVO, caixaAbertoVO.getCodigo(), true, false);
            }
        } catch (Exception ex) {
            throw new Exception("Não foi possível quitar a movconta: " + movContaVO.getCodigo() + " | UID: " + kobanaWebhookDTO.getUid() +
                    " | método quitarMovContaLoteKobana no kobanaWebhookService | " + ex.getMessage());
        } finally {
            movContaDAO = null;
            fecharCaixa(caixaAbertoVO);
        }
    }

    private LoteKobanaItemVO obterLoteKobanaItemPorUID(String uid, int nivelMontarDados) throws Exception {
        LoteKobanaItem loteKobanaItemDAO;
        try {
            loteKobanaItemDAO = new LoteKobanaItem(getCon());
            LoteKobanaItemVO loteKobanaItemVO = loteKobanaItemDAO.consultarByUID(uid, nivelMontarDados);
            return loteKobanaItemVO;
        } catch (Exception ex) {
            throw new Exception("Erro em obterLoteKobanaItemPorUID: " + uid + " | " + ex.getMessage());
        } finally {
            loteKobanaItemDAO = null;
        }
    }

    private LoteKobanaVO obterLoteKobanaPorUID(String uid, int nivelMontarDados) throws Exception {
        LoteKobana loteKobanaDAO;
        try {
            loteKobanaDAO = new LoteKobana(getCon());
            LoteKobanaVO loteKobanaVO = loteKobanaDAO.consultarPorUId(uid, nivelMontarDados);
            return loteKobanaVO;
        } catch (Exception ex) {
            throw new Exception("Erro em obterLoteKobanaItemPorUID: " + uid + " | " + ex.getMessage());
        } finally {
            loteKobanaDAO = null;
        }
    }

    private List<LoteKobanaItemVO> obterTodasMovContasDoLoteByCodigo(int codLote, int nivelMontarDados) throws Exception {
        LoteKobanaItem loteKobanaItemDAO;
        try {
            loteKobanaItemDAO = new LoteKobanaItem(getCon());
            List<LoteKobanaItemVO> list = loteKobanaItemDAO.consultarByCodLote(codLote, nivelMontarDados);
            return list;
        } catch (Exception ex) {
            throw new Exception("Erro em obterTodasMovContasDoLoteByCodigo: " + codLote + " | " + ex.getMessage());
        } finally {
            loteKobanaItemDAO = null;
        }
    }

    private void atualizarLoteKobana(LoteKobanaVO loteKobanaVO) throws Exception {
        LoteKobana loteKobanaDAO;
        try {
            loteKobanaDAO = new LoteKobana(getCon());
            loteKobanaDAO.alterar(loteKobanaVO);
        } catch (Exception ex) {
            throw new Exception("Erro em atualizarLoteKobana: " + loteKobanaVO.getUid() + " | " + ex.getMessage());
        } finally {
            loteKobanaDAO = null;
        }
    }

    private void atualizarStatusLoteKobana(StatusLoteKobanaEnum statusLoteKobanaEnum, int codLote) throws Exception {
        LoteKobana loteKobanaDAO;
        try {
            loteKobanaDAO = new LoteKobana(getCon());
            loteKobanaDAO.alterarStatus(statusLoteKobanaEnum, codLote);
        } catch (Exception ex) {
            throw new Exception("Erro em atualizarStatusLoteKobana: " + codLote + " | " + ex.getMessage());
        } finally {
            loteKobanaDAO = null;
        }
    }

    private KobanaWebhookVO obterUltimoWebhookProcessadoByMovConta(Integer movConta) throws Exception {
        KobanaWebhook kobanaWebhookDAO;
        try {
            kobanaWebhookDAO = new KobanaWebhook(getCon());
            KobanaWebhookVO kobanaWebhookVO = kobanaWebhookDAO.consultarPorMovConta(movConta);
            return kobanaWebhookVO;
        } catch (Exception ex) {
            return null;
        } finally {
            kobanaWebhookDAO = null;
        }
    }

    private KobanaWebhookVO gravarKobanaWebhook(String dados, String event_code) throws Exception {
        KobanaWebhook kobanaWebhookDAO;
        KobanaWebhookVO kobanaWebhookVO = new KobanaWebhookVO();
        try {
            kobanaWebhookDAO = new KobanaWebhook(getCon());
            kobanaWebhookVO.setDados(dados);
            kobanaWebhookVO.setEvent_code(event_code);
            kobanaWebhookVO.setProcessado(false);
            kobanaWebhookDAO.incluir(kobanaWebhookVO);
            return kobanaWebhookVO;
        } catch (Exception ex) {
            throw new Exception("Erro ao em gravarKobanaWebhook: " + ex.getMessage());
        } finally {
            kobanaWebhookDAO = null;
        }
    }

    private CaixaVO abrirCaixa(UsuarioVO usuarioVO, EmpresaVO empresaVo, Date diaProcessar) throws Exception {
        Caixa caixaDAO;
        CaixaVO caixaVo = new CaixaVO();
        try {
            caixaDAO = new Caixa(getCon());
            caixaVo.setUsuarioVo(usuarioVO);
            caixaVo.setEmpresaVo(empresaVo);
            caixaVo.setDataAbertura(diaProcessar);
            caixaVo.setDataTrabalho(diaProcessar);
            CaixaVO caixaAberto = caixaDAO.abrirCaixa(caixaVo);
            return caixaAberto;
        } catch (Exception ex) {
            throw new Exception("Erro ao abrir o caixa: " + ex.getMessage());
        } finally {
            caixaDAO = null;
        }
    }

    private void fecharCaixa(CaixaVO caixaVoEmAberto) throws Exception {
        Caixa caixaDAO;
        try {
            caixaDAO = new Caixa(getCon());
            caixaVoEmAberto.setDataFechamento(Calendario.hoje());
            caixaVoEmAberto.setResponsavelFechamento(caixaVoEmAberto.getUsuarioVo());
            caixaDAO.alterar(caixaVoEmAberto);
        } catch (Exception ex) {
            throw new Exception("Erro ao fechar o caixa: " + ex.getMessage());
        } finally {
            caixaDAO = null;
        }
    }
}
