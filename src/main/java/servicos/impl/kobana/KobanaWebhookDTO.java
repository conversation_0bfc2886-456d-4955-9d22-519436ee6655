package servicos.impl.kobana;

import negocio.comuns.financeiro.LoteKobanaItemVO;
import negocio.comuns.financeiro.enumerador.RegistrationStatusKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusLoteKobanaEnum;
import negocio.comuns.financeiro.enumerador.StatusPagamentoKobanaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 11/11/2024.
 */

public class KobanaWebhookDTO {

    private String uid;
    private String status;
    private String registration_status;
    private String financial_account_uid;
    private double amount;
    private String rejected_error;
    private Date rejected_at;
    private String transaction_code;
    private Date scheduled_to;
    private Date confirmed_at;
    private Date transaction_date;
    private Date created_at;
    private Date updated_at;
    private String event_code;
    private StatusLoteKobanaEnum statusLoteKobanaEnum;
    private StatusPagamentoKobanaEnum statusPagamentoKobanaEnum;
    private RegistrationStatusKobanaEnum registrationStatusKobanaEnum;
    private List<LoteKobanaItemVO> listaLoteKobanaItemVO = new ArrayList<>();

    public KobanaWebhookDTO() {
    }

    public KobanaWebhookDTO(JSONObject json, boolean webhookDeLote) throws Exception {
        JSONObject obj = json.getJSONObject("object");
        this.uid = obj.optString("uid");
        this.status = obj.optString("status");
        if (webhookDeLote) {
            this.statusLoteKobanaEnum = StatusLoteKobanaEnum.obterPorValue(obj.optString("status").toUpperCase());
        } else {
            this.statusPagamentoKobanaEnum = StatusPagamentoKobanaEnum.obterPorValue(obj.optString("status").toUpperCase());
        }
        this.registration_status = obj.optString("registration_status");
        this.registrationStatusKobanaEnum = registrationStatusKobanaEnum.obterPorValue(obj.optString("registration_status").toUpperCase());
        this.financial_account_uid = obj.optString("financial_account_uid");
        this.amount = obj.optDouble("amount");
        this.rejected_error = obj.optString("rejected_error");
        this.transaction_code = obj.optString("transaction_code");
        this.event_code = json.optString("event_code");
        if (!UteisValidacao.emptyString(obj.optString("confirmed_at"))) {
            setConfirmed_at(Uteis.getDate(obj.optString("confirmed_at"), "yyyy-MM-dd' 'HH:mm:ss"));
        }
        if (!UteisValidacao.emptyString(obj.optString("scheduled_to"))) {
            setScheduled_to(Uteis.getDate(obj.optString("scheduled_to"), "yyyy-MM-dd'T'HH:mm:ss"));
        }
        if (!UteisValidacao.emptyString(obj.optString("rejected_at"))) {
            setRejected_at(Uteis.getDate(obj.optString("rejected_at"), "yyyy-MM-dd' 'HH:mm:ss"));
        }
        if (!UteisValidacao.emptyString(obj.optString("created_at"))) {
            setCreated_at(Uteis.getDate(obj.optString("created_at"), "yyyy-MM-dd'T'HH:mm:ss"));
        }
        if (!UteisValidacao.emptyString(obj.optString("updated_at"))) {
            setUpdated_at(Uteis.getDate(obj.optString("updated_at"), "yyyy-MM-dd'T'HH:mm:ss"));
        }
        if (!UteisValidacao.emptyString(obj.optString("transaction_date"))) {
            setTransaction_date(Uteis.getDate(obj.optString("transaction_date"), "yyyy-MM-dd' 'HH:mm:ss"));
        }

        try {
            if (webhookDeLote && obj.has("payments")) {
                if (obj.get("payments") instanceof JSONArray) {
                    JSONArray payments = obj.getJSONArray("payments");
                    for (int i = 0; i < payments.length(); i++) {
                        JSONObject payment = payments.getJSONObject(i);
                        LoteKobanaItemVO loteKobanaItemVO = new LoteKobanaItemVO(payment);
                        this.listaLoteKobanaItemVO.add(loteKobanaItemVO);
                    }
                }
            }
        } catch (Exception ex) {
        }
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getScheduled_to() {
        return scheduled_to;
    }

    public void setScheduled_to(Date scheduled_to) {
        this.scheduled_to = scheduled_to;
    }

    public String getRegistration_status() {
        return registration_status;
    }

    public void setRegistration_status(String registration_status) {
        this.registration_status = registration_status;
    }

    public String getFinancial_account_uid() {
        return financial_account_uid;
    }

    public void setFinancial_account_uid(String financial_account_uid) {
        this.financial_account_uid = financial_account_uid;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public Date getConfirmed_at() {
        return confirmed_at;
    }

    public void setConfirmed_at(Date confirmed_at) {
        this.confirmed_at = confirmed_at;
    }

    public String getRejected_error() {
        return rejected_error;
    }

    public void setRejected_error(String rejected_error) {
        this.rejected_error = rejected_error;
    }

    public Date getRejected_at() {
        return rejected_at;
    }

    public void setRejected_at(Date rejected_at) {
        this.rejected_at = rejected_at;
    }

    public String getTransaction_code() {
        return transaction_code;
    }

    public void setTransaction_code(String transaction_code) {
        this.transaction_code = transaction_code;
    }

    public Date getTransaction_date() {
        return transaction_date;
    }

    public void setTransaction_date(Date transaction_date) {
        this.transaction_date = transaction_date;
    }

    public Date getCreated_at() {
        return created_at;
    }

    public void setCreated_at(Date created_at) {
        this.created_at = created_at;
    }

    public Date getUpdated_at() {
        return updated_at;
    }

    public void setUpdated_at(Date updated_at) {
        this.updated_at = updated_at;
    }

    public String getEvent_code() {
        return event_code;
    }

    public void setEvent_code(String event_code) {
        this.event_code = event_code;
    }

    public StatusPagamentoKobanaEnum getStatusPagamentoKobanaEnum() {
        return statusPagamentoKobanaEnum;
    }

    public void setStatusPagamentoKobanaEnum(StatusPagamentoKobanaEnum statusPagamentoKobanaEnum) {
        this.statusPagamentoKobanaEnum = statusPagamentoKobanaEnum;
    }

    public RegistrationStatusKobanaEnum getRegistrationStatusKobanaEnum() {
        return registrationStatusKobanaEnum;
    }

    public void setRegistrationStatusKobanaEnum(RegistrationStatusKobanaEnum registrationStatusKobanaEnum) {
        this.registrationStatusKobanaEnum = registrationStatusKobanaEnum;
    }

    public StatusLoteKobanaEnum getStatusLoteKobanaEnum() {
        return statusLoteKobanaEnum;
    }

    public void setStatusLoteKobanaEnum(StatusLoteKobanaEnum statusLoteKobanaEnum) {
        this.statusLoteKobanaEnum = statusLoteKobanaEnum;
    }

    public List<LoteKobanaItemVO> getListaLoteKobanaItemVO() {
        if (UteisValidacao.emptyList(listaLoteKobanaItemVO)) {
            return new ArrayList<>();
        }
        return listaLoteKobanaItemVO;
    }

    public void setListaLoteKobanaItemVO(List<LoteKobanaItemVO> listaLoteKobanaItemVO) {
        this.listaLoteKobanaItemVO = listaLoteKobanaItemVO;
    }
}
