package servicos.impl;

public class VendaVitioDTO {
    private String name;
    private String email;
    private String telefone;
    private String chavePacto;
    private String cdEmpresaPacto;
    private String planCost;
    private Integer planMonths;
    private String nomeConsultorQueFezAVenda;
    private String telefoneConsultorQueFezAVenda;
    private Boolean aderiuVitio;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getChavePacto() {
        return chavePacto;
    }

    public void setChavePacto(String chavePacto) {
        this.chavePacto = chavePacto;
    }

    public String getCdEmpresaPacto() {
        return cdEmpresaPacto;
    }

    public void setCdEmpresaPacto(String cdEmpresaPacto) {
        this.cdEmpresaPacto = cdEmpresaPacto;
    }

    public String getPlanCost() {
        return planCost;
    }

    public void setPlanCost(String planCost) {
        this.planCost = planCost;
    }

    public Integer getPlanMonths() {
        return planMonths;
    }

    public void setPlanMonths(Integer planMonths) {
        this.planMonths = planMonths;
    }

    public String getNomeConsultorQueFezAVenda() {
        return nomeConsultorQueFezAVenda;
    }

    public void setNomeConsultorQueFezAVenda(String nomeConsultorQueFezAVenda) {
        this.nomeConsultorQueFezAVenda = nomeConsultorQueFezAVenda;
    }

    public String getTelefoneConsultorQueFezAVenda() {
        return telefoneConsultorQueFezAVenda;
    }

    public void setTelefoneConsultorQueFezAVenda(String telefoneConsultorQueFezAVenda) {
        this.telefoneConsultorQueFezAVenda = telefoneConsultorQueFezAVenda;
    }

    public Boolean getAderiuVitio() {
        return aderiuVitio;
    }

    public void setAderiuVitio(Boolean aderiuVitio) {
        this.aderiuVitio = aderiuVitio;
    }
}
