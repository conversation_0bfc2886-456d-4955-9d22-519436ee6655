package servicos.impl.dcc.getnet;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import servicos.impl.dcc.base.TipoRegistroEnum;

/**
 * Created by <PERSON><PERSON> on 11/10/2018.
 */
public enum TipoRegistroExtratoGetNetEnum {

    TipoRegistro0("0", "HEADER DE ARQUIVO", TipoRegistroEnum.HEADER, TipoConciliacaoEnum.VENDAS),
    TipoRegistro1("1", "DETALHE DO RESUMO DE VENDAS", TipoRegistroEnum.DETALHE, TipoConciliacaoEnum.VENDAS),
    TipoRegistro2("2", "DETALHE DO COMPROVANTE DE VENDA", TipoRegistroEnum.DETALHE, TipoConciliacaoEnum.PAGAMENTOS),
    TipoRegistro3("3", "AJUSTES", TipoRegistroEnum.DETALHE, TipoConciliacaoEnum.VENDAS),
    TipoRegistro4("4", "DETALHE DA OPERAÇÃO DE ANTECIPAÇÃO", TipoRegistroEnum.DETALHE, TipoConciliacaoEnum.VENDAS),
    TipoRegistro9("9", "TRAILER DE ARQUIVO", TipoRegistroEnum.TRAILER, TipoConciliacaoEnum.VENDAS),
    ;

    private String id;
    private String descricao;
    private TipoRegistroEnum tipoRegistroEnum;
    private TipoConciliacaoEnum tipoConciliacaoEnum;

    private TipoRegistroExtratoGetNetEnum(String id, String descricao, TipoRegistroEnum tipoRegistroEnum, TipoConciliacaoEnum tipoConciliacaoEnum) {
        this.id = id;
        this.descricao = descricao;
        this.tipoRegistroEnum = tipoRegistroEnum;
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static TipoRegistroExtratoGetNetEnum valueOff(String id) {
        TipoRegistroExtratoGetNetEnum[] values = TipoRegistroExtratoGetNetEnum.values();
        for (TipoRegistroExtratoGetNetEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return null;
    }

    public static boolean extratoGetNet(String id) {
        TipoRegistroExtratoGetNetEnum[] values = TipoRegistroExtratoGetNetEnum.values();
        for (TipoRegistroExtratoGetNetEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }


    public static String retornarIDs(){
        StringBuilder codigos = new StringBuilder();
        for (TipoRegistroExtratoGetNetEnum eDIStatusEnum : values()) {
            if (codigos.toString().equals("")){
                codigos.append("'").append(eDIStatusEnum.getId()).append("'");
            }else{
                codigos.append(",").append("'").append(eDIStatusEnum.getId()).append("'");
            }
        }
        return codigos.toString();
    }

    public TipoRegistroEnum getTipoRegistroEnum() {
        return tipoRegistroEnum;
    }

    public void setTipoRegistroEnum(TipoRegistroEnum tipoRegistroEnum) {
        this.tipoRegistroEnum = tipoRegistroEnum;
    }

    public TipoConciliacaoEnum getTipoConciliacaoEnum() {
        return tipoConciliacaoEnum;
    }

    public void setTipoConciliacaoEnum(TipoConciliacaoEnum tipoConciliacaoEnum) {
        this.tipoConciliacaoEnum = tipoConciliacaoEnum;
    }
}
