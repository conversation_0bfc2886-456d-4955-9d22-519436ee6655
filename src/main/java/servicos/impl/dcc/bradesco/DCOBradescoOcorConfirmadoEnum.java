/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.bradesco;

/**
 *
 * <AUTHOR>
 */
public enum DCOBradescoOcorConfirmadoEnum {

    MotivoNENHUM("NENHUM", "Nenhum Motivo"),
    Motivo00("00", "Ocorrência aceita"),
    Motivo01("01", "Código do Banco inválido"),
    Motivo04("04", "Código do movimento não permitido para a carteira (Novo)"),
    Motivo15("15", "Características da cobrança incompatíveis (Novo)"),
    Motivo17("17", "Data de vencimento anterior a data de emissão"),
    Moti<PERSON>21("21", "Espécie do Título inválido"),
    Motivo24("24", "Data da emissão inválida"),
    Motivo27("27", "Valor/taxa de juros mora inválido (Novo)"),
    <PERSON><PERSON><PERSON><PERSON>("38", "Prazo para protesto inválido"),
    <PERSON><PERSON><PERSON><PERSON>("39", "Pedido para protesto não permitido para título"),
    <PERSON><PERSON><PERSON>43("43", "Prazo para baixa e devolução inválido"),
    Motivo45("45", "Nome do Pagador inválido"),
    Motivo46("46", "Tipo/num. de inscrição do Pagador inválidos"),
    Motivo47("47", "Endereço do Pagador não informado"),
    Motivo48("48", "CEP Inválido"),
    Motivo50("50", "CEP referente a Banco correspondente"),
    Motivo53("53", "Nº de inscrição do Pagadorr/avalista inválidos (CPF/CNPJ)"),
    Motivo54("54", "Pagadorr/avalista não informado"),
    Motivo67("67", "Débito automático agendado"),
    Motivo68("68", "Débito não agendado - erro nos dados de remessa"),
    Motivo69("69", "Débito não agendado - Pagador não consta no cadastro de autorizante"),
    Motivo70("70", "Débito não agendado - Beneficiario não autorizado pelo Pagador"),
    Motivo71("71", "Débito não agendado - Beneficiario não participa da modalidade de déb.automático"),
    Motivo72("72", "Débito não agendado - Código de moeda diferente de R$"),
    Motivo73("73", "Débito não agendado - Data de vencimento inválida/vencida"),
    Motivo75("75", "Débito não agendado - Tipo do número de inscrição do pagador debitado inválido"),
    Motivo76("76", "Pagador Eletrônico DDA (NOVO)- Esse motivo somente será disponibilizado no arquivo retorno para as empresas cadastradas nessa condição."),
    Motivo86("86", "Seu número do documento inválido"),
    Motivo89("89", "Email Pagador não enviado ? título com débito automático (Novo)"),
    Motivo90("90", "Email pagador não enviado ? título de cobrança sem registro (Novo)");
    //
    private String id;
    private String descricao;

    private DCOBradescoOcorConfirmadoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCOBradescoOcorConfirmadoEnum valueOff(String id) {
        DCOBradescoOcorConfirmadoEnum[] values = DCOBradescoOcorConfirmadoEnum.values();
        for (DCOBradescoOcorConfirmadoEnum eDIMotivoEnum : values) {
            if (eDIMotivoEnum.getId().equals(id)) {
                return eDIMotivoEnum;
            }
        }
        return DCOBradescoOcorConfirmadoEnum.MotivoNENHUM;
    }
}
