package servicos.impl.dcc.bradesco;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * alcides
 */
public class LayoutRemessaBradescoCNAB240 extends LayoutRemessaBase {

    private static Pattern patternHEAD  = Pattern.compile("^[0-9]{8}T");
    private static Pattern patternT  = Pattern.compile("^[0-9]{13}T");
    private static Pattern patternU  = Pattern.compile("^[0-9]{13}U");
    private static Pattern patterTRAILER = Pattern.compile("^[0-9]{7}5");


    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa headerRemessa = new RegistroRemessa(TipoRegistroEnum.HEADER);
        Date dataDeposito = remessa.getDataRegistro();
        //1 a 3
        headerRemessa.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        //4 a 7
        headerRemessa.put(DCCAttEnum.LoteServico, "0000");
        //8
        headerRemessa.put(DCCAttEnum.TipoRegistro, "0");
        //9 a 17
        headerRemessa.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        //18
        headerRemessa.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        //19 a 32
        headerRemessa.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
        //33 a 52
        headerRemessa.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Long.valueOf(remessa.getConvenioCobranca().getNumeroContrato().replaceAll(" ", "")), 20));
        //53 a 57
        headerRemessa.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        //58
        headerRemessa.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV(),1));
        //59 a 70
        headerRemessa.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
        //71
        headerRemessa.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
        //72
        headerRemessa.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        //73 a 102
        headerRemessa.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        //103 a 132
        headerRemessa.put(DCCAttEnum.Banco, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getBanco().getNome(), 30));
        //133 a 142
        headerRemessa.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
        //143
        headerRemessa.put(DCCAttEnum.CodigoRemessa, "1");//1
        //144 a 151
        headerRemessa.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
        //152 a 157
        headerRemessa.put(DCCAttEnum.HoraGeracao, StringUtilities.formatarCampoData(dataDeposito, "HHmmss"));
        //158 a 163

        String numeroResumoOperacoes = headerRemessa.getValue(DCCAttEnum.NumeroResumoOperacoes.name());
        if (UteisValidacao.emptyString(numeroResumoOperacoes) || numeroResumoOperacoes.trim().isEmpty()) {
            numeroResumoOperacoes = remessa.getProps().get(DCCAttEnum.NumeroResumoOperacoes.name());
        }
        numeroResumoOperacoes = UteisValidacao.emptyString(numeroResumoOperacoes) ?
                StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getSequencialDoArquivo()), 6)
                : numeroResumoOperacoes;
        numeroResumoOperacoes = StringUtilities.formatarCampo(new BigDecimal(numeroResumoOperacoes), 6);

        String sequencialArquivo;
        if (remessa.getConvenioCobranca().isOmitirSequencialArquivo()) {
            sequencialArquivo = StringUtilities.formatarCampoZerado(6);
        } else {
            sequencialArquivo = StringUtilities.formatarCampo(new BigDecimal(numeroResumoOperacoes), 6);
        }

        remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), numeroResumoOperacoes);
        headerRemessa.put(DCCAttEnum.NumeroResumoOperacoes, sequencialArquivo);
        //164 a 166
        headerRemessa.put(DCCAttEnum.VersaoLayout, "084");
        //167 a 171
        headerRemessa.put(DCCAttEnum.DensidadeGravacao, "06250");
        //172 a 240
        headerRemessa.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(69));

        remessa.setHeaderRemessa(headerRemessa);
        remessa.setHeaderRemessaArquivo(getHeaderLote(remessa, dataDeposito));
        Double juros = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() ? remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica() : 0;

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int seq = 1;
        for (RemessaItemVO item : lista) {
            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    enderecoVO = endereco;
                }
            }
            if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                    enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
                }
            }

            RegistroRemessa detailP = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_P);
            //01.3P Banco Código do Banco na Compensação 1 1 3 3 - Num G001
            detailP.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            //02.3P Lote Lote de Serviço 4 7 4 - Num *G002
            detailP.put(DCCAttEnum.LoteServico, "0001");
            //03.3P Registro Tipo de Registro 8 8 1 - Num '3' *G003
            detailP.put(DCCAttEnum.TipoRegistro, "3");
            //04.3P Nº do Registro Nº Sequencial do Registro no Lote 9 13 5 - Num *G038
            detailP.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            //05.3P Segmento Cód. Segmento do Registro Detalhe 14 14 1 - Alfa 'P' *G039
            detailP.put(DCCAttEnum.Segmento, "P");
            //06.3P CNAB Uso Exclusivo FEBRABAN/CNAB 15 15 1 - Alfa Brancos G004
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            //07.3P Cód. Mov. Código de Movimento Remessa 16 17 2 - Num *C004
            detailP.put(DCCAttEnum.CodigoMovimento, "01");
            //08.3P Agência Mantenedora da Conta 18 22 5 - Num *G008
            detailP.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
            //09.3P DV Dígito Verificador da Agência 23 23 1 - Alfa *G009
            detailP.put(DCCAttEnum.AgenciaDebitoDigito, UteisValidacao.emptyString(remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV()) ? 0 : remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV());
            //10.3P Conta Número Número da Conta Corrente 24 35 12 - Num *G010
            detailP.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
            //11.3P DV Dígito Verificador da Conta 36 36 1 - Alfa *G011
            detailP.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
            //12.3P DV Dígito Verificador da Ag/Conta 37 37 1 - Alfa *G012
            detailP.put(DCCAttEnum.DigitoVerificadorAgencia, StringUtilities.formatarCampoEmBranco(1));
            //13.3P Identificação do Título Identificação do Produto 38 40 3 Num *G069
            //GTC: ESTÁ ERRADO NO MANUAL
            detailP.put(DCCAttEnum.Carteira, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 3));
            //13.3P Identificação do Título Zeros 41 45 5 Num *G069
            detailP.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(5));
            //13.3P Identificação do Título Nosso Número 46 56 11 Num *G069
            String nossoNumero = StringUtilities.formatarCampoForcandoZerosAEsquerda(new BigDecimal(item.getCodigo()), 11);
            detailP.put(DCCAttEnum.NossoNumero, nossoNumero);
            //13.3P Identificação do Título Digito do nosso Número 57 57 1 Num *G069
            String carteiraParaDV = StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 2);
            detailP.put(DCCAttEnum.NossoNumeroDV, StringUtilities.formatarCampoForcandoZerosAEsquerda(calcularDV(carteiraParaDV + nossoNumero), 1));
            //14.3P Característica Cobrança Carteira Código da Carteira 58 58 1 - Num *C006
            /*Domínio:
                '1'= Cobrança Simples
                '2'= Cobrança Vinculada
                '3'= Cobrança Caucionada
                '4'= Cobrança Descontada
                '5'= Cobrança Vendor*/
            //GTC: ESTÁ ERRADO NO MANUAL
            detailP.put(DCCAttEnum.IdentificadorProduto, "1");
            //14.3P Característica Cobrança Cadastramento Forma de Cadastr. do Título no Banco 59 59 1 - Num *C007
            /*Domínio:
                '1' = Com Cadastramento (Cobrança Registrada)
                '2' = Sem Cadastramento (Cobrança sem Registro)
                Obs.: Destina-se somente para emissão de bloqueto pelo banco
                '3' = Com Cadastramento / Recusa do Débito Automático (utilizado para ocorrências de erro no débito automático. O título não será registrado.)*/
            detailP.put(DCCAttEnum.FormaDeCadastramentoTituloBanco, "3");
            //14.3P Característica Cobrança Documento Tipo de Documento 60 60 1 - Alfa C008
            /*Domínio:
                '1' = Tradicional
                '2' = Escritural
                (NÃO TRATADO PELO BANCO. Obs: prrencher com uma das opções)*/
            detailP.put(DCCAttEnum.TipoDocumento, "1");
            //14.3P Característica Cobrança Emissão Bloqueto Identificação da Emissão do Bloqueto 61 61 1 - Num *C009
            detailP.put(DCCAttEnum.IdentificacaoEmissaoBoleto, "2");
            //14.3P Característica Cobrança Distrib. Bloqueto Identificação da Distribuição 62 62 1 - Alfa C010
             /*Domínio:
                '1' = Banco Distribui
                '2' = Cliente Distribui
                '3' = Banco envia e-mail (NÃO TRATADO PELO BANCO)
                '4' = Banco envia SMS (NÃO TRATADO PELO BANCO)*/
            detailP.put(DCCAttEnum.TipoDistribuicao, "2");
//            19.3P Nº do Documento Número do Documento de Cobrança 63 77
            detailP.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoEmBranco(item.getCodigo().toString(), 15));
            detailP.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(getDataVencimento(item.getMovParcela().getDataVencimento(), dataDeposito, remessa), "ddMMyyyy"));
            detailP.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 15));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(5));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailP.put(DCCAttEnum.EspecieTitulo, "02");
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, "N");
            detailP.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
            /*
            Código dos Juros de Mora
                Código adotado pela FEBRABAN para identificação do tipo de pagamento de juros de mora.
                Domínio:
                    '1' = Valor por Dia
                    '2' = Taxa Mensal
                    '3' = Isento
             */
            detailP.put(DCCAttEnum.CodigoJuros, "3");
            detailP.put(DCCAttEnum.DataJuros, StringUtilities.formatarCampoData(getDataVencimento(item.getMovParcela().getDataVencimento(), dataDeposito, remessa), "ddMMyyyy"));
            detailP.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(juros, 15));
            validarDesconto(item, detailP);
            detailP.put(DCCAttEnum.IOF, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(15));
            detailP.put(DCCAttEnum.IdentificacaoTituloBanco, StringUtilities.formatarCampoEmBranco(item.getCodigo().toString(), 25));
            detailP.put(DCCAttEnum.Protesto, "3");
            detailP.put(DCCAttEnum.DiasProtesto, StringUtilities.formatarCampoZerado(2));
            //38.3P Código p/ Baixa/Devolução Código para Baixa/Devolução 224 224 1 - Num C028
            /*Domínio:
                '1' = Baixar / Devolver
                '2' = Não Baixar / Não Devolver (NÃO TRATADO PELO BANCO)
                '3' = Cancelar Prazo para Baixa / Devolução
                (somente válido p/ CódigoMovimento Remessa = '31' - Descrição C004)*/
            detailP.put(DCCAttEnum.CodigoBaixa, "1");
            detailP.put(DCCAttEnum.NumeroDiasBaixa, StringUtilities.formatarCampoForcandoZerosAEsquerda(15, 3));
            detailP.put(DCCAttEnum.Moeda, "09");
            detailP.put(DCCAttEnum.NumeroContratoOperacaoCredito, StringUtilities.formatarCampoZerado(10));
            detailP.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));

            RegistroRemessa detailQ = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_Q);
            detailQ.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailQ.put(DCCAttEnum.LoteServico, "0001");
            detailQ.put(DCCAttEnum.TipoRegistro, "3");
            seq++;
            detailQ.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailQ.put(DCCAttEnum.Segmento, "Q");
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailQ.put(DCCAttEnum.CodigoMovimento, "01");
            detailQ.put(DCCAttEnum.TipoInscricao, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "1" : "2");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detailQ.put(DCCAttEnum.CpfOuCnpj, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 15));
            detailQ.put(DCCAttEnum.NomeCliente, StringUtilities.formatarCampoEmBranco(item.getPessoa().getNome(), 40));
            detailQ.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 40));
            detailQ.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 15));
            detailQ.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
            detailQ.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNomeSemAcento(), 15));
            detailQ.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(16));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(3));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(20));
            detailQ.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
            seq++;

            RegistroRemessa detailR = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_R);
            detailR.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
            detailR.put(DCCAttEnum.LoteServico, "0001");
            detailR.put(DCCAttEnum.TipoRegistro, "3");
            detailR.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampoForcandoZerosAEsquerda(seq, 5));
            detailR.put(DCCAttEnum.Segmento, "R");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.CodigoMovimento, "01");
            detailR.put(DCCAttEnum.CodigoDesconto, "0");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(13));
            detailR.put(DCCAttEnum.CodigoDesconto, "0");
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(17));

            //Multa
            if(remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros() != null && remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros()){
                detailR.put(DCCAttEnum.CobrarMulta, "2");
                detailR.put(DCCAttEnum.DataMulta, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));
                detailR.put(DCCAttEnum.PercentualMulta, StringUtilities.formatarCampoMonetarioVirgula(remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica(), 13, 2).replace(",",""));
            }else{
                detailR.put(DCCAttEnum.CobrarMulta, "0");
                detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
                detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(15));
            }

            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(10));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(20));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(8));
            //Se tiver removido a autorização de cobrança do aluno daria erro aqui. Tratado para gerar o arquivo normalmente com os dados da conta de débito zerados,
            // dessa forma o arquivo não recusa e o banco retorna o motivo da não aprovação
            detailR.put(DCCAttEnum.BancoCobranca, item.getAutorizacaoCobrancaVO() != null ? StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getAutorizacaoCobrancaVO().getBanco().getCodigoBanco(), 3) : StringUtilities.formatarCampoZerado(3));
            detailR.put(DCCAttEnum.AgenciaDebito, item.getAutorizacaoCobrancaVO() != null ? StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getAutorizacaoCobrancaVO().getAgencia(), 5) : StringUtilities.formatarCampoZerado(5));
            detailR.put(DCCAttEnum.AgenciaDebitoDigito, item.getAutorizacaoCobrancaVO() != null ? StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getAutorizacaoCobrancaVO().getAgenciaDV(), 1) : StringUtilities.formatarCampoZerado(1));
            detailR.put(DCCAttEnum.ContaCorrenteDebito, item.getAutorizacaoCobrancaVO() != null ? StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getAutorizacaoCobrancaVO().getContaCorrente(),12) : StringUtilities.formatarCampoZerado(12));
            detailR.put(DCCAttEnum.ContaCorrenteDebitoDigito, item.getAutorizacaoCobrancaVO() != null ? StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getAutorizacaoCobrancaVO().getContaCorrenteDV(), 1) : StringUtilities.formatarCampoZerado(1));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(1));
            detailR.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
            seq++;

            //
            listaDetalhe.add(detailP);
            listaDetalhe.add(detailQ);
            listaDetalhe.add(detailR);
        }

        remessa.setQtdAceito(lista.size());
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }

        remessa.setTrailerRemessa(getTrailerLote(remessa));

        RegistroRemessa trailerArquivo = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailerArquivo.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailerArquivo.put(DCCAttEnum.LoteServico, "9999");
        trailerArquivo.put(DCCAttEnum.TipoRegistro, "9");
        trailerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailerArquivo.put(DCCAttEnum.QtdLotes, StringUtilities.formatarCampoForcandoZerosAEsquerda(1, 6));
        trailerArquivo.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 3 + 4, 6));
        trailerArquivo.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(6));
        trailerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(205));
        remessa.setTrailerRemessaArquivo(trailerArquivo);

        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setHeaderRemessa(headerRemessa);

        remessa.setTrailer(new StringBuilder(remessa.getTrailerRemessa().toString()));
        remessa.setTrailerArquivo(new StringBuilder(remessa.getTrailerRemessaArquivo().toString()));
        remessa.setHead(new StringBuilder(headerRemessa.toString()));
        remessa.setHeaderArquivo(new StringBuilder(remessa.getHeaderRemessaArquivo().toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
    }

    public static void validarDesconto(RemessaItemVO item, RegistroRemessa detail) {
        if (item.possuiDesconto() && item.getDataPagamentoAntecipado() != null) {
            Double valor = item.getValorBoleto();
            valor = valor * item.getPorcentagemDescontoBoletoPagAntecipado() / 100;

            detail.put(DCCAttEnum.CodigoDesconto, "1");
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoData(item.getDataPagamentoAntecipado(), "ddMMyyyy"));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoMonetario(valor, 15));
        } else {
            detail.put(DCCAttEnum.CodigoDesconto, "0");
            detail.put(DCCAttEnum.DataDesconto, StringUtilities.formatarCampoZerado(8));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(15));
        }
    }

    private static RegistroRemessa getTrailerLote(RemessaVO remessa) {
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);
        trailer.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        trailer.put(DCCAttEnum.LoteServico, "0001");
        trailer.put(DCCAttEnum.TipoRegistro, "5");
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(9));
        trailer.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getListaItens().size() * 3 + 2, 6));
        trailer.put(DCCAttEnum.Zeros, StringUtilities.formatarCampoZerado(100));
        trailer.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(117));
        return trailer;
    }

    private static RegistroRemessa getHeaderLote(RemessaVO remessa, Date dataDeposito) {

        RegistroRemessa registro = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
        //1 a 3
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getBanco().getCodigoBanco(), 3));
        //4 a 7
        registro.put(DCCAttEnum.LoteServico, "0001");
        //8
        registro.put(DCCAttEnum.TipoRegistro, "1");
        //9
        registro.put(DCCAttEnum.TipoOperacao, "R");
        //10 a 11
        registro.put(DCCAttEnum.TipoServico, "01");
        //12 a 13
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(2));
        //14 a 16
        registro.put(DCCAttEnum.VersaoLayout, "042");
        //17
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        //18
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, "2");
        //19 a 33
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 15));
        //34 a 53
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.formatarCampoForcandoZerosAEsquerda(Long.valueOf(remessa.getConvenioCobranca().getNumeroContrato().replaceAll(" ", "")), 20));
        //54 a 58
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgencia(), 5));
        //59
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV(), 1));
        //60 a 71
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getContaCorrente(), 12));
        //72
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, remessa.getConvenioCobranca().getContaEmpresa().getContaCorrenteDV());
        //73
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(1));
        //74 a 103
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 30));
        //104 a 143
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
        //144 a 183
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));
        //184 a 191

        String numeroResumoOperacoes = remessa.getProps().get(DCCAttEnum.NumeroResumoOperacoes.name());
        numeroResumoOperacoes = UteisValidacao.emptyString(numeroResumoOperacoes) ?
                StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getSequencialDoArquivo()), 6)
                : numeroResumoOperacoes;
        numeroResumoOperacoes = StringUtilities.formatarCampo(new BigDecimal(numeroResumoOperacoes), 6);

        String sequencialArquivo;
        if (remessa.getConvenioCobranca().isOmitirSequencialArquivo()) {
            sequencialArquivo = StringUtilities.formatarCampoZerado(8);
        } else {
            sequencialArquivo = StringUtilities.formatarCampo(new BigDecimal(numeroResumoOperacoes), 8);
        }

        registro.put(DCCAttEnum.NumeroResumoOperacoes, sequencialArquivo);
        //192 a 199
        registro.put(DCCAttEnum.DataGravacao, StringUtilities.formatarCampoData(dataDeposito, "ddMMyyyy"));
        //200 a 207
        registro.put(DCCAttEnum.DataCredito, StringUtilities.formatarCampoZerado(8));
        //208 a 240
        registro.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(33));
        return registro;
    }

    public static String calcularNossoNumero(RemessaItemVO obj, String identificadorEmpreasFinanceiro) {
        String nossoNumero;
        if(identificadorEmpreasFinanceiro != null){
            StringBuilder sb = new StringBuilder();
            sb.append(identificadorEmpreasFinanceiro);
            sb.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getIdentificador().toString(), (10 - identificadorEmpreasFinanceiro.length())));
            nossoNumero = sb.toString();
        }else{
            nossoNumero = StringUtilities.formatarCampoForcandoZerosAEsquerda(obj.getIdentificador().toString(), 10);
        }
        return nossoNumero;
    }


    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            while ((linha = br.readLine()) != null) {
                if(patternHEAD.matcher(linha).find()){
                    lerHeader(linha, h);
                    break;
                }
            }
        }
        return h;
    }

    /**
     * Realiza a leitura do header do lote para a validação do arquivo.
     * @param linha
     * @param registro
     */
    private static void lerHeader(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.TipoOperacao, StringUtilities.readString(8,9,linha));
        registro.put(DCCAttEnum.TipoServico, StringUtilities.readString(9,11,linha));
        registro.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(13,16,linha));
        registro.put(DCCAttEnum.TipoInscricaoEmpresa, StringUtilities.readString(17,18,linha));
        registro.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(18,33,linha));
        registro.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(33,42,linha));
        registro.put(DCCAttEnum.IdentificacaoCobranca, StringUtilities.readString(42,46,linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(46,48,linha));
        registro.put(DCCAttEnum.VariacaoCarteira, StringUtilities.readString(48,51,linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(53,58,linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(58,59,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(59,71,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(71,72,linha));
        registro.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(73,103,linha));
        registro.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.readString(183,191,linha));
        registro.put(DCCAttEnum.DataGeracao, StringUtilities.readString(191,199,linha));
    }

    /**
     * Realiza a leitura do registro T do arquivo de retorno do banco.
     * @param linha
     * @param registro
     */
    private static void lerDetailT(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(8,13,linha));
        registro.put(DCCAttEnum.Segmento, StringUtilities.readString(13,14,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(14,15,linha));
        registro.put(DCCAttEnum.StatusVenda, StringUtilities.readString(15,17,linha));
        registro.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(17,22,linha));
        registro.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(22,23,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebito, StringUtilities.readString(23,35,linha));
        registro.put(DCCAttEnum.ContaCorrenteDebitoDigito, StringUtilities.readString(35,36,linha));
        registro.put(DCCAttEnum.EmBranco, StringUtilities.readString(36,37,linha));
        registro.put(DCCAttEnum.ConvenioCobranca, StringUtilities.readString(37,44,linha));
        registro.put(DCCAttEnum.NossoNumero, StringUtilities.readString(44,57,linha));
        registro.put(DCCAttEnum.Carteira, StringUtilities.readString(57,58,linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(58,73,linha));
        registro.put(DCCAttEnum.NumDocumento, StringUtilities.readString(58,73,linha));
        registro.put(DCCAttEnum.DataVencimento, StringUtilities.readString(73,81,linha));
        registro.put(DCCAttEnum.ValorVenda, StringUtilities.readString(81,96,linha));
        registro.put(DCCAttEnum.BancoCompensacao, StringUtilities.readString(96,99,linha));
        registro.put(DCCAttEnum.AgenciaCompensacao, StringUtilities.readString(99,104,linha));
        registro.put(DCCAttEnum.AgenciaDigitoCompensacao, StringUtilities.readString(104,105,linha));
        registro.put(DCCAttEnum.Moeda, StringUtilities.readString(130,132,linha));
        registro.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(198,213,linha));
        registro.put(DCCAttEnum.Motivo, StringUtilities.readString(213, 221, linha));//Motivo de Rejeição

        //Para realizar baixa
        registro.put(DCCAttEnum.NumeroComprovanteVenda, StringUtilities.readString(45, 56, linha));
    }

    /**
     * Realiza a leitura do registro U do arquivo de retorno do banco.
     * @param linha
     * @param registro
     */
    private static void lerDetailU(String linha, RegistroRemessa registro){
        registro.put(DCCAttEnum.ValorMora, StringUtilities.readString(17,32,linha));
        registro.put(DCCAttEnum.ValorPago, StringUtilities.readString(77,92,linha));
        registro.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(198,213,linha));
    }


    /**
     * Realiza a leitura do trailer
     * @param linha
     * @param registro
     */
    private static void lerTrailer(String linha, RegistroRemessa registro) {
        registro.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(0,3,linha));
        registro.put(DCCAttEnum.LoteServico, StringUtilities.readString(3,7,linha));
        registro.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(7,8,linha));
        registro.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(17,23,linha));
    }


    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();

        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            RegistroRemessa detail = null;
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (patternHEAD.matcher(linha).find()) {
                    lerHeader(linha, h);
                } else if (patternT.matcher(linha).find()) {
                    detail = new RegistroRemessa(TipoRegistroEnum.DETALHE_REGISTRO_T);
                    lerDetailT(linha, detail);
                } else if (patternU.matcher(linha).find()) {
                    lerDetailU(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (patterTRAILER.matcher(linha).find()) {
                    lerTrailer(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);
            validarArquivoRemessaRetorno(remessa);
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static String obterCodigosRemessaItem(StringBuilder retorno) throws IOException{
        StringBuilder codigos = new StringBuilder();
        BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
        String linha;
        while ((linha = br.readLine()) != null) {
            if(patternT.matcher(linha).find()){
                String nossoNumero = StringUtilities.readString(44, 57, linha);
                nossoNumero = nossoNumero.trim();
                codigos.append(nossoNumero);
                codigos.append(",");
            }
        }

        if (codigos.length() > 0) {
            codigos.deleteCharAt(codigos.length() - 1);
        }

        return codigos.toString();
    }

    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();
        //HEADER
        sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
        sb.append(remessa.getHeaderRemessaArquivo().toStringBuffer()).append("\r\n");
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuffer sbDetail = new StringBuffer();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer()).append("\r\n");
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer()).append("\r\n");
        sb.append(remessa.getTrailerRemessaArquivo().toStringBuffer());
        sb.append("\r\n");
        return sb;
    }

    public static String calcularDV(final String src) {
        int soma = 0;
        int[] arrBase7 = new int[]{2, 7, 6, 5, 4, 3, 2, 7, 6, 5, 4, 3, 2};
        int indexBase7 = 0;
        for (int i = 0; i < src.length(); i++) {
            if (indexBase7 + 1 > arrBase7.length) {
                indexBase7 = 0;
            }
            int fator = Integer.valueOf(src.substring(i, i + 1)) * arrBase7[indexBase7];
            soma += fator;
            indexBase7++;
        }
        int resto = (soma % 11);

        if (resto == 10) {
            return "P";
        } else if (resto == 0) {
            return "0";
        } else {
            return Integer.toString(11 - resto);
        }
    }

    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                if (patternT.matcher(linha).find()) {
                    codigos.add(Integer.valueOf(StringUtilities.readString(44, 56, linha).trim()));
                }
            }
        }
        Collections.sort(codigos);
        StringBuilder cods = new StringBuilder();
        for (Integer cod : codigos) {
            cods.append(",").append(cod);
        }
        return cods.toString().replaceFirst(",", "");
    }
}



