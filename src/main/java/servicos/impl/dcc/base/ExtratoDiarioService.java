package servicos.impl.dcc.base;


import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.ResultadoServicosVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.ServicoEnum;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCredencialStoneEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.impl.conciliacao.MovimentacaoAutomaticaRecebiveisConciliacaoServiceImpl;
import servicos.impl.dcc.bin.LayoutExtratoDiarioBin;
import servicos.impl.dcc.cielo.LayoutExtratoDiarioCielo;
import servicos.impl.dcc.getnet.LayoutExtratoDiarioGetNet;
import servicos.impl.dcc.getnet.TipoRegistroExtratoGetNetEnum;
import servicos.impl.dcc.rede.ExtratoRedeTipoRegistroEnum;
import servicos.impl.dcc.rede.LayoutExtratoDiarioRede;
import servicos.impl.dcc.rede.TipoArquivoRedeEnum;
import servicos.impl.pagolivre.PagoLivreConciliacaoService;
import servicos.impl.redepay.ERedeStatusConciliacaoEnum;
import servicos.impl.stone.StoneOnlineServiceConciliation;
import servicos.impl.stoneV5.StoneOnlineV5ServiceConciliation;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ExtratoDiarioService extends SuperEntidade {

    public ExtratoDiarioService() throws Exception {

    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            String chave = "banco";
            Date diaAprocessar = null; //Calendario.getDate("dd/MM/yyyy", "01/09/2020");; // Se quiser processar por aqui basta colocar a data inicial aqui. Lembrando ADM>Confg Empresa>Ajustes>Reprocessar Extratos
            if (args.length > 0) {
                chave = args[0];
                if (args.length > 1) {
                    diaAprocessar = Calendario.getDate("dd/MM/yyyy", args[1]);
                }
                Uteis.logarDebug("Obter conexão para chave: " + chave);
                Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(chave));
                ExtratoDiarioService extService = new ExtratoDiarioService();
                if (diaAprocessar != null) {
                    extService.processarExtratos(chave, diaAprocessar, null, false);
                } else {
                    extService.processarExtratos(chave, null, null, false);
                }
            } else {
                Connection conOAMD = DriverManager.getConnection("*************************************", "postgres", "pactodb");
                PreparedStatement st = conOAMD.prepareStatement("SELECT chave from empresa where chave = '" + chave + "'");
                ResultSet rs = st.executeQuery();
                while (rs.next()) {
                    chave = rs.getString("chave");
                    Conexao.guardarConexaoForJ2SE(new DAO().obterConexaoEspecifica(chave));
                    ExtratoDiarioService extService = new ExtratoDiarioService();
                    extService.processarExtratos(chave, diaAprocessar, null, false);
                }
            }

        } catch (Exception ex) {
            Logger.getLogger(ExtratoDiarioService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void processarExtratos(String chave, Date reprocessarAPartirDe, Date reprocessarAte, Boolean arquivosLocais) {
        processarExtratos(chave, reprocessarAPartirDe, reprocessarAte, arquivosLocais, true, "Automatico");
    }

    public void processarExtratos(String chave, Date reprocessarAPartirDe, Date reprocessarAte, Boolean arquivosLocais, Boolean processarMovimentacaoAutomatica,
                                  String origem) {
        Uteis.logar(null, "Iniciando Processamento de Extratos...");
        //Obs: Existe um erro em Produção, onde adicionar dias faz multiplicar o cadastro dos Cancelados, enquanto não descobrir o motivo é manter 0
        reprocessarAte = reprocessarAte == null ? Calendario.hoje() : Uteis.somarDias(reprocessarAte, 0); //Não aumentar esse valor para não deixar lento o Reprocessar Extrato manual
        Map<Integer, Integer> mapaConveniosJaProcessados = new HashMap<>();
        boolean convenioPossuiVariasEmpresasConfiguradas = false; //Atualmente implementado somente para Stone
        try {
            List<EmpresaVO> listaEmpresas = getFacade().getEmpresa().consultarTodas(true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            List<ConvenioCobrancaVO> todosConvenios = getFacade().getConvenioCobranca().
                    consultarTodosGeral(true, false, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            for (ConvenioCobrancaVO convenioCobrancaVO : todosConvenios) {
                if ((convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) || convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT) ||
                        convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5)) &&
                        convenioCobrancaVO.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                        !UteisValidacao.emptyList(convenioCobrancaVO.getConfiguracoesEmpresa()) &&
                        convenioCobrancaVO.getConfiguracoesEmpresa().size() > 1) {
                    convenioPossuiVariasEmpresasConfiguradas = true; //caso true, lá na frente irá usar a empresa do movpagamento para gravar o extratodiarioitem e não a empresa que está processando neste momento...
                    break;
                }
            }

            for (EmpresaVO empresa : listaEmpresas) {

                Uteis.logarDebug("INICIO | Extratos da empresa: " + empresa.getNome());

                ResultadoServicosVO resultadoExtrato = new ResultadoServicosVO(ServicoEnum.EXTRATO, empresa.getCodigo());
                //
                /*TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(), ainda não possui extrato*/
                List<ConvenioCobrancaVO> convenios = getFacade().
                        getConvenioCobranca().consultarPorEmpresaESituacao(
                                empresa.getCodigo(), false, Uteis.NIVELMONTARDADOS_TODOS,
                                new Integer[]{TipoConvenioCobrancaEnum.DCC.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_BIN.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_E_REDE.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_GETNET.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_STONE_ONLINE.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5.getCodigo(),
                                        TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_FACILITEPAY.getCodigo(),
                                        TipoConvenioCobrancaEnum.DCC_PAGOLIVRE.getCodigo()},
                                SituacaoConvenioCobranca.ATIVO);

                for (ConvenioCobrancaVO conv : convenios) {

                    //pode acontecer de passar o mesmo convenio varias vezes na lista caso o convênio tenha varias empresas configuradas dentro dele
                    boolean convenioJaFoiProcessado = !UteisValidacao.emptyNumber(mapaConveniosJaProcessados.getOrDefault(conv.getCodigo(), 0));

                    if (convenioJaFoiProcessado) {
                        Uteis.logarDebug("O Convênio " + conv.getDescricao() + " | cód. " + conv.getCodigo() + " já foi processado anteriormente;");
                        continue;
                    }

                    mapaConveniosJaProcessados.put(conv.getCodigo(), conv.getCodigo());
                    try {

                        Uteis.logarDebug("INICIO | Extratos da convênio: " + conv.getDescricao());

                        //conciliação padrão API Stone normal, usando sempre StoneCode
                        boolean conciliacaoViaAPIStoneXML = conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE) ||
                                conv.getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)
                                || (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) && conv.getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.GATEWAY));

                        //conciliação nova API Pagar.me (Stone v5), usando sempre a Chave da API (SK)
                        boolean conciliacaoViaAPIPagarmeV5 = conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE_V5) && conv.getTipoCredencialStoneEnum().equals(TipoCredencialStoneEnum.PSP);

                        if (conciliacaoViaAPIStoneXML) {
                            if (conv.getCodigoAutenticacao01() != null && conv.getCodigoAutenticacao01().trim() != "") {
                                try {
                                    StoneOnlineServiceConciliation.processarExtratosStone(empresa, conv, reprocessarAPartirDe, reprocessarAte, convenioPossuiVariasEmpresasConfiguradas, getCon(), origem);
                                } catch (Exception e) {
                                    resultadoExtrato.getResultado().put(e.getMessage());
                                    Uteis.logarDebug("# Stone Cobra Com problema : " + e.getMessage());
                                    Uteis.logarDebug("#### Stone Cobra ####");
                                } finally {
                                    processarItensSemPessoa(conv);
                                }
                            }
                            continue;
                        } else if (conciliacaoViaAPIPagarmeV5) {
                            //modelo PSP pode ser com ou sem split --> codigoautenticacao03 = sem split | codigoautenticacao04 = com split
                            String chaveDaApi = "";
                            if (conv.isUsaSplitPagamentoStoneV5()) {
                                chaveDaApi = conv.getCodigoAutenticacao04();
                            } else {
                                chaveDaApi = conv.getCodigoAutenticacao03();
                            }

                            if (!UteisValidacao.emptyString(chaveDaApi)) {
                                try {
                                    StoneOnlineV5ServiceConciliation.processarExtratosStoneV5(empresa, conv, reprocessarAPartirDe, reprocessarAte,
                                            convenioPossuiVariasEmpresasConfiguradas, chaveDaApi, con);
                                } catch (Exception e) {
                                    resultadoExtrato.getResultado().put(e.getMessage());
                                    Uteis.logarDebug("# API Pagar.me conciliação com problema : " + e.getMessage());
                                    Uteis.logarDebug("#### API Pagar.me ####");
                                } finally {
                                    processarItensSemPessoa(conv);
                                }
                            }
                            continue;
                        }

                        if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                                conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY)) {
                            try {
                                PagoLivreConciliacaoService.processarExtratos(empresa, conv, reprocessarAPartirDe, reprocessarAte, resultadoExtrato);
                            } catch (Exception e) {
                                e.printStackTrace();
                                resultadoExtrato.getResultado().put(e.getMessage());
                                Uteis.logarDebug("# PagoLivre Conciliacao | ERRO: " + e.getMessage());
                            } finally {
                                processarItensSemPessoa(conv);
                            }
                            continue;
                        }

                        // Executa a comunicacao com a Rede para obter os dados de Venda e Compensação
                        // Para que o "serviço de comunicação" com a API de conciliação da Rede funcione correntamente,
                        // é necessário que no Convênio de Cobrança, o Tipo Convênio seja DCC Rede Online e que Conciliação esteja com Status APROVADO
                        //Se o StatusConciliacaoRedeOnline for Null ou 0, então não tem Solicitação de Conciliação Online, segue para o fluxo de arquivo
                        if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && !UteisValidacao.emptyNumber(conv.getStatusConciliacaoRedeOnline().getId())) {
                            if (!conv.getStatusConciliacaoRedeOnline().equals(ERedeStatusConciliacaoEnum.APROVADO)) {
                                getFacade().getERedeServiceConciliacao().statusIntegracaoConciliacaoOnline(conv);
                            }

                            if (conv.getStatusConciliacaoRedeOnline().equals(ERedeStatusConciliacaoEnum.APROVADO)) {
                                logServidorConciliacaoRedeAPI(arquivosLocais, TipoConciliacaoEnum.VENDAS, conv, reprocessarAte, reprocessarAPartirDe);
                                getFacade().getERedeServiceConciliacao().processarExtratosERede(TipoConciliacaoEnum.VENDAS, conv, reprocessarAPartirDe, reprocessarAte, empresa,
                                        convenioPossuiVariasEmpresasConfiguradas, null);
                                logServidorConciliacaoRedeAPI(arquivosLocais, TipoConciliacaoEnum.PAGAMENTOS, conv, reprocessarAte, reprocessarAPartirDe);
                                getFacade().getERedeServiceConciliacao().processarExtratosERedeCompensacao(conv, reprocessarAPartirDe, reprocessarAte, empresa, convenioPossuiVariasEmpresasConfiguradas);
                                continue;
                            } else {
                                Uteis.logarDebug("Acesso não liberado para o PV: " + conv.getNumeroContrato() + ". Com o seguinte Status: " + conv.getStatusConciliacaoRedeOnline().getDescricao());
                            }
                        }

                        //Convênios que só podem processar caso esteja com a aba de extrato configurada
                        if (!conv.isUtilizaExtrato() && (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE) ||
                                conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                                conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET))) {
                            Uteis.logarDebug("# IGNORAR - Tipo " + conv.getTipo().getDescricao().toUpperCase() + " - Não está habilitada opção de \"Utilizar Extrato\"... " + conv.getDescricao());
                            continue;
                        }

                        Set<String> listaNomeArquivo = new HashSet<>();

                        List<Map<String, File>> mapaArquivosExtratos = new ArrayList<>();
                        if (reprocessarAPartirDe != null) {
                            int i = 0;
                            while (Calendario.menorOuIgual(Uteis.somarDias(reprocessarAPartirDe, i), reprocessarAte)) {
                                Date data = Uteis.somarDias(reprocessarAPartirDe, i);
                                obterNomeArquivo(listaNomeArquivo, conv, data, chave);
                                List<Map<String, File>> listaArquivosExtratos = receberExtratos(conv, chave, data, arquivosLocais);
                                for (Map<String, File> arquivoExtrato : listaArquivosExtratos) {
                                    if (!mapaArquivosExtratos.contains(arquivoExtrato)) {
                                        mapaArquivosExtratos.add(arquivoExtrato);
                                    }
                                }
                                i++;
                            }
                        } else {
                            obterNomeArquivo(listaNomeArquivo, conv, Calendario.hoje(), chave);
                            mapaArquivosExtratos = receberExtratos(conv, chave, Calendario.hoje(), arquivosLocais);
                        }

                        if (!mapaArquivosExtratos.isEmpty()) {
                            Uteis.logarDebug("Tem arquivos... convênio: " + conv.getDescricao());

                            List<RemessaVO> listaRemessaFAKE = new ArrayList<>();

                            for (Map<String, File> arquivo : mapaArquivosExtratos) {
                                Set<String> s = arquivo.keySet();
                                for (String fileName : s) {
                                    File f = arquivo.get(fileName);

                                    boolean arquivoProcessado = getFacade().getExtratoDiarioItem().arquivoProcessado(f.getName());

                                    if (conv.isUtilizaExtrato()) {

                                        boolean compativel = false;
                                        for (String nomeArquivo : listaNomeArquivo) {
                                            if (f.getName().toLowerCase().contains(nomeArquivo.toLowerCase())) {
                                                compativel = true;
                                                break;
                                            }
                                        }

                                        if (arquivoProcessado || !compativel) {
                                            continue;
                                        }

                                    } else {

                                        if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
                                            if ((!f.getName().startsWith("PGTOS" + conv.getNumeroContrato())
                                                    && !f.getName().startsWith("VENDAS" + conv.getNumeroContrato()))
                                                    || arquivoProcessado) {
                                                continue;
                                            }
                                        } else if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && conv.getUserSFTP().contains(":")) {
                                            if (!f.getName().contains(conv.getUserSFTP().replace(":", ""))
                                                    || arquivoProcessado) {
                                                continue;
                                            }
                                        } else if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                                            if ((!f.getName().toUpperCase().startsWith(Uteis.removerMascara(conv.getEmpresa().getCNPJ()) + "_" + TipoArquivoRedeEnum.EEFI.getId())
                                                    && !f.getName().toUpperCase().startsWith(Uteis.removerMascara(conv.getEmpresa().getCNPJ()) + "_" + TipoArquivoRedeEnum.EEVC.getId())
                                                    && !f.getName().toUpperCase().startsWith(Uteis.removerMascara(conv.getEmpresa().getCNPJ()) + "_" + TipoArquivoRedeEnum.EEVD.getId()))
                                                    || arquivoProcessado) {
                                                continue;
                                            }

                                        } else if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET) || conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE)) {
                                            if (!f.getName().toLowerCase().contains("-" + conv.getNumeroContrato() + "_")
                                                    || arquivoProcessado) {
                                                continue;
                                            }
                                        } else {
                                            if (!(f.getName().toLowerCase().contains("cielo") && f.getName().toLowerCase().contains("_" + conv.getNumeroContrato() + "_"))
                                                    || arquivoProcessado) {
                                                continue;
                                            }
                                        }
                                    }

                                    if (f.length() > 0) {
                                        try {
                                            StringBuilder arq = FileUtilities.readContentFile(fileName);

                                            getFacade().getExtratoDiarioItem().incluirExtratoArquivo(arq.toString(), f.getName(), conv);

                                            Uteis.logarDebug("Lendo arquivo de extrato " + f.getAbsolutePath() + " \nHeader " + arq.substring(0, arq.indexOf("\n")));

                                            RemessaVO remessaFAKE = processarInformacoesArquivoExtrato(arq, conv, f.getName(), convenios);
                                            remessaFAKE.setNomeArquivo(f.getName());
                                            listaRemessaFAKE.add(remessaFAKE);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            String msgErro = "# ERRO ao processar Extrato: " + e.getMessage();
                                            Uteis.logarDebug(msgErro);
                                            resultadoExtrato.getResultado().put(msgErro);
                                        }
                                    }
                                }
                            }


                            //ordenar antes de processar
                            if (conv.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                                Ordenacao.ordenarLista(listaRemessaFAKE, "dataRegistro");
                            }

                            for (RemessaVO remessaVO : listaRemessaFAKE) {
                                try {
                                    Uteis.logarDebug("Processar itens do arquivo de extrato " + remessaVO.getNomeArquivo());
                                    Uteis.logarDebug("Total de itens na listaRemessaFAKE: " + listaRemessaFAKE.size());
                                    List<ExtratoDiarioItemVO> extratoDiarioItemVOs = remessaVO.getListaExtratoDiarioItem();
                                    Uteis.logarDebug("Total de itens no extratoDiarioItemVOs: " + (!UteisValidacao.emptyList(extratoDiarioItemVOs) ? extratoDiarioItemVOs.size() : 0));
                                    if (!getFacade().getExtratoDiarioItem().arquivoProcessado(remessaVO.getNomeArquivo())) {
                                        getFacade().getExtratoDiarioItem().processarListaExtratoDiario(extratoDiarioItemVOs, false, conv);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    String msgErro = "# ERRO ao processar Extrato: " + e.getMessage();
                                    Uteis.logarDebug(msgErro);
                                    resultadoExtrato.getResultado().put(msgErro);
                                }
                            }


                            Uteis.logarDebug("FIM | Extratos da convênio: " + conv.getDescricao());
                        } else {
                            Uteis.logarDebug("Sem arquivos para processar... convênio: " + conv.getDescricao());
                        }
                    } catch (Exception ex) {
                        String msg = "# ERRO Extratos convênio: " + conv.getDescricao() + " | " + ex.getMessage();
                        resultadoExtrato.getResultado().put(msg);
                        ex.printStackTrace();
                        Uteis.logarDebug(msg);
                    }
                }
                getFacade().getResultadoServicos().gravarResultado(resultadoExtrato);
                Uteis.logarDebug("FIM | Extratos da empresa: " + empresa.getNome());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("# ERRO ao processar Extratos: " + e.getMessage());
        } finally {
            mapaConveniosJaProcessados = new HashMap<>();
        }
        Uteis.logarDebug("Finalizando Processamento de Extratos!");
        if (processarMovimentacaoAutomatica) {
            try {
                this.processarMovimentacaoAutomaticaRecebiveisConciliacao(reprocessarAPartirDe, reprocessarAte, con);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }

        try {
            this.processarTransacoesStoneAguardandoConfirmacao(con);
        } catch (Exception e) {
            Uteis.logarDebug("ERRO | PROCESSO AUTOMÁTICO DE IDENTIFICAÇÃO DE COBRANÇAS APROVADAS OU NÃO NA STONE");
            throw new RuntimeException(e);
        }

    }

    private static void logServidorConciliacaoRedeAPI(Boolean arquivosLocais, TipoConciliacaoEnum pagamentos, ConvenioCobrancaVO conv, Date reprocessarAte, Date reprocessarAPartirDe) {
        if (!arquivosLocais) {
            try {
                Uteis.logarDebug("Processar Extrato Rede API Automático - Processo " + pagamentos + " - Convênio: " + conv.getNumeroContrato());
                Uteis.logarDebug("Processar Extrato Rede API Automático - Data Inicio: " + (reprocessarAPartirDe == null ? "Sem Data" : reprocessarAPartirDe.toString()));
                Uteis.logarDebug("Processar Extrato Rede API Automático - Data Até: " + reprocessarAte.toString());
            } catch (Exception e) {
            }
        }
    }

    private void obterNomeArquivo(Set<String> listaNomeArquivo, ConvenioCobrancaVO convenioVO, Date data, String chave) {
        if (convenioVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            for (TipoArquivoRedeEnum tipoArq : TipoArquivoRedeEnum.values()) {
                String nomeArquivo = convenioVO.getNomenclaturaExtrato_Preenchido(chave, data, tipoArq.getId());
                listaNomeArquivo.add(nomeArquivo);
            }
        } else {
            String nomeArquivo = convenioVO.getNomenclaturaExtrato_Preenchido(chave, data, "");
            listaNomeArquivo.add(nomeArquivo);
        }
    }

    public static RemessaVO processarInformacoesArquivoExtrato(StringBuilder arq, ConvenioCobrancaVO convenio, String nomearquivo, List<ConvenioCobrancaVO> convenios) throws Exception {
        RemessaVO remessaFAKE = new RemessaVO();
        remessaFAKE.setRetorno(arq);
        Date dataProcessamento = Calendario.hoje();
        if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_BIN)) {
            remessaFAKE.setNomeArquivo(nomearquivo);
            LayoutExtratoDiarioBin.lerRetorno(remessaFAKE);
        } else if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
            remessaFAKE.setNomeArquivo(nomearquivo);
            LayoutExtratoDiarioRede.lerRetorno(remessaFAKE);
        } else if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET_ONLINE) ||
                convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_GETNET)) {
            remessaFAKE.setNomeArquivo(nomearquivo);
            LayoutExtratoDiarioGetNet.lerRetorno(remessaFAKE);
        } else {
            LayoutExtratoDiarioCielo.lerRetorno(remessaFAKE);
        }

        Integer tipoConciliacao = Integer.valueOf(remessaFAKE.getHeaderRetorno().getValue(DCCAttEnum.OpcaoExtrato.name()));
        tipoConciliacao = tipoConciliacao == 5 ? 4 : tipoConciliacao;

        TipoConciliacaoEnum tipoConciliacaoEnum = TipoConciliacaoEnum.getTipo(tipoConciliacao);
        if (tipoConciliacaoEnum == null) {
            return remessaFAKE;
        }

        List<ExtratoDiarioItemVO> itens = new ArrayList<ExtratoDiarioItemVO>();
        List<ExtratoDiarioItemVO> itensRedeTipoRegisto012 = new ArrayList<ExtratoDiarioItemVO>();
        List<ExtratoDiarioItemVO> itensRedeTipoRegisto014 = new ArrayList<ExtratoDiarioItemVO>();
        List<ExtratoDiarioItemVO> itensRedeTipoRegisto008Pagamento = new ArrayList<ExtratoDiarioItemVO>();
        for (RegistroRemessa item : remessaFAKE.getDetailsRetorno()) {
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC) || convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_CIELO_ONLINE)) {
                Integer tipoLancamento = new Integer(item.getValue(DCCAttEnum.TipoLancamento.name()));
                //Tipo de Lançamento - Sistema trabalha com : 01 - Venda Débito, 02 - Venda Crédito, 03 - Venda Parcelada, 06 - Cancelamento, 07 - Reversão Cancelamento, 08 - Chargeback, 09 - Reversão Chargeback
                //Se não for um desses tipos, não processa o item do extrato
                //Para mais detalhes, tem a documentação da Cielo layout 15.11: https://desenvolvedores.cielo.com.br/api-portal/sites/default/files/CIELO_Extrato_Eletronico_Manual_Versao_15_11.pdf
                if (!tipoLancamento.equals(1) && !tipoLancamento.equals(2) && !tipoLancamento.equals(3) && !tipoLancamento.equals(6) && !tipoLancamento.equals(7) &&
                        !tipoLancamento.equals(8) && !tipoLancamento.equals(9)) {
                    continue;
                }
            }

            String motivo = item.get(DCCAttEnum.Motivo.name());
            ExtratoDiarioItemVO extratoDiarioItemVO = new ExtratoDiarioItemVO(item, nomearquivo, tipoConciliacaoEnum, convenio);
            if (UteisValidacao.emptyString(extratoDiarioItemVO.getAutorizacaoInt())
                    && !UteisValidacao.emptyString(motivo)) {
                continue;
            }

            // Para que o item do extrato da rede seja vinculado ao convenio de numero de estabelecimento correto
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                for (ConvenioCobrancaVO convEcExtrato : convenios) {
                    if (!convEcExtrato.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE)) {
                        continue;
                    }
                    try {
                        Integer nrContratoExtrato = Integer.parseInt(extratoDiarioItemVO.getEstabelecimento());
                        Integer nrContratoConvenio = Integer.parseInt(convEcExtrato.getNumeroContrato());
                        if (nrContratoExtrato.equals(nrContratoConvenio)) {
                            extratoDiarioItemVO.setConvenio(convEcExtrato);
                            extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
                            break;
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            if (extratoDiarioItemVO.getRo() != null && !extratoDiarioItemVO.getRo().isEmpty() && extratoDiarioItemVO.getTipoRegistro().equals(TipoRegistroExtratoGetNetEnum.TipoRegistro2.getId())) {
                Optional<ExtratoDiarioItemVO> auxi = itens.stream().filter(ex -> ex.getTipoRegistro().equals(TipoRegistroExtratoGetNetEnum.TipoRegistro1.getId()) && ex.getRo().equals(extratoDiarioItemVO.getRo())).findFirst();
                if (auxi.get() != null)
                    extratoDiarioItemVO.setCredito(auxi.get().getCredito());
            }
            extratoDiarioItemVO.setDataProcessamentoExtrato(dataProcessamento);
            if (convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) && extratoDiarioItemVO.getTipoArquivo() != null && extratoDiarioItemVO.getTipoRegistro() != null &&
                    extratoDiarioItemVO.getTipoArquivo().equals("EEVC") && (extratoDiarioItemVO.getTipoRegistro().equals("012") || extratoDiarioItemVO.getTipoRegistro().equals("014")
                    || extratoDiarioItemVO.getTipoRegistro().equals("008"))
            ) {
                if (extratoDiarioItemVO.getTipoRegistro().equals("012")) {
                    itensRedeTipoRegisto012.add(extratoDiarioItemVO);
                }
                if (extratoDiarioItemVO.getTipoRegistro().equals("014") && itensRedeTipoRegisto012.size() > 0) {
                    for (ExtratoDiarioItemVO itemRedeTipoRegistro012 : itensRedeTipoRegisto012) {
                        if (extratoDiarioItemVO.getNrParcela() <= itemRedeTipoRegistro012.getNrTotalParcelas()) {
                            ExtratoDiarioItemVO extratoDiarioItemVORedeTipoRegistro014 = (ExtratoDiarioItemVO) extratoDiarioItemVO.getClone(true);
                            extratoDiarioItemVORedeTipoRegistro014.setAutorizacao(itemRedeTipoRegistro012.getAutorizacao());
                            extratoDiarioItemVORedeTipoRegistro014.setNsu(itemRedeTipoRegistro012.getNsu());
                            extratoDiarioItemVORedeTipoRegistro014.setNrCartao(itemRedeTipoRegistro012.getNrCartao());
                            extratoDiarioItemVORedeTipoRegistro014.setValorBruto(itemRedeTipoRegistro012.getValorBruto() / itemRedeTipoRegistro012.getNrTotalParcelas());
                            extratoDiarioItemVORedeTipoRegistro014.setTaxa(itemRedeTipoRegistro012.getTaxa());
                            extratoDiarioItemVORedeTipoRegistro014.setValorLiquido(itemRedeTipoRegistro012.getValorLiquido() / itemRedeTipoRegistro012.getNrTotalParcelas());
                            extratoDiarioItemVORedeTipoRegistro014.setValorComissao(itemRedeTipoRegistro012.getValorComissao() / itemRedeTipoRegistro012.getNrTotalParcelas());
                            extratoDiarioItemVORedeTipoRegistro014.setNrTotalParcelas(itemRedeTipoRegistro012.getNrTotalParcelas());
                            itensRedeTipoRegisto014.add(extratoDiarioItemVORedeTipoRegistro014);
                        }
                    }
                }
                if (extratoDiarioItemVO.getTipoRegistro().equals("008")) {
                    extratoDiarioItemVO.setDataPrevistaPagamento(itens.get((itens.size() - 1)).getDataPrevistaPagamento());
                    itens.add(extratoDiarioItemVO);
                    ExtratoDiarioItemVO extratoDiarioItemVORedeTipoRegistro008Pagamento = (ExtratoDiarioItemVO) extratoDiarioItemVO.getClone(true);
                    extratoDiarioItemVORedeTipoRegistro008Pagamento.setTipoConciliacao(TipoConciliacaoEnum.PAGAMENTOS.getCodigo());
                    extratoDiarioItemVORedeTipoRegistro008Pagamento.setApresentarExtrato(false);
                    itensRedeTipoRegisto008Pagamento.add(extratoDiarioItemVORedeTipoRegistro008Pagamento);
                }
            } else {
                if (itensRedeTipoRegisto012.size() > 0) {
                    itens.addAll(itensRedeTipoRegisto012);
                    itensRedeTipoRegisto012 = new ArrayList<ExtratoDiarioItemVO>();
                }
                if (itensRedeTipoRegisto014.size() > 0) {
                    itens.addAll(itensRedeTipoRegisto014);
                    itensRedeTipoRegisto014 = new ArrayList<ExtratoDiarioItemVO>();
                }
                itens.add(extratoDiarioItemVO);
            }
        }

        //Para adicionar os últimos itens do arquivo
        if (itensRedeTipoRegisto012.size() > 0) {
            itens.addAll(itensRedeTipoRegisto012);
            itensRedeTipoRegisto012 = new ArrayList<ExtratoDiarioItemVO>();
        }
        if (itensRedeTipoRegisto014.size() > 0) {
            itens.addAll(itensRedeTipoRegisto014);
            itensRedeTipoRegisto014 = new ArrayList<ExtratoDiarioItemVO>();
        }
        if (itensRedeTipoRegisto008Pagamento.size() > 0) {
            itens.addAll(itensRedeTipoRegisto008Pagamento);
            itensRedeTipoRegisto008Pagamento = new ArrayList<ExtratoDiarioItemVO>();
        }

        //O pagamento para cartão de débito vem no arquivo EEVD
        //criar os registros de pagamento
        List<ExtratoDiarioItemVO> itensPagamentosDebito = new ArrayList<>();
        for (ExtratoDiarioItemVO itemVO : itens) {
            if (itemVO.getTipoConvenioCobrancaEnum().equals(TipoConvenioCobrancaEnum.DCC_E_REDE) &&
                    itemVO.getTipoArquivo().equalsIgnoreCase(TipoArquivoRedeEnum.EEVD.getId()) &&
                    itemVO.getTipoRegistro().equalsIgnoreCase(ExtratoRedeTipoRegistroEnum.EEVD_TipoRegistro05.getId())) {
                ExtratoDiarioItemVO novoItem = (ExtratoDiarioItemVO) itemVO.getClone(true);
                novoItem.setTipoConciliacao(TipoConciliacaoEnum.PAGAMENTOS.getCodigo());
                itensPagamentosDebito.add(novoItem);
            }
        }
        itens.addAll(itensPagamentosDebito);

        remessaFAKE.setListaExtratoDiarioItem(itens);
        return remessaFAKE;
    }

    private List<Map<String, File>> receberExtratos(ConvenioCobrancaVO convenio, String chave, Date dia, Boolean arquivosLocais) {
        try {
            //TESTE COM ARQUIVO CIELO
            //Em produção os arquivos ficam separadados em diretórios com nome da data Ex:"01-06-2023", quando for testar local precisa criar os diretórios e jogar os arquivos com nome igual aos seus respectivos diretórios.
//            return FileUtilities.readListFilesDirectory("C:\\Pacto\\ArquivosConciliacao\\" + Uteis.getDataAplicandoFormatacao(dia, "dd-MM-yyyy"));
            return RetornoService.receberArquivosExtrato(convenio, chave, dia, arquivosLocais);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    private void processarItensSemPessoa(ConvenioCobrancaVO convenioCobrancaVO) {
        ExtratoDiarioItem itemDAO;
        try {
            if (convenioCobrancaVO.isStone() || convenioCobrancaVO.isStoneConnect() || convenioCobrancaVO.isStoneV5()) {
                itemDAO = new ExtratoDiarioItem(con);
                itemDAO.processarItensSemPessoa(convenioCobrancaVO.getCodigo(), Uteis.somarDias(Calendario.hoje(), -3));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            itemDAO = null;
        }
    }

    public void processarMovimentacaoAutomaticaRecebiveisConciliacao(Date reprocessarAPartirDe, Date reprocessarAte, Connection con) throws Exception {
        Uteis.logarDebug("INICIO | Processar Movimentação Automática Recebíveis Conciliação");

        //quando vem do extrato diário automático, reprocessarAPartirDe são nulos
        if (reprocessarAPartirDe == null) {
            // Conciliation generation is only permitted in past dates.
            reprocessarAPartirDe = Calendario.ontem();
        }

        MovimentacaoAutomaticaRecebiveisConciliacaoServiceImpl service = new MovimentacaoAutomaticaRecebiveisConciliacaoServiceImpl(con);
        service.processarMovimentacaoAutomaticaRecebiveisConciliacao(reprocessarAPartirDe, reprocessarAte);
        Uteis.logarDebug("FIM | Processar Movimentação Automática Recebíveis Conciliação");
    }

    public void processarTransacoesStoneAguardandoConfirmacao(Connection con) throws Exception {
        Uteis.logarDebug("INICIO | PROCESSO AUTOMÁTICO DE IDENTIFICAÇÃO DE COBRANÇAS APROVADAS OU NÃO NA STONE");
        ProcessoIdentificarCobrancasStoneAprovadasInterrogacao.processar(con);
        Uteis.logarDebug("FIM | PROCESSO AUTOMÁTICO DE IDENTIFICAÇÃO DE COBRANÇAS APROVADAS OU NÃO NA STONE");
    }
}
