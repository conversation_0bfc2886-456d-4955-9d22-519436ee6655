/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.dcc.base;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ProcessarDiasRemessaService extends SuperEntidade {

    public ProcessarDiasRemessaService() throws Exception {
    }

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            String chave = null;
            Integer dias = 0;
            Date hoje = Calendario.hoje();

            if (args.length == 0) {
                args = new String[]{"teste"};
            }

            if (args.length == 1) {
                chave = args[0];
            }

            if (args.length > 1) {
                chave = args[0];
                try {
                    dias = Integer.parseInt(args[1]);
                } catch (Exception ex) {
                    dias = 0;
                    hoje = Uteis.getDate(args[1], "dd/MM/yyyy");
                }
            }

            try {

                Uteis.logar(null, "Obter conexão para chave: " + chave);
                Uteis.logar(null, "Números dias processar: " + dias);

                Conexao.guardarConexaoForJ2SE(chave, new DAO().obterConexaoEspecifica(chave));

                Date dataInicial = Uteis.somarDias(hoje, -dias);
                Uteis.logar(null, "Data Inicial: " + Uteis.getData(dataInicial));
                List<Date> diasProcessar = Uteis.getDiasEntreDatas(dataInicial, hoje);

                for (Date dia : diasProcessar) {
                    Calendario.dia = dia;
                    Uteis.logar(null, "########### PROCESSAR DIA | INICIO | " + Uteis.getData(dia));
                    RemessaService service = new RemessaService();
                    service.setKey(chave);
                    service.processarCobrancas(dia, chave);
                    Uteis.logar(null, "########### PROCESSAR DIA | FIM | " + Uteis.getData(dia));
                }
            } catch (Exception e) {
                Logger.getLogger(ProcessarDiasRemessaService.class.getName()).log(Level.SEVERE, null, e);
            }

        } catch (Exception ex) {
            Logger.getLogger(ProcessarDiasRemessaService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}
