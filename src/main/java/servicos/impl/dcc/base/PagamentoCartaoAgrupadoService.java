package servicos.impl.dcc.base;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaColaborador;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoCondicaoPagamentoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.ContratoCondicaoPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class PagamentoCartaoAgrupadoService extends SuperEntidade {

    private EmpresaVO empresaVO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Empresa empresaDAO;
    private MovParcela movParcelaDAO;
    private Pessoa pessoaDAO;
    private AutorizacaoCobrancaCliente autoClienteDAO;
    private AutorizacaoCobrancaColaborador autoColaboradorDAO;
    private ContratoCondicaoPagamento contratoCondicaoPagamentoDAO;

    public PagamentoCartaoAgrupadoService(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO, Connection con) throws Exception {
        super(con);
        Uteis.logar("PagamentoCartaoAgrupadoService | Iniciou construtor...");
        this.empresaDAO = new Empresa(con);
        this.movParcelaDAO = new MovParcela(con);
        this.pessoaDAO = new Pessoa(con);
        this.autoClienteDAO = new AutorizacaoCobrancaCliente(con);
        this.autoColaboradorDAO = new AutorizacaoCobrancaColaborador(con);
        this.contratoCondicaoPagamentoDAO = new ContratoCondicaoPagamento(con);
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.empresaVO = empresaDAO.consultarPorChavePrimaria(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        Uteis.logar("PagamentoCartaoAgrupadoService | Fim construtor...");
    }


    public List<PagamentoCartaoTO> processarParcelas(List<MovParcelaVO> listaParcelas, UsuarioVO usuarioVO,
                                                     List<String> msgErro, Integer ordem) throws Exception {
        try {
            Uteis.logar("PagamentoCartaoAgrupadoService | processarParcelas | Inicio...");

            Map<String, Map<String, PagamentoCartaoTO>> mapaGeral = new HashMap<>();

            for (MovParcelaVO movParcelaVO : listaParcelas) {
                try {
                    //setar null na autorizacao
                    movParcelaVO.setAutorizacaoCobrancaVO(null);

                    AutorizacaoCobrancaVO autorizacaoCobrancaVO = obterAutorizacaoCobranca(movParcelaVO, ordem);
                    if (autorizacaoCobrancaVO == null) {
                        if (msgErro != null) {
                            msgErro.add(movParcelaVO.getPessoa().getNome() + " - Não foi encontrado autorização de cobrança compatível com a parcela " + movParcelaVO.getCodigo() + ". (Verifique o tipo de parcela a cobrar da autorização de cobrança)");
                        }
                        Uteis.logarDebug("Não foi encontrado autorização de cobrança compatível com a parcela " + movParcelaVO.getCodigo());
                        continue;
                    }

                    if (!UteisValidacao.emptyString(autorizacaoCobrancaVO.getTokenAragorn()) &&
                            UteisValidacao.emptyString(autorizacaoCobrancaVO.getCartaoMascarado())) {
                        Uteis.logarDebug("Vou buscar cartão Aragorn.. não tem cartão mascarado.");
                        AragornService aragornService = new AragornService();
                        aragornService.povoarAutorizacaoCobrancaVO(autorizacaoCobrancaVO, true, false);
                        aragornService = null;
                        autorizacaoCobrancaVO.setCartaoMascarado(APF.getCartaoMascarado(autorizacaoCobrancaVO.getNazgDTO().getCard()));
                        try {
                            if (!UteisValidacao.emptyString(autorizacaoCobrancaVO.getCartaoMascarado())) {
                                try {
                                    this.autoClienteDAO.alterarCartaoMascaradoInterno((AutorizacaoCobrancaClienteVO) autorizacaoCobrancaVO);
                                } catch (Exception ignored) {
                                }
                                try {
                                    this.autoColaboradorDAO.alterarCartaoMascaradoInterno((AutorizacaoCobrancaColaboradorVO) autorizacaoCobrancaVO);
                                } catch (Exception ignored) {
                                }
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }

                    movParcelaVO.setAutorizacaoCobrancaVO(autorizacaoCobrancaVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logarDebug("Erro processar parcela " + movParcelaVO.getCodigo() + " | " + ex.getMessage());
                }
            }

            Map<String, NazgDTO> mapaAragorn = povoarDadosCartaoParcelas(listaParcelas);

            //povoar informações sobre a parcela
            for (MovParcelaVO movParcelaVO : listaParcelas) {
                try {
                    if (movParcelaVO.getAutorizacaoCobrancaVO() == null ||
                            UteisValidacao.emptyNumber(movParcelaVO.getAutorizacaoCobrancaVO().getCodigo())) {
                        Uteis.logar(null, "MovParcela sem autorização de cobrança | MovParcela " + movParcelaVO.getCodigo());
                        continue;
                    }

                    Integer nrParcelas = 1;
                    //verificar a quantidade de parcelas para modo automático
                    if (!UteisValidacao.emptyNumber(movParcelaVO.getNumeroParcelasOperadora())) {
                        nrParcelas = 12;
                        try {
                            ContratoCondicaoPagamentoVO condicaoPagamentoVO = this.contratoCondicaoPagamentoDAO.consultarContratoCondicaoPagamentos(movParcelaVO.getCodigoContrato(), Uteis.NIVELMONTARDADOS_TODOS);
                            nrParcelas = condicaoPagamentoVO.getCondicaoPagamento().getNrParcelas();
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logar(null, "Erro ao consultar a condição de pagamento da parcela | " + movParcelaVO.getCodigo());
                        }
                    }


                    String keyMapaGeral;
                    String keyMapaCartao = String.valueOf(Calendario.hoje().getTime());
                    PagamentoCartaoTO pagamentoCartaoTO = null;
                    Map<String, PagamentoCartaoTO> mapaPagamentosCartao;

                    //convenio EDI DCC
                    //sempre agrupa
                    //não tem limite na cobrança.
                    boolean convenioEDI = this.convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.EDI_DCC);

                    //Agrupar por cartão
                    if (this.empresaVO.isAgruparParcelasPorCartao() || convenioEDI) {

                        //Identificador será o número do cartão
                        if (!UteisValidacao.emptyString(movParcelaVO.getAutorizacaoCobrancaVO().getNumeroCartao())) {
                            keyMapaGeral = movParcelaVO.getAutorizacaoCobrancaVO().getNumeroCartao();
                        } else {
                            keyMapaGeral = movParcelaVO.getAutorizacaoCobrancaVO().getNazgDTO().getCard();
                        }

                        //vindi cobrar usando o idvindi
                        if (UteisValidacao.emptyString(movParcelaVO.getAutorizacaoCobrancaVO().getTokenAragorn()) &&
                                !UteisValidacao.emptyNumber(movParcelaVO.getPessoa().getIdVindi()) &&
                                movParcelaVO.getAutorizacaoCobrancaVO().getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_VINDI)) {
                            keyMapaGeral = movParcelaVO.getPessoa().getIdVindi().toString();
                        }
                        //cobrar com token pagolivre
                        if (UteisValidacao.emptyString(movParcelaVO.getAutorizacaoCobrancaVO().getTokenAragorn()) &&
                                !UteisValidacao.emptyString(movParcelaVO.getAutorizacaoCobrancaVO().getTokenPagoLivre()) &&
                                (movParcelaVO.getAutorizacaoCobrancaVO().getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_PAGOLIVRE) ||
                                        movParcelaVO.getAutorizacaoCobrancaVO().getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.DCC_FACILITEPAY))) {
                            keyMapaGeral = movParcelaVO.getAutorizacaoCobrancaVO().getTokenPagoLivre();
                        }

                        if (UteisValidacao.emptyString(keyMapaGeral)) {
                            Uteis.logarDebug("Chave mapa geral não informada...");
                            continue;
                        }

                        mapaPagamentosCartao = mapaGeral.get(keyMapaGeral);
                        if (mapaPagamentosCartao == null) {
                            mapaPagamentosCartao = new HashMap<>();
                        }

                        //se não tem informado o valor é pq não tem limite de valor
                        if (UteisValidacao.emptyNumber(this.empresaVO.getAgruparParcelasPorCartaoValorLimite()) || convenioEDI) {
                            for (String key : mapaPagamentosCartao.keySet()) {
                                pagamentoCartaoTO = mapaPagamentosCartao.get(key);

                                //validar se todos as parcelas são com o mesmo número de parcelas
                                //caso de parcelado na operadora
                                if (pagamentoCartaoTO == null || pagamentoCartaoTO.getNrParcelasOperadora().equals(nrParcelas)) {
                                    keyMapaCartao = key;
                                    pagamentoCartaoTO = mapaPagamentosCartao.get(key);
                                    break;
                                } else {
                                    pagamentoCartaoTO = null;
                                }
                            }
                        } else {

                            //se está definido valor buscar qual pagamento tem valor disponivel
                            for (String key : mapaPagamentosCartao.keySet()) {
                                PagamentoCartaoTO agrupadoTO = mapaPagamentosCartao.get(key);
                                Double valorTotal = (agrupadoTO.valorTotal() + movParcelaVO.getValorParcela());

                                //se o valor ficar abaixo ou igual o limite então utilizar o agrupador
                                if (valorTotal <= this.empresaVO.getAgruparParcelasPorCartaoValorLimite()) {
                                    pagamentoCartaoTO = mapaPagamentosCartao.get(key);

                                    //validar se todos as parcelas são com o mesmo número de parcelas
                                    //caso de parcelado na operadora
                                    if (pagamentoCartaoTO == null || pagamentoCartaoTO.getNrParcelasOperadora().equals(nrParcelas)) {
                                        keyMapaCartao = key;
                                        pagamentoCartaoTO = mapaPagamentosCartao.get(key);
                                        break;
                                    } else {
                                        pagamentoCartaoTO = null;
                                    }
                                }
                            }
                        }
                    } else {

                        //como não vai agrupar.
                        //Identificador será o código da parcela
                        keyMapaGeral = movParcelaVO.getCodigo().toString();
                        keyMapaCartao = movParcelaVO.getCodigo().toString();
                        mapaPagamentosCartao = new HashMap<>();
                        pagamentoCartaoTO = null;
                    }

                    if (pagamentoCartaoTO == null) {
                        pagamentoCartaoTO = new PagamentoCartaoTO();
                        pagamentoCartaoTO.setParcelas(new ArrayList<>());
                        pagamentoCartaoTO.setAutorizacaoCobrancaVO(movParcelaVO.getAutorizacaoCobrancaVO());
                        pagamentoCartaoTO.setNrParcelasOperadora(nrParcelas);
                        pagamentoCartaoTO.setUsuarioVO(usuarioVO);
                        pagamentoCartaoTO.setEmpresaVO(this.empresaVO);
                        pagamentoCartaoTO.setConvenioCobrancaVO(this.convenioCobrancaVO);
                        pagamentoCartaoTO.setPessoaPagador(movParcelaVO.getPessoa());
                    }

                    //usar a pessoa que está como titular do cartão como a pessoa pagador
                    if (movParcelaVO.getAutorizacaoCobrancaVO().isClienteTitularCartao()) {
                        pagamentoCartaoTO.setPessoaPagador(movParcelaVO.getPessoa());
                    }

                    if (UteisValidacao.emptyString(pagamentoCartaoTO.getPessoaPagador().getNome()) &&
                            !UteisValidacao.emptyNumber(pagamentoCartaoTO.getPessoaPagador().getCodigo())) {
                        //consultar com o nome para evitar problema de inclusao de recibo caso não tenha nome
                        pagamentoCartaoTO.setPessoaPagador(this.pessoaDAO.consultarPorChavePrimaria(pagamentoCartaoTO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                    }

                    pagamentoCartaoTO.getParcelas().add(movParcelaVO);
                    mapaPagamentosCartao.put(keyMapaCartao, pagamentoCartaoTO);

                    mapaGeral.put(keyMapaGeral, mapaPagamentosCartao);

                } catch (Exception ex) {
                    ex.printStackTrace();
                    Uteis.logarDebug("Erro processar parcela " + movParcelaVO.getCodigo() + " | " + ex.getMessage());
                }
            }

            List<PagamentoCartaoTO> listaPagamentos = new ArrayList<>();
            for (String key1 : mapaGeral.keySet()) {
                Map<String, PagamentoCartaoTO> mapa2 = mapaGeral.get(key1);
                for (String key2 : mapa2.keySet()) {
                    PagamentoCartaoTO pagamentoCartaoTO = mapa2.get(key2);
                    if (pagamentoCartaoTO != null) {
                        listaPagamentos.add(pagamentoCartaoTO);
                    }
                }
            }

            povoarDadosCartao(mapaAragorn, listaPagamentos);
            return listaPagamentos;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar("PagamentoCartaoAgrupadoService | processarParcelas | ERRO: " + ex.getMessage());
            throw ex;
        } finally {
            Uteis.logar("PagamentoCartaoAgrupadoService | processarParcelas | Fim...");
        }
    }

    private Map<String, NazgDTO> povoarDadosCartaoParcelas(List<MovParcelaVO> listaParcela) throws Exception {
        try {
            Uteis.logar("PagamentoCartaoAgrupadoService | povoarDadosCartaoParcelas | Inicio...");

            List<String> tokens = new ArrayList<>();
            for (MovParcelaVO parc : listaParcela) {
                if (parc.getAutorizacaoCobrancaVO() != null &&
                        !UteisValidacao.emptyString(parc.getAutorizacaoCobrancaVO().getTokenAragorn())) {
                    tokens.add(parc.getAutorizacaoCobrancaVO().getTokenAragorn());
                }
            }

            AragornService aragornService = new AragornService(true);
            Map<String, NazgDTO> mapaAragorn = new HashMap<>();
            if (!UteisValidacao.emptyList(tokens)) {
                mapaAragorn = aragornService.obterMapaNazg(tokens);
            }
            aragornService = null;

            for (MovParcelaVO parc : listaParcela) {
                if (parc.getAutorizacaoCobrancaVO() != null &&
                        !UteisValidacao.emptyString(parc.getAutorizacaoCobrancaVO().getTokenAragorn())) {
                    NazgDTO nazgDTO = mapaAragorn.get(parc.getAutorizacaoCobrancaVO().getTokenAragorn());
                    if (nazgDTO == null) {
                        throw new Exception("Cartão não encontrado autorização " + parc.getAutorizacaoCobrancaVO().getCodigo() + " | Token " + parc.getAutorizacaoCobrancaVO().getTokenAragorn());
                    }
                    parc.getAutorizacaoCobrancaVO().setNumeroCartao(nazgDTO.getCard());
                    parc.getAutorizacaoCobrancaVO().setNomeTitularCartao(nazgDTO.getName());
                    parc.getAutorizacaoCobrancaVO().setCpfTitular(nazgDTO.getCpf());
                    parc.getAutorizacaoCobrancaVO().setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
                    parc.getAutorizacaoCobrancaVO().setValidadeCartao(Uteis.getValidadeMMYYYY(nazgDTO.getMonth(), nazgDTO.getYear(), true));
                    parc.getAutorizacaoCobrancaVO().setNazgDTO(nazgDTO);
                }
            }
            return mapaAragorn;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar("PagamentoCartaoAgrupadoService | povoarDadosCartaoParcelas | ERRO: " + ex.getMessage());
            throw ex;
        } finally {
            Uteis.logar("PagamentoCartaoAgrupadoService | povoarDadosCartaoParcelas | Fim...");
        }
    }

    private void povoarDadosCartao(Map<String, NazgDTO> mapaAragorn, List<PagamentoCartaoTO> listaPagamentos) throws Exception {
        for (PagamentoCartaoTO parc : listaPagamentos) {
            if (parc.getAutorizacaoCobrancaVO() != null &&
                    !UteisValidacao.emptyString(parc.getAutorizacaoCobrancaVO().getTokenAragorn())) {
                NazgDTO nazgDTO = mapaAragorn.get(parc.getAutorizacaoCobrancaVO().getTokenAragorn());
                if (nazgDTO == null) {
                    throw new Exception("Cartão não encontrado autorização " + parc.getAutorizacaoCobrancaVO().getCodigo());
                }
                parc.getAutorizacaoCobrancaVO().setNumeroCartao(nazgDTO.getCard());
                parc.getAutorizacaoCobrancaVO().setNomeTitularCartao(nazgDTO.getName());
                parc.getAutorizacaoCobrancaVO().setCpfTitular(nazgDTO.getCpf());
                parc.getAutorizacaoCobrancaVO().setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
                parc.getAutorizacaoCobrancaVO().setValidadeCartao(Uteis.getValidadeMMYYYY(nazgDTO.getMonth(), nazgDTO.getYear(), true));
                parc.getAutorizacaoCobrancaVO().setNazgDTO(nazgDTO);
            }
        }
    }

    private AutorizacaoCobrancaVO obterAutorizacaoCobranca(MovParcelaVO movParcelaVO, Integer ordem) {
        try {
            try {
                //Ajustar ordem da aut caso estiver errada
                this.autoClienteDAO.ajustarAutorizacaoCobrancaCartaoCliente(movParcelaVO.getPessoa().getCodigo());
            } catch (Exception ignore) {}

            List<AutorizacaoCobrancaVO> listaAuto = new ArrayList<>();
            listaAuto.addAll(this.autoClienteDAO.consultarPorPessoaTipoAutorizacao(movParcelaVO.getPessoa().getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            listaAuto.addAll(this.autoColaboradorDAO.consultarPorPessoaTipoAutorizacao(movParcelaVO.getPessoa().getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));

            for (AutorizacaoCobrancaVO auto : listaAuto) {
                if (!this.empresaVO.isHabilitarReenvioAutomaticoRemessa() &&
                        !auto.getConvenio().getCodigo().equals(this.convenioCobrancaVO.getCodigo())) {
                    Uteis.logarDebug("Autorização (" + auto.getCodigo() + ") do convênio (" + auto.getConvenio().getCodigo() + ") não é do mesmo convênio que está sendo processado (" + this.convenioCobrancaVO.getCodigo() + ") | Vou ignorar...");
                    continue;
                }

                if (ordem != null && !auto.getOrdem().equals(ordem) &&
                        !(ordem.equals(1) && listaAuto.size() == 1)) {
                    continue;
                }

                boolean compativel = auto.getConvenio().getTipo().equals(this.convenioCobrancaVO.getTipo());

                //se não for compativel e a empresa usar retentativa então validar se a operadora é compativel
                if (!compativel && empresaVO.isHabilitarReenvioAutomaticoRemessa()) {
                    compativel = auto.isBandeiraValidaParaTipoConvenioCobranca(this.convenioCobrancaVO.getTipo());
                }

                if (compativel) {
                    //preencer o tipos de produtos
                    movParcelaVO.setTipoProdutos(this.movParcelaDAO.consultaTiposProdutosMovParcela(movParcelaVO.getCodigo()));
                    if (auto.isParcelaCompativel(movParcelaVO, this.convenioCobrancaVO.getEmpresa().isGerarRemessaContratoCancelado(), this.con)) {
                        return auto;
                    }
                }
            }
            return null;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro obter autorização de cobrança - Parcela " + movParcelaVO.getCodigo());
            return null;
        }
    }

    public PagamentoCartaoTO criarPagamentoOnlineParcelaUnica(MovParcelaVO movParcelaVO, UsuarioVO usuarioVO,
                                                              Integer nrParcelasOperadora) throws Exception {
        AutorizacaoCobrancaVO autorizacaoCobrancaVO = obterAutorizacaoCobranca(movParcelaVO, null);
        if (autorizacaoCobrancaVO == null) {
            throw new Exception("Não foi encontrado autorização compatível com a parcela " + movParcelaVO.getCodigo());
        }
        movParcelaVO.setAutorizacaoCobrancaVO(autorizacaoCobrancaVO);

        PagamentoCartaoTO pagamentoCartaoTO = new PagamentoCartaoTO();
        pagamentoCartaoTO.setParcelas(new ArrayList<>());
        pagamentoCartaoTO.setAutorizacaoCobrancaVO(movParcelaVO.getAutorizacaoCobrancaVO());
        pagamentoCartaoTO.setUsuarioVO(usuarioVO);
        pagamentoCartaoTO.setEmpresaVO(movParcelaVO.getEmpresa());
        pagamentoCartaoTO.setConvenioCobrancaVO(this.convenioCobrancaVO);
        pagamentoCartaoTO.setNrParcelasOperadora(nrParcelasOperadora);
        pagamentoCartaoTO.setPessoaPagador(movParcelaVO.getPessoa());
        pagamentoCartaoTO.getParcelas().add(movParcelaVO);
        return pagamentoCartaoTO;
    }
}
