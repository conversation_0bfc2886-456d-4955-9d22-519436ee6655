package servicos.impl.dcc.base;

import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.GenericoTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Date;
import java.util.List;

public class ProcessarExclusaoContasPagarReceber extends SuperEntidade {

    private Date processarAPartirDe;
    private Date processarAte;
    private boolean excluirMovContaTodosTipoOperacaoLancamento;
    private Connection con;

    public ProcessarExclusaoContasPagarReceber(Date processarAPartirDe, Date processarAte, boolean excluirMovContaTodosTipoOperacaoLancamento, Connection con) throws Exception {
        this.processarAPartirDe = processarAPartirDe;
        this.processarAte = processarAte;
        this.excluirMovContaTodosTipoOperacaoLancamento = excluirMovContaTodosTipoOperacaoLancamento;
        this.con = con;
    }

    public void processarExclusaoContasPagarReceber() throws Exception {
        Connection c = null;
        Log logDAO;
        try {
            c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(this.con);
            c.setAutoCommit(false);
            logDAO = new Log(c);

            StringBuilder sql = new StringBuilder();

            if (getProcessarAte() == null) {
                sql.append("delete from caixamovconta  \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("where movconta in (select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append(")");
                }else{
                    sql.append("where movconta in (select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append(")");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                sql = new StringBuilder();
                sql.append("delete from movcontarateio \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("where movconta in (select codigo from movconta  \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append(")");
                }else{
                    sql.append("where movconta in (select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append(")");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                sql = new StringBuilder();
                sql.append("update nfseemitida set movconta = null where codigo in (select codigo from nfseemitida where movconta in (  \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("))");
                }else{
                    sql.append("select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("))");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                adicionarLogProcessarExclusaoContasPagarReceber(c, logDAO);

                sql = new StringBuilder();
                sql.append("delete from movconta  \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("where codigo in (select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append(")");
                }else{
                    sql.append("where codigo in (select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append(")");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

            } else {
                sql.append("delete from caixamovconta  \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("where movconta in (select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append(")");
                }else {
                    sql.append("where movconta in (select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append(")");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                sql = new StringBuilder();
                sql.append("delete from movcontarateio \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("where movconta in (select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append(")");
                }else {
                    sql.append("where movconta in (select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append(")");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                sql = new StringBuilder();
                sql.append("update nfseemitida set movconta = null where codigo in (select codigo from nfseemitida where movconta in (  \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append("))");
                }else {
                    sql.append("select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append("))");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

                adicionarLogProcessarExclusaoContasPagarReceber(c, logDAO);

                sql = new StringBuilder();
                sql.append("delete from movconta  \n");
                if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
                    sql.append("where codigo in (select codigo from movconta \n");
                    sql.append("where datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append(")");
                }else {
                    sql.append("where codigo in (select codigo from movconta where tipooperacao in (1,2)  \n");
                    sql.append("and datalancamento::date >= '"+ Uteis.getDataFormatoBD(getProcessarAPartirDe())+ "' \n");
                    sql.append("and datalancamento::date <= '"+ Uteis.getDataFormatoBD(getProcessarAte())+ "' \n");
                    sql.append(")");
                }
                SuperFacadeJDBC.executarUpdate(sql.toString(), c);

            }
            c.commit();
            c.close();
        }catch(Exception e){
            throw new Exception(e);
        }finally {
            logDAO = null;
            if (c != null) {
                c.close();
            }
        }
    }

    private void adicionarLogProcessarExclusaoContasPagarReceber(Connection c, Log logDAO) throws Exception {
        StringBuilder sql = new StringBuilder();
        if(isExcluirMovContaTodosTipoOperacaoLancamento()) {
            sql.append("select codigo, descricao AS label from movconta \n");
            sql.append("where datalancamento::date >= '" + Uteis.getDataFormatoBD(getProcessarAPartirDe()) + "' \n");
        }else{
            sql.append("select codigo, descricao AS label from movconta where tipooperacao in (1,2)  \n");
            sql.append("and datalancamento::date >= '" + Uteis.getDataFormatoBD(getProcessarAPartirDe()) + "' \n");
        }

        if (getProcessarAte() != null) {
            sql.append("and datalancamento::date <= '" + Uteis.getDataFormatoBD(getProcessarAte()) + "';");
        }
        List<GenericoTO> listaGenerica = SuperFacadeJDBC.consultarSimples(sql.toString(), c);

        StringBuilder log = new StringBuilder();
        log.append("Inicio: " + Uteis.getData(getProcessarAPartirDe(), "dd/MM/yyyy") + " | ");
        log.append("Fim: " + Uteis.getData(getProcessarAte(), "dd/MM/yyyy") + " | ");
        log.append("Opção Excluir contas de todos os Tipos de Operação: " + String.valueOf(isExcluirMovContaTodosTipoOperacaoLancamento()) + " | ");

        log.append("Código MovContas Excluidas: ");
        for (GenericoTO item: listaGenerica) {
            log.append(" " + item.getCodigo() + ",");
        }

        logDAO.incluirLogProcessarExclusaoContasPagarRecebrer(getUsuarioLogado(), log.toString());
    }

    public Date getProcessarAPartirDe() {
        return processarAPartirDe;
    }

    public void setProcessarAPartirDe(Date processarAPartirDe) {
        this.processarAPartirDe = processarAPartirDe;
    }

    public Date getProcessarAte() {
        return processarAte;
    }

    public void setProcessarAte(Date processarAte) {
        this.processarAte = processarAte;
    }

    public boolean isExcluirMovContaTodosTipoOperacaoLancamento() {
        return excluirMovContaTodosTipoOperacaoLancamento;
    }

    public void setExcluirMovContaTodosTipoOperacaoLancamento(boolean excluirMovContaTodosTipoOperacaoLancamento) {
        this.excluirMovContaTodosTipoOperacaoLancamento = excluirMovContaTodosTipoOperacaoLancamento;
    }

    public static void main(String[] args){
        try{
            Connection con = DriverManager.getConnection("************************************************", "postgres", "pactodb");
            Conexao.guardarConexaoForJ2SE(con);
            ProcessarExclusaoContasPagarReceber processar = new ProcessarExclusaoContasPagarReceber(Calendario.hoje(), null, false, con);
            processar.processarExclusaoContasPagarReceber();
        }catch(Exception e){
            e.getStackTrace();
        }
    }

    private UsuarioVO getUsuarioLogado() {
        try {
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            return loginControle.getUsuario();
        } catch (Exception e) {
            return null;
        }
    }

}
