package servicos.impl.dcc.bin;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 * Created by <PERSON> on 29/06/2016.
 */
public enum DCCBinStatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status00("00", "TRANSAÇÃO APROVADA", CodigoRetornoPactoEnum.SUCESSO),
    Status01("01", "ENTRAR EM CONTATO COM O EMISSOR DO CARTÃO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status02("02", "ENTRAR EM CONTATO COM O EMISSOR DO CARTÃO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status03("03", "ESTABELECIMENTO INCORRETO"),
    Status04("04", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO.", OperacaoRetornoCobrancaEnum.REENVIAR),
    Status05("05", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status06("06", "NÃO FOI POSSÍVEL PROCESSAR A TRANSAÇÃO"),
    Status08("08", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status10("10", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status11("11", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status12("12", "TRANSAÇÃO INVÁLIDA"),
    Status13("13", "VALOR INVÁLIDO"),
    Status14("14", "CARTÃO INVÁLIDO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_INVALIDO),
    Status15("15", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO."),
    Status30("30", "ERRO DE FORMATO"),
    Status31("31", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status33("33", "CARTÃO VENCIDO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_VENCIDO),
    Status36("36", "PROBLEMAS COM O CARTÃO"),
    Status38("38", "TENTATIVAS DE SENHA EXCEDIDA"),
    Status41("41", "PROBLEMAS COM O CARTÃO"),
    Status43("43", "PROBLEMAS COM O CARTÃO"),
    Status51("51", "PROBLEMA DE LIMITE", OperacaoRetornoCobrancaEnum.REENVIAR, CodigoRetornoPactoEnum.SALDO_INSUFICIENTE),
    Status54("54", "CARTÃO VENCIDO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_VENCIDO),
    Status56("56", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status57("57", "TRANSAÇÃO NÃO PERMITIDA PARA O PORTADOR"),
    Status61("61", "EXCEDIDO VALOR DE RETIRADAS"),
    Status62("62", "PROBLEMAS COM O CARTÃO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status65("65", "EXCEDIDO NÚMERO DE RETIRADAS"),
    Status68("68", "RESPOSTA TARDIA"),
    Status76("76", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status77("77", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status78("78", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status79("79", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status80("80", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status81("81", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status82("82", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status83("83", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status84("84", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status85("85", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status86("86", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status87("87", "TRANSAÇÃO NÃO AUTORIZADA"),
    Status94("94", "TRANSAÇÃO DUPLICADA"),
    StatusF5("F5", "CARTÃO VENCIDO", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO, CodigoRetornoPactoEnum.CARTAO_VENCIDO),
    StatusN0("N0", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN1("N1", "TAMANHO INVÁLIDO DE CARTÃO"),
    StatusN2("N2", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN3("N3", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN4("N4", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN5("N5", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN6("N6", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN7("N7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusN8("N8", "LIMITE ACIMA DO PISO"),
    StatusN9("N9", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO0("O0", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO1("O1", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO2("O2", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO3("O3", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO4("O4", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO5("O5", "SENHA REQUERIDA"),
    StatusO6("O6", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO7("O7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO8("O8", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusO9("O9", "PROBLEMAS DE PROCESSAMENTO"),
    StatusP0("P0", "PROBLEMAS DE PROCESSAMENTO"),
    StatusP1("P1", "VALOR DIÁRIO EXCEDIDO"),
    StatusP2("P2", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusP3("P3", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusP4("P4", "NÚMERO DE UTILIZAÇÃO EXCEDIDO"),
    StatusP5("P5", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusP6("P6", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO.", OperacaoRetornoCobrancaEnum.REENVIAR),
    StatusP7("P7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusP8("P8", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusP9("P9", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusQ0("Q0", "DATA DA TRANSAÇÃO INVÁLIDA"),
    StatusQ1("Q1", "DATA DE VALIDADE INVÁLIDA"),
    StatusQ2("Q2", "CÓDIGO DE TRANSAÇÃO INVÁLIDO"),
    StatusQ3("Q3", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusQ4("Q4", "NÚMERO DE UTILIZAÇÃO EXCEDIDO"),
    StatusQ5("Q5", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusQ6("Q6", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO.", OperacaoRetornoCobrancaEnum.REENVIAR),
    StatusQ7("Q7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusQ8("Q8", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusQ9("Q9", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR0("R0", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR1("R1", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR2("R2", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR3("R3", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR4("R4", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR5("R5", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR6("R6", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR7("R7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusR8("R8", "PROBLEMAS COM O CARTÃO"),
    StatusS4("S4", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO.", OperacaoRetornoCobrancaEnum.REENVIAR),
    StatusS5("S5", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusS6("S6", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusS7("S7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusS8("S8", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO.", OperacaoRetornoCobrancaEnum.REENVIAR),
    StatusS9("S9", "PROBLEMAS NO PROCESSAMENTO. REENVIE A TRANSAÇÃO.", OperacaoRetornoCobrancaEnum.REENVIAR),
    StatusT1("T1", "VALOR INVÁLIDO"),
    StatusT2("T2", "ERRO DE FORMATO"),
    StatusT3("T3", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusT4("T4", "VALOR INVÁLIDO"),
    StatusT5("T5", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusT6("T6", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusT7("T7", "TRANSAÇÃO NÃO AUTORIZADA"),
    StatusT8("T8", "TRANSAÇÃO NÃO AUTORIZADA"),
    //Status do HEADER
    StatusH4("H4", "CÓDIGO DE CLIENTE/ESTABELECIMENTO INVÁLIDO OU NÃO AUTORIZADO PARA O SERVIÇO");

    private String id;
    private String descricao;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;
    private CodigoRetornoPactoEnum codigoRetornoPacto;

    private DCCBinStatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
    }

    private DCCBinStatusEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    private DCCBinStatusEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
    }

    private DCCBinStatusEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca, CodigoRetornoPactoEnum codigoRetornoPacto) {
        this.id = id;
        this.descricao = descricao;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
        this.codigoRetornoPacto = codigoRetornoPacto;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public static DCCBinStatusEnum valueOff(String id) {
        DCCBinStatusEnum[] values = DCCBinStatusEnum.values();
        for (DCCBinStatusEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return DCCBinStatusEnum.StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }
}
