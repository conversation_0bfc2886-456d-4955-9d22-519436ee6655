package servicos.impl.email;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.ModeloMensagemSistemaVO;
import negocio.comuns.basico.enumerador.IdentificadorMensagemSistema;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.facade.jdbc.basico.ModeloMensagemSistema;
import negocio.facade.jdbc.crm.ConfiguracaoSistemaCRM;
import servicos.interfaces.ModeloMensagemSistemaServiceInterface;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Created by johny<PERSON> on 13/01/2017.
 */
public class ModeloMensagemSistemaService implements ModeloMensagemSistemaServiceInterface{

    @Override
    public void enviarEmail(IdentificadorMensagemSistema identificador, Map<String, String> parametros, UsuarioVO remetente, Collection<String> destinatarios, ConfiguracaoSistemaCRMVO configuracao) throws Exception {
        UteisEmail email = new UteisEmail();
        ModeloMensagemSistema modeloMensagemSistema = new ModeloMensagemSistema();
        email.novo("", configuracao);
        ModeloMensagemSistemaVO modelo = modeloMensagemSistema.consultarPorIdentificador(identificador);
        email.enviarEmailN(destinatarios.toArray(new String[0]), Uteis.substituirTexto(parametros, modelo.getMensagem()), Uteis.substituirTexto(parametros, modelo.getTitulo()), "");
    }

    @Override
    public void enviarEmail(IdentificadorMensagemSistema identificador, Map<String, String> parametros, UsuarioVO remetente, Collection<String> destinatarios) throws Exception {
        ConfiguracaoSistemaCRM configuracao = new ConfiguracaoSistemaCRM();
        enviarEmail(identificador, parametros, remetente, destinatarios, configuracao.consultarConfiguracaoSistemaCRM(Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
    }

    @Override
    public void enviarEmailRecorrrencia(IdentificadorMensagemSistema identificador, EmpresaVO empresaVO, List<String> listaRetornos, List<String> listaEmails, ConfiguracaoSistemaCRMVO configuracaoSistemaCRMVO) throws Exception {
        StringBuilder msgEnviar = new StringBuilder();
        msgEnviar.append("Olá!<br/>");
        msgEnviar.append("Você está recebendo este e-mail de aviso automático, para informar sobre as PARCELAS que não foram cobradas de sua Academia: <br/><br/>");
        for (String msg : listaRetornos) {
            msgEnviar.append(msg).append("<br/>");
        }
        UteisEmail email = new UteisEmail();
        String assunto = "Parcelas não cobradas";
        email.novo(assunto, configuracaoSistemaCRMVO);
        email.enviarEmailN(listaEmails.toArray(new String[0]), msgEnviar.toString(), assunto, empresaVO.getNome());
    }

}
