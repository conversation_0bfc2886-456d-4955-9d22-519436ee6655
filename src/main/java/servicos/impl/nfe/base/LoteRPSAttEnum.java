/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.impl.nfe.base;

/**
 * Created by <PERSON><PERSON> on 27/12/2016.
 */

public enum LoteRPSAttEnum {

    //HEADER

    TipoRegistro,
    VersaoLayout,
    InscricaoMunicipal,
    DataInicio,
    DataFinal,
    CaractereFimDeLinha,

    NumSequencial,

    //DETAIL

    TipoRPS,
    SerieRPS,
    NumeroRPS,
    DataEmissaoRPS,
    SituacaoRPS,
    ValorServicos,
    ValorDeducoes,
    CodigoServicoPrestado,
    Aliquota,
    ISSRetido,
    IndicadorCPFCNPJTomador,
    CPFCNPJTomador,
    InscricaoMunicipalTomador,
    InscricaoEstadualTomador,
    NomeRazaoSocialTomador,
    TipoEnderecoTomador,
    EnderecoTomador,
    NumeroEnderecoTomador,
    ComplementoEnderecoTomador,
    BairroTomador,
    CidadeTomador,
    UFTomador,
    CEPTomador,
    EmailTomador,
    DiscriminacaoServicos,

    NumeroNota,
    CodigoVerificacao,
    CidadeServico,
    UFServico,
    NumeroNotaSubstituida,
    MesCompetencia,
    AnoCompetencia,
    NaturezaOperacao,
    ValorDesconto,
    ValorPIS,
    ValorCOFINS,
    ValorIR,
    ValorCSLL,
    ValorINSS,
    Observacao,

    ItemServico,
    QuantidadeItem,
    ValorUnitarioItem,

    //TRAILER
    NumeroLinhasDetalhe,
    ValorTotalServicoes,
    ValorTotalDeducoes,

    NumeroRegistrosDetalhe;
}
