package servicos.impl.stone.xml.cancellation.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.cancellation.request.MerchantIdentificationCancellationId;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class EnvironmentCancellationResponseEnvt {

    @XStreamAlias("MrchntId")
    private MerchantIdentificationCancellationId merchantIdentificationCancellationId;

    public MerchantIdentificationCancellationId getMerchantIdentificationCancellationId() {
        return merchantIdentificationCancellationId;
    }
}
