package servicos.impl.stone.xml.cancellation.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.response.HeaderResponseHdr;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class AcceptorCancellationResponseAccptrCxlRspn {

    @XStreamAlias("Hdr")
    private HeaderResponseHdr headerHdr;

    @XStreamAlias("CxlRspn")
    private CancellationResponseCxlRspn cancellationResponseCxlRspn;

    HeaderResponseHdr getHeaderHdr() {
        return headerHdr;
    }

    CancellationResponseCxlRspn getCancellationResponseCxlRspn() {
        return cancellationResponseCxlRspn;
    }
}
