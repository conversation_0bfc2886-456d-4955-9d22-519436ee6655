package servicos.impl.stone.xml.authorization.request;

import negocio.comuns.utilitarias.RecuperadorEnumPadrao;
import negocio.comuns.utilitarias.ValorRecuperavel;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public enum MessageFunctionMsgFctn implements ValorRecuperavel {

    AUTHORISATION_REQUEST_AUTQ("AUTQ"),
    CANCELLATION_REQUEST_CCAQ("CCAQ"),
    REJECTION_RJCT("RJCT");

    private final String value;

    MessageFunctionMsgFctn(String value) {
        this.value = value;
    }

    public static MessageFunctionMsgFctn fromValue(String value) {
        return RecuperadorEnumPadrao.fromValue(values(), value);
    }

    @Override
    public String getValor() {
        return value;
    }

}
