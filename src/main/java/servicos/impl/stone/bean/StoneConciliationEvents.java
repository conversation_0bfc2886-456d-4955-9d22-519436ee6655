package servicos.impl.stone.bean;

import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 14/04/2019
 */
public class StoneConciliationEvents implements Serializable {
    private static final long serialVersionUID = 1l;
    @SerializedName("Payments")
    private int payments;
    @SerializedName("Captures")
    private int captures;
    @SerializedName("CancellationCharges")
    private int cancellationCharges;
    @SerializedName("Cancellations")
    private int cancellations;
    @SerializedName("ChargebackRefunds")
    private int chargebackRefunds;
    @SerializedName("Chargebacks")
    private int chargebacks;

    public TipoConciliacaoEnum getTipoConciliacao(){
        if(captures>0)
            return TipoConciliacaoEnum.VENDAS;
        else if(payments>0)
            return TipoConciliacaoEnum.PAGAMENTOS;
        else if (cancellations>0)
            return TipoConciliacaoEnum.CANCELAMENTO;
        else if (chargebacks>0)
            return TipoConciliacaoEnum.CHARGEBACK;
        else if (cancellationCharges>0)
            return TipoConciliacaoEnum.TAXA_CANCELAMENTO;
        else if (chargebackRefunds>0)
            return TipoConciliacaoEnum.ESTORNO_CHARGEBACK;
        else
            return null;
    }

    public Set<TipoConciliacaoEnum> getTipoConciliacaoArray(){
        Set<TipoConciliacaoEnum> tipos = new HashSet<>();
        if(captures>0)
            tipos.add(TipoConciliacaoEnum.VENDAS);
        if(payments>0)
            tipos.add(TipoConciliacaoEnum.PAGAMENTOS);
        if (cancellations>0)
            tipos.add(TipoConciliacaoEnum.CANCELAMENTO);
        if (chargebacks>0)
            tipos.add(TipoConciliacaoEnum.CHARGEBACK);
        if (cancellationCharges>0)
            tipos.add(TipoConciliacaoEnum.TAXA_CANCELAMENTO);
        if (chargebackRefunds>0)
            tipos.add(TipoConciliacaoEnum.ESTORNO_CHARGEBACK);

        return tipos;
    }

    public TipoConciliacaoEnum getTipoCancelamento(){
        if (cancellations>0)
            return TipoConciliacaoEnum.CANCELAMENTO;
        else if (chargebacks>0)
            return TipoConciliacaoEnum.CHARGEBACK;
        else if (cancellationCharges>0)
            return TipoConciliacaoEnum.TAXA_CANCELAMENTO;
        else
            return null;
    }

    public int getPayments() {
        return payments;
    }

    public void setPayments(int payments) {
        this.payments = payments;
    }

    public int getCaptures() {
        return captures;
    }

    public void setCaptures(int captures) {
        this.captures = captures;
    }

    public int getCancellationCharges() {
        return cancellationCharges;
    }

    public void setCancellationCharges(int cancellationCharges) {
        this.cancellationCharges = cancellationCharges;
    }

    public int getCancellations() {
        return cancellations;
    }

    public void setCancellations(int cancellations) {
        this.cancellations = cancellations;
    }

    public int getChargebackRefunds() {
        return chargebackRefunds;
    }

    public void setChargebackRefunds(int chargebackRefunds) {
        this.chargebackRefunds = chargebackRefunds;
    }

    public int getChargebacks() {
        return chargebacks;
    }

    public void setChargebacks(int chargebacks) {
        this.chargebacks = chargebacks;
    }
}
