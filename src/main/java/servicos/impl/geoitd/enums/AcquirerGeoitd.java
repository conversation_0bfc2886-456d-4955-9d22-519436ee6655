package servicos.impl.geoitd.enums;

public enum AcquirerGeoitd {
    DEFAULT("0","Nenhum"),
    AMERICANEXPRESS("2","American Express"),
    ANDA("78","Anda"),
    CABAL("3","Cabal"),
    CREDITEL("25","Creditel"),
    CREDITOSDIRECTOS("6","Creditos Directos"),
    EDENRED("4","Endered"),
    FIRSTDATA("8","First Data"),
    MIDES("19","Mides"),
    OCA("5","OCA"),
    PASSCARD("90","Passcard"),
    VISA("1","Visa");

    private String cod;
    private String desc;
    private Boolean isGeoitd;

    AcquirerGeoitd() {

    }

    AcquirerGeoitd(String cod, String desc) {
        this.cod = cod;
        this.desc = desc;

    }

    public String getCod() {
        return cod;
    }

    public String getDesc() {
        return desc;
    }

}
