package servicos.impl.microsservice.integracoes;

import kong.unirest.json.JSONObject;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Endereco;
import negocio.interfaces.arquitetura.LogInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import relatorio.negocio.jdbc.financeiro.ParcelaSPCTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

public class AcaoSPCCallable implements Callable<ParcelaSPCTO> {

    public static final String INCLUIR = "INCLUIR";
    public static final String EXCLUIR = "EXCLUIR";
    private String integracoesMsUrl;
    private ParcelaSPCTO parcela;
    private EmpresaVO empresaVO;
    private UsuarioVO responsavel;
    private MovParcelaInterfaceFacade movparcelaDAO;
    private LogInterfaceFacade logDAO;
    private String acaoSPC;
    private Endereco enderecoDAO;

    private String chave;

    @Override
    public ParcelaSPCTO call() throws Exception {
        ParcelaSPCTO parcelaCall = null;

        switch (acaoSPC) {
            case INCLUIR:
                parcelaCall = acaoIncluirRegistro();
                break;
            case EXCLUIR:
                parcelaCall = acaoExcluirRegistro();
                break;
            default:
                break;
        }

        return parcelaCall;
    }

    public ParcelaSPCTO acaoExcluirRegistro() throws Exception {
        String retornoInsertSPC = IntegracoesMSService
                .removerNegativacao(integracoesMsUrl,
                        parcela.getJsonSpc(),
                        empresaVO.getOperadorSpc(),
                        empresaVO.getSenhaSpc(),
                        empresaVO.getCodigoAssociadoSpc(),
                        chave);

        if(retornoInsertSPC.contains("Cep invalido ou inexistente")){
            List<EnderecoVO> consultarEnderecos = enderecoDAO.consultarEnderecos(parcela.getCodigoPessoa(), false,Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!consultarEnderecos.isEmpty()) {
                JSONObject json = new JSONObject(parcela.getJsonSpc());
                JSONObject pessoaObj = json.getJSONObject("pessoa");
                EnderecoVO endereco = consultarEnderecos.get(0);
                JSONObject enderecoObj = pessoaObj.getJSONObject("endereco");
                enderecoObj.put("cidade", "null");
                enderecoObj.put("logradouro", endereco.getEndereco());
                enderecoObj.put("numero", endereco.getNumero());
                enderecoObj.put("complemento", endereco.getComplemento());
                enderecoObj.put("bairro", endereco.getBairro());
                enderecoObj.put("cep", endereco.getCep());
                pessoaObj.put("endereco",enderecoObj);
                json.put("pessoa", pessoaObj);
                parcela.setJsonSpc( json.toString());

                movparcelaDAO.salvarJSONEnvio(parcela.getParcela(), parcela.getJsonSpc().toString());

                retornoInsertSPC = IntegracoesMSService
                        .removerNegativacao(integracoesMsUrl,
                                parcela.getJsonSpc(),
                                empresaVO.getOperadorSpc(),
                                empresaVO.getSenhaSpc(),
                                empresaVO.getCodigoAssociadoSpc(),
                                chave);
            }
        }

        boolean parcelaEstaNoSpc = !isExcludedSpc(retornoInsertSPC);

        movparcelaDAO.alterarSituacaoSPC(parcela.getParcela(), parcelaEstaNoSpc, retornoInsertSPC);
        movparcelaDAO.alterarSituacaoSPCCliente(parcela.getCodigoPessoa());
        inserirLog("POSITIVAR PARCELA", parcelaEstaNoSpc, retornoInsertSPC);

        parcela.setIncluidaSpc(parcelaEstaNoSpc);
        if (!parcelaEstaNoSpc) {
            retornoInsertSPC = "Registro removido com sucesso!";
        }
        parcela.setSituacaoSpc(retornoInsertSPC);
        parcela.setSelecionada(false);

        return parcela;
    }

    public ParcelaSPCTO acaoIncluirRegistro() throws Exception {
        MovParcelaCDLTO movParcelaCDLTO = parcela.toParcelaCDTO();
        String retornoInsertSPC = IntegracoesMSService
                .negativar(integracoesMsUrl,
                        movParcelaCDLTO,
                        empresaVO.getOperadorSpc(),
                        empresaVO.getSenhaSpc(),
                        empresaVO.getCodigoAssociadoSpc(),
                        chave);

        boolean parcelaEstaNoSpc = isIncludedSpc(retornoInsertSPC);

        movparcelaDAO.alterarSituacaoSPC(parcela.getParcela(), parcelaEstaNoSpc, retornoInsertSPC);
        if (parcelaEstaNoSpc) {
            parcela.setJsonSpc(movParcelaCDLTO.toString());
            movparcelaDAO.salvarJSONEnvio(parcela.getParcela(), parcela.getJsonSpc());
        }
        movparcelaDAO.alterarSituacaoSPCCliente(parcela.getCodigoPessoa());
        inserirLog("NEGATIVAR PARCELA", parcelaEstaNoSpc, retornoInsertSPC);

        parcela.setIncluidaSpc(parcelaEstaNoSpc);
        parcela.setErroInclusaoSpc(!parcelaEstaNoSpc);
        parcela.setSituacaoSpc(retornoInsertSPC);
        parcela.setSelecionada(false);
        return parcela;
    }

    private Boolean isExcludedSpc(String retornoExclusionSPC) {
        return retornoExclusionSPC != null && (retornoExclusionSPC.contains("sucesso")
                || retornoExclusionSPC.contains("Registro não encontrado"));
    }

    private Boolean isIncludedSpc(String retornoInsertSPC) {
        return retornoInsertSPC != null && (retornoInsertSPC.contains("sucesso")
                || retornoInsertSPC.contains("Registro de SPC já existe"));
    }

    private void inserirLog(String operacao, boolean incluidaSPC, String situacaoSPC) throws Exception {
        Date dataAlteracao = Calendario.hoje();
        List<LogVO> logsAdicionar = new ArrayList<>();
        if (parcela.isIncluidaSpc() != incluidaSPC) {
            LogVO obj = basicLog(operacao, dataAlteracao);
            obj.setNomeCampo("incluidaSPC");
            obj.setValorCampoAnterior(parcela.isIncluidaSpc() ? "Sim" : "Não");
            obj.setValorCampoAlterado(incluidaSPC ? "Sim" : "Não");
            logsAdicionar.add(obj);
        }

        if (!parcela.getSituacaoSpc().equals(situacaoSPC)) {
            LogVO obj = basicLog(operacao, dataAlteracao);
            obj.setNomeCampo("situacaoSPC");
            obj.setValorCampoAnterior(parcela.getSituacaoSpc());
            obj.setValorCampoAlterado(situacaoSPC);
            logsAdicionar.add(obj);
        }

        try {
            for (LogVO log : logsAdicionar) {
                logDAO.incluirSemCommit(log);
            }
        } catch (Exception e) {
            registrarLogErroObjetoVO(
                    parcela.getCodigoPessoa(),
                    responsavel.getNome(),
                    responsavel.getUserOamd());
        }
    }

    private LogVO basicLog(String operacao, Date dataAlteracao) {
        LogVO obj = new LogVO();
        obj.setChavePrimaria(String.valueOf(parcela.getParcela()));
        obj.setNomeEntidade("MOVPARCELA");
        obj.setNomeEntidadeDescricao("parcela");
        obj.setOperacao(operacao);
        obj.setResponsavelAlteracao(responsavel.getNome());
        obj.setUserOAMD(responsavel.getUserOamd());
        obj.setPessoa(parcela.getCodigoPessoa());
        obj.setDataAlteracao(dataAlteracao);
        return obj;
    }

    private void registrarLogErroObjetoVO(int codPessoa, String responsavel, String userOamd) {
        try {
            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade("MOVPARCELA");
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior("ERRO AO REGISTRAR LOG Negativar/Liberar SPC");
            log.setValorCampoAlterado("ERRO AO REGISTRAR LOG Negativar/Liberar SPC");
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public AcaoSPCCallable setIntegracoesMsUrl(String integracoesMsUrl) {
        this.integracoesMsUrl = integracoesMsUrl;
        return this;
    }

    public AcaoSPCCallable setParcela(ParcelaSPCTO parcela) {
        this.parcela = parcela;
        return this;
    }

    public AcaoSPCCallable setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
        return this;
    }

    public AcaoSPCCallable setResponsavel(UsuarioVO responsavel) {
        this.responsavel = responsavel;
        return this;
    }

    public AcaoSPCCallable setMovparcelaDAO(MovParcelaInterfaceFacade movparcelaDAO) {
        this.movparcelaDAO = movparcelaDAO;
        return this;
    }

    public AcaoSPCCallable setLogDAO(LogInterfaceFacade logDAO) {
        this.logDAO = logDAO;
        return this;
    }

    public AcaoSPCCallable setAcaoSPC(String acaoSPC) {
        this.acaoSPC = acaoSPC;
        return this;
    }

    public AcaoSPCCallable setEnderecoDAO(Endereco enderecoDAO ) {
        this.enderecoDAO = enderecoDAO;
        return this;
    }

    public AcaoSPCCallable  setChave(String chave) {
        this.chave = chave;
        return this;
    }
}
