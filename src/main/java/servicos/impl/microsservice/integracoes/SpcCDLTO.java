package servicos.impl.microsservice.integracoes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SpcCDLTO {

    private ResumoSpcCDLTO resumo;
    private String mensagemAlerta;

    public ResumoSpcCDLTO getResumo() {
        return resumo;
    }

    public void setResumo(ResumoSpcCDLTO resumo) {
        this.resumo = resumo;
    }

    public String getMensagemAlerta() {
        return mensagemAlerta;
    }

    public void setMensagemAlerta(String mensagemAlerta) {
        this.mensagemAlerta = mensagemAlerta;
    }
}
