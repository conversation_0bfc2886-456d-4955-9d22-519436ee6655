package servicos.impl.cieloecommerce;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoCieloVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.CieloeCommerceServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 * Created by Luiz Felipe on 31/08/2017.
 */
public class CieloeCommerceService extends AbstractCobrancaOnlineServiceComum implements CieloeCommerceServiceInterface {

    private String urlAPIRequisicao = "";
    private String urlAPIConsulta = "";
    private String merchantId;
    private String merchantKey;

    private Transacao transacaoDAO;
    private Pessoa pessoaDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioCieloECommerce;

    public CieloeCommerceService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.transacaoDAO = new Transacao(con);
        this.pessoaDAO = new Pessoa(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCieloECommerce = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioCieloECommerce != null) {
            this.merchantId = this.convenioCieloECommerce.getCodigoAutenticacao01();
            this.merchantKey = this.convenioCieloECommerce.getCodigoAutenticacao02();

            if (this.convenioCieloECommerce.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCieloRequisicaoProducao);
                this.urlAPIConsulta = PropsService.getPropertyValue(PropsService.urlApiCieloConsultaProducao);
            } else {
                this.urlAPIRequisicao = PropsService.getPropertyValue(PropsService.urlApiCieloRequisicaoSandbox);
                this.urlAPIConsulta = PropsService.getPropertyValue(PropsService.urlApiCieloConsultaSandbox);
            }
        }
    }

    public ValidacaoCartao validarDadosCartao(String numeroCartao) {
        ValidacaoCartao validacao = new ValidacaoCartao();
        try {
            RespostaHttpDTO respostaHttpDTO = executarRequestCieloConsulta("/1/cardBin/" + numeroCartao.replace(" ", "").substring(0, 6));
            String retornoValidacao = respostaHttpDTO.getResponse();
            Uteis.logar("Validar cartão Cielo. Retorno: " + retornoValidacao);
            JSONObject jsonRetornoValidacao = new JSONObject(retornoValidacao);
            validacao.setValido(jsonRetornoValidacao.getString("Status").equals("00") ? true : false);
            if (jsonRetornoValidacao.getString("Status").equals("00")) {
                validacao.setMensagem("Analise autorizada ");
            }
            if (jsonRetornoValidacao.getString("Status").equals("01")) {
                validacao.setMensagem("Bandeira não suportada");
            }
            if (jsonRetornoValidacao.getString("Status").equals("02")) {
                validacao.setMensagem("Cartão não suportado na consulta de bin");
            }
            if (jsonRetornoValidacao.getString("Status").equals("73")) {
                validacao.setMensagem("Afiliação bloqueada");
            }
            validacao.setBandeira(jsonRetornoValidacao.getString("Provider"));
            validacao.setTipo(jsonRetornoValidacao.getString("CardType"));
        } catch (Exception e) {
            e.printStackTrace();
            validacao.setValido(null);
            validacao.setMensagem("Não foi possível comunicar com api da cielo para validar o cartão");
            Uteis.logar("Não foi possível comunicar com api da cielo para validar o cartão. Retorno: " + e.getMessage());
        }
        return validacao;
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            transacao = criarTransacao(dadosCartao, new TransacaoCieloVO(), TipoTransacaoEnum.CIELO_ONLINE, this.convenioCieloECommerce);
            transacao.setCodigo(0);
            transacaoDAO.incluir(transacao);

            PessoaVO pessoa = this.pessoaDAO.consultarPorChavePrimaria(dadosCartao.getIdPessoaCartao(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            JSONObject parametrosPagamento = criarParametrosPagamento(transacao, dadosCartao, pessoa);

            //verifica se é tokenCielo
            if (!UteisValidacao.emptyString(parametrosPagamento.optJSONObject("Payment").optJSONObject("CreditCard").optString("CardToken"))) {
                //se for tokenCielo não precisa encriptar
                transacao.setParamsEnvio(parametrosPagamento.toString());
            } else {
                transacao.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
            }

            transacaoDAO.alterar(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            RespostaHttpDTO respostaHttpDTO = executarRequestCieloRequisicao(parametrosPagamento.toString(), "/1/sales/", MetodoHttpEnum.POST);
            processarRetorno(transacao, respostaHttpDTO, dadosCartao);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.NENHUMA) ||
                    transacao.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) ||
                    transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                consultarSituacaoTransacao(transacao);
            }

            realizarCapturaTransacao(transacao);
            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);

            if (transacao.isProcessarNovamente() && transacao.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
                transacao.getCartaoCreditoNovo().setParcelas(dadosCartao.getParcelas());
                return tentarAprovacao(transacao.getCartaoCreditoNovo());
            }
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    public void repetirConsultarTransacao(int qtd, TransacaoVO transacaoVO, SituacaoTransacaoEnum situacaoAnterior) throws Exception {
        if (qtd > 0) {
            Uteis.logarDebug("CIELO | repetirConsultarTransacao | " + transacaoVO.getCodigo());
            consultarSituacaoTransacao(transacaoVO);
            if (transacaoVO.getSituacao().equals(situacaoAnterior)) {
                Thread.sleep(1000);
                repetirConsultarTransacao(qtd - 1, transacaoVO, situacaoAnterior);
            }
        }
    }

    public TransacaoVO realizarCapturaTransacao(TransacaoVO transacao) {
        try {
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                consultarSituacaoCobrancaTransacao(transacao);
            }

            if (!transacao.isTransacaoVerificarCartao() &&
                    !UteisValidacao.emptyString(transacao.getCodigoExterno()) && transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                RespostaHttpDTO respostaHttpDTO = executarRequestCieloRequisicao(" ", "/1/sales/" + transacao.getCodigoExterno() + "/capture", MetodoHttpEnum.PUT);
                incluirHistoricoRetornoTransacao(transacao, respostaHttpDTO.getResponse(), "realizarCapturaTransacao");

                try {
                    JSONObject retornoCapture = new JSONObject(respostaHttpDTO.getResponse());
                    Integer returnCode = retornoCapture.getInt("ReturnCode");
                    if (returnCode.equals(Integer.parseInt(CieloECommerceRetornoEnum.Status6.getId()))) {
                        transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    consultarSituacaoTransacao(transacao);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return transacao;
    }

    private JSONObject criarParametrosPagamento(TransacaoVO transacaoVO, CartaoCreditoTO dadosCartao, PessoaVO pessoa) {

        String identificador = "TRA" + transacaoVO.getCodigo();
        Uteis.logarDebug("IDENTIFICADOR CIELO-ONLINE: " + identificador);

        JSONObject pagamento = new JSONObject();
        pagamento.put("MerchantOrderId", identificador);
        JSONObject pessoaJson = new JSONObject();
        pessoaJson.put("Name", Uteis.retirarAcentuacao(pessoa.getNome()));
        pagamento.put("Customer", pessoaJson);

        if (!UteisValidacao.emptyString(dadosCartao.getNumero()) || !UteisValidacao.emptyString(dadosCartao.getTokenCielo())) {
            pagamento.put("Payment", criarParametrosCartao(dadosCartao, transacaoVO));
        }
        return pagamento;
    }

    private JSONObject criarParametrosCartao(CartaoCreditoTO cartaoCreditoTO, TransacaoVO transacaoVO) throws JSONException {
        JSONObject dadosPagamento = new JSONObject();
        dadosPagamento.put("Type", "CreditCard");
        dadosPagamento.put("Amount", StringUtilities.formatarCampoMonetario(cartaoCreditoTO.getValor(), 15));
        dadosPagamento.put("Installments", cartaoCreditoTO.getParcelas());
        dadosPagamento.put("SoftDescriptor", StringUtilities.formatarCampoEmBranco(this.convenioCieloECommerce.getEmpresa().getRazaoSocialParaSoftDescriptor(cartaoCreditoTO.isTransacaoVerificarCartao()), 13));
        dadosPagamento.put("Capture", !cartaoCreditoTO.isTransacaoVerificarCartao());

        //transação recorrente
        if (!cartaoCreditoTO.isTransacaoPresencial()) {
            dadosPagamento.put("Recurrent", true);
        }

        JSONObject dadosCartao = new JSONObject();

        if (UteisValidacao.emptyString(cartaoCreditoTO.getTokenCielo())) {
            if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
                dadosCartao.put("CardNumber", cartaoCreditoTO.getNumero());
            }
            if (!UteisValidacao.emptyString(cartaoCreditoTO.getNomeTitular().trim())) {
                dadosCartao.put("Holder", cartaoCreditoTO.getNomeTitular());
            }
            dadosCartao.put("ExpirationDate", cartaoCreditoTO.getValidadeMMYYYY(true));

            if (cartaoCreditoTO.isTransacaoPresencial() && !UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
                dadosCartao.put("SecurityCode", cartaoCreditoTO.getCodigoSeguranca());
            }
            dadosCartao.put("Brand", cartaoCreditoTO.getBand().getDescricaoCielo());
        } else {
            //usar token Cielo para cobrar
            dadosCartao.put("CardToken", cartaoCreditoTO.getTokenCielo());
            if (cartaoCreditoTO.isTransacaoPresencial() && !UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
                dadosCartao.put("SecurityCode", cartaoCreditoTO.getCodigoSeguranca());
            }
            dadosCartao.put("Brand", cartaoCreditoTO.getBand().getDescricaoCielo());
        }

        dadosPagamento.put("CreditCard", dadosCartao);

        //salvar para ser apresentado no gestão de transação
        gravarOutrasInformacoes(cartaoCreditoTO.getNumero(), cartaoCreditoTO, transacaoVO);
        return dadosPagamento;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        JSONObject payment = parametrosPagamento.getJSONObject("Payment");
        JSONObject creditCard = payment.getJSONObject("CreditCard");
        creditCard.put("CardNumber", APF.getCartaoMascarado(creditCard.getString("CardNumber")));
        if (creditCard.has("SecurityCode")) {
            creditCard.put("SecurityCode", "***");
        }
        return parametrosPagamento.toString();
    }

    @Override
    public String consultarTransacao(TransacaoVO transacaoVO) throws Exception {
        RespostaHttpDTO respostaHttpDTO = executarRequestCieloConsulta("/1/sales/" + transacaoVO.getCodigoExterno());
        return respostaHttpDTO.getResponse();
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        realizarCapturaTransacao(transacaoOriginal);
        new Transacao(getCon()).alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        transacaoOriginal.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacaoOriginal);
        return transacaoOriginal;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoNova, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        transacaoNova.setDataProcessamento(Calendario.hoje());
        new Transacao(getCon()).incluir(transacaoNova);
        JSONObject parametrosEnviar = decifrarDadosSigilososReEnvio(transacaoNova);
        RespostaHttpDTO respostaHttpDTO = executarRequestCieloRequisicao(parametrosEnviar.toString(), "/1/sales", MetodoHttpEnum.POST);
        processarRetorno(transacaoNova, respostaHttpDTO, null);
        if (transacaoNova.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA)) {
            consultarSituacaoTransacao(transacaoNova);
        }
        new Transacao(getCon()).alterar(transacaoNova);
        return transacaoNova;
    }

    private JSONObject decifrarDadosSigilososReEnvio(TransacaoVO transacaoVO) throws Exception {
        if (UteisValidacao.emptyString(transacaoVO.getTokenAragorn())) {
            throw new Exception("Não foi possível obter o token do cartão");
        } else {
            NazgDTO nazgDTO = obterNazgTO(transacaoVO.getTokenAragorn());
            JSONObject parametrosEnvio = new JSONObject(transacaoVO.getParamsEnvio());
            JSONObject payment = parametrosEnvio.getJSONObject("Payment");
            JSONObject creditCard = payment.getJSONObject("CreditCard");
            creditCard.put("CardNumber", nazgDTO.getCard());
            return parametrosEnvio;
        }
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
            realizarCancelamentoTransacao(transacao, estornarRecibo);
        }
        return transacao;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        RespostaHttpDTO respostaHttpDTO = executarRequestCieloRequisicao(" ", "/1/sales/" + transacao.getCodigoExterno() + "/void?amount=" + StringUtilities.formatarCampoMonetario(transacao.getValor(), 15), MetodoHttpEnum.PUT);
        if (respostaHttpDTO.getHttpStatus() != null && respostaHttpDTO.getHttpStatus().equals(401)) {
            throw new Exception("401 Unauthorized. Não foi possível realizar a requisição na API da CIELO. Verifique diretamente com a Cielo o motivo.");
        }
        if (respostaHttpDTO.getHttpStatus() != null && respostaHttpDTO.getHttpStatus().equals(404)) {
            throw new Exception("404 Resource Not Found. Não foi possível identificar a transação na CIELO.");
        }
        processarRetornoCancelamento(transacao, respostaHttpDTO.getResponse());
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && estornarRecibo &&
                !UteisValidacao.emptyNumber(transacao.getReciboPagamento())) {
            estornarRecibo(transacao, estornarRecibo);
            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        new Transacao(getCon()).alterar(transacao);
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, String retorno) throws Exception {
        incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetornoCancelamento");
        transacao.setResultadoCancelamento(retorno);
        try {
            if (retorno.toLowerCase().contains("transaction not available")) {
                consultarSituacaoTransacao(transacao);
            } else {
                JSONObject retornoCancelamentoJSON;
                try {
                    retornoCancelamentoJSON = new JSONObject(retorno);
                } catch (Exception ex) {
                    //tentar se é array
                    String msgErro = "";
                    try {
                        JSONArray array = new JSONArray(retorno);
                        msgErro = array.getJSONObject(0).optString("Message");
                        if (msgErro.contains("Transaction temporarily blocked for new refund requests")) {
                            msgErro = "Transação bloqueada temporariamente para novas solicitações de reembolso. Entrar em contato com a CIELO.";
                        }
                    } catch (Exception ignored) {
                    }
                    if (!UteisValidacao.emptyString(msgErro)) {
                        throw new Exception(msgErro);
                    }
                    throw ex;
                }

                Integer status = retornoCancelamentoJSON.getInt("Status");
                String returnCode = retornoCancelamentoJSON.optString("ReturnCode");
                String returnMessage = retornoCancelamentoJSON.optString("ReturnMessage");
                if (status.equals(CieloECommerceStatusEnum.ANULADO.getId()) || status.equals(CieloECommerceStatusEnum.DEVOLVIDO.getId())) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                    transacao.setDataHoraCancelamento(Calendario.hoje());
                } else {
                    CieloECommerceRetornoEnum cieloEnum = CieloECommerceRetornoEnum.valueOff(returnCode);
                    if (!cieloEnum.equals(CieloECommerceRetornoEnum.StatusNENHUM)) {
                        throw new Exception(cieloEnum.getDescricao());
                    } else {
                        throw new Exception(returnMessage);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            new Transacao(getCon()).alterar(transacao);
        }
    }

    public void consultarSituacaoTransacao(TransacaoVO transacao) throws Exception {
        if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
            try {
                consultarPaymentIdPeloMerchantOrderId(transacao);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        if (!UteisValidacao.emptyString(transacao.getCodigoExterno())) {
            RespostaHttpDTO respostaHttpDTO = executarRequestCieloConsulta("/1/sales/" + transacao.getCodigoExterno());
            processarRetornoConsultaTransacao(transacao, respostaHttpDTO.getResponse());
        }
    }

    private void processarRetornoConsultaTransacao(TransacaoVO transacao, String retorno) {
        try {
            incluirHistoricoRetornoTransacao(transacao, retorno, "processarRetornoConsultaTransacao");
            JSONObject retornoTransacao = new JSONObject(retorno);
            JSONObject payment = retornoTransacao.getJSONObject("Payment");
            transacao.setParamsResposta(retorno);
            Integer status = payment.getInt("Status");
            obterBandeira(transacao, payment);

            try {
                Integer nrVezes = payment.getInt("Installments");
                transacao.setNrVezes(nrVezes);
            } catch (Exception ignored) {
            }

            preencherSituacaoTransacao(transacao, status, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            incluirHistoricoRetornoTransacao(transacao, retorno, "ERRO_processarRetornoConsultaTransacao");
            if (!transacao.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
        }
    }

    public void consultarPaymentIdPeloMerchantOrderId(TransacaoVO transacaoVO) throws Exception {
        Transacao transacaoDAO = null;
        try {
            String merchantOrderId = ("TRA" + transacaoVO.getCodigo());
            if (!UteisValidacao.emptyString(transacaoVO.getParamsEnvio())) {
                try {
                    JSONObject envio = new JSONObject(transacaoVO.getParamsEnvio());
                    merchantOrderId = envio.getString("MerchantOrderId");
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            if (UteisValidacao.emptyString(merchantOrderId)) {
                throw new Exception("MerchantOrderId não identificado");
            }

            RespostaHttpDTO respostaHttpDTO = executarRequestCieloConsulta("/1/sales?merchantOrderId=" + merchantOrderId);
            incluirHistoricoRetornoTransacao(transacaoVO, respostaHttpDTO.getResponse(), "consultarPaymentIdPeloMerchantOrderId");

            if (respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_NOT_FOUND)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                throw new Exception("Não foi possível consultar a situação da transação ou transação não encontrada");
            }

            JSONObject retornoTransacao = new JSONObject(respostaHttpDTO.getResponse());
            JSONObject payment;
            if (retornoTransacao.has("Payments")) {
                payment = retornoTransacao.getJSONArray("Payments").getJSONObject(0);
            } else {
                payment = retornoTransacao.getJSONArray("Payment").getJSONObject(0);
            }

            String paymentId = payment.getString("PaymentId");
            if (!UteisValidacao.emptyString(paymentId)) {
                transacaoDAO = new Transacao(getCon());

                transacaoVO.setCodigoExterno(paymentId);
                transacaoDAO.alterarCodigoExterno(transacaoVO);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            transacaoDAO = null;
        }
    }

    private boolean transacaoFoiNegada(RespostaHttpDTO respostaHttpDTO) {
        return (respostaHttpDTO.getResponse().toUpperCase().contains("SECURITYCODE IS INVALID MUST BE ONLY NUMBER") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("SECURITYCODE LENGTH EXCEEDED") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("THE PROVIDED MERCHANTID IS NOT IN CORRECT FORMAT") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("SECURITYCODE LENGTH MUST BE LONGER") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("MERCHANTKEY IS INVALID") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("MERCHANTID IS REQUIRED") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("504 GATEWAY TIME-OUT") ||
                respostaHttpDTO.getResponse().toUpperCase().contains("MERCHANT IS BLOCKED"));
    }

    private void processarRetorno(TransacaoVO transacao, RespostaHttpDTO respostaHttpDTO, CartaoCreditoTO dadosCartao) {
        incluirHistoricoRetornoTransacao(transacao, respostaHttpDTO.getResponse(), "processarRetorno");
        transacao.setParamsResposta(respostaHttpDTO.getResponse());
        boolean naoAprovada = false;
        try {

            JSONObject retornoJSON = null;
            try {
                retornoJSON = new JSONObject(respostaHttpDTO.getResponse());
            } catch (JSONException ex) {
                if (transacaoFoiNegada(respostaHttpDTO)) {
                    naoAprovada = true;
                }
                if (!naoAprovada &&
                        respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_UNAUTHORIZED)) {
                    naoAprovada = true;
                    transacao.setParamsResposta("Credenciais Inválidas ou bloqueadas. Necessário entrar em contato com a Cielo (401 SC_UNAUTHORIZED)");
                    if (UteisValidacao.emptyString(transacao.getCodigoRetorno())) {
                        transacao.setCodigoRetorno("002");
                    }
                    throw new Exception("Credenciais Inválidas ou bloqueadas. Necessário entrar em contato com a Cielo (401 SC_UNAUTHORIZED).");
                } else {
                    throw new Exception(respostaHttpDTO.getResponse());
                }
            }

            JSONObject payment = retornoJSON.getJSONObject("Payment");

            try {
                String dataCobrancaString = payment.getString("CapturedDate");
                Date dataCobranca = Uteis.getDate(dataCobrancaString, "yyyy-MM-dd HH:mm:ss");
                transacao.setDataCobranca(dataCobranca);
            } catch (Exception ignored) {
            }

            Integer status = payment.getInt("Status");
            String returnCode = payment.getString("ReturnCode");
            String paymentId = payment.getString("PaymentId");
            obterBandeira(transacao, payment);

            try {
                Integer nrVezes = payment.getInt("Installments");
                transacao.setNrVezes(nrVezes);
            } catch (Exception ignored) {
            }

            transacao.setCodigoExterno(paymentId);

            if (payment.has("NewCard")) {
                processarNovoCartao(transacao, payment, dadosCartao);
            }

            preencherSituacaoTransacao(transacao, status, returnCode);

        } catch (Exception e) {
            e.printStackTrace();
            transacao.adicionarOutrasInformacoes(AtributoTransacaoEnum.mensagemErro, e.getMessage());
            if (naoAprovada) {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            } else {
                //colocar como APROVADA para evitar caso ocorra algum erro e a transação seja cobrada do cliente
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
            }
        }
    }

    private void preencherSituacaoTransacao(TransacaoVO transacaoVO, Integer status, String returnCode) {

        if (status.equals(CieloECommerceStatusEnum.AUTORIZADO.getId()) || status.equals(CieloECommerceStatusEnum.PAGAMENTO_CONFIRMADO.getId())) {

            if (returnCode.equals(CieloECommerceRetornoEnum.Status4.getId())
                    || returnCode.equals(CieloECommerceRetornoEnum.Status00.getId())
                    || returnCode.equals(CieloECommerceRetornoEnum.Status0.getId())
                    || returnCode.equals(CieloECommerceRetornoEnum.Status11.getId())) {

                transacaoVO.setSituacao(SituacaoTransacaoEnum.APROVADA);
                transacaoVO.setPermiteRepescagem(false);

            } else if (returnCode.equals(CieloECommerceRetornoEnum.Status6.getId())) {

                transacaoVO.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                transacaoVO.setPermiteRepescagem(false);

            } else {

                if (status.equals(CieloECommerceStatusEnum.AUTORIZADO.getId())) {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.APROVADA);
                    transacaoVO.setPermiteRepescagem(false);

                } else if (status.equals(CieloECommerceStatusEnum.PAGAMENTO_CONFIRMADO.getId())) {

                    transacaoVO.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
                    transacaoVO.setPermiteRepescagem(false);

                } else {
                    transacaoVO.setSituacao(SituacaoTransacaoEnum.APROVADA);
                    transacaoVO.setPermiteRepescagem(false);
                }

            }

        } else if (status.equals(CieloECommerceStatusEnum.ANULADO.getId()) || status.equals(CieloECommerceStatusEnum.DEVOLVIDO.getId())) {

            transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            transacaoVO.setPermiteRepescagem(false);

        } else if (status.equals(CieloECommerceStatusEnum.PENDENTE.getId())) {

            transacaoVO.setSituacao(SituacaoTransacaoEnum.PENDENTE);
            transacaoVO.setPermiteRepescagem(false);

        } else {

            transacaoVO.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            transacaoVO.setPermiteRepescagem(true);

        }
    }

    private void processarNovoCartao(TransacaoVO transacaoVO, JSONObject payment, CartaoCreditoTO cartaoCreditoTOAnterior) {
        try {
            transacaoVO.setProcessarNovamente(false);

            JSONObject newCard = payment.getJSONObject("NewCard");

            String nrCartao = newCard.getString("CardNumber");
            String validadeCartao = newCard.getString("ExpirationDate");
            String bandeiraCartao = newCard.getString("Brand");
            String nomeTitular = newCard.optString("Holder");

            if (UteisValidacao.emptyString(nomeTitular) && transacaoVO.getCartaoCreditoTO() != null &&
                    !UteisValidacao.emptyString(transacaoVO.getCartaoCreditoTO().getNomeTitular())) {
                nomeTitular = transacaoVO.getCartaoCreditoTO().getNomeTitular();
            }

            OperadorasExternasAprovaFacilEnum novaOperadora = null;
            for (OperadorasExternasAprovaFacilEnum operadora : OperadorasExternasAprovaFacilEnum.values()) {
                if (operadora.getDescricaoCielo().toUpperCase().equals(bandeiraCartao.toUpperCase())) {
                    novaOperadora = operadora;
                    break;
                }
            }

            String cartaoEnviado = transacaoVO.getValorCartaoMascarado();

            if (novaOperadora != null) {
                ClienteVO clienteVO = new Cliente(getCon()).consultarPorCodigoPessoa(transacaoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                AutorizacaoCobrancaCliente autorizacaoCobrancaCliente = new AutorizacaoCobrancaCliente(getCon());
                List<AutorizacaoCobrancaClienteVO> listaAutorizacoes = autorizacaoCobrancaCliente.consultarPorClienteTipoAutorizacao(clienteVO.getCodigo(), TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                for (AutorizacaoCobrancaClienteVO auto : listaAutorizacoes) {
                    if (auto.getCartaoMascarado_Apresentar().equals(cartaoEnviado)) {

                        autorizacaoCobrancaCliente.alterarSituacaoAutorizacaoCobranca(false, auto, "Processo de RENOVA FACIL CIELO");

                        AutorizacaoCobrancaClienteVO novaAutorizacao = new AutorizacaoCobrancaClienteVO();
                        novaAutorizacao.setNumeroCartao(nrCartao);
                        novaAutorizacao.setCartaoMascarado(APF.getCartaoMascarado(novaAutorizacao.getNumeroCartao()));
                        novaAutorizacao.setValidadeCartao(validadeCartao);
                        novaAutorizacao.setNomeTitularCartao(nomeTitular);
                        novaAutorizacao.setOperadoraCartao(novaOperadora);
                        novaAutorizacao.setConvenio(auto.getConvenio());
                        novaAutorizacao.setTipoAutorizacao(auto.getTipoAutorizacao());
                        novaAutorizacao.setTipoACobrar(auto.getTipoACobrar());
                        novaAutorizacao.setCliente(auto.getCliente());
                        novaAutorizacao.setClienteTitularCartao(auto.isClienteTitularCartao());
                        novaAutorizacao.setValidarClienteTitularCartao(false);
                        novaAutorizacao.setCartaoVerificado(true);
                        novaAutorizacao.setValidarQtdCartoes(false);
                        novaAutorizacao.setOrigemCobrancaEnum(OrigemCobrancaEnum.ZW_AUTOMATICO_RENOVA_FACIL_CIELO);
                        autorizacaoCobrancaCliente.incluir(novaAutorizacao);
                        autorizacaoCobrancaCliente.marcarRenovadoAutomatico(auto, novaAutorizacao);

                        CartaoCreditoTO novoCartao = new CartaoCreditoTO();
                        novoCartao.setOrigemCobranca(transacaoVO.getOrigem());
                        novoCartao.setTipoTransacaoEnum(transacaoVO.getTipo());
                        novoCartao.setTokenAragorn(novaAutorizacao.getTokenAragorn());
                        novoCartao.setNumero(nrCartao);
                        novoCartao.setValidade(validadeCartao);
                        novoCartao.setNomeTitular(nomeTitular);
                        novoCartao.setBand(novaOperadora);
                        novoCartao.setUrl(transacaoVO.getUrlTransiente());
                        novoCartao.setUsuarioResponsavel(transacaoVO.getUsuarioResponsavel());
                        novoCartao.setEmpresa(transacaoVO.getEmpresa());
                        novoCartao.setListaParcelas(transacaoVO.getListaParcelas());
                        novoCartao.setValor(transacaoVO.getValor());
                        if (cartaoCreditoTOAnterior != null) {
                            novoCartao.setTransacaoPresencial(cartaoCreditoTOAnterior.isTransacaoPresencial());
                        }
                        novoCartao.setIdPessoaCartao(transacaoVO.getPessoaPagador().getCodigo());
                        transacaoVO.setCartaoCreditoNovo(novoCartao);
                        transacaoVO.setProcessarNovamente(true);
                        Uteis.logar(null, "Adicionei cartão atraves do renova facil cielo na autorização de cobrança do cliente " + auto.getCliente().getNome_Apresentar());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao adicionar cartão atraves do renova facil cielo " + e.getMessage());
            transacaoVO.setProcessarNovamente(false);
        }
    }

    private RespostaHttpDTO executarRequestCieloConsulta(String metodo) throws Exception {
        return executarRequestCieloECommerce(true, null, metodo, MetodoHttpEnum.GET);
    }

    private RespostaHttpDTO executarRequestCieloRequisicao(String parametros, String metodo, MetodoHttpEnum metodoHttpEnum) throws Exception {
        return executarRequestCieloECommerce(false, parametros, metodo, metodoHttpEnum);
    }

    private RespostaHttpDTO executarRequestCieloECommerce(boolean consulta, String parametros, String metodo, MetodoHttpEnum metodoHttpEnum) throws Exception {
        validarDadosConvenio();

        String URL = this.urlAPIRequisicao;
        if (consulta) {
            URL = this.urlAPIConsulta;
        }
        String path = URL + metodo;
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", "application/json");
        headers.put("MerchantId", this.merchantId);
        headers.put("MerchantKey", this.merchantKey);
//        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, headers, metodoHTTP, "UTF-8");

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, parametros, metodoHttpEnum);
        service = null;
        return respostaHttpDTO;
    }

    private void validarDadosConvenio() throws Exception {
        if (this.convenioCieloECommerce == null || UteisValidacao.emptyNumber(this.convenioCieloECommerce.getCodigo())) {
            throw new Exception("Convênio de cobrança não encontrado ou inativo.");
        }
        if (UteisValidacao.emptyString(this.merchantId)) {
            throw new Exception("MerchantId no convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyString(this.merchantKey)) {
            throw new Exception("MerchantKey no convênio de cobrança não informado.");
        }
    }

    public ConvenioCobrancaVO getConvenioCieloECommerce() {
        return convenioCieloECommerce;
    }

    public void setConvenioCieloECommerce(ConvenioCobrancaVO convenioCieloECommerce) {
        this.convenioCieloECommerce = convenioCieloECommerce;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantKey() {
        return merchantKey;
    }

    public void setMerchantKey(String merchantKey) {
        this.merchantKey = merchantKey;
    }

    private void obterBandeira(TransacaoVO transacao, JSONObject payment) {
        try { //IDENTIFICAR A OPERADORA DO CARTAO
            JSONObject cartao = payment.getJSONObject("CreditCard");
            String name = cartao.optString("Brand");
            if (!UteisValidacao.emptyString(name)) {
                for (OperadorasExternasAprovaFacilEnum ope : OperadorasExternasAprovaFacilEnum.values()) {
                    String operadoraEnum = ope.getDescricao().toUpperCase().replaceAll(" ", "");
                    String operadoraPagamento = name.toUpperCase().replaceAll(" ", "");
                    if (operadoraEnum.equals(operadoraPagamento)) {
                        transacao.setBandeiraPagamento(ope);
                        break;
                    }
                }
            } else {
                transacao.setBandeiraPagamento(null);
            }
        } catch (Exception ignored) {
            transacao.setBandeiraPagamento(null);
        }
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            consultarPaymentIdPeloMerchantOrderId(transacaoVO);
        }
        consultarSituacaoTransacao(transacaoVO);
    }
}
