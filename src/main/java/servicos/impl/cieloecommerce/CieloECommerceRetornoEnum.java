/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.cieloecommerce;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/*
 * Created by <PERSON><PERSON> on 31/08/2017.
 */
public enum CieloECommerceRetornoEnum {

    StatusNENHUM("NENHUM", ""),
    Status1("1","Processamento em andamento"),
    Status2("2","Confirmação de autenticação"),
    Status3("3","Transação não autenticavel"),
    Status4("4","Confirmação de autorização"),
    Status5("5","Confirmação de não autorização"),
    Status6("6","Confirmação de captura"),
    Status9("9","Confirmação de cancelamento"),
    Status10("10","Processamento de autenticação"),
    Status00("00","Transação autorizada com sucesso.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status0("000","Transação autorizada com sucesso.", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status01("01","Transação não autorizada. Referida (suspeita de fraude) pelo banco emissor."),
    Status02("02","Transação não autorizada. Referida (suspeita de fraude) pelo banco emissor."),
    Status03("03","Transação não permitida. Estabelecimento inválido. Entre com contato com a Cielo."),
    Status04("04","Transação não autorizada. Cartão bloqueado pelo banco emissor.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status05("05","Transação não autorizada. Não foi possível processar a transação. Questão relacionada a segurança, inadimplencia ou limite do portador.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Status06("06","Transação não autorizada. Não foi possível processar a transação. Cartão cancelado permanentemente pelo banco emissor.", CodigoRetornoPactoEnum.CARTAO_CANCELADO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status07("07","Transação não autorizada por regras do banco emissor."),
    Status08("08","Transação não autorizada. Código de segurança inválido. Oriente o portador a corrigir os dados e tentar novamente."),
    Status11("11","Transação autorizada com sucesso."),
    Status12("12","Não foi possível processar a transação. Solicite ao portador que verifique os dados do cartão e tente novamente."),
    Status13("13","Transação não permitida. Valor inválido. Solicite ao portador que reveja os dados e novamente. Se o erro persistir, entre em contato com a Cielo."),
    Status14("14","Transação não autorizada. Cartão inválido. Pode ser bloqueio do cartão no banco emissor, dados incorretos ou tentativas de testes de cartão.", OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status15("15","Transação não autorizada. Banco emissor indisponível."),
    Status19("19","Não foi possível processar a transação. Refaça a transação ou tente novamente mais tarde. Se o erro persistir, entre em contato com a Cielo."),
    Status21("21","Não foi possível processar o cancelamento. Se o erro persistir, entre em contato com a Cielo."),
    Status22("22","Não foi possível processar a transação. Número de parcelas inválidas. Se o erro persistir, entre em contato com a Cielo."),
    Status23("23","Não foi possível processar a transação. Valor da prestação inválido. Se o erro persistir, entre em contato com a Cielo."),
    Status24("24","Não foi possível processar a transação. Quantidade de parcelas inválido. Se o erro persistir, entre em contato com a Cielo."),
    Status25("25","Não foi possível processar a transação. Solicitação de autorização não enviou o número do cartão. Se o erro persistir, verifique a comunicação entre loja virtual e Cielo."),
    Status28("28","Não foi possível processar a transação. Arquivo temporariamente indisponível. Reveja a comunicação entre Loja Virtual e Cielo. Se o erro persistir, entre em contato com a Cielo."),
    Status30("30","Não foi possível processar a transação. Solicite ao portador que reveja os dados e tente novamente. Se o erro persistir verifique a comunicação com a Cielo esta sendo feita corretamente"),
    Status39("39","Transação não autorizada. Erro no banco emissor."),
    Status41("41","Transação não autorizada. Cartão bloqueado por perda.", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status43("43","Transação não autorizada. Cartão bloqueado por roubo.", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status51("51","Transação não autorizada. Limite excedido/sem saldo.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Status52("52","Não foi possível processar a transação. Cartão com dígito de controle inválido."),
    Status53("53","Transação não permitida. Cartão poupança inválido."),
    Status54("54","Transação não autorizada. Cartão vencido.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status55("55","Transação não autorizada. Senha inválida."),
    Status57("57","Transação não autorizada. Transação não permitida para o cartão.", OperacaoRetornoCobrancaEnum.REENVIAR),
    Status58("58","Transação não permitida. Opção de pagamento inválida. Reveja se a opção de pagamento escolhida está habilitada no cadastro"),
    Status59("59","Transação não autorizada. Suspeita de fraude."),
    Status60("60","Transação não autorizada. Tente novamente. Se o erro persistir o portador deve entrar em contato com o banco emissor."),
    Status61("61","Transação não autorizada. Banco emissor indisponível."),
    Status62("62","Transação não autorizada. Cartão restrito para uso doméstico.", OperacaoRetornoCobrancaEnum.REENVIAR),
    Status63("63","Transação não autorizada. Violação de segurança."),
    Status64("64","Transação não autorizada. Entre em contato com seu banco emissor."),
    Status65("65","Transação não autorizada. Excedida a quantidade de transações para o cartão."),
    Status67("67","Transação não autorizada. Cartão bloqueado para compras hoje. Bloqueio pode ter ocorrido por excesso de tentativas inválidas. O cartão será desbloqueado automaticamente à meia noite."),
    Status70("70","Transação não autorizada. Limite excedido/sem saldo.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Status72("72","Cancelamento não efetuado. Saldo disponível para cancelamento insuficiente. Se o erro persistir, entre em contato com a Cielo."),
    Status74("74","Transação não autorizada. A senha está vencida."),
    Status75("75","Transação não autorizada."),
    Status76("76","Cancelamento não efetuado. Banco emissor não localizou a transação original"),
    Status77("77","Pagamento não efetuado. Cartão cancelado.", CodigoRetornoPactoEnum.CARTAO_CANCELADO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status78("78","Transação não autorizada. Cartão bloqueado primeiro uso. Solicite ao portador que desbloqueie o cartão diretamente com seu banco emissor."),
    Status80("80","Transação não autorizada. Data da transação ou data do primeiro pagamento inválida."),
    Status82("82","Transação não autorizada. Cartão Inválido. Solicite ao portador que reveja os dados e tente novamente."),
    Status83("83","Transação não autorizada. Erro no controle de senhas"),
    Status85("85","Transação não permitida. Houve um erro no processamento.Solicite ao portador que digite novamente os dados do cartão, se o erro persistir pode haver um problema no terminal do lojista, nesse caso o lojista deve entrar em contato com a Cielo."),
    Status86("86","Transação não permitida. Houve um erro no processamento.Solicite ao portador que digite novamente os dados do cartão, se o erro persistir pode haver um problema no terminal do lojista, nesse caso o lojista deve entrar em contato com a Cielo."),
    Status89("89","Transação não autorizada. Erro na transação. O portador deve tentar novamente e se o erro persistir, entrar em contato com o banco emissor."),
    Status90("90","Transação não permitida. Houve um erro no processamento.Solicite ao portador que digite novamente os dados do cartão, se o erro persistir pode haver um problema no terminal do lojista, nesse caso o lojista deve entrar em contato com a Cielo."),
    Status91("91","Transação não autorizada. Banco emissor temporariamente indisponível."),
    Status92("92","Transação não autorizada. Tempo de comunicação excedido."),
    Status93("93","Transação não autorizada. Violação de regra - Possível erro no cadastro."),
    Status96("96","Não foi possível processar a transação. Falha no sistema da Cielo. Se o erro persistir, entre em contato com a Cielo."),
    Status97("97","Transação não autorizada. Valor não permitido para essa transação."),
    Status98("98","Transação não autorizada. Sistema do emissor sem comunicação. Se for geral, verificar SITEF, GATEWAY e/ou Conectividade."),
    Status99("99","Transação não autorizada. Sistema do emissor sem comunicação. Tente mais tarde. Pode ser erro no SITEF, favor verificar !"),
    Status146("146","Código de segurança informado incorreto! Excedeu o limite de 3 dígitos."),
    Status999("999","Transação não autorizada. Sistema do emissor sem comunicação. Tente mais tarde. Pode ser erro no SITEF, favor verificar !"),
    StatusAA("AA","Tempo excedido na comunicação com o banco emissor. Oriente o portador a tentar novamente, se o erro persistir será necessário que o portador contate seu banco emissor."),
    StatusAC("AC","Transação não permitida. Cartão de débito sendo usado com crédito. Solicite ao portador que selecione a opção de pagamento Cartão de Débito."),
    StatusAE("AE","Tempo excedido na comunicação com o banco emissor. Oriente o portador a tentar novamente, se o erro persistir será necessário que o portador contate seu banco emissor."),
    StatusAF("AF","Transação não permitida. Houve um erro no processamento.Solicite ao portador que digite novamente os dados do cartão, se o erro persistir pode haver um problema no terminal do lojista, nesse caso o lojista deve entrar em contato com a Cielo."),
    StatusAG("AG","Transação não permitida. Houve um erro no processamento.Solicite ao portador que digite novamente os dados do cartão, se o erro persistir pode haver um problema no terminal do lojista, nesse caso o lojista deve entrar em contato com a Cielo."),
    StatusAH("AH","Transação não permitida. Cartão de crédito sendo usado com débito. Solicite ao portador que selecione a opção de pagamento Cartão de Crédito."),
    StatusAI("AI","Transação não autorizada. Autenticação não foi realizada. O portador não concluiu a autenticação. Solicite ao portador que reveja os dados e tente novamente. Se o erro persistir, entre em contato com a Cielo informando o BIN (6 primeiros dígitos do cartão)"),
    StatusAJ("AJ","Transação não permitida. Transação de crédito ou débito em uma operação que permite apenas Private Label. Solicite ao portador que tente novamente selecionando a opção Private Label. Caso não disponibilize a opção Private Label verifique na Cielo se o seu estabelecimento permite essa operação."),
    StatusAV("AV","Falha na validação dos dados da transação. Oriente o portador a rever os dados e tentar novamente."),
    StatusBD("BD","Transação não permitida. Houve um erro no processamento.Solicite ao portador que digite novamente os dados do cartão, se o erro persistir pode haver um problema no terminal do lojista, nesse caso o lojista deve entrar em contato com a Cielo."),
    StatusBL("BL","Transação não autorizada. Limite diário excedido. Solicite ao portador que entre em contato com seu banco emissor."),
    StatusBM("BM","Transação não autorizada. Cartão inválido. Pode ser bloqueio do cartão no banco emissor ou dados incorretos."),
    StatusBN("BN","Transação não autorizada. O cartão ou a conta do portador está bloqueada. Solicite ao portador que entre em contato com seu banco emissor."),
    StatusBO("BO","Transação não permitida. Houve um erro no processamento. Solicite ao portador que digite novamente os dados do cartão, se o erro persistir, entre em contato com o banco emissor."),
    StatusBP("BP","Transação não autorizada. Não possível processar a transação por um erro relacionado ao cartão ou conta do portador. Solicite ao portador que entre em contato com o banco emissor."),
    StatusBV("BV","Transação não autorizada. Cartão vencido.", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    StatusCF("CF","Transação não autorizada. Falha na validação dos dados. Solicite ao portador que entre em contato com o banco emissor."),
    StatusCG("CG","Transação não autorizada. Falha na validação dos dados. Solicite ao portador que entre em contato com o banco emissor."),
    StatusDA("DA","Transação não autorizada. Falha na validação dos dados. Solicite ao portador que entre em contato com o banco emissor."),
    StatusDF("DF","Transação não permitida. Falha no cartão ou cartão inválido. Solicite ao portador que digite novamente os dados do cartão, se o erro persistir, entre em contato com o banco"),
    StatusDM("DM","Transação não autorizada. Limite excedido/sem saldo.", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    StatusDQ("DQ","Transação não autorizada. Falha na validação dos dados. Solicite ao portador que entre em contato com o banco emissor."),
    StatusDS("DS","Transação não autorizada. Transação não permitida para o cartão."),
    StatusEB("EB","Transação não autorizada. Limite diário excedido. Solicite ao portador que entre em contato com seu banco emissor."),
    StatusEE("EE","Transação não permitida. Valor da parcela inferior ao mínimo permitido. Não é permitido parcelas inferiores a R$ 5,00. Necessário rever calculo para parcelas."),
    StatusEK("EK","Transação não autorizada. Transação não permitida para o cartão."),
    StatusFA("FA","Transação não autorizada AmEx."),
    StatusFC("FC","Transação não autorizada. Oriente o portador a entrar em contato com o banco emissor."),
    StatusFD("FD","Transação não autorizada por regras do banco emissor."),
    StatusFE("FE","Transação não autorizada. Data da transação ou data do primeiro pagamento inválida."),
    StatusFF("FF","Transação de cancelamento autorizada com sucesso. ATENÇÂO: Esse retorno é para casos de cancelamentos e não para casos de autorizações."),
    StatusFG("FG","Transação não autorizada. Oriente o portador a entrar em contato com a Central de Atendimento AmEx."),
    StatusGA("GA","Transação não autorizada. Referida pelo Lynx Online de forma preventiva. A Cielo entrará em contato com o lojista sobre esse caso."),
    StatusHJ("HJ","Transação não permitida. Código da operação Coban inválido."),
    StatusIA("IA","Transação não permitida. Indicador da operação Coban inválido."),
    StatusJB("JB","Transação não permitida. Valor da operação Coban inválido."),
    StatusKA("KA","Transação não permitida. Houve uma falha na validação dos dados. Solicite ao portador que reveja os dados e tente novamente. Se o erro persistir verifique a comunicação entre loja virtual e Cielo."),
    StatusKB("KB","Transação não permitida. Selecionado a opção incorreta. Solicite ao portador que reveja os dados e tente novamente. Se o erro persistir deve ser verificado a comunicação entre loja virtual e Cielo."),
    StatusKE("KE","Transação não autorizada. Falha na validação dos dados. Opção selecionada não está habilitada. Verifique as opções disponíveis para o portador."),
    StatusN7("N7","Transação não autorizada. Código de segurança inválido. Oriente o portador corrigir os dados e tentar novamente."),
    StatusR1("R1","Transação não autorizada. Não foi possível processar a transação. Questão relacionada a segurança, inadimplencia ou limite do portador."),
    StatusU3("U3","Transação não permitida. Houve uma falha na validação dos dados. Solicite ao portador que reveja os dados e tente novamente. Se o erro persistir verifique a comunicação entre loja virtual e Cielo."),
    StatusGD("GD","Transação não permitida. Entre em contato com a Cielo."),
    StatusGT("GT","Transação negada. Ataque de força bruta, entre em contato com a Cielo");

    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private CieloECommerceRetornoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private CieloECommerceRetornoEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private CieloECommerceRetornoEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static CieloECommerceRetornoEnum valueOff(String id) {
        CieloECommerceRetornoEnum[] values = CieloECommerceRetornoEnum.values();
        for (CieloECommerceRetornoEnum eDIStatusEnum : values) {
            if (eDIStatusEnum.getId().equals(id)) {
                return eDIStatusEnum;
            }
        }
        return StatusNENHUM;
    }

    public static String obterCodigosRetorno(CodigoRetornoPactoEnum retornoPacto) {
        StringBuilder retorno = new StringBuilder();
        for (CieloECommerceRetornoEnum obj : CieloECommerceRetornoEnum.values()) {
            if (obj.getCodigoRetornoPacto().equals(retornoPacto)) {
                retorno.append("'").append(obj.getId()).append("'").append(",");
            }
        }
        if (!UteisValidacao.emptyString(retorno.toString())) {
            retorno.deleteCharAt(retorno.length() - 1);
        }
        return retorno.toString();
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}
