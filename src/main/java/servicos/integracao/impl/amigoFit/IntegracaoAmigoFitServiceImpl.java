package servicos.integracao.impl.amigoFit;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.crm.IndicadoVO;
import negocio.comuns.utilitarias.Uteis;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.integracao.interfaces.amigoFit.IntegracaoAmigoFitServiceInterface;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IntegracaoAmigoFitServiceImpl extends SuperServico implements IntegracaoAmigoFitServiceInterface {

    private static final String UTF_8 = "UTF-8";
    private static final String urlAmigoFit = "https://amigofit-server.herokuapp.com/";

    public IntegracaoAmigoFitServiceImpl(Connection con) throws Exception {
        super(con);
    }

    @Override
    public String cadastrarIndicacao(JSONObject json, String token) throws Exception {

        String urlIndicados = urlAmigoFit+"indicados/";
        Map<String, String> m = new HashMap<>();
        m.put("Content-Type", "application/json");
        m.put("Authorization", token);
        String retornoRequest = ExecuteRequestHttpService.executeHttpRequest(urlIndicados, json.toString(), m, "POST", "UTF-8");
        Uteis.logar(retornoRequest);
        String retornoR ="";
        if (retornoRequest.contains("{\"success\":false,")){
            if (retornoRequest.equals("{\"success\":false,\"error\":\"Já existe um indicado com o mesmo: cpf\"}")) {
                throw new Exception("CPF já cadastrado");
            }else{
                throw new Exception("Erro ao cadastrar indicação no sistema AmigoFit.");
            }
        }else if (!retornoRequest.contains("{\"success\":false,\"error\":")){
            return retornoRequest;
        }
        return retornoR;
    }

    public String cadastrarCliente(JSONObject json) throws Exception {

        String urlLogin = "register/";
        Map<String, String> m = new HashMap<String, String>();
        m.put("Content-Type", "application/json");
        try {
            String retornoRequest = ExecuteRequestHttpService.executeHttpRequest(urlAmigoFit+urlLogin, json.toString(), m, "POST", "UTF-8");
            if (retornoRequest.contains("cliente_id")) {
                return retornoRequest;
            }else if (retornoRequest.contains("success\":false")){
                JSONObject obj = new JSONObject(retornoRequest.toString());
                throw new Exception("Erro ao cadastrar Aluno: "+obj.getString("error"));
            }
            return retornoRequest;
        }catch (Exception e ){
            return e.getMessage();
        }
    }

    public String loginAmigoFit(String usernameEmpresa,String senhaAmigoFitEmpresa) throws Exception {
        JSONObject json = new JSONObject();
        json.put("username",usernameEmpresa);
        json.put("password",senhaAmigoFitEmpresa);
        String urlLogin = "login/";
        Map<String, String> m = new HashMap<>();
        m.put("Content-Type", "application/json");
        String retornoRequest = ExecuteRequestHttpService.executeHttpRequest(urlAmigoFit+urlLogin, json.toString(), m, "POST", "UTF-8");
        Uteis.logar(retornoRequest);
        if (retornoRequest.contains("{\"id\":")){
            return retornoRequest;
        }else {
            return "Erro ao Realizar Login. \n Cheque suas credenciais AmigoFit.";
        }
    }

    public static void main(String[] args1) {
        String[] args = {"teste"};

//        if (args.length > 0) {

            try {
                Connection con;
//                String retornoLogin = consultarUsuarioLogado("consultarUsuarioLogado");
//                JSONArray jsonArray = new JSONArray(retornoLogin);
//                JSONObject jsonObject = new JSONObject(jsonArray.getJSONObject(0));
//                int cliente = jsonArray.getJSONObject(0).getInt("cliente");


            } catch (Exception e) {
                e.printStackTrace();
            }
    }


    public String consultarUsuarioLogado(String token) throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        DefaultHttpClient httpClient = new DefaultHttpClient();
        String apiOutput = "";
        try
        {
            HttpGet getRequest = new HttpGet("https://academia-servidor.herokuapp.com/api/user");

            getRequest.addHeader("Content-Type", "application/json");
            getRequest.addHeader("Authorization", "Token "+token);

            HttpResponse response = httpClient.execute(getRequest);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != 200){
                throw new RuntimeException("Failed with HTTP error code : " + statusCode);
            }

            HttpEntity httpEntity = response.getEntity();
            apiOutput = EntityUtils.toString(httpEntity);

        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally
        {
            //Important: Close the connect
            httpClient.getConnectionManager().shutdown();
        }
        return apiOutput;
    }

    public List<IndicadoVO> consultarIndicacoesAmigoFit(ClienteVO clienteVO) throws Exception {
        JSONObject jsonObject = new JSONObject(loginAmigoFit(clienteVO.getUsernameAmigoFit(),clienteVO.getSenhaUsuarioAmigoFit()));
        String token = jsonObject.getString("accessToken");
        HttpClient httpclient = ExecuteRequestHttpService.createConnector();
        HttpGet httpget = new HttpGet(urlAmigoFit+"indicados");
        httpget.addHeader("Content-Type", "application/json");
        httpget.addHeader("Authorization", "Bearer " + token);

        HttpResponse response = httpclient.execute(httpget);
        String responseR = null;
        HttpEntity entity = response.getEntity();
        if(entity!=null) {
            responseR = EntityUtils.toString(entity);
        }
        List<IndicadoVO> indicadoVOS = new ArrayList<IndicadoVO>();
        if (responseR.contains("{\"success\":false")){
            throw new Exception("Erro ao consultar Lista de Indicados do Cliente");
        }else if (!responseR.contains("{\"success\":false,\"error\":")){
            JSONArray jsonArray = new JSONArray(responseR.toString());

            for(int i = 0; i < jsonArray.length(); i++){
                JSONObject o = jsonArray.getJSONObject(i);
                IndicadoVO indicadoVO = new IndicadoVO();
                indicadoVO.setCpf( o.getString("cpf").replace("{\"","").replace("\"}",""));
                indicadoVO.setNomeIndicado( o.getString("nome").replace("{\"","").replace("\"}",""));
                indicadoVO.setTelefone( o.getString("contato").replace("{\"","").replace("\"}",""));
                Instant dataLancamento = Instant.parse(o.getString("indicado_at"));
                Date dateLanc = Date.from(dataLancamento);
                indicadoVO.setDataLancamento(dateLanc);

                indicadoVOS.add(indicadoVO);
            }

            return indicadoVOS;
        }
        return indicadoVOS;

    }

    public String cadastrarPagamentoCliente(JSONObject json, String token) throws Exception{

        String urlPagamento = urlAmigoFit+"admin/pagamentos/";
        Map<String, String> m = new HashMap<>();
        m.put("Content-Type", "application/json");
        m.put("Authorization", "Bearer " + token);
        String retornoRequest = ExecuteRequestHttpService.executeHttpRequest(urlPagamento, json.toString(), m, "POST", "UTF-8");
        Uteis.logar(retornoRequest);
        String retornoR ="";
        return retornoR;
    }
}
