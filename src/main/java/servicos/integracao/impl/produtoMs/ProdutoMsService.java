package servicos.integracao.impl.produtoMs;

import org.json.JSONObject;
import servicos.impl.autenticacaoMs.AutenticacaoMsService;
import servicos.impl.microsservice.SuperMSService;
import servicos.util.ExecuteRequestHttpService;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProdutoMsService extends SuperMSService {

    public static JSONObject replicarProduto(JSONObject produtoJSON, String urlProdutoMs, String chave, Integer codigoEmpresaDestino) throws Exception {
        String url = urlProdutoMs + "/produtos/replicar";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", codigoEmpresaDestino.toString());

        String response = null;
        try {
            response = ExecuteRequestHttpService.post(url, produtoJSON.toString(), headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            Logger.getLogger(ProdutoMsService.class.getName()).log(Level.SEVERE, "Retorno URL: " + url + " - Resposta: " +
                    response + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new ProdutoMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new ProdutoMsException(e.getMessage() + " - Response: " + response);
            }
        }
    }

    public static JSONObject clonarProduto(Integer codigo, String urlProdutoMs, String chave) throws Exception {
        String url = urlProdutoMs + "/produtos/" + codigo + "/clonar";
        return get(chave, url);
    }

    public static String atualizarProdutoReplicado(JSONObject produtoJSON, String urlProdutoMs, String chave, Integer codigoEmpresaDestino) throws Exception {
        String url = urlProdutoMs + "/produtos/atualizarProdutoReplicado";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", codigoEmpresaDestino.toString());
        String result = null;

        try {
            result = ExecuteRequestHttpService.post(url, produtoJSON.toString(), headers);
            return "sucesso: " + result;
        } catch (Exception e) {
            Logger.getLogger(ProdutoMsService.class.getName()).log(Level.SEVERE, "Resposta: " +
                    result + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new ProdutoMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new ProdutoMsException(e.getMessage());
            }
        }
    }

    public static JSONObject obterProduto(Integer codigo, String urlProdutoMs, String chave) throws Exception {
        String url = urlProdutoMs + "/produtos/" + codigo;
        return get(chave, url);
    }

    private static JSONObject get(String chave, String url) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", AutenticacaoMsService.token(chave));
        headers.put("empresaId", "1");

        String response = null;
        try {
            response = ExecuteRequestHttpService.get(url, headers);
            return new JSONObject(response).getJSONObject("content");
        } catch (Exception e) {
            Logger.getLogger(ProdutoMsService.class.getName()).log(Level.SEVERE, "Retorno URL: " + url + " - Resposta: " +
                    response + " Mensagem: " + e.getMessage(), e);
            e.printStackTrace();
            try {
                throw new ProdutoMsException(messageError(e.getMessage()));
            } catch (Exception ex) {
                throw new ProdutoMsException(e.getMessage() + " - Response: " + response);
            }
        }
    }
}
