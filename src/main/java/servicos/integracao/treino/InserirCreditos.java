
package servicos.integracao.treino;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de inserirCreditos complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="inserirCreditos">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="colaborador" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="creditos" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="recibozw" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="dataExpiracao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "inserirCreditos", propOrder = {
    "key",
    "colaborador",
    "creditos",
    "recibozw",
    "dataExpiracao"
})
public class InserirCreditos {

    protected String key;
    protected Integer colaborador;
    protected Integer creditos;
    protected Integer recibozw;
    protected String dataExpiracao;

    /**
     * Obtém o valor da propriedade key.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Define o valor da propriedade key.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Obtém o valor da propriedade colaborador.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getColaborador() {
        return colaborador;
    }

    /**
     * Define o valor da propriedade colaborador.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setColaborador(Integer value) {
        this.colaborador = value;
    }

    /**
     * Obtém o valor da propriedade creditos.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCreditos() {
        return creditos;
    }

    /**
     * Define o valor da propriedade creditos.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCreditos(Integer value) {
        this.creditos = value;
    }

    /**
     * Obtém o valor da propriedade recibozw.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getRecibozw() {
        return recibozw;
    }

    /**
     * Define o valor da propriedade recibozw.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setRecibozw(Integer value) {
        this.recibozw = value;
    }

    /**
     * Obtém o valor da propriedade dataExpiracao.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataExpiracao() {
        return dataExpiracao;
    }

    /**
     * Define o valor da propriedade dataExpiracao.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataExpiracao(String value) {
        this.dataExpiracao = value;
    }

}
