package servicos.integracao.pjbank.enums;

public enum StatusTransacao {
    APROVADA("Aprovada"), PENDENTE("Pendente"), RECUSADA("Recusada");

    private String name;

    StatusTransacao(String name) {
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public static StatusTransacao fromString(String name) {
        for (StatusTransacao obj : StatusTransacao.values()) {
            if (obj.name.equalsIgnoreCase(name)) {
                return obj;
            }
        }
        return null;
    }
}
