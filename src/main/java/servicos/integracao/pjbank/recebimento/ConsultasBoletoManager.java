package servicos.integracao.pjbank.recebimento;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.integracao.pjbank.PJBankAuthenticatedService.PJBankAuthenticatedService;
import servicos.integracao.pjbank.api.PJBankClient;
import servicos.integracao.pjbank.exceptions.PJBankException;

import java.io.IOException;

public class ConsultasBoletoManager extends PJBankAuthenticatedService {
    /**
     * EndPoint a ser requisitado na API
     */
    private String endPoint = "recebimentos/{{credencial}}";
    private ConvenioCobrancaVO convenioCobrancaVO;

    public ConsultasBoletoManager(String credencial, String chave, ConvenioCobrancaVO convenioCobrancaVO) {
        super(credencial, chave);
        this.convenioCobrancaVO = convenioCobrancaVO;
        this.endPoint = this.endPoint.replace("{{credencial}}", credencial);
    }

    /**
     * Consulta um boleto baseado no seu idUnico.
     * @return informações do boleto
     */
    public String infoBoleto(String idUnico) throws IOException, PJBankException, ConsistirException {
        String linkInfoBoleto = "";
        PJBankClient client = new PJBankClient(this.endPoint.concat("/transacoes/" + idUnico), this.convenioCobrancaVO.getAmbiente());
        HttpGet httpGet = client.getHttpGetClient();
        httpGet.addHeader("x-chave", this.chave);

        String response = EntityUtils.toString(client.doRequest(httpGet).getEntity(), "UTF-8");
        tratarErroRequisicao(response);

        JSONArray jsonArray = new JSONArray(response);
        for (int i = 0; i < jsonArray.length() ; i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            linkInfoBoleto = jsonObject.getString("link_info");
            break;
        }

        return linkInfoBoleto;
    }

    private void tratarErroRequisicao(String response) throws ConsistirException {

        //Quando dá erro vem JSONObject. Quando dá sucesso vem JSONArray
        boolean erro = false;
        String msgErro = "";
        try {
            JSONObject jsonErro = new JSONObject(response);
            if (jsonErro.optString("status").equals("400") && !UteisValidacao.emptyString(jsonErro.optString("msg"))) {
                erro = true;
                msgErro = jsonErro.optString("msg");
            }
        } catch (Exception e) {
            //ignore
        }

        try {
            JSONArray jsonArray = new JSONArray(response);
            if (jsonArray.length() == 0) {
                erro = true;
                msgErro = "Não foi possível obter as informações do boleto. Talvez ele já esteja cancelado. Entre em contato com a Pacto.";
            }
        } catch (Exception e) {
            //ignore
        }

        if (erro) {
            throw new ConsistirException(msgErro);
        }
    }
}

