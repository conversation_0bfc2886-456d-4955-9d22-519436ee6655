package servicos.integracao.pjbank.PJBankAuthenticatedService;

import org.apache.commons.lang3.StringUtils;

public class PJBankAuthenticatedService {
    protected String credencial;
    protected String chave;

    public PJBankAuthenticatedService(String credencial, String chave) {
        if (StringUtils.isBlank(credencial))
            throw new IllegalArgumentException("Credencial não informada");

        if (StringUtils.isBlank(chave))
            throw new IllegalArgumentException("Chave não informada");

        this.credencial = credencial;
        this.chave = chave;
    }

    public String getCredencial() {
        return credencial;
    }

    public void setCredencial(String credencial) {
        this.credencial = credencial;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }
}

