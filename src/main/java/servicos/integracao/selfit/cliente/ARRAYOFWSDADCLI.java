
package servicos.integracao.selfit.cliente;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ARRAYOFWSDADCLI complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ARRAYOFWSDADCLI">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="WSDADCLI" type="{http://54.207.46.24:91/}WSDADCLI" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ARRAYOFWSDADCLI", propOrder = {
    "wsdadcli"
})
public class ARRAYOFWSDADCLI {

    @XmlElement(name = "WSDADCLI")
    protected List<WSDADCLI> wsdadcli;

    /**
     * Gets the value of the wsdadcli property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the wsdadcli property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWSDADCLI().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WSDADCLI }
     * 
     * 
     */
    public List<WSDADCLI> getWSDADCLI() {
        if (wsdadcli == null) {
            wsdadcli = new ArrayList<WSDADCLI>();
        }
        return this.wsdadcli;
    }

}
