
package servicos.integracao.selfit.notafiscal;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * Servico de Gravacao Notas Fiscais
 * 
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "WSSELFNF", targetNamespace = "http://54.207.46.24:91/", wsdlLocation = "http://54.207.46.24:91/ws/WSSELFNF.apw?WSDL")
public class WSSELFNF
    extends Service
{

    private final static URL WSSELFNF_WSDL_LOCATION;
    private final static WebServiceException WSSELFNF_EXCEPTION;
    private final static QName WSSELFNF_QNAME = new QName("http://54.207.46.24:91/", "WSSELFNF");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://54.207.46.24:91/ws/WSSELFNF.apw?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WSSELFNF_WSDL_LOCATION = url;
        WSSELFNF_EXCEPTION = e;
    }

    public WSSELFNF() {
        super(__getWsdlLocation(), WSSELFNF_QNAME);
    }

    public WSSELFNF(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns WSSELFNFSOAP
     */
    @WebEndpoint(name = "WSSELFNFSOAP")
    public WSSELFNFSOAP getWSSELFNFSOAP() {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFNFSOAP"), WSSELFNFSOAP.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns WSSELFNFSOAP
     */
    @WebEndpoint(name = "WSSELFNFSOAP")
    public WSSELFNFSOAP getWSSELFNFSOAP(WebServiceFeature... features) {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFNFSOAP"), WSSELFNFSOAP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (WSSELFNF_EXCEPTION!= null) {
            throw WSSELFNF_EXCEPTION;
        }
        return WSSELFNF_WSDL_LOCATION;
    }

}
