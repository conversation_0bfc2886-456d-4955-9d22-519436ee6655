
package servicos.integracao.selfit.notafiscal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WSDADNFISC complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WSDADNFISC">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="NITEMNF" type="{http://54.207.46.24:91/}ARRAYOFWSDADITNF"/>
 *         &lt;element name="Z4_CGC" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_CHVNFE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_CODNFE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_DAUTNFE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_DTCANC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_EMINFE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_EMISSAO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_ESPECI1" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_HAUTNFE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_HORNFE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_MATRICU" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_MDCONTR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_MENNOTA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Z4_NFELETR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_NOTA" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_OBSENF" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_PROTOC" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_SERIE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_STATNF" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="Z4_TIPOROT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WSDADNFISC", propOrder = {
    "nitemnf",
    "z4CGC",
    "z4CHVNFE",
    "z4CODNFE",
    "z4DAUTNFE",
    "z4DTCANC",
    "z4EMINFE",
    "z4EMISSAO",
    "z4ESPECI1",
    "z4HAUTNFE",
    "z4HORNFE",
    "z4MATRICU",
    "z4MDCONTR",
    "z4MENNOTA",
    "z4NFELETR",
    "z4NOTA",
    "z4OBSENF",
    "z4PROTOC",
    "z4SERIE",
    "z4STATNF",
    "z4TIPOROT"
})
public class WSDADNFISC {

    @XmlElement(name = "NITEMNF", required = true)
    protected ARRAYOFWSDADITNF nitemnf;
    @XmlElement(name = "Z4_CGC", required = true)
    protected String z4CGC;
    @XmlElement(name = "Z4_CHVNFE", required = true)
    protected String z4CHVNFE;
    @XmlElement(name = "Z4_CODNFE", required = true)
    protected String z4CODNFE;
    @XmlElement(name = "Z4_DAUTNFE", required = true)
    protected String z4DAUTNFE;
    @XmlElement(name = "Z4_DTCANC")
    protected String z4DTCANC;
    @XmlElement(name = "Z4_EMINFE", required = true)
    protected String z4EMINFE;
    @XmlElement(name = "Z4_EMISSAO", required = true)
    protected String z4EMISSAO;
    @XmlElement(name = "Z4_ESPECI1")
    protected String z4ESPECI1;
    @XmlElement(name = "Z4_HAUTNFE", required = true)
    protected String z4HAUTNFE;
    @XmlElement(name = "Z4_HORNFE", required = true)
    protected String z4HORNFE;
    @XmlElement(name = "Z4_MATRICU", required = true)
    protected String z4MATRICU;
    @XmlElement(name = "Z4_MDCONTR", required = true)
    protected String z4MDCONTR;
    @XmlElement(name = "Z4_MENNOTA")
    protected String z4MENNOTA;
    @XmlElement(name = "Z4_NFELETR", required = true)
    protected String z4NFELETR;
    @XmlElement(name = "Z4_NOTA", required = true)
    protected String z4NOTA;
    @XmlElement(name = "Z4_OBSENF", required = true)
    protected String z4OBSENF;
    @XmlElement(name = "Z4_PROTOC", required = true)
    protected String z4PROTOC;
    @XmlElement(name = "Z4_SERIE", required = true)
    protected String z4SERIE;
    @XmlElement(name = "Z4_STATNF", required = true)
    protected String z4STATNF;
    @XmlElement(name = "Z4_TIPOROT", required = true)
    protected String z4TIPOROT;

    /**
     * Gets the value of the nitemnf property.
     * 
     * @return
     *     possible object is
     *     {@link ARRAYOFWSDADITNF }
     *     
     */
    public ARRAYOFWSDADITNF getNITEMNF() {
        return nitemnf;
    }

    /**
     * Sets the value of the nitemnf property.
     * 
     * @param value
     *     allowed object is
     *     {@link ARRAYOFWSDADITNF }
     *     
     */
    public void setNITEMNF(ARRAYOFWSDADITNF value) {
        this.nitemnf = value;
    }

    /**
     * Gets the value of the z4CGC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4CGC() {
        return z4CGC;
    }

    /**
     * Sets the value of the z4CGC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4CGC(String value) {
        this.z4CGC = value;
    }

    /**
     * Gets the value of the z4CHVNFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4CHVNFE() {
        return z4CHVNFE;
    }

    /**
     * Sets the value of the z4CHVNFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4CHVNFE(String value) {
        this.z4CHVNFE = value;
    }

    /**
     * Gets the value of the z4CODNFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4CODNFE() {
        return z4CODNFE;
    }

    /**
     * Sets the value of the z4CODNFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4CODNFE(String value) {
        this.z4CODNFE = value;
    }

    /**
     * Gets the value of the z4DAUTNFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4DAUTNFE() {
        return z4DAUTNFE;
    }

    /**
     * Sets the value of the z4DAUTNFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4DAUTNFE(String value) {
        this.z4DAUTNFE = value;
    }

    /**
     * Gets the value of the z4DTCANC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4DTCANC() {
        return z4DTCANC;
    }

    /**
     * Sets the value of the z4DTCANC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4DTCANC(String value) {
        this.z4DTCANC = value;
    }

    /**
     * Gets the value of the z4EMINFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4EMINFE() {
        return z4EMINFE;
    }

    /**
     * Sets the value of the z4EMINFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4EMINFE(String value) {
        this.z4EMINFE = value;
    }

    /**
     * Gets the value of the z4EMISSAO property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4EMISSAO() {
        return z4EMISSAO;
    }

    /**
     * Sets the value of the z4EMISSAO property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4EMISSAO(String value) {
        this.z4EMISSAO = value;
    }

    /**
     * Gets the value of the z4ESPECI1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4ESPECI1() {
        return z4ESPECI1;
    }

    /**
     * Sets the value of the z4ESPECI1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4ESPECI1(String value) {
        this.z4ESPECI1 = value;
    }

    /**
     * Gets the value of the z4HAUTNFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4HAUTNFE() {
        return z4HAUTNFE;
    }

    /**
     * Sets the value of the z4HAUTNFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4HAUTNFE(String value) {
        this.z4HAUTNFE = value;
    }

    /**
     * Gets the value of the z4HORNFE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4HORNFE() {
        return z4HORNFE;
    }

    /**
     * Sets the value of the z4HORNFE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4HORNFE(String value) {
        this.z4HORNFE = value;
    }

    /**
     * Gets the value of the z4MATRICU property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4MATRICU() {
        return z4MATRICU;
    }

    /**
     * Sets the value of the z4MATRICU property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4MATRICU(String value) {
        this.z4MATRICU = value;
    }

    /**
     * Gets the value of the z4MDCONTR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4MDCONTR() {
        return z4MDCONTR;
    }

    /**
     * Sets the value of the z4MDCONTR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4MDCONTR(String value) {
        this.z4MDCONTR = value;
    }

    /**
     * Gets the value of the z4MENNOTA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4MENNOTA() {
        return z4MENNOTA;
    }

    /**
     * Sets the value of the z4MENNOTA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4MENNOTA(String value) {
        this.z4MENNOTA = value;
    }

    /**
     * Gets the value of the z4NFELETR property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4NFELETR() {
        return z4NFELETR;
    }

    /**
     * Sets the value of the z4NFELETR property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4NFELETR(String value) {
        this.z4NFELETR = value;
    }

    /**
     * Gets the value of the z4NOTA property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4NOTA() {
        return z4NOTA;
    }

    /**
     * Sets the value of the z4NOTA property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4NOTA(String value) {
        this.z4NOTA = value;
    }

    /**
     * Gets the value of the z4OBSENF property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4OBSENF() {
        return z4OBSENF;
    }

    /**
     * Sets the value of the z4OBSENF property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4OBSENF(String value) {
        this.z4OBSENF = value;
    }

    /**
     * Gets the value of the z4PROTOC property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4PROTOC() {
        return z4PROTOC;
    }

    /**
     * Sets the value of the z4PROTOC property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4PROTOC(String value) {
        this.z4PROTOC = value;
    }

    /**
     * Gets the value of the z4SERIE property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4SERIE() {
        return z4SERIE;
    }

    /**
     * Sets the value of the z4SERIE property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4SERIE(String value) {
        this.z4SERIE = value;
    }

    /**
     * Gets the value of the z4STATNF property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4STATNF() {
        return z4STATNF;
    }

    /**
     * Sets the value of the z4STATNF property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4STATNF(String value) {
        this.z4STATNF = value;
    }

    /**
     * Gets the value of the z4TIPOROT property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZ4TIPOROT() {
        return z4TIPOROT;
    }

    /**
     * Sets the value of the z4TIPOROT property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZ4TIPOROT(String value) {
        this.z4TIPOROT = value;
    }

}
