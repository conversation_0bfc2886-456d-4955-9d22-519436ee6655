
package servicos.integracao.selfit.titulooperadora;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * Servico de Gravacao Titulos/Substituição Operadora
 * 
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "WSSELFOP", targetNamespace = "http://54.207.46.24:91/", wsdlLocation = "http://54.207.46.24:91/ws/WSSELFOP.apw?WSDL")
public class WSSELFOP
    extends Service
{

    private final static URL WSSELFOP_WSDL_LOCATION;
    private final static WebServiceException WSSELFOP_EXCEPTION;
    private final static QName WSSELFOP_QNAME = new QName("http://54.207.46.24:91/", "WSSELFOP");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://54.207.46.24:91/ws/WSSELFOP.apw?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WSSELFOP_WSDL_LOCATION = url;
        WSSELFOP_EXCEPTION = e;
    }

    public WSSELFOP() {
        super(__getWsdlLocation(), WSSELFOP_QNAME);
    }

    public WSSELFOP(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns WSSELFOPSOAP
     */
    @WebEndpoint(name = "WSSELFOPSOAP")
    public WSSELFOPSOAP getWSSELFOPSOAP() {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFOPSOAP"), WSSELFOPSOAP.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns WSSELFOPSOAP
     */
    @WebEndpoint(name = "WSSELFOPSOAP")
    public WSSELFOPSOAP getWSSELFOPSOAP(WebServiceFeature... features) {
        return super.getPort(new QName("http://54.207.46.24:91/", "WSSELFOPSOAP"), WSSELFOPSOAP.class, features);
    }

    private static URL __getWsdlLocation() {
        if (WSSELFOP_EXCEPTION!= null) {
            throw WSSELFOP_EXCEPTION;
        }
        return WSSELFOP_WSDL_LOCATION;
    }

}
