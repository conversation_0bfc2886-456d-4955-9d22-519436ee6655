/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package servicos.integracao;

import negocio.comuns.acesso.PessoaConsultaTO;
import negocio.comuns.basico.enumerador.TipoPessoaEnum;
import org.json.JSONArray;
import org.json.JSONException;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.handler.MessageContext;

import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.webservice.client.Exception;
import negocio.comuns.basico.webservice.client.IntegracaoCadastrosWS;
import negocio.comuns.basico.webservice.client.IntegracaoCadastrosWS_Service;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.util.ExecuteRequestHttpService;

import static br.com.pactosolucoes.controle.json.cadastro.OperacoesIntegracaoCadastroEnum.OBTER_CLIENTE_CODACESSO;
import static br.com.pactosolucoes.controle.json.cadastro.OperacoesIntegracaoCadastroEnum.OBTER_COLABORADOR_CODACESSO;

/**
 *
 * <AUTHOR>
 */
public class IntegracaoCadastrosWSConsumer {
    private static IntegracaoCadastrosWS servico;
    public static String urlServico;

    private static IntegracaoCadastrosWS getInstance(String urlServicoParam) {
        if (servico == null || UteisValidacao.emptyString(urlServico) || !urlServico.equals(urlServicoParam)){
            try {
                urlServico = urlServicoParam;
                URL u = new URL( urlServico+"/IntegracaoCadastrosWS?wsdl");
                QName qName = new QName("http://webservice.basico.comuns.negocio/", "IntegracaoCadastrosWS");
                servico = new IntegracaoCadastrosWS_Service(u, qName).getIntegracaoCadastrosWSPort();
                Map<String, Object> reqContext = ((BindingProvider) servico).getRequestContext();

                Map<String, List<String>> headers = new HashMap<>();
                headers.put("Authentication", Collections.singletonList("mkyong"));
                reqContext.put(MessageContext.HTTP_REQUEST_HEADERS, headers);

            } catch (MalformedURLException ex) {
                Logger.getLogger(IntegracaoCadastrosWSConsumer.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return servico;
    }

    
    public static List<EmpresaVO> getListaEmpresasRemotas(String url, String key) throws JSONException, Exception{
        String resultado = getInstance(url).consultarEmpresas(key);
        List<EmpresaVO> empresas = new ArrayList<EmpresaVO>();
        if(UteisValidacao.emptyString(resultado)){
         return empresas;
        }
        JSONArray mJsonArray = new JSONArray(resultado);
        
        for (int i = 0; i < mJsonArray.length(); i++) {
            empresas.add(new EmpresaVO(mJsonArray.getJSONObject(i)));
        }
        return empresas;
    }

    public static List<LocalAcessoVO> getListaLocaisAcesso(String url, String key, Integer empresa) throws JSONException, Exception{
        String resultado = getInstance(url).consultarLocaisAcesso(empresa, key);
        List<LocalAcessoVO> locais = new ArrayList<LocalAcessoVO>();
        if(UteisValidacao.emptyString(resultado)){
         return locais;
        }
        JSONArray mJsonArray = new JSONArray(resultado);
        for (int i = 0; i < mJsonArray.length(); i++) {
            locais.add(new LocalAcessoVO(mJsonArray.getJSONObject(i)));
        }
        return locais;
    }

    public static List<ColetorVO> getListaColetores(String url, String key, Integer localAcesso) throws JSONException, Exception{
        String resultado = getInstance(url).consultarColetores(key, localAcesso);
        List<ColetorVO> locais = new ArrayList<ColetorVO>();
        if(UteisValidacao.emptyString(resultado)){
         return locais;
        }
        JSONArray mJsonArray = new JSONArray(resultado);
        for (int i = 0; i < mJsonArray.length(); i++) {
            locais.add(new ColetorVO(mJsonArray.getJSONObject(i)));
        }
        return locais;
    }

    public static List<ClienteVO> getListaClientes(String url, String key, Integer empresa, String param) throws JSONException, Exception {
        String resultado = getInstance(url).consultarClientes(empresa, key, param);
        List<ClienteVO> clientes = new ArrayList<>();
        if (UteisValidacao.emptyString(resultado)) {
            return clientes;
        }
        JSONArray mJsonArray = new JSONArray(resultado);
        for (int i = 0; i < mJsonArray.length(); i++) {
            clientes.add(new ClienteVO(mJsonArray.getJSONObject(i)));
        }
        return clientes;
    }

    public static List<ColaboradorVO> getListaColaboradores(String url, String key, Integer empresa, String param) throws JSONException, Exception{
        String resultado = getInstance(url).consultarColaboradores(empresa, key, param);
        List<ColaboradorVO> colaboradores = new ArrayList<ColaboradorVO>();
        if(UteisValidacao.emptyString(resultado)){
         return colaboradores;
        }
        JSONArray mJsonArray = new JSONArray(resultado);
        for (int i = 0; i < mJsonArray.length(); i++) {
            colaboradores.add(new ColaboradorVO(mJsonArray.getJSONObject(i)));
        }
        return colaboradores;
    }

    public static String getListaClientesEstacionamentoSelfit(String url, String key, String listaPessoas) throws JSONException, Exception{
        return getInstance(url).listaClientesEstacionamentoSelfit(key, listaPessoas);
    }

    public static PessoaConsultaTO findByAccessCode(final String urlZillyonWeb, final String key, final String codAcesso, final TipoPessoaEnum tipoPessoaEnum) throws java.lang.Exception {
        String urlPost = urlZillyonWeb + "/prest/integracaoCadastro";

        Map<String, String> params = new HashMap<>();
        params.put("key", key);
        params.put("operacao", (tipoPessoaEnum == TipoPessoaEnum.ALUNO ? OBTER_CLIENTE_CODACESSO : OBTER_COLABORADOR_CODACESSO).toString());
        String codAcessoConsultar = codAcesso.startsWith("NU") ? codAcesso.replace("NU", "") : codAcesso;
        params.put("codAcesso", codAcessoConsultar);

        String response = ExecuteRequestHttpService.executeRequest(urlPost, params);

        JSONObject resposta = new JSONObject(response).optJSONObject("sucesso");
        if (resposta == null) {
            return null;
        }
        return new PessoaConsultaTO(resposta);
    }
}
