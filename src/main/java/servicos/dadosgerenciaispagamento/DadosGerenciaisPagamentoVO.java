package servicos.dadosgerenciaispagamento;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EmpresaVO;

import java.util.Date;

public class DadosGerenciaisPagamentoVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private EmpresaVO empresa;
    private Date mes;
    private Integer tipoCobrancaPacto;
    private Integer contratosAtivos;
    private boolean usaCobranca  = false;
    private boolean usaVendasOnline = false;
    private Double faturamento;
    private Double valorCreditoPacto;
    private Integer creditoUtilizado;
    private String jsonDados;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public Date getMes() {
        return mes;
    }

    public void setMes(Date mes) {
        this.mes = mes;
    }

    public Integer getTipoCobrancaPacto() {
        return tipoCobrancaPacto;
    }

    public void setTipoCobrancaPacto(Integer tipoCobrancaPacto) {
        this.tipoCobrancaPacto = tipoCobrancaPacto;
    }

    public boolean isUsaCobranca() {
        return usaCobranca;
    }

    public void setUsaCobranca(boolean usaCobranca) {
        this.usaCobranca = usaCobranca;
    }

    public boolean isUsaVendasOnline() {
        return usaVendasOnline;
    }

    public void setUsaVendasOnline(boolean usaVendasOnline) {
        this.usaVendasOnline = usaVendasOnline;
    }

    public Double getFaturamento() {
        if (faturamento == null) {
            faturamento = 0.0;
        }
        return faturamento;
    }

    public void setFaturamento(Double faturamento) {
        this.faturamento = faturamento;
    }

    public Double getValorCreditoPacto() {
        if (valorCreditoPacto == null) {
            valorCreditoPacto = 0.0;
        }
        return valorCreditoPacto;
    }

    public void setValorCreditoPacto(Double valorCreditoPacto) {
        this.valorCreditoPacto = valorCreditoPacto;
    }

    public Integer getCreditoUtilizado() {
        if (creditoUtilizado == null) {
            creditoUtilizado = 0;
        }
        return creditoUtilizado;
    }

    public void setCreditoUtilizado(Integer creditoUtilizado) {
        this.creditoUtilizado = creditoUtilizado;
    }

    public Integer getContratosAtivos() {
        if (contratosAtivos == null) {
            contratosAtivos = 0;
        }
        return contratosAtivos;
    }

    public void setContratosAtivos(Integer contratosAtivos) {
        this.contratosAtivos = contratosAtivos;
    }

    public String getJsonDados() {
        if (jsonDados == null) {
            jsonDados = "";
        }
        return jsonDados;
    }

    public void setJsonDados(String jsonDados) {
        this.jsonDados = jsonDados;
    }
}
