/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package servicos.operacoes;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.enumeradores.TipoPlanoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.contrato.ContratoComposicaoVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoPlanoProdutoSugeridoVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.ConvenioDescontoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;
import negocio.comuns.plano.*;
import negocio.comuns.plano.enumerador.ReferenciaValorModalidadeEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ColecaoUtils;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.plano.PlanoComposicao;
import negocio.facade.jdbc.plano.PlanoDuracaoCreditoTreino;
import negocio.facade.jdbc.plano.PlanoModalidade;
import negocio.facade.jdbc.plano.PlanoModalidadeVezesSemana;
import org.apache.commons.collections.Predicate;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.text.DecimalFormat;
import java.util.*;

import static controle.arquitetura.SuperControle.*;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

import java.sql.DriverManager;
import java.util.stream.Collectors;

import negocio.comuns.financeiro.MovParcelaVO;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class RenovacaoAutomaticaService {
    
    public RenovacaoAutomaticaService(Connection con) {
        this.con = con;
    }
    private ContratoVO contratoNovo;
    private ContratoVO contratoVelho;
    private ZillyonWebFacade zwFacade;
    private Contrato contratoDAO;
    private Connection con;
    private PlanoComposicao planoComposicaoDAO;
    private PlanoModalidade planoModalidadeDAO;
    private PlanoModalidadeVezesSemana planoModVezDAO;

    private PlanoDuracaoCreditoTreino planoDuracaoCreditoTreinoDAO;


    public ContratoVO renovarAutomatico(ContratoVO contrato, boolean simulacao) throws Exception {
        setContratoNovo(contrato);
        preencherContrato();
        adicionarPeriodoAcesso();
        getContratoNovo().obterDataFinalContrato(getContratoNovo().getVigenciaDe());
        if (!simulacao) {
            getZWFacade().getHorarioTurma().atualizarNrAlunosMatriculadosAulaDoContrato(getContratoNovo(), getContratoNovo().getVigenciaDe());
            getZWFacade().incluirContrato(getContratoNovo());
            ajustarVencimentoParcelasContratoPrePago();
            getZWFacade().atualizarSintetico(getZWFacade().getCliente().consultarPorCodigoPessoa(
                    getContratoNovo().getPessoa().getCodigo(),
                    Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA), Calendario.hoje(),
                    SituacaoClienteSinteticoEnum.GRUPO_TODOS, true);
            getZWFacade().gravarPontuacaoCliente(contrato);
            try {
                LogVO logVO = new LogVO();
                getContratoNovo().montarLogDadosContrato(logVO, null, null);
                logVO.setOperacao("INCLUSÃO DE CONTRATO");
                registrarLogObjetoVO(logVO, getContratoNovo().getPessoa().getCodigo());
            } catch (Exception e) {
                registrarLogErroObjetoVO("CONTRATO", getContratoNovo().getPessoa().getCodigo(), "ERRO AO GERAR LOG DE INCLUSÃO CONTRATO", getContratoNovo().getResponsavelContrato().getNome(), "");
                Uteis.logar(e, RenovacaoAutomaticaRecorrenciaService.class);
            }
        }
        return getContratoNovo();
    }

    
    private void preencherContrato() throws Exception {
        if (!UteisValidacao.emptyNumber(getContratoNovo().getPlano().getPlanoDiferenteRenovacao())) {// se plano diferente e plano recorrente e nao for plano credito ou turma
            setContratoVelho((ContratoVO) getContratoNovo().getClone(true));
            getContratoNovo().setPlano(getZWFacade().getPlano().consultarPorChavePrimaria(getContratoNovo().getPlano().getPlanoDiferenteRenovacao(), Uteis.NIVELMONTARDADOS_TODOS));
            if(!getContratoNovo().getPlano().isVendaCreditoTreino() &&
                    getContratoNovo().getPlano().getPlanoModalidadeVOs().stream().noneMatch(pM -> pM.getModalidade().getUtilizarTurma())){
                adicionarConfigsPlanoDiferente(getContratoNovo().getCodigo());
            }
        } else {
            adicionarConfigsPlano(getContratoNovo().getCodigo());
        }
        getContratoNovo().setResponsavelContrato(getZWFacade().getUsuarioRecorrencia());
        getContratoNovo().setUsuarioVO(getContratoNovo().getResponsavelContrato());
        getContratoNovo().setSomaProduto(0.0);
        getContratoNovo().setImportacao(false);
        getContratoNovo().setDataAlteracaoManual(null);
        preencherDatas();
        if (getContratoVelho() != null && !UteisValidacao.emptyNumber(getContratoVelho().getPlano().getPlanoDiferenteRenovacao()) && !getContratoNovo().getPlano().isVendaCreditoTreino() &&
                getContratoNovo().getPlano().getPlanoModalidadeVOs().stream().noneMatch(pM -> pM.getModalidade().getUtilizarTurma())) { // se plano diferente e plano recorrente e nao for plano credito ou turma
            preencherContratoModalidadePlanoDiferente();
        } else {
            removerModalidadesInativas();
        }
        preencherComposicao();
        preencherModalidadeVezesNaSemana();
        preencherPlanoDuracaoCredito();
        validarConvenioEDescontos();
        getContratoNovo().setNumeroCupomDesconto(null);
        if (!getContratoNovo().getPlano().getRenovarAutomaticamenteUtilizandoValorBaseContrato()) {
            getContratoDAO().calcularValorContrato(getContratoNovo(), true, true);
        } else {
            getContratoNovo().setValorBaseCalculo(getContratoNovo().getValorBaseNegociado());
        }
        if(!getContratoNovo().getPlano().getPermitePagarComBoleto()){ // caso desabilitado ,vai desabilidar no novo contrato. Caso contrário, vai propagar o que vem do contrato anterior
            getContratoNovo().setPagarComBoleto(false);
        }
        Calendar dataInicioContrato = Calendario.getInstance();
        dataInicioContrato.setTime(getContratoNovo().getVigenciaDe());

        IndiceFinanceiroReajustePrecoVO indiceFinanceiroVO = getFacade().getIndiceFinanceiroReajustePreco().consultarIndiceFinanceiroPeriodo(
                (new DecimalFormat("00")).format(dataInicioContrato.get(Calendar.MONTH) + 1), String.valueOf(dataInicioContrato.get(Calendar.YEAR)), true, TipoPlanoEnum.PLANO_NORMAL,
                false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        // IGPM para Contrato Normal
        if (indiceFinanceiroVO!=null) {
            double valorNovoBase = getContratoNovo().getValorBaseNegociado();
            if (UteisValidacao.notEmptyNumber(indiceFinanceiroVO.getPercentualAcumulado())) {
                valorNovoBase = valorNovoBase + Uteis.arredondarForcando2CasasDecimais(valorNovoBase * indiceFinanceiroVO.getPercentualAcumulado() / 100);
            }
            double valorNovoComProduto = valorNovoBase + (getContratoNovo().getValorFinal() - getContratoNovo().getValorBaseCalculo());
            getContratoNovo().setValorBaseCalculo(valorNovoBase);
            getContratoNovo().setValorFinal(valorNovoComProduto);
        }
        getZWFacade().inicializarMovProdutoContrato(getContratoNovo());
        adicionarProdutosObrigatorios();
        getContratoNovo().setValorFinal(Uteis.arredondarForcando2CasasDecimais(getContratoNovo().getValorBaseCalculo() + getContratoNovo().getSomaProduto()));
        setarHistorico();
        inicializarValoresDuracaoPlanoCreditoTreino();
    }

    private void preencherContratoModalidadePlanoDiferente() {
        getContratoNovo().setContratoModalidadeVOs(new ArrayList<>());
        String[] partes = getContratoVelho().getPlano().getModalidadesPlanoDiferenteRenovacao().split(",\\s*");
        List<Integer> arrayNumeros = Arrays.stream(partes).map(Integer::parseInt).collect(Collectors.toList());
        getContratoNovo().getPlano().getPlanoModalidadeVOs().forEach(pM -> {
            arrayNumeros.forEach(a -> {
                ContratoModalidadeVO contratoModalidadeVO = new ContratoModalidadeVO();
                contratoModalidadeVO.setModalidade(pM.getModalidade());
                if (pM.getModalidade().getCodigo().equals(a)) {
                    getContratoNovo().getContratoModalidadeVOs().add(contratoModalidadeVO);
                }
            });
        });
    }

    private void preencherPlanoDuracaoCredito() throws Exception {
        if ((this.getContratoNovo().getPlano().isVendaCreditoTreino()) && !this.getContratoNovo().getPlano().isCreditoSessao() ) {
            PlanoDuracaoCreditoTreinoVO planoDuracaoCreditoTreinoSelecionado =  getPlanoDuracaoCreditoTreinoDAO().consultarPorPlanoDuracao(getContratoNovo().getPlano().getCodigo(), getContratoNovo().getContratoDuracao().getNumeroMeses(), getContratoNovo().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum().getCodigo(), getContratoNovo().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getNumeroVezesSemana(), getContratoNovo().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getQuantidadeCreditoCompra(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if(planoDuracaoCreditoTreinoSelecionado == null ){
                throw new Exception("Contrato de crédito sem duração compativel");
            }
            getContratoNovo().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().setValorUnitario(planoDuracaoCreditoTreinoSelecionado.getValorUnitario());
            int totalModalidadesEscolhida = 0;
            for (ContratoModalidadeVO contratoModalidadeVO : this.getContratoNovo().getContratoModalidadeVOs()) {
                if (contratoModalidadeVO.getModalidade().getModalidadeEscolhida()) {
                    totalModalidadesEscolhida++;
                } else {
                    contratoModalidadeVO.getModalidade().setValorMensal(0.0);
                }
            }
            if (totalModalidadesEscolhida == 0) {
                return;
            }
            double valorMensal = (planoDuracaoCreditoTreinoSelecionado.getValorTotal() / getContratoNovo().getContratoDuracao().getNumeroMeses()) / (totalModalidadesEscolhida);

            for (ContratoModalidadeVO contratoModalidadeVO : this.getContratoNovo().getContratoModalidadeVOs()) {
                if (contratoModalidadeVO.getModalidade().getModalidadeEscolhida()) {
                    contratoModalidadeVO.getModalidade().setValorMensal(valorMensal);
                } else {
                    contratoModalidadeVO.getModalidade().setValorMensal(0.0);
                }
            }

        }
    }

    public void obterValorModalidade(ContratoComposicaoVO contratoComposicaoVO, ContratoModalidadeVO cm) throws Exception {
        List<ComposicaoModalidadeVO> listaComposicaoModalidade = contratoComposicaoVO.getComposicaoVO().getComposicaoModalidadeVOs();
        if (contratoComposicaoVO.getComposicaoVO().isModalidadesEspecificas()
                && listaComposicaoModalidade != null) {
            final int codModalidade = cm.getModalidade().getCodigo();
            Integer tmpvezes = cm.getNrVezesSemana();
            if (!cm.getContratoModalidadeTurmaVOs().isEmpty()) {
                tmpvezes = contarNrVezesSemanaTurmaModalidade(cm);
            }
            final int nrvezes = tmpvezes;
            ComposicaoModalidadeVO compRef = (ComposicaoModalidadeVO) ColecaoUtils.find(listaComposicaoModalidade, new Predicate() {
                @Override
                public boolean evaluate(Object o) {
                    ComposicaoModalidadeVO composicao = (ComposicaoModalidadeVO) o;
                    return composicao.getModalidade().getCodigo().equals(codModalidade)
                            && composicao.getNrVezes().equals(nrvezes);
                }
            });
            if (compRef == null) {
                compRef = (ComposicaoModalidadeVO) ColecaoUtils.find(listaComposicaoModalidade, new Predicate() {
                    @Override
                    public boolean evaluate(Object o) {
                        ComposicaoModalidadeVO composicao = (ComposicaoModalidadeVO) o;
                        return composicao.getModalidade().getCodigo().equals(codModalidade)
                                && composicao.getNrVezes().equals(0);
                    }
                });
            }

            if (compRef != null) {
                cm.getModalidade().setModalidadeEscolhida(true);
                cm.getModalidade().setComposicao(true);
                cm.getModalidade().setNrVezes(cm.getNrVezesSemana());
                double valorMod = compRef.getValorMensalComposicao();
                if (cm.getPlanoVezesSemanaVO().getTipoOperacao().equals("EX")) {
                    cm.getPlanoVezesSemanaVO().setValorEspecifico(valorMod);
                }
                if (cm.getModalidade().getValorOriginal() == null) {
                    cm.getModalidade().setValorOriginal(cm.getModalidade().getValorMensal());
                }
                cm.getModalidade().setValorMensal(valorMod);
                cm.setValorModalidade(valorMod);

                if (!cm.getModalidade().getUtilizarTurma()) {
                    marcarVezesSemanaModalidadeDefault(cm);
                }
                //incluiProdutosModalidade(cm);

                //Alterar PlanoModalidadeVezesSemana para ser exibido na tela de negociação que o Valor de Referência da Modalidade mudou
                List<PlanoModalidadeVO> listaPlanoMod = contratoNovo.getPlano().getPlanoModalidadeVOs();
                final int codMod = compRef.getModalidade().getCodigo();
                final int nrvezesPlano = compRef.getNrVezes().equals(0) ? cm.getNrVezesSemana() : compRef.getNrVezes();
                for (PlanoModalidadeVO planoMod : listaPlanoMod) {
                    if (planoMod.getModalidade().getCodigo().equals(codMod)) {
                        PlanoModalidadeVezesSemanaVO vezesSemana = (PlanoModalidadeVezesSemanaVO) ColecaoUtils.find(planoMod.getPlanoModalidadeVezesSemanaVOs(), new Predicate() {
                            @Override
                            public boolean evaluate(Object o) {
                                PlanoModalidadeVezesSemanaVO obj = (PlanoModalidadeVezesSemanaVO) o;
                                return obj.getNrVezes().equals(nrvezesPlano);
                            }
                        });
                        if (vezesSemana != null) {
                            vezesSemana.setReferencia(true);
                            vezesSemana.setOrigem(ReferenciaValorModalidadeEnum.PACOTE);
                            vezesSemana.setValorReferencia(valorMod);
                        } else {
                            throw new Exception("Não foi possível renovar o contrato pois não existem modalidades compatíveis para este contrato.");
                        }
                        break;
                    }
                }
            }
        }
    }

    public Integer contarNrVezesSemanaTurmaModalidade(ContratoModalidadeVO cm) {
        Integer result = 0;
        if (cm != null && !UteisValidacao.emptyList(cm.getContratoModalidadeTurmaVOs())) {
            for (Iterator it = cm.getContratoModalidadeTurmaVOs().iterator(); it.hasNext(); ) {
                ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) it.next();
                result += cmt.getContratoModalidadeHorarioTurmaVOs().size();
            }
        }
        return result;
    }

    public void marcarVezesSemanaModalidadeDefault(ContratoModalidadeVO obj) {
        Iterator i = contratoNovo.getPlano().getPlanoModalidadeVOs().iterator();
        while (i.hasNext()) {
            PlanoModalidadeVO planoModalidade = (PlanoModalidadeVO) i.next();
            if (planoModalidade.getModalidade().getCodigo().equals(obj.getModalidade().getCodigo())) {
                Iterator k = planoModalidade.getPlanoModalidadeVezesSemanaVOs().iterator();
                while (k.hasNext()) {
                    PlanoModalidadeVezesSemanaVO planoVezes = (PlanoModalidadeVezesSemanaVO) k.next();
                    if (obj.getModalidade().getNrVezes().equals(planoVezes.getNrVezes())) {
                        planoVezes.setVezeSemanaEscolhida(true);
                        obj.setPlanoVezesSemanaVO(planoVezes);
                        obj.setNrVezesSemana(planoVezes.getNrVezes());
                    }
                }
                break;
            }
        }
    }

    private void inicializarValoresDuracaoPlanoCreditoTreino() throws Exception{
        if (getContratoNovo().getPlano().isVendaCreditoTreino()){
            getContratoNovo().getContratoDuracao().setContratoDuracaoCreditoTreinoVO(getZWFacade().getContratoDuracaoCreditoTreino().consultarPorContratoDuracao(getContratoNovo().getContratoDuracao().getCodigo(),Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            // montar os valores de vezes por semana.
            for (ContratoModalidadeVO contratoModalidadeVO: getContratoNovo().getContratoModalidadeVOs()){
                if (contratoModalidadeVO.getModalidade().getModalidadeEscolhida()){
                    contratoModalidadeVO.getPlanoVezesSemanaVO().setNrVezes(contratoModalidadeVO.getNrVezesSemana());
                }
                contratoModalidadeVO.getModalidade().setUtilizarTurma(getContratoNovo().getContratoDuracao().getContratoDuracaoCreditoTreinoVO().getTipoHorarioCreditoTreinoEnum() == TipoHorarioCreditoTreinoEnum.HORARIO_TURMA);
            }
        }
    }


    
    private void adicionarConfigsPlano(Integer codigoContratoRenovado) throws Exception {
        PlanoDuracaoVO planoduracao = getZWFacade().getPlanoDuracao().consultarPorNumeroMesesPlano(getContratoNovo().getContratoDuracao().getNumeroMeses(), 
                getContratoNovo().getPlano().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (planoduracao == null || UteisValidacao.emptyNumber(planoduracao.getCodigo())) {
            throw new Exception("Não foi possível renovar o contrato pois não existem durações de plano compatíveis para este contrato.");
        }
        getContratoNovo().setPlanoDuracao(planoduracao);

        verificarPlanoHorario(getContratoNovo().getPlano().getCodigo(), getContratoNovo().getPlanoHorario().getHorario().getCodigo());
        getContratoNovo().getPlanoHorario().setCodigo(getContratoNovo().getContratoHorario().getCodigo());
        verificarPlanoCondicaoPagamento(planoduracao.getCodigo(), getContratoNovo().getContratoCondicaoPagamento().getCondicaoPagamento().getCodigo());
        getContratoNovo().setContratoBaseadoRenovacao(codigoContratoRenovado);
        getContratoNovo().setSituacao("AT");
        getContratoNovo().setSituacaoContrato("RN");
        getContratoNovo().setRenovarContrato(true);

    }

    private void adicionarConfigsPlanoDiferente(Integer codigoContratoRenovado) throws Exception {
        PlanoDuracaoVO planoduracao = getZWFacade().getPlanoDuracao().consultarPorChavePrimaria(getContratoVelho().getPlano().getDuracaoPlanoDiferenteRenovacao(),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        getContratoNovo().setPlanoDuracao(planoduracao);

        verificarPlanoHorario(getContratoNovo().getPlano().getCodigo(), getContratoVelho().getPlano().getHorarioPlanoDiferenteRenovacao());
        getContratoNovo().getPlanoHorario().setCodigo(getContratoNovo().getContratoHorario().getCodigo());
        verificarPlanoCondicaoPagamento(planoduracao.getCodigo(), getContratoVelho().getPlano().getCondicaoPagPlanoDiferenteRenovacao());
        getContratoNovo().setContratoBaseadoRenovacao(codigoContratoRenovado);
        getContratoNovo().setSituacao("AT");
        getContratoNovo().setSituacaoContrato("RN");
        getContratoNovo().setRenovarContrato(true);

    }

    public void verificarPlanoCondicaoPagamento(Integer planoduracao, Integer condicao) throws Exception {
        try {
            List<PlanoCondicaoPagamentoVO> lista = getZWFacade().getPlanoCondicaoPagamento().consultarPlanoCondicaoPagamentos(planoduracao ,Uteis.NIVELMONTARDADOS_TODOS);
            if (lista != null){
                if (lista.size() == 1){
                    getContratoNovo().setPlanoCondicaoPagamento(lista.get(0));
                } else {
                    boolean achouPlanoCondicaoPagamento = false;
                    for (PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO : lista) {
                        if (achouPlanoCondicaoPagamento){
                            break;
                        }
                        if (planoCondicaoPagamentoVO.getPlanoDuracao().equals(planoduracao) && planoCondicaoPagamentoVO.getCondicaoPagamento().getCodigo().equals(condicao)) {
                            getContratoNovo().setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);
                            achouPlanoCondicaoPagamento = true;
                        }
                    }
                    if (!achouPlanoCondicaoPagamento) {
                        throw new Exception("Não foi possível renovar o contrato pois não existem condições de pagamento compatíveis para este contrato.");
                    }
                }
            } else {
                throw new Exception("Não foi possível renovar o contrato pois não existem condições de pagamento compatíveis para este contrato.");
            }
        } catch (Exception ex){
            throw ex;
        }
    }

    public void verificarPlanoHorario(Integer plano, Integer horario) throws Exception {
        try {
            PlanoHorarioVO planoHorarioVO = getZWFacade().getPlanoHorario().consultarPorPlanoHorario(plano, horario, Uteis.NIVELMONTARDADOS_TODOS);
            if (planoHorarioVO != null && !UteisValidacao.emptyNumber(planoHorarioVO.getCodigo())){
                getContratoNovo().getContratoHorario().setHorario(planoHorarioVO.getHorario());
                getContratoNovo().getContratoHorario().setTipoValor(planoHorarioVO.getTipoValor());
                getContratoNovo().getContratoHorario().setTipoOperacao(planoHorarioVO.getTipoOperacao());
                getContratoNovo().getContratoHorario().setPercentualDesconto(planoHorarioVO.getPercentualDesconto());
                getContratoNovo().getContratoHorario().setValorEspecifico(planoHorarioVO.getValorEspecifico());
                getContratoNovo().setPlanoHorario(planoHorarioVO);
            } else {
                throw new Exception("Não foi possível renovar o contrato pois não existem horários compatíveis para este contrato.");
            }
        } catch (Exception ex){
            throw ex;
        }
    }

    private void adicionarProdutosObrigatorios() throws Exception {
        List<PlanoProdutoSugeridoVO> produtoSugeridos = getZWFacade().getPlanoProdutoSugerido().consultarPlanoProdutoSugeridos(
                getContratoNovo().getPlano().getCodigo(), true, Uteis.NIVELMONTARDADOS_TODOS);
        getContratoNovo().setContratoPlanoProdutoSugeridoVOs(new ArrayList());
          getContratoNovo().setSomaProduto(0D);
         ContratoPlanoProdutoSugeridoVO conPPSugerigo;
        for (PlanoProdutoSugeridoVO planoProdutoSugerido : produtoSugeridos) {
            if ((!planoProdutoSugerido.getProduto().getTipoProduto().equals("MA")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("RE")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("TA")
                    && !planoProdutoSugerido.getProduto().getTipoProduto().equals("TD")
                    && (planoProdutoSugerido.getProduto().getTipoProduto().equals("RN") || (planoProdutoSugerido.isObrigatorio() && getContratoNovo().getPlano().getRenovarProdutoObrigatorio())))
                    || (planoProdutoSugerido.getProduto().getTipoProduto().equals("TA") && planoProdutoSugerido.isObrigatorio() && getContratoNovo().getPlano().isRenovarAnuidadeAutomaticamente())) {
                conPPSugerigo = new ContratoPlanoProdutoSugeridoVO();
                conPPSugerigo.setPlanoProdutoSugerido(planoProdutoSugerido);
                getContratoNovo().getContratoPlanoProdutoSugeridoVOs().add(conPPSugerigo);
                MovProdutoVO movP = new MovProdutoVO();
                movP.setProduto(planoProdutoSugerido.getProduto());
                movP.setApresentarMovProduto(false);
                movP.setContrato(getContratoNovo());
                movP.setDescricao(planoProdutoSugerido.getProduto().getDescricao());
                movP.setSituacao("EA");
                movP.setEmpresa(getContratoNovo().getEmpresa());
                movP.setPessoa(getContratoNovo().getPessoa());
                movP.setMesReferencia(Uteis.getMesReferenciaData(getContratoNovo().getVigenciaDe()));
                movP.setQuantidade(1);
                movP.setAnoReferencia(Uteis.getAnoData(getContratoNovo().getVigenciaDe()));
                movP.setDataLancamento(getContratoNovo().getDataLancamento());
                if (!planoProdutoSugerido.getProduto().getTipoVigencia().equals("")) {
                        movP.setDataInicioVigencia(planoProdutoSugerido.getDataVenda());
                        movP.setDataFinalVigencia(planoProdutoSugerido.getDataValidade());
                    } else {
                        movP.setDataInicioVigencia(null);
                        movP.setDataFinalVigencia(null);
                    }
                movP.setResponsavelLancamento(getContratoNovo().getResponsavelContrato());
                movP.setPrecoUnitario(planoProdutoSugerido.getValorProduto());
                movP.setTotalFinal(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(planoProdutoSugerido.getValorProduto()));
                getContratoNovo().setSomaProduto(
                        Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movP.getTotalFinal()
                        + getContratoNovo().getSomaProduto()));
                if (planoProdutoSugerido.getProduto().getTipoProduto().equals("TA")) {
                    getContratoNovo().setSomaAnuidade(Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movP.getTotalFinal() + getContratoNovo().getSomaAnuidade()));
                }
                getContratoNovo().adicionarObjMovProdutoVOs(movP,true);
                getContratoNovo().setTotalFinalProdutos(
                        Uteis.arredondarForcando2CasasDecimaisMantendoSinal(movP.getTotalFinal()
                                + getContratoNovo().getTotalFinalProdutos()));
            }
        }


    }

    private void adicionarPeriodoAcesso() {
        PeriodoAcessoClienteVO periodoAcessoClienteVO = new PeriodoAcessoClienteVO();
        periodoAcessoClienteVO.setPessoa(getContratoNovo().getPessoa().getCodigo());
        periodoAcessoClienteVO.setDataInicioAcesso(getContratoNovo().getVigenciaDe());
        periodoAcessoClienteVO.setDataFinalAcesso(getContratoNovo().getVigenciaAteAjustada());
        periodoAcessoClienteVO.setTipoAcesso("CA");
        getContratoNovo().getPeriodoAcessoClienteVOs().add(periodoAcessoClienteVO);
    }

    /**
     * Responsável por preencher as datas do novo contrato gerado na renovação
     * <AUTHOR>
     * 27/07/2011
     */
    private void preencherDatas() throws Exception {
        getContratoNovo().setDataLancamento(Calendario.hoje());
        getContratoNovo().setVigenciaDe(Uteis.obterDataFutura(getContratoNovo().getVigenciaAteAjustada(), 2));
        getContratoNovo().setVigenciaAte(Uteis.obterDataAnterior(
                Uteis.somarCampoData(
                getContratoNovo().getVigenciaDe(),
                Calendar.MONTH,
                getContratoNovo().getPlanoDuracao().getNumeroMeses()), 1));
        Calendar cDataPrimeiraParcela = Calendario.getInstance(getContratoNovo().getVigenciaDe());
        if(getContratoNovo().getEmpresa().isPermiteContratoPosPagoRenovacaoAuto()){
            //Verificar se existe uma parcela no mesmo mês para este aluno, caso positivo, empurrar a primeira parcela do contrato para um mês após a último vencimento do contrato anterior
            Date ultimoVencimento  = zwFacade.getMovParcela().consultarUltimoVencimentoContrato(getContratoNovo().getCodigo());
                   
            if (ultimoVencimento != null && ultimoVencimento.getMonth() == cDataPrimeiraParcela.getTime().getMonth() && ultimoVencimento.getYear() == cDataPrimeiraParcela.getTime().getYear()) {
                cDataPrimeiraParcela = Calendario.getInstance(ultimoVencimento);
                cDataPrimeiraParcela.add(Calendar.MONTH, 1);
            }
        }

        getContratoNovo().setVigenciaAteAjustada(getContratoNovo().getVigenciaAte());
        if(zwFacade.isMensalEexisteOperacaoContratoAcrescimoDias(null, getContratoNovo())){
            getContratoNovo().setDiaVencimentoCartaoRecorrencia(Calendar.getInstance().get(Calendar.DAY_OF_MONTH));
            getContratoNovo().setDataPrimeiraParcela(getContratoNovo().getDataLancamento());
        }else{
            getContratoNovo().setDataPrimeiraParcela(cDataPrimeiraParcela.getTime());
        }
        getContratoNovo().setDataPrevistaRenovar(getContratoNovo().getVigenciaAte());
        getContratoNovo().setDataPrevistaRematricula(getContratoNovo().getVigenciaAte());

    }

    private void removerModalidadesInativas() throws Exception {
        Iterator i = getContratoNovo().getContratoModalidadeVOs().iterator();
        while (i.hasNext()) {
            ContratoModalidadeVO contratoModalidadeVO = (ContratoModalidadeVO) i.next();
            if (!contratoModalidadeVO.getModalidade().getAtivo()) {
                i.remove();
            }
        }
        if (UteisValidacao.emptyList(getContratoNovo().getContratoModalidadeVOs())){
            throw new Exception("Não foi possível renovar o contrato, pois o mesmo não possui modalidades ativas.");
        }
    }

    
    private void preencherModalidadeVezesNaSemana() throws Exception {
        for (ContratoModalidadeVO modalidade : getContratoNovo().getContratoModalidadeVOs()) {
                if(modalidade.getModalidade().getComposicao()){
                    continue;
                }
                int nrVezesPorSemana = 0;
                if(modalidade.getModalidade().isUtilizarTurma()){
                    Iterator i = modalidade.getContratoModalidadeTurmaVOs().iterator();
                // percorre as turmas da modalidade
                    while (i.hasNext()) {
                        ContratoModalidadeTurmaVO cmt = (ContratoModalidadeTurmaVO) i.next();
                        // conta qtos horarios tem em cada turma
                        Ordenacao.ordenarLista(cmt.getContratoModalidadeHorarioTurmaVOs(), "diaDaSemana_Apresentar");
                        nrVezesPorSemana += cmt.getContratoModalidadeHorarioTurmaVOs().size();
                    }
                }else {
                       nrVezesPorSemana =modalidade.getNrVezesSemana();
                }

            PlanoModalidadeVO plano;
            try {
                plano = getPlanoModalidadeDAO().consultarPorPlanoModalidade(getContratoNovo().getPlano().getCodigo(), modalidade.getModalidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } catch (Exception ex) {
                String sb = ex.getMessage() + "\n" +
                        " Possivelmente a modalidade " + modalidade.getModalidade().getNome() + " não está mais disponível no plano " + getContratoNovo().getPlano().getDescricao();
                registrarLogErroObjetoVO("CONTRATO", "RENOVAÇÃO AUTOMÁTICA",
                        getContratoNovo().getCodigo().toString(), getContratoNovo().getPessoa().getCodigo(),
                        sb, getContratoNovo().getResponsavelContrato().getNome(), "");
                Uteis.logar(ex, RenovacaoAutomaticaRecorrenciaService.class);
                throw new ConsistirException(sb);
            }
                // pega a lista de vezes por semana de uma modalidade
                List<PlanoModalidadeVezesSemanaVO> lista = getPlanoModVezDAO().consultarPorCodigoPlanoModalidade(plano.getCodigo());
                // percorre a lista de vezes por semana da modalidade
                
                PlanoModalidadeVezesSemanaVO planoVezesReferencia = (PlanoModalidadeVezesSemanaVO) ColecaoUtils.find(
                       lista, new Predicate() {
                    @Override
                    public boolean evaluate(Object o) {
                        PlanoModalidadeVezesSemanaVO vezesSemana = (PlanoModalidadeVezesSemanaVO) o;
                        return vezesSemana.isReferencia();
                    }
                });
                
                if (planoVezesReferencia != null) {
                    if (!modalidade.getModalidade().getUtilizarTurma()) {
                        modalidade.setPlanoVezesSemanaVO(planoVezesReferencia);
                        modalidade.setNrVezesSemana(planoVezesReferencia.getNrVezes());
                    }
                    modalidade.getModalidade().setValorOriginal(planoVezesReferencia.getValorEspecifico());
                    modalidade.getModalidade().setValorMensal(planoVezesReferencia.getValorEspecifico());
                    modalidade.getModalidade().setValorOriginal(planoVezesReferencia.getValorEspecifico());
                    modalidade.getModalidade().setNrVezes(planoVezesReferencia.getNrVezes());
                    planoVezesReferencia.setReferencia(true);
                    planoVezesReferencia.setOrigem(ReferenciaValorModalidadeEnum.PLANO);
                    planoVezesReferencia.setValorReferencia(planoVezesReferencia.getValorEspecifico());
                }


                if (lista.size() == 1){
                    lista.get(0).setVezeSemanaEscolhida(true);
                    modalidade.setPlanoVezesSemanaVO(lista.get(0));
                    modalidade.setNrVezesSemana(lista.get(0).getNrVezes());
                } else {
                    boolean achouVezesSemana = false;
                    for (PlanoModalidadeVezesSemanaVO planoVezesSemana : lista) {
                        // se a quantidade escolhida existir
                        if (planoVezesSemana.getNrVezes() == nrVezesPorSemana) {
                            achouVezesSemana = true;
                            planoVezesSemana.setVezeSemanaEscolhida(true);
                            modalidade.setPlanoVezesSemanaVO(planoVezesSemana);
                            modalidade.setNrVezesSemana(nrVezesPorSemana);
                        }
                    }
                    if (!achouVezesSemana){
                        double diferenca = Uteis.arredondarForcando2CasasDecimais(lista.get(0).getValorEspecifico() - modalidade.getPlanoVezesSemanaVO().getValorEspecifico());
                        PlanoModalidadeVezesSemanaVO planoModalidadeVezesSemanaAux = lista.get(0);
                        for(PlanoModalidadeVezesSemanaVO planoVezesSemana : lista){
                            if(diferenca > Uteis.arredondarForcando2CasasDecimais(planoVezesSemana.getValorEspecifico() - modalidade.getPlanoVezesSemanaVO().getValorEspecifico())){
                                diferenca = Uteis.arredondarForcando2CasasDecimais(planoVezesSemana.getValorEspecifico() - modalidade.getPlanoVezesSemanaVO().getValorEspecifico());
                                planoModalidadeVezesSemanaAux = planoVezesSemana;
                            }
                        }
                        planoModalidadeVezesSemanaAux.setVezeSemanaEscolhida(true);
                        modalidade.setPlanoVezesSemanaVO(planoModalidadeVezesSemanaAux);
                        modalidade.setNrVezesSemana(planoModalidadeVezesSemanaAux.getNrVezes());
                    }
                }
                modalidade.getModalidade().setModalidadeEscolhida(true);
        }
    }

    
    @SuppressWarnings("unchecked")
    private void setarHistorico() throws Exception {
        HistoricoContratoVO historico = new HistoricoContratoVO();
        historico.setDescricao("Renovado");
        historico.setTipoHistorico("RN");
        getContratoNovo().setContratoBaseadoRematricula(0);
        getContratoNovo().setHistoricoContratoVOs(new ArrayList());
        historico.setDataRegistro(Calendario.hoje());
        historico.setDataInicioSituacao(getContratoNovo().getVigenciaDe());
        historico.setDataFinalSituacao(getContratoNovo().getVigenciaAteAjustada());
        historico.setDataInicioTemporal(null);
        historico.setResponsavelRegistro(getZWFacade().getUsuarioRecorrencia());
        getContratoNovo().getHistoricoContratoVOs().add(historico);
        getContratoNovo().atualizarHistoricosContratoAnteriorRenovado(getZWFacade().getHistoricoContrato());
    }


    public ZillyonWebFacade getZWFacade() throws Exception {
        if (zwFacade == null) {
            zwFacade = new ZillyonWebFacade(this.con);
        }
        return zwFacade;
    }

    public ContratoVO getContratoVelho() {
        return contratoVelho;
    }

    public void setContratoVelho(ContratoVO contratoVelho) {
        this.contratoVelho = contratoVelho;
    }

    public ContratoVO getContratoNovo() {
        return contratoNovo;
    }

    public void setContratoNovo(ContratoVO contratoNovo) {
        this.contratoNovo = contratoNovo;
    }

    public Contrato getContratoDAO() throws Exception {
        if (contratoDAO == null) {
            contratoDAO = new Contrato(this.con);
        }
        return contratoDAO;
    }

    public void setContratoDAO(Contrato contratoDAO) {
        this.contratoDAO = contratoDAO;
    }

    public PlanoComposicao getPlanoComposicaoDAO() throws Exception {
        if (planoComposicaoDAO == null) {
            planoComposicaoDAO = new PlanoComposicao(this.con);
        }
        return planoComposicaoDAO;
    }

    public PlanoModalidade getPlanoModalidadeDAO() throws Exception {
         if (planoModalidadeDAO == null) {
            planoModalidadeDAO = new PlanoModalidade(this.con);
        }
        return planoModalidadeDAO;
    }

    public PlanoModalidadeVezesSemana getPlanoModVezDAO() throws Exception {
        if (planoModVezDAO == null) {
            planoModVezDAO = new PlanoModalidadeVezesSemana(this.con);
        }
        return planoModVezDAO;
    }

    public PlanoDuracaoCreditoTreino getPlanoDuracaoCreditoTreinoDAO() throws Exception {
        if (planoDuracaoCreditoTreinoDAO == null) {
            planoDuracaoCreditoTreinoDAO = new PlanoDuracaoCreditoTreino(this.con);
        }
        return planoDuracaoCreditoTreinoDAO;
    }

    private void validarConvenioEDescontos() {
        if (!getContratoNovo().getPlano().isRenovarAutomaticamenteComDesconto()) {
            getContratoNovo().setDesconto(null);
            getContratoNovo().setValorDescontoEspecifico(0.0);
            getContratoNovo().setValorDescontoPorcentagem(0.0);
        }
        if(getContratoNovo().getConvenioDesconto() != null ){
            if(Calendario.maior(getContratoNovo().getVigenciaDe(), getContratoNovo().getConvenioDesconto().getDataFinalVigencia())){
                getContratoNovo().setConvenioDesconto(new ConvenioDescontoVO()); 
            }
        }
        if (getContratoNovo().getOrigemSistema().equals(OrigemSistemaEnum.AUTO_ATENDIMENTO)){
            if (!getContratoNovo().getPlano().isRenovarComDescontoTotem()){
                getContratoNovo().setDesconto(null);
                getContratoNovo().setValorDescontoEspecifico(0.0);
                getContratoNovo().setValorDescontoPorcentagem(0.0);
                getContratoNovo().setConvenioDesconto(new ConvenioDescontoVO());
            }
        }
    }
    
    public void ajustarVencimentoParcelasContratoPrePago() throws Exception{
        if(Calendario.menor(Calendario.hoje(), getContratoNovo().getDataPrimeiraParcela()) && 
                (getContratoNovo().getContratoCondicaoPagamento().getCondicaoPagamento().getRecebimentoPrePago())){
            Date ultimoVencimento  = zwFacade.getMovParcela().consultarUltimoVencimentoContrato(getContratoNovo().getContratoBaseadoRenovacao());
            Date primeiraParcela = Uteis.somarCampoData(getContratoNovo().getDataPrimeiraParcela(), Calendar.MONTH, -1);
            if(ultimoVencimento.getMonth() < primeiraParcela.getMonth()){       
                List<MovParcelaVO> parcelas = zwFacade.getMovParcela().consultarPorContrato(getContratoNovo().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                boolean primeira = true;
                for(MovParcelaVO parcela: parcelas){
                    if(parcela.getDescricao().startsWith("PARCELA")){
                        if(primeira){
                            if(Calendario.menor(primeiraParcela, Calendario.hoje())){
                                parcela.setDataVencimento(Calendario.hoje());
//                                //é preciso verificar se a primeira parcela não está no mesmo mes de hoje,
//                                //se ela não estiver, é necessário incrementar um mes, para que a proxima parcela venha em mês diferente
                                if (Uteis.getMesData(primeiraParcela) < Uteis.getMesData(Calendario.hoje())) {
                                    primeiraParcela = Uteis.somarCampoData(primeiraParcela, Calendar.MONTH, 1);
                                }
                            } else {   
                                parcela.setDataVencimento(primeiraParcela);
                            }
                            parcela.setDataCobranca(parcela.getDataVencimento());
                            primeira = false;
                        } else {
                            primeiraParcela = Uteis.somarCampoData(primeiraParcela, Calendar.MONTH, 1);
                            parcela.setDataVencimento(primeiraParcela);
                            parcela.setDataCobranca(parcela.getDataVencimento());
                        }
                        zwFacade.getMovParcela().alterarSemCommit(parcela);
                    }
                }
            }
        }
    }
    
    public static void main(String... args) throws Exception {
        Connection c = DriverManager.getConnection("******************************************************", "postgres", "pactodb");
        Conexao.guardarConexaoForJ2SE(c);
        RenovacaoAutomaticaService renovacaoService = new RenovacaoAutomaticaService(c);
        ContratoVO contratoVO = renovacaoService.getContratoDAO().consultarPorCodigo(26236,Uteis.NIVELMONTARDADOS_TODOS);
        renovacaoService.renovarAutomatico(contratoVO, false);
    }


    private void preencherComposicao() throws Exception {
        List<ContratoComposicaoVO> novaListaComposicoes = new ArrayList<>();
        for(Object o : contratoNovo.getContratoComposicaoVOs()) {
            ContratoComposicaoVO contratoComposicaoVO = (ContratoComposicaoVO) o;
            PlanoComposicaoVO planoComposicao = contratoNovo.processarContratoComposicaoIgualPlanoComposicao(contratoComposicaoVO);
            if (planoComposicao == null) {
                continue;
            }
            novaListaComposicoes.add(contratoComposicaoVO);
            int quantidadeSelecionadas = 0;
            for (ContratoModalidadeVO contratoModalidadeVO: contratoNovo.getContratoModalidadeVOs()){
                for(ComposicaoModalidadeVO composicaoModalidadeVO: contratoComposicaoVO.getComposicaoVO().getComposicaoModalidadeVOs()){
                    if(contratoModalidadeVO.getModalidade().getCodigo().equals(composicaoModalidadeVO.getModalidade().getCodigo())){
                        quantidadeSelecionadas++;
                        contratoModalidadeVO.getModalidade().setComposicao(true);
                        if(contratoComposicaoVO.getComposicaoVO().isModalidadesEspecificas()){
                            obterValorModalidade(contratoComposicaoVO,contratoModalidadeVO);
                        } else {
                            contratoModalidadeVO.getModalidade().setComposicaoLivre(contratoComposicaoVO.getComposicaoVO().getCodigo());
                        }
                        break;
                    }
                }
            }
            if(!contratoComposicaoVO.getComposicaoVO().isModalidadesEspecificas()){
                atualizaValorComposicao(contratoComposicaoVO.getComposicaoVO(), quantidadeSelecionadas);
            }

        }
        contratoNovo.setContratoComposicaoVOs(novaListaComposicoes);
    }

    private void atualizaValorComposicao(ComposicaoVO composicaoVO, int quantidadeModalidades) {
        // Divide o valor da composição pela quantidade de modalidades desprezando os dígitos após a 2 casa depois da vírgula
        BigDecimal valor = new BigDecimal(composicaoVO.getPrecoComposicao() / quantidadeModalidades).setScale(2, RoundingMode.HALF_UP);
        // Diferença que será adicionado a primeira modalidade para o valor ficar arredondado
        BigDecimal diff = new BigDecimal(composicaoVO.getPrecoComposicao()).subtract(valor.multiply(new BigDecimal(quantidadeModalidades)));
        // verifica todas as modalidades do contrato
        for (ContratoModalidadeVO cm : getContratoNovo().getContratoModalidadeVOs()) {
            // se encontrou a modalidade que faz parte da composicao
            if (composicaoVO.getCodigo() == cm.getModalidade().getComposicaoLivre() && cm.getModalidade().getModalidadeEscolhida()) {
                cm.getModalidade().setValorMensal(valor.add(diff).doubleValue());
                cm.getModalidade().setParteComposicao(true);
                cm.setValorModalidade(cm.getModalidade().getValorMensal());
                diff = BigDecimal.ZERO;
            }
        }
    }
}
