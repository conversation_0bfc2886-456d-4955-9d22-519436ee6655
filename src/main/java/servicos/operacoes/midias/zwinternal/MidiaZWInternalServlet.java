/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.operacoes.midias.zwinternal;

import negocio.comuns.utilitarias.Uteis;
import servicos.operacoes.midias.MidiaService;
import servicos.propriedades.PropsService;

import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class MidiaZWInternalServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String url = request.getRequestURL().toString();
        final String urlServlet = request.getServletPath() + "/";
        String[] urlTemp = url.split(urlServlet);
        String key = urlTemp[1];

        try {
            boolean existe = MidiaService.getInstance().exists(key);

            if (existe) {
                String path = PropsService.getPropertyValue(PropsService.diretorioFotos);
                path += key;
                File f = new File(path);
                response.setHeader("Last-Modified", new Date(f.lastModified()).toGMTString());
                BufferedImage bi = ImageIO.read(f);
                OutputStream out = response.getOutputStream();

                if (key.endsWith(".png")) {
                    response.setContentType("image/png");
                    ImageIO.write(bi, "png", out);
                } else if (key.endsWith(".pdf")){
                    response.setContentType("application/pdf");
                    out.write(Files.readAllBytes(f.toPath()));
                    out.flush();
                } else {
                    response.setContentType("image/jpeg");
                    ImageIO.write(bi, "jpg", out);
                }
                out.close();
            }
        } catch (Exception ex) {
            Uteis.logar(ex, MidiaZWInternalServlet.class);
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">

    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request  servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
