/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.notificador;

import acesso.webservice.AcessoControle;
import acesso.webservice.DaoAuxiliar;
import negocio.comuns.utilitarias.Calendario;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class NotificadorServiceControle {

    private static final String ZW = "ZW";
    private static final String WS = "WS";
    private static List<NotificacaoTO> listaZW = new ArrayList();
    private static List<NotificacaoTO> listaWS = new ArrayList();
    public static List<NotificacaoTO> notificacoesAtuaisZW = new ArrayList();
    public static List<NotificacaoTO> notificacoesAtuaisWS = new ArrayList();

    public static void verificarZW() {
        Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Entrou no método verificarZW");
        try {
            notificacoesAtuaisZW.clear();
            List<NotificacaoTO> tmp = new ArrayList<NotificacaoTO>(listaZW);
            for (NotificacaoTO notificacaoTO : tmp) {
                if (Calendario.entre(Calendario.hoje().getTime(), notificacaoTO.getDataInicio(),
                        notificacaoTO.getDataFim())) {
                    notificacoesAtuaisZW.add(notificacaoTO);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Erro no método verificarZW - " + ex.getMessage());
            throw ex;
        } finally {
            Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Finalizou o método verificarZW");
        }
    }

    public static void verificarWS() {
        Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Entrou no método verificarWS");
        try {
            notificacoesAtuaisWS.clear();
            List<NotificacaoTO> tmp = new ArrayList<NotificacaoTO>(listaWS);
            for (NotificacaoTO notificacaoTO : tmp) {
                Date hoje = Calendario.hoje();
                if (notificacaoTO.getTimeZone() != null) {
                    hoje = Calendario.hojeCalendar(notificacaoTO.getTimeZone()).getTime();
                }

                if (Calendario.entre(hoje.getTime(), notificacaoTO.getDataInicio(), notificacaoTO.getDataFim())) {
                    notificacoesAtuaisWS.add(notificacaoTO);
                    dispararEventos(notificacaoTO);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Erro no método verificarWS - " + ex.getMessage());
            throw ex;
        } finally {
            Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Finalizou o método verificarWS");
        }
    }

    private static void eventoUpdateConfiguracaoSistemaWS(final NotificacaoTO notf) {
        if (notf != null && notf.getDescricao() != null && notf.getDescricao().equals("updateConfiguracaoSistemaWS")
                && notf.getChave() != null && !notf.getChave().isEmpty()) {
            try {
                if (DaoAuxiliar.existeControle(notf.getChave())) {
                    AcessoControle control = DaoAuxiliar.retornarAcessoControle(notf.getChave());
                    if (control != null) {
                        control.updateConfiguracaoSistema();
                    }
                }
            } catch (Exception ex) {
                Logger.getLogger(NotificadorServiceControle.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private static void dispararEventos(final NotificacaoTO notf) {
        eventoUpdateConfiguracaoSistemaWS(notf);
        //demais eventos aqui
    }

    public static void adicionarNotificacao(NotificacaoTO notf) {
        Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Entrou no método adicionarNotificacao");
        try {
            if (notf.getDescricao() != null && !notf.getDescricao().isEmpty()
                    && notf.getDataInicio() != null && notf.getDataFim() != null
                    && notf.getTipo() != null) {
                if (notf.getTipo().equals(ZW)) {
                    listaZW.add(notf);
                } else if (notf.getTipo().equals(WS)) {
                    listaWS.add(notf);
                }
                verificarZW();
                verificarWS();
            }
        } catch (Exception ex) {
            Logger.getLogger(NotificadorServiceControle.class.getSimpleName()).log(Level.INFO, "#### Erro no método adicionarNotificacao - " + ex.getMessage());
            throw ex;
        }

    }

    public static void limpar(String tipo) {
        if (tipo.equals(ZW)) {
            List<NotificacaoTO> l = new ArrayList(listaZW);
            for (NotificacaoTO notificacaoTO : l) {
                if (notificacaoTO.getTipo().equals(tipo)) {
                    listaZW.remove(notificacaoTO);
                }
            }
        } else if (tipo.equals(WS)) {
            List<NotificacaoTO> l = new ArrayList(listaWS);
            for (NotificacaoTO notificacaoTO : l) {
                if (notificacaoTO.getTipo().equals(tipo)) {
                    listaWS.remove(notificacaoTO);
                }
            }
        }
    }

    public static void limpar() {
        listaWS.clear();
        listaZW.clear();
        notificacoesAtuaisZW = new ArrayList();
        notificacoesAtuaisWS = new ArrayList();
        
    }

    public boolean isApresentarNotificacao() {        
        return obterNotificacaoAtualPorTipo(ZW) != null;
    }

    public NotificacaoTO getNotificacaoAtual() {
        return obterNotificacaoAtualPorTipo(ZW);
    }

    public static NotificacaoTO obterNotificacaoAtualPorTipo(final String tipo) {
        if (tipo.equals(ZW)) {
            List<NotificacaoTO> tmp = new ArrayList(notificacoesAtuaisZW);
            for (NotificacaoTO notificacaoTO : tmp) {
                if (notificacaoTO.getTipo().equals(tipo)) {
                    return notificacaoTO;
                }
            }
        } else if (tipo.equals(WS)) {
            List<NotificacaoTO> tmp = new ArrayList(notificacoesAtuaisWS);
            for (NotificacaoTO notificacaoTO : tmp) {
                if (notificacaoTO.getTipo().equals(tipo)) {
                    return notificacaoTO;
                }
            }

        }
        return null;
    }

    public static List<NotificacaoTO> obterListaNotificacoesPorTipo(final String tipo) {
        List<NotificacaoTO> l = new ArrayList();
        if (tipo.equals(ZW)) {
            List<NotificacaoTO> tmp = new ArrayList(notificacoesAtuaisZW);
            for (NotificacaoTO notificacaoTO : tmp) {
                l.add(notificacaoTO);
            }
        } else if (tipo.equals(WS)) {
            List<NotificacaoTO> tmp = new ArrayList(notificacoesAtuaisWS);
            for (NotificacaoTO notificacaoTO : tmp) {
                l.add(notificacaoTO);
            }
        }
        return l;
    }
}
