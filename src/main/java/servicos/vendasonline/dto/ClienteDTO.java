package servicos.vendasonline.dto;

import negocio.comuns.basico.ClienteVO;
import org.json.JSONObject;

public class ClienteDTO {
    private Integer codigo;
    private String matricula;
    private String nome;
    private String email;
    private String cpf;
    private Integer pessoa;
    private String situacao;
    private Integer empresa;

    public ClienteDTO(ClienteVO clienteVO) {
        this.codigo = clienteVO.getCodigo();
        this.matricula = clienteVO.getMatricula();
        this.nome = clienteVO.getPessoa().getNome();
        this.pessoa = clienteVO.getPessoa().getCodigo();
        this.situacao = clienteVO.getSituacao();
        this.empresa = clienteVO.getEmpresa().getCodigo();
        this.email = clienteVO.getPessoa().getEmail();
        this.cpf = clienteVO.getPessoa().getCfp();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public JSONObject toJSON() {
        return new JSONObject(this);
    }
}
