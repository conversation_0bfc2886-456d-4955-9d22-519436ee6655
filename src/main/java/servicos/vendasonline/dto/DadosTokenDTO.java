package servicos.vendasonline.dto;

import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 29/08/2022
 */

public class DadosTokenDTO {

    private String chave;
    private int codEmpresa;
    private int responsavel;
    private String cliente;
    private int tipoConvenio;
    private boolean cobrarParcelasEmAberto;
    private int codCobrancaAntecipada;
    private int codPactoPayComunicacao;
    private int origemCobranca;
    private Boolean todasEmAberto;
    private String parcelasSelecionadas;
    private int numeroVezesParcelamentoOperadora;

    public DadosTokenDTO() {

    }

    public DadosTokenDTO(JSONObject json) {
        this.chave = json.optString("chave");
        this.codEmpresa = json.optInt("codEmpresa");
        this.responsavel = json.optInt("responsavel");
        this.cliente = json.optString("cliente");
        this.tipoConvenio = json.optInt("tipoConvenio");
        this.cobrarParcelasEmAberto = json.optBoolean("cobrarParcelasEmAberto");
        this.origemCobranca = json.optInt("origemCobranca");
        this.codCobrancaAntecipada = json.optInt("codCobrancaAntecipada");
        this.codPactoPayComunicacao = json.optInt("codPactoPayComunicacao");
        this.todasEmAberto = json.optBoolean("todasEmAberto");
        this.parcelasSelecionadas = json.optString("parcelasSelecionadas");
    }


    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public int getCodEmpresa() {
        return codEmpresa;
    }

    public void setCodEmpresa(int codEmpresa) {
        this.codEmpresa = codEmpresa;
    }

    public int getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(int responsavel) {
        this.responsavel = responsavel;
    }

    public String getCliente() {
        return cliente;
    }

    public void setCliente(String cliente) {
        this.cliente = cliente;
    }

    public boolean isCobrarParcelasEmAberto() {
        return cobrarParcelasEmAberto;
    }

    public void setCobrarParcelasEmAberto(boolean cobrarParcelasEmAberto) {
        this.cobrarParcelasEmAberto = cobrarParcelasEmAberto;
    }

    public int getCodCobrancaAntecipada() {
        return codCobrancaAntecipada;
    }

    public void setCodCobrancaAntecipada(int codCobrancaAntecipada) {
        this.codCobrancaAntecipada = codCobrancaAntecipada;
    }

    public int getOrigemCobranca() {
        return origemCobranca;
    }

    public void setOrigemCobranca(int origemCobranca) {
        this.origemCobranca = origemCobranca;
    }

    public int getTipoConvenio() {
        return tipoConvenio;
    }

    public void setTipoConvenio(int tipoConvenio) {
        this.tipoConvenio = tipoConvenio;
    }

    public int getCodPactoPayComunicacao() {
        return codPactoPayComunicacao;
    }

    public void setCodPactoPayComunicacao(int codPactoPayComunicacao) {
        this.codPactoPayComunicacao = codPactoPayComunicacao;
    }

    public Boolean getTodasEmAberto() {
        return todasEmAberto;
    }

    public void setTodasEmAberto(Boolean todasEmAberto) {
        this.todasEmAberto = todasEmAberto;
    }

    public String getParcelasSelecionadas() {
        return parcelasSelecionadas;
    }

    public void setParcelasSelecionadas(String parcelasSelecionadas) {
        this.parcelasSelecionadas = parcelasSelecionadas;
    }

    public int getNumeroVezesParcelamentoOperadora() {
        return numeroVezesParcelamentoOperadora;
    }

    public void setNumeroVezesParcelamentoOperadora(int numeroVezesParcelamentoOperadora) {
        this.numeroVezesParcelamentoOperadora = numeroVezesParcelamentoOperadora;
    }

}
