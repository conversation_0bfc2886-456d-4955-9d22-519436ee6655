package servicos.pix;

public enum PixOperacaoLogEnum {

    NENHUM(1, ""),
    CRIACAO(2, "CRIACAO"),
    CANCELAR(3, "CANCEL<PERSON>"),
    ESTORNO_RECIBO(4, "ESTORNO_RECIBO"),
    ESTORNO_CONTRATO(5, "ESTORNO_CONTRATO"),
    ESTORNO_PRODUTO(6, "ESTORNO_PRODUTO"),
    EXCLUIR_PARCELA(7, "EXCLUIR_PARCELA"),
    ALTERAR_STATUS(8, "ALTERAR_STATUS");

    private Integer codigo;
    private String descricao;

    private PixOperacaoLogEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }
}
