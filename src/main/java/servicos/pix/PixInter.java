package servicos.pix;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.basico.enumerador.TipoTokenEnum;
import negocio.comuns.financeiro.ConvenioCobrancaArquivoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisTelefone;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.Token;
import negocio.interfaces.financeiro.PixServiceInterfaceFacade;
import org.apache.commons.io.FilenameUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import servicos.SuperServico;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Rodrigo Estulano on 24/01/2024.
 */

public class PixInter extends SuperServico implements PixServiceInterfaceFacade {


    public PixInter(Connection con) throws Exception {
        super(con);
    }

    @Override
    public PixRequisicaoDto cancelar(PixVO pixVO) throws Exception {

        HttpClient httpClient = createConnector(pixVO.getConveniocobranca());
        if (httpClient == null) {
            throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
        }

        String token = "";
        try {
            token = token(httpClient, pixVO.getConveniocobranca());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }

        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para cancelar a cobrança");
        }

        String url = apiUrl() + "/cob/" + pixVO.getTxid();
        HttpPatch patch = new HttpPatch(url);

        patch.addHeader("Content-Type", "application/json");
        patch.addHeader("Authorization", "Bearer " + token);
        String body = "{\"status\": \"REMOVIDA_PELO_USUARIO_RECEBEDOR\"}";

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body);

        StringEntity params = new StringEntity(body);
        patch.setEntity(params);

        HttpResponse response = httpClient.execute(patch);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public HttpClient createConnector() {
        return null;
    }

    @Override
    public HttpClient createConnector(String path, String senha) {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception {

        HttpClient httpClient = createConnector(pixVO.getConveniocobranca());
        if (httpClient == null) {
            throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
        }

        String token = "";
        try {
            token = token(httpClient, pixVO.getConveniocobranca());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }

        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para consultar a cobrança");
        }

        String url = apiUrl() + "/cob/" + pixVO.getTxid();

        HttpGet get = new HttpGet(url);
        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token);

        HttpResponse response = httpClient.execute(get);
        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception {

        HttpClient httpClient = createConnector(pixVO.getConveniocobranca());
        if (httpClient == null) {
            throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
        }

        String token = "";
        try {
            token = token(httpClient, pixVO.getConveniocobranca());
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }

        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para cancelar a cobrança");
        }

        String url = apiUrl() + "/pix/" + pixVO.getE2eId();
        HttpGet get = new HttpGet(url);
        get.addHeader("Content-Type", "application/json");
        get.addHeader("Authorization", "Bearer " + token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();

        HttpResponse response = httpClient.execute(get);

        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        return pixRequisicaoDto;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                          String nomeDevedor, String telefoneDevedor,
                                          Double valor, String descricao, Integer expiracao) throws Exception {
        PixCalendarioDto pixCalendarioDto = new PixCalendarioDto(expiracao);

        PixDto pixDto = new PixDto();
        pixDto.setCalendario(pixCalendarioDto);
        pixDto.setChave(convenioCobrancaVO.getPixChave());

        TipoPessoa tipoPessoa = cpfDevedor.length() == 14 ? TipoPessoa.JURIDICA : TipoPessoa.FISICA;

        PixDevedorDto pixDevedorDto = new PixDevedorDto();
        if (tipoPessoa == TipoPessoa.FISICA) {
            pixDevedorDto.setCpf(cpfDevedor.replaceAll("\\.|-", ""));
        } else {
            //JURIDICA
            pixDevedorDto.setCnpj(cpfDevedor.replaceAll("\\.|-", ""));
        }
        pixDevedorDto.setNome(Uteis.removerCaracteresNaoAscii(nomeDevedor).replace("-", " ").replace("_", " "));
        pixDto.setDevedor(pixDevedorDto);

        pixDto.setSolicitacaoPagador(descricao);
        PixValorDto pixValorDto = new PixValorDto();
        pixValorDto.setOriginal(Uteis.formatarValorEmRealSemAlterarPontuacao(valor));
        pixDto.setValor(pixValorDto);

        String txId = gerarTxId();
        pixDto.setTxid(txId);

        PixRequisicaoDto retornoPixDto = criarCobranca(convenioCobrancaVO, pixDto);
        retornoPixDto.getPixDto().setTextoImagemQRcode(retornoPixDto.getPixDto().getPixCopiaECola());
        retornoPixDto.getPixDto().getDevedor().setTelefone(UteisTelefone.removerCaracteresEspeciais(telefoneDevedor));
        return retornoPixDto;
    }

    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception {

        HttpClient httpClient = createConnector(convenioCobrancaVO);
        if (httpClient == null) {
            throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
        }

        String token = "";
        try {
            token = token(httpClient, convenioCobrancaVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }

        if (UteisValidacao.emptyString(token)) {
            throw new Exception("Token não informado para gerar a cobrança");
        }

        String url = apiUrl() + "/cob/" + pixDto.getTxid();
        HttpPut put = new HttpPut(url);

        put.addHeader("Content-Type", "application/json");
        put.addHeader("Authorization", "Bearer " + token);

        PixRequisicaoDto pixRequisicaoDto = new PixRequisicaoDto();
        pixRequisicaoDto.setEnvio(body(pixDto));
        StringEntity params = new StringEntity(pixRequisicaoDto.getEnvio());
        put.setEntity(params);

        HttpResponse response = httpClient.execute(put);
        responseCobrancaDto(pixRequisicaoDto, response);
        return pixRequisicaoDto;
    }

    public String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {

        String token = "";

        //verificar se já existe token gerado para reutilizar
        Token tokenDAO;
        try {
            tokenDAO = new Token(getCon());
            TokenVO tokenReutilizar = tokenDAO.consultarAptoParaReutilizacao(convenioCobrancaVO.getCodigo(), TipoTokenEnum.PIX_INTER);
            if (tokenReutilizar != null && !UteisValidacao.emptyString(tokenReutilizar.getAccess_token())) {
                token = tokenReutilizar.getAccess_token();
                tokenDAO.incrementarUtilizacao(tokenReutilizar.getCodigo());
                return token;
            }
        } catch (Exception ex) {
        } finally {
            tokenDAO = null;
        }

        //Não retornou um token que já existe apto para reutilizar então vai gerar um novo
        try {
            String url = apiAuthUrl() + "/token";
            HttpPost post = new HttpPost(url);

            post.addHeader("Content-Type", "application/x-www-form-urlencoded");

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("client_id", convenioCobrancaVO.getPixClientId()));
            params.add(new BasicNameValuePair("client_secret", convenioCobrancaVO.getPixClientSecret()));
            params.add(new BasicNameValuePair("grant_type", "client_credentials"));
            params.add(new BasicNameValuePair("scope", "cob.write cob.read cobv.write cobv.read pix.write pix.read webhook.read webhook.write pagamento-pix.write pagamento-pix.read webhook-banking.write webhook-banking.read"));
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado os certificados público e privado no convênio.");
            }

            HttpResponse response = httpClient.execute(post);
            token = responseTokenDto(response, convenioCobrancaVO).getAccess_token();

            return token;
        } catch (Exception ex) {
            throw new Exception("Erro ao gerar Token: " + ex.getMessage());
        }
    }

    private HttpClient createConnector(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {

        //usar certificado privado gerado através dos 2 certificados (público e privado) configurados dentro do convênio
        String nomeArquivoSemExtensao = "";
        String nomeArquivoOriginal = "";
        byte[] certificadoPFX = null;

        if (!UteisValidacao.emptyList(convenioCobrancaVO.getListaConvenioCobrancaArquivoVO())) {
            for (ConvenioCobrancaArquivoVO item : convenioCobrancaVO.getListaConvenioCobrancaArquivoVO()) {
                if (item.getArquivo() != null && item.getNomeArquivoOriginal().contains(".pfx")) {
                    String[] nomeArq = convenioCobrancaVO.getListaConvenioCobrancaArquivoVO().get(0).getNomeArquivoOriginal().split("[.]");
                    nomeArquivoSemExtensao = nomeArq[0];
                    nomeArquivoOriginal = item.getNomeArquivoOriginal();
                    certificadoPFX = item.getArquivo();
                    break;
                }
            }
        } else {
            throw new Exception("Não encontrei nenhum arquivo/certificado cadastrado para o convênio: " + convenioCobrancaVO.getDescricao());
        }

        if (certificadoPFX == null) {
            throw new Exception("Não foi possível obter o certificado para criar o conector");
        }

        if (UteisValidacao.emptyString(nomeArquivoSemExtensao) || UteisValidacao.emptyString(nomeArquivoOriginal)) {
            throw new Exception("Não foi possível obter o nome ou extensão do certificado para criar o conector");
        }

        //usar certificado privado configurado dentro do convênio
        return ExecuteRequestHttpService.createConnector(certificadoPFX,
                nomeArquivoSemExtensao,
                FilenameUtils.getExtension(nomeArquivoOriginal),
                senhaPrivateCert());

    }

    private String senhaPrivateCert() {
        return Uteis.desencriptar(PropsService.getPropertyValue(PropsService.senhaDecriptPCertPrivadoPfx), PropsService.getPropertyValue(PropsService.chaveCriptCertPrivado));
    }

    public String gerarTxId() {
        //No Inter é obrigatório informar o txId para gerar o pix
        String txId = UUID.randomUUID().toString().replaceAll("-", "");
        if (txId.length() > 35) {
            txId = txId.substring(0, 35);
        }
        return txId;
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    @Override
    public void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        pixRequisicaoDto.setResposta(responseJsonString);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        Gson json = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss").create();
        pixRequisicaoDto.setPixDto(json.fromJson(responseJsonString, PixDto.class));

        //setar a data de pagamento/quitação do pix que vem no json de retorno quando o pix está pago
        try {
            JSONObject retornoJSON = null;
            retornoJSON = new JSONObject(responseJsonString);
            String dataPagamentoString = retornoJSON.getJSONArray("pix").getJSONObject(0).getString("horario");
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date dataPagamento = sdf.parse(dataPagamentoString);
            pixRequisicaoDto.getPixDto().setDataPagamento(dataPagamento);
        } catch (Exception ignore) {
        }
    }

    @Override
    public PixResponseErrorDto responseErrorDto(String responseJson) {
        Gson json = new GsonBuilder().create();
        return json.fromJson(responseJson, PixResponseErrorDto.class);
    }

    @Override
    public String translateMessages(String message) {
        return message;
    }

    @Override
    public void validateResponseError(String responseJsonString, int status) throws PixRequestException {
        if (status != 200 && status != 201) {
            String mensagemErro = "";
            try {
                PixResponseErrorDto pixResponseErrorDto = responseErrorDto(responseJsonString);

                //mensagem geral
                if (pixResponseErrorDto != null) {
                    if (!UteisValidacao.emptyString(pixResponseErrorDto.getError_title())) {
                        mensagemErro = pixResponseErrorDto.getError_title();
                    } else if (!UteisValidacao.emptyString(pixResponseErrorDto.getMessage())) {
                        mensagemErro = pixResponseErrorDto.getMessage();
                    }
                }

                //tratamento específico
                if (status == 401 && mensagemErro.equals("Login/senha inválido")) {
                    mensagemErro = "Client ID ou Client Secret configurado(s) no convênio de cobrança está/estão incorreto(s)!";
                }
                if (status == 400 ) {
                    if (responseJsonString.contains("violacoes")) {
                        JSONObject json = new JSONObject(responseJsonString);
                        String violacoes = json.optJSONArray("violacoes").toString();
                        if (violacoes.contains("cob.chave corresponde a uma conta que não pertence a este usuário recebedor")) {
                            mensagemErro = "A chave pix informada lá no convênio de cobrança Pix Inter é inválida ou não existe.";
                        } else {
                            mensagemErro = violacoes;
                        }
                    } else if (responseJsonString.contains("Invalid parameters error in key query")) {
                        mensagemErro = "Confira a chave pix cadastrada lá no convênio de cobrança. Certifique-se de que ela realmente exista e esteja configurada corretamente.";
                    } else {
                        mensagemErro = responseJsonString;
                    }
                }
            } catch (Exception e) {
                mensagemErro = responseJsonString;
            }
            throw new PixRequestException(mensagemErro);
        }
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response) throws Exception {
        return null;
    }

    @Override
    public TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        int status = response.getStatusLine().getStatusCode();
        validateResponseError(responseJsonString, status);
        Gson json = new Gson();

        TokenVO tokenVO = json.fromJson(responseJsonString, TokenVO.class);
        tokenVO.setData_gerado(Calendario.hoje());
        tokenVO.setVezes_utilizado(1);
        tokenVO.setTipoTokenEnum(TipoTokenEnum.PIX_INTER);
        tokenVO.setConvenioCobranca(convenioCobrancaVO.getCodigo());

        Token tokenDAO;
        try {
            tokenDAO = new Token(getCon());
            tokenVO.setCodigo(tokenDAO.incluir(tokenVO).getCodigo());
        } catch (Exception ex) {
            throw ex;
        } finally {
            tokenDAO = null;
        }
        return tokenVO;
    }


    private String apiUrl() {
        return PropsService.getPropertyValue(PropsService.urlApiPixInterProducao);
    }

    private String apiAuthUrl() {
        return PropsService.getPropertyValue(PropsService.urlApiPixInterOAuthProducao);
    }

    @Override
    public JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            HttpClient httpClient = createConnector(convenioCobrancaVO);
            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
            }

            String token = "";
            try {
                token = token(httpClient, convenioCobrancaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }

            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para consultar o webhook do pix!");
            }

            String url = apiUrl() + "/webhook/" + convenioCobrancaVO.getPixChave().replace("+", "");
            HttpGet get = new HttpGet(url);
            get.addHeader("Content-Type", "application/json");
            get.addHeader("Authorization", "Bearer " + token);

            HttpResponse response = httpClient.execute(get);
            String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            return new JSONObject(responseJsonString);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception {
        boolean sucesso = false;
        try {

            HttpClient httpClient = createConnector(convenioCobrancaVO);
            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
            }

            String token = "";
            try {
                token = token(httpClient, convenioCobrancaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }

            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para consultar o webhook do pix!");
            }

            String url = apiUrl() + "/webhook/" + convenioCobrancaVO.getPixChave().replace("+", "");

            HttpPut put = new HttpPut(url);
            put.addHeader("Content-Type", "application/json");
            put.addHeader("Authorization", "Bearer " + token);

            //URL de callback NOSSA que o banco vai chamar nas requisições
            JSONObject body = new JSONObject();
            body.put("webhookUrl", urlCallback);

            StringEntity params = new StringEntity(body.toString());
            put.setEntity(params);

            HttpResponse response = httpClient.execute(put);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 204) {
                sucesso = true;
            }

            return sucesso;

        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            HttpClient httpClient = createConnector(convenioCobrancaVO);
            if (httpClient == null) {
                throw new Exception("Não foi possível obter a conexão. Verifique se está configurado o certificado no convênio.");
            }

            String token = "";
            try {
                token = token(httpClient, convenioCobrancaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw ex;
            }

            if (UteisValidacao.emptyString(token)) {
                throw new Exception("Token não informado para consultar o webhook do pix!");
            }

            String url = apiUrl() + "/webhook/" + convenioCobrancaVO.getPixChave().replace("+", "");
            HttpDelete delete = new HttpDelete(url);
            delete.addHeader("Content-Type", "application/json");
            delete.addHeader("Authorization", "Bearer " + token);

            HttpResponse response = httpClient.execute(delete);
            int statusCode = response.getStatusLine().getStatusCode();

            //quando excluir com sucesso eles retornam (204 No Content)
            if (statusCode == 204) {
                return true;
            }

            //chegou até aqui, não deu certo, então lançar exceção com a response da request em String
            //tentar obter motivo do erro
            StringBuilder sb = new StringBuilder();
            try {
                String responseJsonString = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                JSONObject jsonObject = new JSONObject(responseJsonString);
                sb.append(jsonObject.getString("title"));
            } catch (Exception ex) {
            }

            if (UteisValidacao.emptyString(sb.toString())) {
                throw new Exception("Não foi possível excluir o webhook. Motivo não informado pelo banco.");
            } else {
                throw new Exception(sb.toString());
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor, String nomeDevedor, String telefoneDevedor, Double valor, String descricao, Integer expiracao, String chave) throws Exception {
        return null;
    }

    @Override
    public PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PessoaVO pessoaVO, Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception {
        return null;
    }

    @Override
    public String token(PixVO pixVO) {
        return null;
    }

    @Override
    public String token(String basicToken, AmbienteEnum ambiente) throws Exception {
        return null;
    }

    @Override
    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        return null;
    }

    public String gerarTextoQrCode(PixRequisicaoDto pixRequisicaoDto, ConvenioCobrancaVO convenioCobrancaVO) {
        return "";
    }

    @Override
    public String fixResponseErros(String responseJson) {
        return null;
    }

    @Override
    public PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) {
        return null;
    }
}
