package servicos.remessa.dao;

import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Entidade que representa os dados de criptografia de uma remessa no banco centralizado de remessas usado pelo Facilite
 *
 * <AUTHOR>
 * @since 15/10/2018
 */
public class RemessaCriptografia extends SuperEntidadeRemessaTransaction {

    private Integer codigo;
    private TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum;
    private String nomeChave;
    private String chave;

    public RemessaCriptografia() {
        this(null, null, null, null);
    }

    public RemessaCriptografia(Integer codigo, TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum, String nomeChave, String chave) {
        this.codigo = codigo;
        this.tipoConvenioCobrancaEnum = tipoConvenioCobrancaEnum;
        this.nomeChave = nomeChave;
        this.chave = chave;
    }

    public RemessaCriptografia consultarPorTipoConvenio(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) throws Exception {
        if (tipoConvenioCobrancaEnum == null) {
            return null;
        }

        return consultarPorCodigoTipoConvenio(tipoConvenioCobrancaEnum.getCodigo());
    }

    public RemessaCriptografia consultarPorCodigoTipoConvenio(Integer codigoTipoConvenio) throws Exception {
        final String SQL_CONSULTA_CODIGO_TIPO_CONVENIO = "SELECT * FROM remessa_criptografia WHERE tipo_convenio = ?";
        return montarDados(prepararStatement(SQL_CONSULTA_CODIGO_TIPO_CONVENIO, new Object[]{codigoTipoConvenio}).executeQuery());
    }

    private RemessaCriptografia montarDados(ResultSet resultado) throws SQLException {
        if (!resultado.next()) {
            return null;
        }

        return new RemessaCriptografia(
                resultado.getInt("codigo"),
                TipoConvenioCobrancaEnum.valueOf(resultado.getInt("tipo_convenio")),
                resultado.getString("nome_chave"),
                resultado.getString("chave"));
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public TipoConvenioCobrancaEnum getTipoConvenioCobrancaEnum() {
        return tipoConvenioCobrancaEnum;
    }

    public void setTipoConvenioCobrancaEnum(TipoConvenioCobrancaEnum tipoConvenioCobrancaEnum) {
        this.tipoConvenioCobrancaEnum = tipoConvenioCobrancaEnum;
    }

    public String getNomeChave() {
        return nomeChave;
    }

    public void setNomeChave(String nomeChave) {
        this.nomeChave = nomeChave;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }
}
