/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.http;

import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import servicos.util.ExecuteRequestHttpService;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 29/04/2020
 */
public class RequestHttpService {

    private String charsetSend = "UTF-8";
    private String charsetResponse = "UTF-8";
    public Integer connectTimeout = 0;
    public Integer connectionRequestTimeout = 0;


    public RespostaHttpDTO executeRequest(String url, Map<String, String> headers, Map<String, String> params,
                                        String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        return executeRequest(url, headers, params, null, body, null, metodoHttpEnum);
    }

    public RespostaHttpDTO executeRequest(String url, Map<String, String> headers,
                                        Map<String, String> params, String encodingParams,
                                        String body, String encodingBody, MetodoHttpEnum metodoHttpEnum) throws Exception {

        if (headers == null) {
            headers = new HashMap<>();
        }

        if (params == null) {
            params = new HashMap<>();
        }

        //adiconar os params
        url += getParamsString(params, encodingParams);


        //adiconar o body
        StringEntity entity = null;
        if (!UteisValidacao.emptyString(body)) {
            if (UteisValidacao.emptyString(encodingBody)) {
                encodingBody = charsetSend;
            }
            entity = new StringEntity(body, encodingBody);
        }

        if (metodoHttpEnum == null) {
            throw new Exception("Método HTTP não informado");
        }

        HttpResponse httpResponse = null;
        HttpClient httpClient = null;
        if (!UteisValidacao.emptyNumber(connectTimeout)) {
            if (UteisValidacao.emptyNumber(connectionRequestTimeout)) {
                connectionRequestTimeout = 120000;
            }
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectionRequestTimeout).setConnectTimeout(connectTimeout).build();
            httpClient = ExecuteRequestHttpService.createConnector(requestConfig);
        } else {
            httpClient = ExecuteRequestHttpService.createConnector();
        }

        switch (metodoHttpEnum) {
            case GET:

                HttpGet httpGet = new HttpGet(url);
                for (String keyHeader : headers.keySet()) {
                    httpGet.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpGet);
                break;

            case POST:

                HttpPost httpPost = new HttpPost(url);
                if (entity != null) {
                    httpPost.setEntity(entity);
                }
                for (String keyHeader : headers.keySet()) {
                    httpPost.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpPost);
                break;

            case PUT:

                HttpPut httpPut = new HttpPut(url);
                if (entity != null) {
                    httpPut.setEntity(entity);
                }
                for (String keyHeader : headers.keySet()) {
                    httpPut.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpPut);
                break;

            case DELETE:
                HttpDelete httpDelete = new HttpDelete(url);
                for (String keyHeader : headers.keySet()) {
                    httpDelete.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpDelete);
                break;

            case PATCH:
                HttpPatch httpPatch = new HttpPatch(url);
                if (entity != null) {
                    httpPatch.setEntity(entity);
                }
                for (String keyHeader : headers.keySet()) {
                    httpPatch.addHeader(keyHeader, headers.get(keyHeader));
                }
                httpResponse = httpClient.execute(httpPatch);
                break;
        }

        RespostaHttpDTO respostaHttpDTO = new RespostaHttpDTO();
        if (httpResponse != null) {
            int httpStatus = httpResponse.getStatusLine().getStatusCode();
            respostaHttpDTO.setHttpStatus(httpStatus);
            try {
                respostaHttpDTO.setResponse(EntityUtils.toString(httpResponse.getEntity(), charsetResponse));
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            try {
                respostaHttpDTO.setHeaders(new HashMap<>());
                for (Header headersResp : httpResponse.getAllHeaders()) {
                    respostaHttpDTO.getHeaders().put(headersResp.getName(), headersResp.getValue());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return respostaHttpDTO;
    }

    private String getParamsString(Map<String, String> params, String encodingParams) throws UnsupportedEncodingException {
        if (params == null || params.isEmpty()) {
            return "";
        }

        if (UteisValidacao.emptyString(encodingParams)) {
            encodingParams = charsetSend;
        }

        StringBuilder result = new StringBuilder();
        boolean first = true;
        for (String keyParams : params.keySet()) {
            if (first) {
                first = false;
                result.append("?");
            } else {
                result.append("&");
            }
            result.append(URLEncoder.encode(keyParams, encodingParams));
            result.append("=");
            result.append(URLEncoder.encode(params.get(keyParams), encodingParams));
        }
        return result.toString();
    }
}
