package test.simulacao;

import br.com.pactosolucoes.enumeradores.TipoOperacaoCreditoTreinoEnum;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ControleCreditoTreinoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.contrato.ControleCreditoTreino;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoDWVO;
import servicos.integracao.TreinoWSConsumer;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by ulisses on 13/09/2016.
 */
public class CorrecaoSynergy {


    public static void main(String... args) {
        try{
            System.out.println("INICIO PROCESSO ");

            Connection bdZillyon = obterConexaoBDSynergy();
            corrigirLancamentoDeFaltaPeloRobo(bdZillyon);

            //verificarFaltaAlunoAcessouAntesDoHorario(bdZillyon);
            //SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");

            //executarRobo_verificarNaoComparecimentoAulaCreditoTreino(bdZillyon, sdf.parse("14/09/2016"));

            System.out.println("FIM PROCESSO");
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private static void corrigirLancamentoDeFaltaPeloRobo(Connection connection)throws Exception{
        /*
          Pode haver erro no zillyonAcesso onde não é enviado os acessos no mesmo dia. Desta forma, ao rodar o robô é gerado uma operação de FALTA.
          Portanto quando o zillyonAcesso enviar este acesso, devemos excluir a FALTA gerada pelo Robô.
         */
        StringBuilder sql = new StringBuilder();
        sql.append("select contrato, contrato.pessoa, cast(dataOperacao as date) as dataOperacao, count(*) as total \n");
        sql.append("from controleCreditoTreino  \n");
        sql.append("inner join contrato on contrato.codigo = controleCreditoTreino.contrato  \n");
        sql.append("where dataOperacao >= '2016-10-11' and tipoOperacaoCreditoTreino in(").append(TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo()).append(",").append(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo()).append(") and contrato.vigenciaAteAjustada > current_date and controleCreditoTreino.observacao is null  \n");
        sql.append("group by contrato, contrato.pessoa, cast(dataOperacao as date)  \n");
        sql.append("having count(*) > 1 ");
        sql.append("order by dataOperacao ");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        ControleCreditoTreino controleCreditoTreinoDao = new ControleCreditoTreino(connection);
        while (rs.next()){

            StringBuilder sql2 = new StringBuilder();
            sql2.delete(0, sql.length());
            sql2.append("select * \n");
            sql2.append("from controleCreditoTreino \n");
            sql2.append("where contrato = ").append(rs.getInt("contrato"));
            sql2.append(" and cast(dataOperacao as date) =  '").append(sdf.format(rs.getDate("dataOperacao"))).append("' ");
            sql2.append(" and tipoOperacaoCreditoTreino in(2, 3)");
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(sql2.toString());

            boolean temAcesso = false;
            List<ControleCreditoTreinoVO> listaExcluir = new ArrayList<ControleCreditoTreinoVO>();
            while (resultSet.next()){
                if (TipoOperacaoCreditoTreinoEnum.getTipo(resultSet.getInt("tipoOperacaoCreditoTreino")) == TipoOperacaoCreditoTreinoEnum.UTILIZACAO){
                    temAcesso = true;
                }else{
                    ControleCreditoTreinoVO obj = new ControleCreditoTreinoVO();
                    obj.setCodigo(resultSet.getInt("codigo"));
                    obj.setDatalancamento(resultSet.getTimestamp("dataLancamento"));
                    obj.setDataOperacao(resultSet.getTimestamp("dataOperacao"));
                    listaExcluir.add(obj);
                }
            }
            if ((temAcesso) && (listaExcluir.size() > 0)) {
                for (ControleCreditoTreinoVO obj: listaExcluir){
                    controleCreditoTreinoDao.excluirPorCodigo(obj.getCodigo());
                    atualizarSaldoCredito(connection,rs.getInt("contrato"), rs.getInt("pessoa"));

                }
            }
            if (listaExcluir.size() > 0){
                incluirLogExclusaoFalta(connection, listaExcluir, rs.getInt("contrato"), rs.getInt("pessoa"));
            }
        }

    }

    private static Integer consultarSaldo(Connection con, Integer codigoContrato)throws Exception{
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery("select sum(quantidade) as saldoCredito from controleCreditoTreino where contrato = " + codigoContrato);
        rs.next();
        return rs.getInt("saldoCredito");
    }
    private static ResultSet consultarQuantidadeCompraCredito(Connection con, Integer codigoContrato)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select quantidadeCreditoCompra, quantidadeDiasExtra, cli.codigo as codigoCliente ");
        sql.append("from contratoDuracaoCreditoTreino cdc ");
        sql.append("inner join contratoDuracao cd on cd.codigo = cdc.contratoDuracao ");
        sql.append("inner join contrato c on c.codigo = cd.contrato ");
        sql.append("inner join cliente cli on cli.pessoa = c.pessoa ");
        sql.append("where cd.contrato = ").append(codigoContrato);
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        rs.next();
        return rs;
    }


    private static void atualizarSaldoCredito(Connection con, Integer codigoContrato, Integer codigoPessoa)throws Exception{
        Integer saldo = consultarSaldo(con, codigoContrato);
        ResultSet rsCompra = consultarQuantidadeCompraCredito(con, codigoContrato);
        Integer qtdeCompra = rsCompra.getInt("quantidadeCreditoCompra");
        Integer diasExtra = rsCompra.getInt("quantidadeDiasExtra");
        Integer codigoCliente = rsCompra.getInt("codigoCliente");
        // Atualizar o saldo de créditos na tabela SituacaoClienteSinteticoDW
        StringBuilder sqlAlterar = new StringBuilder();
        sqlAlterar.delete(0, sqlAlterar.length());
        sqlAlterar.append("UPDATE SituacaoClienteSinteticoDW set validarSaldoCreditoTreino=true, ");
        sqlAlterar.append("saldoCreditoTreino = ").append(saldo).append(", ");
        sqlAlterar.append("totalCreditoTreino = ").append(qtdeCompra).append(", ");
        sqlAlterar.append("quantidadeDiasExtra = ").append(diasExtra);
        sqlAlterar.append("where codigopessoa = ").append(codigoPessoa);
        Statement st = con.createStatement();
        st.execute(sqlAlterar.toString());

        // Atualizar o saldo de créditos na tabela contratoDuracaoCreditoTreino
        StringBuilder sqlAlt = new StringBuilder();
        sqlAlt.append(" update contratoDuracaoCreditoTreino set quantidadeCreditoDisponivel = ").append(saldo);
        sqlAlt.append(" from contratoDuracao cd  \n");
        sqlAlt.append(" where cd.codigo = contratoDuracaoCreditoTreino.contratoDuracao \n");
        sqlAlt.append(" and cd.contrato = ").append(codigoContrato);
        Statement stAlt = con.createStatement();
        stAlt.execute(sqlAlt.toString());

        //TreinoWSConsumer.sincronizarCreditosAluno("ea01163c80fb28a5a8992971b28db388", codigoCliente, saldo, qtdeCompra);

    }

    private static void incluirLogExclusaoFalta(Connection connection, List<ControleCreditoTreinoVO> listaExcluir, Integer codigoContrato, Integer codigoPessoa) throws Exception {
        try{
            Usuario usuarioDao = new Usuario(connection);
            Log logDao = new Log(connection);
            UsuarioVO usuarioVO = usuarioDao.getUsuarioRecorrencia();
            LogVO logVO = new LogVO();
            logVO.setOperacao("ALTERAÇÃO");
            logVO.setChavePrimaria(String.valueOf(codigoContrato));
            logVO.setNomeEntidade("Contrato");
            logVO.setOperacao("ALTERAÇÃO - EXCLUSÃO DE FALTA(CORRECAO)");
            logVO.setNomeEntidadeDescricao("Contrato");
            logVO.setResponsavelAlteracao(usuarioVO.getNome());
            logVO.setUserOAMD(usuarioVO.getUserOamd());
            logVO.setNomeCampo("Campo(s)");
            logVO.setDataAlteracao(negocio.comuns.utilitarias.Calendario.hoje());
            StringBuilder msgLog = new StringBuilder();
            msgLog.append("Pode haver erro no zillyonAcesso onde não é enviado os acessos no mesmo dia. Desta forma, ao rodar o robô é gerado uma operação de FALTA. \n");
            msgLog.append("Portanto quando o zillyonAcesso enviar este acesso, devemos excluir a FALTA gerada pelo Robô. \n\n\n");
            msgLog.append("OPERAÇÃO EXCLUIDA: FALTA \n");
            for (ControleCreditoTreinoVO controleCreditoTreinoVO: listaExcluir){
                msgLog.append("DATA DE LANÇAMENTO DA FALTA:").append(controleCreditoTreinoVO.getDataLancamento_Apresentar()).append("\n");
                msgLog.append("DATA PREVISTA DA AULA:").append(controleCreditoTreinoVO.getDataOperacao_Apresentar()).append("\n\n");
            }
            logVO.setValorCampoAlterado(msgLog.toString());
            logVO.setPessoa(codigoPessoa);

            logDao.incluirSemCommit(logVO);

        } catch (Exception e) {
            Logger.getLogger(CorrecaoSynergy.class.getSimpleName()).log(Level.SEVERE, "#### ERRO AO GRAVAR LOG DE EXLUSÃO DE NÃO COMPARECIMENTO AULA DE CREDITO. CONTRATO =" + codigoContrato);
        }
    }


    public static void executarRobo_verificarNaoComparecimentoAulaCreditoTreino(Connection connection, Date dataRobo)throws Exception{
        try{
            ControleCreditoTreino controleCreditoTreinoDao = new ControleCreditoTreino(connection);
            controleCreditoTreinoDao.verificarNaoComparecimentoAulaCreditoTreino(dataRobo);
        }catch (Exception e){
            StringBuilder sb = new StringBuilder();
            sb.append(" - ERRO: ").append(e.getMessage());
            Uteis.logar(null, sb.toString());
        }

    }

    public static void verificarFaltaAlunoAcessouAntesDoHorario(Connection connection)throws Exception{
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dataBase = sdf.parse("2016-08-01");
        Date dataAtual = Calendario.getDataComHoraZerada(Calendario.hoje());
        List<ClienteTeste> listaCliente = consultarAlunosCredito(connection);
        for (ClienteTeste clienteTeste: listaCliente){
            dataBase = sdf.parse("2016-08-01");
            while (dataAtual.compareTo(dataBase) != 0){
                dataBase = Uteis.somarDias(dataBase, 1);
                verificaAlunoTemFaltaEAcessoNoMesmoDia(dataBase,clienteTeste,connection);
            }
        }

        for (ClienteTeste clienteTeste: listaCliente){
            if (clienteTeste.getListaDataComErro().size() > 0){
                System.out.println("MATRICULA: " + clienteTeste.getCodigoMatricula() + " - " + clienteTeste.getNomeCliente());
                System.out.println("DATAS COM ERRO: ");
                for (String data: clienteTeste.getListaDataComErro()){
                    System.out.println(data);
                }
            }
        }

    }

    public static void verificaAlunoTemFaltaEAcessoNoMesmoDia(Date dataBase, ClienteTeste clienteTeste, Connection connection)throws Exception{
        StringBuilder sql = new StringBuilder();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sql.append("select * from controlecreditotreino where tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.NAO_COMPARECIMENTO.getCodigo());
        sql.append(" and contrato = ").append(clienteTeste.getCodigoContrato());
        sql.append(" and cast(dataOperacao as date) = '").append(sdf.format(dataBase)).append("'");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()){
            sql.delete(0, sql.length());
            sql.append("select * from controlecreditotreino where tipoOperacaoCreditoTreino = ").append(TipoOperacaoCreditoTreinoEnum.UTILIZACAO.getCodigo());
            sql.append(" and contrato = ").append(clienteTeste.getCodigoContrato());
            sql.append(" and cast(dataOperacao as date) = '").append(sdf.format(dataBase)).append("'");
            Statement stUti = connection.createStatement();
            ResultSet rsUti = st.executeQuery(sql.toString());
            if (rsUti.next()){
                clienteTeste.getListaDataComErro().add(sdf.format(dataBase));
            }

        }

    }


    public static List<ClienteTeste> consultarAlunosCredito(Connection connection)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT c.codigo as codigoContrato, cli.codigoMatricula, p.nome \n");
        sql.append("FROM Contrato c \n");
        sql.append("inner join SituacaoClienteSinteticoDW sintetico on sintetico.codigoContrato = c.codigo \n");
        sql.append("inner join cliente cli on cli.codigo = sintetico.codigoCliente \n");
        sql.append("inner join pessoa p on p.codigo = cli.pessoa \n");
        sql.append("WHERE c.vendaCreditoTreino = true \n");
        sql.append("and sintetico.situacaoContrato = 'NO' \n");
        sql.append("order by p.nome ");
        Statement st = connection.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        List<ClienteTeste> lista = new ArrayList<ClienteTeste>();
        while (rs.next()){
            ClienteTeste clienteTeste = new CorrecaoSynergy(). new ClienteTeste();
            clienteTeste.setCodigoContrato(rs.getInt("codigoContrato"));
            clienteTeste.setCodigoMatricula(rs.getString("codigoMatricula"));
            clienteTeste.setNomeCliente(rs.getString("nome"));
            lista.add(clienteTeste);
        }
        return lista;
    }


    public static Connection obterConexaoBDSynergy(){
        try{
            String url = "********************************************************************";
            String driver = "org.postgresql.Driver";
            String userBD = "postgres";
            String passwordBD = "pactodb";
            Class.forName(driver);
            Connection connection = DriverManager.getConnection(url, userBD, passwordBD);
            Conexao.guardarConexaoForJ2SE("synergy", connection);
            return  connection;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public class ClienteTeste{

        private String codigoMatricula;
        private Integer codigoContrato;
        private String nomeCliente;
        private Set<String> listaDataComErro = new HashSet<String>();

        public String getCodigoMatricula() {
            return codigoMatricula;
        }

        public void setCodigoMatricula(String codigoMatricula) {
            this.codigoMatricula = codigoMatricula;
        }

        public Integer getCodigoContrato() {
            return codigoContrato;
        }

        public void setCodigoContrato(Integer codigoContrato) {
            this.codigoContrato = codigoContrato;
        }

        public String getNomeCliente() {
            return nomeCliente;
        }

        public void setNomeCliente(String nomeCliente) {
            this.nomeCliente = nomeCliente;
        }

        public Set<String> getListaDataComErro() {
            return listaDataComErro;
        }

        public void setListaDataComErro(Set<String> listaDataComErro) {
            this.listaDataComErro = listaDataComErro;
        }
    }
}
