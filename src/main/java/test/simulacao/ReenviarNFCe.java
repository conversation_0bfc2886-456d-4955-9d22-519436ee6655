package test.simulacao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.nfe.NotaFiscalConsumidorNFCeVO;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.nfe.enumerador.ResultadoEnvioNFSeEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.NotaFiscalConsumidorEletronica;
import negocio.facade.jdbc.nfe.LoteNFe;
import negocio.facade.jdbc.nfe.NotaFiscalDeServico;
import servicos.propriedades.PropsService;

import java.sql.Connection;

public class ReenviarNFCe {


    public static void main(String args[]) throws Exception {

        DAO dao =  new DAO();
        Connection con = dao.obterConexaoEspecifica("");

        NotaFiscalDeServico notaFiscalDeServicoDAO = new NotaFiscalDeServico(con);
        NotaFiscalConsumidorEletronica notaFiscalConsumidorEletronicaDAO = new NotaFiscalConsumidorEletronica(con);

        //LISTA DEVE SER A LISTA DOS ID_NFCE !!!

//        String dataEmissao =  "2018-10-31 00:00:00"; //yyyy-MM-dd HH:mm:ss
        String dataEmissao =  ""; //data em branco envia com a data atual
//        String listaIdNFCe =  "4013059,4013060,4013061,4013062,4013063,4013064,4013065";
        String listaIdNFCe =  "22459";

        String[] arrayNFCe = listaIdNFCe.split(",");

        for (String nfce : arrayNFCe) {
            try {
                NotaFiscalConsumidorNFCeVO notaFiscalConsumidorNFCeVO = notaFiscalDeServicoDAO.consultarPorChavePrimariaNFCe(Integer.parseInt(nfce), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                JSONArray jsonArray = lancarCamposAlteradosNFCe(notaFiscalConsumidorNFCeVO.getNumeroEnvio(), dataEmissao);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("idNFCe", notaFiscalConsumidorNFCeVO.getId_NFCe());
                jsonObject.put("alteracoes", jsonArray);
                RetornoEnvioNotaFiscalTO retornoEnvioNotaFiscalTO = notaFiscalConsumidorEletronicaDAO.reenviarNFCe(jsonObject, notaFiscalConsumidorNFCeVO.getId_NFCe());
                if (!retornoEnvioNotaFiscalTO.getResultadoEnvio().equals(ResultadoEnvioNFSeEnum.OK)) {
                    System.out.println("Ocorreu um erro ao reeviar a NFCe! ID_NFCe " + nfce + " | ERRO: " + retornoEnvioNotaFiscalTO.getResultadoEnvio());
                }else {
                    System.out.println("Reenviado ID_NFCe: " + nfce);
                }
            } catch (Exception ex) {
                System.out.println("ERRO: ID_NFCe " + nfce + " | ERRO: "  + ex.getMessage());
            }
        }
    }

    private static JSONArray lancarCamposAlteradosNFCe(String numeroEnvio, String dataEmissao) throws Exception {
        JSONArray camposAlterados = new JSONArray();
        camposAlterados.put(montarCamposAlterados("NumeroEnvio", numeroEnvio, true));
        if (!UteisValidacao.emptyString(dataEmissao)) {
            camposAlterados.put(montarCamposAlterados("DataHoraEmissao", dataEmissao, true));
        }
        return camposAlterados;
    }


    private static JSONObject montarCamposAlterados(String campo, String valor, boolean aspas) throws Exception {
        JSONObject alteracao = new JSONObject();
        alteracao.put("campo", campo);
        alteracao.put("valor", valor);
        alteracao.put("colocarAspas", aspas);
        return alteracao;
    }
}
