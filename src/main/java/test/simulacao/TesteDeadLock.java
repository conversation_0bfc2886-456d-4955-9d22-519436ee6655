package test.simulacao;


import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;



/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 *
 * <AUTHOR>
 */
public class TesteDeadLock {

    public static void main(String... args) throws Exception{
                
        for (int i=1; i < 6; i++){
            ThreadUpdate tUpdate = new ThreadUpdate();
            tUpdate.setName("TheadUpdate" + i);
            tUpdate.start();

            ThreadConsulta tConsulta = new ThreadConsulta();
            tConsulta.setName("ThreadConsulta"+i);
            tConsulta.start();
            
        }
    }

}
