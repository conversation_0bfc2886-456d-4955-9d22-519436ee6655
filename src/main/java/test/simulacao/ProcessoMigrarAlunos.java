package test.simulacao;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ProcessoMigrarAlunos {

    private static StringBuilder logGravar;
    private static String nomeBanco = "";

    public static void main(String args[]) throws Exception {

        logGravar = new StringBuilder();

        try {
            Uteis.debug = true;

            Connection con = new Conexao("****************************************", "postgres", "pactodb").getConexao();
//            Conexao.guardarConexaoForJ2SE(con);
            nomeBanco = con.getCatalog();

            Integer empresaOrigem = 1;
            Integer empresaDestino = 2;
            boolean somenteClienteAtivos = false;
            String matriculasSeparadasPorVirgula = "";

            migrarAlunos(empresaOrigem, empresaDestino, somenteClienteAtivos, matriculasSeparadasPorVirgula, con);
        } catch (Exception ex) {
            adicionarLog(ex.getMessage());
        } finally {
            Uteis.salvarArquivo(nomeBanco + "-" + Calendario.getData("yyyyMMddHHmmss") + ".txt", getLogGravar().toString(), "C:\\PactoJ\\log\\" + File.separator);
        }
    }


    private static void migrarAlunos(Integer empresaOrigem, Integer empresaDestino,
                                     boolean somenteClienteAtivos, String matriculasSeparadasPorVirgula, Connection con) throws Exception {

        Integer sucesso = 0;
        Integer falha = 0;

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("sw.codigocliente as cliente, \n");
        sql.append("sw.codigopessoa as pessoa \n");
        sql.append("from situacaoclientesinteticodw sw \n");
        sql.append("where 1 = 1 \n");
        if (!UteisValidacao.emptyString(matriculasSeparadasPorVirgula)) {
            sql.append(" and sw.matricula in (").append(matriculasSeparadasPorVirgula).append(") \n");
        }
        if (somenteClienteAtivos) {
            sql.append("and sw.situacao = 'AT' \n");
        }
        sql.append("and sw.empresacliente = ").append(empresaOrigem).append(" \n");
        sql.append("order by sw.codigopessoa ");


        Integer total = SuperFacadeJDBC.contar("select count(*) from (" + sql.toString() + ") as sql", con);
        adicionarLog("--- TOTAL CLIENTES: " + total);

        int ind = 0;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {

            Integer cliente = rs.getInt("cliente");
            Integer pessoa = rs.getInt("pessoa");

            adicionarLog(++ind + "/" + total + " | Cliente " + cliente + " | Pessoa: " + pessoa);

            try {
                con.setAutoCommit(false);

                //cliente
                SuperFacadeJDBC.executarConsultaUpdate("update cliente set empresa = " + empresaDestino + " where pessoa = " + pessoa, con);

                //vendaavulsa
                SuperFacadeJDBC.executarConsultaUpdate("update vendaavulsa set empresa = " + empresaDestino + " where cliente = " + cliente, con);

                //contrato
                SuperFacadeJDBC.executarConsultaUpdate("update contrato set empresa = " + empresaDestino + " where pessoa = " + pessoa, con);

                //produtos
                SuperFacadeJDBC.executarConsultaUpdate("update movproduto set empresa = " + empresaDestino + " where pessoa = " + pessoa, con);

                //parcelas
                SuperFacadeJDBC.executarConsultaUpdate("update movparcela set empresa = " + empresaDestino + " where pessoa = " + pessoa, con);

                //movpagamento
                SuperFacadeJDBC.executarConsultaUpdate("update movpagamento set empresa = " + empresaDestino + " where pessoa = " + pessoa, con);

                //recibo pagamento
                SuperFacadeJDBC.executarConsultaUpdate("update recibopagamento  set empresa = " + empresaDestino + " where pessoapagador = " + pessoa, con);

                //situacaoclientesinteticodw
                SuperFacadeJDBC.executarConsultaUpdate("update situacaoclientesinteticodw  set empresacliente = " + empresaDestino + " where codigopessoa = " + pessoa, con);

                sucesso++;
                con.commit();
            } catch (Exception ex) {
                falha++;
                con.rollback();
                con.setAutoCommit(true);
                adicionarLog("PESSOA | " + pessoa + " | ERRO: " + ex.getMessage());
            } finally {
                con.setAutoCommit(true);
            }
        }

        adicionarLog("--- TOTAL | " + total);
        adicionarLog("--- TOTAL | SUCESSO | " + sucesso);
        adicionarLog("--- TOTAL | FALHA   | " + falha);
    }

    private static void migrarContratos(Integer pessoa, boolean somenteContratoAtivo, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("codigo \n");
        sql.append("from contrato  \n");
        sql.append("where pessoa = ").append(pessoa).append(" \n");
        if (somenteContratoAtivo) {
            sql.append("and (situacao = 'AT' or vigenciaateajustada::date >= CURRENT_DATE) \n");
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
        }
    }

    private static void migrarVendaAvulsa(Integer pessoa, Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("");
        sql.append("");
        sql.append("");
        sql.append("");
        sql.append("");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {

        }
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
        getLogGravar().append(s).append("\n");
    }

    private static StringBuilder getLogGravar() {
        if (logGravar == null) {
            logGravar = new StringBuilder();
        }
        return logGravar;
    }

    public void setLogGravar(StringBuilder logGravar) {
        this.logGravar = logGravar;
    }
}
