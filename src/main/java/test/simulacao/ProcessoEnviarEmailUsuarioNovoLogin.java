package test.simulacao;

import br.com.pactosolucoes.atualizadb.processo.SincronizarUsuarioNovoLogin;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ProcessoEnviarEmailUsuarioNovoLogin {

    public static void main(String[] args) {
        try {
            Uteis.debug = true;

            String cpf = "621.840.590-72";

            Integer.parseInt(Uteis.formatarCpfCnpj(cpf, true));

//            Connection con = new Conexao("*********************************************************************", "postgres", "pactodb").getConexao();
//            Conexao.guardarConexaoForJ2SE("f4bc544c4f6d26707d2e3a3efa9e8893", con);
//            Integer empresa = 1;
//            processar(empresa, con);
//            processaraaaaa(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void processaraaaaa( Connection con) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("select   \n");
        sqlStr.append("('update situacaoclientesinteticodw set matricula = ''' || cl.codigomatricula  || ''' where codigo = '||sw.codigo||';') as sqlup,  \n");
        sqlStr.append("sw.codigo,  \n");
        sqlStr.append("sw.codigocliente,  \n");
        sqlStr.append("sw.matricula as sinteco,  \n");
        sqlStr.append("cl.matricula,  \n");
        sqlStr.append("cl.codigomatricula  \n");
        sqlStr.append("from situacaoclientesinteticodw sw   \n");
        sqlStr.append("inner join cliente cl on cl.codigo = sw.codigocliente  \n");
        sqlStr.append("where cl.codigomatricula <> sw.matricula   \n");
        int atu = 0;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                while (tabelaResultado.next()) {
                    System.out.println(atu++);
                    SuperFacadeJDBC.executarConsulta(tabelaResultado.getString("sqlup"), con);
                }
            }
        }
    }

    private static void processar(Integer empresa, Connection con) {
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);

            Set<Integer> usuarioIgnorar = new HashSet<>();
            usuarioIgnorar.add(7);
            usuarioIgnorar.add(8);
            usuarioIgnorar.add(9);

            UsuarioVO usuarioVORec = usuarioDAO.getUsuarioRecorrencia();

            List<UsuarioVO> usuarios = consultarUsuarios(usuarioDAO, con);
            for (UsuarioVO usuarioVO : usuarios) {
                try {
                    if (usuarioIgnorar.contains(usuarioVO.getCodigo())) {
                        continue;
                    }

                    //sincronizar o usuário enviando o email que o usuário informou no cadastro
                    SincronizarUsuarioNovoLogin.enviarEmailNovoUsuario(usuarioVO.getCodigo(),
                            true, false, null,
                            con, empresa,
                            usuarioVORec, "", true);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            usuarioDAO = null;
        }
    }

    public static List<UsuarioVO> consultarUsuarios(Usuario usuarioDAO, Connection con) throws Exception {
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("SELECT  \n");
        sqlStr.append("u.*  \n");
        sqlStr.append("FROM Usuario u  \n");
        sqlStr.append("inner join colaborador col on col.codigo = u.colaborador  \n");
        sqlStr.append("where col.situacao = 'AT'  \n");
        sqlStr.append("and u.administrador = false  \n");
        sqlStr.append("and u.userName <> ''  \n");
        sqlStr.append("and coalesce(u.usuariogeral,'') = '' \n");
        sqlStr.append("and upper(u.userName) not in ('PACTOBR','ADMIN','RECOR')  \n");
        sqlStr.append("and exists(select codigo from usuarioemail where verificado = false and  usuario = u.codigo)  \n");
        sqlStr.append("and exists(select codigo from tipocolaborador where colaborador  = col.codigo and descricao in ('TW','PR')) \n");
        sqlStr.append("order by u.codigo \n");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr.toString())) {
                return usuarioDAO.montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, con);
            }
        }
    }
}
