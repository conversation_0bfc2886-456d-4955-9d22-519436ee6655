package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.enumerador.FiltrosEnum;

/**
 * Created by alcides on 09/11/2017.
 */
public class FiltrosVO extends SuperVO{
    private FiltrosEnum filtro;
    private String valor;
    private Integer usuario;

    public FiltrosEnum getFiltro() {
        return filtro;
    }

    public void setFiltro(FiltrosEnum filtro) {
        this.filtro = filtro;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public Integer getUsuario() {
        return usuario;
    }

    public void setUsuario(Integer usuario) {
        this.usuario = usuario;
    }
}
