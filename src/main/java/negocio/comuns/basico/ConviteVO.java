package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class ConviteVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    private ClienteVO convidou;
    private ClienteVO convidado;
    private Date dia;
    private PlanoVO plano;
    private String situacao;
    private boolean faltalancarfreepass;

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDiaApresentar(){
        return Uteis.getData(getDia());
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ClienteVO getConvidou() {
        return convidou;
    }

    public void setConvidou(ClienteVO convidou) {
        this.convidou = convidou;
    }

    public ClienteVO getConvidado() {
        return convidado;
    }

    public void setConvidado(ClienteVO convidado) {
        this.convidado = convidado;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public PlanoVO getPlano() {
        return plano;
    }

    public void setPlano(PlanoVO plano) {
        this.plano = plano;
    }

    public String getConvidouApresentar(){
        return getConvidou().getPessoa().getNome();
    }

    public String getConvidadoApresentar(){
        return getConvidado().getPessoa().getNome();
    }

    public boolean isFaltalancarfreepass() {
        return faltalancarfreepass;
    }

    public void setFaltalancarfreepass(boolean faltalancarfreepass) {
        this.faltalancarfreepass = faltalancarfreepass;
    }
}
