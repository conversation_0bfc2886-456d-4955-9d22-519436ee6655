package negocio.comuns.basico;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class LogIntegracoesVO extends SuperVO {
    @ChavePrimaria
    private Integer codigo;
    private String servico;
    private String dadosRecebidos;

    private Date dataLancamento;

    private String resultado;
    private String chavePrimaria;


    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getServico() {
        return servico;
    }

    public void setServico(String servico) {
        this.servico = servico;
    }

    public String getDadosRecebidos() {
        return dadosRecebidos;
    }

    public void setDadosRecebidos(String dadosRecebidos) {
        this.dadosRecebidos = dadosRecebidos;
    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getResultado() {
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(String chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }
}
