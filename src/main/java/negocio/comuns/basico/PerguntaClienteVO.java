package negocio.comuns.basico;
import annotations.arquitetura.ListJson;
import annotations.arquitetura.NaoControlarLogAlteracao;
import com.sun.xml.ws.util.StringUtils;
import negocio.comuns.basico.RespostaPergClienteVO;
import java.util.Iterator;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Reponsável por manter os dados da entidade PerguntaCliente. Classe do tipo VO - Value Object
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
*/
public class PerguntaClienteVO extends SuperVO {

    protected Integer codigo;
    protected String descricao;
    protected String tipoPergunta;
    protected Boolean textual;
    protected Boolean multipla;
    protected Boolean simples;
    protected Boolean nps;
    @NaoControlarLogAlteracao
    private String campo;

    @NaoControlarLogAlteracao
    private Boolean obrigatoria = Boolean.FALSE;


    /** Atributo responsável por manter os objetos da classe <code>RespostaPergCliente</code>. */
    @ListJson(clazz = RespostaPergClienteVO.class)
    private List respostaPergClienteVOs;

    /**
     * Construtor padrão da classe <code>PerguntaCliente</code>.
     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
    */
    public PerguntaClienteVO() {
        super();
        inicializarDados();
    }


    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PerguntaClienteVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirExecption Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
    */
    public static void validarDados(PerguntaClienteVO obj) throws ConsistirException {
        if (!obj.getValidarDados().booleanValue()) {
            return;
            }
        if (obj.getDescricao().equals("")) {
            throw new ConsistirException("O campo DESCRIÇÃO (Pergunta Cliente) deve ser informado.");
        }
        if (obj.getTipoPergunta().equals("")) {
            throw new ConsistirException("O campo TIPO DE PERGUNTA (Pergunta Cliente) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
    */
    public void realizarUpperCaseDados() {
        setDescricao( getDescricao().toUpperCase() );
        setTipoPergunta( getTipoPergunta().toUpperCase() );
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
    */
    public void inicializarDados() {
        setCodigo( new Integer(0) );
        setDescricao( "" );
        setTipoPergunta( "" );
        setMultipla(new Boolean(false));
        setSimples(new Boolean(false));
        setTextual(new Boolean(false));
        setNps(new Boolean(false));
        setRespostaPergClienteVOs( new ArrayList() );
    }

    /**
     * Operação responsável por adicionar um novo objeto da classe <code>RespostaPergClienteVO</code>
     * ao List <code>respostaPergClienteVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>RespostaPergCliente</code> - getDescricaoRespota() - como identificador (key) do objeto no List.
     * @param obj    Objeto da classe <code>RespostaPergClienteVO</code> que será adiocionado ao Hashtable correspondente.
    */
    public void adicionarObjRespostaPergClienteVOs(RespostaPergClienteVO obj) throws Exception {
        RespostaPergClienteVO.validarDados(obj);
        int index = 0;
        Iterator i = getRespostaPergClienteVOs().iterator();
        while (i.hasNext()) {
            RespostaPergClienteVO objExistente = (RespostaPergClienteVO)i.next();
            if (objExistente.getDescricaoRespota().equals(obj.getDescricaoRespota())) {
                getRespostaPergClienteVOs().set( index , obj );
                return;
            }
            index++;
        }
        getRespostaPergClienteVOs().add( obj );
    }

    /**
     * Operação responsável por excluir um objeto da classe <code>RespostaPergClienteVO</code>
     * no List <code>respostaPergClienteVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>RespostaPergCliente</code> - getDescricaoRespota() - como identificador (key) do objeto no List.
     * @param descricaoRespota  Parâmetro para localizar e remover o objeto do List.
    */
    public void excluirObjRespostaPergClienteVOs(String descricaoRespota) throws Exception {
        int index = 0;
        Iterator i = getRespostaPergClienteVOs().iterator();
        while (i.hasNext()) {
            RespostaPergClienteVO objExistente = (RespostaPergClienteVO)i.next();
            if (objExistente.getDescricaoRespota().equals(descricaoRespota)) {
                getRespostaPergClienteVOs().remove( index );
                return;
            }
            index++;
        }
    }

    /**
     * Operação responsável por consultar um objeto da classe <code>RespostaPergClienteVO</code>
     * no List <code>respostaPergClienteVOs</code>. Utiliza o atributo padrão de consulta
     * da classe <code>RespostaPergCliente</code> - getDescricaoRespota() - como identificador (key) do objeto no List.
     * @param descricaoRespota  Parâmetro para localizar o objeto do List.
    */
    public RespostaPergClienteVO consultarObjRespostaPergClienteVO(String descricaoRespota) throws Exception {
        Iterator i = getRespostaPergClienteVOs().iterator();
        while (i.hasNext()) {
            RespostaPergClienteVO objExistente = (RespostaPergClienteVO)i.next();
            if (objExistente.getDescricaoRespota().equals(descricaoRespota)) {
                return objExistente;
            }
        }
        return null;
    }


    /** Retorna Atributo responsável por manter os objetos da classe <code>RespostaPergCliente</code>. */
    public List getRespostaPergClienteVOs() {
        return (respostaPergClienteVOs);
    }

    /** Define Atributo responsável por manter os objetos da classe <code>RespostaPergCliente</code>. */
    public void setRespostaPergClienteVOs( List respostaPergClienteVOs ) {
        this.respostaPergClienteVOs = respostaPergClienteVOs;
    }

    public String getTipoPergunta() {
        if (tipoPergunta== null) {
            tipoPergunta = "";
        }
        return (tipoPergunta);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico.
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc.
    */
    public String getTipoPergunta_Apresentar() {
        if (tipoPergunta== null) {
            tipoPergunta = "";
        }
        if (tipoPergunta.equals("ME")) {
            return "Multipla Escolha";
        }
        if (tipoPergunta.equals("SE")) {
            return "Simples  Escolha";
        }
        if (tipoPergunta.equals("TE")) {
            return "Textual";
        }
        if (tipoPergunta.equals("SN")) {
            return "Sim/Não";
        }if(tipoPergunta.equals("NS")){
            return "NPS";
        }
        return (tipoPergunta);
    }

    public void setTipoPergunta( String tipoPergunta ) {
        this.tipoPergunta = tipoPergunta;
    }

    public String getDescricao() {
        if (descricao== null) {
            descricao = "";
        }
        return (descricao);
    }

    public void setDescricao( String descricao ) {
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setCodigo( Integer codigo ) {
        this.codigo = codigo;
    }

    public Boolean getMultipla() {
        return multipla;
    }

    public void setMultipla(Boolean multipla) {
        this.multipla = multipla;
    }

    public Boolean getSimples() {
        return simples;
    }

    public void setSimples(Boolean simples) {
        this.simples = simples;
    }

    public Boolean getTextual() {
        return textual;
    }

    public void setTextual(Boolean textual) {
        this.textual = textual;
    }

    public Boolean getObrigatoria() {
        return obrigatoria;
    }

    public void setObrigatoria(Boolean obrigatoria) {
        this.obrigatoria = obrigatoria;
    }

    public String getDescricao_ApresentarPesquisa() {
        return StringUtils.capitalize(getDescricao().toLowerCase());
    }

    public String getCampo() {
        if (campo == null) {
            campo = "";
        }
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public Boolean getNps() {
        return nps;
    }

    public void setNps(Boolean nps) {
        this.nps = nps;
    }
}