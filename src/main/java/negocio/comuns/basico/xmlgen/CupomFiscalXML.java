/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.xmlgen;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import negocio.comuns.xmlbase.ArquivoXML;
import negocio.comuns.xmlbase.BaseXML;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.to.CupomFiscalTO;
import java.text.Format;

/**
 * Representação XML da classe de Cupom Fiscal
 * 
 * <AUTHOR>
 * 
 */
public class CupomFiscalXML extends BaseXML {

    @SuppressWarnings("unchecked")
    @Override
    public ArquivoXML adicionarDadosNoArquivo(List listaCupons, boolean comAutorizacaoCobranca) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHH:mm:ss");
        for (int i = 0; i < listaCupons.size(); i++) {
            CupomFiscalTO cupomTO = (CupomFiscalTO) listaCupons.get(i);
            Date horaEmissao = cupomTO.getHoraEmissao();
            String dataEmissao = horaEmissao == null ? "" : sf.format(horaEmissao);
            if (!dataEmissao.isEmpty()){
                String parte1 = dataEmissao.substring(0, 8);
                dataEmissao = parte1 + "T"+ dataEmissao.substring(8);
            }else{
                dataEmissao = "18991231T00:00:00";
            }
            String dataVenda = sf.format(cupomTO.getHoraVenda());

            if (!dataVenda.isEmpty()){
                String parte1 = dataVenda.substring(0, 8);
                dataVenda = "'"+parte1 + "T"+ dataVenda.substring(8)+"'";
            }else{
                dataVenda = "18991231T00:00:00";
            }
            String dataPagamento = sf.format(cupomTO.getDataPagamento());
            if (!dataPagamento.isEmpty()){
                String parte1 = dataPagamento.substring(0, 8);
                dataPagamento = "'"+parte1 + "T"+ dataPagamento.substring(8)+"'";
            }else{
            	dataPagamento = "18991231T00:00:00";
            }

            
            addArquivoXML("Codigo", "7", String.class, String.valueOf(cupomTO.getCodigo()), i, arquivoXML);
            addArquivoXML("Responsavel", "5", String.class, String.valueOf(cupomTO.getResponsavel()), i, arquivoXML);
            addArquivoXML("Valor", "14", String.class, Formatador.formatarValorMonetarioSemMoeda(cupomTO.getValor()), i, arquivoXML);
            addArquivoXML("DataVenda", "20", Date.class, dataVenda, i, arquivoXML);

            addArquivoXML("DataEmissao", "20", Date.class, dataEmissao, i, arquivoXML);
            addArquivoXML("StatusCupom", "1", String.class, String.valueOf(cupomTO.getStatusCupom()), i, arquivoXML);
            addArquivoXML("StatusImpressao", "1", String.class, String.valueOf(cupomTO.getStatusImpressao()), i, arquivoXML);
            addArquivoXML("CO_CUPOM", "10", Integer.class,
            		String.valueOf(cupomTO.getCo_cupom()), i, arquivoXML);
            addArquivoXML("Recibo", "7", Integer.class, String.valueOf(cupomTO.getRecibo()), i, arquivoXML);
            addArquivoXML("Cheque", "7", Integer.class, String.valueOf(cupomTO.getCheque()), i, arquivoXML);
            //local
            addArquivoXML("Local", "1", String.class, String.valueOf(cupomTO.getLocal()), i, arquivoXML);
            //cliente
            addArquivoXML("CodigoCliente", "7", String.class, String.valueOf(cupomTO.getCodCliente()), i, arquivoXML);
            addArquivoXML("NomeCliente", "150", String.class, String.valueOf(cupomTO.getNomeCliente()), i, arquivoXML);
            addArquivoXML("CPF", "14", String.class, String.valueOf(cupomTO.getCpfCliente()), i, arquivoXML);
            addArquivoXML("Pagamento", "7", Integer.class, String.valueOf(cupomTO.getPagamento()), i, arquivoXML);
            addArquivoXML("Cartao", "7", Integer.class, String.valueOf(cupomTO.getCartao()), i, arquivoXML);
            addArquivoXML("DataPagamento", "20", Date.class, dataPagamento, i, arquivoXML);
            addArquivoXML("Matricula", "7", String.class, String.valueOf(cupomTO.getMatricula()), i, arquivoXML);
            

        }
        return arquivoXML;
    }
}
