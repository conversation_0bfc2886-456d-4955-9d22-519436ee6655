/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.basico.xmlgen;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.xmlbase.ArquivoXML;
import negocio.comuns.xmlbase.BaseXML;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Data: 30/11/10
 * Objetivo da classe: Gerar um arquivo XML através de uma lista de colaboradores.
 */
public class ColaboradorXML extends BaseXML {

    @Override
    public ArquivoXML adicionarDadosNoArquivo(List listaColaborador, boolean comAutorizacaoCobranca) {
        for (int i = 0; i < listaColaborador.size(); i++) {
            ColaboradorVO colaboradorVo = (ColaboradorVO) listaColaborador.get(i);
            addArquivoXML("Codigo", "", Integer.class, colaboradorVo.getCodigo().toString(), i, arquivoXML);
            addArquivoXML("Nome", "50", String.class, colaboradorVo.getPessoa().getNome(), i, arquivoXML);
            addArquivoXML("Situacao", "2", String.class, colaboradorVo.getSituacao(), i, arquivoXML);
            addArquivoXML("CodigoTipoColaborador", "", Integer.class, "1", i, arquivoXML);
            addArquivoXML("DescricaoTipoColaborador", "9", String.class, "Professor", i, arquivoXML);
            addArquivoXML("CodAcesso", "12", String.class, colaboradorVo.getCodAcesso(), i, arquivoXML);


            addArquivoXML("CodigoMatricula", "", Integer.class, "0", i, arquivoXML);
            addArquivoXML("TelefoneRes", "15", String.class, "", i, arquivoXML);
            addArquivoXML("TelefoneCom", "15", String.class, "", i, arquivoXML);
            addArquivoXML("TelefoneCel", "15", String.class, "", i, arquivoXML);
            addArquivoXML("Email", "50", String.class, "", i, arquivoXML);
            addArquivoXML("Sexo", "2", String.class, "", i, arquivoXML);
            addArquivoXML("EstadoCivil", "10", String.class, "", i, arquivoXML);
            addArquivoXML("DataNasc", "10", Date.class, "null", i, arquivoXML);
            addArquivoXML("MatriculaExterna", "", Integer.class, "", i, arquivoXML);
        }
        return arquivoXML;
    }
}
