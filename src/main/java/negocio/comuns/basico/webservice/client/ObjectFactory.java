
package negocio.comuns.basico.webservice.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the negocio.comuns.basico.webservice.client package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _GerarUsuarioMovelAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarUsuarioMovelAlunoResponse");
    private final static QName _ConsultarProfessoresPeloNomeResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarProfessoresPeloNomeResponse");
    private final static QName _ConsultarLocaisAcessoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarLocaisAcessoResponse");
    private final static QName _ConsultarColetoresResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColetoresResponse");
    private final static QName _ConsultarClientesPeloNome_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPeloNome");
    private final static QName _ConsultarClienteSinteticoPorEmailPessoa_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSinteticoPorEmailPessoa");
    private final static QName _ConsultarEmpresasResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEmpresasResponse");
    private final static QName _ConsultarClientesPeloNomeResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPeloNomeResponse");
    private final static QName _ConsultarProfessoresPeloNome_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarProfessoresPeloNome");
    private final static QName _ConsultarGrupoDeRiscoPelaMatriculaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarGrupoDeRiscoPelaMatriculaResponse");
    private final static QName _ConsultarGrupoDeRiscoPelaMatricula_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarGrupoDeRiscoPelaMatricula");
    private final static QName _ConsultarClientePorMatriculaExternaImportacao_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorMatriculaExternaImportacao");
    private final static QName _AlterarUsuarioMovelAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelAlunoResponse");
    private final static QName _ConsultarClientePorMatriculaExternaImportacaoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientePorMatriculaExternaImportacaoResponse");
    private final static QName _ConsultarClienteSinteticoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSinteticoResponse");
    private final static QName _GerarVinculoProfessorAlunoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarVinculoProfessorAlunoResponse");
    private final static QName _ConsultarColetores_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColetores");
    private final static QName _GerarUsuarioMovelAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarUsuarioMovelAluno");
    private final static QName _ConsultarClientesResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesResponse");
    private final static QName _GerarVinculoProfessorAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "gerarVinculoProfessorAluno");
    private final static QName _ConsultarEmpresas_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarEmpresas");
    private final static QName _ConsultarClienteSintetico_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSintetico");
    private final static QName _ConsultarClientesTreino_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesTreino");
    private final static QName _ConsultarClientesPelaMatriculaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPelaMatriculaResponse");
    private final static QName _ConsultarClientesTreinoResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesTreinoResponse");
    private final static QName _Exception_QNAME = new QName("http://webservice.basico.comuns.negocio/", "Exception");
    private final static QName _ConsultarClientes_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientes");
    private final static QName _ConsultarLocaisAcesso_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarLocaisAcesso");
    private final static QName _AlterarUsuarioMovelAluno_QNAME = new QName("http://webservice.basico.comuns.negocio/", "alterarUsuarioMovelAluno");
    private final static QName _ConsultarClienteSinteticoPorEmailPessoaResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClienteSinteticoPorEmailPessoaResponse");
    private final static QName _ConsultarClientesPelaMatricula_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarClientesPelaMatricula");
    private final static QName _ConsultarColaboradores_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColaboradores");
    private final static QName _ConsultarColaboradoresResponse_QNAME = new QName("http://webservice.basico.comuns.negocio/", "consultarColaboradoresResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: negocio.comuns.basico.webservice.client
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelAluno }
     * 
     */
    public AlterarUsuarioMovelAluno createAlterarUsuarioMovelAluno() {
        return new AlterarUsuarioMovelAluno();
    }

    /**
     * Create an instance of {@link ExceptionBean }
     * 
     */
    public ExceptionBean createExceptionBean() {
        return new ExceptionBean();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNome }
     * 
     */
    public ConsultarClientesPeloNome createConsultarClientesPeloNome() {
        return new ConsultarClientesPeloNome();
    }

    /**
     * Create an instance of {@link ConsultarColaboradores }
     * 
     */
    public ConsultarColaboradores createConsultarColaboradores() {
        return new ConsultarColaboradores();
    }

    /**
     * Create an instance of {@link ConsultarClientesPelaMatricula }
     * 
     */
    public ConsultarClientesPelaMatricula createConsultarClientesPelaMatricula() {
        return new ConsultarClientesPelaMatricula();
    }

    /**
     * Create an instance of {@link ConsultarColaboradoresResponse }
     * 
     */
    public ConsultarColaboradoresResponse createConsultarColaboradoresResponse() {
        return new ConsultarColaboradoresResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesTreinoResponse }
     * 
     */
    public ConsultarClientesTreinoResponse createConsultarClientesTreinoResponse() {
        return new ConsultarClientesTreinoResponse();
    }

    /**
     * Create an instance of {@link ConsultarColetoresResponse }
     * 
     */
    public ConsultarColetoresResponse createConsultarColetoresResponse() {
        return new ConsultarColetoresResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientePorMatriculaExternaImportacao }
     * 
     */
    public ConsultarClientePorMatriculaExternaImportacao createConsultarClientePorMatriculaExternaImportacao() {
        return new ConsultarClientePorMatriculaExternaImportacao();
    }

    /**
     * Create an instance of {@link ConsultarGrupoDeRiscoPelaMatricula }
     * 
     */
    public ConsultarGrupoDeRiscoPelaMatricula createConsultarGrupoDeRiscoPelaMatricula() {
        return new ConsultarGrupoDeRiscoPelaMatricula();
    }

    /**
     * Create an instance of {@link ConsultarEmpresas }
     * 
     */
    public ConsultarEmpresas createConsultarEmpresas() {
        return new ConsultarEmpresas();
    }

    /**
     * Create an instance of {@link ConsultarClientesPelaMatriculaResponse }
     * 
     */
    public ConsultarClientesPelaMatriculaResponse createConsultarClientesPelaMatriculaResponse() {
        return new ConsultarClientesPelaMatriculaResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientePorMatriculaExternaImportacaoResponse }
     * 
     */
    public ConsultarClientePorMatriculaExternaImportacaoResponse createConsultarClientePorMatriculaExternaImportacaoResponse() {
        return new ConsultarClientePorMatriculaExternaImportacaoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteSinteticoResponse }
     * 
     */
    public ConsultarClienteSinteticoResponse createConsultarClienteSinteticoResponse() {
        return new ConsultarClienteSinteticoResponse();
    }

    /**
     * Create an instance of {@link ConsultarLocaisAcessoResponse }
     * 
     */
    public ConsultarLocaisAcessoResponse createConsultarLocaisAcessoResponse() {
        return new ConsultarLocaisAcessoResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientes }
     * 
     */
    public ConsultarClientes createConsultarClientes() {
        return new ConsultarClientes();
    }

    /**
     * Create an instance of {@link GerarVinculoProfessorAluno }
     * 
     */
    public GerarVinculoProfessorAluno createGerarVinculoProfessorAluno() {
        return new GerarVinculoProfessorAluno();
    }

    /**
     * Create an instance of {@link ConsultarProfessoresPeloNomeResponse }
     * 
     */
    public ConsultarProfessoresPeloNomeResponse createConsultarProfessoresPeloNomeResponse() {
        return new ConsultarProfessoresPeloNomeResponse();
    }

    /**
     * Create an instance of {@link ConsultarClientesPeloNomeResponse }
     * 
     */
    public ConsultarClientesPeloNomeResponse createConsultarClientesPeloNomeResponse() {
        return new ConsultarClientesPeloNomeResponse();
    }

    /**
     * Create an instance of {@link ConsultarProfessoresPeloNome }
     * 
     */
    public ConsultarProfessoresPeloNome createConsultarProfessoresPeloNome() {
        return new ConsultarProfessoresPeloNome();
    }

    /**
     * Create an instance of {@link ConsultarClientesResponse }
     * 
     */
    public ConsultarClientesResponse createConsultarClientesResponse() {
        return new ConsultarClientesResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteSintetico }
     * 
     */
    public ConsultarClienteSintetico createConsultarClienteSintetico() {
        return new ConsultarClienteSintetico();
    }

    /**
     * Create an instance of {@link GerarUsuarioMovelAlunoResponse }
     * 
     */
    public GerarUsuarioMovelAlunoResponse createGerarUsuarioMovelAlunoResponse() {
        return new GerarUsuarioMovelAlunoResponse();
    }

    /**
     * Create an instance of {@link ConsultarColetores }
     * 
     */
    public ConsultarColetores createConsultarColetores() {
        return new ConsultarColetores();
    }

    /**
     * Create an instance of {@link ConsultarEmpresasResponse }
     * 
     */
    public ConsultarEmpresasResponse createConsultarEmpresasResponse() {
        return new ConsultarEmpresasResponse();
    }

    /**
     * Create an instance of {@link ConsultarClienteSinteticoPorEmailPessoaResponse }
     * 
     */
    public ConsultarClienteSinteticoPorEmailPessoaResponse createConsultarClienteSinteticoPorEmailPessoaResponse() {
        return new ConsultarClienteSinteticoPorEmailPessoaResponse();
    }

    /**
     * Create an instance of {@link ConsultarGrupoDeRiscoPelaMatriculaResponse }
     * 
     */
    public ConsultarGrupoDeRiscoPelaMatriculaResponse createConsultarGrupoDeRiscoPelaMatriculaResponse() {
        return new ConsultarGrupoDeRiscoPelaMatriculaResponse();
    }

    /**
     * Create an instance of {@link ConsultarLocaisAcesso }
     * 
     */
    public ConsultarLocaisAcesso createConsultarLocaisAcesso() {
        return new ConsultarLocaisAcesso();
    }

    /**
     * Create an instance of {@link ConsultarClientesTreino }
     * 
     */
    public ConsultarClientesTreino createConsultarClientesTreino() {
        return new ConsultarClientesTreino();
    }

    /**
     * Create an instance of {@link ConsultarClienteSinteticoPorEmailPessoa }
     * 
     */
    public ConsultarClienteSinteticoPorEmailPessoa createConsultarClienteSinteticoPorEmailPessoa() {
        return new ConsultarClienteSinteticoPorEmailPessoa();
    }

    /**
     * Create an instance of {@link GerarVinculoProfessorAlunoResponse }
     * 
     */
    public GerarVinculoProfessorAlunoResponse createGerarVinculoProfessorAlunoResponse() {
        return new GerarVinculoProfessorAlunoResponse();
    }

    /**
     * Create an instance of {@link AlterarUsuarioMovelAlunoResponse }
     * 
     */
    public AlterarUsuarioMovelAlunoResponse createAlterarUsuarioMovelAlunoResponse() {
        return new AlterarUsuarioMovelAlunoResponse();
    }

    /**
     * Create an instance of {@link GerarUsuarioMovelAluno }
     * 
     */
    public GerarUsuarioMovelAluno createGerarUsuarioMovelAluno() {
        return new GerarUsuarioMovelAluno();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarUsuarioMovelAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarUsuarioMovelAlunoResponse")
    public JAXBElement<GerarUsuarioMovelAlunoResponse> createGerarUsuarioMovelAlunoResponse(GerarUsuarioMovelAlunoResponse value) {
        return new JAXBElement<GerarUsuarioMovelAlunoResponse>(_GerarUsuarioMovelAlunoResponse_QNAME, GerarUsuarioMovelAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarProfessoresPeloNomeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarProfessoresPeloNomeResponse")
    public JAXBElement<ConsultarProfessoresPeloNomeResponse> createConsultarProfessoresPeloNomeResponse(ConsultarProfessoresPeloNomeResponse value) {
        return new JAXBElement<ConsultarProfessoresPeloNomeResponse>(_ConsultarProfessoresPeloNomeResponse_QNAME, ConsultarProfessoresPeloNomeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarLocaisAcessoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarLocaisAcessoResponse")
    public JAXBElement<ConsultarLocaisAcessoResponse> createConsultarLocaisAcessoResponse(ConsultarLocaisAcessoResponse value) {
        return new JAXBElement<ConsultarLocaisAcessoResponse>(_ConsultarLocaisAcessoResponse_QNAME, ConsultarLocaisAcessoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColetoresResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColetoresResponse")
    public JAXBElement<ConsultarColetoresResponse> createConsultarColetoresResponse(ConsultarColetoresResponse value) {
        return new JAXBElement<ConsultarColetoresResponse>(_ConsultarColetoresResponse_QNAME, ConsultarColetoresResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNome }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPeloNome")
    public JAXBElement<ConsultarClientesPeloNome> createConsultarClientesPeloNome(ConsultarClientesPeloNome value) {
        return new JAXBElement<ConsultarClientesPeloNome>(_ConsultarClientesPeloNome_QNAME, ConsultarClientesPeloNome.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSinteticoPorEmailPessoa }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSinteticoPorEmailPessoa")
    public JAXBElement<ConsultarClienteSinteticoPorEmailPessoa> createConsultarClienteSinteticoPorEmailPessoa(ConsultarClienteSinteticoPorEmailPessoa value) {
        return new JAXBElement<ConsultarClienteSinteticoPorEmailPessoa>(_ConsultarClienteSinteticoPorEmailPessoa_QNAME, ConsultarClienteSinteticoPorEmailPessoa.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEmpresasResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEmpresasResponse")
    public JAXBElement<ConsultarEmpresasResponse> createConsultarEmpresasResponse(ConsultarEmpresasResponse value) {
        return new JAXBElement<ConsultarEmpresasResponse>(_ConsultarEmpresasResponse_QNAME, ConsultarEmpresasResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPeloNomeResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPeloNomeResponse")
    public JAXBElement<ConsultarClientesPeloNomeResponse> createConsultarClientesPeloNomeResponse(ConsultarClientesPeloNomeResponse value) {
        return new JAXBElement<ConsultarClientesPeloNomeResponse>(_ConsultarClientesPeloNomeResponse_QNAME, ConsultarClientesPeloNomeResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarProfessoresPeloNome }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarProfessoresPeloNome")
    public JAXBElement<ConsultarProfessoresPeloNome> createConsultarProfessoresPeloNome(ConsultarProfessoresPeloNome value) {
        return new JAXBElement<ConsultarProfessoresPeloNome>(_ConsultarProfessoresPeloNome_QNAME, ConsultarProfessoresPeloNome.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarGrupoDeRiscoPelaMatriculaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarGrupoDeRiscoPelaMatriculaResponse")
    public JAXBElement<ConsultarGrupoDeRiscoPelaMatriculaResponse> createConsultarGrupoDeRiscoPelaMatriculaResponse(ConsultarGrupoDeRiscoPelaMatriculaResponse value) {
        return new JAXBElement<ConsultarGrupoDeRiscoPelaMatriculaResponse>(_ConsultarGrupoDeRiscoPelaMatriculaResponse_QNAME, ConsultarGrupoDeRiscoPelaMatriculaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarGrupoDeRiscoPelaMatricula }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarGrupoDeRiscoPelaMatricula")
    public JAXBElement<ConsultarGrupoDeRiscoPelaMatricula> createConsultarGrupoDeRiscoPelaMatricula(ConsultarGrupoDeRiscoPelaMatricula value) {
        return new JAXBElement<ConsultarGrupoDeRiscoPelaMatricula>(_ConsultarGrupoDeRiscoPelaMatricula_QNAME, ConsultarGrupoDeRiscoPelaMatricula.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorMatriculaExternaImportacao }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorMatriculaExternaImportacao")
    public JAXBElement<ConsultarClientePorMatriculaExternaImportacao> createConsultarClientePorMatriculaExternaImportacao(ConsultarClientePorMatriculaExternaImportacao value) {
        return new JAXBElement<ConsultarClientePorMatriculaExternaImportacao>(_ConsultarClientePorMatriculaExternaImportacao_QNAME, ConsultarClientePorMatriculaExternaImportacao.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelAlunoResponse")
    public JAXBElement<AlterarUsuarioMovelAlunoResponse> createAlterarUsuarioMovelAlunoResponse(AlterarUsuarioMovelAlunoResponse value) {
        return new JAXBElement<AlterarUsuarioMovelAlunoResponse>(_AlterarUsuarioMovelAlunoResponse_QNAME, AlterarUsuarioMovelAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientePorMatriculaExternaImportacaoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientePorMatriculaExternaImportacaoResponse")
    public JAXBElement<ConsultarClientePorMatriculaExternaImportacaoResponse> createConsultarClientePorMatriculaExternaImportacaoResponse(ConsultarClientePorMatriculaExternaImportacaoResponse value) {
        return new JAXBElement<ConsultarClientePorMatriculaExternaImportacaoResponse>(_ConsultarClientePorMatriculaExternaImportacaoResponse_QNAME, ConsultarClientePorMatriculaExternaImportacaoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSinteticoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSinteticoResponse")
    public JAXBElement<ConsultarClienteSinteticoResponse> createConsultarClienteSinteticoResponse(ConsultarClienteSinteticoResponse value) {
        return new JAXBElement<ConsultarClienteSinteticoResponse>(_ConsultarClienteSinteticoResponse_QNAME, ConsultarClienteSinteticoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarVinculoProfessorAlunoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarVinculoProfessorAlunoResponse")
    public JAXBElement<GerarVinculoProfessorAlunoResponse> createGerarVinculoProfessorAlunoResponse(GerarVinculoProfessorAlunoResponse value) {
        return new JAXBElement<GerarVinculoProfessorAlunoResponse>(_GerarVinculoProfessorAlunoResponse_QNAME, GerarVinculoProfessorAlunoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColetores }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColetores")
    public JAXBElement<ConsultarColetores> createConsultarColetores(ConsultarColetores value) {
        return new JAXBElement<ConsultarColetores>(_ConsultarColetores_QNAME, ConsultarColetores.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarUsuarioMovelAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarUsuarioMovelAluno")
    public JAXBElement<GerarUsuarioMovelAluno> createGerarUsuarioMovelAluno(GerarUsuarioMovelAluno value) {
        return new JAXBElement<GerarUsuarioMovelAluno>(_GerarUsuarioMovelAluno_QNAME, GerarUsuarioMovelAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesResponse")
    public JAXBElement<ConsultarClientesResponse> createConsultarClientesResponse(ConsultarClientesResponse value) {
        return new JAXBElement<ConsultarClientesResponse>(_ConsultarClientesResponse_QNAME, ConsultarClientesResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link GerarVinculoProfessorAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "gerarVinculoProfessorAluno")
    public JAXBElement<GerarVinculoProfessorAluno> createGerarVinculoProfessorAluno(GerarVinculoProfessorAluno value) {
        return new JAXBElement<GerarVinculoProfessorAluno>(_GerarVinculoProfessorAluno_QNAME, GerarVinculoProfessorAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarEmpresas }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarEmpresas")
    public JAXBElement<ConsultarEmpresas> createConsultarEmpresas(ConsultarEmpresas value) {
        return new JAXBElement<ConsultarEmpresas>(_ConsultarEmpresas_QNAME, ConsultarEmpresas.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSintetico }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSintetico")
    public JAXBElement<ConsultarClienteSintetico> createConsultarClienteSintetico(ConsultarClienteSintetico value) {
        return new JAXBElement<ConsultarClienteSintetico>(_ConsultarClienteSintetico_QNAME, ConsultarClienteSintetico.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesTreino }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesTreino")
    public JAXBElement<ConsultarClientesTreino> createConsultarClientesTreino(ConsultarClientesTreino value) {
        return new JAXBElement<ConsultarClientesTreino>(_ConsultarClientesTreino_QNAME, ConsultarClientesTreino.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPelaMatriculaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPelaMatriculaResponse")
    public JAXBElement<ConsultarClientesPelaMatriculaResponse> createConsultarClientesPelaMatriculaResponse(ConsultarClientesPelaMatriculaResponse value) {
        return new JAXBElement<ConsultarClientesPelaMatriculaResponse>(_ConsultarClientesPelaMatriculaResponse_QNAME, ConsultarClientesPelaMatriculaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesTreinoResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesTreinoResponse")
    public JAXBElement<ConsultarClientesTreinoResponse> createConsultarClientesTreinoResponse(ConsultarClientesTreinoResponse value) {
        return new JAXBElement<ConsultarClientesTreinoResponse>(_ConsultarClientesTreinoResponse_QNAME, ConsultarClientesTreinoResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ExceptionBean }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "Exception")
    public JAXBElement<ExceptionBean> createException(ExceptionBean value) {
        return new JAXBElement<ExceptionBean>(_Exception_QNAME, ExceptionBean.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientes }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientes")
    public JAXBElement<ConsultarClientes> createConsultarClientes(ConsultarClientes value) {
        return new JAXBElement<ConsultarClientes>(_ConsultarClientes_QNAME, ConsultarClientes.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarLocaisAcesso }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarLocaisAcesso")
    public JAXBElement<ConsultarLocaisAcesso> createConsultarLocaisAcesso(ConsultarLocaisAcesso value) {
        return new JAXBElement<ConsultarLocaisAcesso>(_ConsultarLocaisAcesso_QNAME, ConsultarLocaisAcesso.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link AlterarUsuarioMovelAluno }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "alterarUsuarioMovelAluno")
    public JAXBElement<AlterarUsuarioMovelAluno> createAlterarUsuarioMovelAluno(AlterarUsuarioMovelAluno value) {
        return new JAXBElement<AlterarUsuarioMovelAluno>(_AlterarUsuarioMovelAluno_QNAME, AlterarUsuarioMovelAluno.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClienteSinteticoPorEmailPessoaResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClienteSinteticoPorEmailPessoaResponse")
    public JAXBElement<ConsultarClienteSinteticoPorEmailPessoaResponse> createConsultarClienteSinteticoPorEmailPessoaResponse(ConsultarClienteSinteticoPorEmailPessoaResponse value) {
        return new JAXBElement<ConsultarClienteSinteticoPorEmailPessoaResponse>(_ConsultarClienteSinteticoPorEmailPessoaResponse_QNAME, ConsultarClienteSinteticoPorEmailPessoaResponse.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarClientesPelaMatricula }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarClientesPelaMatricula")
    public JAXBElement<ConsultarClientesPelaMatricula> createConsultarClientesPelaMatricula(ConsultarClientesPelaMatricula value) {
        return new JAXBElement<ConsultarClientesPelaMatricula>(_ConsultarClientesPelaMatricula_QNAME, ConsultarClientesPelaMatricula.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColaboradores }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColaboradores")
    public JAXBElement<ConsultarColaboradores> createConsultarColaboradores(ConsultarColaboradores value) {
        return new JAXBElement<ConsultarColaboradores>(_ConsultarColaboradores_QNAME, ConsultarColaboradores.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ConsultarColaboradoresResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.basico.comuns.negocio/", name = "consultarColaboradoresResponse")
    public JAXBElement<ConsultarColaboradoresResponse> createConsultarColaboradoresResponse(ConsultarColaboradoresResponse value) {
        return new JAXBElement<ConsultarColaboradoresResponse>(_ConsultarColaboradoresResponse_QNAME, ConsultarColaboradoresResponse.class, null, value);
    }

}
