package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class ContratoDependenteHistoricoVO extends SuperVO {

    private ContratoDependenteVO contratoDependenteVO;
    private ClienteVO dependente;
    private Date dataInicio;
    private Date dataFinal;

    public ContratoDependenteVO getContratoDependenteVO() {
        if (contratoDependenteVO == null) {
            contratoDependenteVO = new ContratoDependenteVO();
        }
        return contratoDependenteVO;
    }

    public void setContratoDependenteVO(ContratoDependenteVO contratoDependenteVO) {
        this.contratoDependenteVO = contratoDependenteVO;
    }

    public ClienteVO getDependente() {
        if (dependente == null) {
            dependente = new ClienteVO();
        }
        return dependente;
    }

    public void setDependente(ClienteVO dependente) {
        this.dependente = dependente;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }
}
