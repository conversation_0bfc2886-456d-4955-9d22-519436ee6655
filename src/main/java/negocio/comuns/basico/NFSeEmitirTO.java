package negocio.comuns.basico;

import org.json.JSONObject;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.NFSeEmitidaFormaPagamentoTO;
import negocio.comuns.financeiro.NFSeEmitidaVO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class NFSeEmitirTO extends SuperTO {

    private Double valor;
    private String descricao;
    private Map<Integer, JSONObject> itens;
    private List<NFSeEmitidaFormaPagamentoTO> formasPagamento;
    private ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO;
    private String observacao;
    private String idReferencia;
    private JSONObject jsonNotaEnviar;
    private NFSeEmitidaVO nfSeEmitidaVO;
    private List<MovProdutoVO> listaMovProduto;

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Map<Integer, JSONObject> getItens() {
        if (itens == null) {
            itens = new HashMap<Integer, JSONObject>();
        }
        return itens;
    }

    public void setItens(Map<Integer, JSONObject> itens) {
        this.itens = itens;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public List<NFSeEmitidaFormaPagamentoTO> getFormasPagamento() {
        if (formasPagamento == null) {
            formasPagamento = new ArrayList<NFSeEmitidaFormaPagamentoTO>();
        }
        return formasPagamento;
    }

    public void setFormasPagamento(List<NFSeEmitidaFormaPagamentoTO> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getIdReferencia() {
        if (idReferencia == null) {
            idReferencia = "";
        }
        return idReferencia;
    }

    public void setIdReferencia(String idReferencia) {
        this.idReferencia = idReferencia;
    }

    public JSONObject getJsonNotaEnviar() {
        return jsonNotaEnviar;
    }

    public void setJsonNotaEnviar(JSONObject jsonNotaEnviar) {
        this.jsonNotaEnviar = jsonNotaEnviar;
    }

    public NFSeEmitidaVO getNfSeEmitidaVO() {
        if (nfSeEmitidaVO == null) {
            nfSeEmitidaVO = new NFSeEmitidaVO();
        }
        return nfSeEmitidaVO;
    }

    public void setNfSeEmitidaVO(NFSeEmitidaVO nfSeEmitidaVO) {
        this.nfSeEmitidaVO = nfSeEmitidaVO;
    }

    public List<MovProdutoVO> getListaMovProduto() {
        if (listaMovProduto == null) {
            listaMovProduto = new ArrayList<MovProdutoVO>();
        }
        return listaMovProduto;
    }

    public void setListaMovProduto(List<MovProdutoVO> listaMovProduto) {
        this.listaMovProduto = listaMovProduto;
    }

    public ConfiguracaoNotaFiscalVO getConfiguracaoNotaFiscalVO() {
        if (configuracaoNotaFiscalVO == null) {
            configuracaoNotaFiscalVO = new ConfiguracaoNotaFiscalVO();
        }
        return configuracaoNotaFiscalVO;
    }

    public void setConfiguracaoNotaFiscalVO(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) {
        this.configuracaoNotaFiscalVO = configuracaoNotaFiscalVO;
    }
}
