package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class FamiliaSinteticoVO extends SuperVO {

    private Integer codigo;
    private String codigos;
    private String situacoes;
    private Double valorContratos;
    private Date inicioMaisAntigo;
    private Date inicioMaisRecente;
    private Integer acessos;
    private Integer empresa;
    private Integer risco;

    private String nomes;
    private String emails;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigos() {
        return codigos;
    }

    public void setCodigos(String codigos) {
        this.codigos = codigos;
    }

    public String getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(String situacoes) {
        this.situacoes = situacoes;
    }

    public Double getValorContratos() {
        return valorContratos;
    }

    public void setValorContratos(Double valorContratos) {
        this.valorContratos = valorContratos;
    }

    public Date getInicioMaisAntigo() {
        return inicioMaisAntigo;
    }

    public void setInicioMaisAntigo(Date inicioMaisAntigo) {
        this.inicioMaisAntigo = inicioMaisAntigo;
    }

    public Date getInicioMaisRecente() {
        return inicioMaisRecente;
    }

    public void setInicioMaisRecente(Date inicioMaisRecente) {
        this.inicioMaisRecente = inicioMaisRecente;
    }

    public Integer getAcessos() {
        return acessos;
    }

    public void setAcessos(Integer acessos) {
        this.acessos = acessos;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getRisco() {
        return risco;
    }

    public void setRisco(Integer risco) {
        this.risco = risco;
    }

    public String getNomes() {
        return nomes;
    }

    public void setNomes(String nomes) {
        this.nomes = nomes;
    }

    public String getEmails() {
        return emails;
    }

    public void setEmails(String emails) {
        this.emails = emails;
    }
}
