package negocio.comuns.basico;

import negocio.comuns.arquitetura.SuperVO;

public class ConfiguracaoIntegracaoFogueteVO extends SuperVO{

    private boolean habilitada = false;
    private String tokenApi ="";
    private int empresa = 0;
    private int produto = 0;
    private String urlApi = "";

    public boolean isHabilitada() {
        return habilitada;
    }
    public void setHabilitada(boolean habilitada) {
        this.habilitada = habilitada;
    }

    public String getTokenApi() {
        return tokenApi;
    }

    public void setTokenApi(String tokenApi) {
        this.tokenApi = tokenApi;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public int getProduto() {
        return produto;
    }

    public void setProduto(int produto) {
        this.produto = produto;
    }

    public String getUrlApi() {
        return urlApi;
    }

    public void setUrlApi(String urlApi) {
        this.urlApi = urlApi;
    }
}
