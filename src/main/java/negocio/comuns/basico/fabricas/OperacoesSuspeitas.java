package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class OperacoesSuspeitas extends BiDCCRelAbstractFactory {
    protected OperacoesSuspeitas() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public ResultSet getResult() throws Exception {
        return null;
    }

    @Override
    public List getList() throws Exception {
        return getFacade().getZWFacade().getContratoRecorrencia().consultarOperacoesSuspeitas(
                        codigoEmpresa, "DISTINCT rei.codigo", false, new String[]{"codigo", "valorparcela"});
    }

    @Override
    public Integer getCount() throws Exception {
        return getFacade().getContratoRecorrencia().contarOperacoesSuspeitas(codigoEmpresa,"DISTINCT rei.codigo, mpa.valorparcela", true, new String[]{"codigo", "valorparcela"});
    }
}