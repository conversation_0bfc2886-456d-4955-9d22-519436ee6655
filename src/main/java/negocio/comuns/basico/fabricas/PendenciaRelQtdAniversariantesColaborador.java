package negocio.comuns.basico.fabricas;

import java.sql.ResultSet;

public class PendenciaRelQtdAniversariantesColaborador extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getColaborador()
                .consultarAniversarioColaborador(codigoEmpresaFiltro,confPaginacao,getDataBaseInicialFiltroBI(), getDataBaseFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getColaborador().contarAniversarioColaborador(codigoEmpresaFiltro,getDataBaseInicialFiltroBI(), getDataBaseFiltroBI());
    }
}