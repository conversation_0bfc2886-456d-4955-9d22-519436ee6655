package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesProdutosVencidos extends PendenciaRelAbstractFactory {

    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getMovProduto()
                .consultarClientesProdutosVencidos(codigoEmpresaFiltro,getDataBaseInicialFiltroBI(), getDataBaseFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getProdutoRel().contarClientesProdutosVencidosResumido(codigoEmpresaFiltro,
                ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores),getDataBaseFiltroBI(), getDataBaseInicialFiltroBI());
    }
}