package negocio.comuns.basico.fabricas;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.basico.enumerador.TipoPendenciaEnum;
import negocio.facade.jdbc.arquitetura.FacadeFactory;

import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class PendenciaRelAbstractFactory {
    protected FacadeFactory facadeFactory;
    protected String colaboradores;
    protected int codigoEmpresaFiltro;
    protected ConfPaginacao confPaginacao;
    protected boolean somenteClientesAtivos;
    protected Boolean assinaturaCancelamento;
    protected Map<String, Object> parametros;
    protected static Map<TipoPendenciaEnum, Class<? extends PendenciaRelAbstractFactory>> instancias = new ConcurrentHashMap<TipoPendenciaEnum, Class<? extends PendenciaRelAbstractFactory>>();

    static {
        instancias.put(TipoPendenciaEnum.NENHUM, PendenciaRelNENHUM.class);
        instancias.put(TipoPendenciaEnum.BVPendente, PendenciaRelQtdBVPendente.class);
        instancias.put(TipoPendenciaEnum.CadastroIncompletoCliente, PendenciaRelQtdCadastroIncompleto.class);
        instancias.put(TipoPendenciaEnum.CadastroIncompletoVisitante, PendenciaRelQtdVisitanteCadastroIncompleto.class);
        instancias.put(TipoPendenciaEnum.ParcelaPendAPagar, PendenciasRelQtdParcelaPendAPagar.class);
        instancias.put(TipoPendenciaEnum.ParcelaPendAtraso, PendenciaRelQtdParcelaPendAtraso.class);
        instancias.put(TipoPendenciaEnum.DebitoCCorrente, PendenciaRelQtdDebitoCCorrente.class);
        instancias.put(TipoPendenciaEnum.CreditoCCorrente, PendenciaRelQtdCreditoCCorrente.class);
        instancias.put(TipoPendenciaEnum.Aniversariantes, PendenciaRelQtdAniversariantes.class);
        instancias.put(TipoPendenciaEnum.AniversariantesColaborador, PendenciaRelQtdAniversariantesColaborador.class);
        instancias.put(TipoPendenciaEnum.ParcelaEmAbertoColaborador, PendenciaRelQtdParcelaEmAbertoColaborador.class);
        instancias.put(TipoPendenciaEnum.ClientesProdutosVencidos, PendenciaRelQtdClientesProdutosVencidos.class);
        instancias.put(TipoPendenciaEnum.ClientesSemProdutos, PendenciaRelQtdClientesSemProdutos.class);
        instancias.put(TipoPendenciaEnum.CartoesVencidos, PendenciaRelQtdCartoesVencidos.class);
        instancias.put(TipoPendenciaEnum.CartoesAVencer, PendenciaRelQtdCartoesAVencer.class);
        instancias.put(TipoPendenciaEnum.ClientesMesmoCartao, PendenciaRelQtdClientesMesmoCartao.class);
        instancias.put(TipoPendenciaEnum.ClientesSemFoto, PendenciaRelQtdClientesSemFoto.class);
        instancias.put(TipoPendenciaEnum.ClientesTrancamentoVencidos, PendenciaRelQtdClientesComTrancamentoVencidos.class);
        instancias.put(TipoPendenciaEnum.ClientesTransferidosContratoCancelado, PendenciaRelQtdClientesTransferidosContratoCancelado.class);
        instancias.put(TipoPendenciaEnum.ClientesSemAssinaturaDigital, PendenciaRelQtdClientesSemAssinaturaDigital.class);
        instancias.put(TipoPendenciaEnum.ClientesSemAssinaturaDigitalCancelamento, PendenciaRelQtdClientesSemAssinaturaDigital.class);
        instancias.put(TipoPendenciaEnum.CartoesComProblema, PendenciaRelQtdCartoesComProblema.class);
        instancias.put(TipoPendenciaEnum.ClientesSemGeoLocalizacao, PendenciaRelQtdClienteSemGeoLocalizacao.class);
        instancias.put(TipoPendenciaEnum.ClientesSemBiometriaFacial, PendenciaRelQtdClientesSemBiometriaFacial.class);
    }

    private static Map<TipoPendenciaEnum, PendenciaRelAbstractFactory> instanciar() throws  Exception{
        Map<TipoPendenciaEnum, PendenciaRelAbstractFactory> inst = new HashMap<TipoPendenciaEnum, PendenciaRelAbstractFactory>(instancias.size());
        for(TipoPendenciaEnum key : instancias.keySet()){
            inst.put(key, instancias.get(key).newInstance());
        }
        return inst;
    }

    public static PendenciaRelAbstractFactory getFactory(TipoPendenciaEnum tipo) throws Exception {
        PendenciaRelAbstractFactory instancia = null;
        if(JSFUtilities.isJSFContext()){
            instancia = getInstanciaContexto(tipo);
        }else{
            instancia = getInstancia(tipo);
        }
        if(instancia == null){
            throw new Exception("Não existe classe utilitária de PendenciaRel para o tipo: " + tipo);
        }else{
            carregarAtributosInstancia(instancia);
        }
        return instancia;
    }

    /**
     * Carrega os atribuos da nova instancia de {@link PendenciaRelAbstractFactory}
     * @param instancia
     */
    private static void carregarAtributosInstancia(PendenciaRelAbstractFactory instancia) {
        instancia.parametros = new HashMap<String, Object>();
    }

    /**
     * Realiza uma nova intancia da classe correspodente ao <code>tipo</code> passado como parâmetro.
     * @param tipo
     * @return
     * @throws Exception
     */
    private static PendenciaRelAbstractFactory getInstancia(TipoPendenciaEnum tipo) throws  Exception{
        return instancias.containsKey(tipo) ? instancias.get(tipo).newInstance() : null;
    }

    /**
     * Retorna a instancia do {@link PendenciaRelAbstractFactory} que esta no contexto do usuario.
     * @param tipo
     * @return
     */
    private static PendenciaRelAbstractFactory getInstanciaContexto(TipoPendenciaEnum tipo) throws  Exception{
        Map<TipoPendenciaEnum, PendenciaRelAbstractFactory> map = (Map<TipoPendenciaEnum, PendenciaRelAbstractFactory>) JSFUtilities.getFromSession(PendenciaRelAbstractFactory.class.getSimpleName() + "INSTANCIAS_PENDENCIASRELFACTORY");
        if(map == null){
            map = instanciar();
            JSFUtilities.storeOnSession(PendenciaRelAbstractFactory.class.getSimpleName() + "INSTANCIAS_PENDENCIASRELFACTORY", map);
        }
        return map.get(tipo);
    }

    public static PendenciaRelAbstractFactory getFactory(TipoPendenciaEnum tipo,
                                                         FacadeFactory facadeFactory,
                                                         String colaboradores,
                                                         int codigoEmpresaFiltro,
                                                         ConfPaginacao confPaginacao,
                                                         boolean somenteClientesAtivos,
                                                         boolean assinaturaCancelamento) throws Exception {
        PendenciaRelAbstractFactory fabrica = getFactory(tipo);
        fabrica.facadeFactory = facadeFactory;
        fabrica.colaboradores = colaboradores;
        fabrica.codigoEmpresaFiltro = codigoEmpresaFiltro;
        fabrica.confPaginacao = confPaginacao;
        fabrica.somenteClientesAtivos = somenteClientesAtivos;
        fabrica.assinaturaCancelamento = assinaturaCancelamento;
        return fabrica;
    }

    public String trataMensagem(String mensagem) {
        return mensagem;
    }

    public abstract ResultSet getList() throws Exception;

    public abstract ResultSet getCount() throws Exception;

    List<Integer> getIdsColaboradores() {
        List<Integer> idsColaboradores = new ArrayList<Integer>();

        if(colaboradores.trim().length() > 0) {
            int inicio = colaboradores.indexOf('(');
            int fim = colaboradores.indexOf(')');

            for(String id: colaboradores.substring(inicio+1, fim).split(",")) {
                idsColaboradores.add(Integer.parseInt(id));
            }
        }

        return idsColaboradores;
    }

    public void adicionarParametro(String chave, Object valor){
        this.parametros.put(chave, valor);
    }

    protected Date getDataBaseFiltroBI() {
        return (Date) JSFUtilities.getManagedBean("PendenciaControleRel.dataBaseFiltro");
    }
    protected Date getDataBaseInicialFiltroBI() {
        return (Date) JSFUtilities.getManagedBean("PendenciaControleRel.dataBaseInicialFiltro");
    }
    protected Date getDataBaseFiltroBIDCC() {
        return (Date) JSFUtilities.getManagedBean("RelContratosRecorrenciaControle.dataBaseFiltro");
    }
    public boolean ignorarSeForZero() {
        return false;
    }
}