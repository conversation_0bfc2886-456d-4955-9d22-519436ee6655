package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdDebitoCCorrente extends PendenciaRelAbstractFactory {
    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getMovimentoContaCorrenteCliente().consultarPendenciasClienteDevendoContaCorrente(
                codigoEmpresaFiltro, colaboradores,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getMovimentoContaCorrenteCliente().contarPendenciasClienteDevendoContaCorrente(codigoEmpresaFiltro,
                ( UteisValidacao.emptyString(colaboradores) ? "" : colaboradores ), getDataBaseInicialFiltroBI());
    }
}