package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.Calendario;

import java.sql.ResultSet;
import java.util.List;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * Created by <PERSON> on 22/06/2016.
 */
public class ParcelasEm<PERSON><PERSON> extends BiDCCRelAbstractFactory {

    protected ParcelasEmAberto() {
    }

    @Override
    public Double getSum() throws Exception{
        return 0.0;
    }

    @Override
    public Integer getCount() throws Exception {
        return 0;
    }

    @Override
    public List getList() throws Exception {
        return getFacade().getZWFacade()
                .getContratoRecorrencia().consultarContratosRecorrenciaParcelaEmAbertoDCC(this.codigoEmpresa, this.getDataBaseFiltroBIDCC(), getConvenios());
    }

    @Override
    public ResultSet getResult() throws Exception {
        return  getFacade().getMovParcela().
                contarPendenciaParcelaEmAbertoAPagar(codigoEmpresa, this.getDataBaseFiltroBIDCC(),
                        "", true,true, false, getConvenios());
    }
}