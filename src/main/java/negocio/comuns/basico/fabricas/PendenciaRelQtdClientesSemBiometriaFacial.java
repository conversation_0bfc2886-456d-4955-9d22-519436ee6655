package negocio.comuns.basico.fabricas;

import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.ResultSet;

public class PendenciaRelQtdClientesSemBiometriaFacial extends PendenciaRelAbstractFactory {

    @Override
    public ResultSet getList() throws Exception {
        return facadeFactory.getCliente().consultarClienteSemBiometriaFacial(codigoEmpresaFiltro, colaboradores,confPaginacao, getDataBaseInicialFiltroBI());
    }

    @Override
    public ResultSet getCount() throws Exception {
        return facadeFactory.getCliente().contarClienteSemBiometriaFacial(codigoEmpresaFiltro,
                ( UteisValidacao.emptyString(colaboradores) ? "true" : colaboradores ), getDataBaseInicialFiltroBI());
    }

}
