package negocio.comuns.basico;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfigConvenioDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfigDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoEmailDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoGymBotDTO;
import br.com.pactosolucoes.integracao.pactopay.front.PactoPayConfiguracaoGymBotProDTO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.ConfiguracaoReenvioMovParcelaEmpresa;
import servicos.impl.dcc.base.RemessaService;

import java.sql.Connection;
import java.util.List;

public class PactoPayConfigVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private EmpresaVO empresaVO;

    private boolean envioAutomaticoCobranca = true;

    private boolean cobrancaAntecipadaAtivo = false;
    private boolean cobrancaAntecipadaSMS = false;
    private boolean cobrancaAntecipadaEmail = false;
    private boolean cobrancaAntecipadaWhatsApp = false;
    private boolean cobrancaAntecipadaApp = false;
    private boolean cobrancaAntecipadaGymbotPro = false;
    private boolean cobrancaAntecipadaComAutorizacao = true;
    private boolean cobrancaAntecipadaAplicarDesconto = false;
    private Double cobrancaAntecipadaDesconto;
    private boolean cobrancaAntecipadaValorFixo = false;
    private Integer cobrancaAntecipadaDiasAnteriores;
    private Integer cobrancaAntecipadaDiasLimitePagamento;

    private boolean comunicadoResultadoCobrancaAtivo = false;
    private boolean comunicadoResultadoCobrancaSMS = false;
    private boolean comunicadoResultadoCobrancaEmail = false;
    private boolean comunicadoResultadoCobrancaWhatsApp = false;
    private boolean comunicadoResultadoCobrancaApp = false;
    private boolean comunicadoResultadoCobrancaGymbotPro = false;
    private boolean comunicadoResultadoCobrancaAprovada = false;
    private boolean comunicadoResultadoCobrancaNegada = false;
    private boolean comunicadoResultadoCobrancaCancelada = false;

    private boolean comunicadoAtrasoAtivo = false;
    private boolean comunicadoAtrasoSMS = false;
    private boolean comunicadoAtrasoEmail = false;
    private boolean comunicadoAtrasoWhatsApp = false;
    private boolean comunicadoAtrasoApp = false;
    private boolean comunicadoAtrasoGymbotPro = false;
    private boolean comunicadoAtrasoComAutorizacao = false;
    private Integer comunicadoAtrasoDiasVencidoMinimo;
    private Integer comunicadoAtrasoDiasVencidoMaximo;
    private Integer comunicadoAtrasoIntervaloDias;
    private Integer comunicadoAtrasoQtdMaximaEnvios;

    private boolean comunicadoCartaoAtivo = false;
    private boolean comunicadoCartaoSMS = false;
    private boolean comunicadoCartaoEmail = false;
    private boolean comunicadoCartaoWhatsApp = false;
    private boolean comunicadoCartaoApp = false;
    private boolean comunicadoCartaoGymbotPro = false;
    private boolean comunicadoCartaoVencido = false;
    private boolean comunicadoCartaoProximoVencimento = false;

    private boolean retentativaAutomaticaAtivo = true;

    private PactoPayConfiguracaoEmailDTO configuracaoEmail;
    private PactoPayConfiguracaoGymBotDTO configuracaoGymBot;
    private PactoPayConfiguracaoGymBotProDTO configuracaoGymBotPro;

    private Integer qtdDiasEficienciaComunicacao;

    public PactoPayConfigVO() {
    }

    public PactoPayConfigVO(boolean novo) {
        if (novo) {
            //valores default
            this.setCobrancaAntecipadaDiasAnteriores(5);
            this.setComunicadoAtrasoDiasVencidoMinimo(5);
            this.setComunicadoAtrasoDiasVencidoMaximo(90);
            this.setComunicadoAtrasoIntervaloDias(3);
            this.setComunicadoAtrasoQtdMaximaEnvios(3);
        }
    }

    public static void validarDados(PactoPayConfigVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        //envio antecipado de cobrança
        if (obj.isCobrancaAntecipadaAtivo()) {

            if (!obj.isCobrancaAntecipadaEmail() &&
                    !obj.isCobrancaAntecipadaSMS() &&
                    !obj.isCobrancaAntecipadaWhatsApp() &&
                    !obj.isCobrancaAntecipadaApp() &&
                    !obj.isCobrancaAntecipadaGymbotPro()) {
                throw new ConsistirException("Comunicação de cobrança antecipada, selecione pelo menos uma forma de comunicação.");
            }

            if (obj.isCobrancaAntecipadaAplicarDesconto()) {
                if (UteisValidacao.emptyNumber(obj.getCobrancaAntecipadaDesconto())) {
                    throw new ConsistirException("O desconto deve ser informado.");
                }

                if (!obj.isCobrancaAntecipadaValorFixo() && obj.getCobrancaAntecipadaDesconto() >= 100) {
                    throw new ConsistirException("O desconto não pode ser superior ou igual a 100%.");
                }
            }

            if (obj.getCobrancaAntecipadaDiasAnteriores() < 5 || obj.getCobrancaAntecipadaDiasAnteriores() > 15) {
                throw new ConsistirException("A quantidade de dias antes do vencimento deve ser entre 5 e 15 dias.");
            }
        }

        //comunicar resultado da cobrança
        if (obj.isComunicadoResultadoCobrancaAtivo()) {
            if (!obj.isComunicadoResultadoCobrancaAprovada() &&
                    !obj.isComunicadoResultadoCobrancaNegada() &&
                    !obj.isComunicadoResultadoCobrancaCancelada()) {
                throw new ConsistirException("Comunicação de resultado de cobrança, selecione pelo menos uma operação.");
            }

            if (!obj.isComunicadoResultadoCobrancaEmail() &&
                    !obj.isComunicadoResultadoCobrancaSMS() &&
                    !obj.isComunicadoResultadoCobrancaWhatsApp() &&
                    !obj.isComunicadoResultadoCobrancaApp() &&
                    !obj.isComunicadoResultadoCobrancaGymbotPro()) {
                throw new ConsistirException("Comunicação de resultado de cobrança, selecione pelo menos uma forma de comunicação.");
            }
        }

//        //retentativa automatica de cobran�a
//        if (obj.isRetentativaAutomaticaAtivo()) {
//            if (obj.getRetentativaAutomaticaLimiteDias() > 95) {
//                throw new ConsistirException("A configuração \"Limite de dias após o vencimento para parcela entrar na Retentativa de cobrança\" deve ser no máximo 95 dias");
//            }
//            if (obj.getRetentativaAutomaticaIntervaloDias() < 3 || obj.getRetentativaAutomaticaIntervaloDias() > 15) {
//                throw new ConsistirException("A configuração \"Intervalo de dias para retentativa de cobrança da parcela\" deve ser no mínimo 3 dias");
//            }
//        }

        //multiplos convênios
//        if (obj.mul) {
//
//        }

        //envio de cobranças em atraso
        if (obj.isComunicadoAtrasoAtivo()) {
            if (!obj.isComunicadoAtrasoEmail() &&
                    !obj.isComunicadoAtrasoSMS() &&
                    !obj.isComunicadoAtrasoWhatsApp() &&
                    !obj.isComunicadoAtrasoApp() &&
                    !obj.isComunicadoAtrasoGymbotPro()) {
                throw new ConsistirException("Comunicação de atraso, selecione pelo menos uma forma de comunicação.");
            }

            if (obj.getComunicadoAtrasoIntervaloDias() < 3 || obj.getComunicadoAtrasoIntervaloDias() > 30) {
                throw new ConsistirException("A configuração \"Intervalo de envio\" deve ser no mínimo 3 e no máximo 30 dias");
            }

            if (obj.getComunicadoAtrasoIntervaloDias() < 3 || obj.getComunicadoAtrasoIntervaloDias() > 30) {
//                throw new ConsistirException("A configuração \"Quantidade de envios\" deve ser no mínimo 3 e no máximo 30 dias");
            }
        }

        //comunicar vencimento do cartão
        if (obj.isComunicadoCartaoAtivo()) {
            if (!obj.isComunicadoCartaoEmail() &&
                    !obj.isComunicadoCartaoSMS() &&
                    !obj.isComunicadoCartaoWhatsApp() &&
                    !obj.isComunicadoCartaoApp() &&
                    !obj.isComunicadoCartaoGymbotPro()) {
                throw new ConsistirException("Comunicação de cartão, selecione pelo menos uma forma de comunicação.");
            }

            if (!obj.isComunicadoCartaoVencido() &&
                    !obj.isComunicadoCartaoProximoVencimento()) {
                throw new ConsistirException("Comunicação de cartão, selecione pelo menos uma forma de envio de cartão (vencido/ próximo do vencimento).");
            }
        }
    }

    public PactoPayConfigDTO toDTO(EmpresaVO empresaVO, Connection con) throws Exception {
        PactoPayConfigDTO configDTO = new PactoPayConfigDTO();
        configDTO.setCodigo(this.getCodigo());
        configDTO.setEmpresa(this.getEmpresaVO().getCodigo());

        configDTO.setEnvio_automatico_cobranca(this.isEnvioAutomaticoCobranca());

        configDTO.getCobranca_antecipada().setAtivo(this.isCobrancaAntecipadaAtivo());

        configDTO.getCobranca_antecipada().setSms(this.isCobrancaAntecipadaSMS());
        configDTO.getCobranca_antecipada().setEmail(this.isCobrancaAntecipadaEmail());
        configDTO.getCobranca_antecipada().setWhatsapp(this.isCobrancaAntecipadaWhatsApp());
        configDTO.getCobranca_antecipada().setApp(this.isCobrancaAntecipadaApp());
        configDTO.getCobranca_antecipada().setGymbotpro(this.isCobrancaAntecipadaGymbotPro());

        configDTO.getCobranca_antecipada().setCom_autorizacao(this.isCobrancaAntecipadaComAutorizacao());
        configDTO.getCobranca_antecipada().setAplicar_desconto(this.isCobrancaAntecipadaAplicarDesconto());
        configDTO.getCobranca_antecipada().setDesconto(this.getCobrancaAntecipadaDesconto());
        configDTO.getCobranca_antecipada().setValor_fixo(this.isCobrancaAntecipadaValorFixo());
        configDTO.getCobranca_antecipada().setDias_anteriores(this.getCobrancaAntecipadaDiasAnteriores());
        configDTO.getCobranca_antecipada().setDias_limite(this.getCobrancaAntecipadaDiasLimitePagamento());

        configDTO.getComunicado_resultado().setAtivo(this.isComunicadoResultadoCobrancaAtivo());
        configDTO.getComunicado_resultado().setSms(this.isComunicadoResultadoCobrancaSMS());
        configDTO.getComunicado_resultado().setEmail(this.isComunicadoResultadoCobrancaEmail());
        configDTO.getComunicado_resultado().setWhatsapp(this.isComunicadoResultadoCobrancaWhatsApp());
        configDTO.getComunicado_resultado().setApp(this.isComunicadoResultadoCobrancaApp());
        configDTO.getComunicado_resultado().setGymbotpro(this.isComunicadoResultadoCobrancaGymbotPro());
        configDTO.getComunicado_resultado().setAprovada(this.isComunicadoResultadoCobrancaAprovada());
        configDTO.getComunicado_resultado().setNegada(this.isComunicadoResultadoCobrancaNegada());
        configDTO.getComunicado_resultado().setCancelada(this.isComunicadoResultadoCobrancaCancelada());

        configDTO.getComunicado_atraso().setAtivo(this.isComunicadoAtrasoAtivo());
        configDTO.getComunicado_atraso().setSms(this.isComunicadoAtrasoSMS());
        configDTO.getComunicado_atraso().setEmail(this.isComunicadoAtrasoEmail());
        configDTO.getComunicado_atraso().setWhatsapp(this.isComunicadoAtrasoWhatsApp());
        configDTO.getComunicado_atraso().setApp(this.isComunicadoAtrasoApp());
        configDTO.getComunicado_atraso().setGymbotpro(this.isComunicadoAtrasoGymbotPro());
        configDTO.getComunicado_atraso().setCom_autorizacao(this.isComunicadoAtrasoComAutorizacao());
        configDTO.getComunicado_atraso().setDias_vencido_minimo(this.getComunicadoAtrasoDiasVencidoMinimo());
        configDTO.getComunicado_atraso().setDias_vencido_maximo(this.getComunicadoAtrasoDiasVencidoMaximo());
        configDTO.getComunicado_atraso().setIntervalo_dias(this.getComunicadoAtrasoIntervaloDias());
        configDTO.getComunicado_atraso().setQuantidade_maxima_envios(this.getComunicadoAtrasoQtdMaximaEnvios());

        configDTO.getComunicado_cartao().setAtivo(this.isComunicadoCartaoAtivo());
        configDTO.getComunicado_cartao().setSms(this.isComunicadoCartaoSMS());
        configDTO.getComunicado_cartao().setEmail(this.isComunicadoCartaoEmail());
        configDTO.getComunicado_cartao().setWhatsapp(this.isComunicadoCartaoWhatsApp());
        configDTO.getComunicado_cartao().setApp(this.isComunicadoCartaoApp());
        configDTO.getComunicado_cartao().setGymbotpro(this.isComunicadoCartaoGymbotPro());
        configDTO.getComunicado_cartao().setVencido(this.isComunicadoCartaoVencido());
        configDTO.getComunicado_cartao().setProximo_vencimento(this.isComunicadoCartaoProximoVencimento());

        //configuracao de sms
        configDTO.setSms_configurado(empresaVO.isFacilitePayReguaCobrancaSms());

        //configuracao de email
        configDTO.setConfiguracao_email(this.getConfiguracaoEmail());
        configDTO.setEmail_configurado(empresaVO.isFacilitePayReguaCobrancaEmail());
        configDTO.setEmail_wagi_configurado(this.getConfiguracaoEmail().isWagi());

        //configuracao de whatsapp
        configDTO.setWhatsapp_configurado(empresaVO.isFacilitePayReguaCobrancaWhatsApp());
        configDTO.setConfiguracao_gymbot(this.getConfiguracaoGymBot());

        //Configuracao do GymbotPro
        configDTO.setGymbotpro_configurado(empresaVO.isFacilitePayReguaCobrancaGymbotPro());
        configDTO.setConfiguracao_gymbotpro(this.getConfiguracaoGymBotPro());

        //configuracao de app
        configDTO.setApp_configurado(empresaVO.isFacilitePayReguaCobrancaApp());

        //retentativa autom�tica
        configDTO.getRetentativa().setAtivo(this.isRetentativaAutomaticaAtivo());
        configDTO.getRetentativa().setLimite_dias(empresaVO.getQtdDiasLimiteCobrancaParcelasRecorrencia());
        configDTO.getRetentativa().setIntervalo_dias(empresaVO.getQtdDiasRepetirCobrancaParcelasRecorrencia());

        //multiplos conv�nios
        configDTO.getMultiplos_convenios().setAtivo(empresaVO.isHabilitarReenvioAutomaticoRemessa());
        configDTO.getMultiplos_convenios().setQtd_tentativas(empresaVO.getQtdExecucoesRetentativa());

        configDTO.setQtdDiasEficienciaComunicacao(this.getQtdDiasEficienciaComunicacao());

        ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioDAO;
        try {
            configuracaoReenvioDAO = new ConfiguracaoReenvioMovParcelaEmpresa(con);
            List<ConvenioCobrancaVO> convenios = configuracaoReenvioDAO.consultarConvenioReenvioAutomatico(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS,
                    TipoConvenioCobrancaEnum.ARRAY_CONVENIOS_DCC_RETENTATIVA, SituacaoConvenioCobranca.ATIVO);
            int i = 0;
            for (ConvenioCobrancaVO conv : convenios) {
                configDTO.getMultiplos_convenios().getConvenios().add(new PactoPayConfigConvenioDTO(++i, conv));
            }
        } finally {
            configuracaoReenvioDAO = null;
        }
        return configDTO;
    }

    public void alterarBaseadoDTO(PactoPayConfigDTO dto) {
        this.setEnvioAutomaticoCobranca(dto.isEnvio_automatico_cobranca());

        this.setCobrancaAntecipadaAtivo(dto.getCobranca_antecipada().isAtivo());
        this.setCobrancaAntecipadaSMS(dto.getCobranca_antecipada().isSms());
        this.setCobrancaAntecipadaEmail(dto.getCobranca_antecipada().isEmail());
        this.setCobrancaAntecipadaWhatsApp(dto.getCobranca_antecipada().isWhatsapp());
        this.setCobrancaAntecipadaApp(dto.getCobranca_antecipada().isApp());
        this.setCobrancaAntecipadaGymbotPro(dto.getCobranca_antecipada().isGymbotpro());
        this.setCobrancaAntecipadaComAutorizacao(dto.getCobranca_antecipada().isCom_autorizacao());
        this.setCobrancaAntecipadaAplicarDesconto(dto.getCobranca_antecipada().isAplicar_desconto());
        this.setCobrancaAntecipadaDesconto(dto.getCobranca_antecipada().getDesconto());
        this.setCobrancaAntecipadaValorFixo(dto.getCobranca_antecipada().isValor_fixo());
        this.setCobrancaAntecipadaDiasAnteriores(dto.getCobranca_antecipada().getDias_anteriores());
        this.setCobrancaAntecipadaDiasLimitePagamento(dto.getCobranca_antecipada().getDias_limite());

        this.setComunicadoResultadoCobrancaAtivo(dto.getComunicado_resultado().isAtivo());
        this.setComunicadoResultadoCobrancaSMS(dto.getComunicado_resultado().isSms());
        this.setComunicadoResultadoCobrancaEmail(dto.getComunicado_resultado().isEmail());
        this.setComunicadoResultadoCobrancaWhatsApp(dto.getComunicado_resultado().isWhatsapp());
        this.setComunicadoResultadoCobrancaApp(dto.getComunicado_resultado().isApp());
        this.setComunicadoResultadoCobrancaGymbotPro(dto.getComunicado_resultado().isGymbotpro());
        this.setComunicadoResultadoCobrancaAprovada(dto.getComunicado_resultado().isAprovada());
        this.setComunicadoResultadoCobrancaNegada(dto.getComunicado_resultado().isNegada());
        this.setComunicadoResultadoCobrancaCancelada(dto.getComunicado_resultado().isCancelada());

        this.setComunicadoAtrasoAtivo(dto.getComunicado_atraso().isAtivo());
        this.setComunicadoAtrasoSMS(dto.getComunicado_atraso().isSms());
        this.setComunicadoAtrasoEmail(dto.getComunicado_atraso().isEmail());
        this.setComunicadoAtrasoWhatsApp(dto.getComunicado_atraso().isWhatsapp());
        this.setComunicadoAtrasoApp(dto.getComunicado_atraso().isApp());
        this.setComunicadoAtrasoGymbotPro(dto.getComunicado_atraso().isGymbotpro());
        this.setComunicadoAtrasoComAutorizacao(dto.getComunicado_atraso().isCom_autorizacao());
        this.setComunicadoAtrasoDiasVencidoMinimo(dto.getComunicado_atraso().getDias_vencido_minimo());
        this.setComunicadoAtrasoDiasVencidoMaximo(dto.getComunicado_atraso().getDias_vencido_maximo());
        this.setComunicadoAtrasoIntervaloDias(dto.getComunicado_atraso().getIntervalo_dias());
        this.setComunicadoAtrasoQtdMaximaEnvios(dto.getComunicado_atraso().getQuantidade_maxima_envios());

        this.setComunicadoCartaoAtivo(dto.getComunicado_cartao().isAtivo());
        this.setComunicadoCartaoSMS(dto.getComunicado_cartao().isSms());
        this.setComunicadoCartaoEmail(dto.getComunicado_cartao().isEmail());
        this.setComunicadoCartaoWhatsApp(dto.getComunicado_cartao().isWhatsapp());
        this.setComunicadoCartaoApp(dto.getComunicado_cartao().isApp());
        this.setComunicadoCartaoGymbotPro(dto.getComunicado_cartao().isGymbotpro());
        this.setComunicadoCartaoVencido(dto.getComunicado_cartao().isVencido());
        this.setComunicadoCartaoProximoVencimento(dto.getComunicado_cartao().isProximo_vencimento());

        this.setRetentativaAutomaticaAtivo(dto.getRetentativa().isAtivo());

        this.setQtdDiasEficienciaComunicacao(dto.getQtdDiasEficienciaComunicacao());
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public EmpresaVO getEmpresaVO() {
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
        }
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public boolean isComunicadoAtrasoAtivo() {
        return comunicadoAtrasoAtivo;
    }

    public void setComunicadoAtrasoAtivo(boolean comunicadoAtrasoAtivo) {
        this.comunicadoAtrasoAtivo = comunicadoAtrasoAtivo;
    }

    public boolean isComunicadoCartaoAtivo() {
        return comunicadoCartaoAtivo;
    }

    public void setComunicadoCartaoAtivo(boolean comunicadoCartaoAtivo) {
        this.comunicadoCartaoAtivo = comunicadoCartaoAtivo;
    }

    public boolean isComunicadoResultadoCobrancaAtivo() {
        return comunicadoResultadoCobrancaAtivo;
    }

    public void setComunicadoResultadoCobrancaAtivo(boolean comunicadoResultadoCobrancaAtivo) {
        this.comunicadoResultadoCobrancaAtivo = comunicadoResultadoCobrancaAtivo;
    }

    public boolean isCobrancaAntecipadaAtivo() {
        return cobrancaAntecipadaAtivo;
    }

    public void setCobrancaAntecipadaAtivo(boolean cobrancaAntecipadaAtivo) {
        this.cobrancaAntecipadaAtivo = cobrancaAntecipadaAtivo;
    }

    public boolean isEnvioAutomaticoCobranca() {
        return envioAutomaticoCobranca;
    }

    public void setEnvioAutomaticoCobranca(boolean envioAutomaticoCobranca) {
        this.envioAutomaticoCobranca = envioAutomaticoCobranca;
    }

    public boolean isRetentativaAutomaticaAtivo() {
        return retentativaAutomaticaAtivo;
    }

    public void setRetentativaAutomaticaAtivo(boolean retentativaAutomaticaAtivo) {
        this.retentativaAutomaticaAtivo = retentativaAutomaticaAtivo;
    }

    public boolean isComunicadoResultadoCobrancaSMS() {
        return comunicadoResultadoCobrancaSMS;
    }

    public void setComunicadoResultadoCobrancaSMS(boolean comunicadoResultadoCobrancaSMS) {
        this.comunicadoResultadoCobrancaSMS = comunicadoResultadoCobrancaSMS;
    }

    public boolean isComunicadoResultadoCobrancaEmail() {
        return comunicadoResultadoCobrancaEmail;
    }

    public void setComunicadoResultadoCobrancaEmail(boolean comunicadoResultadoCobrancaEmail) {
        this.comunicadoResultadoCobrancaEmail = comunicadoResultadoCobrancaEmail;
    }

    public boolean isComunicadoResultadoCobrancaAprovada() {
        return comunicadoResultadoCobrancaAprovada;
    }

    public void setComunicadoResultadoCobrancaAprovada(boolean comunicadoResultadoCobrancaAprovada) {
        this.comunicadoResultadoCobrancaAprovada = comunicadoResultadoCobrancaAprovada;
    }

    public boolean isComunicadoResultadoCobrancaCancelada() {
        return comunicadoResultadoCobrancaCancelada;
    }

    public void setComunicadoResultadoCobrancaCancelada(boolean comunicadoResultadoCobrancaCancelada) {
        this.comunicadoResultadoCobrancaCancelada = comunicadoResultadoCobrancaCancelada;
    }

    public boolean isComunicadoResultadoCobrancaNegada() {
        return comunicadoResultadoCobrancaNegada;
    }

    public void setComunicadoResultadoCobrancaNegada(boolean comunicadoResultadoCobrancaNegada) {
        this.comunicadoResultadoCobrancaNegada = comunicadoResultadoCobrancaNegada;
    }

    public boolean isComunicadoAtrasoSMS() {
        return comunicadoAtrasoSMS;
    }

    public void setComunicadoAtrasoSMS(boolean comunicadoAtrasoSMS) {
        this.comunicadoAtrasoSMS = comunicadoAtrasoSMS;
    }

    public boolean isComunicadoAtrasoEmail() {
        return comunicadoAtrasoEmail;
    }

    public void setComunicadoAtrasoEmail(boolean comunicadoAtrasoEmail) {
        this.comunicadoAtrasoEmail = comunicadoAtrasoEmail;
    }

    public boolean isComunicadoCartaoSMS() {
        return comunicadoCartaoSMS;
    }

    public void setComunicadoCartaoSMS(boolean comunicadoCartaoSMS) {
        this.comunicadoCartaoSMS = comunicadoCartaoSMS;
    }

    public boolean isComunicadoCartaoEmail() {
        return comunicadoCartaoEmail;
    }

    public void setComunicadoCartaoEmail(boolean comunicadoCartaoEmail) {
        this.comunicadoCartaoEmail = comunicadoCartaoEmail;
    }

    public Integer getComunicadoAtrasoIntervaloDias() {
        if (comunicadoAtrasoIntervaloDias == null) {
            comunicadoAtrasoIntervaloDias = 0;
        }
        return comunicadoAtrasoIntervaloDias;
    }

    public void setComunicadoAtrasoIntervaloDias(Integer comunicadoAtrasoIntervaloDias) {
        this.comunicadoAtrasoIntervaloDias = comunicadoAtrasoIntervaloDias;
    }

    public Integer getComunicadoAtrasoQtdMaximaEnvios() {
        if (comunicadoAtrasoQtdMaximaEnvios == null) {
            comunicadoAtrasoQtdMaximaEnvios = 0;
        }
        return comunicadoAtrasoQtdMaximaEnvios;
    }

    public void setComunicadoAtrasoQtdMaximaEnvios(Integer comunicadoAtrasoQtdMaximaEnvios) {
        this.comunicadoAtrasoQtdMaximaEnvios = comunicadoAtrasoQtdMaximaEnvios;
    }

    public Integer getComunicadoAtrasoDiasVencidoMinimo() {
        if (comunicadoAtrasoDiasVencidoMinimo == null) {
            comunicadoAtrasoDiasVencidoMinimo = 0;
        }
        return comunicadoAtrasoDiasVencidoMinimo;
    }

    public void setComunicadoAtrasoDiasVencidoMinimo(Integer comunicadoAtrasoDiasVencidoMinimo) {
        this.comunicadoAtrasoDiasVencidoMinimo = comunicadoAtrasoDiasVencidoMinimo;
    }

    public Integer getComunicadoAtrasoDiasVencidoMaximo() {
        if (comunicadoAtrasoDiasVencidoMaximo == null) {
            comunicadoAtrasoDiasVencidoMaximo = 0;
        }
        return comunicadoAtrasoDiasVencidoMaximo;
    }

    public void setComunicadoAtrasoDiasVencidoMaximo(Integer comunicadoAtrasoDiasVencidoMaximo) {
        this.comunicadoAtrasoDiasVencidoMaximo = comunicadoAtrasoDiasVencidoMaximo;
    }

    public boolean isCobrancaAntecipadaComAutorizacao() {
        return cobrancaAntecipadaComAutorizacao;
    }

    public void setCobrancaAntecipadaComAutorizacao(boolean cobrancaAntecipadaComAutorizacao) {
        this.cobrancaAntecipadaComAutorizacao = cobrancaAntecipadaComAutorizacao;
    }

    public boolean isCobrancaAntecipadaAplicarDesconto() {
        return cobrancaAntecipadaAplicarDesconto;
    }

    public void setCobrancaAntecipadaAplicarDesconto(boolean cobrancaAntecipadaAplicarDesconto) {
        this.cobrancaAntecipadaAplicarDesconto = cobrancaAntecipadaAplicarDesconto;
    }

    public Double getCobrancaAntecipadaDesconto() {
        if (cobrancaAntecipadaDesconto == null) {
            cobrancaAntecipadaDesconto = 0.0;
        }
        return cobrancaAntecipadaDesconto;
    }

    public void setCobrancaAntecipadaDesconto(Double cobrancaAntecipadaDesconto) {
        this.cobrancaAntecipadaDesconto = cobrancaAntecipadaDesconto;
    }

    public boolean isCobrancaAntecipadaValorFixo() {
        return cobrancaAntecipadaValorFixo;
    }

    public void setCobrancaAntecipadaValorFixo(boolean cobrancaAntecipadaValorFixo) {
        this.cobrancaAntecipadaValorFixo = cobrancaAntecipadaValorFixo;
    }

    public Integer getCobrancaAntecipadaDiasAnteriores() {
        if (cobrancaAntecipadaDiasAnteriores == null) {
            cobrancaAntecipadaDiasAnteriores = 0;
        }
        return cobrancaAntecipadaDiasAnteriores;
    }

    public void setCobrancaAntecipadaDiasAnteriores(Integer cobrancaAntecipadaDiasAnteriores) {
        this.cobrancaAntecipadaDiasAnteriores = cobrancaAntecipadaDiasAnteriores;
    }

    public Integer getCobrancaAntecipadaDiasLimitePagamento() {
        if (cobrancaAntecipadaDiasLimitePagamento == null) {
            cobrancaAntecipadaDiasLimitePagamento = 0;
        }
        return cobrancaAntecipadaDiasLimitePagamento;
    }

    public void setCobrancaAntecipadaDiasLimitePagamento(Integer cobrancaAntecipadaDiasLimitePagamento) {
        this.cobrancaAntecipadaDiasLimitePagamento = cobrancaAntecipadaDiasLimitePagamento;
    }

    public boolean isComunicadoCartaoVencido() {
        return comunicadoCartaoVencido;
    }

    public void setComunicadoCartaoVencido(boolean comunicadoCartaoVencido) {
        this.comunicadoCartaoVencido = comunicadoCartaoVencido;
    }

    public boolean isComunicadoCartaoProximoVencimento() {
        return comunicadoCartaoProximoVencimento;
    }

    public void setComunicadoCartaoProximoVencimento(boolean comunicadoCartaoProximoVencimento) {
        this.comunicadoCartaoProximoVencimento = comunicadoCartaoProximoVencimento;
    }

    public boolean isComunicadoAtrasoComAutorizacao() {
        return comunicadoAtrasoComAutorizacao;
    }

    public void setComunicadoAtrasoComAutorizacao(boolean comunicadoAtrasoComAutorizacao) {
        this.comunicadoAtrasoComAutorizacao = comunicadoAtrasoComAutorizacao;
    }

    public PactoPayConfiguracaoEmailDTO getConfiguracaoEmail() {
        if (configuracaoEmail == null) {
            configuracaoEmail = new PactoPayConfiguracaoEmailDTO();
        }
        return configuracaoEmail;
    }

    public void setConfiguracaoEmail(PactoPayConfiguracaoEmailDTO configuracaoEmail) {
        this.configuracaoEmail = configuracaoEmail;
    }

    public boolean isComunicadoResultadoCobrancaWhatsApp() {
        return comunicadoResultadoCobrancaWhatsApp;
    }

    public void setComunicadoResultadoCobrancaWhatsApp(boolean comunicadoResultadoCobrancaWhatsApp) {
        this.comunicadoResultadoCobrancaWhatsApp = comunicadoResultadoCobrancaWhatsApp;
    }

    public boolean isComunicadoAtrasoWhatsApp() {
        return comunicadoAtrasoWhatsApp;
    }

    public void setComunicadoAtrasoWhatsApp(boolean comunicadoAtrasoWhatsApp) {
        this.comunicadoAtrasoWhatsApp = comunicadoAtrasoWhatsApp;
    }

    public boolean isComunicadoCartaoWhatsApp() {
        return comunicadoCartaoWhatsApp;
    }

    public void setComunicadoCartaoWhatsApp(boolean comunicadoCartaoWhatsApp) {
        this.comunicadoCartaoWhatsApp = comunicadoCartaoWhatsApp;
    }

    public boolean isComunicadoResultadoCobrancaApp() {
        return comunicadoResultadoCobrancaApp;
    }

    public void setComunicadoResultadoCobrancaApp(boolean comunicadoResultadoCobrancaApp) {
        this.comunicadoResultadoCobrancaApp = comunicadoResultadoCobrancaApp;
    }

    public boolean isComunicadoAtrasoApp() {
        return comunicadoAtrasoApp;
    }

    public void setComunicadoAtrasoApp(boolean comunicadoAtrasoApp) {
        this.comunicadoAtrasoApp = comunicadoAtrasoApp;
    }

    public boolean isComunicadoCartaoApp() {
        return comunicadoCartaoApp;
    }

    public void setComunicadoCartaoApp(boolean comunicadoCartaoApp) {
        this.comunicadoCartaoApp = comunicadoCartaoApp;
    }

    public PactoPayConfiguracaoGymBotDTO getConfiguracaoGymBot() {
        if (configuracaoGymBot == null) {
            configuracaoGymBot = new PactoPayConfiguracaoGymBotDTO();
        }
        return configuracaoGymBot;
    }

    public void setConfiguracaoGymBot(PactoPayConfiguracaoGymBotDTO configuracaoGymBot) {
        this.configuracaoGymBot = configuracaoGymBot;
    }

    public boolean isCobrancaAntecipadaSMS() {
        return cobrancaAntecipadaSMS;
    }

    public void setCobrancaAntecipadaSMS(boolean cobrancaAntecipadaSMS) {
        this.cobrancaAntecipadaSMS = cobrancaAntecipadaSMS;
    }

    public boolean isCobrancaAntecipadaEmail() {
        return cobrancaAntecipadaEmail;
    }

    public void setCobrancaAntecipadaEmail(boolean cobrancaAntecipadaEmail) {
        this.cobrancaAntecipadaEmail = cobrancaAntecipadaEmail;
    }

    public boolean isCobrancaAntecipadaWhatsApp() {
        return cobrancaAntecipadaWhatsApp;
    }

    public void setCobrancaAntecipadaWhatsApp(boolean cobrancaAntecipadaWhatsApp) {
        this.cobrancaAntecipadaWhatsApp = cobrancaAntecipadaWhatsApp;
    }

    public boolean isCobrancaAntecipadaApp() {
        return cobrancaAntecipadaApp;
    }

    public void setCobrancaAntecipadaApp(boolean cobrancaAntecipadaApp) {
        this.cobrancaAntecipadaApp = cobrancaAntecipadaApp;
    }

    public Integer getQtdDiasEficienciaComunicacao() {
        return qtdDiasEficienciaComunicacao;
    }

    public void setQtdDiasEficienciaComunicacao(Integer qtdDiasEficienciaComunicacao) {
        this.qtdDiasEficienciaComunicacao = qtdDiasEficienciaComunicacao;
    }

    public boolean isCobrancaAntecipadaGymbotPro() {
        return cobrancaAntecipadaGymbotPro;
    }

    public void setCobrancaAntecipadaGymbotPro(boolean cobrancaAntecipadaGymbotPro) {
        this.cobrancaAntecipadaGymbotPro = cobrancaAntecipadaGymbotPro;
    }

    public boolean isComunicadoResultadoCobrancaGymbotPro() {
        return comunicadoResultadoCobrancaGymbotPro;
    }

    public void setComunicadoResultadoCobrancaGymbotPro(boolean comunicadoResultadoCobrancaGymbotPro) {
        this.comunicadoResultadoCobrancaGymbotPro = comunicadoResultadoCobrancaGymbotPro;
    }

    public boolean isComunicadoAtrasoGymbotPro() {
        return comunicadoAtrasoGymbotPro;
    }

    public void setComunicadoAtrasoGymbotPro(boolean comunicadoAtrasoGymbotPro) {
        this.comunicadoAtrasoGymbotPro = comunicadoAtrasoGymbotPro;
    }

    public boolean isComunicadoCartaoGymbotPro() {
        return comunicadoCartaoGymbotPro;
    }

    public void setComunicadoCartaoGymbotPro(boolean comunicadoCartaoGymbotPro) {
        this.comunicadoCartaoGymbotPro = comunicadoCartaoGymbotPro;
    }

    public PactoPayConfiguracaoGymBotProDTO getConfiguracaoGymBotPro() {
        if(configuracaoGymBotPro == null) {
            configuracaoGymBotPro = new PactoPayConfiguracaoGymBotProDTO();
        }
        return configuracaoGymBotPro;
    }

    public void setConfiguracaoGymBotPro(PactoPayConfiguracaoGymBotProDTO configuracaoGymBotPro) {
        this.configuracaoGymBotPro = configuracaoGymBotPro;
    }
}
