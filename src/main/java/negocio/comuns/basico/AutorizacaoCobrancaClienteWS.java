package negocio.comuns.basico;


/**
 * Created by <PERSON><PERSON> on 26/06/2015
 */
public class AutorizacaoCobrancaClienteWS {

    private Integer codigo;
    private Integer cliente;
    private int operadoraCartao;
    private String validadeCartao;
    private int mesValidade;
    private int anoValidade;
    private Integer agencia = 0;
    private String agenciaDV = "0";
    private Integer contaCorrente = 0;
    private String contaCorrenteDV = "0";
    private String listaObjetosACobrar = "";
    private int convenio;
    private String cpfTitular = "";
    private String numeroCartao = "";
    private String nomeTitularCartao;
    private String cvv;
    private String operadoraCartaoDescricao;
    private String tipoAutorizacaoCobranca;
    private String banco;
    private String codigoOperacao;
    private String codigoBanco;
    private Boolean transacaoOnline = false;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCliente() {
        return cliente;
    }

    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    public int getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(int operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public String getValidadeCartao() {
        return validadeCartao;
    }

    public void setValidadeCartao(String validadeCartao) {
        this.validadeCartao = validadeCartao;
    }

    public int getMesValidade() {
        return mesValidade;
    }

    public void setMesValidade(int mesValidade) {
        this.mesValidade = mesValidade;
    }

    public int getAnoValidade() {
        return anoValidade;
    }

    public void setAnoValidade(int anoValidade) {
        this.anoValidade = anoValidade;
    }

    public Integer getAgencia() {
        return agencia;
    }

    public void setAgencia(Integer agencia) {
        this.agencia = agencia;
    }

    public String getAgenciaDV() {
        return agenciaDV;
    }

    public void setAgenciaDV(String agenciaDV) {
        this.agenciaDV = agenciaDV;
    }

    public Integer getContaCorrente() {
        return contaCorrente;
    }

    public void setContaCorrente(Integer contaCorrente) {
        this.contaCorrente = contaCorrente;
    }

    public String getContaCorrenteDV() {
        return contaCorrenteDV;
    }

    public void setContaCorrenteDV(String contaCorrenteDV) {
        this.contaCorrenteDV = contaCorrenteDV;
    }

    public String getListaObjetosACobrar() {
        return listaObjetosACobrar;
    }

    public void setListaObjetosACobrar(String listaObjetosACobrar) {
        this.listaObjetosACobrar = listaObjetosACobrar;
    }

    public int getConvenio() {
        return convenio;
    }

    public void setConvenio(int convenio) {
        this.convenio = convenio;
    }

    public String getCpfTitular() {
        return cpfTitular;
    }

    public void setCpfTitular(String cpfTitular) {
        this.cpfTitular = cpfTitular;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public String getNomeTitularCartao() {
        if (nomeTitularCartao == null) {
            nomeTitularCartao = "";
        }
        return nomeTitularCartao;
    }

    public void setNomeTitularCartao(String nomeTitularCartao) {
        this.nomeTitularCartao = nomeTitularCartao;
    }

    public String getCvv() {
        if (cvv == null) {
            cvv = "";
        }
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getOperadoraCartaoDescricao() {
        if (operadoraCartaoDescricao == null) {
            operadoraCartaoDescricao = "";
        }
        return operadoraCartaoDescricao;
    }

    public void setOperadoraCartaoDescricao(String operadoraCartaoDescricao) {
        this.operadoraCartaoDescricao = operadoraCartaoDescricao;
    }

    public String getTipoAutorizacaoCobranca() {
        return tipoAutorizacaoCobranca;
    }

    public void setTipoAutorizacaoCobranca(String tipoAutorizacaoCobranca) {
        this.tipoAutorizacaoCobranca = tipoAutorizacaoCobranca;
    }

    public String getBanco() {
        if (banco == null) {
            banco = "";
        }
        return banco;
    }

    public void setBanco(String banco) {
        this.banco = banco;
    }

    public String getCodigoOperacao() {
        if (codigoOperacao == null) {
            codigoOperacao = "";
        }
        return codigoOperacao;
    }

    public void setCodigoOperacao(String codigoOperacao) {
        this.codigoOperacao = codigoOperacao;
    }

    public String getCodigoBanco() {
        if (codigoBanco == null) {
            codigoBanco = "";
        }
        return codigoBanco;
    }

    public void setCodigoBanco(String codigoBanco) {
        this.codigoBanco = codigoBanco;
    }

    public Boolean getTransacaoOnline() {
        return transacaoOnline;
    }

    public void setTransacaoOnline(Boolean transacaoOnline) {
        this.transacaoOnline = transacaoOnline;
    }
}