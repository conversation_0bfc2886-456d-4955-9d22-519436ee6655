package negocio.comuns.basico;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by luiz on 08/08/2016.
 */
public class UCPTagTO implements Serializable {

    private String tag;
    private Integer qtdConhecimentos;
    List<PerguntaUcpTO> perguntas;

    public String getTag() {
        if (tag == null) {
            tag = "";
        }
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getQtdConhecimentos() {
        if (qtdConhecimentos == null) {
            qtdConhecimentos = 0;
        }
        return qtdConhecimentos;
    }

    public void setQtdConhecimentos(Integer qtdConhecimentos) {
        this.qtdConhecimentos = qtdConhecimentos;
    }

    public List<PerguntaUcpTO> getPerguntas() {
        if(perguntas == null){
            perguntas = new ArrayList<PerguntaUcpTO>();
        }
        return perguntas;
    }

    public void setPerguntas(List<PerguntaUcpTO> perguntas) {
        this.perguntas = perguntas;
    }
}
