/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.basico.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoPessoaEnum {
    ALUNO("CL", "Aluno"),
    COLABORADOR("CO", "Colaborador"),
    AMBOS("AM", "Ambos");

    private String tipo;
    private String label;
    
    private TipoPessoaEnum(String t, String l){
        tipo = t;
        label = l;
    }

    public static TipoPessoaEnum getTipo(String tipoP){
        for(TipoPessoaEnum tipo : TipoPessoaEnum.values()){
            if(tipo.getTipo().equals(tipoP)){
                return tipo;
            }
        }
        return null;
    }
    public String getLabel() {
        return label;
    }

    public String getTipo() {
        return tipo;
    }
    
}
