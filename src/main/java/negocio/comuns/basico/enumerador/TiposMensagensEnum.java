
package negocio.comuns.basico.enumerador;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

/**
 * Domínio dos tipos de mensagens de clientes.
 * 
 */
public enum TiposMensagensEnum {
    
    NENHUM("","","",false),
    AVISO_CONSULTOR("AA","Mensagem de Aviso ao Consultor", "",false),
    CATRACA("AC","Mensagem de Acesso na Catraca", "",false),
    AVISO_MEDICO("AM","Mensagem de Aviso Médico", "",false),
    BOLETIM("BP","O bv não foi respondido. " +
            "Você está sem o perfil deste cliente.", "faces/questionarioClienteForm.jsp",false),
    DADOS_INCOMPLETOS("DI","Cadastro pendente. Falta(m) campos", "faces/clienteForm.jsp",false),
    PARCELA_ATRASO("PA","Consta parcela em atraso para este cliente. Clique aqui para receber a parcela de código X.", "tela8",true),
    RISCO("RI", "Cliente com peso Z em RISCO de sair da academia. Faça algo urgente. ","newRealizarContatoForm.jsp",true),
    OBJETIVO_CURTO("OC","Mensagem de Objetivo Curto", "",false),
    OBSERVACAO("OB","Mensagens de Observação", "",false),
    OBSERVACAO_CLIENTE("OP","Mensagens de Observação ao Cliente", "",false),
    PRODUTO_VENCIDO("PV", "Produto Z com validade vencida em xx/xx/xx. Renove com o cliente.", "gestaoMensagemClienteProdutoVencido.jsp",true),
    CARTAO_VENCIDO("CV", "Cartão de Crédito Z com validade vencida em xx/xxxx. Ligue para o cliente.", "clienteForm.jsp", false),
    ARMARIO_ALUGUEL_VENCIDO_CHAVE_NAO_DEVOLVIDA("PV", "Aluguel de armario com validade vencida em xx/xx/xx. E a chave ainda não foi devolvida.", "gestaoMensagemClienteProdutoVencido.jsp", true),
    ESTORNO("ES", "O plano XXXX de lançamento na data xx/xx/xxxx no valor de R$XXXX foi estornado automaticamente devido o não pagamento da primeira parcela que teria o vencimento na data xx/xx/xxxx. ","",true),;

    private String sigla;
    private String mensagem;
    private String navegacao; // string usada para o faces navigation ou para ser usada por um javaScript
    private boolean grave;
    
    public static Map<TiposMensagensEnum, Map<String, String>> getMapaPalavrasChave(){
        Map<TiposMensagensEnum, Map<String, String>> mapa = new EnumMap<TiposMensagensEnum, Map<String, String>>(TiposMensagensEnum.class);
        for(TiposMensagensEnum tipo : values()){
            Map<String, String> palavrasChave = new HashMap<String, String>();
            switch(tipo){
                case AVISO_CONSULTOR:
                    palavrasChave.put("Aviso ao Consultor", "<b>Aviso ao Consultor</b>");
                    break;
                case CATRACA:
                    palavrasChave.put("Acesso na Catraca", "<b>Acesso na Catraca</b>");
                    break;
                case AVISO_MEDICO:
                    palavrasChave.put("Aviso Médico", "<b>Aviso Médico</b>");
                    break;
                case BOLETIM:
                    palavrasChave.put("Você está sem o perfil deste cliente.", "<b>Você está sem o perfil deste cliente.</b>");
                    break;
                case DADOS_INCOMPLETOS:
                    palavrasChave.put("Cadastro pendente.", "<b>Cadastro pendente.</b>");
                    break;
                case PARCELA_ATRASO:
                    palavrasChave.put("Consta parcela em atraso para este cliente.", "<b style=\"color: #FF5555;\">Consta parcela em atraso para este cliente.</b>");
                    break;
                case RISCO:
                    palavrasChave.put("RISCO de sair da academia.", "<b style=\"color: #FF5555;\">RISCO de sair da academia.</b>");
                    break;
                case PRODUTO_VENCIDO:
                    palavrasChave.put("Renove com o cliente.", "<b>Renove com o cliente.</b>");
                    break;
            }
            mapa.put(tipo, palavrasChave);
        }
        return mapa;
    }
    
    private TiposMensagensEnum(String sigla, String mensagem, String navegacao, boolean grave) {
        this.sigla = sigla;
        this.grave = grave;
        this.mensagem = mensagem;
        this.navegacao = navegacao;
        this.navegacao = navegacao;
    }

    public boolean isGrave() {
        return grave;
    }

    public void setGrave(boolean grave) {
        this.grave = grave;
    }

    
    public String getSigla() {
        return this.sigla;
    }

    public String getMensagem() {
        return this.mensagem;
    }

    public String getNavegacao() {
        return this.navegacao;
    }

    public static TiposMensagensEnum getConstante(String sigla) {
        // verifica se a sigla corresponde a alguma constante
        for(TiposMensagensEnum tm : TiposMensagensEnum.values())
            if(tm.getSigla().equals(sigla))
                return tm;
        // senao retorna a constante vazia.
        return NENHUM;
    }
}
