package negocio.comuns.basico.enumerador;

public enum ServicoEnum {

    ROBO(0,"RO<PERSON>"),
    CANC<PERSON>AMENTO(1,"CA<PERSON><PERSON>AMENTO AUTOMATICO"),
    REMESSA(2,"REMES<PERSON>"),
    ENVIO_REMESSA(3,"ENVIO DAS REMESSAS"),
    NFSE(4,"<PERSON>FSE AUTOMATICO"),
    NFCE(5,"NFCE AUTOMATICO"),
    ESTACIONAMENTO(6,"ESTACIONAMENTO"),
    ESTORNO(7,"ESTORNO AUTOMATICO"),
    REN<PERSON>VACAO(8,"RENOVACA<PERSON> AUTOMATICA"),
    AN<PERSON><PERSON>DE(9,"ANUIDA<PERSON>"),
    TRANSACAO(10,"TRANSAÇÃO ONLINE"),
    <PERSON>XTRA<PERSON>(11,"EXTRATO"),
    METAS(12,"METAS CRM"),
    RO<PERSON>_PONTUACAO(13,"ROBO PONTUACAO RETROATIVA"),
    INTEGRACAO_F360(14,"INTEGRAÇÃO F360"),
    REMESSA_SERVICE_EMPRESA(15,"REMESSA_SERVICE - GERAL - EMPRESA"),
    REMESSA_SERVICE_GERAL(16,"REMESSA_SERVICE - GERAL"),
    REGUA_COBRANCA_COBRANCA_ANTECIPADA(17,"REGUA_COBRANCA_COBRANCA_ANTECIPADA"),
    REGUA_COBRANCA_COBRANCA_PENDENTE(18,"REGUA_COBRANCA_COBRANCA_PENDENTE"),
    REGUA_COBRANCA_CARTAO_VENCENDO(19,"REGUA_COBRANCA_CARTAO_VENCENDO"),
    RENOVACAO_PRODUTO(20,"RENOVACAO AUTOMATICA PRODUTOS"),
    ;

    private Integer codigo;
    private String descricao;

    private ServicoEnum(Integer codigo,String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static ServicoEnum obterPorCodigo(int codigo){
        for(ServicoEnum item : ServicoEnum.values()){
            if(item.getCodigo() == codigo)
                return item;
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
