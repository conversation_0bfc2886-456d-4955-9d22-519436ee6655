package negocio.comuns.basico.enumerador;

/**
 * Created by glauco on 27/02/2015
 */
public enum TipoCategoriaClubeEnum {

    PROPRIETARIO(1, "PP", "PROPRIETÁRIO"),
    CONTRIBUINTE_GERAL(2, "CG", "CONTRIBUINTE GERAL"),
    DEPENDENTE(3, "DP", "DEPENDENTE"),
    ASSEMELHADO(4, "AS", "ASSEMELHADO"),
    ACOMPANHANTE(5, "AC", "ACOMPANHANTE"),
    ATLETA(6, "AT", "ATLETA"),
    NAO_SOCIO(7, "NS", "NÃO SÓCIO"),
    VISITANTE(8, "VI", "VISITANTE"),
    CONVIDADO(9, "CO", "CONVIDADO"),
    ALUNO(10, "AL", "ALUNO");


    private int codigo;
    private String sigla;
    private String descricao;

    private TipoCategoriaClubeEnum(int codigo, String sigla, String descricao) {
        this.codigo = codigo;
        this.sigla = sigla;
        this.descricao = descricao;
    }

    public static TipoCategoriaClubeEnum getTipoCategoriaClube(Integer codigo) {
    	if (codigo == null)
    		return null;
        for (TipoCategoriaClubeEnum tipo : TipoCategoriaClubeEnum.values()) {
            if (tipo.getCodigo() == codigo) {
                return tipo;
            }
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
