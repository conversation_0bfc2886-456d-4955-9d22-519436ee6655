package negocio.comuns.nfe.enumerador;

/**
 * Created by glauco on 23/06/2014.
 */
public enum CRTEnum {

    NENHUM(0, "0- Nenhum"),
    MICROEMPRESA_MUNICIPAL(1, "1- Microempresa municipal"),
    ESTIMATIVA(2, "2- Estimativa"),
    SOCIEDADE_DE_PROFISSIONAIS(3, "3- Sociedade de profissionais"),
    COOPERATIVA(4, "4- Cooperativa"),
    MEI_SIMPLES_NACIONAL(5, "5- MEI - Simples Nacional"),
    ME_EPP_SIMPLES_NACIONAL(6, "6- ME EPP - Simples Nacional"),
    NENHUM_COM_ENVIO_DE_ALIQUOTA(7, "7- Nenhum - Com Envio de alíquota"),
    MEI_SIMPLES_NACIONAL_COM_ENVIO_DE_ALIQUOTA(8, "8- MEI - Simples Nacional - Com Envio de alíquota"),
    ME_EPP_SIMPLES_NACIONAL_COM_ENVIO_DE_ALIQUOTA(9, "9- ME EPP - Simples Nacional - Com Envio de alíquota");
    private Integer codigo;
    private String descricao;

    private CRTEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static CRTEnum getTipo(int consulta) {
        for (CRTEnum origem : CRTEnum.values()) {
            if (origem.getCodigo() == consulta) {
                return origem;
            }
        }
        return null;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
