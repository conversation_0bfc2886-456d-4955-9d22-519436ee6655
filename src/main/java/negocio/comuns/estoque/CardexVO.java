/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.estoque;

import java.text.SimpleDateFormat;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;

/**
 *
 * <AUTHOR>
 */
public class CardexVO extends SuperVO {

    private OperacaoCardex operacao;
    private Date data;
    private Integer totalEntrada;
    private Integer totalSaida;
    private Integer saldoAnterior;
    private Integer saldoAtual;
    private Integer codigo;
    private Date dataAux;
    private String nomeAux;
    private Integer codigoPessoa;
    private Integer vendaavulsa;

    public String getOperacao_Apresentar() {
        return getOperacao().getDescricao();
    }

    public enum OperacaoCardex {

        INCLUIDO_CONTROLE_ESTOQUE(1, "Adicionado ao Controle de Estoque"),
        REMOVIDO_CONTROLE_ESTOQUE(2, "Retirado do Controle de Estoque"),
        BALANCO(3, "Balanço"),
        BALANCO_CANCELADO(4, "Balanço Cancelado"),
        COMPRA(5, "Compra"),
        COMPRA_CANCELADA(6, "Compra Cancelada"),
        VENDA(7, "Venda"),
        VENDA_CANCELADA(8, "Venda Cancelada"),
        VENDA_CANCELADA_BALANCO(9, "Venda (Cancelada Após Balanço)");

        OperacaoCardex(int codigo, String descricao) {
            setCodigo(codigo);
            setDescricao(descricao);
        }
        private int codigo;
        private String descricao;

        public static OperacaoCardex getOperacaoCardex(int codigo) {
            for (OperacaoCardex obj : OperacaoCardex.values()) {
                if (obj.getCodigo() == codigo) {
                    return obj;
                }
            }
            return null;
        }

        public int getCodigo() {
            return codigo;
        }

        public void setCodigo(int codigo) {
            this.codigo = codigo;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }
    }

    public Date getData() {
        return data;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public OperacaoCardex getOperacao() {
        return operacao;
    }

    public void setOperacao(OperacaoCardex operacao) {
        this.operacao = operacao;
    }

    public Integer getSaldoAnterior() {
        return saldoAnterior;
    }

    public void setSaldoAnterior(Integer saldoAnterior) {
        this.saldoAnterior = saldoAnterior;
    }

    public Integer getSaldoAtual() {
        return saldoAtual;
    }

    public void setSaldoAtual(Integer saldoAtual) {
        this.saldoAtual = saldoAtual;
    }

    public Integer getTotalEntrada() {
        return totalEntrada;
    }

    public void setTotalEntrada(Integer totalEntrada) {
        this.totalEntrada = totalEntrada;
    }

    public Integer getTotalSaida() {
        return totalSaida;
    }

    public void setTotalSaida(Integer totalSaida) {
        this.totalSaida = totalSaida;
    }

    public Date getDataAux() {
        return dataAux;
    }

    public void setDataAux(Date dataAux) {
        this.dataAux = dataAux;
    }

    public String getNomeAux() {
        return nomeAux;
    }

    public void setNomeAux(String nomeAux) {
        this.nomeAux = nomeAux;
    }

    public String getDataApresentar() {
        return (new SimpleDateFormat("dd/MM/yyyy HH:mm:ss")).format(this.data);
    }

    public Integer getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(Integer codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public Integer getVendaavulsa() {
        return vendaavulsa;
    }

    public void setVendaavulsa(Integer vendaavulsa) {
        this.vendaavulsa = vendaavulsa;
    }
    
    
}
