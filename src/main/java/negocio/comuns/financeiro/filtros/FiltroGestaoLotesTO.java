package negocio.comuns.financeiro.filtros;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

public class FiltroGestaoLotesTO extends SuperTO{
    private static final long serialVersionUID = -3761376574106079271L;
    private String descricao = "";
    private Date dataInicialLancamento;
    private Date dataFinalLancamento;
    private Date dataInicialDeposito;
    private Date dataFinalDeposito;
    private int codigoEmpresa;


    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Date getDataInicialLancamento() {
        return dataInicialLancamento;
    }

    public void setDataInicialLancamento(Date dataInicialLancamento) {
        this.dataInicialLancamento = dataInicialLancamento;
    }

    public Date getDataFinalLancamento() {
        return dataFinalLancamento;
    }

    public void setDataFinalLancamento(Date dataFinalLancamento) {
        this.dataFinalLancamento = dataFinalLancamento;
    }

    public Date getDataInicialDeposito() {
        return dataInicialDeposito;
    }

    public void setDataInicialDeposito(Date dataInicialDeposito) {
        this.dataInicialDeposito = dataInicialDeposito;
    }

    public Date getDataFinalDeposito() {
        return dataFinalDeposito;
    }

    public void setDataFinalDeposito(Date dataFinalDeposito) {
        this.dataFinalDeposito = dataFinalDeposito;
    }

    public int getCodigoEmpresa() {
        return codigoEmpresa;
    }

    public void setCodigoEmpresa(int codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }
}
