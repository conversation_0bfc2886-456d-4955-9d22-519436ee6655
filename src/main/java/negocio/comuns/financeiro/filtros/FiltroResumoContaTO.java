package negocio.comuns.financeiro.filtros;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

public class FiltroResumoContaTO extends SuperTO {
    private static final long serialVersionUID = -3761376574106079271L;

    private Date dataInicioConsulta;
    private Date dataFimConsulta;

    public Date getDataInicioConsulta() {
        return dataInicioConsulta;
    }

    public void setDataInicioConsulta(Date dataInicioConsulta) {
        this.dataInicioConsulta = dataInicioConsulta;
    }

    public Date getDataFimConsulta() {
        return dataFimConsulta;
    }

    public void setDataFimConsulta(Date dataFimConsulta) {
        this.dataFimConsulta = dataFimConsulta;
    }
}
