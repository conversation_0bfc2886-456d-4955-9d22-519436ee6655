/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.ContratoVO;

/**
 *
 * <AUTHOR>
 */
public class MovParcelaFiltroVO {
 //atributo usado quando o usuario digitar nome de pessoa
    private PessoaVO pessoaVO;
    //atributo usado quando o usuario acessar o sistema e tiver uma empresa logada
    private EmpresaVO empresaVO;
    //atributo usado quando o usuario digitar um codigo de contrato
    private ContratoVO contratoVO;
     //atributo usado quando o usuario digitar um codigo de movparcela
    private MovParcelaVO movParcelaVO;

    private boolean controlarAcesso;
    private int nivelMontarDados;


    /**
     * @return O campo pessoaVO.
     */
    public PessoaVO getPessoaVO() {
        return this.pessoaVO;
    }

    /**
     * @param pessoaVO O novo valor de pessoaVO.
     */
    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    /**
     * @return O campo empresaVO.
     */
    public EmpresaVO getEmpresaVO() {
        return this.empresaVO;
    }

    /**
     * @param empresaVO O novo valor de empresaVO.
     */
    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    /**
     * @return O campo nivelMontarDados.
     */
    public int getNivelMontarDados() {
        return this.nivelMontarDados;
    }

    /**
     * @param nivelMontarDados O novo valor de nivelMontarDados.
     */
    public void setNivelMontarDados(int nivelMontarDados) {
        this.nivelMontarDados = nivelMontarDados;
    }

    /**
     * @return O campo controlarAcesso.
     */
    public boolean isControlarAcesso() {
        return this.controlarAcesso;
    }

    /**
     * @param controlarAcesso O novo valor de controlarAcesso.
     */
    public void setControlarAcesso(boolean controlarAcesso) {
        this.controlarAcesso = controlarAcesso;
    }

    public ContratoVO getContratoVO() {
        return contratoVO;
    }

    public void setContratoVO(ContratoVO contrato) {
        this.contratoVO = contrato;
    }

    /**
     * @return the movParcelaVO
     */
    public MovParcelaVO getMovParcelaVO() {
        return movParcelaVO;
    }

    /**
     * @param movParcelaVO the movParcelaVO to set
     */
    public void setMovParcelaVO(MovParcelaVO movParcelaVO) {
        this.movParcelaVO = movParcelaVO;
    }
}
