package negocio.comuns.financeiro.enumerador;

/**
 * Created by johny<PERSON> on 17/01/2017.
 */
public enum SituacaoConvenioCobranca {
    INATIVO(0, "Inativo"),
    ATIVO(1, "Ativo");

    private Integer codigo;

    private String descricao;

    private SituacaoConvenioCobranca(Integer codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public static SituacaoConvenioCobranca getPorCodigo(int situacao) {
        SituacaoConvenioCobranca situacaoEnum = null;
        for(SituacaoConvenioCobranca sit : values()){
            if(sit.getCodigo().equals(situacao)){
                situacaoEnum = sit;
                break;
            }
        }
        return situacaoEnum;
    }
}
