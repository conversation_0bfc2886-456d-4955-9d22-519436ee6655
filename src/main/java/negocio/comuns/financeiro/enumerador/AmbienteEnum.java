package negocio.comuns.financeiro.enumerador;

import negocio.comuns.utilitarias.Ordenacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 *
 * Enum utilizado no convenio de cobranca e na transação
 *
 */
public enum AmbienteEnum {

    NENHUM(0, "NENHUM"),
    PRODUCAO(1, "PRODUÇÃO"),
    HOMOLOGACAO(2, "HOMOLOGAÇÃO / SANDBOX"),
    ;

    private Integer codigo;
    private String descricao;

    private AmbienteEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static AmbienteEnum consultarPorCodigo(Integer codigo) {
        for (AmbienteEnum amb : values()) {
            if (amb.getCodigo().equals(codigo)) {
                return amb;
            }
        }
        return NENHUM;
    }

    public static List<SelectItem> obterListSelectItem() {
        List<SelectItem> lista = new ArrayList<>();
        for (AmbienteEnum amb : AmbienteEnum.values()) {
            if (!amb.equals(AmbienteEnum.NENHUM)) {
                lista.add(new SelectItem(amb, amb.getDescricao()));
            }
        }
        Ordenacao.ordenarLista(lista, "label");
        lista.add(0, new SelectItem(AmbienteEnum.NENHUM, ""));
        return lista;
    }
}
