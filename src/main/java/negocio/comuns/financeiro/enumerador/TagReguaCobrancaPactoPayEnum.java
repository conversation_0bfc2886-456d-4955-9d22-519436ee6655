package negocio.comuns.financeiro.enumerador;

public enum TagReguaCobrancaPactoPayEnum {

    CLIENTE_NOME(                           1, "nome", "#CLIENTE_NOME#", ""),
    CLIENTE_PRIMEIRO_NOME(                  2, "primeiro_nome", "#CLIENTE_PRIMEIRO_NOME#", ""),
    CLIENTE_TELEFONE(                       3, "telefone", "#CLIENTE_TELEFONE#", ""),
    CLIENTE_EMAIL(                          4, "email", "#CLIENTE_EMAIL#", ""),

    EMPRESA_RAZAO_SOCIAL(                   5, "empresa_razao_social", "#EMPRESA_RAZAO_SOCIAL#", ""),
    EMPRESA_NOME(                           6, "empresa_nome", "#EMPRESA_NOME#", ""),
    EMPRESA_CNPJ(                           7, "empresa_cnpj", "#EMPRESA_CNPJ#", ""),
    EMPRESA_ENDERECO(                       8, "empresa_endereco", "#EMPRESA_ENDERECO#", ""),
    EMPRESA_LOGO(                           9, "empresa_logo", "#EMPRESA_LOGO#", ""),
    EMPRESA_TELEFONE(                       10, "empresa_telefone", "#EMPRESA_TELEFONE#", ""),
    EMPRESA_EMAIL(                          11, "empresa_email", "#EMPRESA_EMAIL#", ""),

    TRANSACAO_VALOR(                        12, "transacao_valor", "#TRANSACAO_VALOR#", ""),
    TRANSACAO_NSU(                          13, "transacao_nsu", "#TRANSACAO_NSU#", ""),
    TRANSACAO_AUTORIZACAO(                  14, "transacao_autorizacao", "#TRANSACAO_AUTORIZACAO#", ""),
    TRANSACAO_DATA(                         15, "transacao_data", "#TRANSACAO_DATA#", ""),
    TRANSACAO_DATA_CANCELAMENTO(            16, "transacao_data_cancelamento", "#TRANSACAO_DATA_CANCELAMENTO#", ""),
    TRANSACAO_COD_RETORNO(                  17, "transacao_cod_retorno", "#TRANSACAO_COD_RETORNO#", ""),
    TRANSACAO_MOTIVO(                       18, "transacao_motivo", "#TRANSACAO_MOTIVO#", ""),
    TRANSACAO_ULTIMOS_DIGITOS(              19, "transacao_ultimos_digitos", "#TRANSACAO_ULTIMOS_DIGITOS#", ""),
    TRANSACAO_COMPROVANTE_CANCELAMENTO(     20, "comprovante_cancelamento", "#TRANSACAO_COMPROVANTE_CANCELAMENTO#", ""),
    TRANSACAO_LINK_RECIBO(                  21, "link_recibo", "#TRANSACAO_LINK_RECIBO#", ""),

    MENSAGEM(                               22, "mensagem", "#MENSAGEM#", ""),
    CARTAO_ULTIMOS_DIGITOS(                 23, "cartao_ultimos", "#CARTAO_ULTIMOS_DIGITOS#", ""),
    CARTAO_BANDEIRA(                        24, "cartao_bandeira", "#CARTAO_BANDEIRA#", ""),
    CARTAO_VALIDADE(                        25, "cartao_validade", "#CARTAO_VALIDADE#", ""),
    PARCELAS(                               26, "parcelas", "#PARCELAS#", ""),
    LINK_VENDAS_ONLINE(                     27, "link", "#LINK_VENDAS_ONLINE#", ""),

    CONFIG_EMAIL_HEAD(                      28, "email_head", "#CONFIG_EMAIL_HEAD#", ""),
    CONFIG_EMAIL_HEADER(                    29, "email_header", "#CONFIG_EMAIL_HEADER#", ""),
    CONFIG_EMAIL_FOOTER(                    30, "email_footer", "#CONFIG_EMAIL_FOOTER#", ""),
    CONFIG_EMAIL_DISPLAY_DIV(               31, "display_div", "#CONFIG_EMAIL_DISPLAY_DIV#", ""),
    CONFIG_EMAIL_DISPLAY_TELEFONE(          32, "display_telefone", "#CONFIG_EMAIL_DISPLAY_TELEFONE#", ""),
    CONFIG_EMAIL_DISPLAY_EMAIL(             33, "display_email", "#CONFIG_EMAIL_DISPLAY_EMAIL#", ""),
    CONFIG_EMAIL_DISPLAY_BTN_NEGADA(        34, "display_btn_negada", "#CONFIG_EMAIL_DISPLAY_BTN_NEGADA#", ""),
    CONFIG_EMAIL_URL_UNSUBSCRIBE(           35, "url_unsubscribe", "#CONFIG_EMAIL_URL_UNSUBSCRIBE#", ""),
    CONFIG_EMAIL_CSS_COLOR(                 36, "css_color", "#CONFIG_EMAIL_CSS_COLOR#", "#B4B7BB"),
    CONFIG_EMAIL_CSS_BTN_PAGAR_COLOR(       37, "css_btn_pagar_color", "#CONFIG_EMAIL_CSS_BTN_PAGAR_COLOR#", "#2e3133"),
    CONFIG_EMAIL_CSS_BTN_COMPROVANTE_COLOR( 38, "css_btn_comprovante_color", "#CONFIG_EMAIL_CSS_BTN_COMPROVANTE_COLOR#", "#2e3133"),
    CONFIG_EMAIL_CSS_BTN_CADASTRAR_COLOR(   39, "css_btn_cadastrar_color", "#CONFIG_EMAIL_CSS_BTN_CADASTRAR_COLOR#", "#2e3133"),
    ;

    private Integer id;
    private String nomeEnvio;
    private String tag;
    private String padrao;

    TagReguaCobrancaPactoPayEnum(Integer id, String nomeEnvio, String tag, String padrao) {
        this.id = id;
        this.nomeEnvio = nomeEnvio;
        this.tag = tag;
        this.padrao = padrao;
    }

    public Integer getId() {
        return id;
    }

    public String getNomeEnvio() {
        return nomeEnvio;
    }

    public String getTag() {
        return tag;
    }

    public static TagReguaCobrancaPactoPayEnum obterPorId(int id) {
        for (TagReguaCobrancaPactoPayEnum tipo : values()) {
            if (tipo.getId() == id) {
                return tipo;
            }
        }
        return null;
    }

    public static TagReguaCobrancaPactoPayEnum obterPorTag(String identificador) {
        for (TagReguaCobrancaPactoPayEnum tipo : values()) {
            if (tipo.getTag().equalsIgnoreCase(identificador)) {
                return tipo;
            }
        }
        return null;
    }

    public String getPadrao() {
        return padrao;
    }
}
