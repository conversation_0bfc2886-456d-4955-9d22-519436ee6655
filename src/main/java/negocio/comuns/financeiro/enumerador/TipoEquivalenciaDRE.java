/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro.enumerador;

/**
 *
 * <AUTHOR>
 */
public enum TipoEquivalenciaDRE {

    RECEITA_BRUTA(1," (+)<PERSON><PERSON><PERSON>", "555", true),
    CUSTOS_ESPECIFICOS(2," (-)Custos Específicos", "666", true),
    /*apenas para visualização no dre, não é uma equivalencia */LUCRO_BRUTO(98," = Lucro bruto", "777", false),
    DESPESAS_OPERACIONAIS(3,"(-)Despesas Operacionais", "888", true),
    /*apenas para visualização no dre, não é uma equivalencia */LUCRO_OPERACIONAL(99," = Lucro operacional", "999", false);
            
   TipoEquivalenciaDRE(int codigo, String Descricao, String codigoNode, boolean tipo){
        setCodigo(codigo);
        setDescricao(Descricao);
        setCodigoNode(codigoNode);
        setTipo(tipo);
    }

    private int codigo;
    private String Descricao;
    private String codigoNode;
    private boolean tipo;

    public static TipoEquivalenciaDRE getTipoEquivalenciaDRE(int codigo) {
        TipoEquivalenciaDRE tipo = null;
        for(TipoEquivalenciaDRE obj: TipoEquivalenciaDRE.values()){
            if (obj.getCodigo() == codigo){
                tipo = obj;
                break;
            }
        }
       return tipo;
    }

    public String getDescricao() {
        return Descricao;
    }

    public void setDescricao(String Descricao) {
        this.Descricao = Descricao;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getCodigoNode() {
        return codigoNode;
    }

    public void setCodigoNode(String codigoNode) {
        this.codigoNode = codigoNode;
    }

	public void setTipo(boolean tipo) {
		this.tipo = tipo;
	}

	public boolean getTipo() {
		return tipo;
	}
    


}
