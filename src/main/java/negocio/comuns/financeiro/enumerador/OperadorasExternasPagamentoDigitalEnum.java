/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.financeiro.enumerador;

import java.util.ArrayList;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum OperadorasExternasPagamentoDigitalEnum {
    /*
     * 
     *  VISA                1
    Mastercard          2
    American Express    37
    AURA                45
    Diners              55
    Hipercard           56
    Boleto              10
    Transferência OnLine Banco do Brasil 	58
    Transferência OnLine Banco Bradesco 	59
    Transferência OnLine Banco Itaú 	60
     */
     //Pagamento Digital (Buscape)
    OPERADORA_Nenhum(0, "(Nenhum)"),
    OPERADORA_VISA(1, "VISA"),
    OPERADORA_Mastercard(2, "Mastercard"),
    OPERADORA_Boleto(10, "Boleto"),
    OPERADORA_Amex(37, "American Express"),
    OPERADORA_AURA(45, "AURA"),
    OPERADORA_Diners(55, "Diners"),
    OPERADORA_TransfBancoBrasil(58, "Transferência OnLine Banco do Brasil"),
    OPERADORA_TransfBancoBradesco(59, "Transferência OnLine Banco Bradesco"),
    OPERADORA_TransfBancoItau(60, "Transferência OnLine Banco Itaú");    
    private Integer id;
    private String descricao;

    OperadorasExternasPagamentoDigitalEnum(final Integer id, final String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricao() {
        return descricao;
    }

    public static List getSelectListTipo() {
        List temp = new ArrayList<OperadorasExternasPagamentoDigitalEnum>();
        for (int i = 0; i < OperadorasExternasPagamentoDigitalEnum.values().length; i++) {
            OperadorasExternasPagamentoDigitalEnum obj = OperadorasExternasPagamentoDigitalEnum.values()[i];
            temp.add(new SelectItem(obj, obj.getId() + " - " + obj.getDescricao()));
        }
        return temp;
    }

    public static OperadorasExternasPagamentoDigitalEnum valueOf(final int id) {
        switch (id) {
            default:
                return OPERADORA_Nenhum;
            case 1:
                return OPERADORA_VISA;
            case 2:
                return OPERADORA_Mastercard;
            case 10:
                return OPERADORA_Boleto;
            case 37:
                return OPERADORA_Amex;
            case 45:
                return OPERADORA_AURA;
            case 55:
                return OPERADORA_Diners;
            case 58:
                return OPERADORA_TransfBancoBrasil;
            case 59:
                return OPERADORA_TransfBancoBradesco;
            case 60:
                return OPERADORA_TransfBancoItau;

        }
    }
}
