/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.financeiro;

import br.com.pactosolucoes.enumeradores.TipoConsultaVelocimetro;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class VelocimetroTO extends SuperTO{
    private Double valorFinal = 0.0;
    private Double valorInicialAmarelo = 0.0;
    private Double valorFinalAmarelo = 0.0;
    private Double valorPonteiro = 0.0;
    private int tipo;
    private List<DFSinteticoDWVO> valores = new ArrayList<DFSinteticoDWVO>();
    private int mes = 0;
    private String cor1="";
    private String cor2="";

    public Double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(Double valorFinal) {
        this.valorFinal = valorFinal;
    }

    
    public int getMes() {
        return mes;
    }

    public void setMes(int mes) {
        this.mes = mes;
    }

    public int getTipo() {
        return tipo;
    }

    public void setTipo(int tipo) {
        this.tipo = tipo;
    }

    public String getCor1() {
        return cor1;
    }

    public void setCor1(String cor1) {
        this.cor1 = cor1;
    }

    public String getCor2() {
        return cor2;
    }

    public void setCor2(String cor2) {
        this.cor2 = cor2;
    }

    public Double getValorFinalAmarelo() {
        return valorFinalAmarelo;
    }

    public void setValorFinalAmarelo(Double valorFinalAmarelo) {
        this.valorFinalAmarelo = valorFinalAmarelo;
    }

    public Double getValorInicialAmarelo() {
        return valorInicialAmarelo;
    }

    public void setValorInicialAmarelo(Double valorInicialAmarelo) {
        this.valorInicialAmarelo = valorInicialAmarelo;
    }

   
    public List<DFSinteticoDWVO> getValores() {
        return valores;
    }

    public void setValores(List<DFSinteticoDWVO> valores) {
        this.valores = valores;
    }

    public Double getValorPonteiro() {
        return valorPonteiro;
    }

    public void setValorPonteiro(Double valorPonteiro) {
        this.valorPonteiro = valorPonteiro;
    }

}


