package negocio.comuns.financeiro;

import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperTO;

public class ProdutoRateioTO extends SuperTO {

    private static final long serialVersionUID = 7818981396538877979L;
    private int codigo;
    private String nome;
    private List<RateioIntegracaoTO> rateios;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public List<RateioIntegracaoTO> getRateios() {
        if (rateios == null) {
            rateios = new ArrayList<RateioIntegracaoTO>();
        }
        return rateios;
    }

    public void setRateios(List<RateioIntegracaoTO> rateios) {
        this.rateios = rateios;
    }
}
