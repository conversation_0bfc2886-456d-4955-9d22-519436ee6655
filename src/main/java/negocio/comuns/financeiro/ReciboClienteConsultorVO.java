package negocio.comuns.financeiro;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.utilitarias.ConsistirException;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 06/02/13
 * Time: 15:03
 */
public class ReciboClienteConsultorVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo = 0;
    private ReciboPagamentoVO recibo = new ReciboPagamentoVO();
    private ColaboradorVO consultor = new ColaboradorVO();
    private ClienteVO cliente = new ClienteVO();
    private FormaPagamentoVO formaPagamento = new FormaPagamentoVO();
    private UsuarioVO responsavelPagamento;
    private double valor = 0.0;

    public ReciboClienteConsultorVO() {
    }

    public ReciboClienteConsultorVO(ReciboClienteConsultorVO old, Double valor, FormaPagamentoVO formaPagamento) {
        this.codigo = old.codigo;
        this.recibo = old.recibo;
        this.consultor = old.consultor;
        this.cliente = old.cliente;
        this.formaPagamento = formaPagamento;
        this.responsavelPagamento = old.responsavelPagamento;
        this.valor = valor;
    }

    public static void validarDados(ReciboClienteConsultorVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getRecibo() == null || obj.getRecibo().getCodigo() == 0) {
            throw new ConsistirException("O recibo deve ser informado.");
        }
        if (obj.getFormaPagamento() == null || obj.getFormaPagamento().getCodigo() == 0) {
            throw new ConsistirException("A forma de pagamento deve ser informada.");
        }
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ReciboPagamentoVO getRecibo() {
        return recibo;
    }

    public void setRecibo(ReciboPagamentoVO recibo) {
        this.recibo = recibo;
    }

    public ColaboradorVO getConsultor() {
        return consultor;
    }

    public void setConsultor(ColaboradorVO consultor) {
        this.consultor = consultor;
    }

    public ClienteVO getCliente() {
        return cliente;
    }

    public void setCliente(ClienteVO cliente) {
        this.cliente = cliente;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public FormaPagamentoVO getFormaPagamento() {
        return formaPagamento;
    }

    public UsuarioVO getResponsavelPagamento() {
        return responsavelPagamento;
    }

    public void setResponsavelPagamento(UsuarioVO responsavelPagamento) {
        this.responsavelPagamento = responsavelPagamento;
    }

    public void setFormaPagamento(FormaPagamentoVO formaPagamento) {
        this.formaPagamento = formaPagamento;
    }
}
