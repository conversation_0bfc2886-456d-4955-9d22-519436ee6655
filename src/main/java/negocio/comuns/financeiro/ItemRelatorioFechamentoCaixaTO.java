package negocio.comuns.financeiro;

import java.util.Date;

import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Uteis;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

public class ItemRelatorioFechamentoCaixaTO extends SuperTO {

    private static final long serialVersionUID = -8658902138770653119L;
    private Date dia;
    private String descricao = "";
    private String movimentacao = "";
    private String favorecido = "";
    private Double valor = 0.0;
    private TipoES tipo;

    public Date getDia() {
        return dia;
    }

    public String getDiaApresentar() {
        return Uteis.getDataAplicandoFormatacao(dia, "dd/MM | HH:mm");
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getMovimentacao() {
        return movimentacao;
    }

    public void setMovimentacao(String movimentacao) {
        this.movimentacao = movimentacao;
    }

    public String getFavorecido() {
        return favorecido;
    }

    public void setFavorecido(String favorecido) {
        this.favorecido = favorecido;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getValorApresentar() {
        return (getTipo().equals(TipoES.SAIDA) ? " - " : "") + Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public void setTipo(TipoES tipo) {
        this.tipo = tipo;
    }

    public TipoES getTipo() {
        return tipo;
    }
}
