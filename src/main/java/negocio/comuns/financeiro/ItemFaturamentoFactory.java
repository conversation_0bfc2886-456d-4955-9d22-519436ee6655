package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import org.apache.poi.ss.formula.eval.NotImplementedException;

public class ItemFaturamentoFactory {

    public static ItemFaturamentoTO novo(String tipoFormaPagamento, int numeroParcelas) throws Exception {
        if (tipoFormaPagamento == null || tipoFormaPagamento.isEmpty())
            throw new Exception("Informe a o tipo da forma de pagamento");

        if(tipoFormaPagamento.equals(TipoFormaPagto.CARTAODEBITO.getSigla()))
            return new ItemFaturamentoTODebito();

        if (tipoFormaPagamento.equals(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
            if (numeroParcelas == 1) {
                return new ItemFaturamentoTOCreditoAVista();
            } else if (numeroParcelas <= 6) {
                return new ItemFaturamentoTOCreditoAte6X();
            } else if (numeroParcelas <= 12) {
                return new ItemFaturamentoTOCreditoAte12X();
            } else {
                return new ItemFaturamentoTOCreditoAcima12X();
            }
        }

        throw new NotImplementedException("ItemFaturamento não implementado para TipoPagamento: "
                                            + tipoFormaPagamento
                                            + ", e NoParcelas: "
                                            + numeroParcelas);
    }
}
