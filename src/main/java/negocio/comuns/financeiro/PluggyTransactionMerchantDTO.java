package negocio.comuns.financeiro;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created by <PERSON> on 18/07/2023.
 */

public class PluggyTransactionMerchantDTO {

    protected String name;
    protected String businessName;
    protected String cnpj;
    protected String cnae;
    protected String category;

    public PluggyTransactionMerchantDTO() {
    }

    public PluggyTransactionMerchantDTO(JSONObject json) throws Exception {
        this.name = json.optString("name", "");
        this.businessName = json.optString("businessName", "");
        this.cnpj = json.optString("cnpj", "");
        this.cnae = json.optString("cnae", "");
        this.category = json.optString("category", "");
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBusinessName() {
        if (UteisValidacao.emptyString(businessName)) {
            return "";
        }
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public String getCnpj() {
        if (UteisValidacao.emptyString(cnpj)) {
            return "";
        }
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getCnae() {
        return cnae;
    }

    public void setCnae(String cnae) {
        this.cnae = cnae;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
