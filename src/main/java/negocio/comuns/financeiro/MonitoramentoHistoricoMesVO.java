package negocio.comuns.financeiro;

import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class MonitoramentoHistoricoMesVO extends SuperVO {

    private Integer codigo;
    private Integer monitoramento;
    private Date dia;
    private Integer ano;
    private Integer mes;
    private Integer quantidade;
    private Double total;
    private String indicador;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMonitoramento() {
        return monitoramento;
    }

    public void setMonitoramento(Integer monitoramento) {
        this.monitoramento = monitoramento;
    }

    public Date getDia() {
        return dia;
    }

    public void setDia(Date dia) {
        this.dia = dia;
    }

    public Integer getAno() {
        return ano;
    }

    public void setAno(Integer ano) {
        this.ano = ano;
    }

    public Integer getMes() {
        return mes;
    }

    public void setMes(Integer mes) {
        this.mes = mes;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public String getIndicador() {
        return indicador;
    }

    public void setIndicador(String indicador) {
        this.indicador = indicador;
    }
}
