package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import com.sun.xml.ws.api.tx.at.Transactional;
import negocio.comuns.financeiro.enumerador.SituacaoTransactionPluggyEnum;
import negocio.comuns.financeiro.enumerador.TipoTransactionPluggyEnum;
import negocio.comuns.financeiro.enumerador.TransactionPluggyCategoryEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 18/07/2023.
 */

public class PluggyTransactionDTO {

    protected String id;
    protected String description;
    protected String descriptionRaw;
    protected PluggyTransactionPaymentDataDTO paymentData;
    protected String currencyCode;
    protected double amount;
    protected Date date;
    protected double balance;
    protected String providerCode;
    protected String status;
    protected String category;
    protected String categoryId;
    protected PluggyTransactionMerchantDTO merchant;
    protected String dataApresentar = "";
    protected MovContaVO movContaVO;
    protected SituacaoTransactionPluggyEnum situacao;
    protected String corLinha;
    protected TipoTransactionPluggyEnum type;
    protected MovContaTransactionPluggyVO movContaTransactionPluggyVO;
    protected PluggyItemVO pluggyItemVO;
    protected String categoryTranslated;
    @NaoControlarLogAlteracao
    protected PluggyAccountDTO pluggyAccountDTO;
    protected List<MovContaVO> lstMovContaVO;
    protected List<MovContaTransactionPluggyVO> lstMovContaTransactionPluggyVO;

    @Transactional
    protected boolean checkboxChecked;

    public PluggyTransactionDTO() {
    }

    public PluggyTransactionDTO(JSONObject json) throws Exception {
        this.id = json.optString("id", "");
        this.description = json.optString("description", "");
        this.currencyCode = json.optString("currencyCode", "");
        this.amount = json.optDouble("amount", 0.0);
        this.balance = json.optDouble("balance", 0.0);
        this.providerCode = json.optString("providerCode", "");
        this.status = json.optString("status", "");
        this.category = json.optString("category", "");
        this.categoryId = json.optString("categoryId", "");
        if (json.optJSONObject("merchant") != null) {
            this.merchant = new PluggyTransactionMerchantDTO(json.getJSONObject("merchant"));
        }
        if (!UteisValidacao.emptyString(json.optString("date"))) {
            this.date = Uteis.getDate(json.optString("date"), "yyyy-MM-dd'T'HH:mm:ss");
            this.dataApresentar = Uteis.getData(Uteis.getDate(json.optString("date"), "yyyy-MM-dd'T'HH:mm:ss"));
        }
        this.type = TipoTransactionPluggyEnum.obterPorDescricao(json.optString("type"));
        if (json.optJSONObject("paymentData") != null) {
            this.paymentData = new PluggyTransactionPaymentDataDTO(json.optJSONObject("paymentData"));
        }
        if (!UteisValidacao.emptyString(json.optString("descriptionRaw"))) {
            this.descriptionRaw = json.optString("descriptionRaw");
        }
        if (!UteisValidacao.emptyString(json.optString("categoryId"))) {
            this.categoryTranslated = TransactionPluggyCategoryEnum.getTransactionPluggyCategoryEnum(json.optString("categoryId")).getDescriptionTranslated();
        }

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescription() {
        if (UteisValidacao.emptyString(description)) {
            return "";
        }
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public String getProviderCode() {
        return providerCode;
    }

    public void setProviderCode(String providerCode) {
        this.providerCode = providerCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public PluggyTransactionMerchantDTO getMerchant() {
        return merchant;
    }

    public void setMerchant(PluggyTransactionMerchantDTO merchant) {
        this.merchant = merchant;
    }

    public String getDataApresentar() {
        return dataApresentar;
    }

    public void setDataApresentar(String dataApresentar) {
        this.dataApresentar = dataApresentar;
    }

    public String getValorApresentar() {
        if (UteisValidacao.emptyNumber(getAmount())) {
            return "";
        }
        return Uteis.arrendondarForcando2CadasDecimaisComVirgula(getAmount(), true);
    }

    public String getValorApresentarSemSinal() {
        if (UteisValidacao.emptyNumber(getAmount())) {
            return "";
        }
        return Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(getAmount());
    }

    public double getValorApresentarSemSinalDouble() {
        if (UteisValidacao.emptyNumber(getAmount())) {
            return 0.0;
        }
        return Double.valueOf(Uteis.arrendondarForcando2CadasDecimaisComVirgula(getAmount(), false).replace(",", "."));
    }

    public MovContaVO getMovContaVO() {
        if (movContaVO == null) {
            return new MovContaVO();
        }
        return movContaVO;
    }

    public void setMovContaVO(MovContaVO movContaVO) {
        this.movContaVO = movContaVO;
    }

    public String getCorLinha() {
        if (getSituacao().equals(SituacaoTransactionPluggyEnum.NAO_CONCILIADO)) {
            return "#000000"; //preto
        } else if (getSituacao().equals(SituacaoTransactionPluggyEnum.CONCILIADO)) {
            return "#077113"; //verde
        } else if (getSituacao().equals(SituacaoTransactionPluggyEnum.GRAVAR_PENDENTE)) {
            return "#FFFF00"; //amarelo
        }
        return "#000000"; //preto
    }

    public String getTitleCorLinha() {
        if (getSituacao().equals(SituacaoTransactionPluggyEnum.NAO_CONCILIADO)) {
            return "Lançamento ainda não foi conciliado com nenhuma conta a pagar";
        } else if (getSituacao().equals(SituacaoTransactionPluggyEnum.CONCILIADO)) {
            return "Lançamento Conciliado";
        } else if (getSituacao().equals(SituacaoTransactionPluggyEnum.GRAVAR_PENDENTE)) {
            return "Conciliação deste item está pendente de gravação. Grave para alterar o status para \"Conciliado\""; //amarelo
        }
        return "";
    }

    public void setCorLinha(String corLinha) {
        this.corLinha = corLinha;
    }

    public boolean isConciliado() {
        return getSituacao().equals(SituacaoTransactionPluggyEnum.CONCILIADO);
    }

    public boolean isNaoConciliado() {
        return getSituacao().equals(SituacaoTransactionPluggyEnum.NAO_CONCILIADO);
    }

    public boolean isGravarPendente() {
        return getSituacao().equals(SituacaoTransactionPluggyEnum.GRAVAR_PENDENTE);
    }

    public SituacaoTransactionPluggyEnum getSituacao() {
        if (situacao == null) {
            return SituacaoTransactionPluggyEnum.NENHUMA;
        }
        return situacao;
    }

    public void setSituacao(SituacaoTransactionPluggyEnum situacao) {
        this.situacao = situacao;
    }

    public TipoTransactionPluggyEnum getType() {
        if (type == null) {
            return TipoTransactionPluggyEnum.NENHUMA;
        }
        return type;
    }

    public void setType(TipoTransactionPluggyEnum type) {
        this.type = type;
    }

    public String getTitleContaConciliadaApresentar() {
        if (movContaTransactionPluggyVO != null) {
            StringBuilder sb = new StringBuilder();

            //Lançamento genérico "Já recebido no ADM"
            if (movContaTransactionPluggyVO.getPluggyJaRecebidoZw() != null && !UteisValidacao.emptyNumber(movContaTransactionPluggyVO.getPluggyJaRecebidoZw().getCodigo())) {
                sb.append("Este é um lançamento genérico pois você já havia recebido ele de outra forma no ADM. </b></br>");
                sb.append("<b>Código: </b>" + getMovContaTransactionPluggyVO().getPluggyJaRecebidoZw().getCodigo() + "</br>");
                sb.append("<b>Data Vencimento: </b>" + getMovContaTransactionPluggyVO().getPluggyJaRecebidoZw().getDataVencimento_Apresentar() + "</br>");
                sb.append("<b>Data Lançamento: </b>" + getMovContaTransactionPluggyVO().getPluggyJaRecebidoZw().getDataOperacao_Apresentar() + "</br>");
                sb.append("<b>Data Conciliação: </b>" + getMovContaTransactionPluggyVO().getPluggyJaRecebidoZw().getDataOperacao_Apresentar() + "</br>");
            } else {
                sb.append("<b>Código: </b>" + getMovContaTransactionPluggyVO().getMovConta().getCodigo() + "</br>");
                sb.append("<b>Favorecido: </b>" + getMovContaTransactionPluggyVO().getMovConta().getPessoaVO().getNome() + "</br>");
                sb.append("<b>Data Vencimento: </b>" + getMovContaTransactionPluggyVO().getMovConta().getDataVencimento_Apresentar() + "</br>");
                sb.append("<b>Data Competência: </b>" + getMovContaTransactionPluggyVO().getMovConta().getDataCompetencia_Apresentar() + "</br>");
                sb.append("<b>Data Quitação: </b>" + getMovContaTransactionPluggyVO().getMovConta().getDataQuitacao_ApresentarSemHora() + "</br>");
                sb.append("<b>Data Lançamento: </b>" + getMovContaTransactionPluggyVO().getMovConta().getDataLancamento_Apresentar() + "</br>");
                sb.append("<b>Data Conciliação: </b>" + getMovContaTransactionPluggyVO().getDataQuitacaoApresentarComHHMMSS() + "</br>");
            }
            return sb.toString();
        }
        return "";
    }

    public String getTitleDescItemPluggyApresentar() {
        try {
            StringBuilder sb = new StringBuilder();
            boolean possuiInfoAdicional = false;
            if (!UteisValidacao.emptyString(getPluggyItemVO().getNameConnector())) {
                sb.append("<b>Banco: <font color=\"" + getPluggyItemVO().getPrimaryColor() + "\">" + getPluggyItemVO().getNameConnector() + "</font></b></br>");
            }

            if (getPluggyAccountDTO() != null) {
                sb.append(getPluggyAccountDTO().getTextoComplementarApresentar());
                sb.append("</br>");
                sb.append("</br>");
            }

            if (!UteisValidacao.emptyString(this.getDescriptionRaw())) {
                sb.append("<b>Descrição Detalhada: </b>" + this.getDescriptionRaw() + "</br>");
            }

            if (this.paymentData != null) {
                possuiInfoAdicional = true;

                if (!UteisValidacao.emptyString(this.getPaymentData().getPaymentMethod())) {
                    sb.append("<b>Forma de Pagamento: </b>" + this.getPaymentData().getPaymentMethod() + "</br>");
                }

                //FORNECEDOR
                sb.append("</br>" + "<b>FORNECEDOR </b>" + "</br>");
                if (this.getPaymentData().getReceiver() != null) {

                    if (!UteisValidacao.emptyString(this.getPaymentData().getReceiver().getName())) {
                        sb.append("<b>Nome: </b>" + this.getPaymentData().getReceiver().getName() + "</br>");
                    } else if (this.merchant != null && !UteisValidacao.emptyString(this.getMerchant().getBusinessName())) {
                        sb.append("<b>Nome: </b>" + this.getMerchant().getBusinessName() + "</br>");
                    }

                    if (!UteisValidacao.emptyString(this.getPaymentData().getReceiver().getBranchNumber())) {
                        sb.append("<b>Agência: </b>" + this.getPaymentData().getReceiver().getBranchNumber() + "</br>");
                    }
                    if (!UteisValidacao.emptyString(this.getPaymentData().getReceiver().getAccountNumber())) {
                        sb.append("<b>Conta: </b>" + this.getPaymentData().getReceiver().getAccountNumber() + "</br>");
                    }
                    if (this.getPaymentData().getReceiver().getDocumentNumber() != null) {
                        if (!UteisValidacao.emptyString(this.getPaymentData().getReceiver().getDocumentNumber().getType()) &&
                                !UteisValidacao.emptyString(this.getPaymentData().getReceiver().getDocumentNumber().getValue())) {
                            sb.append("<b>" + this.getPaymentData().getReceiver().getDocumentNumber().getType() + ": </b>" +
                                    this.getPaymentData().getReceiver().getDocumentNumber().getValue() + "</br>");
                        } else if (this.merchant != null && !UteisValidacao.emptyString(this.getMerchant().getCnpj())) {
                            sb.append("<b>Cnpj: </b>" + Uteis.formatarCpfCnpj(this.getMerchant().getCnpj(), false) + "</br>");
                        }
                    }
                }

                //PAGADOR
                if (this.getPaymentData().getPayer() != null) {
                    sb.append("</br>" + "<b>PAGADOR </b>" + "</br>");
                    if (!UteisValidacao.emptyString(this.getPaymentData().getPayer().getName())) {
                        sb.append("<b>Nome: </b>" + this.getPaymentData().getPayer().getName() + "</br>");
                    }
                    if (!UteisValidacao.emptyString(this.getPaymentData().getPayer().getBranchNumber())) {
                        sb.append("<b>Agência: </b>" + this.getPaymentData().getPayer().getBranchNumber() + "</br>");
                    }
                    if (!UteisValidacao.emptyString(this.getPaymentData().getPayer().getAccountNumber())) {
                        sb.append("<b>Conta: </b>" + this.getPaymentData().getPayer().getAccountNumber() + "</br>");
                    }
                    if (this.getPaymentData().getPayer().getDocumentNumber() != null) {
                        if (!UteisValidacao.emptyString(this.getPaymentData().getPayer().getDocumentNumber().getType()) &&
                                !UteisValidacao.emptyString(this.getPaymentData().getPayer().getDocumentNumber().getValue())) {
                            sb.append("<b>" + this.getPaymentData().getPayer().getDocumentNumber().getType() + ": </b>" +
                                    this.getPaymentData().getPayer().getDocumentNumber().getValue() + "</br>");
                        } else if (this.merchant != null && !UteisValidacao.emptyString(this.getMerchant().getCnpj())) {
                            sb.append("<b>Cnpj: </b>" + Uteis.formatarCpfCnpj(this.getMerchant().getCnpj(), false) + "</br>");
                        }
                    }
                }

            }

            if (!possuiInfoAdicional) {
                sb.append("</br>O banco não enviou mais informações sobre este recebimento");
            }

            //retornar descrição no hint caso não montou nada
            if (UteisValidacao.emptyString(sb.toString())) {
                return getDescription();
            }

            return sb.toString();
        } catch (Exception ex) {
        }
        return "";
    }

    public MovContaTransactionPluggyVO getMovContaTransactionPluggyVO() {
        return movContaTransactionPluggyVO;
    }

    public void setMovContaTransactionPluggyVO(MovContaTransactionPluggyVO movContaTransactionPluggyVO) {
        this.movContaTransactionPluggyVO = movContaTransactionPluggyVO;
    }

    public PluggyItemVO getPluggyItemVO() {
        return pluggyItemVO;
    }

    public void setPluggyItemVO(PluggyItemVO pluggyItemVO) {
        this.pluggyItemVO = pluggyItemVO;
    }

    public String getDescriptionRaw() {
        return descriptionRaw;
    }

    public void setDescriptionRaw(String descriptionRaw) {
        this.descriptionRaw = descriptionRaw;
    }

    public PluggyTransactionPaymentDataDTO getPaymentData() {
        return paymentData;
    }

    public void setPaymentData(PluggyTransactionPaymentDataDTO paymentData) {
        this.paymentData = paymentData;
    }

    public String getCategoryTranslated() {
        if (UteisValidacao.emptyString(categoryTranslated)) {
            return "";
        }
        return categoryTranslated;
    }

    public void setCategoryTranslated(String categoryTranslated) {
        this.categoryTranslated = categoryTranslated;
    }

    public boolean isCheckboxChecked() {
        return checkboxChecked;
    }

    public void setCheckboxChecked(boolean checkboxChecked) {
        this.checkboxChecked = checkboxChecked;
    }

    public boolean getJaRecebidoZW() {
        //conciliação genérica que o cliente informa que quer conciliar pois já recebeu através do zw adm aquele recebimento
        if (getMovContaTransactionPluggyVO() != null && getMovContaTransactionPluggyVO().getPluggyJaRecebidoZw() != null &&
                !UteisValidacao.emptyNumber(getMovContaTransactionPluggyVO().getPluggyJaRecebidoZw().getCodigo())) {
            return true;
        }
       return false;
    }

    public PluggyAccountDTO getPluggyAccountDTO() {
        return pluggyAccountDTO;
    }

    public void setPluggyAccountDTO(PluggyAccountDTO pluggyAccountDTO) {
        this.pluggyAccountDTO = pluggyAccountDTO;
    }

    public List<MovContaVO> getLstMovContaVO() {
        if(lstMovContaVO == null) {
            lstMovContaVO = new ArrayList<MovContaVO>();
        }
        return lstMovContaVO;
    }

    public void setLstMovContaVO(List<MovContaVO> lstMovContaVO) {
        this.lstMovContaVO = lstMovContaVO;
    }

    public boolean getMultiplasContasPreenchidas() {
        if(lstMovContaVO != null && !lstMovContaVO.isEmpty()) {
            return true;
        }else{
            return false;
        }
    }

    public List<MovContaTransactionPluggyVO> getLstMovContaTransactionPluggyVO() {
        if(lstMovContaTransactionPluggyVO == null) {
            lstMovContaTransactionPluggyVO = new ArrayList<MovContaTransactionPluggyVO>();
        }
        return lstMovContaTransactionPluggyVO;
    }

    public void setLstMovContaTransactionPluggyVO(List<MovContaTransactionPluggyVO> lstMovContaTransactionPluggyVO) {
        this.lstMovContaTransactionPluggyVO = lstMovContaTransactionPluggyVO;
    }

    public String getValorMultiplasContasApresentar() {
        if(!UteisValidacao.emptyList(lstMovContaVO)) {
            double vlrTotalContas = 0d;
            for(MovContaVO movContaVO : lstMovContaVO) {
                vlrTotalContas = new BigDecimal(vlrTotalContas + movContaVO.getValor()).setScale(2, RoundingMode.HALF_UP).doubleValue();
            }

            return Uteis.formataDuasCasasDecimaisEPontoCasasMilenio(Uteis.arredondarForcando2CasasDecimais(vlrTotalContas));

        }else{
            return "";
        }
    }
}
