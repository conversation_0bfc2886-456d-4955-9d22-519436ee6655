package negocio.comuns.financeiro;

import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.Cheque;
import negocio.facade.jdbc.financeiro.MovPagamento;

import java.lang.reflect.Field;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class ChequeTO extends SuperTO {
    private int codigo = 0;
    private String matricula = "";
    private String nomePagador = "";
    private String nomeAlunosDaParcela = "";
    private String nomeNoCheque = "";
    private String numero = "";
    private String agencia = "";
    private String numeroBanco = "";
    private String codigosComposicao = "";
    private String conta = "";
    private int numeroLote = 0;
    private double valor = 0.0;
    @JsonIgnore
    private Date dataCompensacao = null;
    @JsonIgnore
    private Date dataLancamento = null;
    @JsonIgnore
    private Date dataOriginal = null;
    @JsonIgnore
    private Date dataFim = null;
    private boolean chequeEscolhido = false;
    private int recibo = 0;
    private String contaContido = "";
    private int pagaMovConta = 0;
    private int codigoContaContido = 0;
    private int movConta = 0;
    private int loteAvulso = 0;
    private boolean removido = false;
    private int codigoPessoa = 0;
    private boolean ativo = true;
    private boolean devolvido = false;
    private String cpfPagador = "";
    @JsonIgnore
    private MovPagamentoVO movPagamentoVO;
    private EmpresaVO empresa;
    private String codigosParcelas = "";
    private String vencimentosParcelas = "";
    private String numerosParcelas = "";
    private UsuarioVO usuarioVO;
    @NaoControlarLogAlteracao
    private ContratoVO contratoVO;
    @NaoControlarLogAlteracao
    private String nomeResponsavelRecibo;

    public ChequeTO() {

    }

    public ChequeTO(Integer codigo, String codigoComposicao, Date dataCompensacao, Date dataLancamento, Double valor,
                    String agencia, String conta, String numero, String numeroBanco, String nomePagador) {
        this.codigo = codigo;
        this.codigosComposicao = codigoComposicao;
        this.dataCompensacao = dataCompensacao;
        this.dataLancamento = dataLancamento;
        this.valor = valor;
        this.agencia = agencia;
        this.conta = conta;
        this.numero = numero;
        this.numeroBanco = numeroBanco;
        this.nomePagador = nomePagador;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getNomePagador() {
        if(nomePagador == null){
            nomePagador = "";
        }
        return Uteis.getNomeAbreviado(nomePagador.toLowerCase());
    }

    public String getNomePagadorUpper() {
        if(nomePagador == null){
            nomePagador = "";
        }
        return nomePagador.toUpperCase();
    }

    public void setNomePagador(String nomePagador) {
        this.nomePagador = nomePagador;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getNumeroBanco() {
        return numeroBanco;
    }

    public void setNumeroBanco(String numeroBanco) {
        this.numeroBanco = numeroBanco;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public int getNumeroLote() {
        return numeroLote;
    }

    public void setNumeroLote(int numeroLote) {
        this.numeroLote = numeroLote;
    }

    public boolean isApresentarNumeroLote() {
        return numeroLote > 0;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double valor) {
        this.valor = valor;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public String getDataCompensacaoApresentar() {
        return Uteis.getData(dataCompensacao);
    }

    public String getDataCompensacaoYYYYMMDD() {
        return Uteis.getDataAplicandoFormatacao(dataCompensacao, "yyyyMMdd");
    }

    public String getDataCompensacaoOrdenacao() {
        return Uteis.getDataAplicandoFormatacao(dataCompensacao, "yyyyMMddHHmm");
    }


    public String getDataLancamentoApresentar() {
        return Uteis.getData(dataLancamento);
    }

    public String getDataLancamentoYYYYMMDD() {
        return Uteis.getDataAplicandoFormatacao(dataLancamento, "yyyyMMdd");
    }

    public String getDataLancamentoYYYYMMDDHH24MI() {
        return Uteis.getDataAplicandoFormatacao(dataLancamento, "yyyyMMddHHmm");
    }

    public String getDataOriginalApresentar() {
        if (dataOriginal == null) {
            return Uteis.getData(dataCompensacao);
        } else {
            return Uteis.getData(dataOriginal);
        }

    }

    public String getDataOriginalYYYYMMDD() {
        if (dataOriginal == null) {
            return Uteis.getDataAplicandoFormatacao(dataCompensacao, "yyyyMMdd");
        } else {
            return Uteis.getDataAplicandoFormatacao(dataOriginal, "yyyyMMdd");
        }

    }

    public String getDataOriginalOrdenacao() {
        String data = null;
        if (dataOriginal == null) {
            data = Uteis.getDataAplicandoFormatacao(dataCompensacao, "yyyyMMddHHmm");
        } else {
            data = Uteis.getDataAplicandoFormatacao(dataOriginal, "yyyyMMddHHmm");
        }
        return data;

    }

    public Date getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(Date dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public boolean isChequeEscolhido() {
        return chequeEscolhido;
    }

    public void setChequeEscolhido(boolean chequeEscolhido) {
        this.chequeEscolhido = chequeEscolhido;
    }

    public int getRecibo() {
        return recibo;
    }

    public void setRecibo(int recibo) {
        this.recibo = recibo;
    }

    public Date getDataOriginal() {
        return dataOriginal;
    }

    public void setDataOriginal(Date dataOriginal) {
        this.dataOriginal = dataOriginal;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ChequeTO) {
            ChequeTO aux = (ChequeTO) obj;
            return this.codigo == aux.getCodigo();
        }
        return false;
    }

    public String getContaContido() {
        return contaContido == null ? "" : contaContido.toLowerCase();
    }

    public void setContaContido(String contaContido) {
        this.contaContido = contaContido;
    }

    public int getPagaMovConta() {
        return pagaMovConta;
    }

    public void setPagaMovConta(int pagaMovConta) {
        this.pagaMovConta = pagaMovConta;
    }

    public int getCodigoContaContido() {
        return codigoContaContido;
    }

    public void setCodigoContaContido(int codigoContaContido) {
        this.codigoContaContido = codigoContaContido;
    }

    public int getMovConta() {
        return movConta;
    }

    public void setMovConta(int movConta) {
        this.movConta = movConta;
    }

    public String getValorApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(valor);
    }

    public String getNumeroLoteApresentar() {
        return numeroLote == 0 ? "" : String.valueOf(numeroLote);
    }

    public String getNomeNoCheque() {
        if(nomeNoCheque == null){
           nomeNoCheque = "";
        }
        return nomeNoCheque.toLowerCase();
    }

    public String getNomeNoChequeUpper() {
        if(nomeNoCheque == null){
            nomeNoCheque = "";
        }
        return nomeNoCheque.toUpperCase();
    }

    public void setNomeNoCheque(String nomeNoCheque) {
        this.nomeNoCheque = nomeNoCheque;
    }

    public String getCodigosComposicao() {
        return codigosComposicao;
    }

    public void setCodigosComposicao(String codigosComposicao) {
        this.codigosComposicao = codigosComposicao;
    }

    public int getLoteAvulso() {
        return loteAvulso;
    }

    public void setLoteAvulso(int loteAvulso) {
        this.loteAvulso = loteAvulso;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getPrimeiroNomePagador() {
        return nomePagador;
    }

    public boolean isRemovido() {
        return removido;
    }

    public boolean getRemovido() {
        return removido;
    }

    public void setRemovido(boolean removido) {
        this.removido = removido;
    }

    public String getColor() {
        return removido ? "gray" : "blue";
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }

    public int getCodigoPessoa() {
        return codigoPessoa;
    }

    public void setCodigoPessoa(int codigoPessoa) {
        this.codigoPessoa = codigoPessoa;
    }

    public boolean getFornecedor() {
        return UteisValidacao.emptyString(matricula);
    }

    public RecebivelTO getRecebivelTO() throws Exception {
        RecebivelTO recebivelTO = new RecebivelTO();
        recebivelTO.setCodigo(this.getCodigo());
        recebivelTO.setCodigoUnico("CH-"+this.getCodigo());
        recebivelTO.setMatricula(this.getMatricula());
        recebivelTO.setNomePagador(this.getNomePagadorUpper());
        recebivelTO.setCpfPagador(this.getCpfPagador());
        recebivelTO.setNomeNoCheque(this.getNomeNoChequeUpper());
        recebivelTO.setNomePessoa(this.getNomePagadorUpper());
        recebivelTO.setNumero(this.getNumero());
        recebivelTO.setAgencia(this.getAgencia());
        recebivelTO.setNumeroBanco(this.getNumeroBanco());
        recebivelTO.setCodigosComposicao(this.getCodigosComposicao());
        recebivelTO.setConta(this.getConta());
        recebivelTO.setNumeroLote(this.getNumeroLote() == 0 ? null : this.getNumeroLote());
        recebivelTO.setValor(Uteis.arredondarForcando2CasasDecimais(this.getValor()));
        recebivelTO.setDataCompensacao(this.getDataCompensacao());
        recebivelTO.setDataLancamento(this.getDataLancamento());
        recebivelTO.setDataOriginal(this.getDataOriginal());
        recebivelTO.setDataFim(this.getDataFim());
        recebivelTO.setPagamentoMovParcelaVOs(this.getMovPagamentoVO().getPagamentoMovParcelaVOs());
        //chequeEscolhido?
        recebivelTO.setRecibo(this.getRecibo());
        recebivelTO.setContaContido(this.getContaContido());
        recebivelTO.setPagaMovConta(this.getPagaMovConta() == 0 ? null : this.getPagaMovConta());
        recebivelTO.setCodigoContaContido(this.getCodigoContaContido() == 0 ? null : this.getCodigoContaContido());
        recebivelTO.setMovConta(this.getMovConta() == 0 ? null : this.getMovConta());
        recebivelTO.setLoteAvulso(this.getLoteAvulso() == 0 ? null : this.getLoteAvulso());
        //removido?
        recebivelTO.setCodigoPessoa(this.getCodigoPessoa());

        Cheque cheque = new Cheque();
        MovPagamento movPagamento = new MovPagamento();
        ChequeVO chequeVO = cheque.consultarPorChavePrimaria(getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        boolean emContaCorrenteAluno = false;
        if (!UteisValidacao.emptyString(chequeVO.getComposicao())) {
            String produtosPagosComposicao = chequeVO.getProdutosPagos();
            String[] chequesComposicao = chequeVO.getComposicao().split(",");
            for (String cheq : chequesComposicao) {
                cheq = cheque.obterProdutosPagos(Integer.parseInt(cheq));
                if (!UteisValidacao.emptyString(cheq)) {
                    produtosPagosComposicao += cheq;
                } else {
                    emContaCorrenteAluno = true;
                }

            }
            chequeVO.setProdutosPagos(produtosPagosComposicao);
        }
        if (chequeVO.getMovConta().getCodigo() == 0) {
            if (UteisValidacao.emptyString(chequeVO.getProdutosPagos())) {
                recebivelTO.setProdutos("CONTA CORRENTE");
                recebivelTO.setPlanoContrato("");
                recebivelTO.setModalidade("");
            } else {
                String produtosPlanos = movPagamento.consultarDescricaoProdutosPagosPlano(chequeVO.getProdutosPagos());
                String[] valores = produtosPlanos.split("\\?");
                recebivelTO.setProdutos(valores.length > 0 ? valores[0] + (emContaCorrenteAluno ? ",CONTA CORRENTE" : "") : "");
                recebivelTO.setPlanoContrato(valores.length > 1 ? valores[1] : "");
                recebivelTO.setModalidade(valores.length > 2 ? valores[2] : "");
            }
        }

        recebivelTO.setEmpresa(this.getEmpresa());
        recebivelTO.setVencimentosParcelas(this.getVencimentosParcelas());
        recebivelTO.setNumerosParcelas(this.getNumerosParcelas());
        recebivelTO.setCodigosParcelas(this.getCodigosParcelas());
        recebivelTO.setUsuarioVO(this.getUsuarioVO());
        recebivelTO.setFormaPagamento(this.getMovPagamentoVO().getFormaPagamento());
        return recebivelTO;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isDevolvido() {
        return devolvido;
    }

    public void setDevolvido(boolean devolvido) {
        this.devolvido = devolvido;
    }

    public JSONObject toJSON() {
        JSONObject o = new JSONObject();
        Field[] fields = this.getClass().getDeclaredFields();
        try {
            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                o.put(field.getName(), field.get(this));
            }
        } catch (Exception e) {
        }
        return o;
    }

    public String getCpfPagador() {
        return cpfPagador;
    }

    public void setCpfPagador(String cpfPagador) {
        this.cpfPagador = cpfPagador;
    }
    
    public String getObterTodosChequesComposicao() {
        String codigos = ""+this.codigo;
        if (this.codigosComposicao != null && !this.codigosComposicao.equals("")) {
            codigos += "," + this.codigosComposicao;
        }
        return codigos;
    }

    public String getAgenciaContaRelatorio(){
        return Uteis.adicionarValorEsquerda("0", this.agencia, 4) + "/" + Uteis.adicionarValorEsquerda("0", this.conta, 8);
    }

    public String getNumeroLoteRelatorio(){
        String numeroLote = "0";
        if(this.isApresentarNumeroLote()){
            numeroLote = getNumeroLoteApresentar();
        }else{
            if(this.loteAvulso > 0){
                numeroLote = Integer.valueOf(getLoteAvulso()).toString();
            }
        }
        return numeroLote;
    }

    public Long getMatriculaRelatorio() {
        return UteisValidacao.emptyString(matricula) ? 0L : Long.valueOf(matricula);
    }

    public MovPagamentoVO getMovPagamentoVO() {
        return movPagamentoVO;
    }

    public void setMovPagamentoVO(MovPagamentoVO movPagamentoVO) {
        this.movPagamentoVO = movPagamentoVO;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public EmpresaVO getEmpresa() {
        if(empresa == null){
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setCodigosParcelas(String codigosParcelas) {
        this.codigosParcelas = codigosParcelas;
    }

    public String getCodigosParcelas() {
        return codigosParcelas;
    }

    public void setVencimentosParcelas(String vencimentosParcelas) {
        this.vencimentosParcelas = vencimentosParcelas;
    }

    public String getVencimentosParcelas() {
        return vencimentosParcelas;
    }

    public void setNumerosParcelas(String numerosParcelas) {
        this.numerosParcelas = numerosParcelas;
    }

    public String getNumerosParcelas() {
        return numerosParcelas;
    }

    public boolean getConsumidor(){
        return UteisValidacao.emptyString(matricula);
    }

    public UsuarioVO getUsuarioVO() {
        if (usuarioVO == null) {
            usuarioVO = new UsuarioVO();
        }
        return usuarioVO;
    }

    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public String getUsuarioResponsavelApresentar() {
        return (!UteisValidacao.emptyString(this.getNomeResponsavelRecibo())) ? this.getNomeResponsavelRecibo() : "";
    }

    public Integer getReciboPagamentoApresentar() {
        return (!UteisValidacao.emptyNumber(this.getRecibo())) ? this.getRecibo() : null;
    }

    public Integer getContratoReciboApresentar() {
        return (this.getContratoVO() != null) ? this.getContratoVO().getCodigo() : null;
    }

    public ContratoVO getContratoVO() {
        if (contratoVO == null) {
            return new ContratoVO();
        }
        return contratoVO;
    }
    public void setContratoVO(ContratoVO contratoVO) {
        this.contratoVO = contratoVO;
    }

    public String getNomeResponsavelRecibo() {
        if (UteisValidacao.emptyString(nomeResponsavelRecibo)) {
            return "";
        }
        return nomeResponsavelRecibo;
    }

    public void setNomeResponsavelRecibo(String nomeResponsavelRecibo) {
        this.nomeResponsavelRecibo = nomeResponsavelRecibo;
    }

    public String getNomeAlunosDaParcela() {
        return nomeAlunosDaParcela;
    }

    public void setNomeAlunosDaParcela(String nomeAlunosDaParcela) {
        this.nomeAlunosDaParcela = nomeAlunosDaParcela;
    }

}
