package negocio.comuns.financeiro;

import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.getnet.GetnetOnlineRetornoEnum;

/*
 * Created by <PERSON><PERSON> on 28/09/2018.
 */
public class TransacaoGetNetVO extends TransacaoVO {

    public String getValorCodigoExterno() throws Exception {
        return getCodigoExterno();
    }

    public String getValorCartaoMascarado() throws Exception {
        return getCartaoMascarado();
    }

    public String getValorUltimaTransacaoAprovada() throws Exception {
        return getValorAtributoResposta(APF.Transacao);
    }

    public String getResultadoRequisicao() {
        try {
            if (getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) && UteisValidacao.emptyString(getParamsResposta()) &&
                    !UteisValidacao.emptyString(getCodigoRetornoDescricao()) && getCodigoRetornoDescricao().contains("401") &&
                    getCodigoRetornoDescricao().contains("Unauthorized") && getCodigoRetornoDescricao().contains("GENERIC-401")) {
                return "A Getnet não nos retornou o motivo do erro. Credenciais inválidas é a causa mais provável";
            }
            JSONObject json = new JSONObject(getParamsResposta());
            String retorno = "";
            try {
                JSONArray jsonArray = json.getJSONArray("details");
                JSONObject detalhes = jsonArray.getJSONObject(0);
                String error_code = detalhes.optString("error_code");
                String description_detail = detalhes.optString("description_detail");
                if (!UteisValidacao.emptyString(error_code)) {
                    GetnetOnlineRetornoEnum getnetOnlineRetornoEnum = GetnetOnlineRetornoEnum.valueOff(error_code);
                    if (getnetOnlineRetornoEnum != null && !getnetOnlineRetornoEnum.equals(GetnetOnlineRetornoEnum.NENHUM)) {
                        retorno = getnetOnlineRetornoEnum.getDescricao();
                    }
                }

                if (detalhes.has("antifraud")) {
                    JSONObject antifraud = detalhes.optJSONObject("antifraud");

                    if (!UteisValidacao.emptyString(antifraud.optString("description"))) {
                        retorno = antifraud.optString("description");
                    }

                    if (!UteisValidacao.emptyString(antifraud.optString("status_code"))) {
                        StatusAntifraudeGetNetEnum statusEnum = StatusAntifraudeGetNetEnum.valueOff(antifraud.optString("status_code"));
                        if (!statusEnum.equals(StatusAntifraudeGetNetEnum.NENHUM)) {
                            retorno += ((UteisValidacao.emptyString(retorno) ? "" : "<br/><br/>") + statusEnum.getDescricao());
                        }
                    }
                } else if (UteisValidacao.emptyString(retorno)) {
                    String description = detalhes.getString("description");
                    if (description.contains("TransaÃ§Ã£o")) {
                        description = "Transação não aprovada";
                    }
                    retorno = description;
                }

                if (!UteisValidacao.emptyString(description_detail)) {
                    if (!UteisValidacao.emptyString(retorno)) {
                        retorno += ("<br/>" + description_detail);
                    } else {
                        retorno += description_detail;
                    }
                }
            } catch (Exception ignored) {
            }

            if (UteisValidacao.emptyString(retorno)) {
                retorno = json.optString("message");
            }

            if (UteisValidacao.emptyString(retorno)) {
                try {
                    if (json.optJSONObject("credit").optString("reason_message").equalsIgnoreCase("transaction approved")) {
                        retorno = "Transação Aprovada";
                    }
                } catch (Exception ignored) {
                }
            }

            if (retorno.contains("billTo_city")) {
                retorno = retorno.replace("billTo_city", "Cidade");
            }
            return retorno;
        } catch (Exception ex) {
            return "";
        }
    }

    public String getAutorizacao() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            JSONObject credit = obj.getJSONObject("credit");
            return credit.getString("authorization_code");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getPaymentId() {
        try {
            JSONObject json = new JSONObject(this.getParamsResposta());
            return json.getString("payment_id");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getBandeira() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String band = obj.optString("cartaoBandeira");
            if (UteisValidacao.emptyString(band)) {
                band = obj.optString("band");
            }
            return band.toUpperCase();
        } catch (Exception ex) {
            return "";
        }
    }

    public String getCartaoMascarado() {
        try {
            JSONObject obj = new JSONObject(getOutrasInformacoes());
            String card = obj.optString("cartaoMascarado");
            if (UteisValidacao.emptyString(card)) {
                card = obj.optString("card");
            }
            return APF.getCartaoMascarado(card);
        } catch (Exception e) {
            return "";
        }
    }

    public String getValorAtributoResposta(String nomeAtributo) throws Exception {
        String valor = "";
        if (nomeAtributo.equals(APF.Transacao)) {
            return getPaymentId();
        }
        return valor;
    }

    public String getValorAtributoCancelamento(String nomeAtributo) throws Exception {
        String valor = "";
        if (!UteisValidacao.emptyString(getResultadoCancelamento())) {
            try {
                if (APF.ResultSolicCancel.equalsIgnoreCase(nomeAtributo)) {
                    try {
                        JSONObject jsonCancel = new JSONObject(getResultadoCancelamento());
                        valor = jsonCancel.optJSONObject("last_transaction").optString("gateway_message");
                        if (!UteisValidacao.emptyString(valor)) {
                            return valor;
                        }
                    } catch (Exception ignored) {
                    }

                    try {
                        JSONObject jsonCancel = new JSONObject(getResultadoCancelamento());
                        valor = jsonCancel.optString("message");
                        JSONArray lista = jsonCancel.optJSONArray("details");
                        for (int e = 0; e < lista.length(); e++) {
                            JSONObject obj = lista.getJSONObject(e);
                            valor += " - " + obj.optString("description_detail");
                        }
                        if (!UteisValidacao.emptyString(valor)) {
                            return valor;
                        }
                    } catch (Exception ignored) {
                    }
                }
            } catch (Exception ignored) {
            }
        }
        return valor;
    }

    public String getCodErroExterno() {
        try {
            JSONObject json = new JSONObject(getParamsResposta());
            JSONArray jsonArray = json.getJSONArray("details");
            if (jsonArray.length() > 0) {
                JSONObject detalhes = jsonArray.getJSONObject(0);
                if (detalhes.optJSONObject("antifraud") != null) {
                    JSONObject antifraud = detalhes.optJSONObject("antifraud");
                    return antifraud.getString("status_code");
                } else {
                    if(json.optString("name").equals("UnexpectedEOFAtTarget")) {
                        return "";
                    }else{
                        return detalhes.getString("error_code").replace("PAYMENTS-", "");
                    }
                }
            } else {
                return json.get("status_code").toString();
            }
        } catch (Exception ignored) {
            return "";
        }
    }

    public String getNSU() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            JSONObject credit = obj.getJSONObject("credit");
            return credit.optString("terminal_nsu");
        } catch (Exception ex) {
            return "";
        }
    }

    public String getTID() {
        try {
            JSONObject obj = new JSONObject(getParamsResposta());
            JSONObject credit = obj.getJSONObject("credit");
            return credit.optString("acquirer_transaction_id");
        } catch (Exception ex) {
            return "";
        }
    }

    public Integer getNrVezes() {
        try {
            JSONObject obj = new JSONObject(getParamsEnvio());
            JSONObject credit = obj.getJSONObject("credit");
            return credit.getInt("number_installments");
        } catch (Exception ex) {
            return 0;
        }
    }
}
