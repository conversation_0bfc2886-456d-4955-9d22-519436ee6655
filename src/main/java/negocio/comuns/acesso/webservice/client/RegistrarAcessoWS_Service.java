
package negocio.comuns.acesso.webservice.client;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebServiceClient(name = "RegistrarAcessoWS", targetNamespace = "http://webservice.acesso/", wsdlLocation = "http://localhost:8087/ZillyonWeb-Merged/RegistrarAcessoWS?wsdl")
public class RegistrarAcessoWS_Service
    extends Service
{

    private final static URL REGISTRARACESSOWS_WSDL_LOCATION;
    private final static WebServiceException REGISTRARACESSOWS_EXCEPTION;
    private final static QName REGISTRARACESSOWS_QNAME = new QName("http://webservice.acesso/", "RegistrarAcessoWS");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://localhost:8087/ZillyonWeb-Merged/RegistrarAcessoWS?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        REGISTRARACESSOWS_WSDL_LOCATION = url;
        REGISTRARACESSOWS_EXCEPTION = e;
    }

    public RegistrarAcessoWS_Service() {
        super(__getWsdlLocation(), REGISTRARACESSOWS_QNAME);
    }

    public RegistrarAcessoWS_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    /**
     * 
     * @return
     *     returns RegistrarAcessoWS
     */
    @WebEndpoint(name = "RegistrarAcessoWSPort")
    public RegistrarAcessoWS getRegistrarAcessoWSPort() {
        return super.getPort(new QName("http://webservice.acesso/", "RegistrarAcessoWSPort"), RegistrarAcessoWS.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns RegistrarAcessoWS
     */
    @WebEndpoint(name = "RegistrarAcessoWSPort")
    public RegistrarAcessoWS getRegistrarAcessoWSPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://webservice.acesso/", "RegistrarAcessoWSPort"), RegistrarAcessoWS.class, features);
    }

    private static URL __getWsdlLocation() {
        if (REGISTRARACESSOWS_EXCEPTION!= null) {
            throw REGISTRARACESSOWS_EXCEPTION;
        }
        return REGISTRARACESSOWS_WSDL_LOCATION;
    }

}
