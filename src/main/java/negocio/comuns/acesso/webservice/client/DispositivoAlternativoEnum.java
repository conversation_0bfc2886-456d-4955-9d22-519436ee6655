
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for dispositivoAlternativoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="dispositivoAlternativoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="NENHUM_DISP"/>
 *     &lt;enumeration value="CODIGO_BARRAS"/>
 *     &lt;enumeration value="APROXIMACAO"/>
 *     &lt;enumeration value="MIFARE"/>
 *     &lt;enumeration value="APROXIMACAO_E_CODIGO_BARRAS"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "dispositivoAlternativoEnum")
@XmlEnum
public enum DispositivoAlternativoEnum {

    NENHUM_DISP,
    CODIGO_BARRAS,
    APROXIMACAO,
    MIFARE,
    APROXIMACAO_E_CODIGO_BARRAS;

    public String value() {
        return name();
    }

    public static DispositivoAlternativoEnum fromValue(String v) {
        return valueOf(v);
    }

}
