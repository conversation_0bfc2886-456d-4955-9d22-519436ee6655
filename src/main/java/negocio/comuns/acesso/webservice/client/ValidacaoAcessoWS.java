
package negocio.comuns.acesso.webservice.client;

import java.util.List;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "ValidacaoAcessoWS", targetNamespace = "http://webservice.acesso/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface ValidacaoAcessoWS {


    /**
     * 
     * @param forcarLib
     * @param terminal
     * @param codigoCartao
     * @param localAcesso
     * @param sentido
     * @param empresa
     * @param meioIdentificacao
     * @param key
     * @param acessoOutraEmpresa
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarAcessoPeloCodigoAcessoAvaliandoIntegracao", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoPeloCodigoAcessoAvaliandoIntegracao")
    @ResponseWrapper(localName = "validarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoPeloCodigoAcessoAvaliandoIntegracaoResponse")
    public RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcessoAvaliandoIntegracao(
        @WebParam(name = "codigoCartao", targetNamespace = "")
        String codigoCartao,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "forcarLib", targetNamespace = "")
        Boolean forcarLib,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "sentido", targetNamespace = "")
        DirecaoAcessoEnum sentido,
        @WebParam(name = "terminal", targetNamespace = "")
        String terminal,
        @WebParam(name = "acessoOutraEmpresa", targetNamespace = "")
        Boolean acessoOutraEmpresa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param parametros
     * @param localAcesso
     * @param versao
     * @param key
     */
    @WebMethod
    @RequestWrapper(localName = "registrarVersaoAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarVersaoAcesso")
    @ResponseWrapper(localName = "registrarVersaoAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarVersaoAcessoResponse")
    public void registrarVersaoAcesso(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "versao", targetNamespace = "")
        String versao,
        @WebParam(name = "parametros", targetNamespace = "")
        String parametros);

    /**
     * 
     * @param terminal
     * @param tipo
     * @param empresa
     * @param codigoAcesso
     * @param local
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoBuscarCodigoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarPorCodigoAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarPorCodigoAcesso")
    @ResponseWrapper(localName = "buscarPorCodigoAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarPorCodigoAcessoResponse")
    public RetornoRequisicaoBuscarCodigoAcesso buscarPorCodigoAcesso(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "local", targetNamespace = "")
        Integer local,
        @WebParam(name = "codigoAcesso", targetNamespace = "")
        String codigoAcesso,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo)
        throws Exception_Exception
    ;

    /**
     * 
     * @param tipoLiberacao
     * @param terminal
     * @param usuario
     * @param empresa
     * @param dataAcesso
     * @param direcao
     * @param local
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoLiberacaoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "registrarLiberacaoAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarLiberacaoAcesso")
    @ResponseWrapper(localName = "registrarLiberacaoAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarLiberacaoAcessoResponse")
    public RetornoRequisicaoLiberacaoAcesso registrarLiberacaoAcesso(
        @WebParam(name = "dataAcesso", targetNamespace = "")
        XMLGregorianCalendar dataAcesso,
        @WebParam(name = "direcao", targetNamespace = "")
        DirecaoAcessoEnum direcao,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "local", targetNamespace = "")
        Integer local,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "tipoLiberacao", targetNamespace = "")
        TipoLiberacaoEnum tipoLiberacao,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param key
     */
    @WebMethod
    @RequestWrapper(localName = "registrarDownloadBaseOffline", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarDownloadBaseOffline")
    @ResponseWrapper(localName = "registrarDownloadBaseOfflineResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarDownloadBaseOfflineResponse")
    public void registrarDownloadBaseOffline(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso);

    /**
     * 
     * @param terminal
     * @param usuario
     * @param empresa
     * @param senha
     * @param recurso
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidarPermissaoUsuario
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarPemissaoUsuario", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarPemissaoUsuario")
    @ResponseWrapper(localName = "validarPemissaoUsuarioResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarPemissaoUsuarioResponse")
    public RetornoRequisicaoValidarPermissaoUsuario validarPemissaoUsuario(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "recurso", targetNamespace = "")
        String recurso,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario);

    /**
     * 
     * @param localAcesso
     * @param codigoPessoa
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoDadosOffline
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "montarDadosOfflinePessoa", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.MontarDadosOfflinePessoa")
    @ResponseWrapper(localName = "montarDadosOfflinePessoaResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.MontarDadosOfflinePessoaResponse")
    public RetornoRequisicaoDadosOffline montarDadosOfflinePessoa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "codigoPessoa", targetNamespace = "")
        Integer codigoPessoa);

    /**
     * 
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "getVersaoSistema", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GetVersaoSistema")
    @ResponseWrapper(localName = "getVersaoSistemaResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GetVersaoSistemaResponse")
    public String getVersaoSistema();

    /**
     * 
     * @param terminal
     * @param tipo
     * @param empresa
     * @param local
     * @param key
     * @param matricula
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoBuscarCodigoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarCodigoAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarCodigoAcesso")
    @ResponseWrapper(localName = "buscarCodigoAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarCodigoAcessoResponse")
    public RetornoRequisicaoBuscarCodigoAcesso buscarCodigoAcesso(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "local", targetNamespace = "")
        Integer local,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param pessoa
     * @param key
     * @return
     *     returns byte[]
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "pegarFotoPessoa", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.PegarFotoPessoa")
    @ResponseWrapper(localName = "pegarFotoPessoaResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.PegarFotoPessoaResponse")
    public byte[] pegarFotoPessoa(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "pessoa", targetNamespace = "")
        Integer pessoa)
        throws Exception_Exception
    ;

    /**
     * 
     * @param forcarLib
     * @param terminal
     * @param codigoCartao
     * @param localAcesso
     * @param sentido
     * @param empresa
     * @param meioIdentificacao
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarAcessoPeloCodigoAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoPeloCodigoAcesso")
    @ResponseWrapper(localName = "validarAcessoPeloCodigoAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoPeloCodigoAcessoResponse")
    public RetornoRequisicaoValidacaoAcesso validarAcessoPeloCodigoAcesso(
        @WebParam(name = "codigoCartao", targetNamespace = "")
        String codigoCartao,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "forcarLib", targetNamespace = "")
        Boolean forcarLib,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "sentido", targetNamespace = "")
        DirecaoAcessoEnum sentido,
        @WebParam(name = "terminal", targetNamespace = "")
        String terminal)
        throws Exception_Exception
    ;

    /**
     * 
     * @param nomeComputador
     * @param empresa
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoBuscarLocais
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarConfigLocalAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarConfigLocalAcesso")
    @ResponseWrapper(localName = "buscarConfigLocalAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarConfigLocalAcessoResponse")
    public RetornoRequisicaoBuscarLocais buscarConfigLocalAcesso(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomeComputador", targetNamespace = "")
        String nomeComputador)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param pessoa
     * @param key
     * @param codigoAutorizacao
     * @throws Exception_Exception
     */
    @WebMethod
    @RequestWrapper(localName = "excluirPessoaFotoLocalAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ExcluirPessoaFotoLocalAcesso")
    @ResponseWrapper(localName = "excluirPessoaFotoLocalAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ExcluirPessoaFotoLocalAcessoResponse")
    public void excluirPessoaFotoLocalAcesso(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "pessoa", targetNamespace = "")
        Integer pessoa,
        @WebParam(name = "codigoAutorizacao", targetNamespace = "")
        String codigoAutorizacao)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoDadosOffline
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "montarDadosOffline", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.MontarDadosOffline")
    @ResponseWrapper(localName = "montarDadosOfflineResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.MontarDadosOfflineResponse")
    public RetornoRequisicaoDadosOffline montarDadosOffline(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso);

    /**
     * 
     * @param localAcesso
     * @param key
     * @return
     *     returns java.lang.Boolean
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "temDadosOfflineGerados", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.TemDadosOfflineGerados")
    @ResponseWrapper(localName = "temDadosOfflineGeradosResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.TemDadosOfflineGeradosResponse")
    public Boolean temDadosOfflineGerados(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso);

    /**
     * 
     * @param codigo
     * @param terminal
     * @param usuario
     * @param tipo
     * @param empresa
     * @param dataAcesso
     * @param situacao
     * @param direcao
     * @param meioIdentificacao
     * @param local
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoRegistrarAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "registrarAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarAcesso")
    @ResponseWrapper(localName = "registrarAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarAcessoResponse")
    public RetornoRequisicaoRegistrarAcesso registrarAcesso(
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "dataAcesso", targetNamespace = "")
        XMLGregorianCalendar dataAcesso,
        @WebParam(name = "direcao", targetNamespace = "")
        DirecaoAcessoEnum direcao,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "local", targetNamespace = "")
        Integer local,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "situacao", targetNamespace = "")
        SituacaoAcessoEnum situacao,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "ultimoDadosOfflineGerado", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.UltimoDadosOfflineGerado")
    @ResponseWrapper(localName = "ultimoDadosOfflineGeradoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.UltimoDadosOfflineGeradoResponse")
    public String ultimoDadosOfflineGerado(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso);

    /**
     * 
     * @param nome
     * @return
     *     returns java.lang.String
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "solicitarUtilitario", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.SolicitarUtilitario")
    @ResponseWrapper(localName = "solicitarUtilitarioResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.SolicitarUtilitarioResponse")
    public String solicitarUtilitario(
        @WebParam(name = "nome", targetNamespace = "")
        String nome)
        throws Exception_Exception
    ;

    /**
     * 
     */
    @WebMethod
    @RequestWrapper(localName = "resetMapaControladores", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ResetMapaControladores")
    @ResponseWrapper(localName = "resetMapaControladoresResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ResetMapaControladoresResponse")
    public void resetMapaControladores();

    /**
     * 
     * @param conteudo
     * @param nome
     * @param key
     * @throws Exception_Exception
     */
    @WebMethod
    @RequestWrapper(localName = "gravarArquivoLocal", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GravarArquivoLocal")
    @ResponseWrapper(localName = "gravarArquivoLocalResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GravarArquivoLocalResponse")
    public void gravarArquivoLocal(
        @WebParam(name = "conteudo", targetNamespace = "")
        byte[] conteudo,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nome", targetNamespace = "")
        String nome)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param key
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarOperacoesPendentes", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ConsultarOperacoesPendentes")
    @ResponseWrapper(localName = "consultarOperacoesPendentesResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ConsultarOperacoesPendentesResponse")
    public String consultarOperacoesPendentes(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso);

    /**
     * 
     * @param forcarLib
     * @param terminal
     * @param localAcesso
     * @param sentido
     * @param empresa
     * @param meioIdentificacao
     * @param senha
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarAcessoPessoaPorSenha", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoPessoaPorSenha")
    @ResponseWrapper(localName = "validarAcessoPessoaPorSenhaResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoPessoaPorSenhaResponse")
    public RetornoRequisicaoValidacaoAcesso validarAcessoPessoaPorSenha(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "forcarLib", targetNamespace = "")
        Boolean forcarLib,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "senha", targetNamespace = "")
        String senha,
        @WebParam(name = "sentido", targetNamespace = "")
        DirecaoAcessoEnum sentido,
        @WebParam(name = "terminal", targetNamespace = "")
        String terminal)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigo
     * @param forcarLib
     * @param terminal
     * @param localAcesso
     * @param sentido
     * @param empresa
     * @param meioIdentificacao
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarAcessoColaboradorPeloCodigo", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoColaboradorPeloCodigo")
    @ResponseWrapper(localName = "validarAcessoColaboradorPeloCodigoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoColaboradorPeloCodigoResponse")
    public RetornoRequisicaoValidacaoAcesso validarAcessoColaboradorPeloCodigo(
        @WebParam(name = "codigo", targetNamespace = "")
        String codigo,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "forcarLib", targetNamespace = "")
        Boolean forcarLib,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "sentido", targetNamespace = "")
        DirecaoAcessoEnum sentido,
        @WebParam(name = "terminal", targetNamespace = "")
        String terminal)
        throws Exception_Exception
    ;

    /**
     * 
     * @param localAcesso
     * @param key
     * @return
     *     returns java.util.List<java.lang.String>
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "montarListaAcessosPessoasComFoto", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.MontarListaAcessosPessoasComFoto")
    @ResponseWrapper(localName = "montarListaAcessosPessoasComFotoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.MontarListaAcessosPessoasComFotoResponse")
    public List<String> montarListaAcessosPessoasComFoto(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso)
        throws Exception_Exception
    ;

    /**
     * 
     * @param forcarLib
     * @param terminal
     * @param localAcesso
     * @param sentido
     * @param empresa
     * @param meioIdentificacao
     * @param key
     * @param matricula
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoValidacaoAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarAcessoClientePelaMatricula", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoClientePelaMatricula")
    @ResponseWrapper(localName = "validarAcessoClientePelaMatriculaResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarAcessoClientePelaMatriculaResponse")
    public RetornoRequisicaoValidacaoAcesso validarAcessoClientePelaMatricula(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "forcarLib", targetNamespace = "")
        Boolean forcarLib,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "localAcesso", targetNamespace = "")
        Integer localAcesso,
        @WebParam(name = "matricula", targetNamespace = "")
        String matricula,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "sentido", targetNamespace = "")
        DirecaoAcessoEnum sentido,
        @WebParam(name = "terminal", targetNamespace = "")
        String terminal)
        throws Exception_Exception
    ;

    /**
     * 
     * @param idEmpresa
     * @param nomePesquisar
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoConsultarClientes
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "consultarClientesPeloNome", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ConsultarClientesPeloNome")
    @ResponseWrapper(localName = "consultarClientesPeloNomeResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ConsultarClientesPeloNomeResponse")
    public RetornoRequisicaoConsultarClientes consultarClientesPeloNome(
        @WebParam(name = "idEmpresa", targetNamespace = "")
        Integer idEmpresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "nomePesquisar", targetNamespace = "")
        String nomePesquisar)
        throws Exception_Exception
    ;

    /**
     * 
     * @param empresa
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoBuscarIntegracoes
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "buscarConfigIntegracaoAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarConfigIntegracaoAcesso")
    @ResponseWrapper(localName = "buscarConfigIntegracaoAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.BuscarConfigIntegracaoAcessoResponse")
    public RetornoRequisicaoBuscarIntegracoes buscarConfigIntegracaoAcesso(
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key);

    /**
     * 
     * @param codigoAcesso
     * @param key
     * @return
     *     returns java.lang.Boolean
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "validarSePodeExcluirDigital", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarSePodeExcluirDigital")
    @ResponseWrapper(localName = "validarSePodeExcluirDigitalResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.ValidarSePodeExcluirDigitalResponse")
    public Boolean validarSePodeExcluirDigital(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAcesso", targetNamespace = "")
        String codigoAcesso);

    /**
     * 
     * @param codigoAcesso
     * @param senha
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoWS
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarSenhaIntegracao", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GravarSenhaIntegracao")
    @ResponseWrapper(localName = "gravarSenhaIntegracaoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GravarSenhaIntegracaoResponse")
    public RetornoRequisicaoWS gravarSenhaIntegracao(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAcesso", targetNamespace = "")
        String codigoAcesso,
        @WebParam(name = "senha", targetNamespace = "")
        String senha)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigoAcesso
     * @param key
     */
    @WebMethod
    @RequestWrapper(localName = "registrarCodigoNaoPossuiMaisDeumaDigital", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarCodigoNaoPossuiMaisDeumaDigital")
    @ResponseWrapper(localName = "registrarCodigoNaoPossuiMaisDeumaDigitalResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarCodigoNaoPossuiMaisDeumaDigitalResponse")
    public void registrarCodigoNaoPossuiMaisDeumaDigital(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAcesso", targetNamespace = "")
        String codigoAcesso);

    /**
     * 
     * @param senhaEncriptada
     * @param codigoAcesso
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoWS
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "gravarSenhaIntegracaoEncriptada", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GravarSenhaIntegracaoEncriptada")
    @ResponseWrapper(localName = "gravarSenhaIntegracaoEncriptadaResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.GravarSenhaIntegracaoEncriptadaResponse")
    public RetornoRequisicaoWS gravarSenhaIntegracaoEncriptada(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAcesso", targetNamespace = "")
        String codigoAcesso,
        @WebParam(name = "senhaEncriptada", targetNamespace = "")
        String senhaEncriptada)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigoAcesso
     * @param key
     */
    @WebMethod
    @RequestWrapper(localName = "registrarCodigoPossuiMaisDeumaDigital", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarCodigoPossuiMaisDeumaDigital")
    @ResponseWrapper(localName = "registrarCodigoPossuiMaisDeumaDigitalResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarCodigoPossuiMaisDeumaDigitalResponse")
    public void registrarCodigoPossuiMaisDeumaDigital(
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "codigoAcesso", targetNamespace = "")
        String codigoAcesso);

}
