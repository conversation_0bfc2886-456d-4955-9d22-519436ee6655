
package negocio.comuns.acesso.webservice.client;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for retornoRequisicaoBuscarIntegracoes complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="retornoRequisicaoBuscarIntegracoes">
 *   &lt;complexContent>
 *     &lt;extension base="{http://webservice.acesso/}retornoRequisicaoWS">
 *       &lt;sequence>
 *         &lt;element name="listaIntegracoes" type="{http://webservice.acesso/}integracaoAcessoWS" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "retornoRequisicaoBuscarIntegracoes", propOrder = {
    "listaIntegracoes"
})
public class RetornoRequisicaoBuscarIntegracoes
    extends RetornoRequisicaoWS
{

    @XmlElement(nillable = true)
    protected List<IntegracaoAcessoWS> listaIntegracoes;

    /**
     * Gets the value of the listaIntegracoes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the listaIntegracoes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getListaIntegracoes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IntegracaoAcessoWS }
     * 
     * 
     */
    public List<IntegracaoAcessoWS> getListaIntegracoes() {
        if (listaIntegracoes == null) {
            listaIntegracoes = new ArrayList<IntegracaoAcessoWS>();
        }
        return this.listaIntegracoes;
    }

}
