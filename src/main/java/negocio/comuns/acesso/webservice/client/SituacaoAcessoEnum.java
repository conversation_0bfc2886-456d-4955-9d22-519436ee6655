
package negocio.comuns.acesso.webservice.client;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for situacaoAcessoEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="situacaoAcessoEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="RV_BLOQALUNONAOCADASTRADO"/>
 *     &lt;enumeration value="RV_BLOQALUNOMATNAOCADASTRADO"/>
 *     &lt;enumeration value="RV_BLOQCOLABORADORNAOCADASTRADO"/>
 *     &lt;enumeration value="RV_BLOQCOLABORADORCODNAOCADASTRADO"/>
 *     &lt;enumeration value="RV_BLOQEMPRESANAOCONFERE"/>
 *     &lt;enumeration value="RV_BLOQTAMCARTAOINVALIDO"/>
 *     &lt;enumeration value="RV_BLOQFORAHORARIO"/>
 *     &lt;enumeration value="RV_BLOQFORAHORARIOTURMA"/>
 *     &lt;enumeration value="RV_BLOQCONTRATOTRANCADO"/>
 *     &lt;enumeration value="RV_BLOQCONTRATOFERIAS"/>
 *     &lt;enumeration value="RV_BLOQCONTATOVENCIDO"/>
 *     &lt;enumeration value="RV_BLOQCONTRATONAOINICIOU"/>
 *     &lt;enumeration value="RV_BLOQEXAMEVENCIDO"/>
 *     &lt;enumeration value="RV_BLOQMSGPERSONALIZADA"/>
 *     &lt;enumeration value="RV_BLOQACESSOSSEGUIDOS"/>
 *     &lt;enumeration value="RV_BLOQCONTRATOATESTADOM"/>
 *     &lt;enumeration value="RV_BLOQSTATUSALUNO"/>
 *     &lt;enumeration value="RV_BLOQSEMAUTORIZACAO"/>
 *     &lt;enumeration value="RV_BLOQDVNAOCONFERE"/>
 *     &lt;enumeration value="RV_LIBACESSOAUTORIZADO"/>
 *     &lt;enumeration value="RV_BLOQREGRA_LIBERACAO"/>
 *     &lt;enumeration value="RV_BLOQPERSONAL"/>
 *     &lt;enumeration value="RV_BLOQCOLABORADORINATIVO"/>
 *     &lt;enumeration value="RV_BLOQPESSOASENHAINVALIDA"/>
 *     &lt;enumeration value="RV_BLOQALUNOPARCELAABERTA"/>
 *     &lt;enumeration value="RV_BLOQALUNOFREQUENCIAPLANO"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "situacaoAcessoEnum")
@XmlEnum
public enum SituacaoAcessoEnum {

    RV_BLOQALUNONAOCADASTRADO,
    RV_BLOQALUNOMATNAOCADASTRADO,
    RV_BLOQCOLABORADORNAOCADASTRADO,
    RV_BLOQCOLABORADORCODNAOCADASTRADO,
    RV_BLOQEMPRESANAOCONFERE,
    RV_BLOQTAMCARTAOINVALIDO,
    RV_BLOQFORAHORARIO,
    RV_BLOQFORAHORARIOTURMA,
    RV_BLOQCONTRATOTRANCADO,
    RV_BLOQCONTRATOFERIAS,
    RV_BLOQCONTATOVENCIDO,
    RV_BLOQCONTRATONAOINICIOU,
    RV_BLOQEXAMEVENCIDO,
    RV_BLOQMSGPERSONALIZADA,
    RV_BLOQACESSOSSEGUIDOS,
    RV_BLOQCONTRATOATESTADOM,
    RV_BLOQSTATUSALUNO,
    RV_BLOQSEMAUTORIZACAO,
    RV_BLOQDVNAOCONFERE,
    RV_LIBACESSOAUTORIZADO,
    RV_BLOQREGRA_LIBERACAO,
    RV_BLOQPERSONAL,
    RV_BLOQCOLABORADORINATIVO,
    RV_BLOQPESSOASENHAINVALIDA,
    RV_BLOQALUNOPARCELAABERTA,
    RV_BLOQALUNOFREQUENCIAPLANO;

    public String value() {
        return name();
    }

    public static SituacaoAcessoEnum fromValue(String v) {
        return valueOf(v);
    }

}
