
package negocio.comuns.acesso.webservice.client;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.4-b01
 * Generated source version: 2.1
 * 
 */
@WebService(name = "RegistrarAcessoWS", targetNamespace = "http://webservice.acesso/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface RegistrarAcessoWS {


    /**
     * 
     * @param codigo
     * @param terminal
     * @param usuario
     * @param tipo
     * @param empresa
     * @param dataAcesso
     * @param situacao
     * @param direcao
     * @param meioIdentificacao
     * @param local
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoRegistrarAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "registrarAcesso", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarAcesso")
    @ResponseWrapper(localName = "registrarAcessoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarAcessoResponse")
    public RetornoRequisicaoRegistrarAcesso registrarAcesso(
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "dataAcesso", targetNamespace = "")
        XMLGregorianCalendar dataAcesso,
        @WebParam(name = "direcao", targetNamespace = "")
        DirecaoAcessoEnum direcao,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "local", targetNamespace = "")
        Integer local,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "situacao", targetNamespace = "")
        SituacaoAcessoEnum situacao,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario)
        throws Exception_Exception
    ;

    /**
     * 
     * @param codigo
     * @param codAcessoIntegracao
     * @param terminal
     * @param usuario
     * @param tipo
     * @param empresa
     * @param dataAcesso
     * @param situacao
     * @param direcao
     * @param meioIdentificacao
     * @param local
     * @param key
     * @return
     *     returns negocio.comuns.acesso.webservice.client.RetornoRequisicaoRegistrarAcesso
     * @throws Exception_Exception
     */
    @WebMethod
    @WebResult(targetNamespace = "")
    @RequestWrapper(localName = "registrarAcessoAvaliandoIntegracao", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarAcessoAvaliandoIntegracao")
    @ResponseWrapper(localName = "registrarAcessoAvaliandoIntegracaoResponse", targetNamespace = "http://webservice.acesso/", className = "negocio.comuns.acesso.webservice.client.RegistrarAcessoAvaliandoIntegracaoResponse")
    public RetornoRequisicaoRegistrarAcesso registrarAcessoAvaliandoIntegracao(
        @WebParam(name = "codigo", targetNamespace = "")
        Integer codigo,
        @WebParam(name = "dataAcesso", targetNamespace = "")
        XMLGregorianCalendar dataAcesso,
        @WebParam(name = "direcao", targetNamespace = "")
        DirecaoAcessoEnum direcao,
        @WebParam(name = "empresa", targetNamespace = "")
        Integer empresa,
        @WebParam(name = "key", targetNamespace = "")
        String key,
        @WebParam(name = "local", targetNamespace = "")
        Integer local,
        @WebParam(name = "meioIdentificacao", targetNamespace = "")
        MeioIdentificacaoEnum meioIdentificacao,
        @WebParam(name = "situacao", targetNamespace = "")
        SituacaoAcessoEnum situacao,
        @WebParam(name = "terminal", targetNamespace = "")
        Integer terminal,
        @WebParam(name = "tipo", targetNamespace = "")
        String tipo,
        @WebParam(name = "usuario", targetNamespace = "")
        Integer usuario,
        @WebParam(name = "codAcessoIntegracao", targetNamespace = "")
        String codAcessoIntegracao)
        throws Exception_Exception
    ;

}
