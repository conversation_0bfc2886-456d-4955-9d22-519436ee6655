/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.comuns.acesso.auxiliar;

/**
 *
 * <AUTHOR>
 */
public class CodigoAcesso {

    private String Cd;
    private String tipo;
    private String codigo;
    private String id;
    private String via;
    private String dv;

    public CodigoAcesso() {
        setCd("");
        setTipo("");
        setCodigo("");
        setId("");
        setVia("");
        setDv("");
    }

    public String getCd() {
        return Cd;
    }

    public void setCd(String Cd) {
        this.Cd = Cd;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDv() {
        return dv;
    }

    public void setDv(String dv) {
        this.dv = dv;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getVia() {
        return via;
    }

    public void setVia(String via) {
        this.via = via;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
