/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.acesso;

import br.com.pacto.priv.utils.Uteis;
import java.util.Date;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 *
 * <AUTHOR>
 */
public class LiberacaoAcessoVO extends SuperVO {

    private Integer codigo = 0;
    private PessoaVO pessoa = new PessoaVO();
    private TipoLiberacaoEnum tipoLiberacao;
    private DirecaoAcessoEnum sentido;
    private LocalAcessoVO localAcesso = new LocalAcessoVO();
    private ColetorVO coletor = new ColetorVO();
    private UsuarioVO usuario = new UsuarioVO();
    private Integer empresa;
    private Date dataHora;
    private String justificativa = "";
    private Date dthrJustificativa;
    private UsuarioVO usuarioJustificou = new UsuarioVO();
    private String nomeGenerico;

    public static void validarDados(LiberacaoAcessoVO lib, String nomeCampoPessoa) throws ConsistirException {
        if (lib.tipoLiberacao != TipoLiberacaoEnum.TERCEIRIZADO
                && lib.tipoLiberacao != TipoLiberacaoEnum.VISITANTE_DIVERSO) {
            if (lib.pessoa == null || lib.pessoa.getCodigo() == null || lib.pessoa.getCodigo() == 0) {
                throw new ConsistirException("Informe o " + nomeCampoPessoa);
            }
        }
        if (UteisValidacao.emptyString(lib.justificativa)) {
            throw new ConsistirException("Informe a justificativa");
        }
    }

    public String getLocalApresentar() {
        return getLocalAcesso().getDescricao();
    }

    public String getColetorApresentar() {
        return getColetor().getDescricao();
    }

    public String getSentidoApresentar() {
        return getSentido().getId();
    }

    public String getTipoLiberacaoApresentar() {
        return getTipoLiberacao().getDescricao();
    }

    public String getUsuarioLiberouApresentar() {
        return getUsuario().getNomeAbreviado();
    }

    public String getPessoaApresentar() {
        if (isApresentarNomeGenerico()) {
            return getNomeGenerico();
        } else {
            return getPessoa().getPrimeiroNomeConcatenado();
        }
    }

    public String getUsuarioJustificouApresentar() {
        return getUsuarioJustificou().getNomeAbreviado();
    }

    public ColetorVO getColetor() {
        return coletor;
    }

    public void setColetor(ColetorVO coletor) {
        this.coletor = coletor;
    }

    public LocalAcessoVO getLocalAcesso() {
        return localAcesso;
    }

    public void setLocalAcesso(LocalAcessoVO localAcesso) {
        this.localAcesso = localAcesso;
    }

    public UsuarioVO getUsuario() {
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public DirecaoAcessoEnum getSentido() {
        return sentido;
    }

    public void setSentido(DirecaoAcessoEnum sentido) {
        this.sentido = sentido;
    }

    public Date getDataHora() {
        return dataHora;
    }

    public void setDataHora(Date dataHora) {
        this.dataHora = dataHora;
    }

    public Date getDthrJustificativa() {
        return dthrJustificativa;
    }

    public void setDthrJustificativa(Date dthrJustificativa) {
        this.dthrJustificativa = dthrJustificativa;
    }

    public String getJustificativa() {
        if (justificativa == null) {
            justificativa = "";
        }
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    /**
     * Apresenta parte da justificativa
     */
    public String getJustificativaPequena() {
        String justPequena = "";
        if (getJustificativa().length() > 50) {
            justPequena = getJustificativa().substring(0, 50);
        }
        return justPequena;
    }

    public PessoaVO getPessoa() {
        return pessoa;
    }

    public void setPessoa(PessoaVO pessoa) {
        this.pessoa = pessoa;
    }

    public TipoLiberacaoEnum getTipoLiberacao() {
        return tipoLiberacao;
    }

    public void setTipoLiberacao(TipoLiberacaoEnum tipoLiberacao) {
        this.tipoLiberacao = tipoLiberacao;
    }

    /**
     * @return the usuarioJustificou
     */
    public UsuarioVO getUsuarioJustificou() {
        return usuarioJustificou;
    }

    /**
     * @param usuarioJustificou the usuarioJustificou to set
     */
    public void setUsuarioJustificou(UsuarioVO usuarioJustificou) {
        this.usuarioJustificou = usuarioJustificou;
    }
    
    public String getDataHoraApresentar(){
        return Uteis.getDataComHHMM(dataHora);
    }
    
    public String getJustificativaApresentar(){
        String justPequena = "";
        if (getJustificativa().length() > 50) {
            justPequena = getJustificativa().substring(0, 50);
        }else{
            justPequena = getJustificativa();
        }
        return justPequena;
    }

    public String getNomeGenerico() {
        return nomeGenerico;
    }

    public void setNomeGenerico(String nomeGenerico) {
        this.nomeGenerico = nomeGenerico;
    }

    public boolean isApresentarNomeGenerico() {
        return getPessoa() == null || getPessoa().getCodigo() == 0;
    }
}
