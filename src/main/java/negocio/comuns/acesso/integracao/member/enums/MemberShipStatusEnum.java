package negocio.comuns.acesso.integracao.member.enums;

import negocio.comuns.utilitarias.UteisValidacao;

public enum MemberShipStatusEnum {

    ACTIVE("Active"),
    TRANSFER("Transfer"),
    EXPIRED("Expired"),
    CANCELED("Canceled"),
    ;

    MemberShipStatusEnum(String name) {
        this.name = name;
    }

    private String name;

    public static MemberShipStatusEnum getFromName(String nameStatus) {
        if (!UteisValidacao.emptyString(nameStatus)) {
            for (MemberShipStatusEnum m: MemberShipStatusEnum.values()) {
                if (m.getName().toLowerCase().equals(nameStatus.toLowerCase())) {
                    return m;
                }
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
