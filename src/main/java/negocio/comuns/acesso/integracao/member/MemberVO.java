package negocio.comuns.acesso.integracao.member;

import br.com.pactosolucoes.enumeradores.SituacaoClienteEnum;
import br.com.pactosolucoes.enumeradores.TipoColaboradorEnum;
import negocio.comuns.acesso.IntegracaoMemberVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MemberVO extends SuperVO {

    private Integer idMember;
    private String firstName;
    private String lastName;
    private String registerDate;
    private Integer idBranch;
    private String branchName;
    private String document;
    private String documentId;
    private String status;
    private String lastAccessDate;
    private String photoUrl;
    private String birthDate;
    private String gender;
    private String address;
    private String number;
    private String complement;
    private String neighborhood;
    private String state;
    private String city;
    private String country;
    private String zipCode;
    private List<ContactVO> contacts;
    private Integer idMemberMembership;
    private String startDate;
    private String endDate;
    private String nameMembership;
    private String cancelDate;
    private String membershipStatus;

    private Date lastSync;
    private IntegracaoMemberVO integracaoMemberVO;
    private boolean sincronizado = false;
    private JSONObject memberJson;
    private JSONArray salesJson;
    private JSONArray receivablesJson;

    public MemberVO() {
    }

    public MemberVO(MembersApiViewTO membersApiViewTO) {
        setIdMember(membersApiViewTO.getIdMember());
        setFirstName(membersApiViewTO.getFirstName());
        setLastName(membersApiViewTO.getLastName());
        setRegisterDate(membersApiViewTO.getRegisterDate());
        setIdBranch(membersApiViewTO.getIdBranch());
        setBranchName(membersApiViewTO.getBranchName());
        setDocument(membersApiViewTO.getDocument());
        setDocumentId(membersApiViewTO.getDocumentId());
        setStatus(membersApiViewTO.getStatus());
        setLastAccessDate(membersApiViewTO.getLastAccessDate());
        setPhotoUrl(membersApiViewTO.getPhotoUrl());
        if (!membersApiViewTO.getMemberships().isEmpty()) {
            MemberMembershipApiViewTO membership = membersApiViewTO.getMemberships().get(0);
            setIdMemberMembership(membership.getIdMemberMembership());
            setStartDate(membership.getStartDate());
            setEndDate(membership.getEndDate());
            setNameMembership(membership.getName());
            setCancelDate(membership.getCancelDate());
            setMembershipStatus(membership.getMembershipStatus());
        }
        setGender(membersApiViewTO.getGender());
        setAddress(membersApiViewTO.getAddress());
        setNumber(membersApiViewTO.getNumber());
        setComplement(membersApiViewTO.getComplement());
        setNeighborhood(membersApiViewTO.getNeighborhood());
        setZipCode(membersApiViewTO.getZipCode());
        setCity(membersApiViewTO.getCity());
        setState(membersApiViewTO.getState());
        setCountry(membersApiViewTO.getCountry());
        if (!UteisValidacao.emptyList(membersApiViewTO.getContacts())) {
            for (ContactsApiViewTO contactsApiViewTO: membersApiViewTO.getContacts()) {
                getContacts().add(new ContactVO(contactsApiViewTO));
            }
        }
    }

    public Integer getIdMember() {
        return idMember;
    }

    public void setIdMember(Integer idMember) {
        this.idMember = idMember;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getRegisterDate() {
        return registerDate;
    }

    public void setRegisterDate(String registerDate) {
        this.registerDate = registerDate;
    }

    public Integer getIdBranch() {
        return idBranch;
    }

    public void setIdBranch(Integer idBranch) {
        this.idBranch = idBranch;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLastAccessDate() {
        return lastAccessDate;
    }

    public void setLastAccessDate(String lastAccessDate) {
        this.lastAccessDate = lastAccessDate;
    }

    public String getPhotoUrl() {
        return photoUrl;
    }

    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getComplement() {
        return complement;
    }

    public void setComplement(String complement) {
        this.complement = complement;
    }

    public String getNeighborhood() {
        return neighborhood;
    }

    public void setNeighborhood(String neighborhood) {
        this.neighborhood = neighborhood;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public List<ContactVO> getContacts() {
        if (contacts == null) {
            contacts = new ArrayList<>();
        }
        return contacts;
    }

    public void setContacts(List<ContactVO> contacts) {
        this.contacts = contacts;
    }

    public Integer getIdMemberMembership() {
        return idMemberMembership;
    }

    public void setIdMemberMembership(Integer idMemberMembership) {
        this.idMemberMembership = idMemberMembership;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        if (endDate == null) {
            endDate = "";
        }
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getNameMembership() {
        return nameMembership;
    }

    public void setNameMembership(String nameMembership) {
        this.nameMembership = nameMembership;
    }

    public String getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(String cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getMembershipStatus() {
        if (membershipStatus == null) {
            membershipStatus = "";
        }
        return membershipStatus;
    }

    public void setMembershipStatus(String membershipStatus) {
        this.membershipStatus = membershipStatus;
    }

    public Date getLastSync() {
        return lastSync;
    }

    public void setLastSync(Date lastSync) {
        this.lastSync = lastSync;
    }

    public IntegracaoMemberVO getIntegracaoMemberVO() {
        if (integracaoMemberVO == null) {
            integracaoMemberVO = new IntegracaoMemberVO();
        }
        return integracaoMemberVO;
    }

    public void setIntegracaoMemberVO(IntegracaoMemberVO integracaoMemberVO) {
        this.integracaoMemberVO = integracaoMemberVO;
    }

    public boolean isSincronizado() {
        return sincronizado;
    }

    public void setSincronizado(boolean sincronizado) {
        this.sincronizado = sincronizado;
    }

    public JSONObject getMemberJson() {
        return memberJson;
    }

    public void setMemberJson(JSONObject memberJson) {
        this.memberJson = memberJson;
    }

    public JSONArray getSalesJson() {
        return salesJson;
    }

    public void setSalesJson(JSONArray salesJson) {
        this.salesJson = salesJson;
    }

    public JSONArray getReceivablesJson() {
        return receivablesJson;
    }

    public void setReceivablesJson(JSONArray receivablesJson) {
        this.receivablesJson = receivablesJson;
    }

    public void updateFields(boolean changed, MemberVO memberUpdated) {
        if (changed) {
            setIdMemberMembership(memberUpdated.getIdMemberMembership());
            setStartDate(memberUpdated.getStartDate());
            setEndDate(memberUpdated.getEndDate());
            setNameMembership(memberUpdated.getNameMembership());
            setCancelDate(memberUpdated.getCancelDate());
            setMembershipStatus(memberUpdated.getMembershipStatus());
        }
        setLastSync(Calendario.hoje());
    }

    public ClienteVO toCliente() throws ConsistirException {
        Date dataCadastro = new Date();
        if (!UteisValidacao.emptyString(registerDate)) {
            LocalDateTime localDateTime = LocalDateTime.parse(registerDate);
            dataCadastro = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } else if (!UteisValidacao.emptyString(startDate)) {
            LocalDateTime localDateTime = LocalDateTime.parse(startDate);
            dataCadastro = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        }

        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setNome(firstName + " " + lastName);
        pessoaVO.setCfp(Uteis.formatarCpfCnpj(document, false));
        pessoaVO.setDataCadastro(dataCadastro);
        pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
        pessoaVO.setTipoPessoa(TipoPessoa.FISICA.getLabel());


        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setPessoa(pessoaVO);
        clienteVO.setIdExterno(Long.valueOf(idMember));
        clienteVO.setMatriculaExterna(Long.valueOf(idMember));
        clienteVO.setMatricula(Uteis.getMontarMatricula(String.valueOf(idMember), 6));
        clienteVO.setCodigoMatricula(idMember);
        clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
        clienteVO.setEmpresa(new EmpresaVO(1));


        ColaboradorVO colaboradorVO = new ColaboradorVO();
        colaboradorVO.setCodigo(1);

        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setCliente(clienteVO);
        vinculoVO.setColaborador(colaboradorVO);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());

        clienteVO.getVinculoVOs().add(vinculoVO);
        return clienteVO;
    }

    public ClienteVO toCliente(ColaboradorVO colaboradorVinculo) throws ConsistirException {
        LocalDateTime localDateTime = LocalDateTime.parse(registerDate);
        Date dataCadastro = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

        PessoaVO pessoaVO = new PessoaVO();
        pessoaVO.setNome(firstName + " " + lastName);
        pessoaVO.setCfp(Uteis.formatarCpfCnpj(document, false));
        pessoaVO.setDataCadastro(dataCadastro);
        pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
        pessoaVO.setTipoPessoa(TipoPessoa.FISICA.getLabel());


        ClienteVO clienteVO = new ClienteVO();
        clienteVO.setPessoa(pessoaVO);
        clienteVO.setIdExterno(Long.valueOf(idMember));
        clienteVO.setMatriculaExterna(Long.valueOf(idMember));
        clienteVO.setMatricula(Uteis.getMontarMatricula(String.valueOf(idMember), 6));
        clienteVO.setCodigoMatricula(idMember);
        clienteVO.setSituacao(SituacaoClienteEnum.VISITANTE.getCodigo());
        clienteVO.setEmpresa(getIntegracaoMemberVO().getEmpresa());


        VinculoVO vinculoVO = new VinculoVO();
        vinculoVO.setCliente(clienteVO);
        vinculoVO.setColaborador(colaboradorVinculo);
        vinculoVO.setTipoVinculo(TipoColaboradorEnum.CONSULTOR.getSigla());

        clienteVO.getVinculoVOs().add(vinculoVO);
        return clienteVO;
    }
}
