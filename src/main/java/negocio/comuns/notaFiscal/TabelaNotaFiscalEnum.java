package negocio.comuns.notaFiscal;

import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import negocio.comuns.financeiro.NotaFiscalConsumidorEletronicaVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public enum TabelaNotaFiscalEnum {

    COLUNA_CODIGO(0, Integer.class, Integer.class, "codigo", true),
    COLUNA_DATAREGISTRO(1, Date.class, Date.class, "dataRegistro", false),
    COLUNA_IDREFERENCIA(2, String.class, String.class, "idReferencia", false),
    COLUNA_IDEXTERNO(2, String.class, String.class, "idExterno", false),
    COLUNA_STATUSNOTA(3, String.class, String.class, "statusNota", false),
    COLUNA_MOTIVOSTATUS(3, String.class, String.class, "motivoStatus", false),
    COLUNA_NUMERO(4, String.class, String.class, "numero", false),
    COLUNA_SERIE(5, String.class, String.class, "serie", false),
    COLUNA_CHAVEACESSO(6, String.class, String.class, "chaveAcesso", false),
    COLUNA_LINKXML(7, String.class, String.class, "linkXML", false),
    COLUNA_LINKDANFE(8, String.class, String.class, "linkDanfe", false),
    COLUNA_LINKCONSULTACHAVEACESSO(9, String.class, String.class, "linkConsultaChaveAcesso", false),
    COLUNA_PESSOA(10, PessoaVO.class, Integer.class, "pessoa", false),
    COLUNA_TIPO(11, TipoNotaFiscalEnum.class, Integer.class, "tipo", false),
    COLUNA_EMPRESA(12, EmpresaVO.class, Integer.class, "empresa", false),
    COLUNA_NFSEEMITIDA(13, NFSeEmitidaVO.class, Integer.class, "nfSeEmitida", false),
    COLUNA_NOTAFISCALCONSUMIDORELETRONICA(14, NotaFiscalConsumidorEletronicaVO.class, Integer.class, "notaFiscalConsumidorEletronica", false),
    COLUNA_JSONENVIO(15, String.class, String.class, "jsonEnvio", false),
    COLUNA_JSONRETORNO(16, String.class, String.class, "jsonRetorno", false),
    COLUNA_SITUACAO(17, SituacaoNotaFiscalEnum.class, Integer.class, "situacao", false),
    COLUNA_CONFIGURACAONOTAFISCAL(18, ConfiguracaoNotaFiscalVO.class, Integer.class, "configuracaoNotaFiscal", false),
    ;

    public static String NOME_TABELA = "NotaFiscal";

    private Integer codigo;
    private Object tipo;
    private Object tipoColuna;
    private String descricao;
    private boolean chavePrimaria;

    private TabelaNotaFiscalEnum(Integer codigo, Object tipo, Object tipoColuna,
                                 String descricao, boolean chavePrimaria) {
        this.codigo = codigo;
        this.tipo = tipo;
        this.tipoColuna = tipoColuna;
        this.descricao = descricao;
        this.chavePrimaria = chavePrimaria;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Object getTipo() {
        return tipo;
    }

    public void setTipo(Object tipo) {
        this.tipo = tipo;
    }

    public Object getTipoColuna() {
        return tipoColuna;
    }

    public void setTipoColuna(Object tipoColuna) {
        this.tipoColuna = tipoColuna;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isChavePrimaria() {
        return chavePrimaria;
    }

    public void setChavePrimaria(boolean chavePrimaria) {
        this.chavePrimaria = chavePrimaria;
    }

    public static String obterChavePrimaria() {
        for (TabelaNotaFiscalEnum col : TabelaNotaFiscalEnum.values()) {
            if (col.isChavePrimaria()) {
                return col.getDescricao();
            }
        }
        return null;
    }

    public static List<String> obterListaColunas() {
        List<String> listaColunas = new ArrayList<String>();
        for (TabelaNotaFiscalEnum col : TabelaNotaFiscalEnum.values()) {
            if (!col.isChavePrimaria()) {
                listaColunas.add(col.getDescricao());
            }
        }
        return listaColunas;
    }
}
