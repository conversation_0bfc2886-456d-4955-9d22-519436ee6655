package negocio.comuns.notaFiscal;

import negocio.comuns.arquitetura.SuperTO;

public class LayoutTelaNotaFiscalTO extends SuperTO {

    private boolean apresentarInutilizar = false;
    private boolean apresentarCancelar = false;
    private boolean apresentarRPS = false;
    private boolean apresentarFiltroTipoNota = false;


    public boolean isApresentarInutilizar() {
        return apresentarInutilizar;
    }

    public void setApresentarInutilizar(boolean apresentarInutilizar) {
        this.apresentarInutilizar = apresentarInutilizar;
    }

    public boolean isApresentarCancelar() {
        return apresentarCancelar;
    }

    public void setApresentarCancelar(boolean apresentarCancelar) {
        this.apresentarCancelar = apresentarCancelar;
    }

    public boolean isApresentarRPS() {
        return apresentarRPS;
    }

    public void setApresentarRPS(boolean apresentarRPS) {
        this.apresentarRPS = apresentarRPS;
    }

    public boolean isApresentarFiltroTipoNota() {
        return apresentarFiltroTipoNota;
    }

    public void setApresentarFiltroTipoNota(boolean apresentarFiltroTipoNota) {
        this.apresentarFiltroTipoNota = apresentarFiltroTipoNota;
    }
}
