package negocio.comuns.notaFiscal;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.nfe.enumerador.TipoNotaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

public class FiltroNotaFiscalTO extends SuperTO {

    private Integer empresa;
    private Integer codNotaFiscal;
    private String numeroNota;
    private String cpfCnpj;
    private String razaoSocial;
    private Date dataEmissaoInicio;
    private Date dataEmissaoFim;
    private Date dataAutorizacaoInicio;
    private Date dataAutorizacaoFim;
    private String statusNota;
    private Integer configuracaoNotaFiscal;
    private String idEmpresaEnotas;
    private String chaveAcesso;
    private String codigoVerificacao;
    private Integer tipoNota;
    private boolean apresentarExcluidas = false;
    private Date dataRegistroInicio;
    private Date dataRegistroFim;
    private boolean apresentarNotaAlteradaStatusManual = false;

    public FiltroNotaFiscalTO() {
    }

    public String getCodigoVerificacao() {
        if (codigoVerificacao == null) {
            codigoVerificacao = "";
        }
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }

    public Date getDataEmissaoInicio() {
        return dataEmissaoInicio;
    }

    public void setDataEmissaoInicio(Date dataEmissaoInicio) {
        this.dataEmissaoInicio = dataEmissaoInicio;
    }

    public Date getDataEmissaoFim() {
        return dataEmissaoFim;
    }

    public void setDataEmissaoFim(Date dataEmissaoFim) {
        this.dataEmissaoFim = dataEmissaoFim;
    }

    public String getCpfCnpjMascarado(boolean somenteNumeros) {
        if (!UteisValidacao.emptyString(getCpfCnpj())) {
            String semMascara = Uteis.removerMascara(getCpfCnpj());
            String retorno = "";
            if (semMascara.length() == 14) {
                retorno = Uteis.aplicarMascara(semMascara, "99.999.999/9999-99");
            } else if (semMascara.length() == 11) {
                retorno = Uteis.aplicarMascara(semMascara, "999.999.999-99");
            }

            if (somenteNumeros) {
                return Uteis.removerMascara(retorno);
            } else {
                return retorno;
            }
        }
        return "";
    }

    public String getCpfCnpj() {
        if (cpfCnpj == null) {
            cpfCnpj = "";
        }
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getRazaoSocial() {
        if (razaoSocial == null) {
            razaoSocial = "";
        }
        return razaoSocial;
    }

    public void setRazaoSocial(String razaoSocial) {
        this.razaoSocial = razaoSocial;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNumeroNota() {
        if (numeroNota == null) {
            numeroNota = "";
        }
        return numeroNota;
    }

    public void setNumeroNota(String numeroNota) {
        this.numeroNota = numeroNota;
    }

    public String getChaveAcesso() {
        if (chaveAcesso == null) {
            chaveAcesso = "";
        }
        return chaveAcesso;
    }

    public void setChaveAcesso(String chaveAcesso) {
        this.chaveAcesso = chaveAcesso;
    }

    public Integer getTipoNota() {
        if (tipoNota == null) {
            tipoNota = TipoNotaFiscalEnum.TODAS.getCodigo();
        }
        return tipoNota;
    }

    public void setTipoNota(Integer tipoNota) {
        this.tipoNota = tipoNota;
    }

    public boolean isApresentarExcluidas() {
        return apresentarExcluidas;
    }

    public void setApresentarExcluidas(boolean apresentarExcluidas) {
        this.apresentarExcluidas = apresentarExcluidas;
    }

    public String getIdEmpresaEnotas() {
        if (idEmpresaEnotas == null) {
            idEmpresaEnotas = "";
        }
        return idEmpresaEnotas;
    }

    public void setIdEmpresaEnotas(String idEmpresaEnotas) {
        this.idEmpresaEnotas = idEmpresaEnotas;
    }

    public String getStatusNota() {
        if (statusNota == null) {
            statusNota = "";
        }
        return statusNota;
    }

    public void setStatusNota(String statusNota) {
        this.statusNota = statusNota;
    }

    public Integer getCodNotaFiscal() {
        return codNotaFiscal;
    }

    public void setCodNotaFiscal(Integer codNotaFiscal) {
        this.codNotaFiscal = codNotaFiscal;
    }

    public Date getDataAutorizacaoInicio() {
        return dataAutorizacaoInicio;
    }

    public void setDataAutorizacaoInicio(Date dataAutorizacaoInicio) {
        this.dataAutorizacaoInicio = dataAutorizacaoInicio;
    }

    public Date getDataAutorizacaoFim() {
        return dataAutorizacaoFim;
    }

    public void setDataAutorizacaoFim(Date dataAutorizacaoFim) {
        this.dataAutorizacaoFim = dataAutorizacaoFim;
    }

    public Integer getConfiguracaoNotaFiscal() {
        if (configuracaoNotaFiscal == null) {
            configuracaoNotaFiscal = 0;
        }
        return configuracaoNotaFiscal;
    }

    public void setConfiguracaoNotaFiscal(Integer configuracaoNotaFiscal) {
        this.configuracaoNotaFiscal = configuracaoNotaFiscal;
    }

    public Date getDataRegistroInicio() {
        return dataRegistroInicio;
    }

    public void setDataRegistroInicio(Date dataRegistroInicio) {
        this.dataRegistroInicio = dataRegistroInicio;
    }

    public Date getDataRegistroFim() {
        return dataRegistroFim;
    }

    public void setDataRegistroFim(Date dataRegistroFim) {
        this.dataRegistroFim = dataRegistroFim;
    }

    public boolean isApresentarNotaAlteradaStatusManual() {
        return apresentarNotaAlteradaStatusManual;
    }

    public void setApresentarNotaAlteradaStatusManual(boolean apresentarNotaAlteradaStatusManual) {
        this.apresentarNotaAlteradaStatusManual = apresentarNotaAlteradaStatusManual;
    }
}
