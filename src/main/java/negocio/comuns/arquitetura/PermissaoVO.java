package negocio.comuns.arquitetura;

import negocio.comuns.utilitarias.ConsistirException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * Reponsável por manter os dados da entidade Permissao. Classe do tipo VO - Value Object 
 * composta pelos atributos da entidade com visibilidade protegida e os métodos de acesso a estes atributos.
 * Classe utilizada para apresentar e manter em memória os dados desta entidade.
 * @see SuperVO
 * @see PerfilAcessoVO
 */
public class PermissaoVO extends SuperVO {

    protected Integer codPerfilAcesso;
    protected String nomeEntidade;
    protected String permissoes;
    protected String tituloApresentacao;
    protected Integer tipoPermissao;
    protected String valorEspecifico;
    protected String valorInicial;
    protected String valorFinal;
    private Boolean selecionado;
    protected String apresentarPermissao;
    private Boolean verificado;
    private String hint = "";


    /**
     * Construtor padrão da classe <code>Permissao</code>.     * Cria uma nova instância desta entidade, inicializando automaticamente seus atributos (Classe VO).
     */
    public PermissaoVO() {
        super();
        inicializarDados();
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe <code>PermissaoVO</code>.
     * Todos os tipos de consistência de dados são e devem ser implementadas neste método.
     * São validações típicas: verificação de campos obrigatórios, verificação de valores válidos para os atributos.
     * @exception ConsistirException Se uma inconsistência for encontrada aumaticamente é gerada uma exceção descrevendo
     *                               o atributo e o erro ocorrido.
     */
    public static void validarDados(PermissaoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getNomeEntidade().equals("")) {
            throw new ConsistirException("O campo NOME DA AÇÃO (Permissao) deve ser informado.");
        }
        if (obj.getPermissoes().equals("")) {
            throw new ConsistirException("O campo TIPO ACESSO (Permissao) deve ser informado.");
        }
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo String.
     */
//    public void realizarUpperCaseDados() {
//        setPermissoes( permissoes.toUpperCase() );
//        setTituloApresentacao( tituloApresentacao.toUpperCase() );
//        setValorEspecifico( valorEspecifico.toUpperCase() );
//        setValorInicial( valorInicial.toUpperCase() );
//        setValorFinal( valorFinal.toUpperCase() );
//    }
    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
        setNomeEntidade("");
        setPermissoes("");
        setTituloApresentacao("");
        setTipoPermissao(0);
        setValorEspecifico("");
        setValorInicial("");
        setValorFinal("");
        setCodPerfilAcesso(0);
        setApresentarPermissao("");
    }
    
    
    public static Boolean existePermisao(List<PermissaoVO>listaPermisao, String entidade){
    	Iterator i = listaPermisao.iterator();
    	while (i.hasNext()) {
			PermissaoVO permisao = (PermissaoVO) i.next();
			if(permisao.getNomeEntidade().equals(entidade)){
	    		return true;
	    	}
		}
    	return false;
    }

    public String getValorFinal() {
        if (valorFinal == null) {
            valorFinal = "";
        }

        return (valorFinal);
    }

    public void setValorFinal(String valorFinal) {
        this.valorFinal = valorFinal;
    }

    public String getValorInicial() {
        if (valorInicial == null) {
            valorInicial = "";
        }

        return (valorInicial);
    }

    public void setValorInicial(String valorInicial) {
        this.valorInicial = valorInicial;
    }

    public String getValorEspecifico() {
        if (valorEspecifico == null) {
            valorEspecifico = "";
        }
        return (valorEspecifico);
    }

    public void setValorEspecifico(String valorEspecifico) {
        this.valorEspecifico = valorEspecifico;
    }

    public Integer getTipoPermissao() {
        return (tipoPermissao);
    }

    public void setTipoPermissao(Integer tipoPermissao) {
        this.tipoPermissao = tipoPermissao;
    }

    public String getTituloApresentacao() {
        if (tituloApresentacao == null) {
            tituloApresentacao = "";
        }
        return (tituloApresentacao);
    }

    public void setTituloApresentacao(String tituloApresentacao) {
        this.tituloApresentacao = tituloApresentacao;
    }

    public String getPermissoes() {
        if (permissoes == null) {
            permissoes = "";
        }
        return (permissoes);
    }

    /**
     * Operação responsável por retornar o valor de apresentação de um atributo com um domínio específico. 
     * Com base no valor de armazenamento do atributo esta função é capaz de retornar o 
     * de apresentação correspondente. Útil para campos como sexo, escolaridade, etc. 
     */
    public String getPermissoes_Apresentar() {
        if (permissoes == null) {
            permissoes = "";
        }
        if (permissoes.equals("(0)(9)(1)")) {
            return "Incluir e Consultar";
        }
        if (permissoes.equals("(0)")) {
            return "Consultar";
        }
        if (permissoes.equals("(12)")) {
            return "Relatorio";
        }
        if (permissoes.equals("(2)")) {
            return "Alterar";
        }
        if (permissoes.equals("(0)(1)(2)(9)(12)")) {
            return "Total (Sem Excluir)";
        }
        if (permissoes.equals("(0)(1)(2)(3)(9)(12)")) {
            return "Total";
        }
        if (permissoes.equals("(3)")) {
            return "Excluir";
        }
        if (permissoes.equals("(1)(9)")) {
            return "Incluir";
        }
        return (permissoes);
    }

    public String getPermissoes_API() {
        if (permissoes == null) {
            permissoes = "";
        }
        if (permissoes.equals("(0)")
                || permissoes.equals("(12)")) {
            return "CONSULTAR";
        }
        if (permissoes.equals("(2)")) {
            return "EDITAR";
        }
        if (permissoes.equals("(0)(1)(2)(9)(12)")) {
            return "TOTAL_EXCETO_EXCLUIR";
        }
        if (permissoes.equals("(0)(1)(2)(3)(9)(12)")) {
            return "TOTAL";
        }
        if (permissoes.equals("(3)")) {
            return "EXCLUIR";
        }
        if (permissoes.equals("(1)(9)")) {
            return "INCLUIR";
        }
        if (permissoes.equals("(0)(9)(1)")) {
            return "INCLUIR_CONSULTAR";
        }
        return (permissoes);
    }

    public void setPermissoes(String permissoes) {
        this.permissoes = permissoes;
    }

    public String getNomeEntidade() {
        if (nomeEntidade == null) {
            nomeEntidade = "";
        }
        return (nomeEntidade);
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public Integer getCodPerfilAcesso() {
        if (codPerfilAcesso == null) {
            codPerfilAcesso = 0;
        }
        return (codPerfilAcesso);
    }

    public void setCodPerfilAcesso(Integer codPerfilAcesso) {
        this.codPerfilAcesso = codPerfilAcesso;
    }

	/**
	 * @param selecionado the selecionado to set
	 */
	public void setSelecionado(Boolean selecionado) {
		this.selecionado = selecionado;
	}

	/**
	 * @return the selecionado
	 */
	public Boolean getSelecionado() {
		if(selecionado == null){
			selecionado = Boolean.FALSE;
		}
		return selecionado;
	}

    public String getApresentarPermissao() {
        if (apresentarPermissao == null) {
            apresentarPermissao = "";
        }
        return apresentarPermissao;
    }

    public Integer getCodigo() {
        return (codigo);
    }

    public void setApresentarPermissao(String apresentarPermissao) {
        this.apresentarPermissao = apresentarPermissao;
    }


    public Boolean isVerificado() {
        if (verificado == null) {
            verificado = false;
        }
        return verificado;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public void setVerificado(Boolean verificado) {
        this.verificado = verificado;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PermissaoVO that = (PermissaoVO) o;
        return Objects.equals(codPerfilAcesso, that.codPerfilAcesso) &&
                Objects.equals(nomeEntidade, that.nomeEntidade) &&
                Objects.equals(permissoes, that.permissoes) &&
                Objects.equals(tituloApresentacao, that.tituloApresentacao) &&
                Objects.equals(tipoPermissao, that.tipoPermissao) &&
                Objects.equals(valorEspecifico, that.valorEspecifico) &&
                Objects.equals(valorInicial, that.valorInicial) &&
                Objects.equals(valorFinal, that.valorFinal);
    }

    @Override
    public int hashCode() {
        return Objects.hash(codPerfilAcesso, nomeEntidade, permissoes, tituloApresentacao, tipoPermissao, valorEspecifico, valorInicial, valorFinal);
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        json.put("tipoPermissao", this.getTipoPermissao());
        json.put("verificado", this.isVerificado());

        return json;
    }
}
