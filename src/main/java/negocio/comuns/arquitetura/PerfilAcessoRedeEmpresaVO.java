package negocio.comuns.arquitetura;

import annotations.arquitetura.ChavePrimaria;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class PerfilAcessoRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer perfilAcesso;
    private String chaveOrigem;
    private String chaveDestino;
    private Date datacadastro;
    private Date dataatualizacao;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;
    private Integer perfilAcessoReplicado;
    private boolean selecionado;

    public PerfilAcessoRedeEmpresaVO(Integer perfilAcesso, String chaveOrigem, String chaveDestino) {
        this.perfilAcesso = perfilAcesso;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
    }

    public PerfilAcessoRedeEmpresaVO(String nomeUnidade, Integer perfilAcesso, String chaveOrigem, String chaveDestino) {
        this.nomeUnidade = nomeUnidade;
        this.perfilAcesso = perfilAcesso;
        this.chaveOrigem = chaveOrigem;
        this.chaveDestino = chaveDestino;
    }

    public PerfilAcessoRedeEmpresaVO() {
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getPerfilAcesso() {
        return perfilAcesso;
    }

    public void setPerfilAcesso(Integer perfilAcesso) {
        this.perfilAcesso = perfilAcesso;
    }

    public String getChaveOrigem() {
        return chaveOrigem;
    }

    public void setChaveOrigem(String chaveOrigem) {
        this.chaveOrigem = chaveOrigem;
    }

    public String getChaveDestino() {
        return chaveDestino;
    }

    public void setChaveDestino(String chaveDestino) {
        this.chaveDestino = chaveDestino;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataatualizacao() != null;
    }
    public Date getDataatualizacao() {
        return dataatualizacao;
    }

    public void setDataatualizacao(Date dataatualizacao) {
        this.dataatualizacao = dataatualizacao;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }

    public Integer getPerfilAcessoReplicado() {
        return perfilAcessoReplicado;
    }

    public void setPerfilAcessoReplicado(Integer perfilAcessoReplicado) {
        this.perfilAcessoReplicado = perfilAcessoReplicado;
    }

    public boolean isSelecionado() {
        return selecionado;
    }

    public void setSelecionado(boolean selecionado) {
        this.selecionado = selecionado;
    }
}
