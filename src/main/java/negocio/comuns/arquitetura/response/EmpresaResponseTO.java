package negocio.comuns.arquitetura.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.LocaleEnum;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaResponseTO {

    private Integer id;
    private String nome;
    private String siglaNovaPlataforma;

    public EmpresaResponseTO(EmpresaVO empresa) {
        this.id = empresa.getCodigo();
        this.nome = empresa.getNome();
        this.siglaNovaPlataforma = LocaleEnum.obterLocale(empresa.getLocaleTexto()).getSiglaNovaPlataforma();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSiglaNovaPlataforma() {
        return siglaNovaPlataforma;
    }

    public void setSiglaNovaPlataforma(String siglaNovaPlataforma) {
        this.siglaNovaPlataforma = siglaNovaPlataforma;
    }
}
