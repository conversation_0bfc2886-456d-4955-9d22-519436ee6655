package negocio.comuns.arquitetura;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

public class LogProcessoSistemaVO extends SuperVO {

    private Integer codigo;
    private String processo;
    private Date dataInicio;
    private Date dataFinal;
    private UsuarioVO usuario;
    private EmpresaVO empresa;
    private String usuariooamd;

    public LogProcessoSistemaVO(String processo, UsuarioVO usuario, EmpresaVO empresa) {
        this.processo = processo;
        this.dataInicio = Calendario.hoje();
        this.empresa = empresa;
        this.usuario = usuario;
        if (!UteisValidacao.emptyString(usuario.getUserOamd())) {
            this.usuariooamd = usuario.getUserOamd();
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getProcesso() {
        return processo;
    }

    public void setProcesso(String processo) {
        this.processo = processo;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFinal() {
        return dataFinal;
    }

    public void setDataFinal(Date dataFinal) {
        this.dataFinal = dataFinal;
    }

    public EmpresaVO getEmpresa() {
        if (empresa == null) {
            empresa = new EmpresaVO();
        }
        return empresa;
    }

    public void setEmpresa(EmpresaVO empresa) {
        this.empresa = empresa;
    }

    public UsuarioVO getUsuario() {
        if (usuario == null) {
            usuario = new UsuarioVO();
        }
        return usuario;
    }

    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    public String getUsuariooamd() {
        return usuariooamd;
    }

    public void setUsuariooamd(String usuariooamd) {
        this.usuariooamd = usuariooamd;
    }
}
