package negocio.comuns.arquitetura;

import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 30/10/2024
 */

public class DetalhesRequestRecebidaVO extends SuperVO {

    private Integer codigo;
    private Date dataRegistro;
    private String url;
    private String body;
    private String userAgent;
    private String ip;

    public DetalhesRequestRecebidaVO() {
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getBody() {
        if (UteisValidacao.emptyString(body)) {
            return "";
        }
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getUserAgent() {
        if (UteisValidacao.emptyString(userAgent)) {
            return "";
        }
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getIp() {
        if (UteisValidacao.emptyString(ip)) {
            return "";
        }
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
