package negocio.comuns.arquitetura;


public class ResultadoServicosItensVO extends SuperVO {

    private Integer codigo;
    private String descricao;
    private Integer resultadoServicos;

    public ResultadoServicosItensVO() {
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getResultadoServicos() {
        if (resultadoServicos == null) {
            resultadoServicos = 0;
        }
        return resultadoServicos;
    }

    public void setResultadoServicos(Integer resultadoServicos) {
        this.resultadoServicos = resultadoServicos;
    }
}
