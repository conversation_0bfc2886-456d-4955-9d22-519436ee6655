package negocio.comuns.crm;

import annotations.arquitetura.ChavePrimaria;
import negocio.comuns.arquitetura.SuperVO;
import servicos.discovery.RedeDTO;

import java.util.Date;

public class FeriadoRedeEmpresaVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    private Integer feriado;
    private String chave;
    private Date datacadastro;
    private Date dataatualizacao;
    private Integer feriadoReplicado;
    private String nomeUnidade;
    private String mensagemSituacao;
    private RedeDTO redeDTO;

    public FeriadoRedeEmpresaVO(Integer plano, String chave, Integer feriadoReplicado) {
        this.feriado = plano;
        this.chave = chave;
        this.feriadoReplicado = feriadoReplicado;
    }

    public FeriadoRedeEmpresaVO(String nomeUnidade, Integer plano, String chave, Integer feriadoReplicado) {
        this.nomeUnidade = nomeUnidade;
        this.feriado = plano;
        this.chave = chave;
        this.feriadoReplicado = feriadoReplicado;
    }

    public FeriadoRedeEmpresaVO() {
        
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getFeriado() {
        return feriado;
    }

    public void setFeriado(Integer feriado) {
        this.feriado = feriado;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getDatacadastro() {
        return datacadastro;
    }

    public void setDatacadastro(Date datacadastro) {
        this.datacadastro = datacadastro;
    }

    public Boolean getDataAtualizacaoInformada() {
        return getDataatualizacao() != null;
    }
    public Date getDataatualizacao() {
        return dataatualizacao;
    }

    public void setDataatualizacao(Date dataatualizacao) {
        this.dataatualizacao = dataatualizacao;
    }

    public Integer getFeriadoReplicado() {
        return feriadoReplicado;
    }

    public void setFeriadoReplicado(Integer feriadoReplicado) {
        this.feriadoReplicado = feriadoReplicado;
    }

    public String getNomeUnidade() {
        return nomeUnidade;
    }

    public void setNomeUnidade(String nomeUnidade) {
        this.nomeUnidade = nomeUnidade;
    }

    public String getMensagemSituacao() {
        return mensagemSituacao;
    }

    public void setMensagemSituacao(String mensagemSituacao) {
        this.mensagemSituacao = mensagemSituacao;
    }

    public RedeDTO getRedeDTO() {
        return redeDTO;
    }

    public void setRedeDTO(RedeDTO redeDTO) {
        this.redeDTO = redeDTO;
    }
}
