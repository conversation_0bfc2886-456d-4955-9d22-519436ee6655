package negocio.comuns.crm;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.conviteaulaexperimental.ConviteAulaExperimentalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.DiasDaSemana;
import negocio.comuns.utilitarias.Dominios;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import javax.faces.model.SelectItem;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Reponsável por manter os dados da entidade HistoricoContato. Classe do tipo
 * VO - Value Object composta pelos atributos da entidade com visibilidade
 * protegida e os métodos de acesso a estes atributos. Classe utilizada para
 * apresentar e manter em memória os dados desta entidade.
 *
 * @see SuperVO
 */
public class HistoricoContatoVO extends SuperVO {

    @ChavePrimaria
    protected Integer codigo;
    protected Date dia;
    protected String observacao;
    protected String tipoOperacao;
    protected String tipoContato;
    @NaoControlarLogAlteracao
    protected MalaDiretaVO malaDiretaVO;
    //
    protected Integer codigoNotificacao;
    protected String opcoes;
    protected String resposta;
    protected ConviteAulaExperimentalVO conviteAulaExperimentalVO;
    protected int fluxoGymBot;
    protected TipoGymBotEnum tipoGymBotEnum;
    /**
     * Representa a partir de qual data o email pode ser enviado novamente.
     */
    private Date dataProximoEnvio;

    public boolean getApp() {
        try {
            return tipoContato.equals("AP");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Colaborador </code>.
     */
    protected UsuarioVO responsavelCadastro;
    @NaoControlarLogAlteracao
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Agenda </code>.
     */
    protected AgendaVO agendaVO;
    @NaoControlarLogAlteracao
    protected AgendaVO reagendamentoVO;
    @NaoControlarLogAlteracao
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Objecao </code>.
     */
    protected ObjecaoVO objecaoVO;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Colaborador </code>.
     */
    protected UsuarioVO colaboradorResponsavel;
    // atributo criado para que na hora de gravar um historico manter atualizado
    // o ultimo historico para esse fecharMetaDetalhado
    protected Integer codigoFecharMetaDetalhado;
    @NaoControlarLogAlteracao
    private ClienteVO clienteVO;
    @NaoControlarLogAlteracao
    private PassivoVO passivoVO;
    @NaoControlarLogAlteracao
    private IndicadoVO indicadoVO;
    private String grauSatisfacao;
    private String fase;
    private String resultado;
    /**
     * Atributo responsável por manter o objeto relacionado da classe
     * <code>Agenda </code>.
     */
    // Os atributos abaixo não serão gravados no Banco de dados é somente para
    // controlar informações
    private Long totalLigacao;
    private Date diaAbertura;
    private Boolean aberturaMetaEstaEmAberta;
    // Quantidade de dias que a pessoa esta cadastrada
    private Long diasCadastrado;
    private Long ligacoes;
    private Date diasUltAcesso;
    private Date vencimentoContrato;
    private Integer idade;
    private Boolean escolherEmail;
    private Long qtdEmail;
    private Long qtdSMS;
    private Long qtdPessoal;
    private String faseInicio;
    private String faseAtual;
    @NaoControlarLogAlteracao
    private int diasDesdeUltAgendamento;
    @NaoControlarLogAlteracao
    private int sessoesFinais;
    private Boolean contatoAvulso;
    private Integer qtdAPP;
    private boolean wagienvi;
    private String origem;
    private Integer origemCodigo;

    /**
     * Construtor padrão da classe <code>HistoricoContato</code>. Cria uma nova
     * instância desta entidade, inicializando automaticamente seus atributos
     * (Classe VO).
     */
    public HistoricoContatoVO() {
        super();
        setContatoAvulso(Boolean.FALSE);
        inicializarDados();
    }

    /**
     * Operação responsável por validar a unicidade dos dados de um objeto da
     * classe <code>HistoricoContatoVO</code>.
     */
    public static void validarUnicidade(List<HistoricoContatoVO> lista, HistoricoContatoVO obj) throws ConsistirException {
        for (HistoricoContatoVO repetido : lista) {
        }
    }

    /**
     * Operação responsável por validar os dados de um objeto da classe
     * <code>HistoricoContatoVO</code>. Todos os tipos de consistência de dados
     * são e devem ser implementadas neste método. São validações típicas:
     * verificação de campos obrigatórios, verificação de valores válidos para
     * os atributos.
     *
     * @throws ConsistirException Se uma inconsistência for encontrada aumaticamente é
     *                            gerada uma exceção descrevendo o atributo e o erro
     *                            ocorrido.
     */
    public static void validarDados(HistoricoContatoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }
        if (obj.getObservacao().equals("") && !obj.getTipoContato().equals("EM")) {
            throw new ConsistirException("O campo comentário está vazio !");
        }
    }

    public String getResponsavelApresentar() {
        String nome = "";
        if (getResponsavelCadastro().getNome().length() > 15) {
            nome = getResponsavelCadastro().getNome().substring(0, 15);
        } else {
            nome = getResponsavelCadastro().getNome();
        }
        return nome;
    }

    /**
     * Operação reponsável por realizar o UpperCase dos atributos do tipo
     * String.
     */
    public void realizarUpperCaseDados() {
        if (!Uteis.realizarUpperCaseDadosAntesPersistencia) {
            return;
        }
    }

    /**
     * Operação reponsável por inicializar os atributos da classe.
     */
    public void inicializarDados() {
    }

    /**
     * Método responsável por alinhar os outputs caso o campo esteja sem nenhum
     * dado informado.
     *
     * @return
     */
    public Boolean getAlinharOutputs() {
        return true;
    }

    /**
     * Método Responsável por alinhar os outputs caso o campo estadoCivil esteja
     * sem nenhum dado informado.
     *
     * @return
     */
    public Boolean getAlinharOutputEstadoCivil() {
        return getClienteVO().getPessoa().getEstadoCivil().equals("");
    }

    /**
     * Método Responsável por alinhar os outputs caso o campo DiasUltAcesso
     * esteja sem nenhum dado informado.
     *
     * @return
     */
    public Boolean getAlinharOutputDiasUltAcesso() {
        return getDiasUltAcesso() == null;
    }

    /**
     * Método responsável por apresentar os dois botoes Agendar e Objecao eles
     * só vão aparecer se a situação de contato for igual a CO(Contato) e o
     * campo de comentario sempre ira aparecer.
     *
     * @return
     */
    public Boolean getApresentarComentario() {
        if (getTipoContato().equals("EM")) {
            return false;
        } else {
            return true;
        }
    }

    public Boolean getApresentarTipoOperacao() {
        return getTipoOperacao().equals("RE");
    }

    public Date getDia() {
        if (dia == null) {
            dia = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return (dia);
    }

    // /**
    // * Operação responsável por retornar um atributo do tipo data no formato
    // * padrão dd/mm/aaaa.
    // */
    // public String getDia_Apresentar() {
    // return (Uteis.getDataComHora(dia));
    // }
    //

    public void setDia(Date dia) {
        this.dia = dia;
    }

    /**
     * Operação responsável por retornar um atributo do tipo data no formato
     * padrão dd/mm/aaaa.
     *
     * @throws Exception
     */
    public String getDia_Apresentar() throws Exception {
        if(dia!=null) {
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.DOMINGO)) {
                return "Dom, " + (Uteis.getDataComHora(dia)) + "";
            }
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.SEGUNDA)) {
                return "Seg, " + (Uteis.getDataComHora(dia)) + "";
            }
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.TERCA)) {
                return "Ter, " + (Uteis.getDataComHora(dia)) + "";
            }
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.QUARTA)) {
                return "Qua, " + (Uteis.getDataComHora(dia)) + "";
            }
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.QUINTA)) {
                return "Qui, " + (Uteis.getDataComHora(dia)) + "";
            }
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.SEXTA)) {
                return "Sex, " + (Uteis.getDataComHora(dia)) + "";
            }
            if (Uteis.getDiaDaSemana(dia, DiasDaSemana.SABADO)) {
                return "Sab, " + (Uteis.getDataComHora(dia)) + "";
            }
            return (Uteis.getDataComHora(dia));
        }
        return "";
    }

    /**
     * Método responsavel por criar a combo Situacao contato
     *
     * @throws Exception
     */
    public List<SelectItem> getTipoConsultaComboCliente() throws Exception {
        List<SelectItem> itens = new ArrayList<SelectItem>();
        itens.add(new SelectItem("", ""));
        itens.add(new SelectItem("CO", "Contato"));
        itens.add(new SelectItem("OC", "Ocupado"));
        itens.add(new SelectItem("NE", "Não Estava"));
        itens.add(new SelectItem("FI", "Fone Incorreto"));
        return itens;
    }

    public String qualResultado(Boolean reagendamento, String AgendaOrObjecaoOrSimpleRegistro) {
        if (reagendamento) {
            return "Reagendamento para dia: " + getAgendaVO().getDataAgendamentoComparecimento_Apresentar();
        }
        if (AgendaOrObjecaoOrSimpleRegistro.equals("AG")) {
            return getAgendaVO().qualResultadoAgendamento();
        }
        if (AgendaOrObjecaoOrSimpleRegistro.equals("LA")) {
            return getAgendaVO().qualResultadoAgendamento();
        }
        if (AgendaOrObjecaoOrSimpleRegistro.equals("OB")) {
                return getObjecaoVO().qualResultadoObjecao();
        }
        if (AgendaOrObjecaoOrSimpleRegistro.equals("SR")) {
            if (getTipoContato().equals("CS")) {
                return "Envio de SMS";
            } else if (getTipoContato().equals("AP")) {    
                return "Envio APP";
            } else if (getTipoContato().equals("WH")) {
                return "Envio WhatsApp";
            }else{
                return "Simples Registro";
            }

        }
        return "";

    }

    /**
     * @return the codigoFecharMetaDetalhado
     */
    public Integer getCodigoFecharMetaDetalhado() {
        if (codigoFecharMetaDetalhado == null) {
            codigoFecharMetaDetalhado = 0;
        }
        return codigoFecharMetaDetalhado;
    }

    /**
     * @param codigoFecharMetaDetalhado the codigoFecharMetaDetalhado to set
     */
    public void setCodigoFecharMetaDetalhado(Integer codigoFecharMetaDetalhado) {
        this.codigoFecharMetaDetalhado = codigoFecharMetaDetalhado;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return (codigo);
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the clienteVO
     */
    public ClienteVO getClienteVO() {
        if (clienteVO == null) {
            clienteVO = new ClienteVO();
        }
        return clienteVO;
    }

    /**
     * @param clienteVO the clienteVO to set
     */
    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    /**
     * @return the passivoVO
     */
    public PassivoVO getPassivoVO() {
        if (passivoVO == null) {
            passivoVO = new PassivoVO();
        }
        return passivoVO;
    }

    /**
     * @param passivoVO the passivoVO to set
     */
    public void setPassivoVO(PassivoVO passivoVO) {
        this.passivoVO = passivoVO;
    }

    /**
     * @return the totalLigacao
     */
    public Long getTotalLigacao() {
        if (totalLigacao == null) {
            totalLigacao = 0l;
        }
        return totalLigacao;
    }

    /**
     * @param totalLigacao the totalLigacao to set
     */
    public void setTotalLigacao(Long totalLigacao) {
        this.totalLigacao = totalLigacao;
    }

    /**
     * @return the diasCadastrado
     */
    public Long getDiasCadastrado() {
        if (diasCadastrado == null) {
            diasCadastrado = 0l;
        }
        return diasCadastrado;
    }

    /**
     * @param diasCadastrado the diasCadastrado to set
     */
    public void setDiasCadastrado(Long diasCadastrado) {
        this.diasCadastrado = diasCadastrado;
    }

    /**
     * @return the ligacoes
     */
    public Long getLigacoes() {
        if (ligacoes == null) {
            ligacoes = 0l;
        }
        return ligacoes;
    }

    /**
     * @param ligacoes the ligacoes to set
     */
    public void setLigacoes(Long ligacoes) {
        this.ligacoes = ligacoes;
    }

    /**
     * @return the diasUltAcesso
     */
    public Date getDiasUltAcesso() {
//		if (diasUltAcesso == null) {
//			diasUltAcesso = negocio.comuns.utilitarias.Calendario.hoje();
//		}
        return diasUltAcesso;
    }

    /**
     * @param diasUltAcesso the diasUltAcesso to set
     */
    public void setDiasUltAcesso(Date diasUltAcesso) {
        this.diasUltAcesso = diasUltAcesso;
    }

    public String getDiasUltAcesso_Apresentar() {
        return (Uteis.getData(diasUltAcesso));
    }

    public String getNrDiasUltimoAcesso() {
        if (diasUltAcesso == null) {
            return "";
        } else {
            long dias = Uteis.nrDiasEntreDatas(getDiasUltAcesso(), negocio.comuns.utilitarias.Calendario.hoje());
            return String.valueOf(dias);
        }
    }

    /**
     * @return the idade
     */
    public Integer getIdade() {
        if (idade == null) {
            idade = 0;
        }
        return idade;
    }

    /**
     * @param idade the idade to set
     */
    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public Boolean getEscolherEmail() {
        if (escolherEmail == null) {
            escolherEmail = true;
        }
        return escolherEmail;
    }

    public void setEscolherEmail(Boolean escolherEmail) {
        this.escolherEmail = escolherEmail;
    }

    public IndicadoVO getIndicadoVO() {
        if (indicadoVO == null) {
            indicadoVO = new IndicadoVO();
        }
        return indicadoVO;
    }

    public void setIndicadoVO(IndicadoVO indicadoVO) {
        this.indicadoVO = indicadoVO;
    }

    public Long getQtdEmail() {
        if (qtdEmail == null) {
            qtdEmail = 0l;
        }
        return qtdEmail;
    }

    public void setQtdEmail(Long qtdEmail) {
        this.qtdEmail = qtdEmail;
    }

    public Long getQtdPessoal() {
        if (qtdPessoal == null) {
            qtdPessoal = 0l;
        }
        return qtdPessoal;
    }

    public void setQtdPessoal(Long qtdPessoal) {
        this.qtdPessoal = qtdPessoal;
    }

    public Boolean getApresentarBotaoIndicao() {
        return getClienteVO().getCodigo() != 0;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    /**
     * @return the MalaDiretaVO
     */
    public MalaDiretaVO getMalaDiretaVO() {
        if (malaDiretaVO == null) {
            malaDiretaVO = new MalaDiretaVO();
        }
        return malaDiretaVO;
    }

    /**
     * @param malaDiretaVO the malaDiretaVO to set
     */
    public void setMalaDiretaVO(MalaDiretaVO malaDiretaVO) {
        this.malaDiretaVO = malaDiretaVO;
    }

    public String getTipoOperacao_Apresentar() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return Dominios.getTipoOperacaoHistoricoContato().get(tipoOperacao);
    }

    public String getTipoOperacao() {
        if (tipoOperacao == null) {
            tipoOperacao = "";
        }
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public UsuarioVO getResponsavelCadastro() {
        if (responsavelCadastro == null) {
            responsavelCadastro = new UsuarioVO();
        }
        return responsavelCadastro;
    }

    public void setResponsavelCadastro(UsuarioVO responsavelCadastro) {
        this.responsavelCadastro = responsavelCadastro;
    }

    public AgendaVO getAgendaVO() {
        if (agendaVO == null) {
            agendaVO = new AgendaVO();
        }
        return agendaVO;
    }

    public void setAgendaVO(AgendaVO agendaVO) {
        this.agendaVO = agendaVO;
    }

    public ObjecaoVO getObjecaoVO() {
        if (objecaoVO == null) {
            objecaoVO = new ObjecaoVO();
        }
        return objecaoVO;
    }

    public void setObjecaoVO(ObjecaoVO objecaoVO) {
        this.objecaoVO = objecaoVO;
    }

    public String getFase() {
        if (fase == null) {
            fase = "";
        }
        return fase;
    }

    public void setFase(String fase) {
        this.fase = fase;
    }

    public String getFase_Apresentar() {
        return getNomeFase(fase);
    }

    public String getResultado() {
        if (resultado == null) {
            resultado = "";
        }

        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    /**
     * @return the colaboradorResponsavel
     */
    public UsuarioVO getColaboradorResponsavel() {
        if (colaboradorResponsavel == null) {
            colaboradorResponsavel = new UsuarioVO();
        }
        return colaboradorResponsavel;
    }

    /**
     * @param colaboradorResponsavel the colaboradorResponsavel to set
     */
    public void setColaboradorResponsavel(UsuarioVO colaboradorResponsavel) {
        this.colaboradorResponsavel = colaboradorResponsavel;
    }

    public Boolean getHistoricoPassivo() {
        return getPassivoVO().getCodigo() != 0;
    }

    public Boolean getHistoricoCliente() {
        return getClienteVO().getCodigo() != 0;
    }

    public Boolean getHistoricoIndicado() {
        return getIndicadoVO().getCodigo() != 0;
    }

    public String getFaseAtual() {
        return getNomeFase(faseAtual);
    }

    public void setFaseAtual(String faseAtual) {
        this.faseAtual = faseAtual;
    }

    public String getFaseInicio() {
        return getNomeFase(faseInicio);
    }

    public void setFaseInicio(String faseInicio) {
        this.faseInicio = faseInicio;
    }

    public String getTipoContato_Apresentar() {
        if (tipoContato == null) {
            tipoContato = "";
        }
        return Dominios.getTipoContatoHistoricoContato().get(tipoContato);
    }

    public String getTipoContato() {
        if (tipoContato == null) {
            tipoContato = "";
        }
        return tipoContato;
    }

    public void setTipoContato(String tipoContato) {
        this.tipoContato = tipoContato;
    }

    public AgendaVO getReagendamentoVO() {
        if (reagendamentoVO == null) {
            reagendamentoVO = new AgendaVO();
        }
        return reagendamentoVO;
    }

    public void setReagendamentoVO(AgendaVO reagendamentoVO) {
        this.reagendamentoVO = reagendamentoVO;
    }

    /**
     * @return the diaAbertura
     */
    public Date getDiaAbertura() {
        return diaAbertura;
    }

    /**
     * @param diaAbertura the diaAbertura to set
     */
    public void setDiaAbertura(Date diaAbertura) {
        this.diaAbertura = diaAbertura;
    }

    /**
     * @return the aberturaMetaEstaEmAberta
     */
    public Boolean getAberturaMetaEstaEmAberta() {
        if(aberturaMetaEstaEmAberta == null){
            return false;
        }
        return aberturaMetaEstaEmAberta;
    }

    /**
     * @param aberturaMetaEstaEmAberta the aberturaMetaEstaEmAberta to set
     */
    public void setAberturaMetaEstaEmAberta(Boolean aberturaMetaEstaEmAberta) {
        this.aberturaMetaEstaEmAberta = aberturaMetaEstaEmAberta;
    }

    public List getListaTelefoneClientePorTipoContato() {
        //no futuro, tratar os outros tipos, caso necessite, por enquanto apenas tipo
        //contato por SMS: apresentar apenas telefones celulares
        ArrayList listaTemp = new ArrayList();
        if (getTipoContato().endsWith("CS")) {
            for (TelefoneVO tel : clienteVO.getPessoa().getTelefoneVOs()) {
                if (tel.getTipoTelefone().equals("CE")) {
                    listaTemp.add(tel);
                }
            }
            return listaTemp;
        } else
            return clienteVO.getPessoa().getTelefoneVOs();

    }

    /**
     * @return the qtdSMS
     */
    public Long getQtdSMS() {
        if (qtdSMS == null) {
            qtdSMS = 0l;
        }
        return qtdSMS;
    }

    /**
     * @param qtdSMS the qtdSMS to set
     */
    public void setQtdSMS(Long qtdSMS) {
        this.qtdSMS = qtdSMS;
    }

    /**
     * @return the vencimentoContrato
     */
    public Date getVencimentoContrato() {
        return vencimentoContrato;
    }

    /**
     * @param vencimentoContrato the vencimentoContrato to set
     */
    public void setVencimentoContrato(Date vencimentoContrato) {
        this.vencimentoContrato = vencimentoContrato;
    }

    public long getDiasVencido() {
        long diasVencido = Uteis.nrDiasEntreDatas(vencimentoContrato, Calendario.hoje());
        return diasVencido > 0 ? diasVencido : 0;
    }

    public String getVencimentoContrato_Apresentar() {
        return (Uteis.getData(vencimentoContrato));
    }

    public EmpresaVO getEmpresa() {
        if (UteisValidacao.emptyNumber(getPassivoVO().getCodigo())) {
            return getPassivoVO().getEmpresaVO();
        }
        if (UteisValidacao.emptyNumber(getIndicadoVO().getCodigo())) {
            return getIndicadoVO().getEmpresaVO();
        }
        return getClienteVO().getEmpresa();
    }

    public String getGrauSatisfacao() {
        if(grauSatisfacao == null){
            grauSatisfacao =  "";
        }
        return grauSatisfacao;
    }

    public void setGrauSatisfacao(String grauSatisfacao) {
        this.grauSatisfacao = grauSatisfacao;
    }

    public String getResumoObservacao() {
        String resumo = Uteis.retiraTags(this.getObservacao(), true);
        if (resumo.length() > 30) {
            return resumo.substring(0, 30);
        }
        return resumo;
    }

    public int getDiasDesdeUltAgendamento() {
        return diasDesdeUltAgendamento;
    }

    public void setDiasDesdeUltAgendamento(int diasDesdeUltAgendamento) {
        this.diasDesdeUltAgendamento = diasDesdeUltAgendamento;
    }

    public int getSessoesFinais() {
        return sessoesFinais;
    }

    public void setSessoesFinais(int sessoesFinais) {
        this.sessoesFinais = sessoesFinais;
    }

    public String getMatricula_Apresentar() {
        return getClienteVO().getMatricula();
    }

    public Integer getCodigoMalaDireta() {
        return getMalaDiretaVO().getCodigo();
    }

    public String getMensagemMalaDireta() {
        return getMalaDiretaVO().getMensagem();
    }

    public String getNome_Apresentar() {
        if (clienteVO != null && getClienteVO().getCodigo() != 0) {
            return getClienteVO().getPessoa().getNome();
        } else if (passivoVO != null && getPassivoVO().getCodigo() != 0) {
            return getPassivoVO().getNome();
        } else {
            return getIndicadoVO().getNomeIndicado();
        }
    }

    public String getNomeAbreviado(){
        try {
            return Uteis.getNomeAbreviado(getNome_Apresentar());
        }catch (Exception e){
            return "";
        }
    }

    public Boolean getContatoAvulso() {
        return contatoAvulso;
    }

    public void setContatoAvulso(Boolean contatoavulso) {
        this.contatoAvulso = contatoavulso;
    }

    public String getObservacao_Exportar() {
        return Uteis.retiraTags(Uteis.trocarAcentuacaoHTMLPorAcentuacao(getObservacao()), false);
    }

    public Integer getCodigoNotificacao() {
        return codigoNotificacao;
    }

    public void setCodigoNotificacao(Integer codigoNotificacao) {
        this.codigoNotificacao = codigoNotificacao;
    }

    public String getOpcoes() {
        return opcoes;
    }

    public void setOpcoes(String opcoes) {
        this.opcoes = opcoes;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }

    public Date getDataProximoEnvio() {
        return dataProximoEnvio;
    }

    public void setDataProximoEnvio(Date dataProximoEnvio) {
        this.dataProximoEnvio = dataProximoEnvio;
    }

    public String getDiaApresentar() {
        try {
            return Uteis.getDataAplicandoFormatacao(dia, "dd/MM/yy HH:mm");
        } catch (Exception e) {
            return "";
        }
    }

    public String getNomeApresentar() {
        return getClienteVO().getPessoa().getNome();
    }

    public Integer getQtdAPP() {
        if (qtdAPP == null) {
            qtdAPP = 0;
        }
        return qtdAPP;
    }

    public void setQtdAPP(Integer qtdAPP) {
        this.qtdAPP = qtdAPP;
    }

    public String getDiaAbertura_Apresentar() {
        try {
            return Uteis.getData(diaAbertura);
        } catch (Exception e) {
            return "";
        }
    }

    public String getDiaUltimoAcessoComHora_Apresentar() {
        String diaUltimoAcesso = "";
        if (diasUltAcesso != null) {
            diaUltimoAcesso = Uteis.getDataComHora(diasUltAcesso);
        }
        return diaUltimoAcesso;
    }

    public String getDataCadastro_Apresentar() {
        String dataCadastro = "";
        if (getClienteVO().getCodigo() != 0) {
            dataCadastro = getClienteVO().getPessoa().getDataCadastro_Apresentar();
        } else if (getPassivoVO().getCodigo() != 0) {
            dataCadastro = getPassivoVO().getDia_Apresentar();
        } else if (getIndicadoVO().getCodigo() != 0) {
            dataCadastro = getIndicadoVO().getIndicacaoVO().getDia_Apresentar();
        }
        return dataCadastro;
    }

    public ConviteAulaExperimentalVO getConviteAulaExperimentalVO() {
        return conviteAulaExperimentalVO;
    }

    public void setConviteAulaExperimentalVO(ConviteAulaExperimentalVO conviteAulaExperimentalVO) {
        this.conviteAulaExperimentalVO = conviteAulaExperimentalVO;
    }

    private String getNomeFase(String fase) {
        if (fase == null) {
            return "";
        }
        if ((fase.equals(" ") || fase.equals("")) && contatoAvulso) {
            return "Contato Avulso";
        }

        FasesCRMEnum fasesCRMEnum = FasesCRMEnum.getFasePorSigla(fase);
        if (fasesCRMEnum != null)  {
            return fasesCRMEnum.getDescricao();
        }
        return "";
    }

    public Integer getNrDiasUltimoAcessoNovoCRM() {
        try{
            if (diasUltAcesso == null) {
                return 0;
            } else {
                long dias = Uteis.nrDiasEntreDatas(getDiasUltAcesso(), getDiaAbertura());
                return ((Number)dias).intValue();
            }
        } catch (Exception ignored) {
        }
        return 0;
    }

    public boolean isWagienvi() {
        return wagienvi;
    }

    public void setWagienvi(boolean wagienvi) {
        this.wagienvi = wagienvi;
    }

    public String getOrigem() {
        if (origem == null) {
            origem = "";
        }
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public Integer getOrigemCodigo() {
        if (origemCodigo == null) {
            origemCodigo = 0;
        }
        return origemCodigo;
    }

    public void setOrigemCodigo(Integer origemCodigo) {
        this.origemCodigo = origemCodigo;
    }

    public int getFluxoGymBot() {
        return fluxoGymBot;
    }

    public void setFluxoGymBot(int fluxoGymBot) {
        this.fluxoGymBot = fluxoGymBot;
    }

    public TipoGymBotEnum getTipoGymBotEnum() {
        return tipoGymBotEnum;
    }

    public void setTipoGymBotEnum(TipoGymBotEnum tipoGymBotEnum) {
        this.tipoGymBotEnum = tipoGymBotEnum;
    }
}
