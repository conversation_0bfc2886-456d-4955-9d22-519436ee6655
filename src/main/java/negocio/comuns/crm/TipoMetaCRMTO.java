package negocio.comuns.crm;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.enumeradores.SituacaoAtualMetaEnum;
import br.com.pactosolucoes.enumeradores.TipoFaseCRM;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 01/09/2015.
 */
public class TipoMetaCRMTO {

    private FasesCRMEnum fasesCRMEnum;
    private List<FecharMetaVO> listaFecharMetaVO = new ArrayList<>();
    private List<FecharMetaDetalhadoVO> listaMetaDetalhada;
    private String styleClass;
    private SituacaoAtualMetaEnum situacaoAtualMetaEnum = SituacaoAtualMetaEnum.META_NAO_ATENDIDA;
    private List<FecharMetaDetalhadoVO> listaMetaDetalhadaFiltrada;
    private Integer totalMeta = 0;
    private Integer totalMetaRealizada = 0;
    private Integer ordemMeta;
    private Integer totalContatoSemBaterMeta;
    private String nomeMeta;
    private List<FecharMetaDetalhadoVO> listaMetaDetalhadaFiltradaSemPaginacao;

    public FasesCRMEnum getFasesCRMEnum() {
        return fasesCRMEnum;
    }

    public void setFasesCRMEnum(FasesCRMEnum fasesCRMEnum) {
        this.fasesCRMEnum = fasesCRMEnum;
    }

    public List<FecharMetaVO> getListaFecharMetaVO() {
        return listaFecharMetaVO;
    }

    public void setListaFecharMetaVO(List<FecharMetaVO> listaFecharMetaVO) {
        this.listaFecharMetaVO = listaFecharMetaVO;
    }

    public Integer getTotalMeta() {
        return totalMeta;
    }

    public void setTotalMeta(Integer totalMeta) {
        this.totalMeta = totalMeta;
    }

    public List<FecharMetaDetalhadoVO> getListaMetaDetalhada() {
        return listaMetaDetalhada;
    }

    public void setListaMetaDetalhada(List<FecharMetaDetalhadoVO> listaMetaDetalhada) {
        this.listaMetaDetalhada = listaMetaDetalhada;
    }

    public String getStyleClass() {
        if (getSituacaoAtualMetaEnum().equals(SituacaoAtualMetaEnum.META_SELECIONADA)) {
            return "metaSelecionada";
        }
        return "metaNaoSelecionada";
    }

    public String getLabelMeta() {
        if (this.fasesCRMEnum.equals(FasesCRMEnum.AGENDAMENTOS_LIGACOES)) {
            return "Agend. Ligações";
        } else if (this.fasesCRMEnum.equals(FasesCRMEnum.AGENDAMENTO)) {
            return "Agend. Presenciais";
        } else if (this.fasesCRMEnum.getTipoFase().equals(TipoFaseCRM.CRMEXTRA)) {
            return getNomeMeta();
        } else {
            return this.fasesCRMEnum.getDescricao();
        }
    }

    public SituacaoAtualMetaEnum getSituacaoAtualMetaEnum() {
        return situacaoAtualMetaEnum;
    }

    public void setSituacaoAtualMetaEnum(SituacaoAtualMetaEnum situacaoAtualMetaEnum) {
        this.situacaoAtualMetaEnum = situacaoAtualMetaEnum;
    }

    @Override
    public int hashCode() {
        return fasesCRMEnum.getCodigo();
    }

    @Override
    public boolean equals(Object obj) {
        if ((obj == null) || (!(obj instanceof TipoMetaCRMTO))) {
            return false;
        }
        return (((TipoMetaCRMTO) obj).getFasesCRMEnum() == this.fasesCRMEnum);
    }


    public List<FecharMetaDetalhadoVO> getListaMetaDetalhadaFiltrada() {
        if (listaMetaDetalhadaFiltrada == null) {
            listaMetaDetalhadaFiltrada = new ArrayList<>();
        }
        return listaMetaDetalhadaFiltrada;
    }

    public void setListaMetaDetalhadaFiltrada(List<FecharMetaDetalhadoVO> listaMetaDetalhadaFiltrada) {
        this.listaMetaDetalhadaFiltrada = listaMetaDetalhadaFiltrada;
    }

    public Integer getTotalMetaRealizada() {
        return totalMetaRealizada;
    }

    public void setTotalMetaRealizada(Integer totalMetaRealizada) {
        this.totalMetaRealizada = totalMetaRealizada;
    }

    public Double getPorcentagemMetaRealizada() {
        if (getTotalMeta() > 0) {
            Double metaRealizado = Double.valueOf(getTotalMetaRealizada());
            Double totalMeta = Double.valueOf(getTotalMeta());
            Double porcentagem = (metaRealizado / totalMeta) * 100;
            return porcentagem;
        } else {
            return 0.0;
        }
    }

    public String getPorcentagemMetaRealizada_Apresentar() {
        try {
            return Formatador.formatarValorMonetarioSemMoeda(getPorcentagemMetaRealizada()) + "%";
        } catch (Exception ignored) {
        }
        return "";
    }

    public Integer getOrdemMeta() {
        if (ordemMeta == null) {
            ordemMeta = 0;
        }
        return ordemMeta;
    }

    public void setOrdemMeta(Integer ordemMeta) {
        this.ordemMeta = ordemMeta;
    }

    public Integer getTotalContatoSemBaterMeta() {
        if (totalContatoSemBaterMeta == null) {
            totalContatoSemBaterMeta = 0;
        }
        return totalContatoSemBaterMeta;
    }

    public void setTotalContatoSemBaterMeta(Integer totalContatoSemBaterMeta) {
        this.totalContatoSemBaterMeta = totalContatoSemBaterMeta;
    }

    public String getNomeMeta() {
        if (nomeMeta == null) {
            nomeMeta = "";
        }
        return nomeMeta;
    }

    public void setNomeMeta(String nomeMeta) {
        this.nomeMeta = nomeMeta;
    }

    public List<FecharMetaDetalhadoVO> getListaMetaDetalhadaFiltradaSemPaginacao() {
        if (listaMetaDetalhadaFiltradaSemPaginacao == null) {
            listaMetaDetalhadaFiltradaSemPaginacao = new ArrayList<>();
        }
        return listaMetaDetalhadaFiltradaSemPaginacao;
    }

    public void setListaMetaDetalhadaFiltradaSemPaginacao(List<FecharMetaDetalhadoVO> listaMetaDetalhadaFiltradaSemPaginacao) {
        this.listaMetaDetalhadaFiltradaSemPaginacao = listaMetaDetalhadaFiltradaSemPaginacao;
    }

    public JSONObject toJson() {
        JSONObject tipoMeta = new JSONObject();
        tipoMeta.put("descricao", fasesCRMEnum.getDescricao());
        tipoMeta.put("total", totalMeta);
        tipoMeta.put("totalRealizada", totalMetaRealizada);

        return tipoMeta;
    }
}
