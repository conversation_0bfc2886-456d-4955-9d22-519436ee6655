package negocio.comuns.crm;

import br.com.pactosolucoes.enumeradores.StatusEnvioMailingEnum;
import negocio.comuns.arquitetura.SuperVO;

import java.util.Date;

public class MailingHistoricoVO extends SuperVO {
    private Integer codigo;
    private Integer malaDireta;
    private Date dataInicio;
    private Date dataFim;
    private String filtro;
    private Integer registrosAfetados;
    private Integer pessoasAfetadas;
    private String log = "";
    private StatusEnvioMailingEnum status;
    private int nrEnviados = 0;
    private int nrNaoEnviados = 0;
    private int nrAmostraDestinatarios = 0;

    private String clientesEnviados = "";
    private String clientesNaoEnviados = "";

    private Integer saldo = 0;

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getMalaDireta() {
        return malaDireta;
    }

    public void setMalaDireta(Integer malaDireta) {
        this.malaDireta = malaDireta;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public String getFiltro() {
        return filtro;
    }

    public void setFiltro(String filtro) {
        this.filtro = filtro;
    }

    public Integer getRegistrosAfetados() {
        return registrosAfetados;
    }

    public void setRegistrosAfetados(Integer registrosAfetados) {
        this.registrosAfetados = registrosAfetados;
    }

    public Integer getPessoasAfetadas() {
        return pessoasAfetadas;
    }

    public void setPessoasAfetadas(Integer pessoasAfetadas) {
        this.pessoasAfetadas = pessoasAfetadas;
    }

    public String getLog() {
        return log;
    }

    public String getLogTooltipster() {
        if(log != null && log.contains("Retorno serviço sms: ok - ")){
            return "Enviados com sucesso";
        }
        return "";
    }

    public void setLog(String log) {
        this.log = log;
    }

    public StatusEnvioMailingEnum getStatus() {
        return status;
    }

    public void setStatus(StatusEnvioMailingEnum status) {
        this.status = status;
    }

    public String getClientesEnviados() {
        return clientesEnviados;
    }

    public void setClientesEnviados(String clientesEnviados) {
        this.clientesEnviados = clientesEnviados;
    }

    public String getClientesNaoEnviados() {
        return clientesNaoEnviados;
    }

    public void setClientesNaoEnviados(String clientesNaoEnviados) {
        this.clientesNaoEnviados = clientesNaoEnviados;
    }

    public int getNrEnviados() {
        return nrEnviados;
    }

    public void setNrEnviados(int nrEnviados) {
        this.nrEnviados = nrEnviados;
    }

    public int getNrNaoEnviados() {
        return nrNaoEnviados;
    }

    public void setNrNaoEnviados(int nrNaoEnviados) {
        this.nrNaoEnviados = nrNaoEnviados;
    }

    public Integer getSaldo() {
        return saldo;
    }

    public void setSaldo(Integer saldo) {
        this.saldo = saldo;
    }

    public int getNrAmostraDestinatarios() {
        return nrAmostraDestinatarios;
    }

    public void setNrAmostraDestinatarios(int nrAmostraDestinatarios) {
        this.nrAmostraDestinatarios = nrAmostraDestinatarios;
    }
}
