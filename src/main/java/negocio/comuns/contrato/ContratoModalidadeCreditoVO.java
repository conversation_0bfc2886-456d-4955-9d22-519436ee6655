package negocio.comuns.contrato;

import annotations.arquitetura.ChaveEstrangeira;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

public class ContratoModalidadeCreditoVO extends SuperVO {

    @ChavePrimaria
    private Integer codigo;
    @ChaveEstrangeira
    private ContratoModalidadeVO contratoModalidadeVO;
    private Integer qtdCreditoCompra;
    private Integer qtdCreditoDisponivel;
    private Double valorUnitario;
    private Double valorMensal;
    private Double valorTotal;

    public ContratoModalidadeCreditoVO() {
        super();
    }

    public static void validarDados(ContratoModalidadeCreditoVO obj) throws ConsistirException {
        if (!obj.getValidarDados()) {
            return;
        }

        if (UteisValidacao.emptyNumber(obj.getContratoModalidadeVO().getCodigo())) {
            throw new ConsistirException("O campo ContratoModalidade (ContratoModalidadeCredito) deve ser informado.");
        }
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public ContratoModalidadeVO getContratoModalidadeVO() {
        if (contratoModalidadeVO == null) {
            contratoModalidadeVO = new ContratoModalidadeVO();
        }
        return contratoModalidadeVO;
    }

    public void setContratoModalidadeVO(ContratoModalidadeVO contratoModalidadeVO) {
        this.contratoModalidadeVO = contratoModalidadeVO;
    }

    public Integer getQtdCreditoCompra() {
        if (qtdCreditoCompra == null) {
            qtdCreditoCompra = 0;
        }
        return qtdCreditoCompra;
    }

    public void setQtdCreditoCompra(Integer qtdCreditoCompra) {
        this.qtdCreditoCompra = qtdCreditoCompra;
    }

    public Integer getQtdCreditoDisponivel() {
        if (qtdCreditoDisponivel == null) {
            qtdCreditoDisponivel = 0;
        }
        return qtdCreditoDisponivel;
    }

    public void setQtdCreditoDisponivel(Integer qtdCreditoDisponivel) {
        this.qtdCreditoDisponivel = qtdCreditoDisponivel;
    }

    public Double getValorUnitario() {
        if (valorUnitario == null) {
            valorUnitario = 0.0;
        }
        return valorUnitario;
    }

    public void setValorUnitario(Double valorUnitario) {
        this.valorUnitario = valorUnitario;
    }

    public Double getValorMensal() {
        if (valorMensal == null) {
            valorMensal = 0.0;
        }
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Double getValorTotal() {
        if (valorTotal == null) {
            valorTotal = 0.0;
        }
        return valorTotal;
    }

    public void setValorTotal(Double valorTotal) {
        this.valorTotal = valorTotal;
    }

    public String getValorTotal_Apresentar(){
        return Formatador.formatarValorMonetario(getValorTotal());
    }
}