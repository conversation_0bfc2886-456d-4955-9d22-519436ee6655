/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperTO;

/**
 *
 * <AUTHOR>
 */
public class JustificativaOperacaoWS extends SuperTO{
    protected Integer codigo = 0;
    protected String descricao ;
    
    public JustificativaOperacaoWS(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
