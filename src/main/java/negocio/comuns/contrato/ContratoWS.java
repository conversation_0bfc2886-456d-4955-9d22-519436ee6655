package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.plano.ContratoModalidadeWS;
import negocio.comuns.plano.ProdutoWS;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 05/02/2015
 */
public class ContratoWS extends SuperTO {

    private int codigo;
    private int codigoCliente;
    private double valorFinal = 0.0;
    private double valorBase = 0.0;
    private boolean contratoEnviado = false;
    private boolean reciboEnviado = false;
    private String situacaoContrato = "";
    private String dataLancamento = "";
    private String vigenciaDe = "";
    private String vigenciaAteAjustada = "";
    private String situacao = "";
    private String nomePlano = "";
    private List<ContratoModalidadeWS> modalidades = new ArrayList<ContratoModalidadeWS>();
    private List<ProdutoWS> produtos = new ArrayList<ProdutoWS>();
    private List<ProdutoWS> parcelas = new ArrayList<>();
    private double valorMensal = 0.0;
    private double valorAnuidade = 0.0;
    private int diaVencimentoAnuidade = 0;
    private String mesVencimentoAnuidade = "";
    private double valorAdesao  = 0.0;
    private double valorMatricula  = 0.0;
    private double valorProRata = 0.0;
    private double valorPrimeiraParcela = 0.0;
    private int codigoPlano  = 0;
    private int maxVezesParcelarAdesao  = 0;
    private String msgValidacao = "";
    private Integer numeroMeses;
    private boolean crossfit = false;
    private String situacaoSubordinada;
    private boolean permiteMarcarAula = false;
    private boolean vendaCreditoTreino = false;
    private Integer nrParcelas;
    private String horarioDescricao;
    private boolean permiteRenovar = false;
    private boolean simulacao = false;
    private boolean bolsa = false;
    private int maxVezesParcelarProduto  = 0;
    private String descricaoCobrancaPrimeiraParcela = "";
    private Integer anoCobrancaAnuidade;
    private boolean permiterenovacaoautomatica = false;
    private boolean renovacaoautomaticasimnao = false;
    private Integer diasCartao;
    private boolean isRenovacao = false;
    private boolean isRematricula = false;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getCodigoCliente() {
        return codigoCliente;
    }

    public void setCodigoCliente(int codigoCliente) {
        this.codigoCliente = codigoCliente;
    }

    public double getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(double valorFinal) {
        this.valorFinal = valorFinal;
    }

    public boolean isContratoEnviado() {
        return contratoEnviado;
    }

    public void setContratoEnviado(boolean contratoEnviado) {
        this.contratoEnviado = contratoEnviado;
    }

    public boolean isReciboEnviado() {
        return reciboEnviado;
    }

    public void setReciboEnviado(boolean reciboEnviado) {
        this.reciboEnviado = reciboEnviado;
    }

    public String getSituacaoContrato() {
        return situacaoContrato;
    }

    public void setSituacaoContrato(String situacaoContrato) {
        this.situacaoContrato = situacaoContrato;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getVigenciaDe() {
        return vigenciaDe;
    }

    public void setVigenciaDe(String vigenciaDe) {
        this.vigenciaDe = vigenciaDe;
    }

    public String getVigenciaAteAjustada() {
        return vigenciaAteAjustada;
    }

    public void setVigenciaAteAjustada(String vigenciaAteAjustada) {
        this.vigenciaAteAjustada = vigenciaAteAjustada;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getNomePlano() {
        return nomePlano;
    }

    public void setNomePlano(String nomePlano) {
        this.nomePlano = nomePlano;
    }

    public List<ContratoModalidadeWS> getModalidades() {
        return modalidades;
    }

    public void setModalidades(List<ContratoModalidadeWS> modalidades) {
        this.modalidades = modalidades;
    }

    public double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public double getValorAnuidade() {
        return valorAnuidade;
    }

    public void setValorAnuidade(double valorAnuidade) {
        this.valorAnuidade = valorAnuidade;
    }

    public int getDiaVencimentoAnuidade() {
        return diaVencimentoAnuidade;
    }

    public void setDiaVencimentoAnuidade(int diaVencimentoAnuidade) {
        this.diaVencimentoAnuidade = diaVencimentoAnuidade;
    }

    public String getMesVencimentoAnuidade() {
        return mesVencimentoAnuidade;
    }

    public void setMesVencimentoAnuidade(String mesVencimentoAnuidade) {
        this.mesVencimentoAnuidade = mesVencimentoAnuidade;
    }

    public double getValorAdesao() {
        return valorAdesao;
    }

    public void setValorAdesao(double valorAdesao) {
        this.valorAdesao = valorAdesao;
    }

    public int getCodigoPlano() {
        return codigoPlano;
    }

    public void setCodigoPlano(int codigoPlano) {
        this.codigoPlano = codigoPlano;
    }

    public String getMsgValidacao() {
        return msgValidacao;
    }

    public void setMsgValidacao(String msgValidacao) {
        this.msgValidacao = msgValidacao;
    }

    public double getValorBase() {
        return valorBase;
    }

    public void setValorBase(double valorBase) {
        this.valorBase = valorBase;
    }

    public Integer getNumeroMeses() {
        if (numeroMeses == null) {
            numeroMeses = 0;
        }
        return numeroMeses;
    }

    public void setNumeroMeses(Integer numeroMeses) {
        this.numeroMeses = numeroMeses;
    }

    public boolean isCrossfit() {
        return crossfit;
    }

    public void setCrossfit(boolean crossfit) {
        this.crossfit = crossfit;
    }

    public String getSituacaoSubordinada() {
        if (situacaoSubordinada == null) {
            situacaoSubordinada = "";
        }
        return situacaoSubordinada;
    }

    public void setSituacaoSubordinada(String situacaoSubordinada) {
        this.situacaoSubordinada = situacaoSubordinada;
    }

    public boolean isPermiteMarcarAula() {
        return permiteMarcarAula;
    }

    public void setPermiteMarcarAula(boolean permiteMarcarAula) {
        this.permiteMarcarAula = permiteMarcarAula;
    }

    public boolean isVendaCreditoTreino() {
        return vendaCreditoTreino;
    }

    public void setVendaCreditoTreino(boolean vendaCreditoTreino) {
        this.vendaCreditoTreino = vendaCreditoTreino;
    }

    public Integer getNrParcelas() {
        if (nrParcelas == null) {
            nrParcelas = 0;
        }
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public String getHorarioDescricao() {
        if (horarioDescricao == null) {
            horarioDescricao = "";
        }
        return horarioDescricao;
    }

    public void setHorarioDescricao(String horarioDescricao) {
        this.horarioDescricao = horarioDescricao;
    }

    public boolean isPermiteRenovar() {
        return permiteRenovar;
    }

    public void setPermiteRenovar(boolean permiteRenovar) {
        this.permiteRenovar = permiteRenovar;
    }

    public boolean isSimulacao() {
        return simulacao;
    }

    public void setSimulacao(boolean simulacao) {
        this.simulacao = simulacao;
    }

    public boolean isBolsa() {
        return bolsa;
}

    public void setBolsa(boolean bolsa) {
        this.bolsa = bolsa;
    }

    public List<ProdutoWS> getProdutos() {
        return produtos;
    }

    public void setProdutos(List<ProdutoWS> produtos) {
        this.produtos = produtos;
    }

    public List<ProdutoWS> getParcelas() {
        return parcelas;
    }

    public void setParcelas(List<ProdutoWS> parcelas) {
        this.parcelas = parcelas;
    }

    public int getMaxVezesParcelarAdesao() {
        return maxVezesParcelarAdesao;
    }

    public void setMaxVezesParcelarAdesao(int maxVezesParcelarAdesao) {
        this.maxVezesParcelarAdesao = maxVezesParcelarAdesao;
    }

    public double getValorMatricula() {
        return valorMatricula;
    }

    public void setValorMatricula(double valorMatricula) {
        this.valorMatricula = valorMatricula;
    }

    public double getValorProRata() {
        return valorProRata;
    }

    public void setValorProRata(double valorProRata) {
        this.valorProRata = valorProRata;
    }

    public double getValorPrimeiraParcela() {
        return valorPrimeiraParcela;
    }

    public void setValorPrimeiraParcela(double valorPrimeiraParcela) {
        this.valorPrimeiraParcela = valorPrimeiraParcela;
    }

    public int getMaxVezesParcelarProduto() {
        return maxVezesParcelarProduto;
    }

    public void setMaxVezesParcelarProduto(int maxVezesParcelarProduto) {
        this.maxVezesParcelarProduto = maxVezesParcelarProduto;
    }

    public String getDescricaoCobrancaPrimeiraParcela() {
        return descricaoCobrancaPrimeiraParcela;
    }

    public void setDescricaoCobrancaPrimeiraParcela(String descricaoCobrancaPrimeiraParcela) {
        this.descricaoCobrancaPrimeiraParcela = descricaoCobrancaPrimeiraParcela;
    }

    public Integer getAnoCobrancaAnuidade() {
        return anoCobrancaAnuidade;
    }

    public void setAnoCobrancaAnuidade(Integer anoCobrancaAnuidade) {
        this.anoCobrancaAnuidade = anoCobrancaAnuidade;
    }

    public boolean isPermiterenovacaoautomatica() {
        return permiterenovacaoautomatica;
    }

    public void setPermiterenovacaoautomatica(boolean permiterenovacaoautomatica) {
        this.permiterenovacaoautomatica = permiterenovacaoautomatica;
    }

    public boolean isRenovacaoautomaticasimnao() {
        return renovacaoautomaticasimnao;
    }

    public void setRenovacaoautomaticasimnao(boolean renovacaoautomaticasimnao) {
        this.renovacaoautomaticasimnao = renovacaoautomaticasimnao;
    }

    public Integer getDiasCartao() {
        return diasCartao;
    }

    public void setDiasCartao(Integer diasCartao) {
        this.diasCartao = diasCartao;
    }

    public boolean isRenovacao() {
        return isRenovacao;
    }

    public void setRenovacao(boolean renovacao) {
        isRenovacao = renovacao;
    }

    public boolean isRematricula() {
        return isRematricula;
    }

    public void setRematricula(boolean rematricula) {
        isRematricula = rematricula;
    }
}
