package negocio.comuns.contrato;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;

import java.util.Date;

public class ComissaoProdutoConfiguracaoVO extends ComissaoConfiguracaoVO {

    private Integer codigo = 0;
    private CategoriaProdutoVO categoriaProduto;
    private ProdutoVO produto;
    private Double valorFixo = 0.0;
    private Double porcentagem = 0.0;

    public String toString() {
        if (getProduto().getCodigo() > 0) {
            return getProduto().getDescricao();
        } else {
            return getCategoriaProduto().getDescricao();
        }
    }

    public void validarDados() throws Exception {
        if (getEmpresa().getCodigo() == 0)
            throw new ConsistirException("Selecione a Empresa.");
        if (getVigenciaFinal() == null)
            throw new Exception("Informe a Vigência Inicial.");
        if (getVigenciaInicio() == null)
            throw new Exception("Informe a Vigência Final.");
    }

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getItemApresentar() {
        if (getProduto() != null && getProduto().getCodigo() > 0) {
            return "PRODUTO: " + getProduto().getDescricao();
        }
        if (getCategoriaProduto() != null && getCategoriaProduto().getCodigo() > 0) {
            return "CATEGORIA: " + getCategoriaProduto().getDescricao();
        }
        return "";
    }

    public ProdutoVO getProduto() {
        if (produto == null) {
            produto = new ProdutoVO();
        }
        return produto;
    }

    public void setProduto(ProdutoVO produto) {
        this.produto = produto;
    }

    public CategoriaProdutoVO getCategoriaProduto() {
        if (categoriaProduto == null) {
            categoriaProduto = new CategoriaProdutoVO();
        }
        return categoriaProduto;
    }

    public void setCategoriaProduto(CategoriaProdutoVO categoriaProduto) {
        this.categoriaProduto = categoriaProduto;
    }

    public Double getValorFixo() {
        return valorFixo;
    }

    public void setValorFixo(Double valorFixo) {
        this.valorFixo = valorFixo;
    }

    public String getValorFixo_Apresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getValorFixo());
    }

    public Double getPorcentagem() {
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public String getPorcentagemApresentar() {
        return Formatador.formatarValorMonetarioSemMoeda(getPorcentagem()) + "%";
    }

    public void ajustarDataFinal(Date vigenciaInicio) throws Exception {
        if (getVigenciaFinal() == null) {
            Date fim = Uteis.obterPrimeiroDiaMes(vigenciaInicio);
            fim = Uteis.somarDias(fim, -1);
            if (fim.before(vigenciaInicio)) {
                fim = Uteis.somarDias(fim, 1);
            }
            setVigenciaFinal(fim);
        }
    }
}
