package negocio.comuns.contrato;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ArquivoVO;
import negocio.comuns.utilitarias.Calendario;

import java.util.Date;

/**
 * Created by glauco on 04/06/2014.
 */
public class AtestadoVO extends SuperVO {

    private Integer codigo = 0;
    private MovProdutoVO movProduto = new MovProdutoVO();
    private Boolean parqPositivo = false;
    private ArquivoVO arquivo = new ArquivoVO();
    private String observacao = "";
    private Integer avaliacaoFisicaTW;
    private Date dataRegistro;

    @Override
    public Integer getCodigo() {
        return codigo;
    }

    @Override
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public MovProdutoVO getMovProduto() {
        return movProduto;
    }

    public void setMovProduto(MovProdutoVO movProduto) {
        this.movProduto = movProduto;
    }

    public Boolean getParqPositivo() {
        return parqPositivo;
    }

    public void setParqPositivo(Boolean parqPositivo) {
        this.parqPositivo = parqPositivo;
    }

    public ArquivoVO getArquivo() {
        return arquivo;
    }

    public void setArquivo(ArquivoVO arquivo) {
        this.arquivo = arquivo;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean isVigente() {
        boolean vigente = false;
        if (movProduto != null && movProduto.getDataInicioVigencia() != null && movProduto.getDataFinalVigencia() != null) {
            Date hoje = Calendario.hoje();
            vigente = Calendario.entre(hoje, movProduto.getDataInicioVigencia(), movProduto.getDataFinalVigencia());
        }
        return vigente;
    }

    public Integer getAvaliacaoFisicaTW() {
        if (avaliacaoFisicaTW == null) {
            avaliacaoFisicaTW = 0;
        }
        return avaliacaoFisicaTW;
    }

    public void setAvaliacaoFisicaTW(Integer avaliacaoFisicaTW) {
        this.avaliacaoFisicaTW = avaliacaoFisicaTW;
    }

    public Date getDataRegistro() {
        return dataRegistro;
    }

    public void setDataRegistro(Date dataRegistro) {
        this.dataRegistro = dataRegistro;
    }
}