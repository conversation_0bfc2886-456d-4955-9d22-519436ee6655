package negocio.comuns.conviteaulaexperimental;

import negocio.comuns.arquitetura.SuperTO;

import java.util.Date;

/**
 * Created by ulisses on 26/01/2016.
 */
public class TipoConviteAulaExperimentalMobileTO extends SuperTO{

    private Integer codigoTipoConvite;
    private String nomeClienteConvidou;
    private Date vigenciaFinal;
    private Integer quantidadeAulaExperimental;
    private String nomeEmpresa;
    private String enderecoEmpresa;
    private String setorEmpresa;
    private String numeroEmpresa;
    private String complementoEmpresa;
    private String modalidadesConvite;
    private String telefone1;
    private String telefone2;
    private boolean aulasAgendadasEmDiasSeguido;

    public Integer getCodigoTipoConvite() {
        return codigoTipoConvite;
    }

    public void setCodigoTipoConvite(Integer codigoTipoConvite) {
        this.codigoTipoConvite = codigoTipoConvite;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public Integer getQuantidadeAulaExperimental() {
        return quantidadeAulaExperimental;
    }

    public void setQuantidadeAulaExperimental(Integer quantidadeAulaExperimental) {
        this.quantidadeAulaExperimental = quantidadeAulaExperimental;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getEnderecoEmpresa() {
        return enderecoEmpresa;
    }

    public void setEnderecoEmpresa(String enderecoEmpresa) {
        this.enderecoEmpresa = enderecoEmpresa;
    }

    public String getSetorEmpresa() {
        return setorEmpresa;
    }

    public void setSetorEmpresa(String setorEmpresa) {
        this.setorEmpresa = setorEmpresa;
    }

    public String getNumeroEmpresa() {
        return numeroEmpresa;
    }

    public void setNumeroEmpresa(String numeroEmpresa) {
        this.numeroEmpresa = numeroEmpresa;
    }

    public String getComplementoEmpresa() {
        return complementoEmpresa;
    }

    public void setComplementoEmpresa(String complementoEmpresa) {
        this.complementoEmpresa = complementoEmpresa;
    }

    public String getModalidadesConvite() {
        return modalidadesConvite;
    }

    public void setModalidadesConvite(String modalidadesConvite) {
        this.modalidadesConvite = modalidadesConvite;
    }

    public String getTelefone1() {
        return telefone1;
    }

    public void setTelefone1(String telefone1) {
        this.telefone1 = telefone1;
    }

    public String getTelefone2() {
        return telefone2;
    }

    public void setTelefone2(String telefone2) {
        this.telefone2 = telefone2;
    }

    public String getNomeClienteConvidou() {
        return nomeClienteConvidou;
    }

    public void setNomeClienteConvidou(String nomeClienteConvidou) {
        this.nomeClienteConvidou = nomeClienteConvidou;
    }

    public boolean isAulasAgendadasEmDiasSeguido() {
        return aulasAgendadasEmDiasSeguido;
    }

    public void setAulasAgendadasEmDiasSeguido(boolean aulasAgendadasEmDiasSeguido) {
        this.aulasAgendadasEmDiasSeguido = aulasAgendadasEmDiasSeguido;
    }
}
