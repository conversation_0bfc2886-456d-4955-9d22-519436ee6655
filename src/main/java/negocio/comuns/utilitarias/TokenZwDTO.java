package negocio.comuns.utilitarias;

import controle.arquitetura.exceptions.SecretException;

import java.io.Serializable;

public class TokenZwDTO implements Serializable {

    private String secret;
    private long expiresAt;
    private String tokenName;
    private String tokenKey;
    private String ip;
    private String method;
    private String uri;
    private String params;

    public String getSecret() {
        return secret;
    }

    public long getExpiresAt() {
        return expiresAt;
    }

    public String getTokenName() {
        return tokenName;
    }

    public String getTokenKey() {
        return tokenKey;
    }

    public String getIp() {
        return ip;
    }

    public String getMethod() {
        return method;
    }

    public String getUri() {
        return uri;
    }

    public String getParams() {
        return params;
    }

    private boolean isExpired() {
        return System.currentTimeMillis() > getExpiresAt();
    }

    public boolean isValid() throws SecretException {
        String secretKeyword = Uteis.secretKeyword();
        return secretKeyword != null && secretKeyword.equals(getSecret()) && !isExpired();
    }

}
