package negocio.comuns.utilitarias;

import java.util.List;

public class UteisSQL {


    public static String gerarScriptInsert(String nomeTabela, List<String> colunas) {
        return gerarScriptInsertUpdate(true, nomeTabela, "", colunas);
    }

    public static String gerarScriptUpdate(String nomeTabela, String colunaChavePrimaria, List<String> colunas) {
        return gerarScriptInsertUpdate(false, nomeTabela, colunaChavePrimaria, colunas);
    }

    private static String gerarScriptInsertUpdate(boolean insert, String nomeTabela, String colunaChavePrimaria, List<String> colunas) {
        StringBuilder sqlColunas = new StringBuilder();
        StringBuilder sqlPrepare = new StringBuilder();
        for (String col : colunas) {
            sqlColunas.append(col);
            if (insert) {
                sqlColunas.append(", ");
            } else {
                sqlColunas.append(" = ?, ");
            }
            sqlPrepare.append("?, ");
        }
        sqlColunas.delete((sqlColunas.length() - 2), sqlColunas.length());
        sqlPrepare.delete((sqlPrepare.length() - 2), sqlPrepare.length());

        String sql;
        if (insert) {
            sql = "INSERT INTO " + nomeTabela + " (" + sqlColunas + ") VALUES (" + sqlPrepare + ") ";
        } else {
            sql = "UPDATE " + nomeTabela + " SET " + sqlColunas + " WHERE " + colunaChavePrimaria + " = ? ";
        }

        return sql;
    }
}
