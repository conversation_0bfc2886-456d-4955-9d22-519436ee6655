package negocio.comuns.utilitarias.reflexao;

import negocio.comuns.utilitarias.UtilReflection;

import java.util.Arrays;
import java.util.List;

/**
 * Created by johny<PERSON> on 16/01/2017.
 */
public class PovoadorPropriedades {

    public static void povoar(PropriedadePadrao[] propriedades, Object objetoPovoar) throws Exception{
        for(PropriedadePadrao propriedade : propriedades){
            UtilReflection.setValor(objetoPovoar, propriedade.getValorPadrao(), propriedade.getPropriedade().getNome(), false);
        }
    }

    public static void repovoarSubstituta(Propriedade substituta, PropriedadePadrao[] propriedades, String valor, Object objetoPovoar) throws Exception{
        for(PropriedadePadrao propriedade : propriedades){
            if(propriedade.getSubstitutas().length > 0 && Arrays.binarySearch(propriedade.getSubstitutas(), substituta) >= 0){
                String valorPadrao = propriedade.getValorPadrao().toString();
                valorPadrao = valorPadrao.replace(substituta.name(), valor);
                UtilReflection.setValor(objetoPovoar, valorPadrao, propriedade.getPropriedade().getNome(), false);
            }
        }
    }
}
