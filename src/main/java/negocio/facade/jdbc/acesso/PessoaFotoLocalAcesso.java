/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.acesso;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.acesso.PessoaFotoLocalAcessoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.LocalAcessoInterfaceFacade;
import negocio.interfaces.acesso.PessoaFotoLocalAcessoInterfaceFacade;

/**
 *
 * <AUTHOR>
 */
public class PessoaFotoLocalAcesso extends SuperEntidade implements PessoaFotoLocalAcessoInterfaceFacade {

    public PessoaFotoLocalAcesso() throws Exception {
        super();
    }

    public PessoaFotoLocalAcesso(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(Integer localAcesso, Integer pessoa) throws Exception {
        try {
            String sql = "INSERT INTO pessoaFotoLocalAcesso(localAcesso, pessoa, dtHrEnvio) VALUES ( ?, ?, ? )";
            PreparedStatement sqlInserir = getCon().prepareStatement(sql);
            sqlInserir.setInt(1, localAcesso);
            sqlInserir.setInt(2, pessoa);
            sqlInserir.setDate(3, Uteis.getDataJDBC(Calendario.hoje()));

            sqlInserir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void incluir(PessoaFotoLocalAcessoVO obj) throws Exception {
        try {
            PessoaFotoLocalAcessoVO.validarDados(obj);
            incluir(obj.getLocalAcesso().getCodigo().intValue(), obj.getPessoa().getCodigo().intValue());
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    public void incluir(LocalAcessoVO localAcesso, PessoaVO pessoa) throws Exception {
        PessoaFotoLocalAcessoVO vo = new PessoaFotoLocalAcessoVO();
        vo.setLocalAcesso(localAcesso);
        vo.setPessoa(pessoa);
        incluir(vo);
    }

    public void excluir(PessoaFotoLocalAcessoVO obj) throws Exception {
        excluir(obj.getCodigo().intValue());
    }

    public void excluir(int codigo) throws Exception {
        try {
            String sql = "DELETE FROM pessoaFotoLocalAcesso WHERE (codigo = ?)";
            PreparedStatement sqlExcluir = getCon().prepareStatement(sql);
            sqlExcluir.setInt(1, codigo);
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public void excluirFotoPessoaLocalAcesso(int pessoa) throws Exception {
        String sql = "DELETE FROM pessoaFotoLocalAcesso WHERE (pessoa = ?)";
        PreparedStatement sqlExcluir = getCon().prepareStatement(sql);
        sqlExcluir.setInt(1, pessoa);
        sqlExcluir.execute();
    }

    public void excluirFotoPessoaLocalAcesso(int localAcesso, int pessoa) throws Exception {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE FROM pessoaFotoLocalAcesso ");
            sql.append("WHERE (localAcesso = ?) " + (pessoa != 0 ? "and (pessoa = ?)" : ""));
            PreparedStatement sqlExcluir = getCon().prepareStatement(sql.toString());
            sqlExcluir.setInt(1, localAcesso);
            if (pessoa != 0) {
                sqlExcluir.setInt(2, pessoa);
            }
            sqlExcluir.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    public boolean fotoJaEnviadaLocalAcesso(Integer localAcesso, Integer pessoa) throws Exception {
        String sqlStr = "SELECT COUNT(*) FROM pessoaFotoLocalAcesso WHERE localAcesso = " + localAcesso.toString()
                + " and pessoa = " + pessoa.toString();
        Statement stm = getCon().createStatement();

        ResultSet tabelaResultado = stm.executeQuery(sqlStr);

        if (tabelaResultado.next()) {
            return tabelaResultado.getInt(1) > 0;
        } else {
            return false;
        }
    }
}
