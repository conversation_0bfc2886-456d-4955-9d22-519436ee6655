package negocio.facade.jdbc.acesso;

import negocio.comuns.acesso.CameraVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.acesso.CameraInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class Camera extends SuperEntidade implements CameraInterfaceFacade {

    public Camera() throws Exception {
    }

    public Camera(Connection conexao) throws Exception {
        super(conexao);
    }

    public static CameraVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CameraVO obj = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        obj.setNovoObj(false);
        return obj;
    }

    public static CameraVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        CameraVO obj = new CameraVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.setEndereco(dadosSQL.getString("endereco"));
        obj.setTerminal(dadosSQL.getInt("terminal"));
        obj.setIndice(dadosSQL.getInt("indice"));
        obj.setUrlRtsp(dadosSQL.getString("urlRtsp"));
        obj.getServidorFacialVO().setCodigo(dadosSQL.getInt("servidorfacial"));
        return obj;
    }

    @Override
    public void incluir(CameraVO obj) throws Exception {
        String sql = "INSERT INTO camera(endereco, descricao, terminal, servidorFacial, indice, urlRtsp) VALUES (?, ?, ?, ?, ?, ?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        int i = 0;
        sqlInserir.setString(++i, obj.getEndereco());
        sqlInserir.setString(++i, obj.getDescricao());
        sqlInserir.setInt(++i, obj.getTerminal());
        sqlInserir.setInt(++i, obj.getServidorFacialVO().getCodigo());
        sqlInserir.setInt(++i, obj.getIndice());
        sqlInserir.setString(++i, obj.getUrlRtsp());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(CameraVO obj) throws Exception {
        String sql = "UPDATE camera set endereco = ?, terminal = ?, servidorFacial = ?, descricao = ?, indice = ?, urlRtsp = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        int i = 0;
        sqlAlterar.setString(++i, obj.getEndereco());
        sqlAlterar.setInt(++i, obj.getTerminal());
        sqlAlterar.setInt(++i, obj.getServidorFacialVO().getCodigo());
        sqlAlterar.setString(++i, obj.getDescricao());
        sqlAlterar.setInt(++i, obj.getIndice());
        sqlAlterar.setString(++i, obj.getUrlRtsp());
        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public void excluir(CameraVO obj) throws Exception {
        String sql = "DELETE FROM camera WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        int i = 0;
        sqlExcluir.setInt(++i, obj.getCodigo());
        sqlExcluir.execute();
    }

    @Override
    public CameraVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        String sqlStr = "SELECT * FROM camera where codigo = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        tabelaResultado.next();
        return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
    }

    public List<CameraVO> consultarCameras(Integer servidorFacial, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM camera WHERE servidorfacial = ? ORDER BY descricao";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, servidorFacial);
        ResultSet resultado = sqlConsulta.executeQuery();

        List<CameraVO> objetos = new ArrayList<CameraVO>();
        while (resultado.next()) {
            CameraVO novoObj = montarDados(resultado, nivelMontarDados);
            objetos.add(novoObj);
        }
        return objetos;
    }

    public void incluirCameras(Integer servidorFacial, List<CameraVO> objetos) throws Exception {
        for (CameraVO obj : objetos) {
            obj.getServidorFacialVO().setCodigo(servidorFacial);
            incluir(obj);
        }
    }

    public void alterarCameras(Integer servidorFacial, List<CameraVO> cameras) throws Exception {
        StringBuilder sqlColNaoExcluir = new StringBuilder();
        StringBuilder sql = new StringBuilder();
        // Pegar os códigos das cameras que não deverão ser excluídos.
        for (int i = 0; i < cameras.size(); i++) {
            CameraVO obj = cameras.get(i);
            if (i == 0) {
                sqlColNaoExcluir.append(obj.getCodigo());
            } else {
                sqlColNaoExcluir.append(",");
                sqlColNaoExcluir.append(obj.getCodigo());
            }
        }
        if (cameras.isEmpty()) {
            sqlColNaoExcluir.append("0");
        }
        // Excluir as cameras que foram excluídos da Lista de câmeras.
        sql.delete(0, sql.length());
        sql.append("DELETE FROM  camera  WHERE servidorfacial = ").append(servidorFacial).append(" and codigo not in(");
        sql.append(sqlColNaoExcluir.toString());
        sql.append(")");
        PreparedStatement sqlExcluir = con.prepareStatement(sql.toString());
        sqlExcluir.execute();
        Iterator e = cameras.iterator();
        while (e.hasNext()) {
            CameraVO obj = (CameraVO) e.next();
            obj.getServidorFacialVO().setCodigo(servidorFacial);
            if (obj.getCodigo().equals(0)) {
                incluir(obj);
            } else {
                alterar(obj);
            }
        }
    }

}
