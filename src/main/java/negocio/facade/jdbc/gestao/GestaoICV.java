package negocio.facade.jdbc.gestao;

import br.com.pactosolucoes.enumeradores.TipoContratoEnum;
import negocio.comuns.basico.enumerador.TipoBVEnum;
import negocio.comuns.gestao.GestaoICVTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.QuestionarioCliente;
import negocio.interfaces.gestao.GestaoICVInterfaceFacade;
import relatorio.negocio.comuns.financeiro.GestaoFiltroICV;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON> on 17/11/2016.
 */
public class GestaoICV extends SuperEntidade implements GestaoICVInterfaceFacade{

    public GestaoICV(Connection conexao) throws Exception {
        super(conexao);
    }
    public GestaoICVTO processarICV(Date dia, List<Integer> colaboradores , Integer empresa ,
                                               boolean filtroMatriculas, boolean filtroRematriculas, boolean filtroRetornos,
                                               boolean filtroEspontaneo, boolean filtroAgendado,
                                               List<TipoContratoEnum> tiposContrato, List<TipoBVEnum> tiposBV) throws Exception {
        if (empresa == 0) {
            throw new Exception("O campo empresa deve ser informado");
        }
        Date inicio = dia;
        Date fim = Uteis.obterPrimeiroDiaMes(dia);
        //limpar os campos
        GestaoFiltroICV filtros = new GestaoFiltroICV(inicio,fim,colaboradores, empresa ,filtroMatriculas, filtroRematriculas, filtroRetornos,
        filtroEspontaneo,  filtroAgendado, tiposContrato, tiposBV);
        GestaoICVTO resultado = new GestaoICVTO();
        if (!UteisValidacao.emptyString(filtros.getColaboradoresString())) {
            if (filtroMatriculas || filtroRematriculas || filtroRetornos) {
                atualizaQtdQuestionarios(resultado,filtros);
            }
            //deve consultar matricula caso o filtro for matricula
            // ou então se estiver marcado pelo menos espontâneo ou agendado e não estiver marcado matrícula e rematrícula
            // ou então se estiver marcado pelo menos espontâneo ou agendado e estiver marcado matrícula
            if ((filtroMatriculas || filtroRetornos)
                    || ((!filtroMatriculas && !filtroRematriculas) && (filtroEspontaneo || filtroAgendado))
                    || ((filtroMatriculas && !filtroRematriculas) && (filtroEspontaneo || filtroAgendado))) {
                atualizaQtdMatriculas(resultado,filtros);
            }
            //deve consultar rematricula caso o filtro for rematricula
            // ou então se estiver marcado pelo menos espontâneo ou agendado e não estiver marcado matrícula e rematrícula
            // ou então se estiver marcado pelo menos espontâneo ou agendado e estiver marcado rematrícula
            if ((filtroRematriculas || filtroRetornos)
                    || ((!filtroRematriculas && !filtroMatriculas) && (filtroEspontaneo || filtroAgendado))
                    || ((filtroRematriculas && !filtroMatriculas) && (filtroEspontaneo || filtroAgendado))) {
                atualizaQtdRematriculas(resultado,filtros);
            }

            if (!resultado.getQtdQuestionarioMes().equals(0)) {
                resultado.setTotalICV(((resultado.getQtdMatriculaMes() + resultado.getQtdRematriculaMes()) / resultado.getQtdQuestionarioMes().doubleValue()) * 100);
            }
        } else {
            consultarIndiceConversaoVendaPorColaborador(filtros,resultado);
        }
        return resultado;
    }
    public void consultarIndiceConversaoVendaPorColaborador(GestaoFiltroICV filtros,GestaoICVTO resultado) throws Exception {
            // caso nao exista nenhum colaborador marcado a consultar padrao sera por todos colaboradores.
            if (UteisValidacao.emptyList(filtros.getColaboradores())) {
                if (filtros.isFiltroMatriculas() || filtros.isFiltroRematriculas() || filtros.isFiltroRetornos()) {
                    atualizaQtdQuestionarios(resultado,filtros);
                }
                if ((filtros.isFiltroMatriculas() || filtros.isFiltroRetornos())
                        || ((!filtros.isFiltroMatriculas() && ! filtros.isFiltroRematriculas() ) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))
                        || ((filtros.isFiltroMatriculas() && ! filtros.isFiltroRematriculas() ) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))) {
                    atualizaQtdMatriculas(resultado,filtros);
                }
                if (( filtros.isFiltroRematriculas()  || filtros.isFiltroRetornos())
                        || ((! filtros.isFiltroRematriculas()  && !filtros.isFiltroMatriculas()) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))
                        || (( filtros.isFiltroRematriculas()  && !filtros.isFiltroMatriculas()) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))) {
                    atualizaQtdRematriculas(resultado,filtros);
                }
            } else {
                if (filtros.isFiltroMatriculas() ||  filtros.isFiltroRematriculas()  || filtros.isFiltroRetornos()) {
                    atualizaQtdQuestionarios(resultado,filtros);
                }
                if ((filtros.isFiltroMatriculas() || filtros.isFiltroRetornos())
                        || ((!filtros.isFiltroMatriculas() && ! filtros.isFiltroRematriculas() ) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))
                        || ((filtros.isFiltroMatriculas() && ! filtros.isFiltroRematriculas() ) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))) {
                    atualizaQtdMatriculas(resultado,filtros);
                }
                if (( filtros.isFiltroRematriculas()  || filtros.isFiltroRetornos())
                        || ((! filtros.isFiltroRematriculas()  && !filtros.isFiltroMatriculas()) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))
                        || (( filtros.isFiltroRematriculas()  && !filtros.isFiltroMatriculas()) && (filtros.isFiltroEspontaneo() || filtros.isFiltroAgendado()))) {
                    atualizaQtdRematriculas(resultado,filtros);
                }

            }

            if (!resultado.getQtdQuestionarioMes().equals(0)) {
                resultado.setTotalICV(((resultado.getQtdMatriculaMes() + resultado.getQtdRematriculaMes()) / resultado.getQtdQuestionarioMes().doubleValue()) * 100);
            }
    }
    private void atualizaQtdRematriculas(GestaoICVTO dados,GestaoFiltroICV filtro) throws  Exception {
        dados.setQtdRematriculaDia(getQtdMatriculaGenerico("RE", filtro,true));
        dados.setQtdRematriculaMes(getQtdMatriculaGenerico("RE", filtro,false));
    }
    private void atualizaQtdQuestionarios(GestaoICVTO dados,GestaoFiltroICV filtro)  throws  Exception {
        dados.setQtdQuestionarioDia(getQtdQuestionarioDia(filtro));
        dados.setQtdQuestionarioMes(getQtdQuestionarioDia(filtro));
    }

    private void atualizaQtdMatriculas(GestaoICVTO dados,GestaoFiltroICV filtro) throws Exception {
        dados.setQtdMatriculaDia(getQtdMatriculaGenerico("MA", filtro,true));
        dados.setQtdMatriculaMes(getQtdMatriculaGenerico("MA", filtro,false));
    }
    private int getQtdMatriculaGenerico(String situacao, GestaoFiltroICV filtro, boolean qtdMes) throws  Exception {
        return new Cliente(this.con)
                .consultaQuantidadeSituacaoContratoPorDataLancamentoEmpresaColaborador(filtro.getInicio(), filtro.getFim(),
                        filtro.getEmpresa(), filtro.getColaboradoresString(), situacao,filtro.getTiposContrato(),filtro.getTiposBV(), false, qtdMes);
    }
    private Integer getQtdQuestionarioDia(GestaoFiltroICV filtro) throws Exception {
        return new QuestionarioCliente(this.con)
                .consultaQuantidadeQuestionarioPorDataEmpresaPorColaborador(
                        filtro.getInicio(), filtro.getFim(), filtro.getColaboradoresString(), filtro.getEmpresa(), filtro.getTiposBV(), false);
    }
}
