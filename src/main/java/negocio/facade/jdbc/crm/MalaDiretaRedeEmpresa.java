package negocio.facade.jdbc.crm;

import negocio.comuns.crm.MalaDiretaRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class MalaDiretaRedeEmpresa extends SuperEntidade {

    public MalaDiretaRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public MalaDiretaRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(MalaDiretaRedeEmpresaVO malaDiretaRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO MalaDiretaRedeEmpresa " +
                "(maladireta,chaveorigem,chavedestino,datacadastro, nomeUnidade, mensagemSituacao, empresadestino)" +
                "VALUES" +
                "(?,?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, malaDiretaRedeEmpresaVO.getMalaDireta());
            preparedStatement.setString(i++, malaDiretaRedeEmpresaVO.getChaveOrigem());
            if (isNotBlank(malaDiretaRedeEmpresaVO.getChaveDestino())) {
                preparedStatement.setString(i++, malaDiretaRedeEmpresaVO.getChaveDestino());
            } else {
                preparedStatement.setString(i++, "");
            }
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, malaDiretaRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, malaDiretaRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.setInt(i++, malaDiretaRedeEmpresaVO.getEmpresaDestino());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer malaDireta, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE MalaDiretaRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE malaDireta = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, malaDireta);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer malaDireta, String chaveOrigem, String chaveDestino, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE MalaDiretaRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE malaDireta = ? AND chaveorigem = ? AND chavedestino = ? AND empresadestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, malaDireta);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer malaDireta, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer malaDiretaReplicado, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE MalaDiretaRedeEmpresa set " +
                "dataatualizacao = ?, mensagemSituacao = ?, malaDiretaReplicado = ? " +
                "WHERE malaDireta = ? AND chaveorigem = ? AND empresadestino = ? AND chaveDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, malaDiretaReplicado);
            preparedStatement.setInt(i++, malaDireta);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public MalaDiretaRedeEmpresaVO consultarPorChaveEmpresaMalaDireta(String chaveDestino, Integer empresaDestino, Integer malaDireta) throws SQLException {
        String sql = "SELECT * FROM MalaDiretaRedeEmpresa " +
                "WHERE chaveDestino = ? AND empresaDestino = ? AND malaDireta = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, empresaDestino);
            preparedStatement.setInt(i++, malaDireta);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public MalaDiretaRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        MalaDiretaRedeEmpresaVO malaDiretaRedeEmpresaVO = new MalaDiretaRedeEmpresaVO();
        malaDiretaRedeEmpresaVO.setChaveOrigem(resultSet.getString("chaveorigem"));
        malaDiretaRedeEmpresaVO.setChaveDestino(resultSet.getString("chavedestino"));
        malaDiretaRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        malaDiretaRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        malaDiretaRedeEmpresaVO.setMalaDireta(resultSet.getInt("maladireta"));
        malaDiretaRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        malaDiretaRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        malaDiretaRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));
        malaDiretaRedeEmpresaVO.setMalaDiretaReplicado(resultSet.getInt("maladiretareplicado"));
        malaDiretaRedeEmpresaVO.setEmpresaDestino(resultSet.getInt("empresadestino"));

        return malaDiretaRedeEmpresaVO;
    }
}
