package negocio.facade.jdbc.crm;

import java.sql.*;
import java.util.Hashtable;
import negocio.comuns.crm.GrupoColaboradorParticipanteVO;
import negocio.interfaces.crm.*;
import negocio.comuns.crm.GrupoColaboradorVO;
import negocio.facade.jdbc.arquitetura.*;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>GrupoColaboradorVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>GrupoColaboradorVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see GrupoColaboradorVO
 * @see SuperEntidade
 */
public class GrupoColaborador extends SuperEntidade implements GrupoColaboradorInterfaceFacade {

    private Hashtable grupoColaboradorParticipantes;

    public GrupoColaborador() throws Exception {
        super();
        setGrupoColaboradorParticipantes(new Hashtable());
    }

    public GrupoColaborador(Connection con) throws Exception {
        super(con);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>GrupoColaboradorVO</code>.
     */
    public GrupoColaboradorVO novo() throws Exception {
        incluirCRM(getIdEntidade());
        GrupoColaboradorVO obj = new GrupoColaboradorVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>GrupoColaboradorVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>GrupoColaboradorVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(GrupoColaboradorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(new Boolean(true));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(GrupoColaboradorVO obj) throws Exception {
        try {
            GrupoColaboradorVO.validarDados(obj);
            incluirCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "INSERT INTO GrupoColaborador( descricao, gerente, tipoGrupo, empresa ) VALUES ( ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setString(1, obj.getDescricao());
            if (obj.getGerente().getCodigo().intValue() != 0) {
                sqlInserir.setInt(2, obj.getGerente().getCodigo().intValue());
            } else {
                sqlInserir.setNull(2, 0);
            }
            sqlInserir.setString(3, obj.getTipoGrupo());
            sqlInserir.setInt(4, obj.getEmpresa().getCodigo());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            getFacade().getGrupoColaboradorParticipante().incluirGrupoColaboradorParticipantes(obj.getCodigo(), obj.getGrupoColaboradorParticipanteVOs());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>GrupoColaboradorVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>GrupoColaboradorVO</code> que será alterada no banco de dados.
     * @exception Execption Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(GrupoColaboradorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            GrupoColaboradorVO.validarDados(obj);
            executarValidacaoSeExisterParticipanteDiferenteDoTipoGrupo(obj);
            alterarCRM(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE GrupoColaborador set descricao=?, gerente=?, tipoGrupo=?, empresa=? WHERE ((codigo = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            sqlAlterar.setString(1, obj.getDescricao());
            if (obj.getGerente().getCodigo().intValue() != 0) {
                sqlAlterar.setInt(2, obj.getGerente().getCodigo().intValue());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setString(3, obj.getTipoGrupo());
            sqlAlterar.setInt(4, obj.getEmpresa().getCodigo());
            sqlAlterar.setInt(5, obj.getCodigo().intValue());
            sqlAlterar.execute();
            getFacade().getGrupoColaboradorParticipante().alterarGrupoColaboradorParticipantes(obj.getCodigo(), obj.getGrupoColaboradorParticipanteVOs());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>GrupoColaboradorVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>GrupoColaboradorVO</code> que será removido no banco de dados.
     * @exception Execption Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(GrupoColaboradorVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirCRM(getIdEntidade());
            String sql = "DELETE FROM GrupoColaborador WHERE ((codigo = ?))";
            PreparedStatement sqlExcluir = con.prepareStatement(sql);
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
            getFacade().getGrupoColaboradorParticipante().excluirGrupoColaboradorParticipantes(obj.getCodigo());
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaborador</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM GrupoColaborador where codigo >= " + valorConsulta);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND GrupoColaborador.empresa = " + empresa);
        }
        sql.append(" order by codigo ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorCodigo(Integer valorConsulta,int nivelMontarDados, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM GrupoColaborador where codigo = " + valorConsulta);
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND GrupoColaborador.empresa = " + empresa);
        }
        sql.append(" order by codigo ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaborador</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigoDiferenteTipoVisao(Integer valorConsulta, String tipoVisao, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT GrupoColaborador.* FROM GrupoColaborador ");
        sql.append("inner join grupocolaboradorparticipante on grupocolaboradorparticipante.codigo = grupocolaboradorparticipante.grupocolaborador ");
        sql.append(" and grupocolaboradorparticipante.tipoVisao <> '" + tipoVisao + "' ");
        sql.append(" WHERE GrupoColaborador.codigo >= " + valorConsulta.intValue());
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND GrupoColaborador.empresa = " + empresa);
        }
        sql.append(" ORDER BY GrupoColaborador.codigo");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaborador</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorOrganizadorCarteira(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();

        sql.append("select * from ( select  Distinct on (gc1.codigo) gc1.codigo, gc1.descricao, gc1.gerente, \n");
        sql.append("gc1.tipogrupo, ( select total from (  \n");
        sql.append("select Count((Vinculo.cliente)) as total, gcp.grupoColaborador from Vinculo \n");
        sql.append("inner join Colaborador as co on Vinculo.colaborador = co.codigo  \n");
        sql.append("inner join GrupoColaboradorParticipante as gcp on co.codigo = gcp.colaboradorParticipante \n");
        sql.append("inner join Cliente on Cliente.codigo = Vinculo.cliente AND cliente.empresa = gc1.empresa \n");
        sql.append("group by gcp.grupoColaborador )   as tabela  \n");
        sql.append("where grupoColaborador = gc1.codigo) as totalCliente, gc1.empresa from GrupoColaborador as gc1 \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("WHERE gc1.empresa = " + empresa);
        }
        sql.append(" group  by gc1.codigo, gc1.descricao, gc1.gerente , gc1.tipogrupo, gc1.empresa \n");
        sql.append("order by gc1.codigo  ) as tabelaFinal  \n");
        sql.append("where (  totalcliente > 0 ) \n");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>GrupoColaborador</code> através do valor do atributo 
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public GrupoColaboradorVO consultarPorOrganizadorCarteiraColaboradorSemGrupo(Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(c.codigo) AS total FROM cliente c \n");
        sql.append("INNER JOIN situacaoclientesinteticodw sc ON c.codigo = sc.codigocliente \n");
        sql.append("INNER JOIN vinculo v ON v.cliente = c.codigo  \n");
        sql.append("INNER JOIN colaborador co ON v.colaborador = co.codigo \n");
        sql.append("WHERE co.codigo NOT IN (SELECT colaboradorparticipante FROM grupocolaboradorparticipante) \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND co.empresa = " + empresa);
        }
        GrupoColaboradorVO obj;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                obj = new GrupoColaboradorVO();
                while (tabelaResultado.next()) {
                    obj.setDescricao("COLABORADORES SEM GRUPO");
                    obj.setTotalCliente(new Long(tabelaResultado.getInt("total")));
                }
            }
        }
        return obj;
    }

    public List consultarPorPeriodoDataCadastroCliente(Date data, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(c.codigo) AS totalcliente, gc.descricao, gc.codigo, gc.gerente, gc.tipogrupo, gc.empresa FROM cliente c \n");
        sql.append("INNER JOIN situacaoclientesinteticodw sc ON c.codigo = sc.codigocliente  \n");
        sql.append("INNER JOIN vinculo v ON v.cliente = c.codigo \n");
        sql.append("INNER JOIN colaborador co ON v.colaborador = co.codigo \n");
        sql.append("INNER JOIN grupocolaboradorparticipante gcp ON gcp.colaboradorparticipante = co.codigo \n");
        sql.append("INNER JOIN grupocolaborador gc ON gcp.grupocolaborador = gc.codigo \n");
        sql.append("WHERE (sc.situacao IN('AT') \n");
        sql.append("OR ( sc.situacao in ('IN', 'TR') and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(data) + " 00:00:00') \n");
        sql.append("OR ( sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(data) + " 00:00:00')) \n");
        sql.append("AND gc.empresa = c.empresa ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("AND gc.empresa = " + empresa);
        }
        sql.append(" GROUP BY gc.descricao, gc.codigo, gc.gerente, gc.tipogrupo, gc.empresa ");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public GrupoColaboradorVO consultarPorPeriodoDataCadastroClienteColaboradorSemGrupo(Date data, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(c.codigo) AS total FROM cliente c \n");
        sql.append("INNER JOIN situacaoclientesinteticodw sc ON c.codigo = sc.codigocliente \n");
        sql.append("INNER JOIN vinculo v ON v.cliente = c.codigo \n");
        sql.append("INNER JOIN colaborador co ON v.colaborador = co.codigo \n");
        sql.append("WHERE (sc.situacao IN('AT')  \n");
        sql.append("OR ( sc.situacao in ('IN', 'TR') and sc.datavigenciaateajustada >= '" + Uteis.getDataJDBC(data) + " 00:00:00') \n");
        sql.append("OR ( sc.situacao in ('VI') and sc.dataultimobv >= '" + Uteis.getDataJDBC(data) + " 00:00:00')) \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND co.empresa = " + empresa);
        }
        sql.append(" AND co.codigo NOT IN (SELECT colaboradorparticipante FROM grupocolaboradorparticipante) ");

        GrupoColaboradorVO obj;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                obj = new GrupoColaboradorVO();
                while (tabelaResultado.next()) {
                    obj.setDescricao("COLABORADORES SEM GRUPO");
                    obj.setTotalCliente(new Long(tabelaResultado.getInt("total")));
                }
            }
        }
        return obj;
    }

    public List consultarPorDescricaoGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM GrupoColaborador WHERE upper (descricao) like( '" + valorConsulta.toUpperCase() + "%' ) ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND empresa = " + empresa);
        }
        sql.append(" ORDER BY GrupoColaborador.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List<GrupoColaboradorVO> consultarPorTipoGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM GrupoColaborador WHERE upper (tipoGrupo) like( '").append(valorConsulta.toUpperCase()).append("%' ) ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND empresa = ").append(empresa);
        }
        sql.append(" ORDER BY GrupoColaborador.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorDescricaoGrupoDiferenteTipoVisao(String valorConsulta, String tipoVisao, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT GrupoColaborador.* FROM GrupoColaborador ");
        sql.append(" inner join grupocolaboradorparticipante on grupocolaboradorparticipante.codigo = grupocolaboradorparticipante.grupocolaborador and grupocolaboradorparticipante.tipoVisao <> '" + tipoVisao + "' ");
        sql.append(" WHERE upper (descricao) like( '" + valorConsulta.toUpperCase() + "%' ) ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND empresa = " + empresa);
        }
        sql.append(" ORDER BY GrupoColaborador.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarPorResponsavelGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT GrupoColaborador.* \n");
        sql.append("FROM GrupoColaborador \n");
        sql.append("INNER JOIN usuario ON grupocolaborador.gerente = usuario.codigo AND usuario.nome ILIKE ? \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" WHERE empresa = ? \n");
        }
        sql.append(" ORDER BY GrupoColaborador.gerente");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            stm.setString(1, valorConsulta + "%");
            if (!UteisValidacao.emptyNumber(empresa)) {
                stm.setInt(2, empresa);
            }
            try (ResultSet tabelaResultado = stm.executeQuery()) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public Boolean consultarGerentePorCodigoUsuarioLogado(Integer valorConsulta) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlStr = "SELECT GrupoColaborador.* FROM GrupoColaborador INNER join usuario on grupocolaborador.gerente = usuario.codigo and  usuario.codigo  = " + valorConsulta + " ORDER BY GrupoColaborador.gerente";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (!tabelaResultado.next()) {
                    return false;
                }
            }
        }
        return true;
    }

    public Boolean consultarSeExiste() throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sqlStr = "select exists(select codigo from grupocolaborador limit 1) as exists from grupocolaborador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (!tabelaResultado.next()) {
                    return false;
                }
            }
        }
        return true;
    }

    public List consultarGrupoColaboradorComParticipantes(boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT GrupoColaborador.* FROM GrupoColaborador ";
        if (!UteisValidacao.emptyNumber(empresa)) {
            sqlStr = sqlStr + "WHERE empresa = " + empresa;
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public List consultarGrupoColaboradorPorCodigoColaborador(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT GrupoColaborador.* FROM GrupoColaborador ");
        sql.append(" INNER join grupocolaboradorparticipante on grupocolaboradorparticipante.grupocolaborador = grupocolaborador.codigo ");
        sql.append(" INNER join Colaborador on grupocolaboradorparticipante.colaboradorParticipante = Colaborador.codigo and  Colaborador.codigo  = " + valorConsulta + " ");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" WHERE GrupoColaborador.empresa = " + empresa);
        }
        sql.append(" ORDER BY GrupoColaborador.gerente");
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List consultarGrupoColaboradorComParticipantesPorCodigoColaboradorPorTipoVisao(Integer valorConsulta, String tipoVisao, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT GrupoColaborador.* FROM GrupoColaborador ");
        sql.append(" INNER join grupocolaboradorparticipante on grupocolaboradorparticipante.grupocolaborador = grupocolaborador.codigo  and grupocolaboradorparticipante.tipovisao = '" + tipoVisao.toUpperCase() + "' ");
        sql.append(" INNER join Colaborador on grupocolaboradorparticipante.colaboradorParticipante = Colaborador.codigo and  Colaborador.codigo  = " + valorConsulta + " ");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND GrupoColaborador.empresa = " + empresa);
        }
        sql.append(" ORDER BY GrupoColaborador.gerente");
        try (Statement sqlConsultar = con.createStatement()) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    public List consultarPorParticipanteGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct on (GrupoColaborador.codigo) grupoColaborador.* FROM grupoColaborador ");
        sql.append(" INNER join GrupocolaboradorParticipante on grupoColaborador.codigo = grupoColaboradorParticipante.grupocolaborador ");
        sql.append(" INNER join Colaborador on grupocolaboradorparticipante.colaboradorParticipante = Colaborador.codigo ");
        sql.append(" INNER join Pessoa on pessoa.codigo = Colaborador.pessoa and upper (pessoa.nome) like ('" + valorConsulta.toUpperCase() + "%') ");

        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" WHERE GrupoColaborador.empresa = " + empresa);
        }
        sql.append(" ORDER BY grupocolaborador.codigo");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>GrupoColaboradorVO</code> resultantes da consulta.
     */
    public static List<GrupoColaboradorVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<GrupoColaboradorVO> vetResultado = new ArrayList<GrupoColaboradorVO>();
        while (tabelaResultado.next()) {
            GrupoColaboradorVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        tabelaResultado = null;
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>GrupoColaboradorVO</code>.
     * @return  O objeto da classe <code>GrupoColaboradorVO</code> com os dados devidamente montados.
     */
    public static GrupoColaboradorVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GrupoColaboradorVO obj = new GrupoColaboradorVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ORGANIZADORCARTEIRA) {
            obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.getGerente().setCodigo(new Integer(dadosSQL.getInt("gerente")));
            obj.setTipoGrupo(dadosSQL.getString("tipoGrupo"));
            obj.setTotalCliente(new Long(dadosSQL.getInt("totalcliente")));
            obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
            obj.setNovoObj(true);
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_RESULTADOS_BI) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.getGerente().setCodigo(dadosSQL.getInt("gerente"));
            obj.setTipoGrupo(dadosSQL.getString("tipoGrupo"));
            obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
            obj.setNovoObj(true);
            obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantes(
                    obj.getCodigo(), Uteis.NIVELMONTARDADOS_RESULTADOS_BI));
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
            obj.setDescricao(dadosSQL.getString("descricao"));
            obj.getGerente().setCodigo(new Integer(dadosSQL.getInt("gerente")));
            obj.setTipoGrupo(dadosSQL.getString("tipoGrupo"));
            obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
            obj.setNovoObj(new Boolean(false));
            montarDadosColaboradorGerente(obj, nivelMontarDados);
            return obj;
        }

        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setDescricao(dadosSQL.getString("descricao"));
        obj.getGerente().setCodigo(new Integer(dadosSQL.getInt("gerente")));
        obj.setTipoGrupo(dadosSQL.getString("tipoGrupo"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            montarDadosColaboradorGerente(obj, nivelMontarDados);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CAIXAPARAOPERADOR) {
            obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferente(obj.getCodigo(), "VI", nivelMontarDados));
            montarDadosColaboradorGerente(obj, nivelMontarDados);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            //Verificando se o usuario possui permissao de ver todas as carteiras
            if (getFacade().getAberturaMeta().verificarPermitirVisualizarTodasCarteiras()) {
                //Consultando todos os participantes do grupo e setando
                obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantes(obj.getCodigo(), nivelMontarDados));
            } else {
                //Consultando os participantes diferente de VI[Vizualizar todas as carteiras] e setando
                obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantesPorCodigoETipoVisaoDiferente(obj.getCodigo(), "VI", nivelMontarDados));
            }

            return obj;
        }
        obj.setGrupoColaboradorParticipanteVOs(getFacade().getGrupoColaboradorParticipante().consultarGrupoColaboradorParticipantes(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        montarDadosColaboradorGerente(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        return obj;
    }

    public static void montarDadosColaboradorGerente(GrupoColaboradorVO obj, int nivelMontarDados) throws Exception {
        if (obj.getGerente().getCodigo().intValue() == 0) {
            obj.setGerente(new UsuarioVO());
            return;
        }
        obj.setGerente(getFacade().getUsuario().consultarPorChavePrimaria(obj.getGerente().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por adicionar um objeto da <code>GrupoColaboradorParticipanteVO</code> no Hashtable <code>GrupoColaboradorParticipantes</code>.
     * Neste Hashtable são mantidos todos os objetos de GrupoColaboradorParticipante de uma determinada GrupoColaborador.
     * @param obj  Objeto a ser adicionado no Hashtable.
     */
    public void adicionarObjGrupoColaboradorParticipantes(GrupoColaboradorParticipanteVO obj) throws Exception {
        getGrupoColaboradorParticipantes().put(obj.getColaboradorParticipante().getCodigo() + "", obj);
        //adicionarObjSubordinadoOC
    }

    /**
     * Operação responsável por remover um objeto da classe <code>GrupoColaboradorParticipanteVO</code> do Hashtable <code>GrupoColaboradorParticipantes</code>.
     * Neste Hashtable são mantidos todos os objetos de GrupoColaboradorParticipante de uma determinada GrupoColaborador.
     * @param Colaborador Atributo da classe <code>GrupoColaboradorParticipanteVO</code> utilizado como apelido (key) no Hashtable.
     */
    public void excluirObjGrupoColaboradorParticipantes(Integer Colaborador) throws Exception {
        getGrupoColaboradorParticipantes().remove(Colaborador + "");
        //excluirObjSubordinadoOC
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>GrupoColaboradorVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public GrupoColaboradorVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), false);
        String sql = "SELECT * FROM GrupoColaborador WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( GrupoColaborador ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }

    public Hashtable getGrupoColaboradorParticipantes() {
        if (grupoColaboradorParticipantes == null) {
            grupoColaboradorParticipantes = new Hashtable();
        }
        return (grupoColaboradorParticipantes);
    }

    public void setGrupoColaboradorParticipantes(Hashtable grupoColaboradorParticipantes) {
        this.grupoColaboradorParticipantes = grupoColaboradorParticipantes;
    }

    /**
     * 
     * @param grupo
     * @throws ConsistirException 
     */
    public void executarValidacaoSeExisterParticipanteDiferenteDoTipoGrupo(GrupoColaboradorVO grupo) throws ConsistirException {
        if (!grupo.getTipoGrupo().equals(grupo.getTipoGrupoAnterior())) {
            for (GrupoColaboradorParticipanteVO participante : grupo.getGrupoColaboradorParticipanteVOs()) {
                if (!participante.getTipoGrupo().equals(grupo.getTipoGrupo()) && !participante.getTipoVisao().equals("VI")) {
                    throw new ConsistirException("Uma vez que existe participante para o grupo com visão diferente de \"Visualizar todas as carteira\" não poderá ser alterado o campo TIPO GRUPO.");
                }
            }
        }
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT\n" +
                "  g.codigo,\n" +
                "  g.descricao,\n" +
                "  g.tipogrupo,\n" +
                "  u.nome AS responsavelgrupo\n" +
                "FROM grupocolaborador g\n" +
                "  INNER JOIN usuario u ON u.codigo = g.gerente");

        if (empresa > 0){
            sql.append(" WHERE g.empresa = " + empresa + ";");
        }
        return con.prepareStatement(sql.toString());
    }

    public String consultarJSON(Integer empresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getInt("codigo")).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("descricao"))).append("\",");
                json.append("\"").append(Dominios.getTipoGrupo().get(((rs.getString("tipogrupo"))))).append("\",");
                json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("responsavelgrupo"))).append("\"],");
            }
        }
        if(dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    public List<GrupoColaboradorVO> consultarGruposColaboradorComUsuario(Integer codigoUsuario, boolean controlarAcesso, int nivelMontarDados, Integer empresa) throws Exception {
        consultarCRM(getIdEntidade(), controlarAcesso);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct(gc.codigo), gc.* ");
        sql.append("FROM grupocolaborador gc ");
        sql.append("INNER JOIN grupocolaboradorparticipante gp ON gc.codigo = gp.grupocolaborador ");
        sql.append("INNER JOIN usuario u ON u.colaborador = gp.colaboradorparticipante ");
        if (!UteisValidacao.emptyNumber(codigoUsuario)) {
            sql.append(" WHERE u.codigo = " + codigoUsuario);
        }
        if (!UteisValidacao.emptyNumber(codigoUsuario) && !UteisValidacao.emptyNumber(empresa)) {
            sql.append(" AND gc.empresa = " + empresa);
        } else if (!UteisValidacao.emptyNumber(empresa) && UteisValidacao.emptyNumber(codigoUsuario)) {
            sql.append(" WHERE gc.empresa = " + empresa);
        }
        sql.append(" ORDER BY gc.descricao");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }
}
