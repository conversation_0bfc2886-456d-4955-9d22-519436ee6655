package negocio.facade.jdbc.crm;

import negocio.comuns.crm.MalingEnviadosVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.crm.MalingEnviadosInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.Date;

public class MalingEnviados extends SuperEntidade implements MalingEnviadosInterfaceFacade {

    public MalingEnviados(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(MalingEnviadosVO obj) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append(" INSERT INTO malingenviados (pessoa, dataenvio, cliente, situacao ) ");
        sql.append(" VALUES (?,?,?,?)");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        int i = 0;
        stm.setInt(++i, obj.getContrato());
        stm.setTimestamp(++i, Uteis.getDataJDBCTimestamp(obj.getDataenvio()));
        if (!UteisValidacao.emptyNumber(obj.getCliente())){
            stm.setInt(++i, obj.getCliente());
        } else {
            stm.setNull(++i, 0);
        }
        stm.setString(++i, obj.getSituacao());
        stm.execute();
    }

    @Override
    public void excluir(Date data) throws Exception {
        con.prepareStatement("DELETE FROM malingenviados WHERE dataenvio::date <='" + Uteis.getDataJDBCTimestamp(data) +"'" ).execute();
    }


}
