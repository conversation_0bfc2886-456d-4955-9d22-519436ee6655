/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.utilitarias;

import br.com.pactosolucoes.integracao.viacep.ViaCepJSON;
import br.com.pactosolucoes.integracao.viacep.ViaCepServico;
import negocio.comuns.utilitarias.CepVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class Cep {

    protected Connection conBDCep = null;

    public Cep() throws Exception {
        prepararConexao();
    }

    public void prepararConexao() throws Exception {
        try {
            if (conBDCep == null) {
                inicializar();
            } else {
                Statement stm = conBDCep.createStatement();
                stm.execute("select 1");
                stm = null;
            }
        } catch (SQLException ex) {
            try {
                //
                System.out.println("Erro na conexão (" + conBDCep + ") do PostegreSQL (CEP), "
                        + "vou tentar reiniciar a conexão do Banco de Dados."
                        + ex.getMessage());
                setConBDCep(null);
                inicializar();
                System.out.println("    -> Nova conexão: " + conBDCep + " obtida com sucesso!");
            } catch (Exception ex1) {
                throw new Exception("Não foi possível se recuperar de uma perda de conexão: " + ex1.getMessage());
            }
        }
    }

    public void inicializar() throws Exception {
        if (getConBDCep() == null) {
            ConexaoBDCep conexaoBDCep = new ConexaoBDCep();
            setConBDCep(conexaoBDCep.getConexao());
        }
        if (getConBDCep().isClosed()) {
            ConexaoBDCep conexaoBDCep = new ConexaoBDCep();
            setConBDCep(conexaoBDCep.getConexao());
        }
    }

    public void finalizarConexaoBD() throws SQLException {
        if (getConBDCep() != null) {
            getConBDCep().close();
            setConBDCep(null);
        }
    }

    public void executarUpdate(final String sql) throws Exception {
        SuperFacadeJDBC.executarConsulta(sql, conBDCep);
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>CepVO</code> através de sua chave primária.
     *
     * @throws Exception Caso haja problemas de conexão ou localização do
     *                   objeto procurado.
     */
    public CepVO consultarPorNumeroCep(String cep, int nivelMontarDados) throws Exception {
        inicializar();
        String sql = "SELECT endereco.completo as enderecoCompleto, "
                + "endereco.logradouro as enderecoLogradouro , "
                + "endereco.cep as enderecoCep, "
                + "cidade.descricao as cidadeDescricao, "
                + "cidade.cep as cidadeCep, "
                + "uf.descricao as ufDescricao, "
                + "uf.sigla as ufSigla, "
                + "bairro.descricao as bairroDescricao "
                + "FROM uf "
                + "INNER JOIN cidade ON cidade.ufcodigo= uf.codigo "
                + "INNER JOIN bairro ON bairro.cidadecodigo = cidade.codigo "
                + "INNER JOIN endereco ON endereco.bairrocodigo = bairro.codigo "
                + "WHERE endereco.cep = '" + cep + "'";
        PreparedStatement sqlConsultar = conBDCep.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        finalizarConexaoBD();
        if (!tabelaResultado.next()) {
            throw new Exception("Dados Não Encontrados (Cep).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * consulta Ceps pelos atributos cep.enderecoLogradouro,
     * cep.cidadeDescricao, cep.ufSigla retorna uma list de objetos CepVO com os
     * seguintes dados: cep.bairroDescricao, cep.enderecoLogradouro,
     * cep.enderecoCep
     *
     * @param cep CepVO
     * @param nivelMontarDados int
     * @return List <code><CepVO></code>
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    public List consultarDetalhe(CepVO cep, int nivelMontarDados) throws Exception {
        int add = 0;
        inicializar();
        if (cep.getUfSigla().isEmpty()) {
            throw new Exception("Informe o estado");
        }
        boolean consultarApenasCidade = true;
        StringBuffer sqlFiltro = new StringBuffer();
        StringBuffer sqlSelect = new StringBuffer();
        StringBuffer sqlSelectCidade = new StringBuffer();

        sqlSelect.append(" SELECT endereco.logradouro as enderecoLogradouro ");
        sqlSelect.append(" , endereco.cep as enderecoCep, bairro.descricao as bairroDescricao ");
        sqlSelect.append(" , cidade.descricao as cidadeDescricao ");
        sqlSelect.append(" FROM uf ");
        sqlSelect.append(" INNER JOIN cidade ON cidade.ufcodigo = uf.codigo ");
        sqlSelect.append(" INNER JOIN bairro ON bairro.cidadecodigo = cidade.codigo ");
        sqlSelect.append(" INNER JOIN endereco ON endereco.bairrocodigo = bairro.codigo ");

        // se logradouro informado
        if (!cep.getEnderecoLogradouro().trim().isEmpty()) {
            sqlFiltro.append("retira_acentuacao(endereco.logradouro) LIKE '%" + Uteis.retirarAcentuacao(cep.getEnderecoLogradouro().toUpperCase()) + "%' ");
            consultarApenasCidade = false;
            add++;
        }
        // se cidade informada
        if (!cep.getCidadeDescricao().trim().isEmpty()) {
            if (add > 0) {
                sqlFiltro.append(" and ");
            }
            sqlFiltro.append("retira_acentuacao(cidade.descricao) LIKE '%" + Uteis.retirarAcentuacao(cep.getCidadeDescricao().toUpperCase()) + "%' ");
            add++;
        }
        // se cidade informada
        if (!cep.getBairroDescricao().trim().isEmpty()) {
            if (add > 0) {
                sqlFiltro.append(" and ");
            }
            sqlFiltro.append("retira_acentuacao(bairro.descricao) LIKE '%" + Uteis.retirarAcentuacao(cep.getBairroDescricao().toUpperCase()) + "%' ");
            consultarApenasCidade = false;
            add++;
        }
        // se sigla do estado informada
        if (!cep.getUfSigla().trim().isEmpty()) {
            if (add > 0) {
                sqlFiltro.append(" and ");
            }
            sqlFiltro.append("uf.sigla = '" + cep.getUfSigla() + "' ");
        }

        if (sqlFiltro != null && !"".equals(sqlFiltro.toString())) {
            sqlSelect.append(" Where " + sqlFiltro.toString());
        }
        if (consultarApenasCidade) {
            sqlSelectCidade.append("SELECT '' as enderecoLogradouro  , cidade.cep as enderecoCep, '' as bairroDescricao  , cidade.descricao as cidadeDescricao  FROM uf  ");
            sqlSelectCidade.append("INNER JOIN cidade ON cidade.ufcodigo = uf.codigo ");
            sqlSelectCidade.append(" Where " + sqlFiltro.toString() + " UNION ALL");
        }

        sqlSelect.append("ORDER BY enderecologradouro,cidadeDescricao");

        PreparedStatement sqlConsultar = conBDCep.prepareStatement(sqlSelectCidade.toString() + sqlSelect.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        finalizarConexaoBD();
        if (!tabelaResultado.next()) {
            List<CepVO> ceps = consultarApiViaCEP(cep);
            if (ceps.isEmpty()) {
                throw new Exception("Dados Não Encontrados (Cep).");
            }
            return ceps;
        }
        return (montarListDados(tabelaResultado, nivelMontarDados));
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>CepVO</code>.
     *
     * @return O objeto da classe <code>CepVO</code> com os dados devidamente
     * montados.
     */
    public static CepVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CepVO obj = new CepVO();
        obj.setUfDescricao(dadosSQL.getString("ufDescricao"));
        obj.setUfSigla(dadosSQL.getString("ufSigla"));
        obj.setCidadeCep(dadosSQL.getString("cidadeCep"));
        obj.setCidadeDescricao(dadosSQL.getString("cidadeDescricao"));
        obj.setBairroDescricao(dadosSQL.getString("bairroDescricao"));
        obj.setEnderecoCompleto(dadosSQL.getString("enderecoCompleto"));
        obj.setEnderecoLogradouro(dadosSQL.getString("enderecoLogradouro"));
        obj.setEnderecoCep(dadosSQL.getString("enderecoCep"));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em uma lista de objetos da classe
     * <code>CepVO</code>.
     *
     * @return uma lista de objetos da classe <code>CepVO</code> com os dados
     * devidamente montados.
     */
    public static List montarListDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        List<CepVO> lista = new ArrayList();

        do {
            CepVO obj = new CepVO();
            obj.setBairroDescricao(dadosSQL.getString("bairroDescricao"));
            obj.setEnderecoLogradouro(dadosSQL.getString("enderecoLogradouro"));
            obj.setEnderecoCep(dadosSQL.getString("enderecoCep"));
            obj.setCidadeDescricao(dadosSQL.getString("cidadeDescricao"));
            obj.setNovoObj(new Boolean(false));
            lista.add(obj);
        } while (dadosSQL.next());

        return lista;
    }

    public Connection getConBDCep() {
        return conBDCep;
    }

    public void setConBDCep(Connection aConBDCep) {
        conBDCep = aConBDCep;
    }

    private static void importarArquivo(BufferedReader br) {
        Cep cepDao = null;
        try {
            cepDao = new Cep();
            String thisLine;
            int i = 1;
            while ((thisLine = br.readLine()) != null) {
                String comando = thisLine;
                if (!thisLine.substring(0, 1).equals("I")) {
                    comando = comando.substring(1);
                }
                System.out.println(String.format("%s - %s", i, comando));
                try {
                    cepDao.executarUpdate(comando);
                } catch (Exception e) {
                    System.err.println("Erro: " + e);
                }
                i++;
            }
        } catch (Exception e) {
            System.err.println("Erro: " + e);
        } finally {
            if (cepDao != null) {
                try {
                    cepDao.finalizarConexaoBD();
                } catch (SQLException ex) {
                    Logger.getLogger(Cep.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        }
    }

    public static void main(String[] args) {
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(
                    new FileInputStream("F://_downloads//CEP2015//UF.SQL"), "UTF8"));
            importarArquivo(br);
            //
            br = new BufferedReader(new InputStreamReader(
                    new FileInputStream("F://_downloads//CEP2015//Cidade.SQL"), "UTF8"));
            importarArquivo(br);
            //
            br = new BufferedReader(new InputStreamReader(
                    new FileInputStream("F://_downloads//CEP2015//Bairro.SQL"), "UTF8"));
            importarArquivo(br);
            //
            br = new BufferedReader(new InputStreamReader(
                    new FileInputStream("F://_downloads//CEP2015//Endereco.SQL"), "UTF8"));
            importarArquivo(br);
            //
        } catch (Exception e) {
            System.err.println("Erro: " + e);
        }
    }

    public CepVO consultarPorNumeroCepGeral(String cep, int nivelMontarDados) throws Exception {
        inicializar();
        String sql = "SELECT \n" +
                "'' as enderecoCompleto, \n" +
                "'' as enderecoLogradouro , \n" +
                "'' as enderecoCep, \n" +
                "cidade.descricao as cidadeDescricao, \n" +
                "cidade.cep as cidadeCep, \n" +
                "uf.descricao as ufDescricao, \n" +
                "uf.sigla as ufSigla, \n" +
                "'' as bairroDescricao\n" +
                "FROM uf \n" +
                "INNER JOIN cidade ON cidade.ufcodigo= uf.codigo " +
                "WHERE cidade.cep = '" + cep + "'";
        PreparedStatement sqlConsultar = conBDCep.prepareStatement(sql);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        finalizarConexaoBD();
        if (!tabelaResultado.next()) {
            throw new Exception("Dados Não Encontrados (Cep).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public CepVO consultarApiViaCEP(String cep) throws Exception {
        ViaCepServico viaCepServico = new ViaCepServico();
        CepVO cepVo = new CepVO(viaCepServico.consultarCepJson(cep));
        incluir(cepVo);
        return cepVo;
    }

    public List<CepVO> consultarApiViaCEP(CepVO cep) throws Exception {
        ViaCepServico viaCepServico = new ViaCepServico();
        List<ViaCepJSON> viaCepJSONList = viaCepServico.consultarCepJson(cep);
        if (UteisValidacao.emptyList(viaCepJSONList)) {
            viaCepJSONList = viaCepServico.consultarCepJsonExtendido(cep);
            if (UteisValidacao.emptyList(viaCepJSONList)) {
                throw new Exception("Endereço não encontrado!");
            }
        }
        List<CepVO> ceps = new ArrayList<>();
        viaCepJSONList.forEach(
                viaCepJSON -> {
                    ceps.add(new CepVO(viaCepJSON));
                }
        );
        incluirCeps(ceps);
        return ceps;
    }

    private void incluir(CepVO cep) throws Exception {
        incluirCeps(Collections.singletonList(cep));
    }

    private void incluirCeps(List<CepVO> ceps) throws Exception {
        try {
            inicializar();
            StringBuilder sql = new StringBuilder();
            List<UfCepVO> ufs = consultarUfs(ceps.stream().map(CepVO::getUfSigla).collect(Collectors.toSet()));
            List<BairroCepVO> bairros = consultarBairro(ceps.stream().map(CepVO::getBairroDescricao).collect(Collectors.toSet()));
            List<CidadeCepVO> cidades = consultarCidade(ceps.stream().map(CepVO::getCidadeDescricao).collect(Collectors.toSet()));
            Set<String> logradouros = ceps.stream().map(CepVO::getEnderecoLogradouro).collect(Collectors.toSet());
            Set<String> cepsStr = ceps.stream().map(CepVO::getEnderecoCep).collect(Collectors.toSet());
            List<EnderecoCepVO> enderecos = consultarEndereco(logradouros, cepsStr);
            Set<String> ufsIncluir = new HashSet<>();
            Set<BairroCepVO> bairrosIncluir = new HashSet<>();
            Set<CidadeCepVO> cidadesIncluir = new HashSet<>();
            Set<EnderecoCepVO> enderecosIncluir = new HashSet<>();


            ceps.forEach(
                    cepVO -> {
                        if (ufs.isEmpty() &&
                                ufsIncluir.stream().noneMatch(uf -> uf.equals(cepVO.getUfSigla().toUpperCase()))) {
                            ufsIncluir.add(cepVO.getUfSigla());
                        }
                        if ((cidades.stream().noneMatch(cidadeCepVO -> cidadeCepVO.getDescricao().equals(cepVO.getCidadeDescricao().toUpperCase()))) &&
                                cidadesIncluir.stream().noneMatch(cidadeCepVO -> cidadeCepVO.getDescricao().equals(cepVO.getCidadeDescricao().toUpperCase()))) {
                            CidadeCepVO cidadeCepVO = new CidadeCepVO();
                            cidadeCepVO.setDescricao(cepVO.getCidadeDescricao().toUpperCase());
                            cidadeCepVO.setUfSigla(cepVO.getUfSigla().toUpperCase());
                            cidadesIncluir.add(cidadeCepVO);
                        }
                        if (bairros.stream().noneMatch(bairroCepVO -> bairroCepVO.getDescricao().equals(cepVO.getBairroDescricao().toUpperCase()) && bairroCepVO.getCidadeDescricao().equals(cepVO.getCidadeDescricao().toUpperCase())) &&
                                bairrosIncluir.stream().noneMatch(bairroCepVO -> bairroCepVO.getDescricao().equals(cepVO.getBairroDescricao().toUpperCase()))) {
                            BairroCepVO bairroCepVO = new BairroCepVO();
                            bairroCepVO.setDescricao(cepVO.getBairroDescricao().toUpperCase());
                            bairroCepVO.setCidadeDescricao(cepVO.getCidadeDescricao().toUpperCase());
                            bairrosIncluir.add(bairroCepVO);
                        }
                        if (enderecos.isEmpty()) {
                            EnderecoCepVO enderecoCepVO = new EnderecoCepVO();
                            enderecoCepVO.setCep(cepVO.getEnderecoCep());
                            enderecoCepVO.setCidadeDescricao(cepVO.getCidadeDescricao());
                            enderecoCepVO.setBairroDescricao(cepVO.getBairroDescricao().toUpperCase());
                            enderecoCepVO.setLogradouro(cepVO.getEnderecoLogradouro().toUpperCase());
                            enderecoCepVO.setComplemento(cepVO.getEnderecoCompleto().toUpperCase());
                            enderecosIncluir.add(enderecoCepVO);
                        }
                    }
            );

            if (!ufsIncluir.isEmpty()) {
                insertUfs(ufsIncluir);
            }

            if (!cidadesIncluir.isEmpty()) {
                insertCidades(cidadesIncluir);
            }

            if (!bairrosIncluir.isEmpty()) {
                insertBairros(bairrosIncluir);
            }

            if (!enderecosIncluir.isEmpty()) {
                insertEnderecos(enderecosIncluir);
            }

            finalizarConexaoBD();
        } catch (Exception e) {
            throw e;
        }
    }

    private void insertUfs(Set<String> ufs) throws Exception {
        PreparedStatement ps = null;
        try {
            conBDCep.setAutoCommit(false);
            ps = conBDCep.prepareStatement("INSERT INTO uf(sigla) values(?);");
            if (ufs.size() == 1) {
                ps.setString(1, ((String) ufs.toArray()[0]).toUpperCase());
                ps.execute();
            } else {
                Iterator<String> it = ufs.iterator();
                while (it.hasNext()) {
                    ps.setString(1, it.next().toUpperCase());
                    ps.addBatch();
                }
                int[] numUpdates = ps.executeBatch();
                for (int i = 0; i < numUpdates.length; i++) {
                    if (numUpdates[i] == -2)
                        System.out.println("Execution " + i +
                                ": unknown number of rows updated");
                    else
                        System.out.println("Execution " + i +
                                "successful: " + numUpdates[i] + " rows updated");
                }
            }
            conBDCep.commit();
        } catch (Exception e) {
            conBDCep.rollback();
            conBDCep.setAutoCommit(true);
            throw e;
        }
    }

    private List<UfCepVO> consultarUfs(Set<String> ufsSigla) throws SQLException {
        List<UfCepVO> ufs = new ArrayList<>();
        StringBuilder sbUfs = new StringBuilder("SELECT *\n" +
                "FROM uf\n" +
                "WHERE UPPER(sigla) IN (");
        for (String sigla : ufsSigla) {
            sbUfs.append("'").append(sigla.toUpperCase()).append("'").append(",");
        }
        sbUfs.deleteCharAt(sbUfs.length() - 1);
        sbUfs.append(")\n");
        PreparedStatement sqlConsultar = conBDCep.prepareStatement(
                sbUfs.toString()
        );
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while (tabelaResultado.next()) {
            UfCepVO uf = new UfCepVO();
            uf.setCodigo(tabelaResultado.getInt("codigo"));
            uf.setSigla(tabelaResultado.getString("sigla"));
            uf.setDescricao(tabelaResultado.getString("descricao"));
            ufs.add(uf);
        }
        return ufs;
    }

    private List<CidadeCepVO> consultarCidade(Set<String> cidadesDescricao) throws SQLException {
        List<CidadeCepVO> cidades = new ArrayList<>();
        StringBuilder sqlCidades = new StringBuilder("SELECT *\n" +
                "FROM cidade\n" +
                "WHERE UPPER(descricao) IN (");
        for (String cidadeDescricao : cidadesDescricao) {
            sqlCidades.append("'").append(cidadeDescricao.toUpperCase()).append("'").append(",");
        }
        sqlCidades.deleteCharAt(sqlCidades.length() - 1);
        sqlCidades.append(")\n");
        PreparedStatement sqlConsultar = conBDCep.prepareStatement(
                sqlCidades.toString()
        );
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while (tabelaResultado.next()) {
            CidadeCepVO cidade = new CidadeCepVO();
            cidade.setCodigo(tabelaResultado.getInt("codigo"));
            cidade.setCep(tabelaResultado.getString("cep"));
            cidade.setUfCodigo(tabelaResultado.getInt("ufcodigo"));
            cidade.setDescricao(tabelaResultado.getString("descricao"));
            cidades.add(cidade);
        }
        return cidades;
    }

    private void insertCidades(Set<CidadeCepVO> cidades) throws Exception {
        PreparedStatement ps = null;
        try {
            conBDCep.setAutoCommit(false);
            ps = conBDCep.prepareStatement("INSERT INTO cidade(codigo, ufcodigo, cep, descricao) " +
                    "values(COALESCE((SELECT MAX(codigo)+1 FROM cidade), 1), (SELECT MAX(codigo) FROM uf where sigla = ?), ?, ?);");
            Iterator<CidadeCepVO> it = cidades.iterator();
            if (cidades.size() == 1) {
                CidadeCepVO cidadeCepVO = (CidadeCepVO) cidades.toArray()[0];
                ps.setString(1, cidadeCepVO.getUfSigla().toUpperCase());
                ps.setString(2, cidadeCepVO.getCep());
                ps.setString(3, cidadeCepVO.getDescricao().toUpperCase());
                ps.execute();
            } else {
                while (it.hasNext()) {
                    CidadeCepVO cidadeCepVO = it.next();
                    ps.setString(1, cidadeCepVO.getUfSigla());
                    ps.setString(2, cidadeCepVO.getCep());
                    ps.setString(3, cidadeCepVO.getDescricao().toUpperCase());
                    ps.addBatch();
                }
                int[] numUpdates = ps.executeBatch();
                for (int i = 0; i < numUpdates.length; i++) {
                    if (numUpdates[i] == -2)
                        System.out.println("Execution " + i +
                                ": unknown number of rows updated");
                    else
                        System.out.println("Execution " + i +
                                "successful: " + numUpdates[i] + " rows updated");
                }
            }
            conBDCep.commit();
        } catch (Exception e) {
            conBDCep.rollback();
            conBDCep.setAutoCommit(true);
            throw e;
        }
    }

    private List<BairroCepVO> consultarBairro(Set<String> bairrosDescricao) throws SQLException {
        List<BairroCepVO> bairros = new ArrayList<>();
        StringBuilder sbBairros = new StringBuilder("SELECT b.codigo as codigo, b.cidadecodigo as cidadecodigo, b.descricao as descricao, c.descricao as cidadedescricao\n" +
                "FROM bairro b\n" +
                "INNER JOIN cidade c on c.codigo = b.cidadecodigo\n" +
                "WHERE b.descricao IN (");
        for (String bairro : bairrosDescricao) {
            sbBairros.append("'").append(bairro.toUpperCase()).append("'").append(",");
        }
        sbBairros.deleteCharAt(sbBairros.length() - 1);
        sbBairros.append(")\n");
        PreparedStatement sqlConsultar = conBDCep.prepareStatement(
                sbBairros.toString()
        );
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while (tabelaResultado.next()) {
            BairroCepVO bairro = new BairroCepVO();
            bairro.setCodigo(tabelaResultado.getInt("codigo"));
            bairro.setCidadeCodigo(tabelaResultado.getInt("cidadecodigo"));
            bairro.setDescricao(tabelaResultado.getString("descricao"));
            bairro.setCidadeDescricao(tabelaResultado.getString("cidadedescricao"));
            bairros.add(bairro);
        }
        return bairros;
    }

    private void insertBairros(Set<BairroCepVO> bairros) throws Exception {
        PreparedStatement ps = null;
        try {
            conBDCep.setAutoCommit(false);
            ps = conBDCep.prepareStatement("INSERT INTO bairro(codigo, cidadecodigo, descricao) " +
                    "values(COALESCE((SELECT MAX(codigo)+1 FROM bairro), 1), (SELECT MAX(codigo) FROM cidade where descricao = ?), ?);");
            Iterator<BairroCepVO> it = bairros.iterator();
            if (bairros.size() == 1) {
                BairroCepVO bairroCepVO = (BairroCepVO) bairros.toArray()[0];
                ps.setString(1, bairroCepVO.getCidadeDescricao().toUpperCase());
                ps.setString(2, bairroCepVO.getDescricao().toUpperCase());
                ps.execute();
            } else {
                while (it.hasNext()) {
                    BairroCepVO bairroCepVO = it.next();
                    ps.setString(1, bairroCepVO.getCidadeDescricao().toUpperCase());
                    ps.setString(2, bairroCepVO.getDescricao().toUpperCase());
                    ps.addBatch();
                }
                int[] numUpdates = ps.executeBatch();
                for (int i = 0; i < numUpdates.length; i++) {
                    if (numUpdates[i] == -2)
                        System.out.println("Execution " + i +
                                ": unknown number of rows updated");
                    else
                        System.out.println("Execution " + i +
                                "successful: " + numUpdates[i] + " rows updated");
                }
            }
            conBDCep.commit();
        } catch (Exception e) {
            conBDCep.rollback();
            conBDCep.setAutoCommit(true);
            throw e;
        }
    }

    private List<EnderecoCepVO> consultarEndereco(Set<String> logradouros, Set<String> ceps) throws SQLException {
        List<EnderecoCepVO> enderecos = new ArrayList<>();
        StringBuilder sbEnderecos = new StringBuilder("SELECT e.*\n" +
                "FROM endereco e\n" +
                "WHERE e.logradouro IN (");
        for (String logradouro : logradouros) {
            sbEnderecos.append("'").append(logradouro.toUpperCase()).append("'").append(",");
        }
        sbEnderecos.deleteCharAt(sbEnderecos.length() - 1);
        sbEnderecos.append(")\n");
        sbEnderecos.append("AND e.cep IN(");
        for (String cep : ceps) {
            sbEnderecos.append("'").append(Uteis.removerMascara(cep)).append("'").append(",");
        }
        sbEnderecos.deleteCharAt(sbEnderecos.length() - 1);
        sbEnderecos.append(")\n");
        PreparedStatement sqlConsultar = conBDCep.prepareStatement(sbEnderecos.toString());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        while (tabelaResultado.next()) {
            EnderecoCepVO endereco = new EnderecoCepVO();
            endereco.setCodigo(tabelaResultado.getInt("codigo"));
            endereco.setBairroCodigo(tabelaResultado.getInt("bairrocodigo"));
            endereco.setCep(tabelaResultado.getString("cep"));
            endereco.setComplemento(tabelaResultado.getString("completo"));
            endereco.setLogradouro(tabelaResultado.getString("logradouro"));
            enderecos.add(endereco);
        }
        return enderecos;
    }

    private void insertEnderecos(Set<EnderecoCepVO> cidades) throws Exception {
        PreparedStatement ps = null;
        try {
            conBDCep.setAutoCommit(false);
            ps = conBDCep.prepareStatement("INSERT INTO endereco(codigo, bairrocodigo, cep, logradouro, completo)" +
                    " values(COALESCE((SELECT MAX(codigo)+1 FROM endereco), 1), " +
                    "(SELECT MAX(b.codigo) FROM bairro b inner join cidade c on b.cidadecodigo = c.codigo where b.descricao = ? and c.descricao = ?), ?, ?, ?);");
            Iterator<EnderecoCepVO> it = cidades.iterator();
            if (cidades.size() == 1) {
                EnderecoCepVO enderecoCepVO = (EnderecoCepVO) cidades.toArray()[0];
                ps.setString(1, enderecoCepVO.getBairroDescricao().toUpperCase());
                ps.setString(2, enderecoCepVO.getCidadeDescricao().toUpperCase());
                ps.setString(3, Uteis.removerMascara(enderecoCepVO.getCep()));
                ps.setString(4, enderecoCepVO.getLogradouro().toUpperCase());
                ps.setString(5, enderecoCepVO.getComplemento().toUpperCase());
                ps.execute();
            } else {
                while (it.hasNext()) {
                    EnderecoCepVO enderecoCepVO = it.next();
                    ps.setString(1, enderecoCepVO.getBairroDescricao().toUpperCase());
                    ps.setString(2, enderecoCepVO.getCidadeDescricao().toUpperCase());
                    ps.setString(3, Uteis.removerMascara(enderecoCepVO.getCep()));
                    ps.setString(4, enderecoCepVO.getLogradouro().toUpperCase());
                    ps.setString(5, enderecoCepVO.getComplemento().toUpperCase());
                    ps.addBatch();
                }
                int[] numUpdates = ps.executeBatch();
                for (int i = 0; i < numUpdates.length; i++) {
                    if (numUpdates[i] == -2)
                        System.out.println("Execution " + i +
                                ": unknown number of rows updated");
                    else
                        System.out.println("Execution " + i +
                                "successful: " + numUpdates[i] + " rows updated");
                }
            }
            conBDCep.commit();
        } catch (Exception e) {
            e.printStackTrace();
            conBDCep.rollback();
            conBDCep.setAutoCommit(true);
            throw e;
        }
    }

    public CidadeCepVO consultarPorCidadeUF(String nomeCidade, String ufSigla) throws Exception {

        inicializar();
        CidadeCepVO cidade = new CidadeCepVO();
        StringBuilder sqlCidades = new StringBuilder();
        sqlCidades.append("SELECT\n");
        sqlCidades.append("\tcid.codigo as codCidade,\n");
        sqlCidades.append("\tcid.descricao as nomeCidade,\n");
        sqlCidades.append("\test.sigla as ufSigla,\n");
        sqlCidades.append("\test.descricao as nomeEstado,\n");
        sqlCidades.append("\test.codigo as codigoEstado\n");
        sqlCidades.append("FROM cidade cid\n");
        sqlCidades.append("INNER JOIN uf AS est ON est.codigo = cid.ufcodigo\n");
        sqlCidades.append("WHERE cid.descricao ILIKE ?\n");
        sqlCidades.append("AND est.sigla ILIKE ?\n");
        sqlCidades.append("LIMIT 1\n");
        PreparedStatement pstm = conBDCep.prepareStatement(sqlCidades.toString());
        pstm.setString(1, nomeCidade);
        pstm.setString(2, ufSigla);
        ResultSet tabelaResultado = pstm.executeQuery();
        if (tabelaResultado.next()) {
            cidade.setCodigo(tabelaResultado.getInt("codCidade"));
            cidade.setDescricao(tabelaResultado.getString("nomeCidade"));
        }
        finalizarConexaoBD();
        return cidade;

    }

}
