package negocio.facade.jdbc.utilitarias;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConfiguracaoSistemaUsuarioVO;
import negocio.comuns.utilitarias.SolicitacaoVO;
import negocio.comuns.utilitarias.StatusSolicitacaoEnum;
import negocio.comuns.utilitarias.TipoSolicitacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.basico.SolicitacaoInterfaceFacade;

import javax.servlet.http.HttpSession;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Solicitacao extends SuperEntidade implements SolicitacaoInterfaceFacade {
    public Solicitacao() throws Exception {
    }

    public Solicitacao(HttpSession session) throws Exception {
        super(session);
    }

    public Solicitacao(Connection conexao) throws Exception {
        super(conexao);
    }

    @Override
    public void incluir(SolicitacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            String sql = "INSERT INTO solicitacao(tipo, datalancamento , dataprocessamento,status,resultado, dadossolicitacao, usuariosolicitante, empresa) VALUES ( ?, ? ,?, ?, ?, ?, ?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            int i = 1;
            sqlInserir.setInt(i++, obj.getTipo().getCodigo());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));
            sqlInserir.setInt(i++, obj.getStatus().getCodigo());
            sqlInserir.setString(i++, obj.getResultado());
            sqlInserir.setString(i++, obj.getDadosSolicitacao());
            sqlInserir.setInt(i++, obj.getUsuarioSolicitante().getCodigo());
            sqlInserir.setInt(i++, obj.getEmpresa().getCodigo());
            sqlInserir.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);

            //realizar chamada integracao
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void alterar(SolicitacaoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConfiguracaoSistemaUsuarioVO.validarDados(obj);
            String sql = " update  solicitacao set tipo = ?, datalancamento = ? , dataprocessamento = ?, status = ?, resultado = ?, dadossolicitacao = ?, usuariosolicitante = ?, empresa = ? where  codigo = ?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            int i = 1;
            sqlAlterar.setInt(i++, obj.getTipo().getCodigo());
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
            sqlAlterar.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataProcessamento()));
            sqlAlterar.setInt(i++, obj.getStatus().getCodigo());
            sqlAlterar.setString(i++, obj.getResultado());
            sqlAlterar.setString(i++, obj.getDadosSolicitacao());
            sqlAlterar.setInt(i++, obj.getUsuarioSolicitante().getCodigo());
            sqlAlterar.setInt(i++, obj.getEmpresa().getCodigo());
            sqlAlterar.setInt(i++, obj.getCodigo());
            sqlAlterar.execute();
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }

    }

    @Override
    public List<SolicitacaoVO> consultarListaSolicitacoes(Integer codigoUsuario, Date dataInicio, Date dataFim, TipoSolicitacaoEnum tipo, StatusSolicitacaoEnum status, int nivelMontarDados, Integer empresa) throws Exception {
        String  sql = "Select * from solicitacao ";
        StringBuilder where = new StringBuilder();
        if(UteisValidacao.notEmptyNumber(codigoUsuario)){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" usuariosolicitante = ").append(codigoUsuario);
        }

        if(UteisValidacao.notEmptyNumber(empresa)){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" empresa = ").append(empresa);
        }

        if(dataInicio != null){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" datalancamento >= '").append(Uteis.getDataFormatoBD(dataInicio)).append(" 00:00:00.000' \n");
        }

        if(dataFim != null){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" datalancamento <= '").append(Uteis.getDataFormatoBD(dataFim)).append(" 23:59:59.999' \n");
        }

        if(tipo != null){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" tipo = ").append(tipo.getCodigo());
        }

        if(status != null){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" status = ").append(status.getCodigo());
        }
        if(where.length() > 0){
            sql += " WHERE "+ where.toString();
        }
        sql += " order by datalancamento desc ";

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    @Override
    public SolicitacaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM solicitacao WHERE codigo= ? ";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            return new SolicitacaoVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public static SolicitacaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        SolicitacaoVO obj = new SolicitacaoVO();

        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setTipo(TipoSolicitacaoEnum.obterPorCodigo(dadosSQL.getInt("tipo")));
        obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
        obj.setDataProcessamento(dadosSQL.getTimestamp("dataprocessamento"));
        obj.setStatus(StatusSolicitacaoEnum.obterPorCodigo(dadosSQL.getInt("status")));
        obj.setResultado(dadosSQL.getString("resultado"));
        obj.setDadosSolicitacao(dadosSQL.getString("dadossolicitacao"));
        obj.getUsuarioSolicitante().setCodigo(dadosSQL.getInt("usuariosolicitante"));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));


        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        montarDadosUsuarioSolicitante(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        montarDadosEmpresa(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        return  obj;
    }

    public static void montarDadosUsuarioSolicitante(SolicitacaoVO  obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getUsuarioSolicitante().getCodigo() == 0) {
            obj.setUsuarioSolicitante(new UsuarioVO());
            return;
        }
        Usuario usuario = new Usuario(con);
        obj.setUsuarioSolicitante(usuario.consultarPorChavePrimaria(obj.getUsuarioSolicitante().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        obj.getUsuarioSolicitante().setEmail(usuario.consultarEmailUsuarioEmail(obj.getUsuarioSolicitante().getCodigo()));
        usuario = null;
    }

    public static void montarDadosEmpresa(SolicitacaoVO  obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getEmpresa().getCodigo() == 0) {
            obj.setEmpresa(new EmpresaVO());
            return;
        }
        Empresa empresaDao = new Empresa(con);
        obj.setEmpresa(empresaDao.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
        empresaDao = null;
    }

    public static List<SolicitacaoVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<SolicitacaoVO> vetResultado = new ArrayList<SolicitacaoVO>();
        while (tabelaResultado.next()) {
            SolicitacaoVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    @Override
    public SolicitacaoVO existeSolicitacaoComMesmoDados(SolicitacaoVO solicitacaoVO) throws Exception {
        String  sql = "Select * from solicitacao ";
        StringBuilder where = new StringBuilder();
        if(UteisValidacao.notEmptyNumber( solicitacaoVO.getUsuarioSolicitante().getCodigo())){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" usuariosolicitante = ").append(solicitacaoVO.getUsuarioSolicitante().getCodigo());
        }


        if(!UteisValidacao.emptyString(solicitacaoVO.getDadosSolicitacao())){
            where.append((where.length() == 0 ? "" : " AND") );
            where.append(" dadosSolicitacao  ilike ? ");
        }
        where.append((where.length() == 0 ? "" : " AND") );
        where.append(" tipo = ").append(solicitacaoVO.getTipo().getCodigo());
        where.append(" AND status in (").append(StatusSolicitacaoEnum.AGUARDANDO_PROCESSAMENTO.getCodigo()).append(",").append(StatusSolicitacaoEnum.PROCESSANDO.getCodigo()).append(",").append(StatusSolicitacaoEnum.CONCLUIDA.getCodigo()).append(") ");



        if(where.length() > 0){
            sql += " WHERE "+ where.toString();
        }
        sql += " order by datalancamento desc ";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            if(!UteisValidacao.emptyString(solicitacaoVO.getDadosSolicitacao())){
                ps.setString(1, solicitacaoVO.getDadosSolicitacao());
            }
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return new SolicitacaoVO();
                }
                return  montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con);

            }
        }
    }
}
