package negocio.facade.jdbc.utilitarias;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.apache.commons.dbcp.ConnectionFactory;
import org.apache.commons.dbcp.DriverManagerConnectionFactory;
import org.apache.commons.dbcp.PoolableConnectionFactory;
import org.apache.commons.dbcp.PoolingDataSource;
import org.apache.commons.pool.ObjectPool;
import org.apache.commons.pool.impl.GenericObjectPool;

/**
 * Classe responsável por obter as conexões.
 * 
 * Ela encapsula varios data source com pooling de conexões.
 * 
 * Um data source para cada empresa.
 * 
 * <AUTHOR>
 *
 */
public class MultiBancoDataSource {
	//mapa <Chave da Empresa, Poolable DS>
	private static Map<String, DataSource> listDataSource = new HashMap<String, DataSource>();
	
	public static Connection getConnection(String key) throws SQLException{
		System.out.println("Obtendo conexão para a chave -> " + key);
		//obtem o data source
		DataSource dataSourceObj = listDataSource.get(key);
		//cria a coneção
		Connection connectionObj = dataSourceObj.getConnection();
		System.out.println("Obtida com sucesso.");
		//retorna a conecção criada
		return connectionObj;
	}
	
	public static void setupDataSource(String key, String connectURI, String user, String senha) {
		System.out.println("CRIAÇÃO DE DATA SOURCE");
		System.out.println("Crianco para a chave -> " + key);
		System.out.println("Sob a connectURI -> " + connectURI);
        // Pool de objetos.
		// Implementação: GenericObjectPool
        ObjectPool connectionPool = new GenericObjectPool(null);
        // Fábrica que o poon usará para criar as conexões.
        ConnectionFactory connectionFactory = new DriverManagerConnectionFactory(connectURI, user, senha);
        // Encapsulamento das conecções criadas pela fabrica acima.
        PoolableConnectionFactory poolableConnectionFactory = new PoolableConnectionFactory(connectionFactory,connectionPool,null,null,false,true);
        // O Data Source com o recurso de pool.
        PoolingDataSource dataSourceObj = new PoolingDataSource(connectionPool);
        listDataSource.put(key, dataSourceObj);
        System.out.println("Criado com sucesso!");
    }
}
