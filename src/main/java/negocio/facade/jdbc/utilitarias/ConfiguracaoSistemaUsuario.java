package negocio.facade.jdbc.utilitarias;

import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.ConfiguracaoSistemaUsuarioVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ConfiguracaoSistemaUsuarioInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import negocio.comuns.basico.enumerador.ConfiguracaoUsuarioEnum;

/**
 * Created by Rafael on 27/02/2016.
 */
public class ConfiguracaoSistemaUsuario extends SuperEntidade  implements ConfiguracaoSistemaUsuarioInterfaceFacade {

    public ConfiguracaoSistemaUsuario() throws Exception {
    }
    public ConfiguracaoSistemaUsuario(Connection conexao) throws Exception {
        super(conexao);
    }

    /**
     * Operação responsável por retornar um novo objeto da classe
     * <code>ConfiguracaoSistemaUsuarioVO</code>.
     */
    public ConfiguracaoSistemaUsuarioVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ConfiguracaoSistemaUsuarioVO();
    }

    @Override
    public void incluir(ConfiguracaoSistemaUsuarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConfiguracaoSistemaUsuarioVO.validarDados(obj);
            String sql = "INSERT INTO ConfiguracaoSistemaUsuario( usuario, dataCadastro ,valor, tipo ) VALUES ( ?, ? ,?, ?)";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getUsuario().getCodigo());
            sqlInserir.setTimestamp(2, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
            sqlInserir.setString(3, obj.getValor());
            sqlInserir.setInt(4, obj.getTipo().ordinal());
            sqlInserir.execute();
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    @Override
    public void alterar(ConfiguracaoSistemaUsuarioVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            ConfiguracaoSistemaUsuarioVO.validarDados(obj);
            String sql = "UPDATE ConfiguracaoSistemaUsuario SET usuario = ?, valor = ?, tipo = ? WHERE codigo = ?";
            PreparedStatement sqlInserir = con.prepareStatement(sql);
            sqlInserir.setInt(1, obj.getUsuario().getCodigo());
            sqlInserir.setString(3, obj.getValor());
            sqlInserir.setInt(4, obj.getTipo().ordinal());
            sqlInserir.setInt(5, obj.getCodigo());
            sqlInserir.execute();
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
    public static ConfiguracaoSistemaUsuarioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ConfiguracaoSistemaUsuarioVO obj = new ConfiguracaoSistemaUsuarioVO();
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            obj.setCodigo(dadosSQL.getInt("codigo"));
            obj.setValor(dadosSQL.getString("valor"));
            obj.getUsuario().setCodigo(dadosSQL.getInt("usuario"));
            obj.setDataRegistro(dadosSQL.getDate("dataCadastro"));
            obj.setTipo(ConfiguracaoUsuarioEnum.getFromOrdinal(dadosSQL.getInt("tipo")));
        }
        return obj;
    }
    @Override
    public ConfiguracaoSistemaUsuarioVO consultarUltimoPorCodigoUsuario(int codigoUsuario, ConfiguracaoUsuarioEnum tipo) throws Exception {

        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ConfiguracaoSistemaUsuario WHERE usuario = ? and tipo = ? order by  dataCadastro desc limit 1";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoUsuario);
        sqlConsultar.setInt(2, tipo.ordinal());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
           return new ConfiguracaoSistemaUsuarioVO();
        }
        return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, this.con));

    }

}
