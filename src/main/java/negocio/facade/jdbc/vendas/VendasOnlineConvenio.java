package negocio.facade.jdbc.vendas;

import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.vendas.VendasOnlineConvenioInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 23/06/2020
 */
public class VendasOnlineConvenio extends SuperEntidade implements VendasOnlineConvenioInterfaceFacade {

    public VendasOnlineConvenio() throws Exception {
        super();
    }

    public VendasOnlineConvenio(Connection con) throws Exception {
        super(con);
    }

    public void gravar(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO, List<VendasOnlineConvenioVO> listaAtual, boolean formaPagamento) throws Exception {
        try {
            con.setAutoCommit(false);

            List<VendasOnlineConvenioVO> listaAnterior = consultarPorEmpresa(vendasConfigVO.getEmpresa(), false, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, formaPagamento);

            List<VendasOnlineConvenioVO> listaExcluido = new ArrayList<>();
            List<VendasOnlineConvenioVO> listaAdicionado = new ArrayList<>();


            //verificar os itens que foram removidos
            for (VendasOnlineConvenioVO objAnt : listaAnterior) {
                boolean naoExisteMais = true;
                for (VendasOnlineConvenioVO objAtu : listaAtual) {
                    if (objAnt.getCodigo().equals(objAtu.getCodigo())) {
                        naoExisteMais = false;
                        break;
                    }
                }
                if (naoExisteMais) {
                    listaExcluido.add(objAnt);
                }
            }

            excluirPorEmpresa(vendasConfigVO.getEmpresa(), formaPagamento);

            for (VendasOnlineConvenioVO obj : listaAtual) {
                if (UteisValidacao.emptyNumber(obj.getCodigo())) {
                    listaAdicionado.add(obj);
                }
                if(formaPagamento && existeIgual(obj)){
                    throw new Exception("Já existe uma configuração de forma de pagamento por plano ou produto para a forma de pagamento adicionada");
                }
                incluir(obj, formaPagamento);
            }


            //verificar se existe algum conflito
            if (!formaPagamento) {
                List<VendasOnlineConvenioVO> listaConflito = new ArrayList<>();
                for (VendasOnlineConvenioVO obj : listaAtual) {
                    validarExisteOutro(obj);
                    if (!UteisValidacao.emptyNumber(obj.getConvenioCobrancaConflito().getCodigo())) {
                        listaConflito.add(obj);
                    }
                }
                if (!UteisValidacao.emptyList(listaConflito)) {
                    StringBuilder conflito = new StringBuilder();
                    conflito.append("Os seguintes configurações do vendas online já estão adicionados em outro Convênio de Cobrança: \n");
                    for (VendasOnlineConvenioVO obj : listaConflito) {
                        conflito.append("Convênio: ").append(obj.getConvenioCobrancaConflito().getDescricao());
                        if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
                            conflito.append(" | Plano: ").append(obj.getPlanoVO().getDescricao());
                        }
                        if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
                            conflito.append(" | Produto: ").append(obj.getProdutoVO().getDescricao());
                        }
                        conflito.append(" \n");
                    }
                    throw new Exception(conflito.toString());
                }
            }

            if (!UteisValidacao.emptyList(listaAdicionado) || !UteisValidacao.emptyList(listaExcluido)) {
                gravarLog(vendasConfigVO, usuarioVO, listaAdicionado, listaExcluido);
            }

            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private boolean existeIgual(VendasOnlineConvenioVO obj) throws Exception {
        String sql = "select * from VendasOnlineConvenio where 1=1 ";
        if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            sql += " and empresa = " + obj.getEmpresaVO().getCodigo();
        }
        if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
            sql += " and plano = " + obj.getPlanoVO().getCodigo();
        }
        if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
            sql += " and produto = " + obj.getProdutoVO().getCodigo();
        }
        sql += " and formaPagamento = " + obj.getFormaPagamento();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                if (rs.next()) {
                    return true;
                }
            }
        }
        return false;
    }

    private void validarExisteOutro(VendasOnlineConvenioVO obj) throws Exception {
        obj.setConvenioCobrancaConflito(null);
        String sql = "select * from VendasOnlineConvenio where codigo <> " + obj.getCodigo();
        if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            sql += " and empresa = " + obj.getEmpresaVO().getCodigo();
        }
        if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
            sql += " and plano = " + obj.getPlanoVO().getCodigo();
        }
        if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
            sql += " and produto = " + obj.getProdutoVO().getCodigo();
        }
        sql += " and ((formaPagamento is null) or formaPagamento = 0) ";
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql)) {
                while (rs.next()) {
                    ConvenioCobranca dao = new ConvenioCobranca(this.con);
                    obj.setConvenioCobrancaConflito(dao.consultarPorChavePrimaria(rs.getInt("conveniocobranca"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    dao = null;
                }
            }
        }
    }

    private void gravarLog(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO,
                           List<VendasOnlineConvenioVO> listaAdicionado, List<VendasOnlineConvenioVO> listaExcluido) throws Exception {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("VENDASCONFIG");
            log.setNomeEntidadeDescricao("VENDASCONFIG");
            log.setDescricao("VENDASCONFIG");
            log.setChavePrimaria(vendasConfigVO.getCodigo().toString());
            log.setDataAlteracao(Calendario.hoje());
            log.setUsuarioVO(usuarioVO);
            log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
            log.setOperacao("ALTERAÇÃO");
            log.setNomeCampo("VENDASONLINECONVENIO");
            log.setUserOAMD(usuarioVO.getUserOamd());
            log.setValorCampoAnterior("");

            JSONObject json = new JSONObject();
            if (!UteisValidacao.emptyList(listaAdicionado)) {
                json.put("adicionado", obterArrayLog(listaAdicionado));
            }
            if (!UteisValidacao.emptyList(listaExcluido)) {
                json.put("excluido", obterArrayLog(listaExcluido));
            }
            log.setValorCampoAlterado(json.toString());

            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(log);
            logDAO = null;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    private JSONArray obterArrayLog(List<VendasOnlineConvenioVO> lista) {
        JSONArray array = new JSONArray();
        for (VendasOnlineConvenioVO rateioVO : lista) {
            JSONObject rateio = new JSONObject();
            rateio.put("convenio", rateioVO.getConvenioCobrancaVO().getCodigo());
            rateio.put("convenioDescricao", rateioVO.getConvenioCobrancaVO().getDescricao());
            if (!UteisValidacao.emptyNumber(rateioVO.getPlanoVO().getCodigo())) {
                rateio.put("plano", rateioVO.getPlanoVO().getCodigo());
                rateio.put("planoDescricao", rateioVO.getPlanoVO().getDescricao());
            }
            if (!UteisValidacao.emptyNumber(rateioVO.getProdutoVO().getCodigo())) {
                rateio.put("produto", rateioVO.getProdutoVO().getCodigo());
                rateio.put("produtoDescricao", rateioVO.getProdutoVO().getDescricao());
            }
            rateio.put("empresa", rateioVO.getEmpresaVO().getCodigo());
            rateio.put("empresaNome", rateioVO.getEmpresaVO().getNome());
            array.put(rateio);
        }
        return array;
    }

    private void incluir(VendasOnlineConvenioVO obj, boolean formaPagamento) throws Exception {
        VendasOnlineConvenioVO.validarDados(obj, formaPagamento);
        String insert = "INSERT INTO VendasOnlineConvenio(dataregistro, conveniocobranca, empresa, produto, plano, tipoParcelamentoStone, formaPagamento) VALUES (?, ?, ?, ?, ?, ?, ?);";
        try (PreparedStatement ps = con.prepareStatement(insert)) {
            int i = 0;
            ps.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            resolveFKNull(ps, ++i, obj.getConvenioCobrancaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getEmpresaVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getProdutoVO().getCodigo());
            resolveFKNull(ps, ++i, obj.getPlanoVO().getCodigo());
            ps.setString(++i, obj.getTipoParcelamentoStone());
            resolveFKNull(ps, ++i, obj.getFormaPagamento());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    private void excluirPorEmpresa(Integer empresa, boolean formaPagamento) throws Exception {
        String sql = "DELETE FROM VendasOnlineConvenio WHERE empresa = ?";
        if (formaPagamento) {
            sql += " and formaPagamento is not null";
        } else {
            sql += " and ((formaPagamento is null) or formaPagamento = 0)";
        }
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, empresa);
            ps.execute();
        }
    }

    public List<VendasOnlineConvenioVO> consultarPorEmpresa(Integer empresa, boolean somenteConvenioAtivo, int nivelMontarDados, boolean formaPagamento) throws Exception {
        if (UteisValidacao.emptyNumber(empresa)) {
            return new ArrayList<>();
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cp.* \n");
        sql.append("from VendasOnlineConvenio cp \n");
        sql.append("left join conveniocobranca cc on cc.codigo = cp.conveniocobranca \n");
        sql.append("where cp.empresa = ").append(empresa).append(" \n");
        if (!formaPagamento && somenteConvenioAtivo) {
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
        }
        if (formaPagamento) {
            sql.append("and cp.formaPagamento is not null \n");
        }else{
            sql.append("and ((cp.formaPagamento is null) or cp.formaPagamento = 0)\n");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                return montarDadosConulta(rs, nivelMontarDados, this.con);
            }
        }
    }

    private List<VendasOnlineConvenioVO> montarDadosConulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<VendasOnlineConvenioVO> lista = new ArrayList<>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivelMontarDados, con));
        }
        return lista;
    }

    private VendasOnlineConvenioVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        VendasOnlineConvenioVO obj = new VendasOnlineConvenioVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDataRegistro(rs.getTimestamp("dataregistro"));
        obj.getConvenioCobrancaVO().setCodigo(rs.getInt("convenioCobranca"));
        obj.getEmpresaVO().setCodigo(rs.getInt("empresa"));
        obj.getProdutoVO().setCodigo(rs.getInt("produto"));
        obj.getPlanoVO().setCodigo(rs.getInt("plano"));
        obj.setTipoParcelamentoStone(rs.getString("tipoParcelamentoStone"));
        obj.setFormaPagamento(rs.getInt("formaPagamento"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosConvenioCobranca(obj, con);
            montarDadosEmpresa(obj, con);
            montarDadosPlano(obj, con);
            montarDadosProduto(obj, con);
        }
        return obj;
    }

    private void montarDadosConvenioCobranca(VendasOnlineConvenioVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            ConvenioCobranca dao = new ConvenioCobranca(con);
            obj.setConvenioCobrancaVO(dao.consultarPorChavePrimaria(obj.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            dao = null;
        }
    }

    private void montarDadosEmpresa(VendasOnlineConvenioVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            Empresa dao = new Empresa(con);
            obj.setEmpresaVO(dao.consultarPorChavePrimaria(obj.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA));
            dao = null;
        }
    }

    private void montarDadosPlano(VendasOnlineConvenioVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getPlanoVO().getCodigo())) {
            Plano dao = new Plano(con);
            obj.setPlanoVO(dao.consultarPorChavePrimaria(obj.getPlanoVO().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            dao = null;
        }
    }

    private void montarDadosProduto(VendasOnlineConvenioVO obj, Connection con) throws Exception {
        if (!UteisValidacao.emptyNumber(obj.getProdutoVO().getCodigo())) {
            Produto dao = new Produto(con);
            obj.setProdutoVO(dao.consultarPorChavePrimaria(obj.getProdutoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            dao = null;
        }
    }

    public VendasOnlineConvenioVO consultarPorProdutoOuPlano(Integer empresa, boolean produto, Integer idConsulta, boolean somenteConvenioAtivo, int nivelMontarDados, boolean formaPagamento) throws Exception {
        if (UteisValidacao.emptyNumber(empresa)) {
            return new VendasOnlineConvenioVO();
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cp.* \n");
        sql.append("from VendasOnlineConvenio cp \n");
        sql.append("left join conveniocobranca cc on cc.codigo = cp.conveniocobranca \n");
        sql.append("where cp.empresa = ").append(empresa).append(" \n");
        if (!formaPagamento && somenteConvenioAtivo) {
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
        }
        if (produto){
            sql.append("and cp.produto = ").append(idConsulta).append(" \n");
        } else {
            sql.append("and cp.plano = ").append(idConsulta).append(" \n");
        }
        if (formaPagamento) {
            sql.append("and cp.formaPagamento is not null \n");
        } else {
            sql.append("and ((cp.formaPagamento is null) or cp.formaPagamento = 0)\n");
        }
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, this.con);
                }
                return null;
            }
        }
    }

    public List<VendasOnlineConvenioVO> consultarPorProdutoOuPlano(Integer empresa, boolean produto, Integer idConsulta, boolean somenteConvenioAtivo, int nivelMontarDados, boolean formaPagamento, Integer idForma) throws Exception {
        if (UteisValidacao.emptyNumber(empresa)) {
            return new ArrayList<>();
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cp.* \n");
        sql.append("from VendasOnlineConvenio cp \n");
        sql.append("left join conveniocobranca cc on cc.codigo = cp.conveniocobranca \n");
        sql.append("where cp.empresa = ").append(empresa).append(" \n");
        if (!formaPagamento && somenteConvenioAtivo) {
            sql.append("and cc.situacao = ").append(SituacaoConvenioCobranca.ATIVO.getCodigo()).append(" \n");
        }
        if (produto){
            sql.append("and cp.produto = ").append(idConsulta).append(" \n");
        } else {
            sql.append("and cp.plano = ").append(idConsulta).append(" \n");
        }
        if (formaPagamento) {
            if(!UteisValidacao.emptyNumber(idForma)) {
                sql.append("and cp.formaPagamento = "+idForma+" \n");
            }else{
                sql.append("and cp.formaPagamento is not null \n");
            }
        } else {
            sql.append("and ((cp.formaPagamento is null) or cp.formaPagamento = 0)\n");
        }

        List<VendasOnlineConvenioVO> list = new ArrayList<>();
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    list.add(montarDados(rs, nivelMontarDados, this.con));
                }
            }
        }
        return list;
    }

}
