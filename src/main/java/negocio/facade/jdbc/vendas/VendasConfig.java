package negocio.facade.jdbc.vendas;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.TipoAgrupamentoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import controle.arquitetura.security.LoginControle;
import kong.unirest.Config;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmailPagamentoTO;
import negocio.comuns.basico.FormaPagamentoPlanoProdutoTO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.vendas.VendasConfigInterfaceFacade;
import servicos.vendasonline.VendasOnlineService;

import javax.servlet.http.HttpSession;
import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class VendasConfig extends SuperEntidade implements VendasConfigInterfaceFacade {

    public VendasConfig() throws Exception {
    }

    public VendasConfig(HttpSession session) throws Exception {
        super(session);
    }

    public VendasConfig(Connection conexao) throws Exception {
        super(conexao);
    }

    public VendasConfigVO config(Integer empresa) throws Exception {
        ResultSet resultSet = criarConsulta("select * from vendasonlineconfig where empresa = " + empresa, con);
        VendasConfigVO vendas = new VendasConfigVO(empresa);
        if (resultSet.next()) {
            vendas.setCodigo(resultSet.getInt("codigo"));
            vendas.setRedirecionarApp(resultSet.getBoolean("redirecionarapp"));
            vendas.setPaginaRedirecionar(resultSet.getString("paginaredirecionar"));
            vendas.setUrlvenda(resultSet.getString("urlvenda"));
            vendas.setCor(resultSet.getString("cor"));
            vendas.setEmailAvisar(resultSet.getString("emailavisar"));
            vendas.setCamposAdicionais(resultSet.getString("camposadicionais"));
            vendas.setCamposAdicionaisProduto(resultSet.getString("camposadicionaisproduto"));
            vendas.setCobrarPrimeiraParcelaCompra(resultSet.getBoolean("cobrarprimeiraparcelacompra"));
            vendas.setCobrarPrimeiraParcelaCompraRenovacao(resultSet.getBoolean("cobrarPrimeiraParcelaCompraRenovacao"));
            vendas.setDetalharParcelaTelaCheckout(resultSet.getBoolean("detalharParcelaTelaCheckout"));
            vendas.setCobrarProdutosJuntoAdesao(resultSet.getBoolean("cobrarProdutosJuntoAdesao"));
            vendas.setTituloCheckout(resultSet.getString("tituloCheckout"));
            vendas.getConsultorSite().setCodigo(resultSet.getInt("consultorSite"));
            vendas.setApresentarValorAnuidade(resultSet.getBoolean("apresentarvaloranuidade"));
            vendas.setApresentarProdutoSemEstoque(resultSet.getBoolean("apresentarProdutoSemEstoque"));
            vendas.setUsarConvenioPlanoProduto(resultSet.getBoolean("usarConvenioPlanoProduto"));
            vendas.setUsarFormaPagamentoPlanoProduto(resultSet.getBoolean("usarFormaPagamentoPlanoProduto"));
            vendas.setApresentarCPFLinkPag(resultSet.getBoolean("apresentarCPFLinkPag"));
            vendas.setApresentarDtFaturaLinkPag(resultSet.getBoolean("apresentarDtFaturaLinkPag"));
            vendas.setApresentarTermoAceiteLinkPag(resultSet.getBoolean("apresentarTermoAceiteLinkPag"));
            vendas.setAnalyticsId(resultSet.getString("analyticsId"));
            vendas.setPixelId(resultSet.getString("pixelId"));
            vendas.setTokenApiConversao(resultSet.getString("tokenApiConversao"));
            vendas.setRenovarContratoAntigo(resultSet.getBoolean("renovarContratoAntigo"));
            vendas.setCobrarParcelasMesSeguinteRenovacao(resultSet.getBoolean("cobrarParcelasMesSeguinteRenovacao"));
            vendas.setPagarUsandoSaldoCliente(resultSet.getBoolean("pagarUsandoSaldoCliente"));
            vendas.setHabilitarCampanha(resultSet.getBoolean("habilitarCampanha"));
            vendas.setTemaClaro(resultSet.getBoolean("temaClaro"));
            vendas.setTemaEscuro(resultSet.getBoolean("temaEscuro"));
            vendas.getConvenioCobrancaPixVO().setCodigo(resultSet.getInt("conveniocobrancapix"));
            vendas.getConvenioCobrancaBoletoVO().setCodigo(resultSet.getInt("conveniocobrancaboleto"));
            vendas.setGoogleTagId(resultSet.getString("googleTagId"));
            vendas.setGoogleTagIdHotsite(resultSet.getString("googleTagIdHotsite"));
            vendas.setIntegracaoBotConversa(resultSet.getBoolean("integracaoBotConversa"));
            vendas.setEnderecoEnviarAcoesBotConversa(resultSet.getString("enderecoEnviarAcoesBotConversa"));
            vendas.setGerarBoletoTodasParcelas(resultSet.getBoolean("gerarBoletoTodasParcelas"));
            vendas.setCriarAutorizacaoCobrancaPix(resultSet.getBoolean("criarAutorizacaoCobrancaPix"));
            vendas.setCriarAutorizacaoCobrancaBoleto(resultSet.getBoolean("criarAutorizacaoCobrancaBoleto"));
            vendas.setDiasVencimentoBoleto(resultSet.getInt("diasVencimentoBoleto"));
            vendas.setIncluirCategoriaPix(resultSet.getBoolean("incluirCategoriaPix"));
            vendas.setEstornarVendaPix(resultSet.getBoolean("estornarVendaPix"));
            vendas.setDominioProprioHotsite(resultSet.getBoolean("dominioProprioHotsite"));
            vendas.setPermitirMudarTipoParcelamento(resultSet.getBoolean("permitirMudarTipoParcelamento"));
            vendas.setApresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano(resultSet.getBoolean("apresentarvalortotaldoplanonateladeselecaodoplano"));
            vendas.setPermiteContratoConcomitanteComParcelaEmAberto(resultSet.getBoolean("permiteContratoConcomitanteComParcelaEmAberto"));
            vendas.getCampanhaCupomDescontoIndicacoes().setId(resultSet.getInt("campanhaCupomDescontoIndicacoes"));
            vendas.setPermiteVendaProdutoAlunoOutraUnidade(resultSet.getBoolean("permitevendaprodutoalunooutraunidade"));
            vendas.setExibirTipoDocumentoTelaVendasOnline(resultSet.getBoolean("exibirTipoDocumentoTelaVendasOnline"));
            vendas.setHabilitarAgendamentoAulaExperimentalLinkVisitante(resultSet.getBoolean("habilitarAgendamentoAulaExperimentalLinkVisitante"));
            vendas.setEnviarEmailUsuarioMovelAutomaticamente(resultSet.getBoolean("enviarEmailUsuarioMovelAutomaticamente"));
            vendas.setHabilitarPreCadastro(resultSet.getBoolean("habilitarprecadastro"));
            vendas.setPrimeiraCobrancaPixEGuardarCartao(resultSet.getBoolean("primeiraCobrancaPixEGuardarCartao"));
            vendas.setExibirBotaoAgendaSobreBanner(resultSet.getBoolean("exibirBotaoAgendaSobreBanner"));
            vendas.setModalidadesIniciarSelecionadasContratoTurma(resultSet.getBoolean("modalidadesIniciarSelecionadasContratoTurma"));
            vendas.setAtivarLinksGooglePlayEAppleStore(resultSet.getBoolean("ativarLinksGooglePlayEAppleStore"));
            vendas.setUrlLinkGooglePlay(resultSet.getString("urlLinkGooglePlay"));
            vendas.setUrlLinkAppleStore(resultSet.getString("urlLinkAppleStore"));
            vendas.setPermiteProsseguirMesmoCpfCadastroVisitante(resultSet.getBoolean("permiteProsseguirMesmoCpfOuEmailCadastroVisitante"));
            vendas.setCamposAdicionaisPlanoFlow(resultSet.getString("camposadicionaisplanoflow"));
            vendas.setCamposAdicionaisProdutoFlow(resultSet.getString("camposadicionaisprodutoflow"));
            vendas.setExibeDataUtilizacaoDiaria(resultSet.getBoolean("exibedatautilizacao"));
        }
        return vendas;

    }

    public List<FormaPagamentoPlanoProdutoTO> obterVendasOnlineConvenioConfigs(Integer empresa, boolean formaPagamento) throws Exception {
        String sql = "select * from VendasOnlineConvenio where empresa = " + empresa;
        sql += formaPagamento ? " and formaPagamento is not null " : " and ((formaPagamento is null) or formaPagamento = 0) ";
        ResultSet resultSet = criarConsulta(sql, con);
        List<FormaPagamentoPlanoProdutoTO> vendasFormaConfigs = new ArrayList<>();
        while (resultSet.next()) {
            FormaPagamentoPlanoProdutoTO vendasFormaConfig = new FormaPagamentoPlanoProdutoTO();
            vendasFormaConfig.setCodigo(resultSet.getInt("codigo"));
            vendasFormaConfig.setEmpresa(resultSet.getInt("empresa"));
            vendasFormaConfig.setProduto(resultSet.getInt("produto"));
            vendasFormaConfig.setPlano(resultSet.getInt("plano"));
            vendasFormaConfig.setFormaPagamento(resultSet.getInt("formaPagamento"));
            vendasFormaConfigs.add(vendasFormaConfig);
        }
        return vendasFormaConfigs;

    }

    public void gravarConfig(VendasConfigVO vendasConfigVO) throws Exception {
        ResultSet resultSet = criarConsulta("select codigo from vendasonlineconfig where empresa = " + vendasConfigVO.getEmpresa(), con);
        if (!resultSet.next()) {
            String sqlInsert = "insert into vendasonlineconfig (empresa) values (?);";
            try (PreparedStatement stm = con.prepareStatement(sqlInsert, Statement.RETURN_GENERATED_KEYS)) {
                stm.setInt(1, vendasConfigVO.getEmpresa());
                stm.execute();
                ResultSet rs = stm.getGeneratedKeys();
                if (rs.next()) {
                    vendasConfigVO.setCodigo(rs.getInt("codigo"));
                }
                vendasConfigVO.setNovoObj(false);
            }
        }

        String sqlUpdate = "UPDATE vendasonlineconfig " +
                " SET redirecionarapp=?, paginaredirecionar=?, cor = ?, urlvenda = ?, emailavisar = ?, camposadicionais = ?, CobrarPrimeiraParcelaCompra = ?, cobrarPrimeiraParcelaCompraRenovacao = ?, " +
                "detalharParcelaTelaCheckout = ?, cobrarProdutosJuntoAdesao = ?, tituloCheckout = ?, consultorSite = ?, apresentarvaloranuidade=?, apresentarProdutoSemEstoque = ?, " +
                "usarConvenioPlanoProduto = ?, usarFormaPagamentoPlanoProduto = ?, apresentarCPFLinkPag = ?, apresentarDtFaturaLinkPag = ?, apresentarTermoAceiteLinkPag = ?, analyticsId = ?, pixelId = ?, tokenApiConversao = ?, " +
                "renovarContratoAntigo = ?,  cobrarParcelasMesSeguinteRenovacao = ?, pagarUsandoSaldoCliente = ?, temaClaro = ?, temaEscuro = ?, conveniocobrancapix = ?, googleTagId= ?, habilitarCampanha=?, " +
                "integracaoBotConversa = ?, enderecoEnviarAcoesBotConversa = ?, conveniocobrancaboleto = ?, gerarBoletoTodasParcelas = ?, criarAutorizacaoCobrancaPix = ?, criarAutorizacaoCobrancaBoleto = ?, " +
                "diasVencimentoBoleto = ?, incluirCategoriaPix = ?, estornarVendaPix = ?, permitirMudarTipoParcelamento = ?, apresentarvalortotaldoplanonateladeselecaodoplano = ?, " +
                "permiteContratoConcomitanteComParcelaEmAberto = ?, campanhaCupomDescontoIndicacoes = ?, permitevendaprodutoalunooutraunidade = ?, exibirTipoDocumentoTelaVendasOnline = ?, " +
                "camposadicionaisproduto = ?, habilitarAgendamentoAulaExperimentalLinkVisitante = ?, enviarEmailUsuarioMovelAutomaticamente = ?, habilitarPreCadastro = ?, configSescHabilitada = ?, " +
                "primeiraCobrancaPixEGuardarCartao = ?, exibirBotaoAgendaSobreBanner = ?, modalidadesIniciarSelecionadasContratoTurma = ?, " +
                "ativarLinksGooglePlayEAppleStore = ?, urlLinkGooglePlay = ?, urlLinkAppleStore = ?, permiteProsseguirMesmoCpfOuEmailCadastroVisitante = ?, camposadicionaisplanoflow = ?, camposadicionaisprodutoflow = ?, " +
                "exibedatautilizacao = ? " +
                " WHERE empresa = ? ;";
        try (PreparedStatement stm = con.prepareStatement(sqlUpdate)) {
            int i = 0;
            stm.setBoolean(++i, vendasConfigVO.getRedirecionarApp());
            stm.setString(++i, vendasConfigVO.getPaginaRedirecionar());
            stm.setString(++i, vendasConfigVO.getCor());
            stm.setString(++i, vendasConfigVO.getUrlvenda());
            stm.setString(++i, vendasConfigVO.getEmailAvisar());
            stm.setString(++i, vendasConfigVO.getCamposAdicionais());
            stm.setBoolean(++i, vendasConfigVO.getCobrarPrimeiraParcelaCompra());
            stm.setBoolean(++i, vendasConfigVO.isCobrarPrimeiraParcelaCompraRenovacao());
            stm.setBoolean(++i, vendasConfigVO.getDetalharParcelaTelaCheckout());
            stm.setBoolean(++i, vendasConfigVO.getCobrarProdutosJuntoAdesao());
            stm.setString(++i, vendasConfigVO.getTituloCheckout());
            stm.setInt(++i, vendasConfigVO.getConsultorSite().getCodigo());
            stm.setBoolean(++i, vendasConfigVO.getApresentarValorAnuidade());
            stm.setBoolean(++i, vendasConfigVO.isApresentarProdutoSemEstoque());
            stm.setBoolean(++i, vendasConfigVO.isUsarConvenioPlanoProduto());
            stm.setBoolean(++i, vendasConfigVO.isUsarFormaPagamentoPlanoProduto());
            stm.setBoolean(++i, vendasConfigVO.isApresentarCPFLinkPag());
            stm.setBoolean(++i, vendasConfigVO.isApresentarDtFaturaLinkPag());
            stm.setBoolean(++i, vendasConfigVO.isApresentarTermoAceiteLinkPag());
            stm.setString(++i, vendasConfigVO.getAnalyticsId());
            stm.setString(++i, vendasConfigVO.getPixelId());
            stm.setString(++i, vendasConfigVO.getTokenApiConversao());
            stm.setBoolean(++i, vendasConfigVO.isRenovarContratoAntigo());
            stm.setBoolean(++i, vendasConfigVO.isCobrarParcelasMesSeguinteRenovacao());
            stm.setBoolean(++i, vendasConfigVO.isPagarUsandoSaldoCliente());
            stm.setBoolean(++i, vendasConfigVO.isTemaClaro());
            stm.setBoolean(++i, vendasConfigVO.isTemaEscuro());
            resolveIntegerNull(stm, ++i, vendasConfigVO.getConvenioCobrancaPixVO().getCodigo());
            stm.setString(++i, vendasConfigVO.getGoogleTagId());
            stm.setBoolean(++i, vendasConfigVO.isHabilitarCampanha());
            stm.setBoolean(++i, vendasConfigVO.isIntegracaoBotConversa());
            stm.setString(++i, vendasConfigVO.getEnderecoEnviarAcoesBotConversa());
            resolveIntegerNull(stm, ++i, vendasConfigVO.getConvenioCobrancaBoletoVO().getCodigo());
            stm.setBoolean(++i, vendasConfigVO.isGerarBoletoTodasParcelas());
            stm.setBoolean(++i, vendasConfigVO.isCriarAutorizacaoCobrancaPix());
            stm.setBoolean(++i, vendasConfigVO.isCriarAutorizacaoCobrancaBoleto());
            stm.setInt(++i, vendasConfigVO.getDiasVencimentoBoleto());
            stm.setBoolean(++i, vendasConfigVO.isIncluirCategoriaPix());
            stm.setBoolean(++i, vendasConfigVO.isEstornarVendaPix());
            stm.setBoolean(++i, vendasConfigVO.isPermitirMudarTipoParcelamento());
            stm.setBoolean(++i, vendasConfigVO.isApresentarValorTotalDoPlanoNaTelaDeSelecaoDoPlano());
            stm.setBoolean(++i, vendasConfigVO.isPermiteContratoConcomitanteComParcelaEmAberto());
            resolveIntegerNull(stm, ++i, vendasConfigVO.getCampanhaCupomDescontoIndicacoes().getId());
            stm.setBoolean(++i, vendasConfigVO.isPermiteVendaProdutoAlunoOutraUnidade());
            stm.setBoolean(++i, vendasConfigVO.isExibirTipoDocumentoTelaVendasOnline());
            stm.setString(++i, vendasConfigVO.getCamposAdicionaisProduto());
            stm.setBoolean(++i, vendasConfigVO.isHabilitarAgendamentoAulaExperimentalLinkVisitante());
            stm.setBoolean(++i, vendasConfigVO.isEnviarEmailUsuarioMovelAutomaticamente());
            stm.setBoolean(++i, vendasConfigVO.isHabilitarPreCadastro());
            stm.setBoolean(++i, vendasConfigVO.isConfigSescHabilitada());
            stm.setBoolean(++i, vendasConfigVO.isPrimeiraCobrancaPixEGuardarCartao());
            stm.setBoolean(++i, vendasConfigVO.isExibirBotaoAgendaSobreBanner());
            stm.setBoolean(++i, vendasConfigVO.isModalidadesIniciarSelecionadasContratoTurma());
            stm.setBoolean(++i, vendasConfigVO.isAtivarLinksGooglePlayEAppleStore());
            stm.setString(++i, vendasConfigVO.getUrlLinkGooglePlay());
            stm.setString(++i, vendasConfigVO.getUrlLinkAppleStore());
            stm.setBoolean(++i, vendasConfigVO.isPermiteProsseguirMesmoCpfCadastroVisitante());
            stm.setString(++i, vendasConfigVO.getCamposAdicionaisPlanoFlow());
            stm.setString(++i, vendasConfigVO.getCamposAdicionaisProdutoFlow());
            stm.setBoolean(++i, vendasConfigVO.isExibeDataUtilizacaoDiaria());

            stm.setInt(++i, vendasConfigVO.getEmpresa());
            stm.execute();
        }
    }

    public void gravarPaginaInicial(PaginaInicialVendasOnlineVO pagInicialVendasOnline, Integer posicao) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(pagInicialVendasOnline.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into paginainicialvendasonline (codVendasOnlineConfig, textoBoxArredondado, tituloPrincipal, fotoKey, posicao) values (?, ?, ?, ?, ?)");
            stm.setInt(1, pagInicialVendasOnline.getCodVendasOnlineConfig());
            stm.setString(2, pagInicialVendasOnline.getTextoBoxArredondado());
            stm.setString(3, pagInicialVendasOnline.getTituloPrincipal());
            stm.setString(4, pagInicialVendasOnline.getFotoKey());
            stm.setInt(5, posicao);
            stm.execute();

            ResultSet resultSet = criarConsulta("select * from paginainicialvendasonline " +
                    "where codvendasonlineconfig = " + pagInicialVendasOnline.getCodVendasOnlineConfig() +
                    " and posicao = " + posicao, con);
            if (resultSet.next()) {
                pagInicialVendasOnline.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update paginainicialvendasonline set codVendasOnlineConfig = ?, textoBoxArredondado = ?,"
                            + "tituloPrincipal = ?, fotoKey = ? where codigo = ?");
            stm.setInt(1, pagInicialVendasOnline.getCodVendasOnlineConfig());
            stm.setString(2, pagInicialVendasOnline.getTextoBoxArredondado());
            stm.setString(3, pagInicialVendasOnline.getTituloPrincipal());
            stm.setString(4, pagInicialVendasOnline.getFotoKey());
            stm.setInt(5, pagInicialVendasOnline.getCodigo());
            stm.execute();
        }
    }

    public void gravarFotoFachada(FotoFachadaVendasOnlineVO fotoFachada) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(fotoFachada.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into fotofachadavendasonline (codVendasOnlineConfig, fotoKey) values (?, ?)");
            stm.setInt(1, fotoFachada.getCodVendasOnlineConfig());
            stm.setString(2, fotoFachada.getFotoKey());
            stm.execute();

            ResultSet resultSet = criarConsulta("select codigo from fotofachadavendasonline where codvendasonlineconfig = " + fotoFachada.getCodVendasOnlineConfig(), con);
            if (resultSet.next()) {
                fotoFachada.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update fotofachadavendasonline set codVendasOnlineConfig = ?,"
                            + "fotoKey = ? where codigo = ?");
            stm.setInt(1, fotoFachada.getCodVendasOnlineConfig());
            stm.setString(2, fotoFachada.getFotoKey());
            stm.setInt(3, fotoFachada.getCodigo());
            stm.execute();
        }
    }

    public void gravarMultiEmpresaConfig(MultiEmpresaConfigsVendasOnlineVO multiEmpresaConfigsVendasOnlineVO) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(multiEmpresaConfigsVendasOnlineVO.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into multiempresaconfigsvendasonline (fotoKey, cor, linkWhatsapp, linkInstagram, linkFacebook, linkTwitter ) " +
                            " values (?, ?, ?, ?, ?, ?)");
            stm.setString(1, multiEmpresaConfigsVendasOnlineVO.getFotoKey());
            stm.setString(2, multiEmpresaConfigsVendasOnlineVO.getCor());
            stm.setString(3, multiEmpresaConfigsVendasOnlineVO.getLinkWhatsapp());
            stm.setString(4, multiEmpresaConfigsVendasOnlineVO.getLinkInstagram());
            stm.setString(5, multiEmpresaConfigsVendasOnlineVO.getLinkFacebook());
            stm.setString(6, multiEmpresaConfigsVendasOnlineVO.getLinkTwitter());
            stm.execute();

            ResultSet resultSet = criarConsulta("select codigo from multiempresaconfigsvendasonline ", con);
            if (resultSet.next()) {
                multiEmpresaConfigsVendasOnlineVO.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update multiempresaconfigsvendasonline set fotoKey = ?, cor = ?,"
                            + "linkWhatsapp = ?, linkInstagram = ?, linkFacebook = ?, " +
                              "linkTwitter = ? where codigo = ?");
            stm.setString(1, multiEmpresaConfigsVendasOnlineVO.getFotoKey());
            stm.setString(2, multiEmpresaConfigsVendasOnlineVO.getCor());
            stm.setString(3, multiEmpresaConfigsVendasOnlineVO.getLinkWhatsapp());
            stm.setString(4, multiEmpresaConfigsVendasOnlineVO.getLinkInstagram());
            stm.setString(5, multiEmpresaConfigsVendasOnlineVO.getLinkFacebook());
            stm.setString(6, multiEmpresaConfigsVendasOnlineVO.getLinkTwitter());
            stm.setInt(7, multiEmpresaConfigsVendasOnlineVO.getCodigo());
            stm.execute();
        }
    }

    public void gravarMenu(MenuVendasOnlineVO menuVendasOnline) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(menuVendasOnline.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into menuvendasonline (codVendasOnlineConfig, menuPersonalizado01, menuPersonalizado02, menuPersonalizado03, linkMenuPersonalizado01, linkMenuPersonalizado02, linkMenuPersonalizado03, fotoKey) values (?, ?, ?, ?, ?, ?, ?, ?)");
            stm.setInt(1, menuVendasOnline.getCodVendasOnlineConfig());
            stm.setString(2, menuVendasOnline.getMenuPersonalizado01());
            stm.setString(3, menuVendasOnline.getMenuPersonalizado02());
            stm.setString(4, menuVendasOnline.getMenuPersonalizado03());
            stm.setString(5, menuVendasOnline.getLinkMenuPersonalizado01());
            stm.setString(6, menuVendasOnline.getLinkMenuPersonalizado02());
            stm.setString(7, menuVendasOnline.getLinkMenuPersonalizado03());
            stm.setString(8, menuVendasOnline.getFotoKey());
            stm.execute();

            ResultSet resultSet = criarConsulta("select codigo from menuvendasonline where codvendasonlineconfig = " + menuVendasOnline.getCodVendasOnlineConfig(), con);
            if (resultSet.next()) {
                menuVendasOnline.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update menuvendasonline set codVendasOnlineConfig = ?, menuPersonalizado01 = ?,"
                            + "menuPersonalizado02 = ?, menuPersonalizado03 = ?, linkMenuPersonalizado01 = ?, linkMenuPersonalizado02 = ?, linkMenuPersonalizado03 = ?, fotokey = ? where codigo = ?");
            stm.setInt(1, menuVendasOnline.getCodVendasOnlineConfig());
            stm.setString(2, menuVendasOnline.getMenuPersonalizado01());
            stm.setString(3, menuVendasOnline.getMenuPersonalizado02());
            stm.setString(4, menuVendasOnline.getMenuPersonalizado03());
            stm.setString(5, menuVendasOnline.getLinkMenuPersonalizado01());
            stm.setString(6, menuVendasOnline.getLinkMenuPersonalizado02());
            stm.setString(7, menuVendasOnline.getLinkMenuPersonalizado03());
            stm.setString(8, menuVendasOnline.getFotoKey());
            stm.setInt(9, menuVendasOnline.getCodigo());
            stm.execute();
        }
    }

    public void gravarModalidadeCarrousel(ModalidadeCarrouselVendasOnlineVO modalidadeCarrousel) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(modalidadeCarrousel.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into modalidadecarrouselvendasonline " +
                            "(codVendasOnlineConfig, tituloModalidadeVendas, descricaoModalidadeVendas, fotoKey, " +
                            " banner, tipoAgrupamento) " +
                            "values (?, ?, ?, ?, ?, ?)");
            stm.setInt(1, modalidadeCarrousel.getCodVendasOnlineConfig());
            stm.setString(2, modalidadeCarrousel.getTituloModalidadeVendas());
            stm.setString(3, modalidadeCarrousel.getDescricaoModalidadeVendas());
            stm.setString(4, modalidadeCarrousel.getFotoKey());
            stm.setBoolean(5, modalidadeCarrousel.getBanner());
            stm.setInt(6, modalidadeCarrousel.getTipoAgrupamento().getId());
            stm.execute();

            ResultSet resultSet = criarConsulta("select max(codigo) as codigo from modalidadecarrouselvendasonline ", con);
            if (resultSet.next()) {
                modalidadeCarrousel.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update modalidadecarrouselvendasonline set codModalidade = ?, codVendasOnlineConfig = ?, tituloModalidadeVendas = ?,"
                            + "descricaoModalidadeVendas = ?, fotoKey = ?, banner = ?, tipoAgrupamento = ?" +
                            " where codigo = ?");
            stm.setInt(1, modalidadeCarrousel.getCodModalidade());
            stm.setInt(2, modalidadeCarrousel.getCodVendasOnlineConfig());
            stm.setString(3, modalidadeCarrousel.getTituloModalidadeVendas());
            stm.setString(4, modalidadeCarrousel.getDescricaoModalidadeVendas());
            stm.setString(5, modalidadeCarrousel.getFotoKey());
            stm.setBoolean(6, modalidadeCarrousel.getBanner());
            stm.setInt(7, modalidadeCarrousel.getTipoAgrupamento().getId());
            stm.setInt(8, modalidadeCarrousel.getCodigo());
            stm.execute();
        }
    }

    public void gravarConfigModalidadeCarrossel(ConfigModalidadeCarrosselVendasOnlineVO configModalidadeCarrossel) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(configModalidadeCarrossel.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into configmodalidadecarrosselvendasonline " +
                            "(codVendasOnlineConfig, tituloModalidadeVendas, descricaoModalidadeVendas)" +
                            "values (?, ?, ?)");
            stm.setInt(1, configModalidadeCarrossel.getCodVendasOnlineConfig());
            stm.setString(2, configModalidadeCarrossel.getTituloModalidadeVendas());
            stm.setString(3, configModalidadeCarrossel.getDescricaoModalidadeVendas());
            stm.execute();

            ResultSet resultSet = criarConsulta("select max(codigo) as codigo from modalidadecarrouselvendasonline ", con);
            if (resultSet.next()) {
                configModalidadeCarrossel.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update configmodalidadecarrosselvendasonline set " +
                            "codVendasOnlineConfig = ?, tituloModalidadeVendas = ?, descricaoModalidadeVendas = ? " +
                            "where codigo = ?");
            stm.setInt(1, configModalidadeCarrossel.getCodVendasOnlineConfig());
            stm.setString(2, configModalidadeCarrossel.getTituloModalidadeVendas());
            stm.setString(3, configModalidadeCarrossel.getDescricaoModalidadeVendas());
            stm.setInt(4, configModalidadeCarrossel.getCodigo());
            stm.execute();
        }
    }

    public void gravarCaptacaoLeads(CaptacaoLeadsVendasOnlineVO capLeads) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(capLeads.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into captacaoleadsvendasonline (codVendasOnlineConfig, tituloCabecalho, fotokey) values (?, ?, ?)");
            stm.setInt(1, capLeads.getCodVendasOnlineConfig());
            stm.setString(2, capLeads.getTituloCabecalho());
            stm.setString(3, capLeads.getFotoKey());
            stm.execute();

            ResultSet resultSet = criarConsulta("select codigo from captacaoleadsvendasonline where codvendasonlineconfig = " + capLeads.getCodVendasOnlineConfig(), con);
            if (resultSet.next()) {
                capLeads.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update captacaoleadsvendasonline set codVendasOnlineConfig = ?, tituloCabecalho = ?, fotokey = ? where codigo = ?");
            stm.setInt(1, capLeads.getCodVendasOnlineConfig());
            stm.setString(2, capLeads.getTituloCabecalho());
            stm.setString(3, capLeads.getFotoKey());
            stm.setInt(4, capLeads.getCodigo());
            stm.execute();
        }
    }

    public void gravarContatoRodape(ContatoRodapeVendasOnlineVO contatoRodape) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(contatoRodape.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into contatorodapevendasonline (codVendasOnlineConfig, horarioSegundaSexta, horarioSabado, horarioDomingoFeriado, linkWhatsapp, linkInstagram, LinkFacebook, LinkTwitter) values (?,?,?,?, ?, ?, ?, ?)");
            stm.setInt(1, contatoRodape.getCodVendasOnlineConfig());
            stm.setString(2, contatoRodape.getHorarioSegundaSexta());
            stm.setString(3, contatoRodape.getHorarioSabado());
            stm.setString(4, contatoRodape.getHorarioDomingoFeriado());
            stm.setString(5, contatoRodape.getLinkWhatsapp());
            stm.setString(6, contatoRodape.getLinkInstagram());
            stm.setString(7, contatoRodape.getLinkFacebook());
            stm.setString(8, contatoRodape.getLinkTwitter());
            stm.execute();

            ResultSet resultSet = criarConsulta("select codigo from contatorodapevendasonline where codvendasonlineconfig = " + contatoRodape.getCodVendasOnlineConfig(), con);
            if (resultSet.next()) {
                contatoRodape.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update contatorodapevendasonline set codVendasOnlineConfig = ?, horarioSegundaSexta = ?, horarioSabado = ?, horarioDomingoFeriado = ?, linkWhatsapp = ?, linkInstagram = ?, linkFacebook = ?, linkTwitter = ? where codigo = ?");
            stm.setInt(1, contatoRodape.getCodVendasOnlineConfig());
            stm.setString(2, contatoRodape.getHorarioSegundaSexta());
            stm.setString(3, contatoRodape.getHorarioSabado());
            stm.setString(4, contatoRodape.getHorarioDomingoFeriado());
            stm.setString(5, contatoRodape.getLinkWhatsapp());
            stm.setString(6, contatoRodape.getLinkInstagram());
            stm.setString(7, contatoRodape.getLinkFacebook());
            stm.setString(8, contatoRodape.getLinkTwitter());
            stm.setInt(9, contatoRodape.getCodigo());
            stm.execute();
        }
    }

    public void gravarIntegracoesHotsite(VendasConfigVO configVO) throws Exception {
        con.setAutoCommit(true);
        PreparedStatement stm = con.prepareStatement(
                "update vendasonlineconfig set googletagidhotsite = ? where codigo = ?");
        stm.setString(1, configVO.getGoogleTagIdHotsite());
        stm.setInt(2, configVO.getCodigo());
        stm.execute();
    }

    public void gravarAgendaVendasOnlineLinkVisitante(AgendaVendasOnlineVO vo, UsuarioVO us, Integer configVendas, Integer empresa) throws Exception {
        try {
            con.setAutoCommit(false);

            String query1 = "update vendasonlineconfig set tipoAulasLinkVisitante = " +
                    (vo.getTipoAulasLinkVisitante().equals(AgendaVendasOnlineVO.TODAS_AULAS) ? 0 : 1) + " where codigo = " + configVendas + " and empresa = " + empresa;
            PreparedStatement ps1 = con.prepareStatement(query1);
            ps1.executeUpdate();
            if (!vo.getTipoAulasLinkVisitante().equals(AgendaVendasOnlineVO.TODAS_AULAS) &&
                    vo.getAulasVendasOnlineLinkVisitante() != null && vo.getAulasVendasOnlineLinkVisitante().isEmpty()) {
                throw new Exception("Por favor, escolha uma aula para agendamento de aula experimental atraves do link de visitante");
            }

            if (vo.getTipoAulasLinkVisitante().equals(AgendaVendasOnlineVO.TODAS_AULAS) && !UteisValidacao.emptyList(vo.getAulasVendasOnlineLinkVisitante())) {
                //Mudou para todas as aulas, então excluir todas as aulas específicas previamente configuradas
                excluirTodasAulaVendasOnline(configVendas, usuario);
                vo.setAulasVendasOnlineLinkVisitante(new ArrayList<>());
            } else if (!UteisValidacao.emptyList(vo.getAulasVendasOnlineLinkVisitante())){
                boolean inseriuAulaNova = false;
                List<AulasVendasOnline> aulasInserir = new ArrayList<>();
                for (AulasVendasOnline aula : vo.getAulasVendasOnlineLinkVisitante()) {
                    String query = "select * from aulavendasonlinelinkvisitante where turma = ? and modalidade = ? and vendasonlineconfig = ? and dataexclusao is null;";
                    PreparedStatement ps = con.prepareStatement(query);
                    ps.setInt(1, aula.getCodigoTurma());
                    ps.setInt(2, aula.getCodigoModalidade());
                    ps.setInt(3, configVendas);
                    ResultSet rs = ps.executeQuery();

                    if (rs.next()) {
                        //já existe, não rpecisa gravar nova
                        continue;
                    }

                    String insert = "insert into aulavendasonlinelinkvisitante (turma, modalidade, vendasonlineconfig, datalancamento) values (?, ?, ?, ?);";
                    PreparedStatement ps2 = con.prepareStatement(insert);
                    ps2.setInt(1, aula.getCodigoTurma());
                    ps2.setInt(2, aula.getCodigoModalidade());
                    ps2.setInt(3, configVendas);
                    ps2.setTimestamp(4, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                    ps2.executeUpdate();
                    aulasInserir.add(aula);
                    inseriuAulaNova = true;
                }
                if (inseriuAulaNova) {
                    inserirLogInclusaoDeAulas(us, configVendas, aulasInserir);
                }
            }
            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        }
    }

    public void inserirLogInclusaoDeAulas(UsuarioVO usuarioVO, int codConfig, List<AulasVendasOnline> aulasInserir) {
        StringBuilder aulasString = new StringBuilder();
        for (AulasVendasOnline aula : aulasInserir) {
            aulasString.append(aula.getTurma());
        }

        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("VENDASCONFIG");
            log.setNomeEntidadeDescricao("VENDASCONFIG");
            log.setDescricao("VENDASCONFIG");
            log.setChavePrimaria(String.valueOf(codConfig));
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao("INCLUSÃO");
            log.setValorCampoAnterior("incluir novas aulas");
            log.setNomeCampo("AULAS AGENDA LINK VISITANTE");
            log.setValorCampoAlterado(aulasString.toString());

            try {
                if (usuarioVO == null) {
                    if (context() != null) { //pegar da sessão
                        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        usuarioVO = loginControle.getUsuarioLogado();
                    }
                }
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("Não identificado");
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao tentar identificar o usuário logado no fluxo de incluir autorização de cobrança: " + ex.getMessage());
            }

            Log logDAO;
            try {
                logDAO = new Log(con);
                logDAO.incluirSemCommit(log);
            } catch (Exception ex) {
            } finally {
                logDAO = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gravar Log exclusão de aulavendasonlinelinkvisitante: " + ex.getMessage());
        }
    }

    public void gravarAgendaVendasOnline(AgendaVendasOnlineVO vo) throws  Exception {
        if (vo.getTipoProduto().equals(AgendaVendasOnlineVO.NOVO_PRODUTO)) {
            validarCamposAgendaVendasOnline(vo);
        } else {
            if (Objects.isNull(vo.getProdutoEscolhido())) {
                throw new Exception("Por favor, escolha uma diária para realizar o cadastro.");
            }
        }

        Integer codigoModalidade = getCodigoModalidadeDaTurma(vo.getAulaEscolhida());

        if (codigoModalidade == 0) throw new Exception("Ocorreu um erro ao buscar o código da " +
                "modalidade para a turma " + vo.getAulaEscolhida());

        ProdutoVO produtoVO = new ProdutoVO();
        if (vo.getTipoProduto().equals(AgendaVendasOnlineVO.NOVO_PRODUTO) || vo.getEstaEditando()) {
            produtoVO.setValorFinal(new Double(vo.getValorProduto().replace(".", "").replace(",", ".")));
            produtoVO.setDescricao(vo.getDescricaoProduto());
            produtoVO.setModalidadeVendasOnline(codigoModalidade);
        } else {
            produtoVO.setCodigo(vo.getProdutoEscolhido());
        }

        if (vo.getEstaEditando()) {
            String query = "select * from aulavendasonline a where codigoturma = " + vo.getAulaEscolhida() + " and data_exclusao is null";
            ResultSet resultSet = criarConsulta(query, con);
            Integer codigoProduto = 0;
            try {
                resultSet.next();
                codigoProduto = resultSet.getInt("codigoproduto");
            } catch (Exception e) {
                throw new Exception("Ocorreu um erro ao realizar a atualização do produto, pois o código está errado ou o produto não existe.");
            }
            produtoVO.setCodigo(codigoProduto);
            new Produto(con).atualizarProdutoAulaDiaria(produtoVO);
        } else {
            try {
                con.setAutoCommit(false);
                if (vo.getTipoProduto().equals(AgendaVendasOnlineVO.NOVO_PRODUTO)) {
                    produtoVO = new Produto(con).criarProdutoAulaDiaria(produtoVO, true);
                }

                String query = "insert into aulavendasonline (codigoproduto, codigoturma, codmodalidade) values (?, ?, ?);";
                PreparedStatement ps = con.prepareStatement(query);
                ps.setInt(1, produtoVO.getCodigo());
                ps.setInt(2, vo.getAulaEscolhida());
                ps.setInt(3, codigoModalidade);
                ps.executeUpdate();

                String queryInserirProdutoTurma = "INSERT INTO produtoturmavendasonline VALUES (?, ?)";
                PreparedStatement ps2 = con.prepareStatement(queryInserirProdutoTurma);
                ps2.setInt(1, produtoVO.getCodigo());
                ps2.setInt(2, vo.getAulaEscolhida());
                ps2.executeUpdate();

                con.commit();
            } catch (Exception e) {
                con.rollback();
                throw e;
            }
        }
    }

    private Integer getCodigoModalidadeDaTurma(Integer aulaEscolhida) throws Exception {
        String query = "select modalidade from turma t where codigo = " + aulaEscolhida;
        ResultSet resultSet = criarConsulta(query, con);
        Integer codigoModalidade = 0;

        while(resultSet.next()) {
            codigoModalidade = resultSet.getInt("modalidade");
        }

        return codigoModalidade;
    }

    private void validarCamposAgendaVendasOnline(AgendaVendasOnlineVO vo) throws Exception {
        if (vo.getAulaEscolhida() == null) {
            throw new Exception("Por favor, escolha a aula/turma para criar o produto.");
        }

        if (UteisValidacao.emptyString(vo.getDescricaoProduto())
                || UteisValidacao.emptyString(vo.getValorProduto())) {
            throw new Exception("Por favor, preencha os campos descrição e valor do produto");
        }

        String message = "";

        if (vo.getEstaEditando() && vo.getAulaEscolhida() == null) {
            if (UteisValidacao.emptyString(message))
                message = "Por favor, escolha o horário da aula.";
            else
                message = message + " e também o horário da aula.";
        }

        if (!UteisValidacao.emptyString(message)) {
            throw new Exception(message);
        }
    }

    public void gravarPlanoSiteVendasOnline(PlanosSiteVendasOnlineVO planoSite) throws Exception {
        con.setAutoCommit(true);
        if (UteisValidacao.emptyNumber(planoSite.getCodigo())) {
            PreparedStatement stm = con.prepareStatement(
                    "insert into planositevendasonline (codPlano, codVendasOnlineConfig,"
                            + "destaqueCabecalho, descricaoPlano, beneficio01, beneficio02, beneficio03, precoOriginal, lstBeneficios) values (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            stm.setInt(1, planoSite.getCodPlano());
            stm.setInt(2, planoSite.getCodVendasOnlineConfig());
            stm.setString(3, planoSite.getDestaqueCabecalho());
            stm.setString(4, planoSite.getDescricaoPlano());
            stm.setString(5, planoSite.getBeneficio01());
            stm.setString(6, planoSite.getBeneficio02());
            stm.setString(7, planoSite.getBeneficio03());
            stm.setDouble(8, planoSite.getPrecoOriginal());
            stm.setString(9, planoSite.getBeneficios());
            stm.execute();

            ResultSet resultSet = criarConsulta("select codigo from planositevendasonline where codvendasonlineconfig = " + planoSite.getCodVendasOnlineConfig(), con);
            if (resultSet.next()) {
                planoSite.setCodigo(resultSet.getInt("codigo"));
            }
            con.setAutoCommit(false);
        } else {
            PreparedStatement stm = con.prepareStatement(
                    "update planositevendasonline set codPlano = ?, codVendasOnlineConfig = ?, destaqueCabecalho = ?,"
                            + "descricaoPlano = ?, beneficio01 = ?, beneficio02 = ?, beneficio03 = ?, precoOriginal = ?, lstBeneficios = ? where codigo = ?");
            stm.setInt(1, planoSite.getCodPlano());
            stm.setInt(2, planoSite.getCodVendasOnlineConfig());
            stm.setString(3, planoSite.getDestaqueCabecalho());
            stm.setString(4, planoSite.getDescricaoPlano());
            stm.setString(5, planoSite.getBeneficio01());
            stm.setString(6, planoSite.getBeneficio02());
            stm.setString(7, planoSite.getBeneficio03());
            stm.setDouble(8, planoSite.getPrecoOriginal());
            stm.setString(9, planoSite.getBeneficios());
            stm.setInt(10, planoSite.getCodigo());
            stm.execute();
        }
    }

    public void gravarImagem(ImagensAcademiaVendasVO imagem) throws Exception {
        PreparedStatement stm = con.prepareStatement("insert into imagensacademiavendas (fotokey, empresa) " +
                " values ( ?, ?)");

        stm.setString(1, imagem.getFotoKey());
        stm.setInt(2, imagem.getEmpresa());

        stm.execute();
    }

    public List<ModalidadeCarrouselVendasOnlineVO> consultarModalidadesCarrouselVendasOnline(Integer codConfigVendasOnline, Boolean banner) throws Exception {
        List<ModalidadeCarrouselVendasOnlineVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from modalidadecarrouselvendasonline where codvendasonlineconfig = " + codConfigVendasOnline
                + (banner ? " and banner is true" : " and banner is false"), con);
        while (resultSet.next()) {
            ModalidadeCarrouselVendasOnlineVO mCVO = new ModalidadeCarrouselVendasOnlineVO();
            mCVO.setCodigo(resultSet.getInt("codigo"));
            mCVO.setCodModalidade(resultSet.getInt("codModalidade"));
            mCVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            mCVO.setTituloModalidadeVendas(resultSet.getString("tituloModalidadeVendas"));
            mCVO.setDescricaoModalidadeVendas(resultSet.getString("descricaoModalidadeVendas"));
            mCVO.setFotoKey(resultSet.getString("fotoKey"));
            mCVO.setBanner(resultSet.getBoolean("banner"));
            mCVO.setTipoAgrupamento(TipoAgrupamentoEnum.getFromId(resultSet.getInt("tipoAgrupamento")));
            list.add(mCVO);
        }
        return list;
    }

    public ConfigModalidadeCarrosselVendasOnlineVO consultarConfigModalidadesCarrosselVendasOnline(Integer codConfigVendasOnline) throws Exception {
        ConfigModalidadeCarrosselVendasOnlineVO cmcVO = new ConfigModalidadeCarrosselVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from configmodalidadecarrosselvendasonline where codvendasonlineconfig = " + codConfigVendasOnline, con);
        if (resultSet.next()) {
            cmcVO.setCodigo(resultSet.getInt("codigo"));
            cmcVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            cmcVO.setTituloModalidadeVendas(resultSet.getString("tituloModalidadeVendas"));
            cmcVO.setDescricaoModalidadeVendas(resultSet.getString("descricaoModalidadeVendas"));
        }
        return cmcVO;
    }

    public List<ModalidadeCarrouselVendasOnlineVO> consultarModalidadesCarrouselVendasOnlinePorAgrupamento(Integer idAgrupamento, boolean banner) throws Exception {
        List<ModalidadeCarrouselVendasOnlineVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from modalidadecarrouselvendasonline where tipoAgrupamento = " + idAgrupamento
                + (banner ? " and banner is true" : "and banner is false") + " order by codigo", con);
        while (resultSet.next()) {
            ModalidadeCarrouselVendasOnlineVO mCVO = new ModalidadeCarrouselVendasOnlineVO();
            mCVO.setCodigo(resultSet.getInt("codigo"));
            mCVO.setCodModalidade(resultSet.getInt("codModalidade"));
            mCVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            mCVO.setTituloModalidadeVendas(resultSet.getString("tituloModalidadeVendas"));
            mCVO.setDescricaoModalidadeVendas(resultSet.getString("descricaoModalidadeVendas"));
            mCVO.setFotoKey(resultSet.getString("fotoKey"));
            mCVO.setBanner(resultSet.getBoolean("banner"));
            mCVO.setTipoAgrupamento(TipoAgrupamentoEnum.getFromId(resultSet.getInt("tipoAgrupamento")));
            list.add(mCVO);
        }
        return list;
    }

    @Override
    public List<EmpresaHotsiteVO> consultarEmpresasHotsite() throws Exception {
        List<EmpresaHotsiteVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from empresahotsite where ativo = true ", con);
        while (resultSet.next()) {
            EmpresaHotsiteVO emp = new EmpresaHotsiteVO();
            emp.setCodigo(resultSet.getInt("codigo"));
            emp.setEmpresa(resultSet.getInt("empresa"));
            emp.setAtivo(resultSet.getBoolean("ativo"));
            list.add(emp);
        }
        return list;
    }


    @Override
    public List<EmpresaHotsiteVO> consultarEmpresasHotsite(Integer empresa) throws Exception {
        List<EmpresaHotsiteVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from empresahotsite where empresa= " + empresa, con);
        while (resultSet.next()) {
            EmpresaHotsiteVO emp = new EmpresaHotsiteVO();
            emp.setCodigo(resultSet.getInt("codigo"));
            emp.setAtivo(resultSet.getBoolean("ativo"));
            list.add(emp);
        }
        return list;
    }

   public void incluirAtualiza(Integer empresa) throws  Exception{
        if(consultarEmpresasHotsite(empresa).size() > 0){
            String query = "update empresahotsite set ativo = not ativo where empresa = ?";
            PreparedStatement ps = con.prepareStatement(query);
            ps.setInt(1, empresa);
            ps.executeUpdate();
        }else{
            String sqlInsert = "insert into empresahotsite (empresa, ativo) values (?,?);";
            try (PreparedStatement stm = con.prepareStatement(sqlInsert, Statement.RETURN_GENERATED_KEYS)) {
                stm.setInt(1, empresa);
                stm.setBoolean(2, true);
                stm.execute();
            }
        }
    }
    public List<ModalidadeCarrouselVendasOnlineVO> consultarModalidadesCarrouselVendasOnlinePorEmpresa(Integer codigoEmpresa, Boolean banner) throws Exception{
        List<ModalidadeCarrouselVendasOnlineVO> list = new ArrayList<>();
        StringBuilder str = new StringBuilder();
        str.append("select * from modalidadecarrouselvendasonline as m ");
        str.append("inner join vendasonlineconfig v on v.codigo  = m.codvendasonlineconfig ");
        str.append("inner join empresa e on e.codigo = v.empresa ");
        str.append("WHERE e.codigo = ").append(codigoEmpresa).append(banner ? " and banner is true" : "and banner is false");
        ResultSet resultSet = criarConsulta(str.toString(), con);
        while (resultSet.next()) {
            ModalidadeCarrouselVendasOnlineVO mCVO = new ModalidadeCarrouselVendasOnlineVO();
            mCVO.setCodigo(resultSet.getInt("codigo"));
            mCVO.setCodModalidade(resultSet.getInt("codModalidade"));
            mCVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            mCVO.setTituloModalidadeVendas(resultSet.getString("tituloModalidadeVendas"));
            mCVO.setDescricaoModalidadeVendas(resultSet.getString("descricaoModalidadeVendas"));
            mCVO.setFotoKey(resultSet.getString("fotoKey"));
            mCVO.setBanner(resultSet.getBoolean("banner"));
            mCVO.setOrdenacaoModalidadeVendas(resultSet.getInt("tipoagrupamento") );
            list.add(mCVO);
        }
        return list;
    }

    public ConfigModalidadeCarrosselVendasOnlineVO consultarConfigModalidadesCarrosselVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception {
        ConfigModalidadeCarrosselVendasOnlineVO cmcVO = new ConfigModalidadeCarrosselVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from configmodalidadecarrosselvendasonline cm " +
                "inner join vendasonlineconfig v on v.codigo  = cm.codvendasonlineconfig " +
                "inner join empresa e on e.codigo = v.empresa " +
                "where e.codigo = " + codigoEmpresa, con);
        if (resultSet.next()) {
            cmcVO.setCodigo(resultSet.getInt("codigo"));
            cmcVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            cmcVO.setTituloModalidadeVendas(resultSet.getString("tituloModalidadeVendas"));
            cmcVO.setDescricaoModalidadeVendas(resultSet.getString("descricaoModalidadeVendas"));
        }
        return cmcVO;
    }

    public List<AgendaTotalJSON> produtosAgendaAulaEmpresa(Integer codigoUnidade,
                                                           Date dataInicial,
                                                           Date dataFinal,
                                                           Connection con) throws Exception{

        return new TurmasServiceImpl(con).consultarParaAgenda(dataInicial, dataFinal, null, null,
                codigoUnidade, false, null, null, null, null,
                false, true);
    }

    public MenuVendasOnlineVO consultarMenuVendasOnlineEmpresa(Integer codigoEmpresa) throws Exception{
        MenuVendasOnlineVO menu = new MenuVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from menuvendasonline as m " +
                "inner join vendasonlineconfig v on v.codigo  = m.codvendasonlineconfig " +
                "inner join empresa e on e.codigo = v.empresa " +
                "WHERE e.codigo  ="+codigoEmpresa, con);
        while (resultSet.next()) {
            menu.setCodigo(resultSet.getInt("codigo"));
            menu.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            menu.setMenuPersonalizado01(resultSet.getString("menuPersonalizado01"));
            menu.setMenuPersonalizado02(resultSet.getString("menuPersonalizado02"));
            menu.setMenuPersonalizado03(resultSet.getString("menuPersonalizado03"));
            menu.setLinkMenuPersonalizado01(resultSet.getString("linkMenuPersonalizado01"));
            menu.setLinkMenuPersonalizado02(resultSet.getString("linkMenuPersonalizado02"));
            menu.setLinkMenuPersonalizado03(resultSet.getString("linkMenuPersonalizado03"));
            menu.setFotoKey(resultSet.getString("fotoKey"));
        }
        return menu;
    }




    public PaginaInicialVendasOnlineVO consultarPaginaInicialVendasOnline(Integer codConfigVendasOnline, Integer posicao) throws Exception{
        PaginaInicialVendasOnlineVO pagIni = new PaginaInicialVendasOnlineVO();
        ResultSet resultSet = criarConsulta(
                "select * from paginainicialvendasonline " +
                    "where codvendasonlineconfig = " + codConfigVendasOnline +
                    " and posicao = "+ posicao, con);
        while (resultSet.next()) {
            pagIni.setCodigo(resultSet.getInt("codigo"));
            pagIni.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            pagIni.setTextoBoxArredondado(resultSet.getString("textoBoxArredondado"));
            pagIni.setTituloPrincipal(resultSet.getString("tituloPrincipal"));
            pagIni.setFotoKey(resultSet.getString("fotoKey"));
            pagIni.setPosicao(resultSet.getInt("posicao"));
        }
        return pagIni;
    }

    public List<PaginaInicialVendasOnlineVO> consultarPaginaInicialVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception{
        List<PaginaInicialVendasOnlineVO> listaRetorno = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from paginainicialvendasonline as m " +
                "inner join vendasonlineconfig v on v.codigo  = m.codvendasonlineconfig  " +
                "inner join empresa e on e.codigo = v.empresa " +
                "WHERE e.codigo  = "+codigoEmpresa, con);
        while (resultSet.next()) {
            PaginaInicialVendasOnlineVO pagIni = new PaginaInicialVendasOnlineVO();
            pagIni.setCodigo(resultSet.getInt("codigo"));
            pagIni.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            pagIni.setTextoBoxArredondado(resultSet.getString("textoBoxArredondado"));
            pagIni.setTituloPrincipal(resultSet.getString("tituloPrincipal"));
            pagIni.setFotoKey(resultSet.getString("fotoKey"));
            pagIni.setPosicao(resultSet.getInt("posicao"));
            pagIni.setExibirBotaoAgendaSobreBanner(resultSet.getBoolean("exibirbotaoagendasobrebanner"));

            //Foto padrão do avatar da pacto não possui fotokey, portanto não deve ser adicionada na lista para não exibir lá no front
            if (!UteisValidacao.emptyString(pagIni.getFotoKey())) {
                listaRetorno.add(pagIni);
            }
        }
        return listaRetorno;
    }

    public List<FotoFachadaVendasOnlineVO> consultarFotoFachadaVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception{
        List<FotoFachadaVendasOnlineVO> listaRetorno = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from fotofachadavendasonline as m " +
                "inner join vendasonlineconfig v on v.codigo  = m.codvendasonlineconfig  " +
                "inner join empresa e on e.codigo = v.empresa " +
                "WHERE e.codigo  = "+codigoEmpresa, con);
        while (resultSet.next()) {
            FotoFachadaVendasOnlineVO fach = new FotoFachadaVendasOnlineVO();
            fach.setCodigo(resultSet.getInt("codigo"));
            fach.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            fach.setFotoKey(resultSet.getString("fotoKey"));
            listaRetorno.add(fach);
        }
        return listaRetorno;
    }

    public CaptacaoLeadsVendasOnlineVO consultarCapLeadsVendasOnline(Integer codConfigVendasOnline) throws Exception{
        CaptacaoLeadsVendasOnlineVO cap = new CaptacaoLeadsVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from captacaoleadsvendasonline where codvendasonlineconfig = " + codConfigVendasOnline, con);
        while (resultSet.next()) {
            cap.setCodigo(resultSet.getInt("codigo"));
            cap.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            cap.setTituloCabecalho(resultSet.getString("tituloCabecalho"));
            cap.setFotoKey(resultSet.getString("fotokey"));
        }
        return cap;
    }

    public CaptacaoLeadsVendasOnlineVO consultarCapLeadsVendasOnlineApi(Integer codConfigVendasOnline) throws Exception{
        CaptacaoLeadsVendasOnlineVO cap = new CaptacaoLeadsVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select \n" +
                "e.nome as nomeempresa,\n" +
                "c.*,\n" +
                "e.usaintegracoescrm,\n" +
                "configleads.habilitada, \n" +
                "configleads.responsavelpadrao \n" +
                "from empresa e \n" +
                "inner join vendasonlineconfig v on v.empresa = e.codigo \n" +
                "inner join captacaoleadsvendasonline c on c.codvendasonlineconfig = v.codigo \n" +
                "inner join configuracaointegracaogenericaleads configleads on configleads.empresa = e.codigo\n" +
                "where e.codigo = " + codConfigVendasOnline, con);
        while (resultSet.next()) {
            cap.setCodigo(resultSet.getInt("codigo"));
            cap.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            cap.setTituloCabecalho(resultSet.getString("tituloCabecalho"));
            cap.setFotoKey(resultSet.getString("fotokey"));
            cap.setUsaIntegracaoCRM(resultSet.getBoolean("habilitada"));
            cap.setResponsavelPadrao(resultSet.getInt("responsavelpadrao"));
            if (cap.getResponsavelPadrao() == 0){
                cap.setResponsavelPadrao(-1);
            }
        }
        return cap;
    }

    public FotoFachadaVendasOnlineVO consultarFotoFachadaVendasOnline(Integer codConfigVendasOnline) throws Exception{
        FotoFachadaVendasOnlineVO fotoFach = new FotoFachadaVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from fotofachadavendasonline where codvendasonlineconfig = " + codConfigVendasOnline, con);
        while (resultSet.next()) {
            fotoFach.setCodigo(resultSet.getInt("codigo"));
            fotoFach.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            fotoFach.setFotoKey(resultSet.getString("fotoKey"));
        }
        return fotoFach;
    }

    public ContatoRodapeVendasOnlineVO consultarContatoRodapeVendasOnline(Integer codConfigVendasOnline) throws Exception{
        ContatoRodapeVendasOnlineVO contRoda = new ContatoRodapeVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from contatorodapevendasonline where codvendasonlineconfig = " + codConfigVendasOnline, con);
        while (resultSet.next()) {
            contRoda.setCodigo(resultSet.getInt("codigo"));
            contRoda.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            contRoda.setHorarioSegundaSexta(resultSet.getString("horarioSegundaSexta"));
            contRoda.setHorarioSabado(resultSet.getString("horarioSabado"));
            contRoda.setHorarioDomingoFeriado(resultSet.getString("horarioDomingoFeriado"));
            contRoda.setLinkWhatsapp(resultSet.getString("linkWhatsapp"));
            contRoda.setLinkInstagram(resultSet.getString("linkInstagram"));
            contRoda.setLinkFacebook(resultSet.getString("linkFacebook"));
            contRoda.setLinkTwitter(resultSet.getString("linkTwitter"));
        }
        return contRoda;
    }

    public MultiEmpresaConfigsVendasOnlineVO consultarMultiEmpresaCongifsVendasOnline() throws Exception{
        MultiEmpresaConfigsVendasOnlineVO multiConfigs = new MultiEmpresaConfigsVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from multiempresaconfigsvendasonline ", con);
        while (resultSet.next()) {
            multiConfigs.setCodigo(resultSet.getInt("codigo"));
            multiConfigs.setFotoKey(resultSet.getString("fotoKey"));
            multiConfigs.setCor(resultSet.getString("cor"));
            multiConfigs.setLinkWhatsapp(resultSet.getString("linkWhatsapp"));
            multiConfigs.setLinkInstagram(resultSet.getString("linkInstagram"));
            multiConfigs.setLinkFacebook(resultSet.getString("linkFacebook"));
            multiConfigs.setLinkTwitter(resultSet.getString("linkTwitter"));
        }
        return multiConfigs;
    }

    public ContatoRodapeVendasOnlineVO consultarContatoRodapeVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception{
        ContatoRodapeVendasOnlineVO contRoda = new ContatoRodapeVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from contatorodapevendasonline as c "+
                " inner join vendasonlineconfig v on v.codigo  = c.codvendasonlineconfig"+
                " inner join empresa e on e.codigo = v.empresa"+
                " WHERE e.codigo  = "+codigoEmpresa, con);
        while (resultSet.next()) {
            contRoda.setCodigo(resultSet.getInt("codigo"));
            contRoda.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            contRoda.setHorarioSegundaSexta(resultSet.getString("horarioSegundaSexta"));
            contRoda.setHorarioSabado(resultSet.getString("horarioSabado"));
            contRoda.setHorarioDomingoFeriado(resultSet.getString("horarioDomingoFeriado"));
            contRoda.setLinkWhatsapp(resultSet.getString("linkWhatsapp"));
            contRoda.setLinkInstagram(resultSet.getString("linkInstagram"));
            contRoda.setLinkFacebook(resultSet.getString("linkFacebook"));
            contRoda.setLinkTwitter(resultSet.getString("linkTwitter"));
        }
        return contRoda;
    }

    @Override
    public List<TurmaEHorarioVendasOnlineVO> consultarTurmaEHorarioVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception {
        String query =
                "		SELECT DISTINCT                                                    \n" +
                "				t.codigo AS codturma,                                      \n" +
                "				t.descricao,                                               \n" +
                "				a.descricao AS ambiente,                                   \n" +
                "				a.codigo AS codambiente,                                   \n" +
                "				t.dias  as diassemana,                                      \n" +
                "				m.codigo  as codmodalidade,                                      \n" +
                "				m.nome  as modalidade                                      \n" +
                "		FROM turma t                                                       \n" +
                "		 	INNER JOIN empresa emp                                         \n" +
                "		 		ON t.empresa = emp.codigo                                  \n" +
                "		 	INNER JOIN modalidade m                                        \n" +
                "		 		ON m.codigo = t.modalidade                                 \n" +
                "		 	INNER JOIN horarioturma ht                                     \n" +
                "		 		ON ht.turma = t.codigo                                     \n" +
                "		 	INNER JOIN ambiente a                                          \n" +
                "		 		ON ht.ambiente = a.codigo                                  \n" +
                "		 	INNER JOIN nivelturma n                                        \n" +
                "		 		ON ht.nivelturma = n.codigo                                \n" +
                "		 	INNER JOIN colaborador c                                       \n" +
                "		 		ON c.codigo = ht.professor                                 \n" +
                "		 	INNER JOIN pessoa p                                            \n" +
                "		 		ON c.pessoa = p.codigo                                     \n" +
                "		WHERE emp.ativa                                                    \n" +
                "		    AND t.ambiente notnull                                         \n" +
                "			AND t.datafinalvigencia >= now()                               \n" +
                "		 	AND (ht.situacao = 'AT' OR ht.datasaiuturma IS NOT NULL)       \n" +
                "		 	AND (t.usuariodesativou IS NULL )                              \n" +
                "		 	AND t.empresa = " + codigoEmpresa                                +
                "           AND t.codigo not in (select tmp.codigo                         \n" +
                "                                from produtoturmavendasonline ptv         \n" +
                "                                inner join turma tmp                      \n" +
                "                                   on tmp.codigo = ptv.codigoturma        \n" +
                "                                   and tmp.empresa = t.empresa            \n" +
                "                               )                                          \n" +
                "		ORDER BY t.descricao, a.descricao	                               " ;

        ResultSet resultSet = criarConsulta(query, con);
        List<TurmaEHorarioVendasOnlineVO> list = new ArrayList<>();
        while(resultSet.next()) {
            TurmaEHorarioVendasOnlineVO vo = new TurmaEHorarioVendasOnlineVO();
            vo.setCodigoTurma(resultSet.getInt("codturma"));
            vo.setNomeModalidade(resultSet.getString("modalidade"));
            vo.setCodigoModalidade(resultSet.getInt("codmodalidade"));
            vo.setDescricao(resultSet.getString("descricao"));
            vo.setAmbiente(resultSet.getString("ambiente"));
            vo.setDiaSemana(resultSet.getString("diassemana"));
            list.add(vo);
        }

        return list;
    }

    @Override
    public List<TurmaEHorarioVendasOnlineVO> consultarTurmaEHorarioVendasOnlineLinkVisitantePorEmpresa(Integer codigoEmpresa) throws Exception {
        String query =
                "		SELECT DISTINCT                                                    \n" +
                        "				t.codigo AS codturma,                                      \n" +
                        "				t.descricao,                                               \n" +
                        "				a.descricao AS ambiente,                                   \n" +
                        "				a.codigo AS codambiente,                                   \n" +
                        "				t.dias  as diassemana,                                      \n" +
                        "				m.codigo  as codmodalidade,                                      \n" +
                        "				m.nome  as modalidade                                      \n" +
                        "		FROM turma t                                                       \n" +
                        "		 	INNER JOIN empresa emp                                         \n" +
                        "		 		ON t.empresa = emp.codigo                                  \n" +
                        "		 	INNER JOIN modalidade m                                        \n" +
                        "		 		ON m.codigo = t.modalidade                                 \n" +
                        "		 	INNER JOIN horarioturma ht                                     \n" +
                        "		 		ON ht.turma = t.codigo                                     \n" +
                        "		 	INNER JOIN ambiente a                                          \n" +
                        "		 		ON ht.ambiente = a.codigo                                  \n" +
                        "		 	INNER JOIN nivelturma n                                        \n" +
                        "		 		ON ht.nivelturma = n.codigo                                \n" +
                        "		 	INNER JOIN colaborador c                                       \n" +
                        "		 		ON c.codigo = ht.professor                                 \n" +
                        "		 	INNER JOIN pessoa p                                            \n" +
                        "		 		ON c.pessoa = p.codigo                                     \n" +
                        "		WHERE emp.ativa                                                    \n" +
                        "		    AND t.ambiente notnull                                         \n" +
                        "			AND t.datafinalvigencia >= now()                               \n" +
                        "		 	AND (ht.situacao = 'AT' OR ht.datasaiuturma IS NOT NULL)       \n" +
                        "		 	AND (t.usuariodesativou IS NULL )                              \n" +
                        "		 	AND t.empresa = " + codigoEmpresa                                +
                        "           AND t.codigo not in (select alv.turma                         \n" +
                        "                                from aulavendasonlinelinkvisitante alv         \n" +
                        "                                where dataexclusao is null                      \n" +
                        "                                   and alv.turma = t.codigo        \n" +
                        "                               )                                          \n" +
                        "		ORDER BY t.descricao, a.descricao	                               " ;

        ResultSet resultSet = criarConsulta(query, con);
        List<TurmaEHorarioVendasOnlineVO> list = new ArrayList<>();
        while(resultSet.next()) {
            TurmaEHorarioVendasOnlineVO vo = new TurmaEHorarioVendasOnlineVO();
            vo.setCodigoTurma(resultSet.getInt("codturma"));
            vo.setNomeModalidade(resultSet.getString("modalidade"));
            vo.setCodigoModalidade(resultSet.getInt("codmodalidade"));
            vo.setDescricao(resultSet.getString("descricao"));
            vo.setAmbiente(resultSet.getString("ambiente"));
            vo.setDiaSemana(resultSet.getString("diassemana"));
            list.add(vo);
        }

        return list;
    }

    public List<AulasVendasOnline> consultarAulasVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception {
        String query =
                "	select t.descricao as turma, t.codigo as codigoturma,    \n" +
                "	 m.nome as modalidade,                                   \n" +
                "	 p.descricao descProduto,                                \n" +
                "	 p.valorfinal as valor, p.codigo codProduto,             \n" +
                "	 a.ativo, am.descricao AS ambiente                       \n" +
                "	from aulavendasonline a                                  \n" +
                "			inner join turma t                               \n" +
                "				on a.codigoturma = t.codigo                  \n" +
                "				and a.data_exclusao is null                  \n" +
                "           inner join ambiente as am                        \n" +
                "               on am.codigo = t.ambiente                    \n" +
                "	 		inner join modalidade m                          \n" +
                "	 			on a.codmodalidade = m.codigo                \n" +
                "	 		inner join produto p                             \n" +
                "	 			on a.codigoproduto = p.codigo                \n" +
                "	where t.empresa = " + codigoEmpresa;

        ResultSet resultSet = criarConsulta(query, con);
        List<AulasVendasOnline> list = new ArrayList<>();
        while(resultSet.next()) {
            AulasVendasOnline vo = new AulasVendasOnline();
            vo.setTurma(resultSet.getString("turma"));
            vo.setCodigoTurma(resultSet.getInt("codigoturma"));
            vo.setModalidade(resultSet.getString("modalidade"));
            vo.setCodigoProduto(resultSet.getInt("codProduto"));
            vo.setDescricaoProduto(resultSet.getString("descProduto"));
            vo.setValor(String.format("%.02f", resultSet.getFloat("valor")));
            vo.setAtivo(resultSet.getBoolean("ativo"));
            vo.setAmbiente(resultSet.getString("ambiente"));
            list.add(vo);
        }

        return list;
    }

    public Integer obterTipoAulasLinkVisitante(Integer configVendas) throws Exception {
        String query =
                "	select v.tipoAulasLinkVisitante                  \n" +
                        "	from vendasonlineconfig v                \n" +
                        "	where v.codigo = " + configVendas;

        ResultSet resultSet = criarConsulta(query, con);
        if (resultSet.next()) {
            return resultSet.getInt("tipoAulasLinkVisitante");
        }
        return 0;
    }

    public List<AulasVendasOnline> consultarAulasVendasOnlineLinkVisitantePorEmpresa(Integer codigoEmpresa, Integer config) throws Exception {
        String query =
                "	select a.codigo, t.descricao as turma, t.codigo as codigoturma,    \n" +
                        "	 m.nome as modalidade, m.codigo as codigomodalidade      \n" +
                        "	from aulavendasonlinelinkvisitante a                     \n" +
                        "			inner join turma t                               \n" +
                        "				on a.turma = t.codigo                  \n" +
                        "				and a.dataexclusao is null                    \n" +
                        "	 		inner join modalidade m                          \n" +
                        "	 			on a.modalidade = m.codigo                \n" +
                        "	where t.empresa = " + codigoEmpresa + "\n" +
                        "	and a.vendasonlineconfig = " + config;

        ResultSet resultSet = criarConsulta(query, con);
        List<AulasVendasOnline> list = new ArrayList<>();
        while(resultSet.next()) {
            AulasVendasOnline vo = new AulasVendasOnline();
            vo.setCodigo(resultSet.getInt("codigo"));
            vo.setTurma(resultSet.getInt("codigoturma") + " - " + resultSet.getString("turma"));
            vo.setCodigoTurma(resultSet.getInt("codigoturma"));
            vo.setModalidade(resultSet.getInt("codigomodalidade") + " - " + resultSet.getString("modalidade"));
            vo.setCodigoModalidade(resultSet.getInt("codigomodalidade"));
            list.add(vo);
        }

        return list;
    }
    public List<AulasVendasOnline> consultarAulasVendasOnlineAtivaPorEmpresa(Integer codigoEmpresa) throws Exception {
        String query =
                "	select t.descricao as turma, t.codigo as codigoturma,    \n" +
                        "	 m.nome as modalidade,                                   \n" +
                        "	 p.descricao descProduto,                                \n" +
                        "	 p.valorfinal as valor, p.codigo codProduto,             \n" +
                        "	 a.ativo, am.descricao AS ambiente                       \n" +
                        "	from aulavendasonline a                                  \n" +
                        "			inner join turma t                               \n" +
                        "				on a.codigoturma = t.codigo                  \n" +
                        "				and a.data_exclusao is null                  \n" +
                        "           inner join ambiente as am                        \n" +
                        "               on am.codigo = t.ambiente                    \n" +
                        "	 		inner join modalidade m                          \n" +
                        "	 			on a.codmodalidade = m.codigo                \n" +
                        "	 		inner join produto p                             \n" +
                        "	 			on a.codigoproduto = p.codigo                \n" +
                        "	where a.ativo and t.empresa = " + codigoEmpresa;

        ResultSet resultSet = criarConsulta(query, con);
        List<AulasVendasOnline> list = new ArrayList<>();
        while(resultSet.next()) {
            AulasVendasOnline vo = new AulasVendasOnline();
            vo.setTurma(resultSet.getString("turma"));
            vo.setCodigoTurma(resultSet.getInt("codigoturma"));
            vo.setModalidade(resultSet.getString("modalidade"));
            vo.setCodigoProduto(resultSet.getInt("codProduto"));
            vo.setDescricaoProduto(resultSet.getString("descProduto"));
            vo.setValor(String.format("%.02f", resultSet.getFloat("valor")));
            vo.setAtivo(resultSet.getBoolean("ativo"));
            vo.setAmbiente(resultSet.getString("ambiente"));
            list.add(vo);
        }

        return list;
    }

    public void mudarStatusAulaVendasOnline(Integer codigo) throws Exception {
        String query = "update aulavendasonline set ativo = not ativo where codigoproduto = ?";
        PreparedStatement ps = con.prepareStatement(query);
        ps.setInt(1, codigo);
        ps.executeUpdate();
    }

    public boolean consultarDominioProprioHotsite(Integer empresa) throws Exception {
        String query = "select dominiopropriohotsite from vendasonlineconfig where empresa = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(query)) {
                if (rs.next()) {
                    return rs.getBoolean("dominiopropriohotsite");
                } else {
                    return false;
                }
            }
        }
    }

    public void alterarDominioProprioHotsite(Integer empresa, boolean dominioproprio) throws Exception {
        String query = "update vendasonlineconfig set dominioProprioHotsite = " + dominioproprio + " where empresa = " + empresa;
        PreparedStatement ps = con.prepareStatement(query);
        ps.executeUpdate();
    }

    public void excluirAulaVendasOnline(VendasConfigVO vendasConfigVO, UsuarioVO usuarioVO, Integer codigo, boolean linkVisitante, Integer turma) throws Exception {
        String query = "";
        if (linkVisitante) {
            query = "delete from aulavendasonlinelinkvisitante where codigo = ?";
        } else {
            query = "update aulavendasonline set data_exclusao = now() where codigoproduto = ?";
        }
        PreparedStatement ps = con.prepareStatement(query);
        ps.setInt(1, codigo);
        ps.executeUpdate();

        if (!linkVisitante) {
            String queryExclusaoProdutoTurmaVendasOnline = "DELETE FROM produtoturmavendasonline WHERE codigoproduto = ?";
            PreparedStatement ps2 = con.prepareStatement(queryExclusaoProdutoTurmaVendasOnline);
            ps2.setInt(1, codigo);
            ps2.executeUpdate();
        }

        LogVO log = new LogVO();
        log.setNomeEntidade("VENDASCONFIG");
        log.setNomeEntidadeDescricao("VENDASCONFIG");
        log.setDescricao("VENDASCONFIG");
        log.setChavePrimaria(vendasConfigVO.getCodigo().toString());
        log.setDataAlteracao(Calendario.hoje());
        log.setUsuarioVO(usuarioVO);
        log.setResponsavelAlteracao(usuarioVO.getNomeAbreviado());
        log.setOperacao("EXCLUSÃO");
        log.setUserOAMD(usuarioVO.getUserOamd());
        log.setValorCampoAnterior("");
        if (linkVisitante) {
            log.setNomeCampo("AULA AGENDA LINK VISITANTE");
            log.setValorCampoAlterado("Codigo da turma: " + turma);
        } else {
            log.setNomeCampo("AGENDA");
            log.setValorCampoAlterado("Código do Produto: " + codigo);
        }

        Log logDAO = new Log(con);
        logDAO.incluirSemCommit(log);
    }

    public void excluirTodasAulaVendasOnline(int codConfig, UsuarioVO usuarioVO) throws Exception {
        String query = "delete from aulavendasonlinelinkvisitante where vendasonlineconfig = ?";

        PreparedStatement ps = con.prepareStatement(query);
        ps.setInt(1, codConfig);
        ps.executeUpdate();

        try {
            inserirLogExclusaoDeTodasAulas(usuarioVO, codConfig);
        } catch (Exception ignore) {
        }

    }

    public void inserirLogExclusaoDeTodasAulas(UsuarioVO usuarioVO, int codConfig) {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("VENDASCONFIG");
            log.setNomeEntidadeDescricao("VENDASCONFIG");
            log.setDescricao("VENDASCONFIG");
            log.setChavePrimaria(String.valueOf(codConfig));
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao("EXCLUSÃO");
            log.setValorCampoAnterior("");
            log.setNomeCampo("AULAS AGENDA LINK VISITANTE");
            log.setValorCampoAlterado("Aulas excluídas pois cliente optou em deixar 'Todas as aulas'");

            try {
                if (usuarioVO == null) {
                    if (context() != null) { //pegar da sessão
                        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        usuarioVO = loginControle.getUsuarioLogado();
                    }
                }
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("Não identificado");
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao tentar identificar o usuário logado no fluxo de incluir autorização de cobrança: " + ex.getMessage());
            }

            Log logDAO;
            try {
                logDAO = new Log(con);
                logDAO.incluirSemCommit(log);
            } catch (Exception ex) {
            } finally {
                logDAO = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("Erro ao gravar Log exclusão de aulavendasonlinelinkvisitante: " + ex.getMessage());
        }
    }

    public List<ProdutoVO> consultarProdutosVendasOnlinePorTurma(Integer turma) throws Exception {
        String query =
                "	select p.*                                                                 " +
                "			from turma t                                                       " +
                "				inner join modalidade m on m.codigo = t.modalidade             " +
                "				inner join produto p on m.codigo = p.modalidadevendasonline    " +
                "			where p.desativado = false                                         " +
                "					and p.apresentarvendasonline                               " +
                "					and t.codigo = " + turma;

        List<ProdutoVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta(query, con);
        while(resultSet.next()) {
            ProdutoVO vo = new ProdutoVO();
            vo.setCodigo(resultSet.getInt("codigo"));
            vo.setDescricao(resultSet.getString("descricao"));
            list.add(vo);
        }

        return list;
    }

    public TurmaEHorarioVendasOnlineVO consultarTurmaEHorarioVendasOnlinePorCodigoTurma(Integer codigoTurma) throws Exception {
        String query =
                "	select t.codigo codturma, h.codigo codhorario,         " +
                "	t.descricao, h.horainicial, h.horafinal, h.diasemana,  " +
                "	m.codigo as codmodalidade, m.valormensal, m.nome,      " +
                "   am.descricao AS ambiente                               " +
                "	from turma t                                           " +
                "		inner join horarioturma h                          " +
                "			on t.codigo = h.turma                          " +
                "			and h.ativo = true                             " +
                "       inner join ambiente as am                          " +
                "           on am.codigo = h.ambiente                      " +
                "		inner join modalidade m                            " +
                "           on m.codigo = t.modalidade                     " +
                "	where t.datafinalvigencia >= now()                     " +
                "	and t.codigo  = " + codigoTurma;

        ResultSet resultSet = criarConsulta(query, con);
        TurmaEHorarioVendasOnlineVO vo = new TurmaEHorarioVendasOnlineVO();
        while(resultSet.next()) {
            popularTurmaEHorarioVO(resultSet, vo);
        }

        return vo;
    }

    private void popularTurmaEHorarioVO(ResultSet resultSet, TurmaEHorarioVendasOnlineVO vo) throws SQLException {
        vo.setCodigoTurma(resultSet.getInt("codturma"));
        vo.setCodigoHorario(resultSet.getInt("codhorario"));
        vo.setCodigoModalidade(resultSet.getInt("codmodalidade"));
        vo.setDescricao(resultSet.getString("descricao"));
        vo.setHorarioInicial(resultSet.getString("horainicial"));
        vo.setHorarioFinal(resultSet.getString("horafinal"));
        vo.setDiaSemana(resultSet.getString("diasemana"));
        vo.setValorModalidade(resultSet.getDouble("valormensal"));
        vo.setNomeModalidade(resultSet.getString("nome"));
        vo.setAmbiente(resultSet.getString("ambiente"));
    }

    public MenuVendasOnlineVO consultarMenuVendasOnline(Integer codConfigVendasOnline) throws Exception{
        MenuVendasOnlineVO menu = new MenuVendasOnlineVO();
        ResultSet resultSet = criarConsulta("select * from menuvendasonline where codvendasonlineconfig = " + codConfigVendasOnline, con);
        while (resultSet.next()) {
            menu.setCodigo(resultSet.getInt("codigo"));
            menu.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            menu.setMenuPersonalizado01(resultSet.getString("menuPersonalizado01"));
            menu.setMenuPersonalizado02(resultSet.getString("menuPersonalizado02"));
            menu.setMenuPersonalizado03(resultSet.getString("menuPersonalizado03"));
            menu.setLinkMenuPersonalizado01(resultSet.getString("linkMenuPersonalizado01"));
            menu.setLinkMenuPersonalizado02(resultSet.getString("linkMenuPersonalizado02"));
            menu.setLinkMenuPersonalizado03(resultSet.getString("linkMenuPersonalizado03"));
            menu.setFotoKey(resultSet.getString("fotoKey"));

        }
        return menu;
    }

    public List<PlanosSiteVendasOnlineVO> consultarPlanosSiteVendasOnline(Integer codConfigVendasOnline) throws Exception{
        List<PlanosSiteVendasOnlineVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select * from planositevendasonline where codvendasonlineconfig = " + codConfigVendasOnline, con);
        while (resultSet.next()) {
            PlanosSiteVendasOnlineVO pSVO = new PlanosSiteVendasOnlineVO();
            pSVO.setCodigo(resultSet.getInt("codigo"));
            pSVO.setCodPlano(resultSet.getInt("codPlano"));
            pSVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            pSVO.setDestaqueCabecalho(resultSet.getString("destaqueCabecalho"));
            pSVO.setDescricaoPlano(resultSet.getString("descricaoPlano"));
            pSVO.setBeneficio01(resultSet.getString("beneficio01"));
            pSVO.setBeneficio02(resultSet.getString("beneficio02"));
            pSVO.setBeneficio03(resultSet.getString("beneficio03"));
            pSVO.setPrecoOriginal(resultSet.getDouble("precoOriginal"));
            pSVO.setBeneficios(resultSet.getString("lstbeneficios"));
            list.add(pSVO);
        }
        return list;
    }

    public List<PlanosSiteVendasOnlineVO> consultarPlanosSiteVendasOnlinePorEmpresa(Integer codigoEmpresa) throws Exception{
        List<PlanosSiteVendasOnlineVO> list = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select p.descricao as nomePlano, pd.numeromeses, pd.valordesejadomensal, p.recorrencia," +
                " p.maximovezesparcelar, c.* from planositevendasonline as c \n"+
                " inner join vendasonlineconfig v on v.codigo  = c.codvendasonlineconfig \n"+
                " inner join empresa e on e.codigo = v.empresa \n"+
                " left join plano p on p.codigo = c.codplano  \n"+
                " left join planoduracao pd on pd.plano = p.codigo \n"+
                " WHERE e.codigo  = " + codigoEmpresa, con);
        while (resultSet.next()) {
            PlanosSiteVendasOnlineVO pSVO = new PlanosSiteVendasOnlineVO();
            pSVO.setCodigo(resultSet.getInt("codigo"));
            pSVO.setCodPlano(resultSet.getInt("codPlano"));
            pSVO.setCodVendasOnlineConfig(resultSet.getInt("codVendasOnlineConfig"));
            pSVO.setDestaqueCabecalho(resultSet.getString("destaqueCabecalho"));
            pSVO.setDescricaoPlano(resultSet.getString("descricaoPlano"));
            pSVO.setBeneficio01(resultSet.getString("beneficio01"));
            pSVO.setBeneficio02(resultSet.getString("beneficio02"));
            pSVO.setBeneficio03(resultSet.getString("beneficio03"));
            pSVO.setPrecoOriginal(resultSet.getDouble("precoOriginal"));
            pSVO.setBeneficios(resultSet.getString("lstbeneficios"));
            pSVO.setValor(resultSet.getDouble("valordesejadomensal"));
            pSVO.setTitulo(resultSet.getString("nomePlano"));
            pSVO.setMaximoVezesParcelar(resultSet.getInt("maximovezesparcelar"));
            pSVO.setDuracao(resultSet.getInt("numeromeses"));
            pSVO.setRecorrencia(resultSet.getBoolean("recorrencia"));
            try {
                if (!pSVO.isRecorrencia() && pSVO.getMaximoVezesParcelar() > 1) {
                    // no caso de plano no recorrência o front usa o psVO.getValorMensalExibir
                    pSVO.setValorMensalExibir((pSVO.getValor() * pSVO.getDuracao()) / pSVO.getMaximoVezesParcelar());
                } else {
                    //setar zerado quando for recorrência, pois para recorrência o cálculo  outro. o front vai usar o psVO.getValor
                    pSVO.setValorMensalExibir(0.0);
                }
            } catch (Exception ex) {
                pSVO.setValorMensalExibir(0.0);
            }
            list.add(pSVO);
        }
        return list;
    }

    public void gravarImagemModalidadeCarrousel(String fotoKeyModalidadeCarrousel, Integer codModalidadeCarrousel) throws Exception{
        PreparedStatement stm = con.prepareStatement("update modalidadecarrouselvendasonline set fotokey = ? where codigo = ?");

        stm.setString(1, fotoKeyModalidadeCarrousel);
        stm.setInt(2, codModalidadeCarrousel);

        stm.execute();
    }

    public void excluirImagem(ImagensAcademiaVendasVO imagem) throws Exception{
        PreparedStatement stm = con.prepareStatement("delete from imagensacademiavendas where codigo = ?");

        stm.setInt(1, imagem.getCodigo());

        stm.execute();
    }

    public void excluirModaCarrousel(Integer codModaCarrousel) throws Exception{
        PreparedStatement stm = con.prepareStatement("delete from modalidadecarrouselvendasonline where codigo = ?");

        stm.setInt(1, codModaCarrousel);

        stm.execute();
    }

    public void excluirPlanoSite(Integer codPlanoSite) throws Exception{
        PreparedStatement stm = con.prepareStatement("delete from planositevendasonline where codigo = ?");

        stm.setInt(1, codPlanoSite);

        stm.execute();
    }

    public List<ImagensAcademiaVendasVO> imagens(Integer empresa) throws Exception{
        List<ImagensAcademiaVendasVO> imagens = new ArrayList<>();
        ResultSet resultSet = criarConsulta("select fotokey, codigo, empresa from imagensacademiavendas where empresa = " + empresa, con);
        while(resultSet.next()){
            imagens.add(new ImagensAcademiaVendasVO(resultSet.getInt("codigo"),
                    resultSet.getString("fotokey"),
                    resultSet.getInt("empresa")));
        }
        return imagens;
    }

    public String tagPagamentosms(String chave, Integer empresa, Integer codigoCliente, boolean cobrarParcelasEmAberto) throws Exception{
        VendasOnlineService vendasOnlineService;
        Cliente clienteDAO;
        try {
            vendasOnlineService = new VendasOnlineService(null, this.con);
            clienteDAO = new Cliente(this.con);

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return vendasOnlineService.obterLinkPagamentoVendasOnlineSms(chave, clienteVO, empresa, cobrarParcelasEmAberto, OrigemCobrancaEnum.MAILING, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            vendasOnlineService = null;
            clienteDAO = null;
        }
        return "";
    }

    public String tagPagamento(String chave, Integer empresa, Integer codigoCliente, boolean cobrarParcelasEmAberto) throws Exception{
        VendasOnlineService vendasOnlineService;
        Cliente clienteDAO;
        try {
            vendasOnlineService = new VendasOnlineService(null, this.con);
            clienteDAO = new Cliente(this.con);

            ClienteVO clienteVO = clienteDAO.consultarPorChavePrimaria(codigoCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return vendasOnlineService.obterLinkPagamentoVendasOnline(chave, clienteVO, empresa, cobrarParcelasEmAberto, OrigemCobrancaEnum.MAILING,
                    null, null, null, null, null, 1);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            vendasOnlineService = null;
            clienteDAO = null;
        }
        return "";
    }

    public boolean isApresentarProdutoSemEstoque(Integer empresa) {
        try {
            String sqlStr = "SELECT apresentarProdutoSemEstoque FROM vendasonlineconfig where empresa = " + empresa;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    if (rs.next()) {
                        return rs.getBoolean("apresentarProdutoSemEstoque");
                    } else {
                        return false;
                    }
                }
            }
        } catch (Exception ex) {
            return false;
        }
    }

    public boolean isEnviarEmailUsuarioMovelAutomaticamente(Integer empresa) {
        try {
            String sqlStr = "SELECT enviarEmailUsuarioMovelAutomaticamente FROM vendasonlineconfig where empresa = " + empresa;
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlStr)) {
                    if (rs.next()) {
                        return rs.getBoolean("enviarEmailUsuarioMovelAutomaticamente");
                    } else {
                        return false;
                    }
                }
            }
        } catch (Exception ex) {
            return false;
        }
    }

    public EmailPagamentoTO obterDados(Integer transacao, Integer pix) throws Exception {
        if (UteisValidacao.emptyNumber(transacao) && UteisValidacao.emptyNumber(pix)) {
            throw new Exception("Código Transação ou Pix não informado");
        }

        StringBuilder sql = new StringBuilder();

        if (!UteisValidacao.emptyNumber(transacao)) {
            //consultar dados transacao
            sql.append("select  \n");
            sql.append("e.codigo as empresa, \n");
            sql.append("e.nome as nomefantasia, \n");
            sql.append("e.cnpj as cnpj, \n");
            sql.append("e.email as emailempresa, \n");
            sql.append("e.telcomercial1 as telefoneempresa, \n");
            sql.append("p.nome, \n");
            sql.append("em.email, \n");
            sql.append("tr.valor, \n");
            sql.append("rp.data as datapagamento, \n");
            sql.append("mov.autorizacaocartao as autorizacao, \n");
            sql.append("split_part(split_part(tr.outrasinformacoes, 'cartaoMascarado\":\"', 2), '\"', 1) as cartao \n");
            sql.append("from transacao tr \n");
            sql.append("inner join empresa e on e.codigo = tr.empresa \n");
            sql.append("inner join pessoa p on p.codigo = tr.pessoapagador \n");
            sql.append("inner join recibopagamento rp on rp.codigo = tr.recibopagamento \n");
            sql.append("inner join movpagamento mov on mov.codigo = tr.movpagamento \n");
            sql.append("left join email em on em.codigo = (select max(codigo) from email where pessoa = p.codigo and emailcorrespondencia) \n");
            sql.append("where tr.codigo = ").append(transacao).append(" \n");
        } else {
            //consultar dados pix
            sql.append("select  \n");
            sql.append("e.codigo as empresa, \n");
            sql.append("e.nome as nomefantasia, \n");
            sql.append("e.cnpj as cnpj, \n");
            sql.append("e.email as emailempresa, \n");
            sql.append("e.telcomercial1 as telefoneempresa, \n");
            sql.append("p.nome, \n");
            sql.append("em.email, \n");
            sql.append("px.valor, \n");
            sql.append("rp.data as datapagamento, \n");
            sql.append("px.txid as autorizacao, \n");
            sql.append("'' as cartao \n");
            sql.append("from pix px \n");
            sql.append("inner join empresa e on e.codigo = px.empresa \n");
            sql.append("inner join pessoa p on p.codigo = px.pessoa \n");
            sql.append("inner join recibopagamento rp on rp.codigo = px.recibopagamento \n");
            sql.append("inner join movpagamento mov on mov.recibopagamento = px.recibopagamento \n");
            sql.append("left join email em on em.codigo = (select max(codigo) from email where pessoa = p.codigo and emailcorrespondencia) \n");
            sql.append("where px.codigo = ").append(pix).append(" \n");
        }

        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    EmailPagamentoTO email = new EmailPagamentoTO(UteisValidacao.emptyNumber(transacao) ? TipoCobrancaEnum.PIX : TipoCobrancaEnum.ONLINE);
                    email.setEmpresa(rs.getInt("empresa"));
                    email.setEmpresaNomeFantasia(rs.getString("nomefantasia"));
                    email.setEmpresaCNPJ(rs.getString("cnpj"));
                    email.setEmpresaEmail(rs.getString("emailempresa"));
                    email.setEmpresaTelefone(rs.getString("telefoneempresa"));
                    email.setNomePessoa(rs.getString("nome"));
                    email.setCartao(rs.getString("cartao"));
                    email.setEmail(rs.getString("email"));
                    email.setValor(rs.getDouble("valor"));
                    email.setDataPagamento(rs.getTimestamp("dataPagamento"));
                    email.setCodigoAutorizacao(rs.getString("autorizacao"));
                    return email;
                }
            }
        }
        return null;
    }

    public StringBuilder gerarCorpoEmailPagamento(EmailPagamentoTO emailPagamentoTO) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailPagamentoTransacao.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath());

        String chave = DAO.resolveKeyFromConnection(this.con);
        String cartao = "";
        if (emailPagamentoTO.getCartao().contains("******")) {
            cartao = emailPagamentoTO.getCartao().split("\\*\\*\\*\\*\\*\\*")[1];
        }

        String aux = texto.toString()
                .replaceAll("#NOME_EMPRESA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(emailPagamentoTO.getEmpresaNomeFantasia().toUpperCase()))
                .replaceAll("#DATA", Uteis.trocarAcentuacaoPorAcentuacaoHTML(Uteis.getDataComHora(emailPagamentoTO.getDataPagamento())))
                .replaceAll("#CNPJ", Uteis.trocarAcentuacaoPorAcentuacaoHTML(emailPagamentoTO.getEmpresaCNPJ()))
                .replaceAll("#ULTIMO_CARTAO", cartao)
                .replaceAll("#AUTORIZACAO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(emailPagamentoTO.getCodigoAutorizacao()))
                .replaceAll("#VALOR", Uteis.trocarAcentuacaoPorAcentuacaoHTML(Formatador.formatarValorMonetario(emailPagamentoTO.getValor())))
                .replaceAll("#PRODUTO", Uteis.trocarAcentuacaoPorAcentuacaoHTML(emailPagamentoTO.getProduto()))
                .replaceAll("#URL_LOGO_EMPRESA", emailPagamentoTO.getUrlLogoEmpresa(chave))
                .replaceAll("#TRANSACAO", emailPagamentoTO.getTipoCobrancaEnum().equals(TipoCobrancaEnum.ONLINE) ? "" : "style=\"display: none\"")
                .replaceAll("#PIX", emailPagamentoTO.getTipoCobrancaEnum().equals(TipoCobrancaEnum.PIX) ? "" : "style=\"display: none\"")
//                .replaceAll("#APRESENTAR_COMPROVANTE", emailPagamentoTO.getTipoCobrancaEnum().equals(TipoCobrancaEnum.ONLINE) ? "block" : "none"
                .replaceAll("#APRESENTAR_COMPROVANTE", "none")// não foi implementado o download
//                .replaceAll("#URL_COMPROVANTE", emailPagamentoTO.getUrlLogoEmpresa(chave))
                .replaceAll("#NOME_CLIENTE", Uteis.trocarAcentuacaoPorAcentuacaoHTML(emailPagamentoTO.getNomePessoa().toUpperCase()));
        return new StringBuilder(aux);
    }

    public Integer consultarCampanhaCupomDescontoIndicacoes(Integer empresa) throws Exception {
        String query = "select campanhaCupomDescontoIndicacoes from vendasonlineconfig where empresa = " + empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(query)) {
                if (rs.next()) {
                    return rs.getInt("campanhaCupomDescontoIndicacoes");
                } else {
                    return 0;
                }
            }
        }
    }
}
