package negocio.facade.jdbc.vendas;

import controle.vendasonline.CamposAdicionaisVendasOnlineEnum;
import negocio.comuns.utilitarias.UteisValidacao;

public class CampoAdicionalObrigatorioVO {

    protected CamposAdicionaisVendasOnlineEnum obj;
    protected String descricao;
    protected boolean obrigatorioPlano = false;
    protected boolean obrigatorioProduto = false;
    protected boolean obrigatorioPlanoFlow = false;
    protected boolean obrigatorioProdutoFlow = false;
    protected boolean habilitaCampoPlano = false;
    protected boolean habilitaCampoProduto = false;
    protected boolean habilitaCampoPlanoFlow = false;
    protected boolean habilitaCampoProdutoFlow = false;
    protected String hint;

    public CampoAdicionalObrigatorioVO(CamposAdicionaisVendasOnlineEnum obj, String descricao, boolean obrigatorioPlano, boolean obrigatorioProduto, boolean habilitaCampoPlano, boolean habilitaCampoProduto, String hint) {
        this.obj = obj;
        this.descricao = descricao;
        this.obrigatorioPlano = obrigatorioPlano;
        this.obrigatorioProduto = obrigatorioProduto;
        this.obrigatorioPlanoFlow = false;
        this.obrigatorioProdutoFlow = false;
        this.habilitaCampoPlano = habilitaCampoPlano;
        this.habilitaCampoProduto = habilitaCampoProduto;
        this.habilitaCampoPlanoFlow = habilitaCampoPlano;
        this.habilitaCampoProdutoFlow = habilitaCampoProduto;
        this.hint = hint;
    }

    public CamposAdicionaisVendasOnlineEnum getObj() {
        return obj;
    }

    public void setObj(CamposAdicionaisVendasOnlineEnum obj) {
        this.obj = obj;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean isObrigatorioPlano() {
        return obrigatorioPlano;
    }

    public void setObrigatorioPlano(boolean obrigatorioPlano) {
        this.obrigatorioPlano = obrigatorioPlano;
    }

    public boolean isObrigatorioProduto() {
        return obrigatorioProduto;
    }

    public boolean isObrigatorioPlanoFlow() {
        return obrigatorioPlanoFlow;
    }

    public void setObrigatorioPlanoFlow(boolean obrigatorioPlanoFlow) {
        this.obrigatorioPlanoFlow = obrigatorioPlanoFlow;
    }

    public boolean isObrigatorioProdutoFlow() {
        return obrigatorioProdutoFlow;
    }

    public void setObrigatorioProdutoFlow(boolean obrigatorioProdutoFlow) {
        this.obrigatorioProdutoFlow = obrigatorioProdutoFlow;
    }

    public void setObrigatorioProduto(boolean obrigatorioProduto) {
        this.obrigatorioProduto = obrigatorioProduto;
    }

    public boolean isHabilitaCampoPlano() {
        return habilitaCampoPlano;
    }

    public void setHabilitaCampoPlano(boolean habilitaCampoPlano) {
        this.habilitaCampoPlano = habilitaCampoPlano;
    }

    public boolean isHabilitaCampoProduto() {
        return habilitaCampoProduto;
    }

    public void setHabilitaCampoProduto(boolean habilitaCampoProduto) {
        this.habilitaCampoProduto = habilitaCampoProduto;
    }

    public String getHint() {
        return hint;
    }

    public void setHint(String hint) {
        this.hint = hint;
    }

    public boolean isExibirIconeInfoAdicional() {
        if (!UteisValidacao.emptyString(getDescricao())) {
            if (getDescricao().equals("Responsável Pai") || getDescricao().equals("Responsável Mãe") ||
                    getDescricao().equals("CPF Responsável Pai") || getDescricao().equals("CPF Responsável Mãe")) {
                return true;
            }
        }
        return false;
    }

    public String getStyleCheckBoxWithIcon() {
        if (isExibirIconeInfoAdicional()) {
            return "margin-left: 14px;";
        }
        return "";
    }

    public boolean isHabilitaCampoPlanoFlow() {
        return habilitaCampoPlanoFlow;
    }

    public void setHabilitaCampoPlanoFlow(boolean habilitaCampoPlanoFlow) {
        this.habilitaCampoPlanoFlow = habilitaCampoPlanoFlow;
    }

    public boolean isHabilitaCampoProdutoFlow() {
        return habilitaCampoProdutoFlow;
    }

    public void setHabilitaCampoProdutoFlow(boolean habilitaCampoProdutoFlow) {
        this.habilitaCampoProdutoFlow = habilitaCampoProdutoFlow;
    }
}
