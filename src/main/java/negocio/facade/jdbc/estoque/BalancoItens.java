/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.facade.jdbc.estoque;

import negocio.comuns.estoque.BalancoItensVO;
import negocio.comuns.estoque.BalancoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.estoque.BalancoItensInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class BalancoItens extends SuperEntidade implements BalancoItensInterfaceFacade {


    public BalancoItens() throws Exception {
        super();
        setIdEntidade("BalancoItens");
    }

    public BalancoItens(Connection con) throws Exception {
        super(con);
        setIdEntidade("BalancoItens");

    }
    public BalancoItensVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM BalancoItens WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm.intValue());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( BalancoItens ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List<BalancoItensVO> consultarPorBalanco(Integer codigoBalanco, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder(getSqlPadrao()).append(" where bi.Balanco = ").append(codigoBalanco);
        sql.append(" order by p.descricao ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = pst.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public Date pesquisarBalancoComDataMaior(Date dataComparar, Integer codigoProduto, Integer codigoEmpresa)throws Exception{
        StringBuilder sql = new StringBuilder();

        sql.append("select max(b.dataCadastro) dataBalanco \n");
        sql.append("from balanco b \n");
        sql.append("inner join balancoItens bi on bi.balanco = b.codigo \n");
        sql.append("where cancelado = false and bi.produto = ").append(codigoProduto).append(" and b.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("and b.dataCadastro > '").append(Calendario.getData(dataComparar, "yyyy-MM-dd HH:mm:ss")).append("'");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
           return rs.getTimestamp("dataBalanco");
        }
        return null;
    }

    /**
     * MontarDados para varios objetos
     * @param tabelaResultado
     * @param nivelMontarDados
     * @return
     * @throws Exception
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado,
            int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            BalancoItensVO obj = new BalancoItensVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>BalancoVO</code>.
     * @return  O objeto da classe <code>BalancoVO</code> com os dados devidamente montados.
     */
    public static BalancoItensVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        BalancoItensVO obj = new BalancoItensVO();
        obj.setCodigo(dadosSQL.getInt("codigoBalancoItens"));
        obj.getBalanco().setCodigo(dadosSQL.getInt("codigoBalanco"));
        obj.getProduto().setCodigo(dadosSQL.getInt("codigoProduto"));
        obj.getProduto().setDescricao(dadosSQL.getString("descricaoProduto"));
		
        obj.setQtdeBalanco(dadosSQL.getInt("qtdeBalanco"));
		obj.setQtdeEstoqueAnterior(dadosSQL.getInt("qtdeEstoqueAnterior"));

        obj.setNovoObj(false);
        return obj;
    }


    private String getSqlPadrao(){
        StringBuilder sql = new StringBuilder();
        sql.append("select ");
        sql.append("bi.codigo as codigoBalancoItens, ");
        sql.append("b.codigo as codigoBalanco, ");
        sql.append("p.codigo as codigoProduto, ");
        sql.append("p.descricao descricaoProduto, ");
        sql.append("bi.qtdeBalanco, ");
        sql.append("bi.qtdeEstoqueAnterior ");
        sql.append("from balancoItens bi ");
        sql.append("inner join balanco b on b.codigo = bi.balanco ");
        sql.append("inner join produto p on p.codigo = bi.produto ");
        return sql.toString();


    }
	/**
     * Operação responsável por retornar um novo objeto da classe <code>BalancoItensVO</code>.
     */
    public BalancoItensVO novo() throws Exception {
        incluir(getIdEntidade());
        BalancoItensVO obj = new BalancoItensVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>BalancoItensVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>BalancoItensVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void incluir(BalancoItensVO obj) throws Exception {
        BalancoItensVO.validarDados(obj);
        //incluir(getIdEntidade());
        String sql = "INSERT INTO BalancoItens(balanco, produto, qtdeBalanco, qtdeEstoqueAnterior) VALUES ( ?, ?, ?, ?)";
		

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getBalanco().getCodigo());
        sqlInserir.setInt(2, obj.getProduto().getCodigo());
        sqlInserir.setInt(3, obj.getQtdeBalanco());
		sqlInserir.setInt(4, obj.getQtdeEstoqueAnterior());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(new Boolean(false));
    }

    public void incluirListaBalancoItens(BalancoVO balancoVO, Set<BalancoItensVO> lista) throws Exception {
        for (BalancoItensVO obj: lista){
            obj.setBalanco(balancoVO);
            incluir(obj);
        }
    }


}
