package negocio.facade.jdbc.feed;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.feed.CondicaoEnum;
import negocio.comuns.feed.FeedGestaoVO;
import negocio.comuns.feed.DicaEnum;
import negocio.comuns.feed.FeedGestaoHistoricoVO;
import negocio.comuns.feed.IndicadorEnum;
import negocio.comuns.feed.PaginaInicialFeedVO;
import negocio.comuns.feed.PerfilUsuarioEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import static negocio.facade.jdbc.arquitetura.SuperFacadeJDBC.resolveIntegerNullComZero;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.feed.FeedGestaoInterfaceFacade;

import javax.rmi.CORBA.Util;

/**
 *
 * <AUTHOR>
 */
public class FeedGestao extends SuperEntidade implements FeedGestaoInterfaceFacade {

    public FeedGestao() throws Exception {
        super();
    }

    public FeedGestao(Connection con) throws Exception {
        super(con);
    }

    @Override
    public void incluir(FeedGestaoVO obj, Integer empresa) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj, empresa);
            con.commit();
        } catch (Exception e) {
            obj.setNovoObj(true);
            obj.setCodigo(new Integer(0));
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void incluirSemCommit(FeedGestaoVO obj, Integer empresa) throws Exception {
        StringBuilder insert = new StringBuilder();
        insert.append("INSERT INTO feedgestao( condicao, diames, dica, mensagem, nome, periodicidade,  \n");
        insert.append("    valorcondicao, vigenciaate, vigenciade, perfil, empresa, codigooamd, codigohistorico, indicadorgrupo) \n");
        insert.append("VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?); ");

        PreparedStatement insStm = con.prepareStatement(insert.toString());
        int i = 1;

        resolveIntegerNullComZero(insStm, i++, obj.getCondicao() == null ? null : obj.getCondicao().ordinal());
        resolveIntegerNullComZero(insStm, i++, obj.getDiaMes());
        resolveIntegerNullComZero(insStm, i++, obj.getDica() == null ? null : obj.getDica().ordinal());
        insStm.setString(i++, obj.getMensagem());
        insStm.setString(i++, obj.getNome());
        resolveIntegerNullComZero(insStm, i++, obj.getPeriodicidade());
        resolveDoubleNull(insStm, i++, obj.getValorCondicao());
        resolveDateNull(insStm, i++, obj.getVigenciaDe());
        resolveDateNull(insStm, i++, obj.getVigenciaAte());
        resolveIntegerNullComZero(insStm, i++, obj.getPerfil() == null ? null : obj.getPerfil().ordinal());
        insStm.setInt(i++, empresa);
        insStm.setInt(i++, obj.getCodigoOAMD());
        resolveIntegerNull(insStm, i++, obj.getCodigoHistorico());
        resolveIntegerNullComZero(insStm, i++, obj.getIndicadorGrupo() == null ? null : obj.getIndicadorGrupo().ordinal());

        insStm.execute();

        obj.setCodigo(obterValorChavePrimariaCodigo());
    }

    @Override
    public void deletarTodos(Integer empresa) throws Exception {
        executarConsulta("DELETE FROM feedgestao WHERE empresa = " + empresa, con);
    }

    @Override
    public void deletarPorTipo(Integer empresa, DicaEnum dica) throws Exception {
        executarConsulta("DELETE FROM feedgestao WHERE empresa = " + empresa + " AND dica = " + dica.ordinal(), con);
    }

    @Override
    public FeedGestaoVO consultarPorDica(DicaEnum dica, Integer empresa) throws Exception {
        ResultSet rs = criarConsulta("SELECT * FROM feedgestao WHERE dica = " + dica.ordinal() + " AND empresa = " + empresa, con);
        if (rs.next()) {
            return montarDados(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
        }
        return null;
    }

    @Override
    public List<FeedGestaoVO> obterTodos(Integer empresa, UsuarioVO usuario, Integer perfil) throws Exception {
        StringBuilder sql = new StringBuilder(" SELECT fg.*, fgl.datalida, fgl.liked, fgl.disliked, fgh.dataapresentar FROM feedgestao fg \n");
        sql.append(" LEFT JOIN feedgestaohistorico fgh ON fgh.codigo = fg.codigohistorico \n");
        sql.append(" LEFT JOIN feedgestaolida fgl ON fgh.codigo = fgl.codigohistorico AND fgl.usuario = ").append(usuario.getCodigo());
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append(" WHERE fg.empresa = ").append(empresa);
            if (!UteisValidacao.emptyNumber(perfil)) {
                sql.append(" and (perfil is null or perfil = ");
                sql.append(PerfilUsuarioEnum.TODOS.ordinal());
                sql.append(" or perfil = ").append(perfil).append(") ");
            }
        }
        sql.append(sql.toString().contains("WHERE") ? " AND " : " WHERE ");
        sql.append("(fg.indicadorgrupo IS NULL OR fg.indicadorgrupo <> ").append(IndicadorEnum.TELA_BI.getId()).append(")");
        sql.append(" ORDER BY fgl.datalida DESC ");

        ResultSet rs = criarConsulta(sql.toString(), con);
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    @Override
    public List<FeedGestaoVO> obterFeedsTelaBI(Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder(" SELECT fg.*, fgl.datalida, fgl.liked, fgl.disliked FROM feedgestao fg \n");
        sql.append(" LEFT JOIN feedgestaohistorico fgh ON fgh.codigo = fg.codigohistorico \n");
        sql.append(" LEFT JOIN feedgestaolida fgl ON fgh.codigo = fgl.codigohistorico \n");
        sql.append(" WHERE fg.empresa = ").append(empresa);
        sql.append(" AND (perfil is null or perfil = 0 or perfil = 1) \n");
        sql.append("AND fg.indicadorgrupo = ").append(IndicadorEnum.TELA_BI.getId());
        ResultSet rs = criarConsulta(sql.toString(), con);
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    public static List<FeedGestaoVO> montarDadosConsulta(ResultSet tabelaResultado, Integer nivelMontarDados, Connection con) throws Exception {
        List<FeedGestaoVO> vetResultado = new ArrayList<FeedGestaoVO>();
        while (tabelaResultado.next()) {
            FeedGestaoVO obj = FeedGestao.montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }

        return vetResultado;
    }

    public static FeedGestaoVO montarDados(ResultSet rs, Integer nivelMontarDados, Connection con) throws Exception {
        FeedGestaoVO feed = new FeedGestaoVO();
        feed.setCodigo(rs.getInt("codigo"));
        feed.setNome(rs.getString("nome"));
        feed.setMensagem(rs.getString("mensagem"));
        feed.setDica(DicaEnum.getFromOrdinal(rs.getInt("dica")));
        feed.setCondicao(CondicaoEnum.getFromOrdinal(rs.getInt("condicao")));
        feed.setPerfil(PerfilUsuarioEnum.getFromOrdinal(rs.getInt("perfil")));
        feed.setValorCondicao(rs.getDouble("valorcondicao"));
        feed.setVigenciaAte(rs.getDate("vigenciaate"));
        feed.setVigenciaDe(rs.getDate("vigenciade"));
        feed.setPeriodicidade(rs.getInt("periodicidade"));
        feed.setDiaMes(rs.getInt("diames"));
        feed.setEmpresa(rs.getInt("empresa"));
        feed.setCodigoOAMD(rs.getInt("codigooamd"));
        feed.setCodigoHistorico(rs.getInt("codigohistorico"));

        feed.setIndicadorGrupo(IndicadorEnum.getFromOrdinal(rs.getInt("indicadorgrupo")));
        try {
            feed.setDataVista(rs.getDate("datalida"));
            feed.setLiked(rs.getBoolean("liked"));
            feed.setDisliked(rs.getBoolean("disliked"));
        } catch (Exception e) {
        }

        try{
            feed.setDataProcessada(rs.getDate("dataapresentar"));
        }catch (Exception e){
        }
        return feed;
    }

    @Override
    public boolean usouRecorrenciaPeriodo(Date inicio, Date fim, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(codigo) AS cont FROM transacao \n");
        sql.append(" WHERE dataprocessamento BETWEEN '").append(Uteis.getDataJDBC(inicio));
        sql.append(" 00:00:00' AND '").append(Uteis.getDataJDBC(inicio)).append(" 23:59:59' and empresa = ").append(empresa);
        sql.append(" UNION ALL ");
        sql.append(" SELECT count(codigo) AS cont FROM remessa ");
        sql.append(" WHERE dataregistro BETWEEN '").append(Uteis.getDataJDBC(inicio));
        sql.append(" 00:00:00' AND '").append(Uteis.getDataJDBC(inicio)).append(" 23:59:59'  and empresa = ").append(empresa);
        ResultSet rs = criarConsulta(sql.toString(), con);
        while (rs.next()) {
            if (rs.getInt("cont") > 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void marcarLida(Integer codigoHistorico, Integer usuario) throws Exception {
        StringBuilder insert = new StringBuilder("INSERT INTO feedgestaolida ");
        insert.append(" (codigohistorico, datalida, usuario) VALUES (?,?,?) ");
        PreparedStatement stm = con.prepareStatement(insert.toString());
        stm.setInt(1, codigoHistorico);
        Date now = new Date(System.currentTimeMillis());
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(now));
        stm.setInt(3, usuario);
        stm.execute();
    }

    @Override
    public void marcarAvaliacao(Integer codigoHistorico, Integer usuario, boolean liked, boolean disliked) throws Exception {
        if(!isFeedGestaoLida(codigoHistorico, usuario)) {
            throw new Exception("Não existe registro de Feed para o codigo Usuário: "+codigoHistorico+ " e codigo usuário: "+ usuario);
        }

        Integer codigoFeedGestao = codigoFeedGestao(codigoHistorico, usuario);

        PreparedStatement stm = con.prepareStatement("UPDATE feedgestaolida SET liked = ?, disliked = ? WHERE codigo = ?");
        stm.setBoolean(1, liked);
        stm.setBoolean(2, disliked);
        stm.setInt(3, codigoFeedGestao);
        stm.execute();
    }

    @Override
    public Integer codigoFeedGestao(Integer codigoHistorico, Integer usuario) throws Exception {
        ResultSet rs = criarConsulta("select * from feedgestaolida\n" +
                "where usuario = " + usuario +
                " and codigohistorico = " + codigoHistorico
                , con);
        Integer codigoFeedGestaoLida = 0;
        while(rs.next()){
            codigoFeedGestaoLida = rs.getInt("codigo");
        }
        return codigoFeedGestaoLida;
    }

    @Override
    public boolean isFeedGestaoLida(Integer codigoHistorico, Integer usuario) throws Exception {
        ResultSet rs = criarConsulta("select * from feedgestaolida\n" +
                "where usuario = " + usuario +
                " and codigohistorico = " + codigoHistorico
                , con);
        Integer codigoFeedGestaoLida = 0;
        if(rs.next()){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public Integer inserirHistorico(Integer codigoOAMD, Integer empresa, Date dia) throws Exception {
        StringBuilder insert = new StringBuilder("INSERT INTO feedgestaohistorico ");
        insert.append(" (codigofeedoamd, dataapresentar, empresa) VALUES (?,?,?) ");
        PreparedStatement stm = con.prepareStatement(insert.toString());
        stm.setInt(1, codigoOAMD);
        stm.setDate(2, Uteis.getDataJDBC(dia));
        stm.setInt(3, empresa);
        stm.execute();

        return Conexao.obterUltimoCodigoGeradoTabela(con, "feedgestaohistorico");
    }

    @Override
    public FeedGestaoHistoricoVO obterUltimaApresentacaoDaDica(Integer codigoOamd) throws Exception {
        ResultSet rs = criarConsulta("SELECT fgh.*, fgl.codigo as lida FROM feedgestaohistorico fgh"
                + " LEFT JOIN feedgestaolida fgl ON fgl.codigohistorico = fgh.codigo "
                + " WHERE fgh.codigofeedoamd = " + codigoOamd
                + " ORDER BY fgh.dataapresentar DESC LIMIT 1", con);
        if (rs.next()) {
            FeedGestaoHistoricoVO feedHistorico = new FeedGestaoHistoricoVO();
            feedHistorico.setDia(rs.getDate("dataapresentar"));
            feedHistorico.setFoiVisto(!UteisValidacao.emptyNumber(rs.getInt("lida")));
            return feedHistorico;
        }
        return null;
    }

    @Override
    public String gerarFeedBack(Date data) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select fgl.datalida, fgl.liked, fgl.disliked, fgl.usuario, fgh.codigofeedoamd, \n");
        sql.append(" fgh.empresa, emp.nome as nomeempresa from feedgestaolida fgl \n");
        sql.append(" INNER JOIN feedgestaohistorico fgh ON fgh.codigo = fgl.codigohistorico  \n");
        sql.append(" INNER JOIN empresa emp ON emp.codigo = fgh.empresa  \n");
        sql.append(" where datalida BETWEEN ? AND ? ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(data)));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.getDataComHora(data, "23:59")));
        String retorno = "";
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            String historico = rs.getString("nomeempresa");
            historico += ";" + Uteis.getDataComHora(rs.getTimestamp("datalida"));
            historico += ";" + (rs.getBoolean("liked") ? "t" : "f");
            historico += ";" + (rs.getBoolean("disliked") ? "t" : "f");
            historico += ";" + rs.getInt("empresa");
            historico += ";" + rs.getInt("codigofeedoamd");
            historico += ";" + rs.getInt("usuario");
            retorno += "|" + historico;
        }
        return retorno;
    }
}
