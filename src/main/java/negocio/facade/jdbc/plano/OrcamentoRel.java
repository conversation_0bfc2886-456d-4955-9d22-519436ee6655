package negocio.facade.jdbc.plano;

import negocio.comuns.plano.OrcamentoRelVO;
import negocio.comuns.plano.OrcamentoVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.plano.OrcamentoRelInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class OrcamentoRel extends SuperEntidade implements OrcamentoRelInterfaceFacade {

    public OrcamentoRel() throws Exception {
        super();
        setIdEntidade("OrcamentoRel");
    }

    public OrcamentoRel(Connection con) throws Exception {
        super(con);
    }

    @Override
    public String consultarJSON() throws Exception {
        ResultSet rs = getRS();
        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigoorcamento")).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomecliente").trim())).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("modeloorcamento").trim())).append("\",");
            json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("nomecolaborador").trim())).append("\",");
            json.append("\"").append(rs.getDate("dataregistroorcamento")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    @Override
    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int i) throws SQLException {

        ResultSet rs = getRS();
        List lista = new ArrayList();

        while (rs.next()) {

            OrcamentoRelVO texto = new OrcamentoRelVO();
            String geral = rs.getString("codigoorcamento") + rs.getString("nomeCliente") + rs.getString("modeloorcamento") + rs.getString("nomeColaborador")+ rs.getDate("dataRegistroOrcamento");
            if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                texto.setCodigo(rs.getInt("codigoorcamento"));
                texto.setNomeCliente(rs.getString("nomeCliente"));
                texto.setModeloorcamento(rs.getString("modeloorcamento"));
                texto.setNomeColaborador(rs.getString("nomeColaborador"));
                texto.setDataregistro(rs.getDate("dataRegistroOrcamento"));
                lista.add(texto);
            }
        }
        if (campoOrdenacao.equals("codigoorcamento")) {
            Ordenacao.ordenarLista(lista, "codigoorcamento");
        } else if (campoOrdenacao.equals("nomeCliente")) {
            Ordenacao.ordenarLista(lista, "nomeCliente");
        } else if (campoOrdenacao.equals("modeloorcamento")) {
            Ordenacao.ordenarLista(lista, "modeloorcamento");
        } else if (campoOrdenacao.equals("nomeColaborador")) {
            Ordenacao.ordenarLista(lista, "nomeColaborador");
        } else if (campoOrdenacao.equals("dataRegistroOrcamento")) {
            Ordenacao.ordenarLista(lista, "dataRegistroOrcamento");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    private ResultSet getRS() throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT orc.codigo as codigoOrcamento, p.nome as nomeCliente, mo.descricao as modeloOrcamento, pco.nome as nomeColaborador, orc.data as dataRegistroOrcamento ");
        sql.append(" FROM orcamento orc ");
        sql.append(" inner join cliente c on c.codigo = orc.cliente ");
        sql.append(" inner join pessoa p on p.codigo = c.pessoa ");
        sql.append(" inner join modeloorcamento mo on mo.codigo = orc.modeloorcamento ");
        sql.append(" inner join colaborador co on co.codigo = orc.consultor ");
        sql.append(" inner join pessoa pco on pco.codigo = co.pessoa ");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        ResultSet rs = sqlConsultar.executeQuery();
        return rs;
    }
}
