package negocio.facade.jdbc.financeiro.lancamentos;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 14/08/2018
 */
enum ColunasColaboradorPessoa implements ColunaPrefixavel {

    CODIGO("codigo"),
    NOME("nome"),
    PROFISSAO("profissao"),
    DATA_CADASTRO("dataCadastro"),
    DATA_NASC("dataNasc"),
    NOME_PAI("nomePai"),
    NOME_MAE("nomeMae"),
    CFP("cfp"),
    LIBERA_SENHA_ACESSO("liberaSenhaAcesso"),
    RG("rg"),
    RG_ORGAO("rgOrgao"),
    RG_UF("rgUf"),
    CIDADE("cidade"),
    ESTADO("estado"),
    PAIS("pais"),
    ESTADO_CIVIL("estadoCivil"),
    NACIONALIDADE("nacionalidade"),
    NATURALIDADE("naturalidade"),
    SEXO("sexo"),
    WEB_PAGE("webPage"),
    GRAU_INSTRUCAO("grauInstrucao"),
    TIPO_PESSOA("tipoPessoa"),
    FOTO_KEY("fotokey"),
    ID_VINDI("idvindi"),
    DATA_ALTERACAO_VINDI("dataalteracaovindi"),
    ID_MAXI_PAGO("idMaxiPago"),
    DATA_ALTERACAO_MAXI_PAGO("dataAlteracaoMaxiPago"),
    FOTO("foto"),
    CNPJ("cnpj"),
    INSC_ESTADUAL("inscEstadual"),
    INSC_MUNICIPAL("inscMunicipal"),
    CFDF("cfdf"),
    EMITIR_NOTA_NOME_MAE("emitirNotaNomeMae");

    private static final String PREFIXO = "colaboradorPessoa";
    private String label;

    ColunasColaboradorPessoa(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return PREFIXO + "." + label;
    }
}
