package negocio.facade.jdbc.financeiro;

/**
 * Created by <PERSON> on 25/05/2015.
 */
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import java.util.Calendar;

import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.CartaoCreditoVO;
import negocio.comuns.financeiro.HistoricoCartaoVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.HistoricoCartaoInterfaceFacade;


public class HistoricoCartao extends SuperEntidade implements HistoricoCartaoInterfaceFacade {

    public HistoricoCartao() throws Exception {
        super();
    }
    public HistoricoCartao(Connection con) throws Exception {
        super(con);
    }

    public void incluir(HistoricoCartaoVO obj) throws Exception {
        incluir(obj, true);
    }

    @Override
    public void incluir(HistoricoCartaoVO obj, boolean controlarTransacao) throws Exception {
        try {
            if (controlarTransacao) {
                con.setAutoCommit(false);
            }
                finalizarHistorico(obj, obj.getDataInicio());
                StringBuilder sql = new StringBuilder();
                sql.append("INSERT INTO historicocartao(cartao, lote, datainicio, movconta) ");
                sql.append("VALUES ( ?, ?, ?, ?);");

                PreparedStatement sqlInserir = con.prepareStatement(sql.toString());
                int i = 1;
                sqlInserir.setInt(i++, obj.getCartao().getCodigo());
                resolveFKNull(sqlInserir, i++, obj.getLote().getCodigo());
                sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(obj.getDataInicio()));
                resolveFKNull(sqlInserir, i++, obj.getMovConta().getCodigo());


                sqlInserir.execute();
                obj.setCodigo(obterValorChavePrimariaCodigo());
                obj.setNovoObj(false);

            if (controlarTransacao) {
                con.commit();
            }
        } catch (Exception e) {
            obj.setNovoObj(true);
            if (controlarTransacao) {
                con.rollback();
            }
            throw e;
        } finally {
            if (controlarTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

   @Override
    public List<HistoricoCartaoVO> consultarPorCartaoComposicao(String codigos) throws Exception{
         StringBuilder sql = new StringBuilder();
         sql.append("SELECT hc.*,l.descricao as desclote, c.*, mc.tipooperacao, p.nome,  mc.codigo as codigomovconta  FROM historicocartao hc\n");
         sql.append(" LEFT JOIN movconta mc ON mc.codigo = hc.movconta \n");
         sql.append(" LEFT JOIN conta c ON c.codigo = mc.conta \n");
         sql.append(" LEFT JOIN lote l ON hc.lote = l.codigo \n");
         sql.append(" LEFT JOIN pessoa p ON p.codigo = mc.pessoa \n");
         sql.append(" WHERE cartao in ( "+codigos+") ORDER BY hc.datafim DESC ");
        ResultSet dados = con.prepareStatement(sql.toString()).executeQuery();

        return montarDadosConsulta(dados, Uteis.NIVELMONTARDADOS_TELACONSULTA);
    }


    public  List<HistoricoCartaoVO> montarDadosConsulta(ResultSet dados, int nivelMontarDados) throws Exception{
        List<HistoricoCartaoVO> vetResultado = new ArrayList<HistoricoCartaoVO>();
        while (dados.next()) {
            HistoricoCartaoVO obj = new HistoricoCartaoVO();
            obj = montarDados(dados, nivelMontarDados);
            vetResultado.add(obj);
        }

        return vetResultado;
    }

    public void finalizarHistorico(HistoricoCartaoVO histCartao, Date data) throws Exception{
        ResultSet dados = con.prepareStatement("SELECT * FROM historicocartao WHERE cartao in ( "+histCartao.getCartao().getObterTodosCartoesComposicao()
                +") AND datafim is null ORDER BY datainicio DESC limit 1").executeQuery();
        if(dados.next()){
            HistoricoCartaoVO hist = montarDados(dados, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PreparedStatement stm = con.prepareStatement("UPDATE historicocartao SET datafim = ? WHERE codigo = ? ");
            stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Uteis.somarCampoData(data, Calendar.SECOND, -1)));
            stm.setInt(2, hist.getCodigo());
            stm.execute();
        }
    }



    public static HistoricoCartaoVO montarDados(ResultSet dados, int nivelMontarDados) throws Exception{
        HistoricoCartaoVO historico = new HistoricoCartaoVO();
        historico.getCartao().setCodigo(dados.getInt("cartao"));
        historico.getLote().setCodigo(dados.getInt("lote"));

        historico.setDataInicio(dados.getTimestamp("datainicio"));
        historico.setDataFim(dados.getTimestamp("datafim"));
        historico.setCodigo(dados.getInt("codigo"));
        historico.setCredito(dados.getBoolean("credito"));
        historico.getMovConta().setCodigo(dados.getInt("movconta"));

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA){
            historico.getMovConta().setContaVO(Conta.montarDados(dados, Uteis.NIVELMONTARDADOS_DADOSBASICOS, ""));
            historico.getLote().setDescricao(UteisValidacao.emptyString(dados.getString("desclote")) ? String.valueOf(dados.getInt("lote")) : dados.getString("desclote"));
            historico.getMovConta().setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(dados.getInt("tipooperacao")));
            historico.getMovConta().getPessoaVO().setNome(dados.getString("nome"));
            historico.getMovConta().setCodigo(UteisValidacao.emptyNumber(dados.getInt("codigomovconta")) ? 0 : dados.getInt("codigomovconta"));
        }
        return historico;
    }

    public void inicializarHistorico(CartaoCreditoVO cartao, int movConta, LoteVO lote) throws Exception{
        HistoricoCartaoVO histCartao = new HistoricoCartaoVO();
        histCartao.setCartao(cartao);
        histCartao.setDataInicio(Calendario.hoje());
        histCartao.getMovConta().setCodigo(movConta);
        histCartao.setLote(lote);


        incluir(histCartao);
    }

    public void getContaLoteCartao(CartaoCreditoTO cartao, boolean datafimisnull) throws Exception{
        StringBuilder sql = sqlLote(cartao.getObterTodosCartoesComposicao(), datafimisnull);

        ResultSet resultSet = criarConsulta(sql.toString(), con);
        if(resultSet.next()){
            cartao.setContaContido(resultSet.getString("contac"));
            cartao.setNumeroLote(resultSet.getInt("nrlote"));
            cartao.setCodigoContaContido(resultSet.getInt("codigoconta"));
        }
    }

    public void getCodigoLoteCartao(CartaoCreditoVO cartao, boolean datafimisnull) throws Exception{
        StringBuilder sql = sqlLote(cartao.getObterTodosCartoesComposicao(), datafimisnull);

        ResultSet resultSet = criarConsulta(sql.toString(), con);
        Lote loteDAO = new Lote(con);
        if(resultSet.next()){
            int loteAvulso = loteDAO.consultarLoteAvulso(cartao.getCodigo());
            if (resultSet.getDate("datafim") == null || loteAvulso != 0) {
                cartao.getLote().setCodigo(resultSet.getInt("nrlote"));
            }
        }
    }
    /**
     * Responsável por
     * <AUTHOR>
     * 25/05/2015
     */
    private StringBuilder sqlLote(String codigos, boolean datafimisnull) {
        StringBuilder sql = new StringBuilder("SELECT c.codigo as codigoconta, c.descricao as contac, lote.codigo as nrlote, coalesce(lote.pagamovconta,mcLote.codigo) as pagamovconta, hc.datafim ")
                .append(" FROM historicocartao hc ")
                .append(" INNER JOIN movconta mc ON mc.codigo = hc.movconta ")
                .append(" left JOIN movconta mcLote ON mcLote.codigo = hc.lote ")
                .append(" INNER JOIN lote ON hc.lote = lote.codigo ")
                .append(" INNER JOIN conta c ON c.codigo = mc.conta ")
                .append(" WHERE hc.cartao in ("+codigos+")")
                .append(" AND not lote.avulso ");
        if(datafimisnull){
            sql.append(" AND hc.datafim is null ");
        }
        sql.append("ORDER BY hc.datainicio DESC limit 1");
        return sql;
    }

    public boolean cartaoJaSaiuLote(String codigosComposicao, Integer codigoLote) throws Exception{
        ResultSet set = criarConsulta("SELECT codigo FROM historicocartao histc\n" +
                "WHERE cartao in ("+codigosComposicao
                + ") AND lote = "+codigoLote+" AND datafim is not null", con);

        return set.next();
    }

}
