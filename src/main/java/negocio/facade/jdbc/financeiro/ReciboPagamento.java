/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.evento.EventoInteresse;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao.NegociacaoEvento;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.ecf.cupomfiscal.comuns.dao.CupomFiscal;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import controle.arquitetura.SuperControle;
import controle.financeiro.ReciboControle;
import controle.integracao.LogEnvio;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.IdentificadorInternacionalEnum;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.nfe.enumerador.StatusNotaEnum;
import negocio.comuns.notaFiscal.NotaFiscalVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoModalidade;
import negocio.facade.jdbc.nfe.LoteNFe;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;
import negocio.interfaces.financeiro.MovPagamentoInterfaceFacade;
import net.sf.jasperreports.engine.JRParameter;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.controle.arquitetura.SuperControleRelatorio;
import relatorio.controle.financeiro.CaixaPorOperadorRelControleRel;
import relatorio.negocio.comuns.financeiro.AuxiliarReciboPagamentoRelTO;
import relatorio.negocio.comuns.financeiro.ReciboPagamentoRelTO;
import relatorio.negocio.jdbc.financeiro.CaixaPorOperadorRel;
import servicos.impl.devolucaocheque.DevolucaoChequeServiceImpl;
import servicos.integracao.TreinoWSConsumer;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static controle.financeiro.ReciboControle.getCaminhoSubRelatorio;
import static controle.financeiro.ReciboControle.getDesignIReportRelatorio;

/**
 *
 * <AUTHOR>
 */
public class ReciboPagamento extends SuperEntidade {

    public static String SQL_TEM_CREDITO_PERSONAL = " select mp.codigo from movproduto mp \n"
            + " inner join produto p on p.codigo = mp.produto and p.tipoproduto = '" + TipoProduto.CREDITO_PERSONAL.getCodigo() + "'\n"
            + " inner join movprodutoparcela mpp on mpp.movproduto = mp.codigo and mpp.recibopagamento = ";

    public ReciboPagamento() throws Exception {
        super();
        setIdEntidade("MovPagamento");
    }

    public ReciboPagamento(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("MovPagamento");
    }

    /*
     * Operação responsável por retornar um novo objeto da classe
     * <code>ReciboPagamentoVO</code>.
     */
    public ReciboPagamentoVO novo() throws Exception {
        incluir(getIdEntidade());
        ReciboPagamentoVO obj = new ReciboPagamentoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe
     * <code>ReciboPagamentoVO</code>. Primeiramente valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>incluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ReciboPagamentoVO</code> que será
     * gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void incluir(ReciboPagamentoVO obj) throws Exception {
        ReciboPagamentoVO.validarDados(obj);
        //incluir(getIdEntidade());

        obj.setCodigo(existe(obj));
        if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
            alterar(obj);
        } else {
            String sql = "INSERT INTO ReciboPagamento(pessoaPagador, responsavelLancamento, contrato, data, valorTotal, nomePessoaPagador, empresa) VALUES (?, ?, ?, ?, ?, ?, ?) RETURNING codigo";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                preecherParametrosBasicos(sqlInserir, obj);
                try (ResultSet rs = sqlInserir.executeQuery()) {
                    if (rs.next()) {
                        obj.setCodigo(rs.getInt("codigo"));
                        obj.setNovoObj(false);
                    }
                }
            }
        }
    }

    private void preecherParametrosBasicos(PreparedStatement sqlInserir, ReciboPagamentoVO obj) throws Exception {
        if (obj.getPessoaPagador().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getPessoaPagador().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getResponsavelLancamento().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getResponsavelLancamento().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        if (obj.getContrato().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getContrato().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        sqlInserir.setTimestamp(4, Uteis.getDataJDBCTimestamp(obj.getData()));
        sqlInserir.setDouble(5, obj.getValorTotal());
        sqlInserir.setString(6, obj.getNomePessoaPagador());
        if (obj.getEmpresa().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getEmpresa().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
    }

    private Integer existe(ReciboPagamentoVO obj) throws Exception {
        String sql = "Select * from ReciboPagamento where ";
        PreparedStatement sqlConsultar;
        if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
            sql.concat(" codigo = ? ");
            sqlConsultar = con.prepareStatement(sql);
            sqlConsultar.setInt(1, obj.getCodigo());
        } else {
            sql += " pessoapagador = ? "
                    + " AND nomepessoapagador = ? "
                    + " AND responsavellancamento = ? "
                    + " AND contrato = ? "
                    + " AND \"data\"::date = ? "
                    + " AND empresa = ? "
                    + " AND nfseemitida is " + obj.getNfseEmitida()
                    + " AND valortotal = ? ";

            sqlConsultar = con.prepareStatement(sql);
            sqlConsultar.setInt(1, obj.getPessoaPagador().getCodigo());
            sqlConsultar.setString(2, obj.getNomePessoaPagador());
            sqlConsultar.setInt(3, obj.getResponsavelLancamento().getCodigo());
            sqlConsultar.setInt(4, obj.getContrato().getCodigo());
            sqlConsultar.setDate(5, Uteis.getDataJDBC(obj.getData()));
            sqlConsultar.setInt(6, obj.getEmpresa().getCodigo());
            sqlConsultar.setDouble(7, obj.getValorTotal());
        }
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("codigo");
        }
        return 0;
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe
     * <code>ReciboPagamentoVO</code>. Sempre utiliza a chave primária da classe
     * como atributo para localização do registro a ser alterado. Primeiramente
     * valida os dados (
     * <code>validarDados</code>) do objeto. Verifica a conexão com o banco de
     * dados e a permissão do usuário para realizar esta operacão na entidade.
     * Isto, através da operação
     * <code>alterar</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ReciboPagamentoVO</code> que será
     * alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso
     * ou validação de dados.
     */
    public void alterar(ReciboPagamentoVO obj) throws Exception {
        ReciboPagamentoVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE ReciboPagamento set pessoaPagador = ?, responsavelLancamento = ?, contrato = ?, data = ?, "
                + "valorTotal = ?, nomePessoaPagador=?, empresa=? WHERE codigo = ?";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            preecherParametrosBasicos(sqlAlterar,obj);
            sqlAlterar.setInt(8, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    public List consularReciboPagamParcelaPorCodigoVariosContratos(List codigos, int nivelMontarDados) throws Exception {
        StringBuilder cod = new StringBuilder("");
        for (int i=0; i<codigos.size(); i++){
            cod.append(codigos.get(i));
             if (i<(codigos.size()-1)){
                cod.append(",");
             }
        }

        String sqlStr = "select distinct on (reciboPagamento.codigo) reciboPagamento.* from reciboPagamento "
                + "inner join pagamentomovparcela on pagamentomovparcela.reciboPagamento = reciboPagamento.codigo "
                + "and pagamentomovparcela.movparcela in (select movparcela.codigo from movparcela where contrato in (" + cod.toString()+ "))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consularReciboComCodigoContrato(Integer codigo) throws Exception {
        List codigos = new ArrayList(0);
        String sqlStr = "select distinct(reciboPagamento.contrato) from reciboPagamento "
                + "inner join pagamentomovparcela on pagamentomovparcela.reciboPagamento = reciboPagamento.codigo "
                + "and pagamentomovparcela.movparcela in (select movparcela from movprodutoparcela where movproduto in(select codigo from movproduto where contrato = " + codigo + "))";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                while (tabelaResultado.next()) {
                    if (tabelaResultado.getInt("contrato") != codigo) {
                        codigos.add(tabelaResultado.getInt("contrato"));
                    }
                }
            }
        }
        codigos.add(codigo);
        return codigos;
    }



    public List consularReciboPagamParcelaPorCodigoContrato(Integer codigo, int nivelMontarDados) throws Exception {
        String sqlStr = "select distinct on (reciboPagamento.codigo) reciboPagamento.* from reciboPagamento "
                + "inner join pagamentomovparcela on pagamentomovparcela.reciboPagamento = reciboPagamento.codigo "
                + "and pagamentomovparcela.movparcela in (select movparcela.codigo from movparcela where contrato = " + codigo + ")";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consularReciboPagamParcelaPorCodigoMovProduto(Integer codigo, int nivelMontarDados) throws Exception {
        String sqlStr = "select distinct on (reciboPagamento.codigo) reciboPagamento.* from reciboPagamento "
                + "inner join pagamentomovparcela on pagamentomovparcela.reciboPagamento = reciboPagamento.codigo "
                + "inner join movprodutoparcela on movprodutoparcela.movparcela = pagamentomovparcela.movparcela and movprodutoparcela.movproduto = " + codigo + "";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public void estornarReciboPagamento(EstornoReciboVO obj, MovPagamentoInterfaceFacade movPagamentoFacade,
                                        MovProdutoParcela movProdutoParcelaFacade, CaixaVO caixaAberto, String key) throws Exception {
        estornarReciboPagamento(obj, movPagamentoFacade, movProdutoParcelaFacade, caixaAberto, key, null);
    }

    public void estornarReciboPagamento(EstornoReciboVO obj, MovPagamentoInterfaceFacade movPagamentoFacade, MovProdutoParcela movProdutoParcelaFacade,
                                        CaixaVO caixaAberto, String key, Date dataEstorno) throws Exception {
        estornarReciboPagamento(obj, movPagamentoFacade, movProdutoParcelaFacade, caixaAberto, key, dataEstorno, true);
    }

    public void estornarReciboPagamento(EstornoReciboVO obj, MovPagamentoInterfaceFacade movPagamentoFacade,
                                        MovProdutoParcela movProdutoParcelaFacade, CaixaVO caixaAberto, String key,
                                        Date dataEstorno, boolean controlarTransacao) throws Exception {
        try {
            if (controlarTransacao) {
                con.setAutoCommit(false);
            }
            estornarReciboPagamentoSemCommit(obj, movPagamentoFacade, movProdutoParcelaFacade, caixaAberto, key, dataEstorno);
            if (controlarTransacao) {
                con.commit();
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (controlarTransacao) {
                con.rollback();
            }
            throw e;
        } finally {
            if (controlarTransacao) {
                con.setAutoCommit(true);
            }
        }
    }

    public void estornarReciboPagamentoSemCommit(EstornoReciboVO obj,
                                                 MovPagamentoInterfaceFacade movPagamentoFacade,
                                                 MovProdutoParcela movProdutoParcelaFacade, CaixaVO caixaAberto, String key,
                                                 Date dataEstorno) throws Exception {

        try {
            if(Boolean.valueOf(PropsService.getPropertyValue(PropsService.integraProtheus))){
               new ReciboPagamento(con).inserirIntegracaoProtheus(obj.getReciboPagamentoVO().getCodigo(), "E", dataEstorno);
            }
        }catch (Exception e){
            System.out.println("Erro ao integrar com protheus");
        }

        try {
            ConciliadoraEstorno conciliadoraEstornoDAO = new ConciliadoraEstorno(con);
            conciliadoraEstornoDAO.estornarConciliadora(obj.getReciboPagamentoVO().getCodigo());
            conciliadoraEstornoDAO = null;
        } catch (Exception e) {
            System.out.println("Erro ao estornar conciliadora | estornarReciboPagamentoSemCommit");
        }

        estornarBoletosOnline(obj);
        estornarNotasFiscais(obj, null);
        atualizarConciliacaoEstornoPagamentos(obj);
        tratarParcelasChequesDevolvidos(obj);
        boolean pagaCredito;
        try (ResultSet qcp = con.prepareStatement(SQL_TEM_CREDITO_PERSONAL + obj.getReciboPagamentoVO().getCodigo()).executeQuery()) {
            pagaCredito = qcp.next();
        }

        ConfiguracaoFinanceiroVO cFinan = new ConfiguracaoFinanceiro(con).consultar();
        if (!cFinan.getUsarMovimentacaoContas()) {
            Lote loteDao = new Lote(con);
            for (MovPagamentoVO mp : obj.getListaMovPagamento()) {
                loteDao.atualizarValoresLotePorMovPagamentoCartao(mp.getCodigo());
                loteDao.atualizarValoresLotePorMovPagamentoCheque(mp.getCodigo());
            }
        }
        List<EstornoReciboVO> estornorecibos = new ArrayList<EstornoReciboVO>();
        estornorecibos.add(obj);
        ReciboDevolucao recDev = new ReciboDevolucao(con);
        recDev.estornarRecibosDevolucao(cFinan.getUsarMovimentacaoContas(), estornorecibos);
        List<LogVO> listaLog = obj.criarLogEstornoRecibo(movPagamentoFacade, movProdutoParcelaFacade, con);
        try {
            if(dataEstorno != null){
                for(LogVO log : listaLog){
                    log.setDataAlteracao(dataEstorno);
                }
            }

            SuperControle.registrarLogObjetoVO(listaLog, obj.getReciboPagamentoVO().getPessoaPagador().getCodigo(), con);
        } catch (Exception e) {
            SuperControle.registrarLogErroObjetoVO("ESTORNORECIBO", obj.getReciboPagamentoVO().getPessoaPagador().getCodigo(), "ERRO AO GERAR LOG DE ESTORNO RECIBO",
                    obj.getUsuarioVO().getNome(),  obj.getUsuarioVO().getUserOamd(), con);
            e.printStackTrace();
        }
        obj.alterarSituacaoMovParcela(con);
        Contrato contratoDao = new Contrato(con);
        contratoDao.lancarSaidaEstornoRecibo(obj, null, caixaAberto);
        obj.validarSeExisteMovimentoContaCorrente(obj, con);
        obj.excluirProdutosPagamentoDebito(con);
        obj.excluirMovPagamento(con);
        obj.excluirReferenciaMovPagamentoExtratoDiarioItem(con);
        removerCupom(obj);
        NegociacaoEvento negEvDao = new NegociacaoEvento(con);
        negEvDao.excluirReciboSemCommit(obj.getReciboPagamentoVO());
        excluirMultaJurosParcela(obj);
        tentarEstornoCreditoPersonal(obj.getReciboPagamentoVO(), key, pagaCredito);
        exluirPix(obj);
        excluir(obj.getReciboPagamentoVO());
    }

    public void exluirPix(EstornoReciboVO obj) throws Exception {
        Pix pixDAO = new Pix(con);
        pixDAO.excluirPorReciboPagamento(obj.getReciboPagamentoVO(), obj.getResponsavelEstornoRecivo());
        pixDAO = null;
    }

    private void estornarBoletosOnline(EstornoReciboVO obj) throws Exception {
        Boleto boletoDAO;
        try {
            if (obj == null) {
                return;
            }
            if (!UteisValidacao.emptyNumber(obj.getReciboPagamentoVO().getCodigo())) {
                boletoDAO = new Boleto(con);
                boletoDAO.estornarReciboPagamento(obj.getReciboPagamentoVO().getCodigo(), obj.getResponsavelEstornoRecivo(), "EstornoReciboVO");
            }
        } finally {
            boletoDAO = null;
        }
    }

    public void excluirMultaJurosParcela(EstornoReciboVO obj) throws Exception {
        MovParcela movParcelaDao;
        try {
            movParcelaDao = new MovParcela(con);

            for (MovParcelaVO parcelaVO : obj.getListaMovParcela()) {
                movParcelaDao.excluirMultaJurosParcela(parcelaVO);
            }
        } finally {
            movParcelaDao = null;
        }
    }

    private boolean validarNFSeEmitida(EstornoReciboVO estornoReciboVO, NFSeEmitidaVO notaEmitida, boolean podeEstornar, List<NFSeEmitidaVO> notasEmitidas) throws Exception {
        if (estornoReciboVO.isExcluirNFSe()){
            if (notaEmitida != null && notaEmitida.getIdRps() != null) {
                notasEmitidas.add(notaEmitida);
                return true;
            }
        }else{
            if (notaEmitida == null) {
                return podeEstornar;
            }

            if (notaEmitida.isEnotas()) {
                NotaFiscal notaFiscalDAO = new NotaFiscal(con);
                NotaFiscalVO notaFiscalVO = notaFiscalDAO.consultarPorNFSeEmitida(notaEmitida.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                notaFiscalDAO = null;

                podeEstornar = notaFiscalVO.getStatusNotaEnum().isPodeEstornar();
                notasEmitidas.add(notaEmitida);
                if (!podeEstornar) {
                    estornoReciboVO.setMostrarMsgExcluirNFse(true);
                }
            } else {
                if (notaEmitida.getIdRps() != null) {
                    LoteNFe loteNFeDAO = new LoteNFe(con);
                    StatusNotaEnum statusNotaEnum = loteNFeDAO.retornarStatus(notaEmitida.getIdRps());
                    loteNFeDAO = null;

                    podeEstornar = statusNotaEnum.isPodeEstornar();
                    notasEmitidas.add(notaEmitida);
                    if (!podeEstornar) {
                        estornoReciboVO.setMostrarMsgExcluirNFse(true);
                    }
                } else {
                    podeEstornar = false;
                }
            }
        }
        return podeEstornar;
    }

    public void estornarNotasFiscais(EstornoReciboVO estornoReciboVO, ContratoVO contratoVO) throws Exception {
        boolean podeEstornar = true;
        NFSeEmitida nfseEmitida = new NFSeEmitida(con);
        MovPagamento pagamentoDao = new MovPagamento(con);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
//        List movparcelas = .getMovParcela().consultarPorCodigoRecibo(reciboPagamentoVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        List<MovPagamentoVO> pagamentosValidar = null;
        for (MovPagamentoVO pagamentoVO : estornoReciboVO.getListaMovPagamento()) {
            pagamentosValidar = new ArrayList<MovPagamentoVO>();
            pagamentosValidar.add(pagamentoVO);
            if(pagamentoVO.getValor() == 0.0 ){
                pagamentosValidar.addAll(pagamentoDao.consultarCreditosDependentes(pagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            }
            for (MovPagamentoVO pagamento : pagamentosValidar) {
                NFSeEmitidaVO notaEmitida = nfseEmitida.consultaPorPagamento(pagamento.getCodigo());
                podeEstornar = validarNFSeEmitida(estornoReciboVO, notaEmitida, podeEstornar, notasEmitidas);

                if (pagamento.getReciboPagamento().getCodigo() != 0) {
                    List<NFSeEmitidaVO> notasEmitidasRecibo = nfseEmitida.consultarListaPorRecibo(estornoReciboVO.getReciboPagamentoVO().getCodigo());
                    for(NFSeEmitidaVO notaEmitidaRecibo :  notasEmitidasRecibo){
                        podeEstornar = validarNFSeEmitida(estornoReciboVO, notaEmitidaRecibo, podeEstornar, notasEmitidas);
                    }
                }
                for (CartaoCreditoVO cartaoCreditoVO : pagamento.getCartaoCreditoVOs()) {
                    NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                    List<NFSeEmitidaVO> notasEmitidasCartao = nfSeEmitidaDAO.consultaListaPorCartao(cartaoCreditoVO.getObterTodosCartoesComposicao());
                    nfSeEmitidaDAO = null;
                    for(NFSeEmitidaVO notaEmitidaCartao:  notasEmitidasCartao){
                        podeEstornar = validarNFSeEmitida(estornoReciboVO,notaEmitidaCartao, podeEstornar, notasEmitidas);
                    }
                }

                for (ChequeVO chequeVO : pagamento.getChequeVOs()) {
                    NFSeEmitida nfSeEmitidaDAO = new NFSeEmitida(con);
                    List<NFSeEmitidaVO> notasEmitidasCheque = nfSeEmitidaDAO.consultaListaPorCheque(chequeVO.getObterTodosChequesComposicao());
                    nfSeEmitidaDAO = null;
                    for(NFSeEmitidaVO notaEmitidaCheque:  notasEmitidasCheque){
                        podeEstornar = validarNFSeEmitida(estornoReciboVO,notaEmitidaCheque, podeEstornar, notasEmitidas);
                    }
                }
                if(estornoReciboVO.isValidarNFSeProdutosPagos()){
                    String[] infoProdutosPagos = pagamento.getProdutosPagos().split("\\|");
                    for (String produto : infoProdutosPagos) {
                        if (!produto.trim().equals("")) {
                            String[] infoProduto = produto.split(",");
                            notaEmitida = nfseEmitida.consultaPorProduto(Integer.parseInt(infoProduto[0]));
                            podeEstornar = validarNFSeEmitida(estornoReciboVO,notaEmitida, podeEstornar, notasEmitidas);
                        }
                    }
                }

            }
        }

        boolean excluirMesmoAssim = false;
        if (contratoVO != null) {
            excluirMesmoAssim = contratoVO.isExcluirNFSe();
        }


        if (podeEstornar || excluirMesmoAssim) {
            for (NFSeEmitidaVO nfse : notasEmitidas) {
                nfseEmitida.excluir(nfse);
            }
        } else {
            if (contratoVO != null) {
                contratoVO.setMostrarMsgExcluirNFse(true);
            }
            throw new ConsistirException("Já existe nota fiscal emitida para recibo(s) deste contrato, que não são canceladas automaticamente. Deseja estornar mesmo assim?");
        }
    }

    private void removerCupom(EstornoReciboVO obj) throws SQLException,
            Exception {
        CupomFiscal cf = new CupomFiscal();
        cf.setCon(con);
        CupomFiscalVO cupom = cf.consultaPorRecibo(obj.getReciboPagamentoVO().getCodigo());
        //caso o cupom tenha sido impresso, não irá ocorrer o estorno do recibo
        if (cupom != null && cupom.getStatusImpressao() == StatusImpressaoEnum.SUCESSO) {
            throw new Exception("Um cupom fiscal associado a esse recibo foi impresso. Esse estorno não pode ser realizado");
        } else if (cupom != null) {
            cf.remover(cupom);
        }
    }

    public void excluirReciboPagamentoEstornoContrato(Integer codigoContrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ReciboPagamento WHERE ((contrato = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codigoContrato.intValue());
            sqlExcluir.execute();
        }
    }

    public Double cosultarValorPagoContratoAteEstaData(Date dataBase,
            Integer codigoContrato) throws Exception {
        consultar(getIdEntidade());
        String sql = "select sum(valortotal) as valortotal from recibopagamento where (contrato = ?) "
                + "and (data <= ?)";
        try {
            try (PreparedStatement stm = con.prepareStatement(sql)) {
                stm.setInt(1, codigoContrato);
                stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(dataBase));
                try (ResultSet rs = stm.executeQuery()) {
                    if (!rs.next()) {
                        return 0.0;
                    } else {
                        return rs.getDouble("valortotal");
                    }
                }
            }
        } catch (SQLException ex) {
            Logger.getLogger(ReciboPagamento.class.getName()).log(Level.SEVERE,
                    ex.getMessage(), ex);
        }

        return 0.0;

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe
     * <code>ReciboPagamentoVO</code>. Sempre localiza o registro a ser excluído
     * através da chave primária da entidade. Primeiramente verifica a conexão
     * com o banco de dados e a permissão do usuário para realizar esta operacão
     * na entidade. Isto, através da operação
     * <code>excluir</code> da superclasse.
     *
     * @param obj Objeto da classe <code>ReciboPagamentoVO</code> que será
     * removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de
     * acesso.
     */
    public void excluir(ReciboPagamentoVO obj) throws Exception {
        String sql = "DELETE FROM ReciboPagamento WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }

    }

    public void tentarEstornoCreditoPersonal(ReciboPagamentoVO obj, String key, Boolean pagaCreditos) throws Exception {
        boolean usaCredito = false;
        try (PreparedStatement sqlGP = con.prepareStatement("SELECT usargestaocreditospersonal FROM empresa WHERE codigo = " + obj.getEmpresa().getCodigo())) {
            try (ResultSet qgp = sqlGP.executeQuery()) {
                usaCredito = qgp.next() && qgp.getBoolean("usargestaocreditospersonal");
            }
        }
        String msgNaoFoiPossivel = "Não foi possível estornar o recibo pois ele paga Créditos de Personal e a conexão com o TreinoWeb pode estar comprometida. ";
        if (usaCredito && key != null && pagaCreditos) {
            try {
                String result = TreinoWSConsumer.estornarCreditos(key, obj.getCodigo());
                if (!result.equals("ok")) {
                    throw new Exception(result);
                }
            } catch (Exception e) {
                throw new Exception(msgNaoFoiPossivel+e.getMessage());
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma
     * consulta ao banco de dados (
     * <code>ResultSet</code>). Faz uso da operação
     * <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     *
     * @return List Contendo vários objetos da classe
     * <code>PagamentoMovParcelaVO</code> resultantes da consulta.
     */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ReciboPagamentoVO obj = new ReciboPagamentoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de
     * dados (
     * <code>ResultSet</code>) em um objeto da classe
     * <code>PagamentoMovParcelaVO</code>.
     *
     * @return O objeto da classe <code>PagamentoMovParcelaVO</code> com os
     * dados devidamente montados.
     */
    public static ReciboPagamentoVO montarDados(JSONObject dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ReciboPagamentoVO obj = new ReciboPagamentoVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.getContrato().setCodigo(new Integer(dadosSQL.getInt("contrato")));
        obj.getPessoaPagador().setCodigo(new Integer(dadosSQL.getInt("pessoapagador")));
        obj.getResponsavelLancamento().setCodigo(new Integer(dadosSQL.getInt("responsavellancamento")));
        obj.setValorTotal(new Double(dadosSQL.getDouble("valortotal")));
        obj.setData(new Date(dadosSQL.getLong("data")));
        obj.setNomePessoaPagador((dadosSQL.getString("nomepessoapagador")));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL){
            if (!obj.getPessoaPagador().getCodigo().equals(0)) {
                Pessoa pessDao = new Pessoa(con);
                obj.setPessoaPagador(pessDao.consultarPorChavePrimaria(obj.getPessoaPagador().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }

        try {
            obj.setNfseEmitida(dadosSQL.getBoolean("nfseemitida"));
        } catch (Exception e) {
            obj.setNfseEmitida(false);
        }
        try {
            obj.getContrato().setDuracaoContratoApresentar(dadosSQL.getInt("numeromeses") > 0 ? String.valueOf(dadosSQL.getInt("numeromeses")) : "");
        } catch (Exception e) {
            obj.getContrato().setDuracaoContratoApresentar("");
        }

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            empresa = null;
            montarDadosResponsavelLancamento(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
            return obj;
        }
        if (obj.getEmpresa().getCodigo() > 0) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            empresa = null;
        }
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        montarDadosResponsavelLancamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    public static ReciboPagamentoVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ReciboPagamentoVO obj = new ReciboPagamentoVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getContrato().setCodigo(dadosSQL.getInt("contrato"));
        obj.getPessoaPagador().setCodigo(dadosSQL.getInt("pessoaPagador"));
        obj.getResponsavelLancamento().setCodigo(dadosSQL.getInt("responsavelLancamento"));
        obj.setValorTotal(dadosSQL.getDouble("valorTotal"));
        obj.setData((dadosSQL.getTimestamp("data")));
        obj.setNomePessoaPagador((dadosSQL.getString("nomePessoaPagador")));
        obj.getEmpresa().setCodigo(dadosSQL.getInt("empresa"));

        if(nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTAESPECIAL){
            if (!obj.getPessoaPagador().getCodigo().equals(0)) {
                Pessoa pessDao = new Pessoa(con);
                obj.setPessoaPagador(pessDao.consultarPorChavePrimaria(obj.getPessoaPagador().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
        }

        try {
            obj.setNfseEmitida(dadosSQL.getBoolean("nfseemitida"));
        } catch (Exception e) {
            obj.setNfseEmitida(false);
        }
        try {
            obj.getContrato().setDuracaoContratoApresentar(dadosSQL.getString("numeromeses"));
        } catch (Exception e) {
            obj.getContrato().setDuracaoContratoApresentar("");
        }

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS) {
            montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_ESPECIFICO_RECEBIVEIS, con);
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TELACONSULTA) {
            return obj;
        }
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            empresa = null;
            montarDadosResponsavelLancamento(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS, con);
            return obj;
        }
        if (obj.getEmpresa().getCodigo() > 0) {
            Empresa empresa = new Empresa(con);
            obj.setEmpresa(empresa.consultarPorChavePrimaria(obj.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            empresa = null;
        }
        montarDadosContrato(obj, Uteis.NIVELMONTARDADOS_MINIMOS, con);
        montarDadosResponsavelLancamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>PessoaVO</code> relacionado ao objeto
     * <code>MovPagamentoVO</code>. Faz uso da chave primária da classe
     * <code>PessoaVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosPessoaPagadora(ReciboPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getPessoaPagador().getCodigo().intValue() == 0) {
            obj.setPessoaPagador(new PessoaVO());
            return;
        }
        Pessoa pessDao = new Pessoa(con);
        obj.setPessoaPagador(pessDao.consultarPorChavePrimaria(obj.getPessoaPagador().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>FormaPagamentoVO</code> relacionado ao objeto
     * <code>MovPagamentoVO</code>. Faz uso da chave primária da classe
     * <code>FormaPagamentoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosResponsavelLancamento(ReciboPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getResponsavelLancamento().getCodigo().intValue() == 0) {
            obj.setResponsavelLancamento(new UsuarioVO());
            return;
        }
        Usuario uDao = new Usuario(con); 
        obj.setResponsavelLancamento(uDao.consultarPorChavePrimaria(obj.getResponsavelLancamento().getCodigo(), Uteis.NIVELMONTARDADOS_LOGIN));
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe
     * <code>FormaPagamentoVO</code> relacionado ao objeto
     * <code>MovPagamentoVO</code>. Faz uso da chave primária da classe
     * <code>FormaPagamentoVO</code> para realizar a consulta.
     *
     * @param obj Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosContrato(ReciboPagamentoVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getContrato().getCodigo().intValue() == 0) {
            obj.setContrato(new ContratoVO());
            return;
        }
        Contrato contratoDao = new Contrato(con);
        obj.setContrato(contratoDao.consultarPorChavePrimaria(obj.getContrato().getCodigo(), nivelMontarDados));
    }

    /**
     * Operação responsável por localizar um objeto da classe
     * <code>PagamentoMovParcelaVO</code> através de sua chave primária.
     *
     * @exception Exception Caso haja problemas de conexão ou localização do
     * objeto procurado.
     */
    public ReciboPagamentoVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ReciboPagamento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigoPrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Recibo Pagamento).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public ReciboPagamentoVO consultarPorCodigo(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ReciboPagamento WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo.intValue());
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return (montarDados(tabelaResultado, nivelMontarDados, con));
                }
            }
        }
        return null;
    }

    public ReciboPagamentoVO consultarPorCodigoMovPagamento(Integer codigoMovPagamento, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM ReciboPagamento \n");
        sql.append("where codigo = (select reciboPagamento from movPagamento where codigo = ").append(codigoMovPagamento).append(")");
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql.toString())) {
                if (rs.next()) {
                    return (montarDados(rs, nivelMontarDados, con));
                }
            }
        }
        return null;
    }



    public List existeReciboPagamentoParaEsteContrato(Integer codigoContrato, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ReciboPagamento WHERE contrato = " + codigoContrato;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consultarPorNomePessoa(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ReciboPagamento WHERE ( nomepessoapagador ) like ('" + valorConsulta.toUpperCase() + "%') ORDER BY nomepessoapagador";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ReciboPagamento WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Metodo responsavel por retornar uma consulta paginada em banco, de acordo
     * com os filtros passados
     *
     * Autor: Carla Criado em 13/01/2011
     */
    @SuppressWarnings("unchecked")
    public List consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao confPaginacao) throws Exception {
        // consultar(getIdEntidade(), filtro.isControlarAcesso());

        //1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();

        //sql principal
        StringBuffer sqlSelect = new StringBuffer("SELECT distinct recibopagamento.* FROM recibopagamento");

        //sql inner joins
        associacoes(filtro, sqlSelect);

        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);

        //a utilização dos filtros deve ser feita utilizando confPaginacao.getSqlFiltro()
        filtros(filtro, confPaginacao.getSqlFiltro());
        if (!"".equals(confPaginacao.getSqlFiltro().toString())) {
            sqlSelect.append(" WHERE ");
            sqlSelect.append(confPaginacao.getSqlFiltro().toString());
        }

        //concatena ordenações
        ordenacao(filtro, sqlSelect);

//		System.out.println("sql: " + sqlSelect);

        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sqlSelect);

        //seta os valores dos filtros
        filtrosValoresStatement(filtro, confPaginacao.getStm());

        //4 - REALIZA A CONSULTA COM PAGINACAO
        try (ResultSet tabelaResultado = confPaginacao.consultaPaginada()) {
            return (montarDadosConsulta(tabelaResultado, filtro.getNivelMontarDados(), con));
        }
    }

    public List consultarPorListaDeCodigos(String listaCodigosSeparadosPorVirgula, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ReciboPagamento WHERE codigo in (" + listaCodigosSeparadosPorVirgula + ") ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    /**
     * Atribuindo valores para os filtros no Statement para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void filtrosValoresStatement(ReciboPagamentoFiltroVO filtro, PreparedStatementPersonalizado stm) throws SQLException, Exception {
        int i = 0;
        //INICIO - SETANDO PARAMETROS PARA "filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro)"
        if (filtro != null && filtro.getReciboPagamentoVO() != null && filtro.getReciboPagamentoVO().getCodigo() > 0) {
            stm.setInt(i++, filtro.getReciboPagamentoVO().getCodigo());
        }
        if (filtro != null && filtro.getPessoaVO() != null) {
            if (filtro.getPessoaVO().getNome() != null && !"sem valor".equals(filtro.getPessoaVO().getNome())) {
                stm.setString(i++, filtro.getPessoaVO().getNome() + "%");
            }
        }
        if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
            stm.setInt(i++, filtro.getEmpresaVO().getCodigo());
        }
        //FIM - SETANDO PARAMETROS PARA "filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro)"
    }

    /**
     * Definindo o tipo de ordenacao para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void ordenacao(ReciboPagamentoFiltroVO filtro, StringBuffer sql) {
        if (filtro != null) {
            if (filtro.getPessoaVO() != null) {
                sql.append(" ORDER BY recibopagamento.nomepessoapagador");
            } else {
                sql.append(" ORDER BY recibopagamento.codigo desc");
            }
        }
    }

    /**
     * Definindo as condicoes da clausa WHERE para o metodo
     * consultarPaginado(ClienteFiltroVO filtro, ConfPaginacao confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void filtros(ReciboPagamentoFiltroVO filtro, StringBuffer sqlFiltro) {
        if (filtro != null && filtro.getReciboPagamentoVO() != null) {
            if (filtro.getReciboPagamentoVO().getCodigo() > 0) {
                sqlFiltro.append(" recibopagamento.codigo  = ?");
            }
        } else if (filtro != null && filtro.getPessoaVO() != null && !filtro.getPessoaVO().getNome().trim().isEmpty()) {
            sqlFiltro.append(" recibopagamento.nomepessoapagador ilike ?");
        }
        if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
            if (!"".equals(sqlFiltro.toString())) {
                sqlFiltro.append(" AND empresa.codigo = ?");
            } else {
                sqlFiltro.append(" empresa.codigo  = ?");
            }
        }

    }

    /**
     * Definindo os Inner Join e Left Join para o metodo
     * consultarPaginado(ReciboPagamentoFiltroVO filtro, ConfPaginacao
     * confPaginacao)
     *
     * Autora: Carla Criado em 14/01/2011
     */
    private void associacoes(ReciboPagamentoFiltroVO filtro, StringBuffer sql) {
        if (filtro != null && filtro.getEmpresaVO() != null && filtro.getEmpresaVO().getCodigo() > 0) {
            sql.append(" inner join movprodutoparcela mpp on recibopagamento.codigo = mpp.recibopagamento ");
            sql.append(" inner join movproduto mp on mpp.movproduto = mp.codigo ");
            sql.append(" inner join empresa on mp.empresa = empresa.codigo ");
        }
    }

    /**
     * Método responsável por inserir em banco a relação entre contrato de
     * evento e recibo de pagamento.
     *
     * @param codigoContratoEvento
     * <AUTHOR>
     * @throws Exception
     */
    public void salvarPagamentoContratoEvento(Integer codigoContratoEvento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO negociacaoeventocontratorecibo(contrato, recibo)");
        sql.append(" VALUES (?, ?)");
        Integer recibo = obterValorChavePrimariaCodigo();
        Declaracao dc = new Declaracao(sql.toString(), con);
        int i = 0;
        dc.setInt(++i, codigoContratoEvento);
        dc.setInt(++i, recibo);
        dc.execute();

    }

    /**
     * <AUTHOR> 29/09/2011
     */
    public ReciboPagamentoVO consultarPorParcela(Integer movParcela, Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT rp.* FROM recibopagamento rp \n");
        sql.append("INNER JOIN pagamentomovparcela pmp ON rp.codigo = pmp.recibopagamento \n");
        sql.append("AND pmp.movparcela = " + movParcela);
        ReciboPagamentoVO recibo;
        try (ResultSet rs = ReciboPagamento.criarConsulta(sql.toString(), con)) {
            recibo = new ReciboPagamentoVO();
            if (rs.next()) {
                recibo = montarDados(rs, nivelMontarDados, con);
            }
        }
        return recibo;
    }

    public void gravarNFSEEmitida(int codigo) throws Exception {
        executarConsulta("UPDATE recibopagamento SET nfseemitida = true WHERE codigo = " + codigo, con);
    }

    public String consultarJSON(Integer empresa, String sEcho, Integer offset, Integer limit, String clausulaLike, Integer colOrdenar, String dirOrdenar) throws Exception {
        StringBuilder sqlCount = new StringBuilder("SELECT count(codigo) FROM recibopagamento rp");
        if (empresa != 0) {
            sqlCount.append("  WHERE rp.empresa = ").append(empresa).append("\n");
        }

        StringBuilder sql = new StringBuilder("SELECT rp.codigo, rp.nomepessoapagador AS cliente, rp.data, \n"
                + "usr.username AS responsavel, emp.nome AS empresa, rp.valortotal\n"
                + "FROM recibopagamento rp\n"
                + "  LEFT JOIN usuario usr ON rp.responsavellancamento = usr.codigo\n"
                + "  LEFT JOIN empresa emp ON rp.empresa = emp.codigo\n"
                + "WHERE 1 = 1\n");
        if (empresa != 0) {
            sql.append("  AND rp.empresa = ").append(empresa).append("\n");
        }

        if (!UteisValidacao.emptyString(clausulaLike)) {
            sql.append(" AND (");
            sql.append("lower(rp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(rp.nomepessoapagador) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(rp.data::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(usr.username) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(emp.nome) ~ '").append(clausulaLike).append("' OR\n");
            sql.append("lower(rp.valortotal::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sql.append(")");
        }
        sql.append("  ORDER BY ").append(colOrdenar + 1).append(" ").append(dirOrdenar).append("\n");
        if (limit > 0) {
            sql.append(" limit ").append(limit).append("\n");
        }
        sql.append(" offset ").append(offset).append("\n");

        StringBuilder sqlContarFiltrados = new StringBuilder("SELECT count(rp.codigo)\n");
        sqlContarFiltrados.append("FROM recibopagamento rp\n");
        sqlContarFiltrados.append("  LEFT JOIN usuario usr ON rp.responsavellancamento = usr.codigo\n");
        sqlContarFiltrados.append("  LEFT JOIN empresa emp ON rp.empresa = emp.codigo\n");
        sqlContarFiltrados.append(" WHERE 1 = 1\n");
        if (empresa != 0) {
            sqlContarFiltrados.append("  AND rp.empresa = ").append(empresa).append("\n");
        }
        if (!UteisValidacao.emptyString(clausulaLike)) {
            sqlContarFiltrados.append(" AND (");
            sqlContarFiltrados.append("lower(rp.codigo::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(rp.nomepessoapagador) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(rp.data::VARCHAR) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(usr.username) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(emp.nome) ~ '").append(clausulaLike).append("' OR\n");
            sqlContarFiltrados.append("lower(rp.valortotal::VARCHAR) ~ '").append(clausulaLike).append("'\n");
            sqlContarFiltrados.append(")");
        }

        StringBuilder json;
        boolean dados;
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {

                json = new StringBuilder();
                json.append("{");
                json.append("\"iTotalRecords\":\"").append(contar(sqlCount.toString(), getCon())).append("\",");
                json.append("\"iTotalDisplayRecords\":\"").append(contar(sqlContarFiltrados.toString(), getCon())).append("\",");
                json.append("\"sEcho\":\"").append(sEcho).append("\",");
                json.append("\"aaData\":[");
                dados = false;
                while (rs.next()) {
                    dados = true;
                    json.append("[\"").append(rs.getString("codigo")).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("cliente"))).append("\",");
                    json.append("\"").append(rs.getString("responsavel").trim().replaceAll("\"", "\'")).append("\",");
                    json.append("\"").append(Uteis.getData(rs.getTimestamp("data"))).append("\",");
                    json.append("\"").append(Uteis.normalizarStringJSON(rs.getString("empresa"))).append("\",");
                    json.append("\"").append(Formatador.formatarValorMonetario(rs.getDouble("valortotal"))).append("\"],");
                }
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT rp.codigo, rp.nomepessoapagador AS cliente, rp.data, \n"
                + "usr.username AS responsavel, emp.nome AS empresa, rp.valortotal\n"
                + "FROM recibopagamento rp\n"
                + "  LEFT JOIN usuario usr ON rp.responsavellancamento = usr.codigo\n"
                + "  LEFT JOIN empresa emp ON rp.empresa = emp.codigo\n");
        if (empresa != 0) {
            sql.append("  WHERE rp.empresa = ?\n");
        }
        sql.append("  ORDER BY rp.codigo DESC");
        PreparedStatement sqlConsultar = con.prepareStatement(sql.toString());
        if (empresa != 0) {
            sqlConsultar.setInt(1, empresa);
        }
        return sqlConsultar;
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(empresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                ReciboPagamentoVO recibopag = new ReciboPagamentoVO();
                String geral = rs.getString("codigo") + rs.getString("cliente") + rs.getString("data") + rs.getString("responsavel") + rs.getString("empresa") + rs.getString("valortotal");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    recibopag.setCodigo(rs.getInt("codigo"));
                    recibopag.setNomePessoaPagador(rs.getString("cliente"));
                    recibopag.setData(rs.getDate("data"));
                    recibopag.setUsuarioVO(new UsuarioVO());
                    recibopag.getUsuarioVO().setUsername(rs.getString("responsavel"));
                    recibopag.getEmpresa().setNome(rs.getString("empresa"));
                    recibopag.setValorTotal(rs.getDouble("valortotal"));
                    lista.add(recibopag);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Cliente")) {
            Ordenacao.ordenarLista(lista, "nomePessoaPagador");
        } else if (campoOrdenacao.equals("Data")) {
            Ordenacao.ordenarLista(lista, "data_Apresentar");
        } else if (campoOrdenacao.equals("Responsável")) {
            Ordenacao.ordenarLista(lista, "responsavel_Apresentar");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "empresa_Apresentar");
        } else if (campoOrdenacao.equals("Valor")) {
            Ordenacao.ordenarLista(lista, "valor_Apresentar");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;

    }

    public List<ReciboPagamentoVO> consultarPorNFeEmitida(boolean nfseEmitida, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM recibopagamento WHERE nfseemitida = " + nfseEmitida;
        try (ResultSet rs = criarConsulta(sql, con)) {
            return montarDadosConsulta(rs, nivelMontarDados, con);
        }
    }

    public Integer codigoEventoCE(Integer codigoRecibo) {
        try {
            try (ResultSet rs = criarConsulta("select nec.eventointeresse from negociacaoeventocontratorecibo necr\n"
                    + "inner join negociacaoeventocontrato nec on nec.codigo = necr.contrato\n"
                    + " where necr.recibo = " + codigoRecibo, con)) {
                if (rs.next()) {
                    return rs.getInt("eventointeresse");
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public List<ReciboPagamentoVO> consultarPorCodigoContrato(Integer contrato, int nivelMontarDados) throws Exception {
        String sql = "select * from reciboPagamento where contrato = " + contrato + "";
        try (ResultSet rs = criarConsulta(sql, con)) {
            return montarDadosConsulta(rs, nivelMontarDados, con);
        }
    }
    
    public boolean reciboPagaProdutosPessoa(Integer recibo, Integer pessoa) throws Exception {
        String sql = "select rec.codigo from recibopagamento rec "
                + " inner join movprodutoparcela mpp on mpp.recibopagamento = rec.codigo "
                + " inner join movproduto prod on prod.codigo = mpp.movproduto "
                + " where rec.codigo = "+recibo +"  and prod.pessoa = "+pessoa+" limit 1";
        return existe(sql, con);
    }
    
    public Integer consultarPrimeiroReciboCheques(String codigoCheques) {
        try {
            try (ResultSet rs = criarConsulta("select re.codigo as recibo from recibopagamento re inner join movpagamento mov on "
                    + " re.codigo = mov.recibopagamento inner join cheque ch on "
                    + " ch.movpagamento = mov.codigo where ch.codigo in (" + codigoCheques + ") order by re.codigo;", con)) {
                if (rs.next()) {
                    return rs.getInt("recibo");
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public String imprimirReciboPDF(Boolean ce, Boolean reciboParaImpressoraTermica, EmpresaVO empresaVO, EmpresaVO empresaLogado,
                                    UsuarioVO usuarioLogado, ReciboPagamentoVO reciboVO, Integer codigoEvento, String tipoRelatorio, HttpServletRequest request) throws Exception{
        ReciboControle controleRecibo = new ReciboControle(Boolean.TRUE);

        String nomeRelatorio = getIdEntidade();
        String titulo = "RECIBO";
        String design = getDesignIReportRelatorio(reciboParaImpressoraTermica);
        String nomeEmpresa = "";
        if (empresaLogado.getCodigo() != 0) {
            nomeEmpresa = empresaLogado.getNome();
        }
        if (reciboVO.getPessoaPagador().getCodigo() != 0) {
            Pessoa pessoaDAO = new Pessoa(con);
            Cliente clienteDAO = new Cliente(con);
            reciboVO.setPessoaPagador(pessoaDAO.consultarPorChavePrimaria(reciboVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS));
            reciboVO.setNomeResponsavelLegal(reciboVO.getNomePessoaPagador());
            PessoaVO pessoaResponsavelPagamento = clienteDAO.obterPessoaResponsavelCliente(null, reciboVO.getPessoaPagador().getCodigo(), true, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            pessoaDAO = null;
            clienteDAO = null;
            if (Uteis.calcularIdadePessoa(negocio.comuns.utilitarias.Calendario.hoje(), reciboVO.getPessoaPagador().getDataNasc()) < 18) {
                if (pessoaResponsavelPagamento != null && !UteisValidacao.emptyString(pessoaResponsavelPagamento.getNome())) {
                    reciboVO.setNomeResponsavelLegal(pessoaResponsavelPagamento.getNome());
                    reciboVO.setNomeResponsaveis("(Resp.: " + pessoaResponsavelPagamento.getNome() + ")");
                } else if (!UteisValidacao.emptyString(reciboVO.getPessoaPagador().getNomeMae())) {
                    reciboVO.setNomeResponsavelLegal(reciboVO.getPessoaPagador().getNomeMae());
                    reciboVO.setNomeResponsaveis("(Resp.: " + reciboVO.getPessoaPagador().getNomeMae() + ")");
                } else if (!UteisValidacao.emptyString(reciboVO.getPessoaPagador().getNomePai())) {
                    reciboVO.setNomeResponsavelLegal(reciboVO.getPessoaPagador().getNomePai());
                    reciboVO.setNomeResponsaveis("(Resp.: " + reciboVO.getPessoaPagador().getNomePai() + ")");
                }
            }
        }

        ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
        Integer codigoEventoCE = reciboPagamentoDAO.codigoEventoCE(reciboVO.getCodigo());
        reciboPagamentoDAO = null;
        if(!UteisValidacao.emptyNumber(codigoEventoCE)){
            ce = true;
            codigoEvento = codigoEventoCE;
        }
        CaixaPorOperadorRel caixaPorOperadorRel = new CaixaPorOperadorRel();
        caixaPorOperadorRel.setMostraContrato(true);
        reciboVO.setEmpresa(empresaVO);
        caixaPorOperadorRel.carregarDadosReciboPagamentoRelVO(reciboVO);
        if (ce) {
            MovParcela movParcelaDAO = new MovParcela(con);
            caixaPorOperadorRel.getReciboPagamentoRelTO().setListaMovParcelaVOs(movParcelaDAO.consultarParcelasEventoPorRecibo(reciboVO.getCodigo()));
            movParcelaDAO = null;
            EventoInteresse eventoInteresseDAO = new EventoInteresse(con);
            EventoInteresseVO evento = eventoInteresseDAO.obter(codigoEvento);
            eventoInteresseDAO = null;
            caixaPorOperadorRel.getReciboPagamentoRelTO().setCentralEventos(true);
            if (evento != null) {
                caixaPorOperadorRel.getReciboPagamentoRelTO().setDescricaoDevolucao("Evento: " + evento.getNomeEvento());
            }

        }

        for (Object obj : caixaPorOperadorRel.getReciboPagamentoRelTO().getListaMovPagamentoVOs()) {
            MovPagamentoVO movPag = (MovPagamentoVO) obj;
            Cheque chequeDAO = new Cheque(con);
            movPag.setChequeVOs(chequeDAO.consultarPagamentoCheques(movPag.getCodigo(), null, false,false,false, Uteis.NIVELMONTARDADOS_TODOS));
            chequeDAO = null;
        }

        caixaPorOperadorRel.adicionarObjReciboPagamentoRelVOs(caixaPorOperadorRel.getReciboPagamentoRelTO());
        caixaPorOperadorRel.setReciboPagamentoRelTO(new ReciboPagamentoRelTO());
        caixaPorOperadorRel.contarMarcadoresRelatorio();
        CaixaPorOperadorRelControleRel caixaPorOperadorControle = new CaixaPorOperadorRelControleRel(Boolean.TRUE);
        caixaPorOperadorControle.setCaixaPorOperadorRel(caixaPorOperadorRel);
        List <Integer> contratosJaAdicionadosModalidades = new ArrayList<Integer>(); // armazena
        StringBuilder observacoesRecibo = new StringBuilder();
        List<String> infoTurmas = new ArrayList<>();
        for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
            ContratoVO contratoVO = new ContratoVO();
            List<MovProdutoVO> produtoVOSRemover = new ArrayList<>();
            for (Object obj : recibo.getListaMovProdutoVOs()) {
                MovProdutoVO movProduto = (MovProdutoVO) obj;
                if(movProduto.getTotalFinal()==0 && !empresaLogado.isMostrarValoresZeradosRel()) {
                    produtoVOSRemover.add(movProduto);
                    continue;
                }
                if (movProduto.getProduto().getTipoProduto().equals("PM") && movProduto.getMulta() == 0.0 && movProduto.getJuros() == 0.0) {

                    recibo.getMovProduto().getProduto().setTipoProduto(movProduto.getProduto().getTipoProduto());
                    if( UteisValidacao.emptyNumber(contratoVO.getCodigo()) || contratoVO.getCodigo().equals(movProduto.getContrato().getCodigo())){
                        Contrato contratoDAO = new Contrato(con);
                        contratoVO = contratoDAO.consultarPorChavePrimaria(movProduto.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        contratoDAO = null;
                    }

                    controleRecibo.calcularConvDescontoMovProduto(recibo, movProduto, contratoVO);
                    controleRecibo.calcularDescontoExtraMovProduto(recibo, movProduto);

                    if (empresaVO.isMostrarModalidade() && !contratosJaAdicionadosModalidades.contains(reciboVO.getContrato().getCodigo())) {
                        int nivelMontarDados = Uteis.NIVELMONTARDADOS_ROBO;
                        if  (empresaVO.isDetalharNiveisModalidades()) {
                            nivelMontarDados = Uteis.NIVELMONTARDADOS_TODOS;
                        }
                        ContratoModalidade contratoModalidadeDAO = new ContratoModalidade(con);
                        List contratoModalidadeVO = contratoModalidadeDAO.consultarPorCodigoContrato(reciboVO.getContrato().getCodigo(), nivelMontarDados);
                        contratoModalidadeDAO = null;
                        for (Object contrato : contratoModalidadeVO) {
                            ContratoModalidadeVO contratoMod = (ContratoModalidadeVO) contrato;
                            if (!recibo.getModalidades().contains(contratoMod.getModalidade().getNome())) {
                                recibo.setModalidades(" | " + contratoMod.getModalidade().getNome() + recibo.getModalidades());
                            }

                            if (empresaVO.isDetalharNiveisModalidades()) {
                                for (Object contratoModalidadeHorarioTurma : contratoMod.getListaContratoModalidadesHorarioTurmaVOs()) {
                                    ContratoModalidadeHorarioTurmaVO cmhtVO = (ContratoModalidadeHorarioTurmaVO) contratoModalidadeHorarioTurma;
                                    HorarioTurmaVO htVO = cmhtVO.getHorarioTurma();
                                    String infoAdd = "Nível: " + htVO.getNivelTurma().getDescricao() + "; Professor: " + htVO.getProfessor().getPessoa_Apresentar() + " ; Horários: " + htVO.getDiaSemana() + ": " + htVO.getHoraInicial() + " - " + htVO.getHoraFinal() + ";\r\n";

                                    if (!infoTurmas.contains(infoAdd)) {
                                        infoTurmas.add(infoAdd);
                                    }
                                }
                            }
                        }
                        contratosJaAdicionadosModalidades.add(reciboVO.getContrato().getCodigo());
                    }
                }
                if ( movProduto.getProduto().getTipoProduto().equals("DI")){
                    recibo.getMovProduto().setDataInicioVigencia(movProduto.getProduto().getDataInicioVigencia());
                    recibo.getMovProduto().setDataFinalVigencia(movProduto.getProduto().getDataFinalVigencia());
                }
            }
            recibo.getListaMovProdutoVOs().removeAll(produtoVOSRemover);
            recibo.setModalidades(recibo.getModalidades().replaceFirst("\\|", ""));

            if (empresaVO.isDetalharNiveisModalidades()) {
                for (String info : infoTurmas) {
                    observacoesRecibo.append(info);
                }
            }
        }
        observacoesRecibo.append(empresaVO.getObservacaoRecibo());

        boolean sairFor = false;
        for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
            for (Object obj : recibo.getListaMovPagamentoVOs()) {
                MovPagamentoVO movPagamentoVO = (MovPagamentoVO) obj;
                if (!UteisValidacao.emptyNumber(movPagamentoVO.getConvenio().getCodigo())) {
                    ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorChavePrimaria(movPagamentoVO.getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    movPagamentoVO.setConvenio(convenioCobrancaVO);
                    if (movPagamentoVO.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.PINPAD_STONE_CONNECT)) {
                        movPagamentoVO.setObservacao("\nOrigem: Stone Connect\n");
                        sairFor = true;
                        break;
                    }
                }
            }
            if (sairFor) {
                break;
            }
        }

        tratarDadosCupomPinpadObservacaoRecibo(caixaPorOperadorRel);

        Map<String, Object> params = new HashMap<String, Object>();
        String nomeBundle = "bundleRelatorios.resourceBundleRelatorios";
        ResourceBundle relatorioResourceBundle = ResourceBundle.getBundle(nomeBundle, new Locale(empresaVO.getLocale().getLanguage(),empresaVO.getLocale().getCountry()));
        params.put(JRParameter.REPORT_LOCALE, new Locale(empresaVO.getLocale().getLanguage(),empresaVO.getLocale().getCountry()));
        params.put(JRParameter.REPORT_RESOURCE_BUNDLE, relatorioResourceBundle);
        params.put("nomeRelatorio", nomeRelatorio);
        params.put("tituloRelatorio", titulo);
        params.put("nomeEmpresa", nomeEmpresa);
        params.put("imagemLogo", "");
        params.put("mensagemRel", "");
        params.put("tipoRelatorio", tipoRelatorio);
        params.put("caminhoParserXML", "/" + this.getIdEntidade() + "/registros");
        params.put("nomeDesignIReport", design);
        params.put("nomeUsuario", usuarioLogado.getNome());
        params.put("filtros", "nenhum");
        params.put("dataIni", "");
        params.put("dataFim", "");
        params.put("qtdAV", String.valueOf(caixaPorOperadorRel.getQtdPagamentoAV()));
        params.put("qtdCA", String.valueOf(caixaPorOperadorRel.getQtdPagamentoCA()));
        params.put("qtdCD", String.valueOf(caixaPorOperadorRel.getQtdPagamentoCD()));
        params.put("qtdBB", String.valueOf(caixaPorOperadorRel.getQtdPagamentoBB()));
        params.put("qtdChequeAV", String.valueOf(caixaPorOperadorRel.getQtdPagamentoChAvista()));
        params.put("qtdChequePR", String.valueOf(caixaPorOperadorRel.getQtdPagamentoChPrazo()));
        params.put("qtdOutro", String.valueOf(caixaPorOperadorRel.getQtdPagamentoOutros()));
        params.put("valorAV", caixaPorOperadorRel.getValorPagamentoAV());
        params.put("valorCA", caixaPorOperadorRel.getValorPagamentoCA());
        params.put("valorCD", caixaPorOperadorRel.getValorPagamentoCD());
        params.put("valorBB", caixaPorOperadorRel.getValorPagamentoBB());
        params.put("valorChequeAV", caixaPorOperadorRel.getValorPagamentoChAvista());
        params.put("valorChequePR", caixaPorOperadorRel.getValorPagamentoChPrazo());
        params.put("valorOutro", caixaPorOperadorRel.getValorPagamentoOutros());
        params.put("SUBREPORT_DIR", getCaminhoSubRelatorio(reciboParaImpressoraTermica));
        params.put("SUBREPORT_DIR1", getCaminhoSubRelatorio(reciboParaImpressoraTermica));
        params.put("SUBREPORT_DIR2", getCaminhoSubRelatorio(reciboParaImpressoraTermica));
        params.put("qtdDV", "");
        params.put("valorDV", 0.0);
        params.put("qtdDR", "");
        params.put("valorDR", 0.0);
        params.put("devolucoes", null);
        params.put("totalizadores", null);
        params.put("apresentarDados", !caixaPorOperadorRel.getListaReciboPagamentoRelTOs().isEmpty());
        params.put("somenteSintetico", false);

        params.put("mostrarCnpj", empresaVO.isMostrarCnpj());
        params.put("mostrarModalidade", empresaVO.isMostrarModalidade());
        params.put("detalharPeriodoProduto", empresaVO.isDetalharPeriodoProduto());
        params.put("detalharParcelas", empresaVO.isDetalharParcelas());
        params.put("detalharPagamentos", empresaVO.isDetalharPagamentos());
        params.put("detalharDescontos", empresaVO.isDetalharDescontos());
        params.put("emitirNomeResponsavel", empresaVO.isEmitirNoNomeResponsavel());
        params.put("apresentarAssinaturas", empresaVO.isApresentarAssinaturas());
        params.put("apresentarObservacao", !UteisValidacao.emptyString(empresaVO.getObservacaoRecibo()) || empresaVO.isDetalharNiveisModalidades());
        params.put("observacaoRecibo", observacoesRecibo.toString());
        params.put("group_startnewpage", empresaVO.isQuebrarPaginaRecibo());
        params.put("moeda", empresaVO.getMoeda());
        params.put("identificador", IdentificadorInternacionalEnum.BRASIL.getIdentificadorPrimario());

        List<AuxiliarReciboPagamentoRelTO> auxiliarReciboPagamentoRelTOs = new ArrayList<AuxiliarReciboPagamentoRelTO>();
        int qtdVias = empresaVO.getQtdVias();
        if (reciboParaImpressoraTermica) {
            qtdVias = 1;
        }
        for (int i = 0; i < qtdVias; i++) {
            for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
                AuxiliarReciboPagamentoRelTO auxiliar = new AuxiliarReciboPagamentoRelTO();
                auxiliar.setRecibo(recibo);
                auxiliar.setSequencial(i);
                auxiliarReciboPagamentoRelTOs.add(auxiliar);
            }
        }

        params.put("listaObjetos", auxiliarReciboPagamentoRelTOs);

        SuperControleRelatorio controleRelatorio = new SuperControleRelatorio();
        controleRelatorio.apresentarRelatorioObjetosComRequest(params, request);
        boolean servletRelatorio = (request.getAttribute("servlet-relatorio") != null && request.getAttribute("servlet-relatorio").toString().equalsIgnoreCase("true"));
        return Uteis.getPath(request) + "/faces/" + (servletRelatorio ? "servlet-relatorio" : "relatorio") + "/" + (String) request.getAttribute("nomeArquivoRelatorioGeradoAgora");
    }

    private static void tratarDadosCupomPinpadObservacaoRecibo(CaixaPorOperadorRel caixaPorOperadorRel) {
        StringBuilder cuponsCappta = new StringBuilder();
        int quantidadeItensLista = 0;
        for (ReciboPagamentoRelTO recibo : caixaPorOperadorRel.getListaReciboPagamentoRelTOs()) {
            quantidadeItensLista = recibo.getListaMovPagamentoVOs().size();
            // Constroi o cupom para o último item
            for (Object obj : recibo.getListaMovPagamentoVOs()) {
                MovPagamentoVO movPagamentoVO = (MovPagamentoVO) obj;
                if (movPagamentoVO.getFormaPagamento().getPinpad().isCAPPTA()) {
                    cuponsCappta.append(" \n\n");
                    String observacao = movPagamentoVO.getObservacao();
                    observacao = observacao.replace("\\n", "\n");
                    observacao = observacao.replace("<br/>", "\n");
                    cuponsCappta.append(observacao);
                }
            }
            if (!"".equals(cuponsCappta.toString())) {
                for (int i = 0; i < quantidadeItensLista; i++) {
                    MovPagamentoVO mov = (MovPagamentoVO) recibo.getListaMovPagamentoVOs().get(i);
                    // Limpa todas as observações das MovPagamentoVO
                    mov.setObservacao("");
                    // Se for o último item, seta a observação com o conteúdo montado, pois no PDF só exibe a observação do último MovPagamentoVO
                    if (i == quantidadeItensLista - 1) {
                        mov.setObservacao(cuponsCappta.toString());
                    }
                }
            }
        }
    }

    public void atualizarConciliacaoEstornoPagamentos(EstornoReciboVO obj) throws Exception {
        StringBuilder sql = new StringBuilder();
          for (MovPagamentoVO pagamentoVO : obj.getListaMovPagamento()) {
              sql = new StringBuilder();
              sql.append("UPDATE extratodiarioitem \n");
              sql.append("   SET situacao = " + SituacaoItemExtratoEnum.ESTORNADO_SISTEMA.ordinal() + "\n");
              sql.append("      ,pessoa = " + pagamentoVO.getPessoa().getCodigo() + "\n");
              sql.append(" WHERE codigomovpagamento  = " + pagamentoVO.getCodigo() + " \n");
              executarConsulta(sql.toString(), con);
          }
    }

    private void tratarParcelasChequesDevolvidos(EstornoReciboVO obj) throws Exception {
        DevolucaoChequeServiceImpl devService = new DevolucaoChequeServiceImpl(con);
        devService.tratarEstornoReciboPagamentoChequesDevolvidos(obj.getListaMovParcela());
    }

    public void inserirIntegracaoProtheus(Integer recibo, String operacao) throws Exception {
        inserirIntegracaoProtheus(recibo, operacao, null);
    }

    public void inserirIntegracaoProtheus(Integer recibo, String operacao, Date data) throws Exception {
        if(operacao.equals("CP")){
            String url = PropsService.getPropertyValue(PropsService.urlIntegracaoProtheus) + "/parcela/cancelar?id=" + recibo;
            String resposta = ExecuteRequestHttpService.executeRequest(url, new HashMap<String, String>());
            JSONObject json = new JSONObject(resposta);
            if (!json.optBoolean("status")) {
                throw new Exception("ERRO NA INTEGRAÇÃO COM PROTHEUS: " + json.optString("erro"));
            }
        }else{
            String url = PropsService.getPropertyValue(PropsService.urlIntegracaoProtheus) + "/recibo/"+(operacao.equals("A") ? "alteracao" : "estorno")
                    +"?id=" + recibo + (data == null ? "" : ("&data="+Uteis.getDataAplicandoFormatacao(data, "ddMMyyyy")));
            String resposta = ExecuteRequestHttpService.executeRequest(url, new HashMap<String, String>());
            JSONObject json = new JSONObject(resposta);
            if (!json.optBoolean("status")) {
                throw new Exception("ERRO NA INTEGRAÇÃO COM PROTHEUS: " + json.optString("erro"));
            }
        }
    }

    public Map<String, Object> errosProtheus() throws Exception{
        Map<String, Object> map = new HashMap<String,Object>();
        String url = PropsService.getPropertyValue(PropsService.urlIntegracaoProtheus) + "/recibo/erros";
        String resposta = ExecuteRequestHttpService.executeRequestGET(url, new HashMap<String, String>());
        JSONObject json = new JSONObject(resposta);
        if (!json.optBoolean("status")) {
            throw new Exception("Verifique o serviço da Integração Pacto-Protheus");
        }
        map.put("erros", montarLogsProtheus("erros", json));
        map.put("sucessos", montarLogsProtheus("sucessos", json));
        map.put("hoje", json.optInt("hoje"));
        return map;
    }

    private List<LogEnvio> montarLogsProtheus(String campo, JSONObject json) throws Exception{
        List<LogEnvio> logs = new ArrayList<LogEnvio>();
        JSONArray arrays = json.getJSONArray(campo);

        for(int i = 0; i < arrays.length(); i++){
            LogEnvio logEnvio = new LogEnvio(arrays.getJSONObject(i));
            try {
                ResultSet rs = criarConsulta("select matricula, nomecliente from situacaoclientesinteticodw sw \n" +
                        "inner join recibopagamento rp on rp.pessoapagador = sw.codigopessoa\n" +
                        "where rp.codigo = " + logEnvio.getRecibo(), con);
                if(rs.next()){
                    logEnvio.setMatricula(rs.getString("matricula"));
                    logEnvio.setNome(rs.getString("nomecliente"));
                }
            }catch (Exception e){}
            logs.add(logEnvio);
        }
        return logs;
    }

    public List<LogEnvio> montarLogsAguardando() throws Exception {
        List<LogEnvio> logs = new ArrayList<LogEnvio>();
        try (ResultSet rs = criarConsulta("select nomepessoapagador, data from recibopagamento where not integrado order by data desc", con)) {
            while (rs.next()) {
                LogEnvio logEnvio = new LogEnvio();
                logEnvio.setNome(rs.getString("nomepessoapagador"));
                logEnvio.setUltimaTentativa(rs.getTimestamp("data").getTime());
                logs.add(logEnvio);
            }
        }
        return logs;
    }

    public Integer totalEmBordero() throws Exception{
        String sql = "select count(mp.recno) as total from remessaitem ri\n" +
                "inner join remessa r on r.codigo = ri.remessa\n" +
                "inner join movparcela mp on mp.codigo = ri.movparcela \n" +
                "where r.identificador like 'FAKE%' \n" +
                "and (ri.props not like '%StatusVenda=99%' or ri.props not like '%StatusVenda=00%')";
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;

    }

    public String emBordero() throws Exception{
        String sql = "select mp.recno, mp.valorparcela, p.nome, cli.matricula, e.idexterno  from remessaitem ri\n" +
                "inner join remessa r on r.codigo = ri.remessa\n" +
                "inner join movparcela mp on mp.codigo = ri.movparcela\n" +
                "inner join pessoa p on p.codigo = mp.pessoa\n" +
                "inner join cliente cli on cli.pessoa = p.codigo\n" +
                "inner join empresa e on e.codigo = cli.empresa \n" +
                "where r.identificador like 'FAKE%' \n" +
                "and (ri.props not like '%StatusVenda=99%' or ri.props not like '%StatusVenda=00%') order by p.nome";

        StringBuilder resultado;
        try (ResultSet rs = criarConsulta(sql, con)) {
            resultado = new StringBuilder();
            while (rs.next()) {
                resultado.append(rs.getString("idexterno")).append(";");
                resultado.append(rs.getString("recno")).append(";");
                resultado.append(rs.getString("matricula")).append(";");
                resultado.append(rs.getString("nome")).append(";");
                resultado.append(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valorparcela"))).append(";\n");
            }
        }
        return resultado.toString();

    }

    public Integer totalAguardando() throws Exception{
        try (ResultSet rs = criarConsulta("select count(codigo) as total from recibopagamento where not integrado", con)) {
            if (rs.next()) {
                return rs.getInt("total");
            }
        }
        return 0;
    }

    public ReciboPagamentoVO obterReciboExportacaoRecebiveis(Integer recibo, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("u.*, \n");
        sql.append("rp.contrato \n");
        sql.append("from recibopagamento rp \n");
        sql.append("inner join usuario u on u.codigo = rp.responsavellancamento \n");
        sql.append("where rp.codigo = ").append(recibo);
        try (PreparedStatement sqlConsultar = this.con.prepareStatement(sql.toString())) {
            try (ResultSet rs = sqlConsultar.executeQuery()) {
                if (rs.next()) {
                    ReciboPagamentoVO reciboPagamentoVO = new ReciboPagamentoVO();
                    reciboPagamentoVO.setResponsavelLancamento(Usuario.montarDados(rs, nivelMontarDados, this.con));
                    reciboPagamentoVO.getContrato().setCodigo(rs.getInt("contrato"));
                    return reciboPagamentoVO;
                }
                return null;
            }
        }
    }
}
