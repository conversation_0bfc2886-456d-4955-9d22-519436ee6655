package negocio.facade.jdbc.financeiro;

import negocio.comuns.financeiro.enumerador.IndicadoresDadosGerencialEnum;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.Date;
import java.util.concurrent.Callable;

public class DadosGerenciaisPmgCallable implements Callable<String> {

    private final Connection con;
    Integer empresa;
    String nomeEmpresa;
    String chave;
    Date data;
    IndicadoresDadosGerencialEnum indicador;
    String frequencia;

    public DadosGerenciaisPmgCallable(Connection con, Integer empresa, String nomeEmpresa, String chave, Date data, IndicadoresDadosGerencialEnum indicador, String frequencia) {
        this.con = con;
        this.empresa = empresa;
        this.nomeEmpresa = nomeEmpresa;
        this.chave = chave;
        this.data = data;
        this.indicador = indicador;
        this.frequencia = frequencia;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public IndicadoresDadosGerencialEnum getIndicador() {
        return indicador;
    }

    public void setIndicador(IndicadoresDadosGerencialEnum indicador) {
        this.indicador = indicador;
    }

    public String getFrequencia() {
        return frequencia;
    }

    public void setFrequencia(String frequencia) {
        this.frequencia = frequencia;
    }

    @Override
    public String call() throws Exception {
        try (Connection newConnection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            DadosGerencialPmg dadosGerencialPmgDAO = new DadosGerencialPmg(newConnection);
            dadosGerencialPmgDAO.gerarDadosIndicador(empresa, nomeEmpresa, chave, data, indicador, frequencia);
            return "";
        }
    }
}
