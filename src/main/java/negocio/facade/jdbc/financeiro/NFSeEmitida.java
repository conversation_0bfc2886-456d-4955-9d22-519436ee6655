package negocio.facade.jdbc.financeiro;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.LogAjusteGeral;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.notaFiscal.NotaFiscal;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NFSeEmitida extends SuperEntidade {

    public NFSeEmitida() throws Exception {
    }

    public NFSeEmitida(Connection conexao) throws Exception {
        super(conexao);
    }

    public static NFSeEmitidaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        NFSeEmitidaVO obj = new NFSeEmitidaVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getRecibo().setCodigo(dadosSQL.getInt("recibopagamento"));
        obj.getCheque().setCodigo(dadosSQL.getInt("cheque"));
        obj.getCartaoCredito().setCodigo(dadosSQL.getInt("cartaocredito"));
        obj.getMovPagamento().setCodigo(dadosSQL.getInt("movpagamento"));
        obj.getMovProduto().setCodigo(dadosSQL.getInt("movproduto"));
        obj.getMovConta().setCodigo(dadosSQL.getInt("movconta"));
        obj.getContrato().setCodigo(dadosSQL.getInt("contrato"));
        obj.setIdRps(dadosSQL.getInt("rps"));
        obj.setNrNotaManual(dadosSQL.getString("nrNotaManual"));
        obj.setIdReferencia(dadosSQL.getString("IdReferencia"));
        obj.setValor(dadosSQL.getDouble("valor"));
        obj.setDataEnvio(dadosSQL.getTimestamp("dataEnvio"));
        obj.setPessoa(dadosSQL.getInt("pessoa"));
        obj.setJsonEnviar(dadosSQL.getString("jsonEnviar"));
        obj.setDataRegistro(dadosSQL.getTimestamp("dataRegistro"));
        obj.setDataEmissao(dadosSQL.getTimestamp("dataEmissao"));
        obj.setDataReferencia(dadosSQL.getTimestamp("dataReferencia"));
        obj.setEmpresa(dadosSQL.getInt("empresa"));
        obj.setNotaFamilia(dadosSQL.getBoolean("notaFamilia"));
        obj.getConfiguracaoNotaFiscalVO().setCodigo(dadosSQL.getInt("ConfiguracaoNotaFiscal"));
        obj.setEnotas(dadosSQL.getBoolean("enotas"));
        obj.setResultadoEnvio(dadosSQL.getString("resultadoEnvio"));
        obj.setSituacaoNotaFiscal(SituacaoNotaFiscalEnum.obterPorCodigo(dadosSQL.getInt("situacaoNotaFiscal")));
        obj.setSequencialFamilia(dadosSQL.getInt("sequencialFamilia"));
        obj.setNovoObj(false);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
            montarDadosMovProduto(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosMovPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosCheque(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosCartaoCredito(obj, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS, con);
            montarDadosReciboPagamento(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            montarDadosMovConta(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            return obj;
        }

        return obj;
    }

    private static void montarDadosMovProduto(NFSeEmitidaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getMovProduto() == null || UteisValidacao.emptyNumber(obj.getMovProduto().getCodigo())) {
            return;
        }
        MovProduto movProdutoDAO = new MovProduto(con);
        obj.setMovProdutoVO(movProdutoDAO.consultarPorChavePrimaria(obj.getMovProduto().getCodigo(), nivelMontarDados));
        movProdutoDAO = null;
    }

    private static void montarDadosMovPagamento(NFSeEmitidaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getMovPagamento() == null || UteisValidacao.emptyNumber(obj.getMovPagamento().getCodigo())) {
            return;
        }
        MovPagamento movPagamentoDAO = new MovPagamento(con);
        obj.setMovPagamento(movPagamentoDAO.consultarPorChavePrimaria(obj.getMovPagamento().getCodigo(), nivelMontarDados));
        movPagamentoDAO = null;
    }

    private static void montarDadosMovConta(NFSeEmitidaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getMovConta() == null || UteisValidacao.emptyNumber(obj.getMovConta().getCodigo())) {
            return;
        }
        MovConta contaDAO = new MovConta(con);
        obj.setMovConta(contaDAO.consultarPorCodigo(obj.getMovConta().getCodigo(), nivelMontarDados));
        contaDAO = null;
    }

    private static void montarDadosReciboPagamento(NFSeEmitidaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getRecibo() == null || UteisValidacao.emptyNumber(obj.getRecibo().getCodigo())) {
            return;
        }
        ReciboPagamento reciboDAO = new ReciboPagamento(con);
        obj.setRecibo(reciboDAO.consultarPorChavePrimaria(obj.getRecibo().getCodigo(), nivelMontarDados));
        reciboDAO = null;
    }

    private static void montarDadosCheque(NFSeEmitidaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCheque() == null || UteisValidacao.emptyNumber(obj.getCheque().getCodigo())) {
            return;
        }
        Cheque chequeDAO = new Cheque(con);
        obj.setCheque(chequeDAO.consultarPorChavePrimaria(obj.getCheque().getCodigo(), nivelMontarDados));
        chequeDAO = null;
    }

    private static void montarDadosCartaoCredito(NFSeEmitidaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getCartaoCredito() == null || UteisValidacao.emptyNumber(obj.getCartaoCredito().getCodigo())) {
            return;
        }
        CartaoCredito cartaoDAO = new CartaoCredito(con);
        obj.setCartaoCredito(cartaoDAO.consultarPorChavePrimaria(obj.getCartaoCredito().getCodigo(), nivelMontarDados));
        cartaoDAO = null;
    }

    public void excluir(NFSeEmitidaVO obj) throws Exception {
        String sql = "DELETE FROM nfseemitida WHERE codigo = " + obj.getCodigo();
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }

    public void excluirComLog(NFSeEmitidaVO obj, UsuarioVO usuarioLogado, ProcessoAjusteGeralEnum processoAjusteGeralEnum) throws SQLException {
        try {
            con.setAutoCommit(false);
            excluirComLogSemCommit(obj, usuarioLogado, processoAjusteGeralEnum);
            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
        } finally {
            con.setAutoCommit(true);
        }
    }
    public void excluirComLogEnotasSemCommit(Integer codNotaFiscal, NFSeEmitidaVO obj,
                                    UsuarioVO usuarioVO) throws Exception {
        excluirComLogSemCommit(obj, usuarioVO, ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL_ENOTAS);

        NFSeEmitidaHistorico histDAO = new NFSeEmitidaHistorico(con);
        histDAO.excluirPorNFSeEmitida(obj);
        histDAO = null;

        NotaFiscal notaDAO = new NotaFiscal(con);
        notaDAO.marcarExcluido(true, codNotaFiscal, usuarioVO);
        notaDAO = null;
    }

    public void excluirComLogEnotas(Integer codNotaFiscal, NFSeEmitidaVO obj,
                                    UsuarioVO usuarioVO) throws SQLException {
        try {
            con.setAutoCommit(false);

            excluirComLogEnotasSemCommit(codNotaFiscal, obj, usuarioVO);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirComLogFamiliaEnotas(Integer codNotaFiscal, Integer sequencialFamilia, UsuarioVO usuarioVO) throws SQLException {
        try {
            con.setAutoCommit(false);

            List<NFSeEmitidaVO> lista = consultarPorSequencialFamilia(sequencialFamilia);

            for (NFSeEmitidaVO obj : lista) {
                excluirComLogSemCommit(obj, usuarioVO, ProcessoAjusteGeralEnum.EXCLUIR_NOTA_FISCAL_ENOTAS);

                NFSeEmitidaHistorico histDAO = new NFSeEmitidaHistorico(con);
                histDAO.excluirPorNFSeEmitida(obj);
                histDAO = null;

                NotaFiscal notaDAO = new NotaFiscal(con);
                notaDAO.marcarExcluido(true, codNotaFiscal, usuarioVO);
                notaDAO = null;
            }

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            con.setAutoCommit(true);
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirComLogSemCommit(NFSeEmitidaVO obj, UsuarioVO usuarioLogado, ProcessoAjusteGeralEnum processoAjusteGeralEnum) throws SQLException, Exception {
        excluir(obj);
        LogAjusteGeral logAjusteGeralDAO = new LogAjusteGeral(con);
        logAjusteGeralDAO.incluir(Calendario.hoje(), usuarioLogado.getNome(), usuarioLogado.getUserOamd(), processoAjusteGeralEnum, montarDescricaoExcluirNotaFiscal(obj));
        logAjusteGeralDAO = null;
    }

    public void incluir(NFSeEmitidaVO obj) throws Exception {
        String sql = "INSERT INTO nfseemitida(recibopagamento, cartaocredito, cheque, movpagamento, movproduto, rps, contrato, movconta, "
                + "nrNotaManual, idReferencia, valor, dataEnvio, pessoa, jsonEnviar, dataRegistro, dataEmissao, dataReferencia, "
                + "empresa, notaFamilia, ConfiguracaoNotaFiscal, enotas, resultadoEnvio, situacaoNotaFiscal, sequencialFamilia) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getRecibo() != null && obj.getRecibo().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getRecibo().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getCartaoCredito() != null && obj.getCartaoCredito().getCodigo() != 0) {
            sqlInserir.setInt(2, obj.getCartaoCredito().getCodigo());
        } else {
            sqlInserir.setNull(2, 0);
        }
        if (obj.getCheque() != null && obj.getCheque().getCodigo() != 0) {
            sqlInserir.setInt(3, obj.getCheque().getCodigo());
        } else {
            sqlInserir.setNull(3, 0);
        }
        if (obj.getMovPagamento() != null && obj.getMovPagamento().getCodigo() != 0) {
            sqlInserir.setInt(4, obj.getMovPagamento().getCodigo());
        } else {
            sqlInserir.setNull(4, 0);
        }
        if (obj.getMovProduto() != null && obj.getMovProduto().getCodigo() != 0) {
            sqlInserir.setInt(5, obj.getMovProduto().getCodigo());
        } else {
            sqlInserir.setNull(5, 0);
        }
        if (!UteisValidacao.emptyNumber(obj.getIdRps())) {
            sqlInserir.setInt(6, obj.getIdRps());
        } else {
            sqlInserir.setNull(6, 0);
        }
        if (obj.getContrato() != null && obj.getContrato().getCodigo() != 0) {
            sqlInserir.setInt(7, obj.getContrato().getCodigo());
        } else {
            sqlInserir.setNull(7, 0);
        }
        if (obj.getMovConta() != null && obj.getMovConta().getCodigo() != 0) {
            sqlInserir.setInt(8, obj.getMovConta().getCodigo());
        } else {
            sqlInserir.setNull(8, 0);
        }
        sqlInserir.setString(9, obj.getNrNotaManual());
        sqlInserir.setString(10, obj.getIdReferencia());
        sqlInserir.setDouble(11, obj.getValor());
        sqlInserir.setTimestamp(12, Uteis.getDataJDBCTimestamp(obj.getDataEnvio()));
        sqlInserir.setInt(13, obj.getPessoa());
        sqlInserir.setString(14, obj.getJsonEnviar());
        sqlInserir.setTimestamp(15, Uteis.getDataJDBCTimestamp(obj.getDataRegistro()));
        sqlInserir.setTimestamp(16, Uteis.getDataJDBCTimestamp(obj.getDataEmissao()));
        sqlInserir.setTimestamp(17, Uteis.getDataJDBCTimestamp(obj.getDataReferencia()));
        sqlInserir.setInt(18, obj.getEmpresa());
        sqlInserir.setBoolean(19, obj.isNotaFamilia());
        resolveIntegerNull(sqlInserir, 20, obj.getConfiguracaoNotaFiscalVO().getCodigo());
        sqlInserir.setBoolean(21, obj.isEnotas());
        sqlInserir.setString(22, obj.getResultadoEnvio());
        sqlInserir.setInt(23, obj.getSituacaoNotaFiscal().getCodigo());
        resolveIntegerNull(sqlInserir, 24, obj.getSequencialFamilia());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);

        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.incluir(new NFSeEmitidaHistoricoVO(obj));
        nfSeEmitidaHistorico = null;
    }

    public void atualizarSituacaoIdRps(NFSeEmitidaVO obj) throws Exception {
        String sql = "UPDATE nfseemitida SET situacaoNotaFiscal = ?, rps = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getSituacaoNotaFiscal().getCodigo());
        sqlInserir.setInt(2, obj.getIdRps());
        sqlInserir.setInt(3, obj.getCodigo());
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarSituacaoIdRps(obj);
        nfSeEmitidaHistorico = null;
    }

    public void atualizarSituacaoIdRpsNotaFamilia(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer rps, String idNotaFamilia) throws Exception {
        String sql = "UPDATE nfseemitida SET situacaoNotaFiscal = ?, rps = ? WHERE notafamilia = true and idreferencia = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, rps);
        sqlInserir.setString(3, idNotaFamilia);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarSituacaoIdRpsNotaFamilia(situacaoNotaFiscal, rps, idNotaFamilia);
        nfSeEmitidaHistorico = null;
    }

    public void atualizarNrNotaManual(NFSeEmitidaVO obj) throws Exception {
        String sql = "UPDATE nfseemitida SET nrNotaManual = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getNrNotaManual());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarNrNotaManual(obj);
        nfSeEmitidaHistorico = null;
    }

    public NFSeEmitidaVO consultarPorRecibo(Integer codigoRecibo) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE recibopagamento = " + codigoRecibo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public boolean existeNFSePorRecibo(Integer codigoRecibo) throws Exception {
        return existe("SELECT codigo FROM nfseemitida WHERE recibopagamento = " + codigoRecibo, getCon());
    }

    public List<NFSeEmitidaVO> consultarListaPorRecibo(Integer codigoRecibo) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE recibopagamento = " + codigoRecibo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public void excluirPorNrNotaManual(String nrNotaManual) throws Exception {
        String sql = "DELETE FROM nfseemitida WHERE nrNotaManual = '" +nrNotaManual+"'";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.execute();
    }

    public Integer consultaPorNrNotaManual(String nrNotaManual) throws Exception {
        String sql = "SELECT count(*) as qtd FROM nfseemitida WHERE nrNotaManual = '" + nrNotaManual +"'";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return tabelaResultado.getInt("qtd");
        }
        return 0;
    }

    public NFSeEmitidaVO consultaPorPagamento(Integer codigoMovPagamento) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE movpagamento = " + codigoMovPagamento;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public boolean existeNFSePorPagamento(Integer codigoMovPagamento) throws Exception {
        return existe("SELECT codigo FROM nfseemitida WHERE movpagamento = " + codigoMovPagamento, getCon());
    }

    public NFSeEmitidaVO consultaPorCheque(String composicaoCheque) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE cheque in (" + composicaoCheque +")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public NFSeEmitidaVO consultaPorCheque(Integer codigoCheque) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE cheque = " + codigoCheque;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public boolean existeNFSePorCheque(Integer codigoCheque) throws Exception {
        return existe("SELECT codigo FROM nfseemitida WHERE cheque = " + codigoCheque, getCon());
    }

    public NFSeEmitidaVO consultaPorMovPagamentoOuReciboPagamento(Integer codigoMovPagamento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * \n");
        sql.append("FROM nfseemitida \n");
        sql.append("where movPagamento = ").append(codigoMovPagamento);
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }else{
            sql.delete(0, sql.length());
            sql.append("SELECT * \n");
            sql.append("FROM nfseemitida \n");
            sql.append("where reciboPagamento = (select reciboPagamento from movPagamento where codigo =").append(codigoMovPagamento).append(")");
            ResultSet rsRecibo = stm.executeQuery(sql.toString());
            if (rsRecibo.next()) {
                return montarDados(rsRecibo, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            }

        }
        return null;
    }

    public NFSeEmitidaVO consultaPorCartao(String composicaoCartao) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE cartaocredito in (" + composicaoCartao +")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public boolean existeNFSePorCartao(Integer codigoCartao) throws Exception {
        if (!UteisValidacao.emptyNumber(codigoCartao)) {
            return existe("SELECT codigo FROM nfseemitida WHERE cartaocredito = " + codigoCartao , getCon());
        }
//        if(!existeNota && !UteisValidacao.emptyString(composicaoCartao)){
//            existeNota =  existe("SELECT codigo FROM nfseemitida WHERE cartaocredito in (" + composicaoCartao +")", getCon());
//        }
        return false;
    }

    public NFSeEmitidaVO consultaPorProduto(Integer codigoProduto) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE movproduto = " + codigoProduto;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public NFSeEmitidaVO consultaPorConta(Integer codigoMovConta) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE movconta = " + codigoMovConta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public boolean existeNFSePorConta(Integer codigoMovConta) throws Exception {
        return existe("SELECT codigo FROM nfseemitida WHERE movconta = " + codigoMovConta, getCon());
    }

    public List<NFSeEmitidaVO> consultarPorContrato(Integer codigoContrato) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE contrato = " + codigoContrato;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public boolean existeNFSePorProduto(Integer codProduto) throws Exception {
        return existe("SELECT codigo FROM nfseemitida WHERE movproduto = " + codProduto, getCon());
    }

    public boolean existeNFSePorReciboPagamentoProduto(Integer codProduto) throws Exception {
        return existe("select codigo from nfseemitida  where recibopagamento in (select recibopagamento from movprodutoparcela  where movproduto  = " + codProduto + " )", getCon());
    }

    public boolean contratoParcialmenteEnviado(Integer codContrato) throws Exception {
        return existe("SELECT codigo FROM nfseemitida WHERE contrato = " + codContrato, getCon());
    }

    public void atualizarCartao(Integer codAntigo, Integer codNovo) throws Exception {
        String sql = "UPDATE nfseemitida SET cartaocredito  = ? WHERE cartaocredito= ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, codNovo);
        sqlInserir.setInt(2, codAntigo);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarCartao(codAntigo, codNovo);
        nfSeEmitidaHistorico = null;
    }

    public void incluirNFSeParaCartaoCreditoComposicao(String composicao, Integer codigoCartaoComposicao) throws Exception {
        if ((composicao != null) && (!composicao.trim().equals(""))) {
            String[] codigos = composicao.split(",");
            for (String codigo : codigos) {
                Integer codigoCartaoAntigo = Integer.parseInt(codigo);
                NFSeEmitidaVO nfSeEmitidaVO = consultarPorCartaoCredito(codigoCartaoAntigo);
                if (nfSeEmitidaVO != null) {
                    nfSeEmitidaVO.setNovoObj(false);
                    CartaoCreditoVO cartaoCreditoVO = new CartaoCreditoVO();
                    cartaoCreditoVO.setCodigo(codigoCartaoComposicao);
                    nfSeEmitidaVO.setCartaoCredito(cartaoCreditoVO);
                    incluir(nfSeEmitidaVO);
                }
            }
        }
    }

    public void incluirNFSeParaPagamentoDinheiro(Integer codigoMovPagamento, Integer movPagamentoOrigemCredito) throws Exception {
        NFSeEmitidaVO nfSeEmitidaVO = consultarPorMovPagamentoOrigemCredito(movPagamentoOrigemCredito);
        if (nfSeEmitidaVO != null){
            nfSeEmitidaVO.setNovoObj(false);
            MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
            movPagamentoVO.setCodigo(codigoMovPagamento);
            nfSeEmitidaVO.setMovPagamento(movPagamentoVO);
            incluir(nfSeEmitidaVO);
        }
    }

    private NFSeEmitidaVO consultarPorMovPagamentoOrigemCredito(Integer movPagamentoOrigemCredito) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * \n");
        sql.append("from nfseemitida \n");
        sql.append("where movPagamento in( \n");
        sql.append("                      select mov.codigo\n");
        sql.append("                      from movPagamento mov\n");
        sql.append("                      where mov.reciboPagamento in(select reciboPagamento from movPagamento where codigo = ").append (movPagamentoOrigemCredito).append(")) \n");
        sql.append(" and cheque is null and cartaoCredito is null \n");
        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        if (rs.next()) {
            return montarDados(rs, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }


    public void incluirNFSeParaChequeComposicao(String composicao, Integer codigoChequeComposicao) throws Exception {
        if ((composicao != null) && (!composicao.trim().equals(""))){
            String[] codigos = composicao.split(",");
            for (String codigo: codigos){
                Integer codigoChequeAntigo = Integer.parseInt(codigo);
                NFSeEmitidaVO nfSeEmitidaVO = consultaPorCheque(codigoChequeAntigo);
                if (nfSeEmitidaVO != null){
                    nfSeEmitidaVO.setNovoObj(false);
                    ChequeVO chequeVO = new ChequeVO();
                    chequeVO.setCodigo(codigoChequeComposicao);
                    nfSeEmitidaVO.setCheque(chequeVO);
                    incluir(nfSeEmitidaVO);
                }
            }
        }
    }


    public NFSeEmitidaVO consultarPorCartaoCredito(Integer codigoCartaoCredito)throws Exception{
        String sql = "SELECT * FROM nfseemitida WHERE cartaoCredito = " + codigoCartaoCredito;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;

    }

    public void atualizarCheque(Integer codAntigo, Integer codNovo) throws Exception {
        String sql = "UPDATE nfseemitida SET cheque  = ? WHERE cheque= ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, codNovo);
        sqlInserir.setInt(2, codAntigo);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarCheque(codAntigo, codNovo);
        nfSeEmitidaHistorico = null;
    }

    public void excluirPorCartaoCredito(List<CartaoCreditoVO> listaCartaoCredito)throws Exception{
        if ((listaCartaoCredito == null) || (listaCartaoCredito.size() <= 0)){
            return ;
        }
        StringBuilder codigos = new StringBuilder();
        for (CartaoCreditoVO cartaoCreditoVO: listaCartaoCredito){
            if (codigos.length() <= 0){
                codigos.append(cartaoCreditoVO.getCodigo());
            }else{
                codigos.append(",").append(cartaoCreditoVO.getCodigo());
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("delete from nfseemitida where cartaoCredito in(").append(codigos.toString()).append(")");
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    public void excluirPorCheque(List<ChequeVO> listaCheque)throws Exception{
        if ((listaCheque == null) || (listaCheque.size() <= 0)){
            return ;
        }
        StringBuilder codigos = new StringBuilder();
        for (ChequeVO chequeVO: listaCheque){
            if (codigos.length() <= 0){
                codigos.append(chequeVO.getCodigo());
            }else{
                codigos.append(",").append(chequeVO.getCodigo());
            }
        }
        StringBuilder sql = new StringBuilder();
        sql.append("delete from nfseemitida where cheque in(").append(codigos.toString()).append(")");
        Statement st = con.createStatement();
        st.execute(sql.toString());
    }

    public void atualizarMovPagamento(Integer codAntigo, Integer codNovo) throws Exception {
        String sql = "UPDATE nfseemitida SET movpagamento = ? WHERE movpagamento = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, codNovo);
        sqlInserir.setInt(2, codAntigo);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarMovPagamento(codAntigo, codNovo);
        nfSeEmitidaHistorico = null;
    }

    public NFSeEmitidaVO consultaPorRPS(Integer codigoRPS) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE rps = " + codigoRPS;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public String montarDescricaoExcluirNotaFiscal(NFSeEmitidaVO nfSeEmitidaVO) throws Exception {
        StringBuilder descricaoNota = new StringBuilder();
        descricaoNota.append("Código NFSeEmitida: ").append(nfSeEmitidaVO.getCodigo());
        if(!UteisValidacao.emptyNumber(nfSeEmitidaVO.getIdRps())) {
            descricaoNota.append("<br>ID_RPS: ").append(nfSeEmitidaVO.getIdRps());
        }
        //INFORMAÇÕES DO CHEQUE
        if (nfSeEmitidaVO.getCheque() != null && nfSeEmitidaVO.getCheque().getCodigo() != null && !nfSeEmitidaVO.getCheque().getCodigo().equals(0)) {
            Cheque chequeDAO = new Cheque(con);
            ChequeVO chequeVO = chequeDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getCheque().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            descricaoNota.append("<br><br>Código Cheque: ").append(chequeVO.getCodigo());
            descricaoNota.append("<br>Valor Cheque: ").append(Formatador.formatarValorMonetario(chequeVO.getValorTotal()));
            descricaoNota.append("<br>Dt. Compensação: ").append(Uteis.getData(chequeVO.getDataCompensacao()));
            descricaoNota.append("<br>MovPagamento: ").append(chequeVO.getMovPagamento());
            chequeDAO = null;
        }
        //INFORMAÇÕES DO CARTAO DE CREDITO
        if (nfSeEmitidaVO.getCartaoCredito() != null && !nfSeEmitidaVO.getCartaoCredito().getCodigo().equals(0)) {
            CartaoCredito cartaoCreditoDAO = new CartaoCredito(con);
            CartaoCreditoVO cartaoCreditoVO = cartaoCreditoDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getCartaoCredito().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            descricaoNota.append("<br><br>Código Cartão: ").append(cartaoCreditoVO.getCodigo());
            descricaoNota.append("<br>Valor Cartão: ").append(Formatador.formatarValorMonetario(cartaoCreditoVO.getValorTotal()));
            descricaoNota.append("<br>Dt. Compensação: ").append(Uteis.getData(cartaoCreditoVO.getDataCompensacao()));
            descricaoNota.append("<br>MovPagamento: ").append(cartaoCreditoVO.getMovpagamento().getCodigo());
            cartaoCreditoDAO = null;
        }
        //INFORMAÇÕES DO MOVPAGAMENTO
        if (nfSeEmitidaVO.getMovPagamento() != null && nfSeEmitidaVO.getMovPagamento().getCodigo() != null && !nfSeEmitidaVO.getMovPagamento().getCodigo().equals(0)) {
            MovPagamento movPagamentoDAO = new MovPagamento(con);
            MovPagamentoVO movPagamentoVO = movPagamentoDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getMovPagamento().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            descricaoNota.append("<br><br>Código MovPagamento: ").append(movPagamentoVO.getCodigo());
            descricaoNota.append("<br>Valor: ").append(Formatador.formatarValorMonetario(movPagamentoVO.getValorTotal()));
            descricaoNota.append("<br>Dt. Pagamento: ").append(Uteis.getData(movPagamentoVO.getDataPagamento()));
            movPagamentoDAO = null;
        }
        //INFORMAÇÕES DO RECIBO PAGAMENTO
        if (nfSeEmitidaVO.getRecibo() != null && !nfSeEmitidaVO.getRecibo().getCodigo().equals(0)) {
            ReciboPagamento reciboPagamentoDAO = new ReciboPagamento(con);
            ReciboPagamentoVO reciboPagamentoVO = reciboPagamentoDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getRecibo().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            descricaoNota.append("<br><br>Código Recibo: ").append(reciboPagamentoVO.getCodigo());
            descricaoNota.append("<br>Valor Recibo: ").append(Formatador.formatarValorMonetario(reciboPagamentoVO.getValorTotal()));
            descricaoNota.append("<br>Dt. Recibo: ").append(Uteis.getData(reciboPagamentoVO.getData()));
            descricaoNota.append("<br>Contrato: ").append(reciboPagamentoVO.getContrato().getCodigo());
            reciboPagamentoDAO = null;
        }
        //INFORMAÇÕES DO MOV PRODUTO
        if (nfSeEmitidaVO.getMovProduto() != null && nfSeEmitidaVO.getMovProduto().getCodigo() != null && !nfSeEmitidaVO.getMovProduto().getCodigo().equals(0)) {
            MovProduto movProdutoDAO = new MovProduto(con);
            MovProdutoVO movProdutoVO = movProdutoDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getMovProduto().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            descricaoNota.append("<br><br>Código MovProduto: ").append(movProdutoVO.getCodigo());
            descricaoNota.append("<br>Valor Total Produto: ").append(Formatador.formatarValorMonetario(movProdutoVO.getTotalFinal()));
            descricaoNota.append("<br>Mês Referência: ").append(movProdutoVO.getMesReferencia());
            descricaoNota.append("<br>Contrato: ").append(movProdutoVO.getContrato_Apresentar());
            descricaoNota.append("<br>Descrição: ").append(movProdutoVO.getDescricao());
            movProdutoDAO = null;
        }
        //INFORMAÇÕES DO CONTRATO
        if (nfSeEmitidaVO.getContrato() != null && !nfSeEmitidaVO.getContrato().getCodigo().equals(0)) {
            Contrato contratoDAO = new Contrato(con);
            ContratoVO contratoVO = contratoDAO.consultarPorChavePrimaria(nfSeEmitidaVO.getContrato().getCodigo(), Uteis.NIVELMONTARDADOS_ROBO);
            descricaoNota.append("<br><br>Código Contrato: ").append(contratoVO.getCodigo());
            descricaoNota.append("<br>Matricula: ").append(contratoVO.getMatricula_Apresentar());
            descricaoNota.append("<br>Nome Cliente: ").append(contratoVO.getNome_Apresentar());
            descricaoNota.append("<br>Plano: ").append(contratoVO.getPlano().getDescricao());
            contratoDAO = null;
        }
        //INFORMAÇÕES DA CONTA
        if (nfSeEmitidaVO.getMovConta() != null && nfSeEmitidaVO.getMovConta().getCodigo() != null && !nfSeEmitidaVO.getMovConta().getCodigo().equals(0)) {
            MovConta movContaDAO = new MovConta(con);
            MovContaVO movContaVO = movContaDAO.consultarPorCodigo(nfSeEmitidaVO.getMovConta().getCodigo(), Uteis.NIVELMONTARDADOS_PAGAMENTOS_CONJUNTO_RESUMIDOS);
            descricaoNota.append("<br><br>Código MovConta: ").append(movContaVO.getCodigo());
            descricaoNota.append("<br>Descrição Conta: ").append(movContaVO.getDescricao());
            descricaoNota.append("<br>Valor Conta: ").append(Formatador.formatarValorMonetario(movContaVO.getValor()));
            movContaDAO = null;
        }

        return descricaoNota.toString();
    }

    public List<NFSeEmitidaVO> consultaListaPorCartao(String composicaoCartao) throws Exception {
        List<NFSeEmitidaVO> notas = new ArrayList<NFSeEmitidaVO>();
        String sql = "SELECT * FROM nfseemitida WHERE cartaocredito in (" + composicaoCartao +")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    public List<NFSeEmitidaVO> consultaListaPorCheque(String composicaoCheque) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE cheque in (" + composicaoCheque +")";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    public List<NFSeEmitidaVO>  montarDadosConsulta (ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception{
        List<NFSeEmitidaVO> notas = new ArrayList<NFSeEmitidaVO>();
        while(tabelaResultado.next()) {
            NFSeEmitidaVO nota = montarDados(tabelaResultado, nivelMontarDados, con);
            notas.add(nota);
        }
        return notas;
    }

    public String consultarPorPessoaParaTela(Integer codigoPessoa, Integer limit) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select array_to_string(array(select rps from nfseemitida where rps is not null ");
        sql.append("and pessoa = ").append(codigoPessoa);
        sql.append(" order by rps desc ");
        if (!UteisValidacao.emptyNumber(limit)) {
            sql.append(" limit ").append(limit);
        }
        sql.append("), ',', '') as rps");
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sql.toString());
        if (rs.next()) {
            return rs.getString("rps");
        } else {
            return "";
        }
    }

    public NFSeEmitidaVO consultarPorChavePrimaria(Integer codigo) throws Exception {
        return consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }

    public NFSeEmitidaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE codigo = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return null;
    }

    public void atualizarDataReferenciaJsonEnviar(Date dataReferencia, String jsonEnviar, Integer codigo) throws Exception {
        String sql = "UPDATE nfseemitida SET dataReferencia = ?, jsonEnviar  = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataReferencia));
        sqlInserir.setString(2, jsonEnviar);
        sqlInserir.setInt(3, codigo);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarDataReferenciaJsonEnviar(dataReferencia, jsonEnviar, codigo);
        nfSeEmitidaHistorico = null;
    }

    public void atualizarJsonEnviarIdReferencia(NFSeEmitidaVO nfSeEmitidaVO) throws Exception {
        String sql = "UPDATE nfseemitida SET jsonEnviar  = ?, idReferencia  = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, nfSeEmitidaVO.getJsonEnviar());
        sqlInserir.setString(2, nfSeEmitidaVO.getIdReferencia());
        sqlInserir.setInt(3, nfSeEmitidaVO.getCodigo());
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarJsonEnviarIdReferencia(nfSeEmitidaVO);
        nfSeEmitidaHistorico = null;
    }

    public void atualizarSituacao(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer codigoNFSeEmitida) throws Exception {
        String sql = "UPDATE nfseemitida SET situacaoNotaFiscal = ? WHERE codigo  = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, codigoNFSeEmitida);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarSituacao(situacaoNotaFiscal, codigoNFSeEmitida);
        nfSeEmitidaHistorico = null;
    }

    public void atualizarMovConta(Integer codigoNFSeEmitida, Integer codigoMovConta) throws Exception {
        String sql = "UPDATE nfseemitida SET movconta = ? WHERE codigo  = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, codigoMovConta);
        sqlInserir.setInt(2, codigoNFSeEmitida);
        sqlInserir.execute();
    }

    public void atualizarSituacaoPorSequencialFamilia(SituacaoNotaFiscalEnum situacaoNotaFiscal, Integer sequencialFamilia) throws Exception {
        String sql = "UPDATE nfseemitida SET situacaoNotaFiscal = ? WHERE sequencialFamilia  = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, situacaoNotaFiscal.getCodigo());
        sqlInserir.setInt(2, sequencialFamilia);
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarSituacaoPorSequencialFamilia(situacaoNotaFiscal, sequencialFamilia);
        nfSeEmitidaHistorico = null;
    }

    public void atualizarDataEnvioSituacao(Date dataEnvio, SituacaoNotaFiscalEnum situacaoNotaFiscal, String codigos) throws Exception {
        String sql = "UPDATE nfseemitida SET dataenvio = ?, situacaoNotaFiscal  = ? WHERE codigo in ("+codigos+")";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(1, Uteis.getDataJDBCTimestamp(dataEnvio));
        sqlInserir.setInt(2, situacaoNotaFiscal.getCodigo());
        sqlInserir.execute();
        NFSeEmitidaHistorico nfSeEmitidaHistorico = new NFSeEmitidaHistorico(con);
        nfSeEmitidaHistorico.atualizarDataEnvioSituacao(dataEnvio, situacaoNotaFiscal, codigos);
        nfSeEmitidaHistorico = null;
    }

    public List<NFSeEmitidaVO> consultarNotasAguardandoEnvio() throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE enotas = false and coalesce(nrNotaManual, '') = '' and situacaoNotaFiscal = " + SituacaoNotaFiscalEnum.GERADA.getCodigo() + " order by codigo ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public boolean existeNFSePorProdutoCartaoChequeDinheiro(Integer codProduto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select codigo from nfseemitida where movpagamento is not null and movpagamento in (select codigo from movpagamento where produtospagos  like '%|").append(codProduto).append(",%') \n");
        sql.append("UNION \n");
        sql.append("select codigo from nfseemitida where cartaocredito is not null and cartaocredito in (select codigo from cartaocredito where produtospagos  like '%|").append(codProduto).append(",%') \n");
        sql.append("UNION \n");
        sql.append("select codigo from nfseemitida where cheque is not null and cheque in (select codigo from cheque where produtospagos  like '%|").append(codProduto).append(",%') \n");
        return existe(sql.toString(), getCon());
    }

    public NFSeEmitidaVO consultaPorProdutoComCartaoChequeEmitido(Integer codProduto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from nfseemitida where movpagamento is not null and movpagamento in (select codigo from movpagamento where produtospagos  like '%|").append(codProduto).append(",%') \n");
        sql.append("UNION \n");
        sql.append("select * from nfseemitida where cartaocredito is not null and cartaocredito in (select codigo from cartaocredito where produtospagos  like '%|").append(codProduto).append(",%') \n");
        sql.append("UNION \n");
        sql.append("select * from nfseemitida where cheque is not null and cheque in (select codigo from cheque where produtospagos  like '%|").append(codProduto).append(",%') \n");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public NFSeEmitidaVO consultaPorMovPagamentoComCartaoChequeEmitido(Integer codMovPagamento) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from nfseemitida where movpagamento is not null and movpagamento = ").append(codMovPagamento).append(" \n");
        sql.append("UNION \n");
        sql.append("select * from nfseemitida where cartaocredito is not null and cartaocredito in (select codigo from cartaocredito where movpagamento = ").append(codMovPagamento).append(") \n");
        sql.append("UNION \n");
        sql.append("select * from nfseemitida where cheque is not null and cheque in (select codigo from cheque where movpagamento = ").append(codMovPagamento).append(") \n");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public NFSeEmitidaVO consultaNFSePorReciboPagamentoProduto(Integer movProduto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from nfseemitida where recibopagamento is not null and recibopagamento in (select recibopagamento from movprodutoparcela  where movproduto  = ").append(movProduto).append( " )");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public NFSeEmitidaVO consultaNFSePorReciboPagamentoEProdCartaoChequeEmitido(Integer movProduto) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from nfseemitida where recibopagamento is not null and recibopagamento in (select recibopagamento from movprodutoparcela  where movproduto  = ").append(movProduto).append( " ) ");
        sql.append("OR ");
        sql.append("(movpagamento is not null and movpagamento in (select codigo from movpagamento where produtospagos like '%|").append(movProduto).append(",%')) ");
        sql.append("OR ");
        sql.append("(cartaocredito is not null and cartaocredito in (select codigo from cartaocredito where produtospagos like '%|").append(movProduto).append(",%')) ");
        sql.append("OR ");
        sql.append("(cheque is not null and cheque in (select codigo from cheque where produtospagos like '%|").append(movProduto).append(",%'))");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public boolean existeNFSeIdReferencia(String idReferencia) throws Exception {
//        return existe("SELECT codigo FROM nfseemitida WHERE idreferencia = '" + idReferencia + "'", con);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("codigo \n");
        sql.append("FROM nfseemitida \n");
        sql.append("WHERE \n");
        sql.append("(enotas = false and idreferencia ilike '").append(idReferencia).append("') \n");
        sql.append("or \n");
        sql.append("(enotas = true and idreferencia ilike '").append(idReferencia).append("\\_%') \n");
        return existe(sql.toString(), con);
    }

    public void atualizarResultadoEnvio(NFSeEmitidaVO obj) throws Exception {
        String sql = "UPDATE nfseemitida SET resultadoEnvio = ? WHERE codigo = ?";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setString(1, obj.getResultadoEnvio());
        sqlInserir.setInt(2, obj.getCodigo());
        sqlInserir.execute();
    }

    public List<NFSeEmitidaVO> consultarNotasAguardandoEnvioEnotas() throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE enotas = true and coalesce(nrNotaManual, '') = '' and coalesce(sequencialFamilia, 0) = 0 and situacaoNotaFiscal = " + SituacaoNotaFiscalEnum.GERADA.getCodigo() + " order by codigo ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public List<NFSeEmitidaVO> consultarNotasEnviadasEnotas() throws Exception {
        String sql = "SELECT nfseemitida.*, notafiscal.codigo notafiscal_codigo " +
                "FROM nfseemitida " +
                "         inner join notafiscal on notafiscal.nfseemitida = nfseemitida.codigo " +
                "WHERE nfseemitida.enotas = true " +
                "  and coalesce(nfseemitida.nrNotaManual, '') = '' " +
                "  and coalesce(nfseemitida.sequencialFamilia, 0) = 0 " +
                "  and nfseemitida.situacaoNotaFiscal = "+SituacaoNotaFiscalEnum.ENVIADA.getCodigo() +
                "  and notafiscal.statusnota = 'Gerada' " +
                "order by nfseemitida.codigo; ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        while (tabelaResultado.next()) {
            NFSeEmitidaVO nfSeEmitidaVO = montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
            nfSeEmitidaVO.getNotaFiscalVO().setCodigo(tabelaResultado.getInt("notafiscal_codigo"));
            notasEmitidas.add(nfSeEmitidaVO);
        }
        return notasEmitidas;
    }

    public List<NFSeEmitidaVO> consultarPorSequencialFamilia(Integer sequencialFamilia) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE sequencialFamilia = " + sequencialFamilia;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        List<NFSeEmitidaVO> notasEmitidas = new ArrayList<NFSeEmitidaVO>();
        while (tabelaResultado.next()) {
            notasEmitidas.add(montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con));
        }
        return notasEmitidas;
    }

    public NFSeEmitidaVO consultaPorCodigo(Integer codigo) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE codigo = " + codigo;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        }
        return null;
    }

    public NFSeEmitidaVO consultarPorMovConta(Integer codigoMovConta, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM nfseemitida WHERE movconta = " + codigoMovConta;
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql);
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return null;
    }
}
