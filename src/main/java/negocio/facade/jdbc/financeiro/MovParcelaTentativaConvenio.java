/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.financeiro;

import org.json.JSONArray;
import org.json.JSONObject;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.MovParcelaTentativaConvenioInterfaceFacade;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/*
 * <AUTHOR> <PERSON> 17/10/2017
 */
public class MovParcelaTentativaConvenio extends SuperEntidade implements MovParcelaTentativaConvenioInterfaceFacade {

    public MovParcelaTentativaConvenio() throws Exception {
        super();
    }

    public MovParcelaTentativaConvenio(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(MovParcelaTentativaConvenioVO obj) throws Exception {
        String sql = "INSERT INTO MovParcelaTentativaConvenio(movparcela, conveniocobranca, nrTentativaParcela) VALUES (?, ?, ?)";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(1, obj.getMovParcelaVO().getCodigo());
        resolveIntegerNull(sqlInserir, 2, obj.getConvenioCobrancaVO().getCodigo());
        sqlInserir.setInt(3, obj.getNrTentativaParcela());
        sqlInserir.execute();

        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void incluirNrTentativaParcela(MovParcelaVO movParcelaVO, ConvenioCobrancaVO convenioCobrancaVO) {
        try {
            if (movParcelaVO == null ||
                    UteisValidacao.emptyNumber(movParcelaVO.getCodigo())) {
                Uteis.logar(null, "### ERRO!! Não será incluido MovParcelaTentativaConvenioVO - MovParcela não informada!");
                return;
            }

            if (convenioCobrancaVO != null) {
                MovParcelaTentativaConvenioVO novo = new MovParcelaTentativaConvenioVO();
                novo.setMovParcelaVO(movParcelaVO);
                novo.setConvenioCobrancaVO(convenioCobrancaVO);
                novo.setNrTentativaParcela(movParcelaVO.getNrTentativas());
                incluir(novo);
            }
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao incluir MovParcelaTentativaConvenioVO: " + e.getMessage());
        }
    }

    public void alterar(MovParcelaTentativaConvenioVO obj) throws Exception {
        String sql = "UPDATE MovParcelaTentativaConvenio set movparcela = ?, conveniocobranca = ?, nrTentativaParcela = ? WHERE codigo = ?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(1, obj.getMovParcelaVO().getCodigo());
        sqlAlterar.setInt(2, obj.getConvenioCobrancaVO().getCodigo());
        sqlAlterar.setInt(3, obj.getMovParcelaVO().getNrTentativas());
        sqlAlterar.setInt(4, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(MovParcelaTentativaConvenioVO obj) throws Exception {
        String sql = "DELETE FROM MovParcelaTentativaConvenio WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public static List<MovParcelaTentativaConvenioVO> montarDadosConsulta(ResultSet tabelaResultado,
                                                    int nivelMontarDados, Connection con) throws Exception {
        List<MovParcelaTentativaConvenioVO> vetResultado = new ArrayList<MovParcelaTentativaConvenioVO>();
        while (tabelaResultado.next()) {
            MovParcelaTentativaConvenioVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }


    public static MovParcelaTentativaConvenioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        MovParcelaTentativaConvenioVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        return obj;
    }

    public static MovParcelaTentativaConvenioVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        MovParcelaTentativaConvenioVO obj = new MovParcelaTentativaConvenioVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getMovParcelaVO().setCodigo(dadosSQL.getInt("movparcela"));
        obj.getConvenioCobrancaVO().setCodigo(dadosSQL.getInt("conveniocobranca"));
        obj.setNrTentativaParcela(dadosSQL.getInt("nrTentativaParcela"));
        return obj;
    }

    public MovParcelaTentativaConvenioVO consultarPorMovParcelaConvenioCobranca(MovParcelaVO movParcelaVO, ConvenioCobrancaVO convenioCobrancaVO, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM MovParcelaTentativaConvenio WHERE movparcela = ? AND convenioCobranca = ? ";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, movParcelaVO.getCodigo());
        sqlConsultar.setInt(2, convenioCobrancaVO.getCodigo());
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (tabelaResultado.next()) {
            return montarDados(tabelaResultado, nivelMontarDados, con);
        }
        return null;
    }

    public Integer contarNrTentativaParcelaConvenioCobranca(Integer convenioCobranca, Integer movParcela) throws Exception {

        String sql = "SELECT COUNT(*) as qtd FROM MovParcelaTentativaConvenio WHERE conveniocobranca = ? AND movparcela = ? ";
        PreparedStatement stm = con.prepareStatement(sql);
        stm.setInt(1, convenioCobranca);
        stm.setInt(2, movParcela);
        ResultSet rs = stm.executeQuery();
        if (rs.next()) {
            return rs.getInt("qtd");
}
        return 0;
    }


    public void totalizadorPorConvenio(TotalizadorRemessaTO totalizador, Integer codigoEmpresa, Date dataInicio, Date dataFim, String convenios) throws Exception {

        String sql = sqlTotalizadorPorConvenio(codigoEmpresa, dataInicio, dataFim, convenios);

        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            totalizador.setQuantidade(rs.getInt("qtd"));
            totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
            totalizador.setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorCodigoEmpresa(rs.getInt("conveniocobranca"), codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            totalizador.setLabel(totalizador.getConvenioCobrancaVO().getDescricao());
        }
        totalizadorPorConvenioPorNrTentativa(totalizador, codigoEmpresa, dataInicio, dataFim, convenios);
    }

    private void totalizadorPorConvenioPorNrTentativa(TotalizadorRemessaTO totalizadorPai, Integer codigoEmpresa, Date dataInicio, Date dataFim, String convenios) throws Exception {

        String sql = sqlTotalizadorPorNrTentativa(codigoEmpresa, dataInicio, dataFim, convenios);

        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            TotalizadorRemessaTO totalizador = new TotalizadorRemessaTO();
            totalizador.setConvenioCobrancaVO(getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(rs.getInt("conveniocobranca"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            totalizador.setLabel(rs.getInt("nrTentativaConv") + "º Tentativa");
            totalizador.setQuantidade(rs.getInt("qtd"));
            totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
            totalizador.calcularPorcentagem(totalizadorPai.getValor());
            totalizadorPai.getListaFilhoTotalizadorRemessaTO().add(totalizador);
        }
    }

    private String sqlTotalizadorPorConvenio(Integer codigoEmpresa,
                                             Date dataInicio, Date dataFim, String convenios) throws Exception {

        StringBuilder sql = new StringBuilder("");

        sql.append("select \n");
        sql.append("count(codigo) as qtd, \n");
        sql.append("sum(valor::numeric) as valor, \n");
        sql.append("conveniocobranca \n");
        sql.append("from( \n");
        sql.append("select \n");
        sql.append("distinct(m.codigo), \n");
        sql.append("m.valorparcela as valor, \n");
        sql.append("infos.conveniocobranca \n");
        sql.append("from movparcela m \n");
        sql.append("left join remessaitem ri on ri.movparcela = m.codigo \n");
        sql.append("left join remessa re on re.codigo = ri.remessa \n");
        sql.append("left join transacaomovparcela tm on tm.movparcela = m.codigo \n");
        sql.append("left join transacao t on t.codigo = tm.transacao \n");
        sql.append("left join pagamentomovparcela pmp on pmp.movparcela = m.codigo \n");
        sql.append("left join movpagamento mp on mp.codigo = pmp.movpagamento \n");
        sql.append("left join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("LEFT JOIN (select movparcela, conveniocobranca, count(codigo) as nrTentativaConv from movparcelatentativaconvenio group by 1,2) as infos ON infos.movparcela = m.codigo \n");
        sql.append("where 1 = 1 \n");
        sql.append("and m.situacao = 'PG' \n");
        sql.append("and ((fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) or t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sql.append("and (re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
        sql.append("or \n");
        sql.append("t.dataprocessamento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("') \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("and (t.empresa = ").append(codigoEmpresa).append(" OR re.empresa = ").append(codigoEmpresa).append(") \n");
        }

        if (!UteisValidacao.emptyString(convenios)) {
            sql.append("AND infos.conveniocobranca IN ( ").append(convenios).append(" ) \n");
        }

        sql.append("group by 1,2,3 \n");
        sql.append(") as foo \n");
        sql.append("group by 3 \n");

        return sql.toString();
    }

    private String sqlTotalizadorPorNrTentativa(Integer codigoEmpresa,
                                                Date dataInicio, Date dataFim, String convenios) throws Exception {

        StringBuilder sql = new StringBuilder("");

        sql.append("select \n");
        sql.append("count(codigo) as qtd, \n");
        sql.append("sum(valor::numeric) as valor, \n");
        sql.append("conveniocobranca, \n");
        sql.append("nrTentativaConv \n");
        sql.append("from( \n");
        sql.append("select \n");
        sql.append("distinct(m.codigo), \n");
        sql.append("m.valorparcela as valor, \n");
        sql.append("infos.conveniocobranca, \n");
        sql.append("infos.nrTentativaConv \n");
        sql.append("from movparcela m \n");
        sql.append("left join remessaitem ri on ri.movparcela = m.codigo \n");
        sql.append("left join remessa re on re.codigo = ri.remessa \n");
        sql.append("left join transacaomovparcela tm on tm.movparcela = m.codigo \n");
        sql.append("left join transacao t on t.codigo = tm.transacao \n");
        sql.append("left join pagamentomovparcela pmp on pmp.movparcela = m.codigo \n");
        sql.append("left join movpagamento mp on mp.codigo = pmp.movpagamento \n");
        sql.append("left join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("LEFT JOIN (select movparcela, conveniocobranca, count(codigo) as nrTentativaConv from movparcelatentativaconvenio group by 1,2) as infos ON infos.movparcela = m.codigo \n");
        sql.append("where 1 = 1 \n");
        sql.append("and m.situacao = 'PG' \n");
        sql.append("and ((fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) or t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");

//        sql.append("and (re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
//        sql.append("or \n");
//        sql.append("t.dataprocessamento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("') \n");

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("and (t.empresa = ").append(codigoEmpresa).append(" OR re.empresa = ").append(codigoEmpresa).append(") \n");
        }

        if (!UteisValidacao.emptyString(convenios)) {
            sql.append("AND infos.conveniocobranca IN ( ").append(convenios).append(" ) \n");
        }

        sql.append("group by 1,2,3,4 \n");
        sql.append("order by 4 \n");
        sql.append(") as foo \n");
        sql.append("group by 3,4 \n");
        sql.append("order by 3,4 \n");

        return sql.toString();
    }

    public String graficoTotalizadorPorConvenio(Integer codigoEmpresa, Date dataInicio, Date dataFim, List<Integer> convenios) throws Exception {

        JSONArray dadosBI = new JSONArray();
        JSONArray legendaBI = new JSONArray();



        for (String tenta : new String[]{"1", "2", "3","4"}) {
            JSONObject jsonBI = new JSONObject();

            String condicaoNrTentativaConv;
            if (tenta.equals("4")) {
                jsonBI.put("tentativa", "Outras Tentativas");
                condicaoNrTentativaConv = " >= " + tenta;
            } else {
                jsonBI.put("tentativa", tenta + "º Tentativa");
                condicaoNrTentativaConv = " = " + tenta;
            }

            char letra = 'A';
            for (Integer cod : convenios) {
                ConvenioCobrancaVO convenioCobrancaVO = getFacade().getConvenioCobranca().consultarPorCodigoSemInfoEmpresa(cod, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                String identificacao = "convenioCod" + letra;
                letra++;


                String somenteConvenio = "and ((fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) or t.situacao = " + SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId() + ") ";
                String situacaoParcela = "= 'PG'";
                boolean somenteMes =  true;
                boolean somenteForaMes = false;

                String sql = sqlBITotalizadorParcelasCobranca(codigoEmpresa, dataInicio, dataFim, cod.toString(), condicaoNrTentativaConv, situacaoParcela, somenteConvenio, "", somenteMes, somenteForaMes);

                PreparedStatement stm = con.prepareStatement(sql);
                ResultSet rs = stm.executeQuery();
                Double valor = 0.0;
                if (rs.next()) {
                    valor = Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor"));
                    jsonBI.put(identificacao, valor);
                } else {
                    jsonBI.put(identificacao, valor);
                }

                if (tenta.equals("1")) {
                    JSONObject jsonLegend = new JSONObject();
                    jsonLegend.put("balloonText", "<b>[[title]]</b><br><span style='font-size:14px'>[[category]]: <b>R$ [[value]]</b></span>");
                    jsonLegend.put("fillAlphas", 0.8);
                    jsonLegend.put("labelText", "[[value]]");
                    jsonLegend.put("lineAlpha", 0.3);
                    jsonLegend.put("title", convenioCobrancaVO.getDescricao());
                    jsonLegend.put("type", "column");
                    jsonLegend.put("valueField", identificacao);
                    legendaBI.put(jsonLegend);
                }
            }
            dadosBI.put(jsonBI);
        }
        return dadosBI.toString() + "#####" + legendaBI.toString();
    }

    private String sqlBITotalizadorParcelasCobranca(Integer codigoEmpresa,
                                                       Date dataInicio, Date dataFim, String convenios, String condicaoNrTentativaConv, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                                       boolean somenteMes, boolean somenteForaMes) throws Exception {

        StringBuilder sql = new StringBuilder("");

        sql.append("select \n");
        sql.append("count(codigo) as qtd, \n");
        if (!UteisValidacao.emptyString(condicaoNrTentativaConv)) {
            sql.append("sum(valor::numeric) as valor, \n");
            sql.append("conveniocobranca \n");
        } else {
            sql.append("sum(valor::numeric) as valor \n");
        }
        sql.append("from( \n");
        sql.append("select \n");
        sql.append("distinct(m.codigo), \n");
        sql.append("m.valorparcela as valor, \n");
        sql.append("infos.conveniocobranca, \n");
        sql.append("infos.nrTentativaConv \n");
        sql.append("from movparcela m \n");
        sql.append("left join remessaitem ri on ri.movparcela = m.codigo \n");
        sql.append("left join remessa re on re.codigo = ri.remessa \n");
        sql.append("left join transacaomovparcela tm on tm.movparcela = m.codigo \n");
        sql.append("left join transacao t on t.codigo = tm.transacao \n");
        sql.append("left join pagamentomovparcela pmp on pmp.movparcela = m.codigo \n");
        sql.append("left join movpagamento mp on mp.codigo = pmp.movpagamento \n");
        sql.append("left join formapagamento fp on fp.codigo = mp.formapagamento \n");
        sql.append("LEFT JOIN (select movparcela, conveniocobranca, count(codigo) as nrTentativaConv from movparcelatentativaconvenio group by 1,2) as infos ON infos.movparcela = m.codigo \n");
        sql.append("where 1 = 1 \n");
//        sql.append("and ((fp.conveniocobranca is not null OR fp.defaultrecorrencia = true) or t.situacao = ").append(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO.getId()).append(") \n");
        sql.append("and (re.dataregistro::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
        sql.append("or \n");
        sql.append("t.dataprocessamento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("') \n");

        if (!((somenteForaMes && somenteMes) || (!somenteMes && !somenteForaMes))) {
            if (somenteMes) {
                sql.append("AND m.datavencimento::date BETWEEN '").append(Uteis.getData(dataInicio)).append("' AND '").append(Uteis.getData(dataFim)).append("' \n");
            }
            if (somenteForaMes) {
                sql.append("AND m.datavencimento::date < '").append(Uteis.getData(dataInicio)).append("' \n");
            }
        }

        if (!UteisValidacao.emptyString(situacaoParcela)) {
            sql.append("AND m.situacao ").append(situacaoParcela).append(" \n");
        }

        if (!UteisValidacao.emptyString(situacaoRemessa)) {
            sql.append(situacaoRemessa).append(" \n");
        }

        if (!UteisValidacao.emptyString(somenteConvenio)) {
            sql.append(somenteConvenio).append(" \n");
        }

        if (!UteisValidacao.emptyNumber(codigoEmpresa)) {
            sql.append("and (t.empresa = ").append(codigoEmpresa).append(" OR re.empresa = ").append(codigoEmpresa).append(") \n");
        }

        if (!UteisValidacao.emptyString(convenios)) {
            sql.append("AND infos.conveniocobranca IN ( ").append(convenios).append(" ) \n");
        }

        if (!UteisValidacao.emptyString(condicaoNrTentativaConv)) {
            sql.append("AND infos.nrTentativaConv ").append(condicaoNrTentativaConv).append(" \n");
        }

        sql.append("group by 1,2,3,4 \n");
        sql.append("order by 4 \n");
        sql.append(") as foo \n");
        if (!UteisValidacao.emptyString(condicaoNrTentativaConv)) {
            sql.append("group by 3 \n");
            sql.append("order by 3 \n");
        }

        return sql.toString();
    }

    public void totalizadorBIResultadoDCC(TotalizadorRemessaTO totalizador, Integer codigoEmpresa,
                                          Date dataInicio, Date dataFim, List<Integer> convenios, String condicaoNrTentativaConv, String situacaoParcela, String somenteConvenio, String situacaoRemessa,
                                          boolean somenteMes, boolean somenteForaMes) throws Exception {

        String convenio = Uteis.montarListaIN(convenios);

        String sql = sqlBITotalizadorParcelasCobranca(codigoEmpresa, dataInicio, dataFim, convenio, condicaoNrTentativaConv, situacaoParcela, somenteConvenio, situacaoRemessa, somenteMes, somenteForaMes);

        PreparedStatement stm = con.prepareStatement(sql);
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            totalizador.setQuantidade(rs.getInt("qtd"));
            totalizador.setValor(Uteis.arredondarForcando2CasasDecimais(rs.getDouble("valor")));
        }
    }
}
