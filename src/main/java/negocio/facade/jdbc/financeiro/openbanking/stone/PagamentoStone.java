package negocio.facade.jdbc.financeiro.openbanking.stone;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import negocio.comuns.financeiro.openbanking.stone.PagamentoStoneVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.financeiro.openbanking.stone.PagamentoStoneInterfaceFacade;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;


public class PagamentoStone extends SuperEntidade implements PagamentoStoneInterfaceFacade {

    public PagamentoStone() throws Exception {
        super();
    }

    public PagamentoStone(Connection con) throws Exception {
        super(con);
    }


    @Override
    public void incluir(PagamentoStoneVO obj) throws Exception {
        PagamentoStoneVO.validarDados(obj);
        String insert = "insert into pagamentoStone (id, empresa, idempotency_key, account_id, created_at, created_by, amount, approved_at, barcode, movConta, eventIdWebhook)" +
                "values (?,?,?,?,?,?,?,?,?,?,?)";
        PreparedStatement stm = con.prepareStatement(insert);
        stm.setString(1, obj.getId());
        stm.setInt(2, obj.getEmpresa());
        stm.setString(3, obj.getIdempotency_key());
        stm.setString(4, obj.getAccount_id());
        stm.setString(5, obj.getCreated_at());
        stm.setString(6, obj.getCreated_by());
        stm.setInt(7, obj.getAmount());
        stm.setString(8, obj.getApproved_at());
        stm.setString(9, obj.getBarcode());
        stm.setInt(10, obj.getMovConta());
        stm.setString(11, obj.getEventIdWebhook());
        stm.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(PagamentoStoneVO obj) throws Exception {
        PagamentoStoneVO.validarDados(obj);
        String update = "update pagamentoStone set id = ?, empresa = ?, idempotency_key = ?, account_id = ?," +
                " created_at = ?, created_by = ?, amount = ?, approved_at = ?, barcode = ?, movConta = ?, eventIdWebhook = ? where codigo = ?";
        PreparedStatement stm = con.prepareStatement(update);
        stm.setString(1, obj.getId());
        stm.setInt(2, obj.getEmpresa());
        stm.setString(3, obj.getIdempotency_key());
        stm.setString(4, obj.getAccount_id());
        stm.setString(5, obj.getCreated_at());
        stm.setString(6, obj.getCreated_by());
        stm.setInt(7, obj.getAmount());
        stm.setString(8, obj.getApproved_at());
        stm.setString(9, obj.getBarcode());
        stm.setInt(10, obj.getMovConta());
        stm.setString(11, obj.getEventIdWebhook());
        stm.setInt(12, obj.getCodigo());
        stm.execute();
    }

    public PagamentoStoneVO montarDados(ResultSet rs) throws Exception{
        PagamentoStoneVO pagamentoStoneVO = new PagamentoStoneVO();
        pagamentoStoneVO.setCodigo(rs.getInt("codigo"));
        pagamentoStoneVO.setId(rs.getString("id"));
        pagamentoStoneVO.setEmpresa(rs.getInt("empresa"));
        pagamentoStoneVO.setIdempotency_key(rs.getString("idempotency_key"));
        pagamentoStoneVO.setAccount_id(rs.getString("account_id"));
        pagamentoStoneVO.setCreated_at(rs.getString("created_at"));
        pagamentoStoneVO.setCreated_by(rs.getString("created_by"));
        pagamentoStoneVO.setAmount(rs.getInt("amount"));
        pagamentoStoneVO.setApproved_at(rs.getString("approved_at"));
        pagamentoStoneVO.setBarcode(rs.getString("barcode"));
        pagamentoStoneVO.setMovConta(rs.getInt("movConta"));
        pagamentoStoneVO.setEventIdWebhook(rs.getString("eventIdWebhook"));
        return pagamentoStoneVO;
    }

    public PagamentoStoneVO montarPagamento(String response, Integer movContaZW, Integer empresaZW) throws Exception {
        PagamentoStoneVO pagamentoStone = JSONMapper.getObject(new JSONObject(response), PagamentoStoneVO.class);
        pagamentoStone.setMovConta(movContaZW);
        pagamentoStone.setEmpresa(empresaZW);
        return pagamentoStone;
    }

    public PagamentoStoneVO buscarPagamentoStoneById(String id, Integer empresa){
        StringBuilder sql = new StringBuilder();
        sql.append("select * from pagamentostone  where id = ? and empresa = ?");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setString(1, id);
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado);
                }
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    public PagamentoStoneVO buscarPagamentoStoneByMovConta(Integer movConta, Integer empresa){
        StringBuilder sql = new StringBuilder();
        sql.append("select * from pagamentostone  where movconta = ? and empresa = ?");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            sqlConsultar.setInt(1, movConta);
            sqlConsultar.setInt(2, empresa);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (tabelaResultado.next()) {
                    return montarDados(tabelaResultado);
                }
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }
}
