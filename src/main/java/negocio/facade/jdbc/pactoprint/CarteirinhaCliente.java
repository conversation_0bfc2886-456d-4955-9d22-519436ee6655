package negocio.facade.jdbc.pactoprint;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoCategoriaClubeEnum;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.pactoprint.CarteirinhaClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Familiar;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.interfaces.pactoprint.CarteirinhaClienteInterfaceFacade;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

public class CarteirinhaCliente extends SuperEntidade implements CarteirinhaClienteInterfaceFacade {


    public CarteirinhaCliente() throws Exception {
        super();
    }

    public CarteirinhaCliente(Connection conexao) throws Exception {
        super(conexao);
    }

    public CarteirinhaClienteVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        CarteirinhaClienteVO carteirinhaClienteVO = new CarteirinhaClienteVO();
        carteirinhaClienteVO.setCodigo(dadosSQL.getInt("codigo"));
        carteirinhaClienteVO.setClienteVO(new ClienteVO());
        carteirinhaClienteVO.getClienteVO().setCodigo(dadosSQL.getInt("cliente"));
        carteirinhaClienteVO.setDataLancamento(dadosSQL.getTimestamp("dataLancamento"));
        carteirinhaClienteVO.setDataImpressao(dadosSQL.getTimestamp("dataImpressao"));
        carteirinhaClienteVO.setValidadeAnterior(dadosSQL.getDate("validadeAnterior"));
        carteirinhaClienteVO.setVia(dadosSQL.getInt("via"));
        carteirinhaClienteVO.setUsuarioVO(new UsuarioVO());
        carteirinhaClienteVO.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
        carteirinhaClienteVO.setCodigoAcesso(dadosSQL.getString("codigoAcesso"));
        carteirinhaClienteVO.setMovParcelaVO(new MovParcelaVO());
        carteirinhaClienteVO.getMovParcelaVO().setCodigo(dadosSQL.getInt("movParcela"));
        carteirinhaClienteVO.setColaboradorVO(new ColaboradorVO());
        carteirinhaClienteVO.getColaboradorVO().setCodigo(dadosSQL.getInt("colaborador"));
        carteirinhaClienteVO.setOrigemAtualizacaoCodigoAcesso(dadosSQL.getInt("origemAtualizacaoCodigoAcesso"));
        carteirinhaClienteVO.setDataValidadeCarteirinha(dadosSQL.getDate("dataValidadeCarteirinha"));

        return carteirinhaClienteVO;
    }

    public CarteirinhaClienteVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CarteirinhaClienteVO carteirinhaClienteVO = montarDadosBasico(dadosSQL);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            if (carteirinhaClienteVO.getClienteVO().getCodigo() > 0)
                carteirinhaClienteVO.setClienteVO(getFacade().getCliente().consultarPorCodigo(carteirinhaClienteVO.getClienteVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (carteirinhaClienteVO.getColaboradorVO().getCodigo() > 0)
                carteirinhaClienteVO.setColaboradorVO(getFacade().getColaborador().consultarPorChavePrimaria(carteirinhaClienteVO.getColaboradorVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            carteirinhaClienteVO.setUsuarioVO(getFacade().getUsuario().consultarPorCodigo(carteirinhaClienteVO.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        }

        return carteirinhaClienteVO;
    }


    private List<CarteirinhaClienteVO> consultarPorCliente(Integer codigoCliente, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * ");
        sql.append(" from carteirinhaCliente ");
        sql.append(" where cliente = ").append(codigoCliente);
        sql.append(" order by dataLancamento desc ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet dados = pst.executeQuery();
        return montarDadosConsulta(dados, nivelMontarDados);

    }

    public List<CarteirinhaClienteVO> consultarCarteirinhas(ClienteVO clienteVO, int nivelMontarDados) throws Exception {

        if ((clienteVO.getCategoria().getTipoCategoriaClubeEnum() == TipoCategoriaClubeEnum.ALUNO) ||
                (clienteVO.getCategoria().getTipoCategoriaClubeEnum() == TipoCategoriaClubeEnum.ATLETA) ||
                (clienteVO.getCategoria().getTipoCategoriaClubeEnum() == TipoCategoriaClubeEnum.ACOMPANHANTE)) {
            return consultarPorCliente(clienteVO.getCodigo(), nivelMontarDados);
        } else {
            StringBuilder sql = new StringBuilder();
            sql.append("select cart.* ");
            sql.append(" from carteirinhaCliente cart ");
            sql.append(" inner join clienteTitularDependente ct on ct.cliente = cart.cliente");
            sql.append(" where ct.clienteTitular = ").append("(select clienteTitular from clienteTitularDependente where cliente = ").append(clienteVO.getCodigo()).append(" )");
            sql.append(" order by dataLancamento desc ");
            PreparedStatement pst = con.prepareStatement(sql.toString());
            ResultSet rs = pst.executeQuery();
            List<CarteirinhaClienteVO> lista = montarDadosConsulta(rs, nivelMontarDados);
            if (lista.size() <= 0) {
                return consultarPorCliente(clienteVO.getCodigo(), nivelMontarDados);
            }
            return lista;
        }

    }

    public boolean existeCarteirinhaPendenteDeImpressao(Integer codigoCliente) throws Exception {
        String sql = "select * from carteirinhaCliente \n" +
                "where cliente = " + codigoCliente + " \n" +
                "and dataImpressao is null \n";
        try (ResultSet rs = criarConsulta(sql, con)) {
            return rs.next();
        }
    }

    public boolean possuiCarteirinha(Integer codigoCliente) throws Exception {
        String sql = "select codigo from carteirinhaCliente \n" +
                "where cliente =  " + codigoCliente + "\n" +
                "and dataImpressao is not null";
        try (ResultSet rs = criarConsulta(sql, con)) {
            return rs.next();
        }
    }

    public List<CarteirinhaClienteVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados) throws Exception {
        List<CarteirinhaClienteVO> lista = new ArrayList<CarteirinhaClienteVO>();
        while (rs.next()) {
            lista.add(montarDados(rs, nivelMontarDados));
        }
        return lista;
    }

    public void atualizarImpressaoCarteirinha(Integer codigoCarteirinha) throws Exception {
        con.setAutoCommit(false);
        try {
            String sql = "update carteirinhaCliente set dataImpressao = current_timestamp where codigo = ?";
            PreparedStatement pst = con.prepareStatement(sql);
            pst.setInt(1, codigoCarteirinha);
            pst.execute();
            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void atualizarDataValidade(Integer codigoCarteirinha) {
        String sql = "select cart.cliente, e.validademesescarteirinhasocio from carteirinhacliente cart\n" +
                "inner join cliente cli on cli.codigo = cart.cliente\n" +
                "inner join empresa e on e.codigo = cli.empresa\n" +
                "where cart.codigo = " + codigoCarteirinha;
        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                Integer validadeMeses = rs.getInt("validademesescarteirinhasocio");
                if (!UteisValidacao.emptyNumber(validadeMeses)) {
                    Date dataValidade = Calendario.getDataComHora(Calendario.somarMeses(Calendario.hoje(), validadeMeses), "23:59:59");
                    PreparedStatement pst = con.prepareStatement("update cliente set datavalidadecarteirinha = ? where codigo = ?");
                    pst.setDate(1, Uteis.getDataJDBC(dataValidade));
                    pst.setInt(2, rs.getInt("cliente"));
                    pst.execute();

                    pst = con.prepareStatement("update carteirinhacliente set datavalidadecarteirinha = ? where codigo = ?");
                    pst.setDate(1, Uteis.getDataJDBC(dataValidade));
                    pst.setInt(2, Integer.valueOf(codigoCarteirinha));
                    pst.execute();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void incluirNovaCarteirinha(CarteirinhaClienteVO obj) throws Exception {
        try {
            con.setAutoCommit(false);

            Integer via = obterUltimoNumeroViaCarteirinha(obj.getClienteVO().getCodigo());
            obj.setVia(via + 1);
            obj.setNumeroCarteirinha(String.format("%08d%03d", obj.getClienteVO().getCodigoMatricula(), obj.getVia()));
            obj.validarDados();

            String sql = "INSERT INTO carteirinhaCliente (cliente, usuario, codigoAcesso, via, movParcela, validadeAnterior, dataLancamento, colaborador, numeroCarteirinha, datavalidadecarteirinha, dataimpressao) \n " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
            PreparedStatement pst = con.prepareStatement(sql);
            if (!UteisValidacao.emptyNumber(obj.getClienteVO().getCodigo())) {
                pst.setInt(1, obj.getClienteVO().getCodigo());
            } else {
                pst.setNull(1, Types.NULL);
            }
            pst.setInt(2, obj.getUsuarioVO().getCodigo());
            pst.setString(3, obj.getCodigoAcesso());
            pst.setInt(4, obj.getVia());
            if (!UteisValidacao.emptyNumber(obj.getMovParcelaVO().getCodigo())) {
                pst.setInt(5, obj.getMovParcelaVO().getCodigo());
            } else {
                pst.setNull(5, Types.NULL);
            }
            if (obj.getClienteVO().getDataValidadeCarteirinha() != null) {
                pst.setDate(6, Uteis.getDataJDBC(obj.getClienteVO().getDataValidadeCarteirinha()));
            } else {
                pst.setNull(6, Types.NULL);
            }
            pst.setTimestamp(7, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            if (!UteisValidacao.emptyNumber(obj.getColaboradorVO().getCodigo())) {
                pst.setInt(8, obj.getColaboradorVO().getCodigo());
            } else {
                pst.setNull(8, Types.NULL);
            }
            pst.setString(9, obj.getNumeroCarteirinha());
            pst.setDate(10, Uteis.getDataJDBC(obj.getDataValidadeCarteirinha()));

            if (obj.getDataImpressao() != null) {
                pst.setTimestamp(11, Uteis.getDataJDBCTimestamp(obj.getDataImpressao()));
            } else {
                pst.setNull(11, Types.NULL);
            }

            pst.execute();
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(false);

            con.commit();
        } catch (Exception ex) {
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private Integer obterUltimoNumeroViaCarteirinha(Integer codigoCliente) {
        String sql = "select via from carteirinhacliente\n" +
                "where via > 0\n" +
                "and cliente = " + codigoCliente + "\n" +
                "order by via desc limit 1";

        try (ResultSet rs = criarConsulta(sql, con)) {
            if (rs.next()) {
                return rs.getInt("via");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 0;
    }

    public JSONObject consultarCarteirinhaParaImpressao(String url, String key, Integer codigoEmpresa, String dataHoraIni, String dataHoraFim, String tipoConsulta, String tipoCarteirinha) throws Exception {
        if (UteisValidacao.emptyNumber(codigoEmpresa)) {
            throw new ConsistirException("É necessário informar a empresa.");
        }

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        validacoesConfigEmpresa(empresaVO);

        JSONArray jsonArrayCarteirinhas = new JSONArray();

        String sql = "SELECT \n" +
                " cli.codigomatricula, \n" +
                " cli.matricula, \n" +
                " pes.nome as nomecliente, \n" +
                " pes.fotokey, \n" +
                " emp.nome as nomeEmpresa, \n" +
                " cart.* \n" +
                "FROM carteirinhacliente cart \n" +
                "INNER JOIN cliente cli ON cli.codigo = cart.cliente \n" +
                "INNER JOIN pessoa pes ON pes.codigo = cli.pessoa \n" +
                "INNER JOIN empresa emp ON emp.codigo = cli.empresa " +
                "WHERE cart.dataimpressao IS NULL \n" +
                "AND cart.datalancamento BETWEEN TO_TIMESTAMP('" + dataHoraIni + "',  'YYYY-MM-DD HH24:MI:SS') \n" +
                "AND TO_TIMESTAMP('" + dataHoraFim + "',  'YYYY-MM-DD HH24:MI:SS') \n" +
                "ORDER BY cart.dataLancamento DESC";


        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
        while (rs.next()) {
            CarteirinhaClienteVO obj = montarDadosBasico(rs);
            obj.getClienteVO().setPessoa(new PessoaVO());
            obj.getClienteVO().getPessoa().setNome(rs.getString("nomeCliente"));
            obj.getClienteVO().setCodigo(rs.getInt("cliente"));
            obj.getClienteVO().setCodigoMatricula(rs.getInt("codigoMatricula"));
            obj.getClienteVO().setMatricula(rs.getString("matricula"));
            obj.setUrlFoto(Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));

            Date dataValidade = Calendario.getDataComHora(Calendario.somarMeses(obj.getDataLancamento(), empresaVO.getValidadeMesesCarteirinhaSocio()), "23:59:59");
            obj.setDataValidadeCarteirinha(dataValidade);
            obj.setNumeroCarteirinha(rs.getString("numeroCarteirinha"));
            obj.setNomeEmpresa(obterNomeEmpresa(obj.getClienteVO().getCodigo()));

            obj.setPresidente(empresaVO.getPresidente());
            obj.setSuperintendente(empresaVO.getSuperintendente());

            if (!UteisValidacao.emptyString(obj.getNomeEmpresa())) {
                jsonArrayCarteirinhas.put(obj.getJson());
            }
        }

        JSONObject jsonRetorno = new JSONObject();
        jsonRetorno.put(CarteirinhaClienteVO.JSON_NAME, jsonArrayCarteirinhas);
        return jsonRetorno;
    }

    public JSONObject consultarCarteirinhaPorCodigoClienteFormatoJson(Integer codigoCliente, Integer codigoEmpresa, String key) throws Exception {
        JSONObject json = new JSONObject();

        String sql = "SELECT cc.*, p.nome, p.fotokey, c.matricula, " +
                "(SELECT e.presidente FROM empresa e WHERE e.codigo = " + codigoEmpresa + ") AS presidente, " +
                "(SELECT e.superintendente FROM empresa e WHERE e.codigo = " + codigoEmpresa + ") AS superintendente " +
                "FROM carteirinhacliente cc " +
                "JOIN cliente c ON cc.cliente = c.codigo " +
                "JOIN pessoa p ON c.pessoa = p.codigo " +
                "WHERE cc.cliente = " + codigoCliente +
                " ORDER BY cc.dataLancamento DESC " +
                " LIMIT 1";

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);

        if (rs.next()) {
            json.put("codigo", rs.getInt("codigo"));
            json.put("cliente", rs.getInt("cliente"));
            json.put("nomePessoa", rs.getString("nome"));
            json.put("urlFoto", Uteis.getPaintFotoDaNuvem(rs.getString("fotokey")));
            json.put("matricula", rs.getString("matricula"));
            json.put("codigoAcesso", rs.getString("codigoAcesso"));
            json.put("dataImpressao", rs.getTimestamp("dataImpressao"));
            json.put("dataLancamento", rs.getTimestamp("dataLancamento"));
            json.put("usuario", rs.getInt("usuario"));
            json.put("validadeAnterior", rs.getDate("validadeAnterior"));
            json.put("via", rs.getInt("via"));
            json.put("movParcela", rs.getInt("movParcela"));
            json.put("origemAtualizacaoCodigoAcesso", rs.getInt("origemAtualizacaoCodigoAcesso"));
            json.put("colaborador", rs.getInt("colaborador"));
            json.put("dataValidadeCarteirinha", rs.getDate("dataValidadeCarteirinha"));
            json.put("numeroCarteirinha", rs.getString("numeroCarteirinha"));
            json.put("presidente", rs.getString("presidente"));
            json.put("superintendente", rs.getString("superintendente"));
            json.put("nomeEmpresa", obterNomeEmpresa(codigoCliente));
            if (!UteisValidacao.emptyString(rs.getString("chaveArquivo"))) {
                byte[] bytes = MidiaService.getInstance().downloadObjectAsByteArray(key, MidiaEntidadeEnum.CARTEIRINHA_CLIENTE_PACTO_PRINT, rs.getString("codigo") + "_DOC", null);
                json.put("base64", Base64.getEncoder().encodeToString(bytes));
            }
        } else {
            json.put("erro", "carteirinha não encontrada");
        }

        return json;
    }


    public void atualizarChaveArquivo(String chavearquivo, Integer codigoCarteirinha) throws Exception {
        con.setAutoCommit(false);
        try {
            String sql = "update carteirinhaCliente set chavearquivo = ? where codigo = ?";
            PreparedStatement pst = con.prepareStatement(sql);
            pst.setString(1, chavearquivo);
            pst.setInt(2, codigoCarteirinha);
            pst.execute();

            con.commit();
        } catch (Exception e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }


    private void validacoesConfigEmpresa(EmpresaVO empresaVO) throws ConsistirException {
        if (empresaVO == null) {
            throw new ConsistirException("Empresa não encontrada.");
        }
        if (UteisValidacao.emptyNumber(empresaVO.getValidadeMesesCarteirinhaSocio())) {
            throw new ConsistirException("A empresa não possui validade de carteirinha configurada.");
        }
    }

    public String obterNomeEmpresa(Integer codigoCliente) throws Exception {
        Cliente clienteDAO = new Cliente(con);
        ClienteVO clienteVO = clienteDAO.consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(clienteVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        String nomeEmpresa = obterNomeEmpresa(clienteVO, empresaVO);
        if (!UteisValidacao.emptyString(nomeEmpresa)) {
            return nomeEmpresa;
        }

        // SESI - PA | Caso o cliente não tenha empresa, então usar a do titular ou responsavel
        Familiar familiarDAO = new Familiar(con);
        List<FamiliarVO> familiares = familiarDAO.consultarFamiliars(clienteVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        familiarDAO = null;
        for (FamiliarVO familiarVO : familiares) {
            Integer codigoClienteTitular = 0;
            String parentesco = familiarVO.getParentesco().getDescricao().toUpperCase();
            if (familiarVO.getCliente().equals(codigoCliente) && parentesco.contains("TITULAR")) {
                codigoClienteTitular = familiarVO.getFamiliar();
            }
            if (!familiarVO.getCliente().equals(codigoCliente) && parentesco.contains("DEPENDENTE")) {
                codigoClienteTitular = familiarVO.getCliente();
            }

            if (!UteisValidacao.emptyNumber(codigoClienteTitular)) {
                ClienteVO clienteFamiliar = clienteDAO.consultarPorCodigo(codigoClienteTitular, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                return obterNomeEmpresa(clienteFamiliar, empresaVO);
            }
        }
        if (clienteVO.getPessoaResponsavel() != null && !UteisValidacao.emptyNumber(clienteVO.getPessoaResponsavel().getCodigo())) {
            ClienteVO clienteResponsavel = clienteDAO.consultarPorCodigoPessoa(clienteVO.getPessoaResponsavel().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            return obterNomeEmpresa(clienteResponsavel, empresaVO);
        }
        clienteDAO = null;
        return "";
    }

    private String obterNomeEmpresa(ClienteVO clienteVO, EmpresaVO empresaVO) {
        if (empresaVO == null || !empresaVO.isHabilitarCadastroEmpresaSesi()) {
            if(!UteisValidacao.emptyString(clienteVO.getRazaoSocialEmpresaSesiCe())) {
                return clienteVO.getRazaoSocialEmpresaSesiCe();
            }
        }
        FornecedorVO empresaFornecedor = clienteVO.getEmpresaFornecedor();
        if (empresaFornecedor != null
                && !UteisValidacao.emptyNumber(empresaFornecedor.getCodigo())
                && Calendario.menorOuIgual(Calendario.hoje(), empresaFornecedor.getDataValidade())) {
            return empresaFornecedor.getNome_Apresentar();
        }
        return "";
    }

}
