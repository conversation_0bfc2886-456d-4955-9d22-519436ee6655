package negocio.facade.jdbc.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.estrutura.paginacao.PreparedStatementPersonalizado;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.PerfilAcessoVO;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.CacheControl;
import negocio.facade.jdbc.utilitarias.Conexao;
import servicos.propriedades.PropsService;

import javax.servlet.http.HttpSession;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Iterator;
import java.util.List;

/**
 * SuperClasse padrão para classes de persistência que encapsulam todas as
 * operações de manipulação dos dados. Responsável por implementar
 * características comuns a todas as operações das classes de persistência. Como
 * por exemplo: verificar a permissão do usuário para realizar uma determinada
 * operação. Possui implementações parciais das operações incluir, alterar,
 * consultar e excluir.
 */
public class SuperEntidade extends ControleAcesso {

    protected static String FOREIGN_KEY_VIOLATION = "23503";
    public static int ZERO = 0;
    
    public SuperEntidade() throws Exception {
        /**
         * Inicializa o nome da entidade com o próprio nome da classe.
         * Caso seja necessário sobrescrever este nome, deverá ser feito nas
         * descendentes.
         * 
         */
        setIdEntidade(this.getClass().getSimpleName());        
        prepararConexao();
    }

    public SuperEntidade(HttpSession session) throws Exception {
        super(session);
        prepararConexao();
        setIdEntidade(this.getClass().getSimpleName());
    }

    public SuperEntidade(Connection conexao) throws Exception {
        super(conexao);
        prepararConexao();
        setIdEntidade(this.getClass().getSimpleName());
    }

    /**
     * Gera automaticamente um valor não repetido e válido para uma chave
     * primária do tipo <code>Int</code>.
     *
     * @param nomeEntidade
     *            Nome da entidade para a qual se deseja gerar o valor da chave
     *            primária.
     * @param chavePrimaria
     *            Nome da chave primária (atributo) para o qual se deseja gerar
     *            o valor.
     * @return int Valor gerado para a chave primária.
     * @exception Exception
     *                Caso haja problemas de conexão.
     */
    public Integer gerarChavePrimariaInt(String nomeEntidade, String chavePrimaria) throws Exception {
        inicializar();
        int novoCodigo = 1;
        String sql = "select max(" + chavePrimaria + ") from " + nomeEntidade;
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        ResultSet resultado = sqlConsultar.executeQuery();
        if (resultado.next()) {
            int ultimoCodigo = resultado.getInt(1);
            novoCodigo = ultimoCodigo + 1;
        }
        return (new Integer(novoCodigo));
    }

    // /**
    // * Método responsável pelo registro do LOG de exclusão do objeto
    // selecionado.
    // * No método é gerado uma lista com objetos do tipo LogVO, onde cada
    // objeto desta lista é incluida na tabela Log
    // * no BD (Banco de Dados) com a sua operação.
    // * @param ObjetoVO Objeto a ser validado para geração o LOG.
    // * <AUTHOR> Cantarelli dos Santos
    // */
    // public static void registrarLogExclusaoObjetoVO(SuperVO ObjetoVO) throws
    // Exception {
    // try {
    // Log logFacade = new Log();
    // List lista = ObjetoVO.gerarLogExclusaoObjetoVO();
    // Iterator i = lista.iterator();
    // while (i.hasNext()) {
    // LogVO log = (LogVO) i.next();
    // logFacade.incluirSemCommit(log);
    // }
    // } catch (Exception e) {
    // throw e;
    // }
    // }

    public void validarPermissaoInclusao(String idEntidade) throws Exception {
        incluir(idEntidade);
    }

    /**
     * Operação padrão para realizar o INCLUIR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação INCLUIR.
     *
     * @param idEntidade
     *            Nome da entidade para a qual se deseja realizar a operação.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso a
     *                esta operação.
     */
    public void incluir(String idEntidade) throws Exception {
        // O povoador de banco inicial utiliza métodos via reflection para incluir/alterar registros, e neste caso não é necessário validar a permissão de incluir/alterar
        if (JSFUtilities.isJSFContext()){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null){
                super.incluir(idEntidade);
            }
        }
    }
    /* (non-Javadoc)
     * @see negocio.facade.jdbc.arquitetura.ControleAcesso#incluirObj(java.lang.String)
     */
    public void incluirObj(String idEntidade) throws Exception {
        super.incluirObj(idEntidade);
    }

    public void validarPermissaoAlteracao(String idEntidade) throws Exception{
        alterar(idEntidade);
    }

    /**
     * Operação padrão para realizar o ALTERAR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação ALTERAR.
     *
     * @param idEntidade
     *            Nome da entidade para a qual se deseja realizar a operação.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso a
     *                esta operação.
     */
    public void alterar(String idEntidade) throws Exception {
        // O povoador de banco inicial utiliza métodos via reflection para incluir/alterar registros, e neste caso não é necessário validar a permissão de incluir/alterar
        if (JSFUtilities.isJSFContext()){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null){
                super.alterar(idEntidade);
            }
        }
    }
    public void alterarObj(String idEntidade) throws Exception {
        // O povoador de banco inicial utiliza métodos via reflection para incluir/alterar registros, e neste caso não é necessário validar a permissão de incluir/alterar
        if (JSFUtilities.isJSFContext()){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null) {
                super.alterarObj(idEntidade);
            }
        }
    }

    public void validarPerissaoExclusao(String idEntidade) throws Exception {
        excluir(idEntidade);
    }

    /**
     * Operação padrão para realizar o EXCLUIR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação EXCLUIR.
     *
     * @param idEntidade
     *            Nome da entidade para a qual se deseja realizar a operação.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso a
     *                esta operação.
     */
    public void excluir(String idEntidade) throws Exception {
        // O povoador de banco inicial utiliza métodos via reflection para incluir/alterar registros, e neste caso não é necessário validar a permissão de incluir/alterar
        if (JSFUtilities.isJSFContext()){
            LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
            if (loginControle != null) {
                super.excluir(idEntidade);
            }
        }
    }
    
    public void excluirObj(String idEntidade) throws Exception {
        super.excluirObj(idEntidade);
    }

    /**
     * Operação padrão para realizar o CONSULTAR de dados de uma entidade no BD.
     * Verifica e inicializa (se necessário) a conexão com o BD. Verifica se o
     * usuário logado possui permissão de acesso a operação CONSULTAR.
     *
     * @param idEntidade
     *            Nome da entidade para a qual se deseja realizar a operação.
     * @exception Exception
     *                Caso haja problemas de conexão ou restrição de acesso a
     *                esta operação.
     */
    public void consultar(String idEntidade, boolean verificarAcesso) throws Exception {
    //  Retirada pois a mesma validação faz com que operações não vindas da telaCadastro não sejam realizadas,
    //  por falta de permissão e muitas vezes essa mesma exceção não chega a tela.
    //   super.consultar(idEntidade, verificarAcesso);
    }
    
    public void consultarObj(String idEntidade) throws Exception {
        super.consultarObj(idEntidade);
    }

    public Integer obterValorChavePrimariaCodigo() throws Exception {    
        return Conexao.obterUltimoCodigoGeradoTabela(con, this.getClass().getSimpleName());
    }

    public Integer obterValorChavePrimariaCodigoAHT() throws Exception {
        return Conexao.obterUltimoCodigoGeradoTabela(con, "alunohorarioturma");
    }

    public Integer obterValorChavePrimariaCodigo(String identificadorPK) throws Exception {
        return Conexao.obterUltimoCodigoGeradoTabela(con, this.getClass().getSimpleName(), identificadorPK);
    }


    /**
     * Metodo generico que obtem o numero total de registros de uma tabela
     *
     * Autor: Pedro Y. Saito
     * Criado em 23/12/2010
     *
     */
    public Integer numeroRegistrosTabela(PreparedStatementPersonalizado stm) throws Exception {
        inicializar();
        ResultSet tabelaResultado = stm.executeQuery();
        tabelaResultado.next();
        return (tabelaResultado.getInt(1));
    }

    public String getProp(final String prop) {
        return PropsService.getPropertyValue(prop);
    }
    
    public SuperVO obterFromCache(final Integer codigo) {
        return CacheControl.getFromCache(codigo, this.getClass());
    }
    
    public void putToCache(final SuperVO vo) {
        CacheControl.putToCache(vo, this.getClass());
    }

    public static void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg,
                                                String responsavel, String userOamd, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(responsavel);
            log.setUserOAMD(userOamd);
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    public static void registrarLogObjetoVO(List<LogVO> listaLog, int codPessoa, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            Iterator i = listaLog.iterator();
            while (i.hasNext()) {
                LogVO log = (LogVO) i.next();
                log.setPessoa(codPessoa);
                logDAO.incluirSemCommit(log);
            }
        } finally {
            logDAO = null;
        }
    }

    public static void registrarLogObjetoVO(LogVO logVO, int codPessoa, Connection con) throws Exception {
        Log logDAO;
        try {
            if (logVO != null) {
                logDAO = new Log(con);
                logVO.setPessoa(codPessoa);
                logDAO.incluirSemCommit(logVO);
            }
        } finally {
            logDAO = null;
        }
    }

    public boolean permissao(UsuarioVO usuarioLogadoVO, EmpresaVO empresaLogadoVO, String nomePermissao) {
        boolean permissao;
        try {
            permissao = getPerfilUsuarioLogado(usuarioLogadoVO, empresaLogadoVO).consultarObjPermissaoVO(nomePermissao) != null || usuarioLogadoVO.getAdministrador();
        } catch (Exception e) {
            e.printStackTrace();
            permissao = false;
        }
        return permissao;
    }

    private PerfilAcessoVO getPerfilUsuarioLogado(UsuarioVO usuarioLogadoVO, EmpresaVO empresaLogadoVO) throws Exception {
        Permissao permissaoDAO;
        UsuarioPerfilAcesso usuarioPerfilAcessoDAO;
        try {
            permissaoDAO = new Permissao(this.con);
            usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(this.con);

            if (UteisValidacao.emptyList(usuarioLogadoVO.getUsuarioPerfilAcessoVOs())) {
                usuarioLogadoVO.setUsuarioPerfilAcessoVOs(usuarioPerfilAcessoDAO.consultarUsuarioPerfilAcesso(usuarioLogadoVO.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                for (UsuarioPerfilAcessoVO usuarioPerfilAcesso : usuarioLogadoVO.getUsuarioPerfilAcessoVOs()) {
                    usuarioPerfilAcesso.getPerfilAcesso().setPermissaoVOs(permissaoDAO.consultarPermissaos(usuarioPerfilAcesso.getPerfilAcesso().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                }
            }
            for (UsuarioPerfilAcessoVO uPerfil : usuarioLogadoVO.getUsuarioPerfilAcessoVOs()) {
                if (uPerfil.getEmpresa().getCodigo().equals(empresaLogadoVO.getCodigo())) {
                    return uPerfil.getPerfilAcesso();
                }
            }
            return new PerfilAcessoVO();
        } finally {
            permissaoDAO = null;
            usuarioPerfilAcessoDAO = null;
        }
    }

    public void registrarLogObjetoVO(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa) throws Exception {
        registrarLogObjetoVOGeral(ObjetoVO, codigoCliente, nomeEntidade, codPessoa, false);
    }

    public void registrarLogObjetoVOGeral(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa, boolean validarValorNull) throws Exception {
        registrarLogObjetoVOGeralAlterandoResponsavel(ObjetoVO, codigoCliente, nomeEntidade, codPessoa, validarValorNull, "", null);
    }

    public void registrarLogObjetoVOGeralAlterandoResponsavel(SuperVO ObjetoVO, Integer codigoCliente, String nomeEntidade, int codPessoa,
                                                                     boolean validarValorNull, String responsavel, UsuarioVO usuarioVO) throws Exception{
        Log lodDAO;
        try {
            lodDAO = new Log(this.con);

            List lista = ObjetoVO.gerarLogAlteracaoObjetoVO(validarValorNull, usuarioVO);
            for (Object aLista : lista) {
                LogVO log = (LogVO) aLista;
                log.setChavePrimaria(codigoCliente.toString());
                log.setNomeEntidade(nomeEntidade);
                log.setPessoa(codPessoa);
                if (usuarioVO != null) {
                    log.setUsuarioVO(usuarioVO);
                    log.setResponsavelAlteracao(usuarioVO.getNome());
                    log.setUserOAMD(usuarioVO.getUserOamd());
                } else if (responsavel != null && !responsavel.equals("")) {
                    log.setResponsavelAlteracao(responsavel);
                }
                if (log.getNomeEntidade().equals("PRODUTO_ESTOQUE")) {
                    if (log.getNomeCampo().equals("situacao")) {
                        if (log.getValorCampoAnterior().equals("A") && log.getValorCampoAlterado().equals("C")) {
                            log.setOperacao("CANCELAMENTO PRODUTO");
                            log.setNomeEntidadeDescricao("PRODUTO");
                            log.setNomeCampo("*Situação Produto:");
                            log.setValorCampoAnterior("Ativo");
                            log.setValorCampoAlterado("Cancelado");
                        } else if (log.getValorCampoAnterior().equals("C") && log.getValorCampoAlterado().equals("A")) {
                            log.setOperacao("ALTERAÇÃO");
                            log.setNomeCampo("*Situação Produto:");
                            log.setValorCampoAnterior("Cancelado");
                            log.setValorCampoAlterado("Ativo");
                        }
                    }
                }
                if (log.getNomeEntidade().equals("BALANCO")) {
                    if (log.getNomeCampo().equals("cancelado")) {
                        log.setOperacao("CANCELAMENTO BALANÇO");
                        log.setNomeEntidadeDescricao("BALANÇO");
                        log.setNomeCampo("*Situação Balanço:");
                        log.setValorCampoAnterior("Ativo");
                        log.setValorCampoAlterado("Cancelado");
                        lodDAO.incluirSemCommit(log);
                    }
                }
                if (!log.getNomeEntidade().equals("BALANCO")) {
                    lodDAO.incluirSemCommit(log);
                }
            }
        } finally {
            lodDAO = null;
        }
    }

    protected static EmpresaVO getCachedEmpresa(int codigo, Connection con) throws Exception {
        EmpresaVO empresaVO = null;
        if (CacheControl.cacheEnabled(Empresa.class)) {
            empresaVO = (EmpresaVO) CacheControl.getFromCache(codigo, Empresa.class);
            Empresa empresa = new Empresa(con);
            if (empresaVO == null) {
                empresaVO = empresa.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
                CacheControl.putToCache(empresaVO, Empresa.class);
            }
        }
        if (empresaVO == null) {
            empresaVO = new EmpresaVO();
            empresaVO.setCodigo(codigo);
        }
        return empresaVO;
    }
}
