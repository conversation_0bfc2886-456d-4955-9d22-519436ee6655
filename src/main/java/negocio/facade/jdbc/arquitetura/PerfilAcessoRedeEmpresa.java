package negocio.facade.jdbc.arquitetura;

import negocio.comuns.arquitetura.PerfilAcessoRedeEmpresaVO;
import negocio.comuns.utilitarias.Uteis;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class PerfilAcessoRedeEmpresa extends SuperEntidade {

    public PerfilAcessoRedeEmpresa(Connection conexao) throws Exception {
        super(conexao);
    }

    public PerfilAcessoRedeEmpresa() throws Exception {
        super();
    }

    public void inserir(PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresaVO) throws SQLException {
        String sql = "INSERT INTO PerfilAcessoRedeEmpresa " +
                "(perfilacesso,chaveorigem,chavedestino,datacadastro, nomeUnidade, mensagemSituacao)" +
                "VALUES" +
                "(?,?,?,?,?,?)";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setInt(i++, perfilAcessoRedeEmpresaVO.getPerfilAcesso());
            preparedStatement.setString(i++, perfilAcessoRedeEmpresaVO.getChaveOrigem());
            if (isNotBlank(perfilAcessoRedeEmpresaVO.getChaveDestino())) {
                preparedStatement.setString(i++, perfilAcessoRedeEmpresaVO.getChaveDestino());
            } else {
                preparedStatement.setString(i++, "");
            }
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, perfilAcessoRedeEmpresaVO.getNomeUnidade());
            preparedStatement.setString(i++, perfilAcessoRedeEmpresaVO.getMensagemSituacao());
            preparedStatement.execute();
        }
    }

    public void alterarMensagemSituacao(Integer perfilAcesso, String chaveOrigem, String chaveDestino, String mensagemSituacao) throws SQLException {
        String sql = "UPDATE PerfilAcessoRedeEmpresa set " +
                "mensagemSituacao = ? " +
                "WHERE perfilacesso = ? AND chaveorigem = ? AND chavedestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, perfilAcesso);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public void limparDataAtualizacao(Integer perfilAcesso, String chaveOrigem, String chaveDestino, Integer empresaDestino) throws SQLException {
        String sql = "UPDATE PerfilAcessoRedeEmpresa set " +
                "dataatualizacao = ? " +
                "WHERE perfilacesso = ? AND chaveorigem = ? AND chavedestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, null);
            preparedStatement.setInt(i++, perfilAcesso);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public void alterarDataAtualizacao(Integer perfilAcesso, String chaveOrigem, String chaveDestino, String mensagemSituacao, Integer perfilAcessoReplicado) throws SQLException {
        String sql = "UPDATE PerfilAcessoRedeEmpresa set " +
                "dataatualizacao = ?, mensagemSituacao = ?, perfilacessoreplicado = ? " +
                "WHERE perfilacesso = ? AND chaveorigem = ? AND chaveDestino = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setTimestamp(i++, Uteis.getTimestamp());
            preparedStatement.setString(i++, mensagemSituacao);
            preparedStatement.setInt(i++, perfilAcessoReplicado);
            preparedStatement.setInt(i++, perfilAcesso);
            preparedStatement.setString(i++, chaveOrigem);
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.execute();
        }
    }

    public PerfilAcessoRedeEmpresaVO consultarPorChavePerfilAcesso(String chaveDestino, Integer perfilAcesso) throws SQLException {
        String sql = "SELECT * FROM PerfilAcessoRedeEmpresa " +
                "WHERE chaveDestino = ? AND perfilacesso = ?";

        try (PreparedStatement preparedStatement = con.prepareStatement(sql)) {
            int i = 1;
            preparedStatement.setString(i++, chaveDestino);
            preparedStatement.setInt(i++, perfilAcesso);
            try (ResultSet resultSet = preparedStatement.executeQuery()) {
                if (resultSet.next()) {
                    return montarDados(resultSet);
                }
            }
        }
        return null;
    }

    public PerfilAcessoRedeEmpresaVO montarDados(ResultSet resultSet) throws SQLException {
        PerfilAcessoRedeEmpresaVO perfilAcessoRedeEmpresaVO = new PerfilAcessoRedeEmpresaVO();
        perfilAcessoRedeEmpresaVO.setChaveOrigem(resultSet.getString("chaveorigem"));
        perfilAcessoRedeEmpresaVO.setChaveDestino(resultSet.getString("chavedestino"));
        perfilAcessoRedeEmpresaVO.setCodigo(resultSet.getInt("codigo"));
        perfilAcessoRedeEmpresaVO.setDatacadastro(resultSet.getDate("datacadastro"));
        perfilAcessoRedeEmpresaVO.setPerfilAcesso(resultSet.getInt("perfilacesso"));
        perfilAcessoRedeEmpresaVO.setDataatualizacao(resultSet.getDate("dataatualizacao"));
        perfilAcessoRedeEmpresaVO.setNomeUnidade(resultSet.getString("nomeUnidade"));
        perfilAcessoRedeEmpresaVO.setMensagemSituacao(resultSet.getString("mensagemsituacao"));
        perfilAcessoRedeEmpresaVO.setPerfilAcessoReplicado(resultSet.getInt("perfilacessoreplicado"));

        return perfilAcessoRedeEmpresaVO;
    }
}
