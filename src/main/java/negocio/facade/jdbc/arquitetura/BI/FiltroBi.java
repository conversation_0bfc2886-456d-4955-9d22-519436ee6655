package negocio.facade.jdbc.arquitetura.BI;

import negocio.comuns.arquitetura.BI.FiltroBiVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.bi.FiltroJSON;
import org.json.JSONObject;

import java.sql.*;
import java.time.Instant;

public class FiltroBi extends SuperEntidade {

    public FiltroBi() throws Exception {
    }

    public FiltroBi(Connection conexao) throws Exception {
        super(conexao);
    }

    public void excluir(String tokenfiltro) throws SQLException {
        String sql = "DELETE FROM filtrobi where tokenfiltro = '"+tokenfiltro+"'";

        try(PreparedStatement ps = con.prepareStatement(sql)) {
            ps.execute();
        }
    }

    public FiltroBiVO incluir(String nomeBI, FiltroJSON filtroJSON, JSONObject dadosJSON) throws Exception {

        FiltroBiVO filtroBiVO = new FiltroBiVO();
        filtroBiVO.setNome(nomeBI);
        filtroBiVO.setDataGeracao(Instant.now().toEpochMilli());
        filtroBiVO.setJsonDados(dadosJSON == null ? null : dadosJSON.toString());
        filtroBiVO.setFiltros(filtroJSON.toJSON());
        filtroBiVO.setTokenFiltro(filtroJSON.getToken());

        final String sql = "INSERT INTO FiltroBi( datageracao, filtros, jsondados, tokenfiltro, nome ) "
                + "VALUES ( ?, ?, ?, ?, ? ) RETURNING codigo ";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setLong(1, filtroBiVO.getDataGeracao());
            ps.setString(2, filtroBiVO.getFiltros());
            ps.setString(3, filtroBiVO.getJsonDados());
            ps.setString(4, filtroBiVO.getTokenFiltro());
            ps.setString(5, filtroBiVO.getNome());
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    filtroBiVO.setCodigo(rs.getInt("codigo"));
                }
            }
        }

        return filtroBiVO;
    }

    public FiltroBiVO obterPorToken(String tokenFiltro, String nomeBI) throws Exception {
        String sql = "SELECT * FROM FiltroBi " +
                " WHERE tokenfiltro = '" + tokenFiltro + "' " +
                " AND nome = '" + nomeBI + "' " +
                " ORDER BY codigo DESC" +
                " LIMIT 1";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    return montarDados(rs);
                }
            }
        }

        return null;
    }

    private FiltroBiVO montarDados(ResultSet rs) throws SQLException {
        FiltroBiVO filtroBiVO = new FiltroBiVO();
        filtroBiVO.setCodigo(rs.getInt("codigo"));
        filtroBiVO.setDataGeracao(rs.getLong("datageracao"));
        filtroBiVO.setFiltros(rs.getString("filtros"));
        filtroBiVO.setJsonDados(rs.getString("jsondados"));
        filtroBiVO.setNome(rs.getString("nome"));
        filtroBiVO.setTokenFiltro(rs.getString("tokenfiltro"));

        return filtroBiVO;
    }
}
