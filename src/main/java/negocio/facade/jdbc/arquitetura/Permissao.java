package negocio.facade.jdbc.arquitetura;
import java.sql.Connection;
import java.util.Iterator;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.List;
import java.util.ArrayList;
import negocio.comuns.utilitarias.*;
import negocio.comuns.arquitetura.*;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>PermissaoVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>PermissaoVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see PermissaoVO
 * @see SuperEntidade
 * @see PerfilAcesso
*/
public class Permissao extends SuperEntidade {    
	
    public Permissao() throws Exception {
        super();
        setIdEntidade("PerfilAcesso");
    }

    public Permissao(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("PerfilAcesso");
    }


    /**
     * Operação responsável por retornar um novo objeto da classe <code>PermissaoVO</code>.
    */
    public PermissaoVO novo() throws Exception {
        incluir(getIdEntidade());
        PermissaoVO obj = new PermissaoVO();
        return obj;
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>PermissaoVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>PermissaoVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void incluir(PermissaoVO obj) throws Exception {
        PermissaoVO.validarDados(obj);
       // Permissao.incluir(getIdEntidade());
      //  obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Permissao( codPerfilAcesso, nomeEntidade, permissoes, tituloApresentacao, tipoPermissao, valorEspecifico, valorInicial, valorFinal ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ? )";
        try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
            if (obj.getCodPerfilAcesso().intValue() != 0) {
                sqlInserir.setInt(1, obj.getCodPerfilAcesso().intValue());
            } else {
                sqlInserir.setNull(1, 0);
            }
            sqlInserir.setString(2, obj.getNomeEntidade());
            sqlInserir.setString(3, obj.getPermissoes());
            sqlInserir.setString(4, obj.getTituloApresentacao());
            sqlInserir.setInt(5, obj.getTipoPermissao().intValue());
            sqlInserir.setString(6, obj.getValorEspecifico());
            sqlInserir.setString(7, obj.getValorInicial());
            sqlInserir.setString(8, obj.getValorFinal());
            sqlInserir.execute();
        }
        obj.setNovoObj(new Boolean(false));
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>PermissaoVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>PermissaoVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
    */
    public void alterar(PermissaoVO obj) throws Exception {
        PermissaoVO.validarDados(obj);
        alterar(getIdEntidade());
     //   obj.realizarUpperCaseDados();
        String sql = "UPDATE Permissao set permissoes=?, tituloApresentacao=?, tipoPermissao=?, valorEspecifico=?, valorInicial=?, valorFinal=? WHERE ((codPerfilAcesso = ?) and (nomeEntidade = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            sqlAlterar.setString(1, obj.getPermissoes());
            sqlAlterar.setString(2, obj.getTituloApresentacao());
            sqlAlterar.setInt(3, obj.getTipoPermissao().intValue());
            sqlAlterar.setString(4, obj.getValorEspecifico());
            sqlAlterar.setString(5, obj.getValorInicial());
            sqlAlterar.setString(6, obj.getValorFinal());
            if (obj.getCodPerfilAcesso().intValue() != 0) {
                sqlAlterar.setInt(7, obj.getCodPerfilAcesso().intValue());
            } else {
                sqlAlterar.setNull(7, 0);
            }
            sqlAlterar.setString(8, obj.getNomeEntidade());
            sqlAlterar.execute();
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>PermissaoVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>PermissaoVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
    */
    public void excluir(PermissaoVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM Permissao WHERE ((codPerfilAcesso = ?) and (nomeEntidade = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            if (obj.getCodPerfilAcesso().intValue() != 0) {
                sqlExcluir.setInt(1, obj.getCodPerfilAcesso().intValue());
            } else {
                sqlExcluir.setNull(1, 0);
            }
            sqlExcluir.setString(2, obj.getNomeEntidade());
            sqlExcluir.execute();
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Permissao</code> através do valor do atributo 
     * <code>String tituloApresentacao</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PermissaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorTituloApresentacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Permissao WHERE upper( tituloApresentacao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY tituloApresentacao";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Permissao</code> através do valor do atributo 
     * <code>String nomeEntidade</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>PermissaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorNomeEntidade(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM Permissao WHERE upper( nomeEntidade ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY nomeEntidade";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
            }
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>Permissao</code> através do valor do atributo 
     * <code>nome</code> da classe <code>PerfilAcesso</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>PermissaoVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
    */
    public List consultarPorNomePerfilAcesso(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT Permissao.* FROM Permissao, PerfilAcesso WHERE Permissao.codPerfilAcesso = PerfilAcesso.codigo and upper( PerfilAcesso.nome ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY PerfilAcesso.nome";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados);
            }
        }
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>PermissaoVO</code> resultantes da consulta.
    */
    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            PermissaoVO obj = new PermissaoVO();
            obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static PermissaoVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        PermissaoVO obj = new PermissaoVO();
        obj.setNovoObj(false);
        obj.setCodPerfilAcesso( new Integer( dadosSQL.getInt("codPerfilAcesso")));
        obj.setNomeEntidade( dadosSQL.getString("nomeEntidade"));
        obj.setPermissoes( dadosSQL.getString("permissoes"));
        obj.setTituloApresentacao( dadosSQL.getString("tituloApresentacao"));
        obj.setTipoPermissao( new Integer( dadosSQL.getInt("tipoPermissao")));
        obj.setValorEspecifico( dadosSQL.getString("valorEspecifico"));
        obj.setValorInicial( dadosSQL.getString("valorInicial"));
        obj.setValorFinal( dadosSQL.getString("valorFinal"));

        return obj;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>PermissaoVO</code>.
     * @return  O objeto da classe <code>PermissaoVO</code> com os dados devidamente montados.
    */
    public static PermissaoVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        PermissaoVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA)
            return obj;

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }
    /**
     * Operação responsável por excluir todos os objetos da <code>PermissaoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>Permissao</code>.
     * @param <code>codPerfilAcesso</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public void excluirPermissaos( Integer codPerfilAcesso ) throws Exception {
        String sql = "DELETE FROM Permissao WHERE (codPerfilAcesso = ?)";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codPerfilAcesso.intValue());
            sqlExcluir.execute();
        }
    }

    /**
     * Operação responsável por alterar todos os objetos da <code>PermissaoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirPermissaos</code> e <code>incluirPermissoes</code> disponíveis na classe <code>Permissao</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public void alterarPermissaos( Integer codPerfilAcesso, List objetos ) throws Exception {
        excluirPermissaos( codPerfilAcesso );
        incluirPermissoes( codPerfilAcesso, objetos );
    }

    /**
     * Operação responsável por incluir objetos da <code>PermissaoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>arquitetura.PerfilAcesso</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public void incluirPermissoes( Integer codPerfilAcessoPrm, List objetos ) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            PermissaoVO obj = (PermissaoVO)e.next();
            obj.setCodPerfilAcesso(codPerfilAcessoPrm);
            incluir(obj);
        }
    }

    /**
     * Operação responsável por consultar todos os <code>PermissaoVO</code> relacionados a um objeto da classe <code>arquitetura.PerfilAcesso</code>.
     * @param codPerfilAcesso  Atributo de <code>arquitetura.PerfilAcesso</code> a ser utilizado para localizar os objetos da classe <code>PermissaoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>PermissaoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
    */
    public List<PermissaoVO> consultarPermissaos(Integer codPerfilAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        List<PermissaoVO> objetos = new ArrayList<PermissaoVO>();
        String sql = "SELECT * FROM Permissao WHERE codPerfilAcesso = ?";
        try (PreparedStatement sqlConsulta = con.prepareStatement(sql)) {
            sqlConsulta.setInt(1, codPerfilAcesso);
            try (ResultSet resultado = sqlConsulta.executeQuery()) {
                while (resultado.next()) {
                    PermissaoVO novoObj = Permissao.montarDados(resultado, nivelMontarDados);
                    objetos.add(novoObj);
                }
            }
        }
        return objetos;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>PermissaoVO</code>
     * através de sua chave primária. 
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
    */
    public PermissaoVO consultarPorChavePrimaria( Integer codPerfilAcessoPrm, String nomeEntidadePrm, int nivelMontarDados ) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM Permissao WHERE codPerfilAcesso = ? and nomeEntidade = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codPerfilAcessoPrm.intValue());
            sqlConsultar.setString(2, nomeEntidadePrm);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( Permissao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados));
            }
        }
    }
    
}
