package negocio.facade.jdbc.contrato;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.contrato.ContratoHorarioVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Horario;
import negocio.facade.jdbc.plano.Plano;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>ContratoHorarioVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>ContratoHorarioVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ContratoHorarioVO
 * @see SuperEntidade
 * @see Plano
 */
public class ContratoHorario extends SuperEntidade {    

    public ContratoHorario() throws Exception {
        super();
        setIdEntidade("Contrato");
    }

    public ContratoHorario(Connection conexao) throws Exception {
        super(conexao);
        setIdEntidade("Contrato");
    }


    /**
     * Operação responsável por retornar um novo objeto da classe <code>ContratoHorarioVO</code>.
     */
    public ContratoHorarioVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ContratoHorarioVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>ContratoHorarioVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>ContratoHorarioVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */

    public void incluirHorarioAlterandoPlano(ContratoHorarioVO obj,ContratoHorarioVO contrato ) throws Exception {
        ContratoHorarioVO.validarDados(contrato);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoHorario( horario, Contrato, percentualDesconto, valorEspecifico,  tipoValor, tipoOperacao) VALUES ( ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (contrato.getHorario().getCodigo() != 0) {
            sqlInserir.setInt(1, contrato.getHorario().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getContrato() != 0) {
            sqlInserir.setInt(2, obj.getContrato());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setDouble(3, obj.getPercentualDesconto());
        sqlInserir.setDouble(4, obj.getValorEspecifico());
        sqlInserir.setString(5, obj.getTipoValor());
        sqlInserir.setString(6, obj.getTipoOperacao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    public void incluir(ContratoHorarioVO obj) throws Exception {
        ContratoHorarioVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ContratoHorario( horario, Contrato, percentualDesconto, valorEspecifico,  tipoValor, tipoOperacao) VALUES ( ?, ?, ?, ?, ?, ? )";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getHorario().getCodigo() != 0) {
            sqlInserir.setInt(1, obj.getHorario().getCodigo());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getContrato() != 0) {
            sqlInserir.setInt(2, obj.getContrato());
        } else {
            sqlInserir.setNull(2, 0);
        }
        sqlInserir.setDouble(3, obj.getPercentualDesconto());
        sqlInserir.setDouble(4, obj.getValorEspecifico());
        sqlInserir.setString(5, obj.getTipoValor());
        sqlInserir.setString(6, obj.getTipoOperacao());
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoHorarioVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoHorarioVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterar(ContratoHorarioVO obj) throws Exception {
        ContratoHorarioVO.validarDados(obj);
        alterar(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ContratoHorario set horario=?, Contrato=?, percentualDesconto=?, valorEspecifico=?, tipoValor=?, tipoOperacao=? WHERE ((codigo = ?))";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        if (obj.getHorario().getCodigo() != 0) {
            sqlAlterar.setInt(1, obj.getHorario().getCodigo());
        } else {
            sqlAlterar.setNull(1, 0);
        }
        if (obj.getContrato() != 0) {
            sqlAlterar.setInt(2, obj.getContrato());
        } else {
            sqlAlterar.setNull(2, 0);
        }
        sqlAlterar.setDouble(3, obj.getPercentualDesconto());
        sqlAlterar.setDouble(4, obj.getValorEspecifico());
        sqlAlterar.setString(5, obj.getTipoValor());
        sqlAlterar.setString(6, obj.getTipoOperacao());
        sqlAlterar.setInt(7, obj.getCodigo());
        sqlAlterar.execute();
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>ContratoHorarioVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoHorarioVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    public void alterarPorCodigoContrato(ContratoHorarioVO obj) throws Exception {
        try {
            ContratoHorarioVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE ContratoHorario set horario=?, Contrato=?, percentualDesconto=?, valorEspecifico=?, tipoValor=?, tipoOperacao=? WHERE ((Contrato = ?))";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (obj.getHorario().getCodigo() != 0) {
                sqlAlterar.setInt(1, obj.getHorario().getCodigo());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (obj.getContrato() != 0) {
                sqlAlterar.setInt(2, obj.getContrato());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setDouble(3, obj.getPercentualDesconto());
            sqlAlterar.setDouble(4, obj.getValorEspecifico());
            sqlAlterar.setString(5, obj.getTipoValor());
            sqlAlterar.setString(6, obj.getTipoOperacao());
            sqlAlterar.setInt(7, obj.getContrato());
            sqlAlterar.execute();
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>ContratoHorarioVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>ContratoHorarioVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public void excluir(ContratoHorarioVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ContratoHorario WHERE ((codigo = ?))";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    /**
     *

    /**
     * Responsável por realizar uma consulta de <code>ContratoHorario</code> através do valor do atributo
     * <code>descricao</code> da classe <code>Horario</code>
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @return  List Contendo vários objetos da classe <code>ContratoHorarioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorDescricaoHorario(String valorConsulta, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT ContratoHorario.* FROM ContratoHorario, Horario WHERE ContratoHorario.horario = Horario.codigo and upper( Horario.descricao ) like('" + valorConsulta.toUpperCase() + "%') ORDER BY Horario.descricao";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
    }

    /**
     * Responsável por realizar uma consulta de <code>ContratoHorario</code> através do valor do atributo
     * <code>Integer codigo</code>. Retorna os objetos com valores iguais ou superiores ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @return  List Contendo vários objetos da classe <code>ContratoHorarioVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContratoHorario WHERE codigo >= " + valorConsulta + " ORDER BY codigo";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
    }

    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @return  List Contendo vários objetos da classe <code>ContratoHorarioVO</code> resultantes da consulta.
     */
    public static List<ContratoHorarioVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List<ContratoHorarioVO> vetResultado = new ArrayList<ContratoHorarioVO>();
        while (tabelaResultado.next()) {
            ContratoHorarioVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ContratoHorarioVO montarDadosBasico(ResultSet dadosSQL) throws Exception {
        ContratoHorarioVO obj = new ContratoHorarioVO();
        obj.setNovoObj(false);
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.getHorario().setCodigo(dadosSQL.getInt("horario"));
        obj.setContrato(dadosSQL.getInt("Contrato"));
        obj.setTipoValor(dadosSQL.getString("tipoValor"));
        obj.setTipoOperacao(dadosSQL.getString("tipoOperacao"));
        obj.setPercentualDesconto(dadosSQL.getDouble("percentualDesconto"));
        obj.setValorEspecifico(dadosSQL.getDouble("valorEspecifico"));

        return obj;

    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>ContratoHorarioVO</code>.
     * @return  O objeto da classe <code>ContratoHorarioVO</code> com os dados devidamente montados.
     */
    public static ContratoHorarioVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ContratoHorarioVO obj = montarDadosBasico(dadosSQL);

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return obj;
        }

        montarDadosHorario(obj, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
        return obj;
    }

    /**
     * Operação responsável por montar os dados de um objeto da classe <code>HorarioVO</code> relacionado ao objeto <code>ContratoHorarioVO</code>.
     * Faz uso da chave primária da classe <code>HorarioVO</code> para realizar a consulta.
     * @param obj  Objeto no qual será montado os dados consultados.
     */
    public static void montarDadosHorario(ContratoHorarioVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getHorario().getCodigo() == 0) {
            obj.setHorario(new HorarioVO());
            return;
        }
        Horario horario = new Horario(con);
        obj.setHorario(horario.consultarPorChavePrimaria(obj.getHorario().getCodigo(), nivelMontarDados));
        horario = null;
    }

    /**
     * Operação responsável por consultar todos os <code>ContratoHorarioVO</code> relacionados a um objeto da classe <code>plano.Contrato</code>.
     * @param contrato Atributo de <code>Contrato.Contrato</code> a ser utilizado para localizar os objetos da classe <code>ContratoHorarioVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ContratoHorarioVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public ContratoHorarioVO consultarContratoHorarios(Integer contrato, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade());
        String sql = "SELECT * FROM ContratoHorario WHERE Contrato = ?";
        PreparedStatement sqlConsulta = con.prepareStatement(sql);
        sqlConsulta.setInt(1, contrato);
        ResultSet resultado = sqlConsulta.executeQuery();
        if (!resultado.next()) {
            return new ContratoHorarioVO();
        }
        return (montarDados(resultado, nivelMontarDados, this.con));
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>ContratoHorarioVO</code>
     * através de sua chave primária.
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    public ContratoHorarioVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM ContratoHorario WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigoPrm);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ContratoHorario ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados, this.con));
    }

    public void alterarHorariosConfiguracoes(ContratoVO contratoVO, HorarioVO horarioVO) throws Exception{

        try {
            String sql = "UPDATE ContratoHorario set horario=? WHERE contrato =?";
            PreparedStatement sqlAlterar = con.prepareStatement(sql);
            if (horarioVO.getCodigo() != 0) {
                sqlAlterar.setInt(1, horarioVO.getCodigo());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (contratoVO.getCodigo() != 0) {
                sqlAlterar.setInt(2, contratoVO.getCodigo());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.execute();
        }catch (Exception e){
            throw  new Exception(e);
        }

    }
}
