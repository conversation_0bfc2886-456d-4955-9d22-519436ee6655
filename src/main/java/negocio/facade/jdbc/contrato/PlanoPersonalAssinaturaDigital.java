package negocio.facade.jdbc.contrato;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.PlanoPersonalAssinaturaDigitalVO;
import negocio.comuns.contrato.PlanoPersonalTextoPadraoVO;
import negocio.comuns.financeiro.ControleTaxaPersonalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.contrato.PlanoPersonalAssinaturaDigitalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class PlanoPersonalAssinaturaDigital extends SuperEntidade implements PlanoPersonalAssinaturaDigitalInterfaceFacade {

    public PlanoPersonalAssinaturaDigital() throws Exception {
        super();
        setIdEntidade("PlanoPersonal");
    }

    public PlanoPersonalAssinaturaDigital(Connection con) throws Exception {
        super(con);
        setIdEntidade("PlanoPersonal");
    }

    //Este método serve para disponibilizar as taxas para serem assinadas.
    @Override
    public void atualizaPlanoPersonalParaAssinar() throws Exception{
        StringBuilder sql = new StringBuilder();

        sql.append(" INSERT INTO planopersonaltextopadrao (planotextopadrao, taxapersonal, codigo)( ");
        sql.append(" SELECT p.planotextopadrao, c.codigo, c.codigo ");
        sql.append(" FROM controletaxapersonal c, plano p ");
        sql.append(" WHERE c.plano = p.codigo ");
        sql.append(" AND c.plano IS NOT NULL) ");

        criarConsulta(sql.toString(), con);
    }

    @Override
    public void incluir(PlanoPersonalAssinaturaDigitalVO obj) throws Exception {
        String sql = "INSERT INTO planopersonalassinaturadigital (taxapersonal, usuarioresponsavel, planopersonaltextopadrao,"
                + "documentos, endereco, assinatura, atestado, anexo1, anexo2, lancamento) VALUES (?,?,?,?,?,?,?,?,?,?)";
        int i = 1;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(i++, obj.getTaxaPersonal().getCodigo());
        sqlInserir.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlInserir.setInt(i++, obj.getTaxaPersonal().getCodigo());
        sqlInserir.setString(i++, obj.getDocumentos());
        sqlInserir.setString(i++, obj.getEndereco());
        sqlInserir.setString(i++, obj.getAssinatura());
        sqlInserir.setString(i++, obj.getAtestado());
        sqlInserir.setString(i++, obj.getAnexo1());
        sqlInserir.setString(i++, obj.getAnexo2());
        sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void excluir(PlanoPersonalAssinaturaDigitalVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM planopersonalassinaturadigital WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo().intValue());
        sqlExcluir.execute();
    }

    @Override
    public void alterar(PlanoPersonalAssinaturaDigitalVO obj) throws Exception {
        String sql = "UPDATE planopersonalassinaturadigital set taxapersonal = ?, usuarioresponsavel = ?,"
                + "documentos = ?, endereco = ?,  assinatura=?, atestado = ?, anexo1 = ?, anexo2 = ? WHERE codigo = ?";
        int i = 1;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setInt(i++, obj.getTaxaPersonal().getCodigo());
        sqlAlterar.setInt(i++, obj.getUsuarioResponsavel().getCodigo());
        sqlAlterar.setString(i++, obj.getDocumentos());
        sqlAlterar.setString(i++, obj.getEndereco());
        sqlAlterar.setString(i++, obj.getAssinatura());
        sqlAlterar.setString(i++, obj.getAtestado());
        sqlAlterar.setString(i++, obj.getAnexo1());
        sqlAlterar.setString(i++, obj.getAnexo2());
        sqlAlterar.setInt(i++, obj.getCodigo());
        sqlAlterar.execute();
    }

    @Override
    public List<ControleTaxaPersonalVO> consultarPlanos(boolean assinados, String filtro, Integer empresa, boolean todos) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT clb.codigo, pes.nome, ctp.codigo as planoPersonal, pes.fotokey, ppa.lancamento ");
        sql.append(" FROM controletaxapersonal ctp ");
        sql.append(" INNER JOIN colaborador clb ON clb.codigo = ctp.personal ");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = clb.pessoa ");
        sql.append(" LEFT JOIN planopersonalassinaturadigital ppa ON ppa.taxapersonal = ctp.codigo ");
        sql.append(" WHERE clb.situacao = 'AT' ");
        sql.append(" AND ctp.plano IS NOT NULL ");
        sql.append(" AND ctp.cancelado IS NOT TRUE ");
        if (empresa != null){
            sql.append(" AND clb.empresa = ").append(empresa);
        }
        sql.append(assinados ? " AND (ppa.assinatura IS NOT NULL OR ppa.assinatura <> '') " : " AND (ppa.assinatura IS NULL OR ppa.assinatura = '') ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" AND (pes.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append(" OR clb.codigo::varchar = '").append(filtro).append("' )");
        }
        sql.append(assinados ? " ORDER BY ppa.lancamento DESC " : " ORDER BY pes.nome ").append(todos ? "" : "LIMIT 20");
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<ControleTaxaPersonalVO> lista = new ArrayList<>();
        while(rs.next()){
            ControleTaxaPersonalVO controleTaxaPersonal = new ControleTaxaPersonalVO();
            controleTaxaPersonal.setCodigo(rs.getInt("planoPersonal"));
            controleTaxaPersonal.setPersonal(new ColaboradorVO());
            controleTaxaPersonal.getPersonal().setCodigo(rs.getInt("codigo"));
            controleTaxaPersonal.getPersonal().getPessoa().setNome(rs.getString("nome").toLowerCase());
            controleTaxaPersonal.getPersonal().getPessoa().setFotoKey(rs.getString("fotokey"));
            controleTaxaPersonal.setAssinadoEm(rs.getTimestamp("lancamento"));
            lista.add(controleTaxaPersonal);
        }
        return lista;
    }

    @Override
    public Integer countPlanos(boolean assinados, String filtro, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append(" SELECT count(ctp.codigo) as cont ");
        sql.append(" FROM controletaxapersonal ctp ");
        sql.append(" INNER JOIN colaborador clb ON clb.codigo = ctp.personal ");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = clb.pessoa ");
        sql.append(" LEFT JOIN planopersonalassinaturadigital ppa ON ppa.taxapersonal = ctp.codigo ");
        sql.append(" WHERE clb.situacao = 'AT' ");
        sql.append(" AND ctp.plano IS NOT NULL ");
        sql.append(" AND ctp.cancelado IS NOT TRUE ");
        if(empresa != null){
            sql.append(" AND clb.empresa = ").append(empresa);
        }
        sql.append(assinados ? " AND (ppa.assinatura IS NOT NULL AND ppa.assinatura <> '') " : " AND (ppa.assinatura IS NULL OR ppa.assinatura = '') ");
        if (!UteisValidacao.emptyString(filtro)) {
            sql.append(" AND (pes.nomeconsulta LIKE remove_acento_upper('").append(filtro.replaceAll(" ", "%")).append("%')");
            sql.append(" OR clb.codigo::varchar = '").append(filtro).append("' )");
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    @Override
    public Integer countPlanosAtivosSemAssinatura(Integer codigoPersonal, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT count(ctp.codigo) as cont ");
        sql.append(" FROM controletaxapersonal ctp ");
        sql.append(" INNER JOIN colaborador clb ON clb.codigo = ctp.personal ");
        sql.append(" INNER JOIN pessoa pes ON pes.codigo = clb.pessoa ");
        sql.append(" LEFT JOIN planopersonalassinaturadigital ppa ON ppa.taxapersonal = ctp.codigo ");
        sql.append(" WHER clb.situacao = 'AT' ");
        sql.append(" AND clb.empresa = ").append(empresa);
        sql.append(" AND ctp.personal = ").append(codigoPersonal);
        sql.append(" AND (ppa.assinatura IS NULL OR ppa.assinatura = '') ");

        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("cont");
        }
        return 0;
    }

    @Override
    public String obterPorPlano(Integer planoPersonal) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT assinatura from planopersonalassinaturadigital ");
        sql.append(" WHERE taxapersonal = ").append(planoPersonal);
        ResultSet rs = criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getString("assinatura");
        }
        return null;
    }

    @Override
    public PlanoPersonalAssinaturaDigitalVO consultarPorPlano(Integer planoPersonal) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT c.codigo, c.documentos, c.endereco, c.assinatura, c.atestado, u.nome as nomeusuario, ");
        sql.append(" c.anexo1, c.anexo2 ");
        sql.append(" FROM planopersonalassinaturadigital c\n");
        sql.append(" inner join usuario u on u.codigo = c.usuarioresponsavel where taxapersonal = ").append(planoPersonal);
        ResultSet rs = criarConsulta(sql.toString(), con);
        PlanoPersonalAssinaturaDigitalVO docs = new PlanoPersonalAssinaturaDigitalVO();
        docs.setTaxaPersonal(new ControleTaxaPersonalVO());
        docs.getTaxaPersonal().setCodigo(planoPersonal);
        if (rs.next()) {
            docs.setCodigo(rs.getInt("codigo"));
            if (UteisValidacao.emptyString(rs.getString("documentos"))) {
                docs.setDocumentos("");
            } else {
                docs.setDocumentos(Uteis.getPaintFotoDaNuvem(rs.getString("documentos")));
            }
            if (UteisValidacao.emptyString(rs.getString("endereco"))) {
                docs.setEndereco("");
            } else {
                docs.setEndereco(Uteis.getPaintFotoDaNuvem(rs.getString("endereco")));
            }
            if (UteisValidacao.emptyString(rs.getString("atestado"))) {
                docs.setAtestado("");
            } else {
                docs.setAtestado(Uteis.getPaintFotoDaNuvem(rs.getString("atestado")));
            }
            if (UteisValidacao.emptyString(rs.getString("anexo1"))) {
                docs.setAnexo1("");
            } else {
                docs.setAnexo1(Uteis.getPaintFotoDaNuvem(rs.getString("anexo1")));
            }
            if (UteisValidacao.emptyString(rs.getString("anexo2"))) {
                docs.setAnexo2("");
            } else {
                docs.setAnexo2(Uteis.getPaintFotoDaNuvem(rs.getString("anexo2")));
            }
            if (UteisValidacao.emptyString(rs.getString("assinatura"))) {
                docs.setAssinatura("");
            } else {
                docs.setAssinatura(Uteis.getPaintFotoDaNuvem(rs.getString("assinatura")));
            }
            docs.setUsuarioResponsavel(new UsuarioVO());
            docs.getUsuarioResponsavel().setNome(rs.getString("nomeusuario"));
        }
        return docs;
    }

    @Override
    public PlanoPersonalAssinaturaDigitalVO consultarPorCodigoPlano(Integer planoPersonal) throws Exception {
        String sql = "SELECT * FROM planopersonalassinaturadigital WHERE contrato = " + planoPersonal;
        ResultSet rs = criarConsulta(sql, con);
        PlanoPersonalAssinaturaDigitalVO planoPersonalAssinaturaDigital = new PlanoPersonalAssinaturaDigitalVO();
        if (rs.next()) {
            planoPersonalAssinaturaDigital.setCodigo(rs.getInt("codigo"));
            planoPersonalAssinaturaDigital.setTaxaPersonal(new ControleTaxaPersonalVO());
            planoPersonalAssinaturaDigital.getTaxaPersonal().setCodigo(rs.getInt("taxapersonal"));
            planoPersonalAssinaturaDigital.setUsuarioResponsavel(new UsuarioVO());
            planoPersonalAssinaturaDigital.getUsuarioResponsavel().setCodigo(rs.getInt("usuarioresponsavel"));
            planoPersonalAssinaturaDigital.setPlanoTextoPadrao(new PlanoPersonalTextoPadraoVO());
            planoPersonalAssinaturaDigital.getPlanoTextoPadrao().setCodigo(rs.getInt("planopersonaltextopadrao"));
            planoPersonalAssinaturaDigital.setDocumentos(rs.getString("documentos"));
            planoPersonalAssinaturaDigital.setEndereco(rs.getString("endereco"));
            planoPersonalAssinaturaDigital.setAssinatura(rs.getString("assinatura"));
            planoPersonalAssinaturaDigital.setAtestado(rs.getString("atestado"));
            planoPersonalAssinaturaDigital.setLancamento(rs.getTimestamp("lancamento"));
            planoPersonalAssinaturaDigital.setAnexo1(rs.getString("anexo1"));
            planoPersonalAssinaturaDigital.setAnexo2(rs.getString("anexo2"));
        }
        return null;
    }

    @Override
    public void excluirPorPlano(Integer contrato) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM planopersonalassinaturadigital WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, contrato);
        sqlExcluir.execute();
    }

    @Override
    public List<PlanoPersonalAssinaturaDigitalVO> consultarTodosPlanosDigitais(Integer montarDados, boolean assinados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM planopersonalassinaturadigital ppa ");
        if(assinados){
            sql.append(" WHERE ppa.assinatura <> '' OR ppa.assinatura <> null ");
        }
        ResultSet rs = criarConsulta(sql.toString(), con);
        List<PlanoPersonalAssinaturaDigitalVO> planos = new ArrayList<>();
        while(rs.next()){
            if(montarDados == Uteis.NIVELMONTARDADOS_TODOS){
                PlanoPersonalAssinaturaDigitalVO planoPersonalAssinaturaDigital = new PlanoPersonalAssinaturaDigitalVO();
                planoPersonalAssinaturaDigital.setCodigo(rs.getInt("codigo"));
                planoPersonalAssinaturaDigital.setTaxaPersonal(new ControleTaxaPersonalVO());
                planoPersonalAssinaturaDigital.getTaxaPersonal().setCodigo(rs.getInt("taxapersonal"));
                planoPersonalAssinaturaDigital.setUsuarioResponsavel(new UsuarioVO());
                planoPersonalAssinaturaDigital.getUsuarioResponsavel().setCodigo(rs.getInt("usuarioresponsavel"));
                planoPersonalAssinaturaDigital.setPlanoTextoPadrao(new PlanoPersonalTextoPadraoVO());
                planoPersonalAssinaturaDigital.getPlanoTextoPadrao().setCodigo(rs.getInt("planopersonaltextopadrao"));
                planoPersonalAssinaturaDigital.setDocumentos(rs.getString("documentos"));
                planoPersonalAssinaturaDigital.setEndereco(rs.getString("endereco"));
                planoPersonalAssinaturaDigital.setAssinatura(rs.getString("assinatura"));
                planoPersonalAssinaturaDigital.setAtestado(rs.getString("atestado"));
                planoPersonalAssinaturaDigital.setLancamento(rs.getTimestamp("lancamento"));
                planoPersonalAssinaturaDigital.setAnexo1(rs.getString("anexo1"));
                planoPersonalAssinaturaDigital.setAnexo2(rs.getString("anexo2"));
                planos.add(planoPersonalAssinaturaDigital);
            }
            if(montarDados == Uteis.NIVELMONTARDADOS_CONSULTA_DADOS_FOTOS_IMPORTADOR){
                PlanoPersonalAssinaturaDigitalVO planoPersonalAssinaturaDigital = new PlanoPersonalAssinaturaDigitalVO();
                planoPersonalAssinaturaDigital.setCodigo(rs.getInt("codigo"));
                planoPersonalAssinaturaDigital.setTaxaPersonal(new ControleTaxaPersonalVO());
                planoPersonalAssinaturaDigital.getTaxaPersonal().setCodigo(rs.getInt("contrato"));
                planoPersonalAssinaturaDigital.setAssinatura(rs.getString("assinatura"));
                planos.add(planoPersonalAssinaturaDigital);
            }
        }
        return null;
    }

    @Override
    public void excluirAssinaturaPersonal(Integer taxaCodigo) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM planopersonalassinaturadigital WHERE taxapersonal = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, taxaCodigo);
        sqlExcluir.execute();
    }

}
