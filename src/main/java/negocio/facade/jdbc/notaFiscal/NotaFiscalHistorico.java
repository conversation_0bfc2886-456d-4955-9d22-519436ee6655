package negocio.facade.jdbc.notaFiscal;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.notaFiscal.*;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.interfaces.notaFiscal.NotaFiscalHistoricoInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class NotaFiscalHistorico extends SuperEntidade implements NotaFiscalHistoricoInterfaceFacade {

    public NotaFiscalHistorico() throws Exception {
        super();
    }

    public NotaFiscalHistorico(Connection con) throws Exception {
        super(con);
    }

    public void incluir(NotaFiscalHistoricoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            incluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void incluirSemCommit(NotaFiscalHistoricoVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO NotaFiscalHistorico (dataRegistro, descricao, observacao, usuario, notaFiscal) VALUES (?, ?, ?, ?, ?)";
        int i = 0;
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setTimestamp(++i, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
        sqlInserir.setString(++i, obj.getDescricao());
        sqlInserir.setString(++i, obj.getObservacao());
        resolveIntegerNull(sqlInserir, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlInserir, ++i, obj.getNotaFiscalVO().getCodigo());

        sqlInserir.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }


    public void alterar(NotaFiscalHistoricoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void alterarSemCommit(NotaFiscalHistoricoVO obj) throws Exception {
        obj.realizarUpperCaseDados();
        String sql = "UPDATE NotaFiscalHistorico SET descricao ?, observacao = ?, usuario = ?, notafiscal = ? WHERE codigo = ?";
        int i = 0;
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setString(++i, obj.getDescricao());
        sqlAlterar.setString(++i, obj.getObservacao());
        resolveIntegerNull(sqlAlterar, ++i, obj.getUsuarioVO().getCodigo());
        resolveIntegerNull(sqlAlterar, ++i, obj.getNotaFiscalVO().getCodigo());

        sqlAlterar.setInt(++i, obj.getCodigo());
        sqlAlterar.execute();
    }

    public void excluir(NotaFiscalHistoricoVO obj) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(obj);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    public void excluirSemCommit(NotaFiscalHistoricoVO obj) throws Exception {
        String sql = "DELETE FROM NotaFiscalHistorico WHERE codigo = ?";
        PreparedStatement sqlExcluir = con.prepareStatement(sql);
        sqlExcluir.setInt(1, obj.getCodigo());
        sqlExcluir.execute();
    }

    public static List<NotaFiscalHistoricoVO> montarDadosConsulta(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        List<NotaFiscalHistoricoVO> vetResultado = new ArrayList<NotaFiscalHistoricoVO>();
        while (rs.next()) {
            NotaFiscalHistoricoVO obj = montarDados(rs, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static NotaFiscalHistoricoVO montarDados(ResultSet rs, int nivelMontarDados, Connection con) throws Exception {
        NotaFiscalHistoricoVO obj = new NotaFiscalHistoricoVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setNovoObj(false);
        obj.setDataRegistro(rs.getTimestamp("dataRegistro"));
        obj.setDescricao(rs.getString("descricao"));
        obj.setObservacao(rs.getString("observacao"));
        obj.getUsuarioVO().setCodigo(rs.getInt("usuario"));
        obj.getNotaFiscalVO().setCodigo(rs.getInt("notaFiscal"));

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS) {
            NotaFiscal notaFiscalDAO = new NotaFiscal(con);
            obj.setNotaFiscalVO(notaFiscalDAO.consultarPorChavePrimaria(obj.getNotaFiscalVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            notaFiscalDAO = null;

            if (!UteisValidacao.emptyNumber(obj.getUsuarioVO().getCodigo())) {
                Usuario usuarioDAO = new Usuario(con);
                obj.setUsuarioVO(usuarioDAO.consultarPorChavePrimaria(obj.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                usuarioDAO = null;
            } else {
                obj.setUsuarioVO(new UsuarioVO());
            }

            return obj;
        }

        return obj;
    }

    public NotaFiscalHistoricoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM NotaFiscalHistorico WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet rs = sqlConsultar.executeQuery();
        if (!rs.next()) {
            return new NotaFiscalHistoricoVO();
        }
        return montarDados(rs, nivelMontarDados, con);
    }

    public void gerarHistoricoSemCommit(String descricao, String observacao, String idPacto, Integer codNotaFiscal, UsuarioVO usuarioVO) throws Exception {
        if (UteisValidacao.emptyNumber(codNotaFiscal) && !UteisValidacao.emptyString(idPacto)) {
            NotaFiscal notaDAO = new NotaFiscal(con);
            codNotaFiscal = notaDAO.obterCodigoPorIdPacto(idPacto);
            notaDAO = null;
        }

        if (!UteisValidacao.emptyNumber(codNotaFiscal)) {
            NotaFiscalHistoricoVO novo = new NotaFiscalHistoricoVO();
            novo.setDescricao(descricao);
            novo.setObservacao(observacao);
            novo.setUsuarioVO(usuarioVO);
            novo.getNotaFiscalVO().setCodigo(codNotaFiscal);
            incluirSemCommit(novo);
        } else {
            throw new Exception("ERRO AO GERAR HISTÓRICO NOTA FISCAL - CÓDIGO NOTA FISCAL NÃO ENCONTRADO");
        }
    }

    public List<NotaFiscalHistoricoVO> consultar(Integer codNotaFiscal, int nivelMontarDados) throws Exception {

        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append(" SELECT \n ");
        sqlStr.append(" * \n ");
        sqlStr.append(" FROM NotaFiscalHistorico \n ");
        sqlStr.append(" WHERE 1 = 1 \n");

        if (!UteisValidacao.emptyNumber(codNotaFiscal)) {
            sqlStr.append(" AND notafiscal = ").append(codNotaFiscal).append(" \n");
        }

        sqlStr.append(" ORDER BY dataRegistro desc ");
        Statement stm = con.createStatement();
        ResultSet rs = stm.executeQuery(sqlStr.toString());
        return montarDadosConsulta(rs, nivelMontarDados, con);
    }
}
