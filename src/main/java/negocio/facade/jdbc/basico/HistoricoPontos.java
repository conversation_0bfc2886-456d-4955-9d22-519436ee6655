/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.CampanhaDuracaoVO;
import negocio.comuns.acesso.AcessoClienteVO;
import negocio.comuns.basico.BrindeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.ItemCampanhaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.webservice.Clima;
import negocio.facade.jdbc.plano.HorarioTurma;
import negocio.facade.jdbc.plano.Turma;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.basico.HistoricoPontosInterfaceFacade;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.*;
import java.text.Normalizer;


/**
 *
 * <AUTHOR>
 */
public class HistoricoPontos extends SuperEntidade implements HistoricoPontosInterfaceFacade{

    public HistoricoPontos() throws Exception {
        super();
    }
    
    public HistoricoPontos(Connection con) throws Exception {
        super(con);
    }
    
    @Override
    public HistoricoPontosVO novo() throws Exception {
        return new HistoricoPontosVO();
    }

    public HistoricoPontosVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM historicopontos WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException("Dados Não Encontrados ( ContratoOperacao ).");
                }
                return (montarDados(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    @Override
    public void incluirPontuacaoPorTipo(TipoItemCampanhaEnum tipoItemCampanha, Integer chaveEstrangeira, String descricao, ClienteVO cliente, EmpresaVO empresa) throws Exception {
        if (empresa.isTrabalharComPontuacao()) {
            //Clube de Vantagens  adicionar  pontuacao por indicação
            ItemCampanha itemCampanhaDAO = new ItemCampanha(con);
            CampanhaDuracao campanhaDuracaoDAO = new CampanhaDuracao(con);
            ItemCampanhaVO itemCampanhaPontos = itemCampanhaDAO.consultarPorChaveEstrangeira(empresa.getCodigo(), chaveEstrangeira, tipoItemCampanha.getCodigo());
            CampanhaDuracaoVO maiorCampanhaAtiva = campanhaDuracaoDAO.campanhaVigenteMultiplicador(Calendario.hoje(), tipoItemCampanha, empresa.getCodigo());
            //Validar se a empresa trabalha com pontos apenas em campanhas ativas
            if ((empresa.isPontuarApenasCategoriasEmCampanhasAtivas() && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())
                    || !empresa.isPontuarApenasCategoriasEmCampanhasAtivas()
                    // INDICAÇÕES NÃO ENTRAM NAS CAMPANHAS, POR ISSO NÃO PRECISA VERIFICAR SE A CAMPANHA ESTÁ ATIVA
                    // OU SE A EMPRESA SÓ PONTUA EM CAMPANHAS ATIVAS PARA ESTE TIPO DE PONTOS
                    || tipoItemCampanha.equals(TipoItemCampanhaEnum.INDICACAO)
                    || tipoItemCampanha.equals(TipoItemCampanhaEnum.INDICACAO_CONVERTIDA))
                    && (itemCampanhaPontos != null && itemCampanhaPontos.getPontos() > 0)) {
                HistoricoPontosVO historicoDePontos = new HistoricoPontosVO();
                historicoDePontos.setEntrada(true);
                historicoDePontos.setTipoPonto(tipoItemCampanha);
                Integer pontosTotal = 0;
                if (maiorCampanhaAtiva != null && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())) {
                    pontosTotal = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * itemCampanhaPontos.getPontos() : itemCampanhaPontos.getPontos());
                    historicoDePontos.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
                }  else {
                    pontosTotal = itemCampanhaPontos.getPontos();
                }
                historicoDePontos.setPontos(pontosTotal);
                if (maiorCampanhaAtiva != null && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())) {
                    historicoDePontos.setDescricao("Pontos por " + tipoItemCampanha.getDescricao() + descricao + " Pontos: " + pontosTotal + maiorCampanhaAtiva.getTextoCampanhaApresentar());
                } else {
                    historicoDePontos.setDescricao("Pontos por " + tipoItemCampanha.getDescricao() + descricao + " Pontos: " + pontosTotal);
                }
                historicoDePontos.setDataConfirmacao(Calendario.hoje());
                historicoDePontos.setDataaula(Calendario.hoje());
                historicoDePontos.setCliente(cliente);

                incluir(historicoDePontos);
                itemCampanhaDAO = null;
                campanhaDuracaoDAO = null;
            }
        }
    }

    @Override
    public void incluir(HistoricoPontosVO historicoPontos) throws Exception {
        if(historicoPontos.getTipoPonto()==TipoItemCampanhaEnum.AJUSTE_PONTO && !historicoPontos.getEntrada() && historicoPontos.getPontos()==0)
            historicoPontos.setPontostotal(0);
        else {
            Integer pontosTotal = 0;
            try (Statement stm = con.createStatement()) {
                StringBuilder sqlBuscarPontosTotal = new StringBuilder();
                sqlBuscarPontosTotal.append("SELECT pontostotal from historicopontos where cliente = " + historicoPontos.getCliente().getCodigo() + " order by codigo desc limit 1 ");
                try (ResultSet tabelaResultadoPontos = stm.executeQuery(sqlBuscarPontosTotal.toString())) {
                    if (tabelaResultadoPontos.next())
                        pontosTotal = tabelaResultadoPontos.getInt("pontostotal");
                }
                pontosTotal += historicoPontos.getPontos();
                if (pontosTotal < 0){
                    throw new Exception("Não é possível deixar o saldo de pontos do aluno negativo.");
                }
                historicoPontos.setPontostotal(pontosTotal);
            }
        }


        StringBuilder sql = new StringBuilder();
        if (historicoPontos.getBrinde().getCodigo() > 0) {
            sql.append(" INSERT INTO historicopontos(descricao,pontos,cliente,dataaula,dataconfirmacao,entrada,tipodepontos,pontostotal,contrato,produto,codigovenda,brinde,codigocampanha,observacao) \n");
            sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
        } else {
            sql.append(" INSERT INTO historicopontos(descricao,pontos,cliente,dataaula,dataconfirmacao,entrada,tipodepontos,pontostotal,contrato,produto,codigovenda,codigocampanha,observacao) \n");
            sql.append(" VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?) ");
        }
        try (PreparedStatement sqlInserir = con.prepareStatement(sql.toString())) {
            int i = 1;
            sqlInserir.setString(i++, historicoPontos.getDescricao());
            sqlInserir.setInt(i++, historicoPontos.getPontos());
            sqlInserir.setInt(i++, historicoPontos.getCliente().getCodigo());
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(historicoPontos.getDataaula()));
            sqlInserir.setTimestamp(i++, Uteis.getDataJDBCTimestamp(historicoPontos.getDataConfirmacao()));
            sqlInserir.setBoolean(i++, historicoPontos.getEntrada());
            sqlInserir.setInt(i++, historicoPontos.getTipoPonto().getCodigo());
            sqlInserir.setInt(i++, historicoPontos.getPontostotal());
            sqlInserir.setInt(i++, historicoPontos.getContrato());
            sqlInserir.setInt(i++, historicoPontos.getProduto());
            sqlInserir.setInt(i++, historicoPontos.getCodigoVenda());
            if (historicoPontos.getBrinde().getCodigo() > 0) {
                sqlInserir.setInt(i++, historicoPontos.getBrinde().getCodigo());
            }
            resolveIntegerNull(sqlInserir, i++, historicoPontos.getCodigoCampanha());
            resolveStringNull(sqlInserir, i++, historicoPontos.getObservacao());
            sqlInserir.execute();
        }
        historicoPontos.setCodigo(obterValorChavePrimariaCodigo());
    }

    @Override
    public void alterar(HistoricoPontosVO historicoPontos) throws Exception {
        try {
            con.setAutoCommit(false);
            alterarSemCommit(historicoPontos);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public List<HistoricoPontosVO> consultaTodosHistoricoPontos(Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from historicopontos");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public List<HistoricoPontosVO> consultaTotalPontosAlunos(String situacao,boolean pesquisaDetalhada,Date dataInicio, 
            Date dataFim,BrindeVO brinde, ClienteVO clientePesquisa, Integer empresa) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (!pesquisaDetalhada) {
            sql.append("SELECT hp.cliente,max(hp.codigo) as codigo,e.nome as nomeEmpresa from historicopontos hp");
            sql.append(" inner join cliente cli on cli.codigo = hp.cliente \n");
            sql.append(" inner join empresa e on e.codigo = cli.empresa \n");
            if (clientePesquisa != null || !clientePesquisa.getPessoa().getNome().isEmpty()) {
                sql.append(" inner join pessoa pe on pe.codigo = cli.pessoa ");
            }
            sql.append(" WHERE 1 = 1 ");
            if (!situacao.isEmpty()) {
                sql.append(" AND cli.situacao in ("+situacao+") ");
            }
            if (clientePesquisa != null || !clientePesquisa.getPessoa().getNome().isEmpty()) {
                sql.append(" AND pe.nome like '"+clientePesquisa.getPessoa().getNome().toUpperCase()+"%'");
            }
            if (empresa !=null && UteisValidacao.notEmptyNumber(empresa)){
                sql.append(" AND e.codigo="+empresa);
            }
            sql.append(" GROUP BY cliente,e.nome").append("\n");
            sql.append(" ORDER BY codigo");
        }else{
            sql.append("SELECT hp.*,e.nome as nomeEmpresa from historicopontos hp");
            sql.append(" inner join cliente cli on cli.codigo = hp.cliente \n");
            sql.append(" inner join empresa e on e.codigo = cli.empresa \n");
            if (clientePesquisa != null || !clientePesquisa.getPessoa().getNome().isEmpty()) {
                sql.append(" inner join pessoa pe on pe.codigo = cli.pessoa ");
            }
            sql.append(" where 1 = 1 ");
            if (!situacao.isEmpty()) {
                sql.append(" AND cli.situacao in ("+situacao+") ");
            }
            if (dataInicio != null && dataFim != null) {
                sql.append(" AND cast(hp.dataaula as DATE) BETWEEN '"+Uteis.getDataJDBC(dataInicio)+"' and '"+Uteis.getDataJDBC(dataFim)+"' ");
            }
            if (brinde.getCodigo() != 0) {
                sql.append(" AND hp.brinde = "+brinde.getCodigo());
            }
            if (clientePesquisa != null || !clientePesquisa.getPessoa().getNome().isEmpty()) {
                sql.append(" AND pe.nome like '"+clientePesquisa.getPessoa().getNome().toUpperCase()+"%'");
            }
            if (empresa !=null && UteisValidacao.notEmptyNumber(empresa)){
                sql.append(" AND e.codigo="+empresa);
            }
            sql.append(" ORDER BY hp.dataaula desc ");
        }
        List<HistoricoPontosVO> listaResultado;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {

                listaResultado = new ArrayList<HistoricoPontosVO>();
                Cliente cliente = new Cliente(con);
                Brinde brindePesquisa = new Brinde(con);
                if (!pesquisaDetalhada) {
                    while (tabelaResultado.next()) {
                        HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
                        ClienteVO clienteVO = cliente.consultarPorCodigo(tabelaResultado.getInt("cliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        historicoPontos.setCliente(clienteVO);
                        historicoPontos.setNomeEmpresa(tabelaResultado.getString("nomeEmpresa"));
                        historicoPontos.setPontostotal(obterPontosTotalPorCodigo(tabelaResultado.getInt("codigo")));
                        listaResultado.add(historicoPontos);
                    }
                } else {
                    while (tabelaResultado.next()) {
                        HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
                        ClienteVO clienteVO = cliente.consultarPorCodigo(tabelaResultado.getInt("cliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        historicoPontos.setCliente(clienteVO);
                        historicoPontos.setPontos(tabelaResultado.getInt("pontos"));
                        historicoPontos.setDataaula(tabelaResultado.getDate("dataaula"));
                        historicoPontos.setDataConfirmacao(tabelaResultado.getDate("dataconfirmacao"));
                        historicoPontos.setEntrada(tabelaResultado.getBoolean("entrada"));
                        historicoPontos.setTipoPonto(TipoItemCampanhaEnum.getTipo(tabelaResultado.getInt("tipodepontos")));
                        historicoPontos.setNomeEmpresa(tabelaResultado.getString("nomeEmpresa"));
                        if (tabelaResultado.getInt("brinde") != 0) {
                            historicoPontos.setBrinde(brindePesquisa.consultarPorChavePrimaria(tabelaResultado.getInt("brinde")));
                        } else {
                            historicoPontos.setBrinde(new BrindeVO());
                        }
                        listaResultado.add(historicoPontos);
                    }
                }
            }
        }
        return listaResultado;
    }
    
    public List<HistoricoPontosVO> montarDadosConsulta(ResultSet tabelaResultado,int nivelMontarDados, Connection con) throws SQLException, Exception{
        List<HistoricoPontosVO> listaResultado = new ArrayList<HistoricoPontosVO>();
        while (tabelaResultado.next()) {            
          HistoricoPontosVO historicoPontos = montarDados(tabelaResultado, nivelMontarDados, con);
          listaResultado.add(historicoPontos);
        }
        
        return listaResultado;
    }
    
    public HistoricoPontosVO montarDados(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws SQLException, Exception{
        HistoricoPontosVO historicoPontos = new HistoricoPontosVO();
        Cliente cliente = new Cliente(con);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
            ClienteVO clienteVO = cliente.consultarPorCodigo(tabelaResultado.getInt("cliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            historicoPontos.setPontostotal(tabelaResultado.getInt("pontostotal"));
            historicoPontos.setCliente(clienteVO);
            
        }
        
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            historicoPontos.setCodigo(tabelaResultado.getInt("codigo"));
            historicoPontos.setDescricao(tabelaResultado.getString("descricao"));
            historicoPontos.setPontos(tabelaResultado.getInt("pontos"));
            historicoPontos.setPontostotal(tabelaResultado.getInt("pontostotal"));
            historicoPontos.setEntrada(tabelaResultado.getBoolean("entrada"));
            historicoPontos.setTipoPonto(TipoItemCampanhaEnum.getTipo(tabelaResultado.getInt("tipodepontos")));
            historicoPontos.setContrato(tabelaResultado.getInt("contrato"));
            historicoPontos.setProduto(tabelaResultado.getInt("produto"));
            historicoPontos.setCodigoVenda(tabelaResultado.getInt("codigovenda"));
            ClienteVO clienteVO= new ClienteVO();
            clienteVO.setCodigo(tabelaResultado.getInt("cliente"));
            historicoPontos.setCliente(clienteVO);
        }
        
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_TODOS) {
            Brinde brinde = new Brinde(con);
            historicoPontos.setCodigo(tabelaResultado.getInt("codigo"));
            historicoPontos.setDescricao(tabelaResultado.getString("descricao"));
            historicoPontos.setPontos(tabelaResultado.getInt("pontos"));
            historicoPontos.setPontostotal(tabelaResultado.getInt("pontostotal"));
            historicoPontos.setEntrada(tabelaResultado.getBoolean("entrada"));
            historicoPontos.setTipoPonto(TipoItemCampanhaEnum.getTipo(tabelaResultado.getInt("tipodepontos")));
            ClienteVO clienteVO = cliente.consultarPorCodigo(tabelaResultado.getInt("cliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            historicoPontos.setCliente(clienteVO);
            historicoPontos.setDataaula(tabelaResultado.getTimestamp("dataaula"));
            historicoPontos.setDataConfirmacao(tabelaResultado.getTimestamp("dataconfirmacao"));
            BrindeVO brindeVO = brinde.consultarPorChavePrimaria(tabelaResultado.getInt("brinde"));
            historicoPontos.setBrinde(brindeVO);
            historicoPontos.setContrato(tabelaResultado.getInt("contrato"));
            historicoPontos.setProduto(tabelaResultado.getInt("produto"));
            historicoPontos.setCodigoVenda(tabelaResultado.getInt("codigovenda"));
            historicoPontos.setObservacao(tabelaResultado.getString("observacao"));
        }
        
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_PONTUACAO) {
            Brinde brinde = new Brinde(con);
            historicoPontos.setCodigo(tabelaResultado.getInt("codigo"));
            historicoPontos.setDescricao(tabelaResultado.getString("descricao"));
            historicoPontos.setPontos(tabelaResultado.getInt("pontos"));
            historicoPontos.setPontostotal(tabelaResultado.getInt("pontostotal"));
            historicoPontos.setEntrada(tabelaResultado.getBoolean("entrada"));
            historicoPontos.setTipoPonto(TipoItemCampanhaEnum.getTipo(tabelaResultado.getInt("tipodepontos")));
            ClienteVO clienteVO = cliente.consultarPorCodigo(tabelaResultado.getInt("cliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            historicoPontos.setCliente(clienteVO);
            historicoPontos.setDataaula(tabelaResultado.getTimestamp("dataaula"));
            historicoPontos.setDataConfirmacao(tabelaResultado.getTimestamp("dataconfirmacao"));
            BrindeVO brindeVO = brinde.consultarPorChavePrimaria(tabelaResultado.getInt("brinde"));
            historicoPontos.setBrinde(brindeVO);
            historicoPontos.setCodigoUltimoRegistro(tabelaResultado.getInt("codigoultimoregistro"));
        }

        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_RESULTADOS_BI) {
            if (Uteis.resultSetContemColuna(tabelaResultado, "codigoHistorico")) {
                historicoPontos.setCodigo(tabelaResultado.getInt("codigoHistorico"));
            }
            historicoPontos.setPontos(tabelaResultado.getInt("pontos"));
            historicoPontos.setTipoPonto(TipoItemCampanhaEnum.getTipo(tabelaResultado.getInt("tipodepontos")));
            historicoPontos.setCliente(new ClienteVO(tabelaResultado.getString("nome")));
            historicoPontos.getCliente().setMatricula(tabelaResultado.getString("matricula"));
            if (Uteis.resultSetContemColuna(tabelaResultado, "dataconfirmacao")) {
                historicoPontos.setDataConfirmacao(tabelaResultado.getTimestamp("dataconfirmacao"));
            }
            if (Uteis.resultSetContemColuna(tabelaResultado, "codigocampanha")) {
                historicoPontos.setCodigoCampanha(tabelaResultado.getInt("codigocampanha"));
            }
        }

        return historicoPontos;
    }

    @Override
    public void alterarSemCommit(HistoricoPontosVO historicoPontos) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE historicopontos set descricao = ?, pontos = ?, entrada = ? tipodepontos = ? WHERE codigo = ?");
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql.toString())) {
            sqlAlterar.setString(1, historicoPontos.getDescricao());
            sqlAlterar.setInt(2, historicoPontos.getPontos());
            sqlAlterar.setBoolean(3, historicoPontos.getEntrada());
            sqlAlterar.setInt(4, historicoPontos.getTipoPonto().getCodigo());
            sqlAlterar.setInt(5, historicoPontos.getCodigo());
            sqlAlterar.execute();
        }
    }

    @Override
    public List<HistoricoPontosVO> consultarHistoricoPorCliente(Integer cliente,Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT hp.*, (select max(hpc.codigo) from historicopontos hpc where hpc.cliente = hp.cliente) as codigoultimoregistro "
                + " from historicopontos hp WHERE cliente = "+cliente+" order by hp.codigo desc ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public Date existeAcessoPorClienteHorarioRobo(Integer cliente, Date dataRegistro) throws Exception {
        try (Statement stmCodigo = con.createStatement()) {
            try (ResultSet tabelaResultadoCodigo = stmCodigo.executeQuery("select max(dataconfirmacao) as ultimaData from historicopontos where cliente=" + cliente + " and Date(dataconfirmacao)='" + Uteis.getData(dataRegistro) + "' and tipodepontos = " + TipoItemCampanhaEnum.ACESSO.getCodigo())) {
                if (tabelaResultadoCodigo.next())
                    return tabelaResultadoCodigo.getTimestamp("ultimaData");
                else
                    return null;
            }
        }
    }

    @Override
    public Integer obterPontosTotalPorCliente(Integer cliente) throws Exception {
        StringBuilder sqlObterCodigo = new StringBuilder();
        sqlObterCodigo.append("SELECT max(codigo) as codigo from historicopontos where cliente = "+cliente);
        Integer codigo;
        try (Statement stmCodigo = con.createStatement()) {
            try (ResultSet tabelaResultadoCodigo = stmCodigo.executeQuery(sqlObterCodigo.toString())) {
                tabelaResultadoCodigo.next();
                codigo = tabelaResultadoCodigo.getInt("codigo");
            }
        }

        if (codigo > 0) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT pontostotal from historicopontos where codigo = "+codigo);
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                    tabelaResultado.next();
                    return tabelaResultado.getInt("pontostotal");
                }
            }
        }else{
            return 0;
        }
    }

    @Override
    public Integer obterPontosTotalPorTipoItemCampanha(ItemCampanhaVO itemCampanha, Date dataInicial, Date dataFinal) throws Exception {
        StringBuilder sqlObterCodigo = new StringBuilder();
        sqlObterCodigo.append("select abs(sum(h.pontos)) total,"+itemCampanha.getTipoItemCampanha().getCodigo()+" tipodepontos from historicopontos h " +
                " inner join cliente c on h.cliente=c.codigo "+
                " where h.dataconfirmacao between '"+ Uteis.getDataJDBCTimestamp(dataInicial)+"' and '"+Uteis.getDataHoraJDBC(dataFinal,"23:59:00")+"'" +
                (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANO ? " and tipodepontos in (" + TipoItemCampanhaEnum.PLANO.getCodigo() + "," + TipoItemCampanhaEnum.ESTORNO_PLANO.getCodigo() + ","+TipoItemCampanhaEnum.PLANODURACAO.getCodigo()+")" :
                        (itemCampanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PRODUTO ? " and tipodepontos in (" + TipoItemCampanhaEnum.PRODUTO.getCodigo() + "," + TipoItemCampanhaEnum.ESTORNO_PRODUTO.getCodigo() + ")" :
                                " and tipodepontos=" + itemCampanha.getTipoItemCampanha().getCodigo())) +
                (UteisValidacao.emptyNumber(itemCampanha.getEmpresa().getCodigo()) ? "" : " and c.empresa=" + itemCampanha.getEmpresa().getCodigo()) +
                " order by total desc");
        Integer totalPontos;
        try (Statement stmCodigo = con.createStatement()) {
            try (ResultSet tabelaResultadoCodigo = stmCodigo.executeQuery(sqlObterCodigo.toString())) {
                totalPontos = 0;
                if (tabelaResultadoCodigo.next())
                    totalPontos = tabelaResultadoCodigo.getInt("total");
            }
        }
        return totalPontos;
    }

    @Override
    public Integer obterPontosTotalPorCodigo(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT pontostotal from historicopontos where codigo = "+codigo);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                tabelaResultado.next();
                return tabelaResultado.getInt("pontostotal");
            }
        }
    }

    @Override
    public JSONArray consultaTotalPontosAlunosTreino(String situacao, boolean analitico, Integer cliente, String nomeCliente, Date dataInicio, Date dataFinal) throws Exception {
        JSONArray array;
        try (ResultSet rs = getPS(situacao, analitico, cliente, nomeCliente, dataInicio, dataFinal).executeQuery()) {
            array = new JSONArray();
            if (analitico) {
                while (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("codigocliente", rs.getInt("codigocliente"));
                    json.put("nomecliente", rs.getString("nomecliente"));
                    json.put("codigohistorico", rs.getInt("codigohistorico"));
                    json.put("descricao", rs.getString("descricao"));
                    json.put("pontos", rs.getInt("pontos"));
                    json.put("dataconfirmacao", rs.getDate("dataconfirmacao"));
                    String nomeBrinde = rs.getString("nomebrinde");
                    if (nomeBrinde == null) {
                        json.put("nomebrinde", "-");
                    } else {
                        json.put("nomebrinde", nomeBrinde);
                    }
                    if (rs.getBoolean("entrada")) {
                        json.put("operacao", "Entrada");
                    } else {
                        json.put("operacao", "Saida");
                    }
                    array.put(json);
                }
            } else {
                while (rs.next()) {
                    JSONObject json = new JSONObject();
                    json.put("codigocliente", rs.getInt("codigocliente"));
                    json.put("nomecliente", rs.getString("nomecliente"));
                    json.put("totalpontos", rs.getInt("pontosperiodo"));
                    array.put(json);
                }
            }
        }

        return array;
    }

    private PreparedStatement getPS(String situacao,boolean analitico,Integer cliente,String nomeCliente, Date dataInicio, Date dataFinal) throws SQLException, Exception{
        StringBuilder sql = new StringBuilder();
        if (!analitico) {
            sql.append(" SELECT sw.codigocliente as codigocliente,sw.nomecliente, sum(pontos) pontosperiodo, max(hp.codigo) as codigohistorico from historicopontos hp").append("\n");
            sql.append(" inner join situacaoclientesinteticodw sw on sw.codigocliente = hp.cliente ").append("\n");
            sql.append(" where 1=1 ").append("\n");
            if (dataInicio != null && dataFinal != null) {
                sql.append("and hp.dataaula::date between '" + Uteis.getData(dataInicio) + "' AND '" + Uteis.getData(dataFinal)).append("'\n");
            }
            if (!situacao.isEmpty()) {
                if (situacao.contains(",")) {
                    String[] dados = situacao.split(",");
                    StringBuffer situacaoSql = new StringBuffer("");
                    for (int i = 0; i < dados.length; i++) {
                        if (situacaoSql.toString().isEmpty()) {
                            situacaoSql.append("'"+dados[i]+"'");
                        }else{
                            situacaoSql.append(",").append("'"+dados[i]+"'");
                        }
                    }
                    sql.append(" and sw.situacao IN ("+situacaoSql.toString().toUpperCase()+") ").append("\n");
                }else{
                    sql.append(" and sw.situacao IN ('"+situacao.toUpperCase()+"') ").append("\n");
                }
                
            }
            if (!nomeCliente.isEmpty()) {
                sql.append(" and sw.nomecliente like '"+nomeCliente.toUpperCase()+"%' ").append("\n");
            }
            sql.append(" GROUP BY sw.nomecliente,sw.codigocliente").append("\n");
            sql.append(" ORDER BY codigohistorico");
        }else{
            sql.append(" SELECT sw.codigocliente as codigocliente,sw.nomecliente,hp.codigo as codigohistorico,").append("\n");
            sql.append(" hp.descricao,hp.pontos,hp.dataconfirmacao,b.nome as nomebrinde,hp.entrada ").append("\n");
            sql.append(" from historicopontos hp ").append("\n");
            sql.append(" inner join situacaoclientesinteticodw sw on sw.codigocliente = hp.cliente ").append("\n");
            sql.append(" left join brinde b on b.codigo = hp.brinde ").append("\n");
            sql.append(" where 1=1 ").append("\n");
            if (dataInicio != null && dataFinal != null) {
                sql.append("and hp.dataaula::date  between '" + Uteis.getData(dataInicio) + "' AND '" + Uteis.getData(dataFinal)).append("'\n");
            }
            sql.append(" and hp.cliente = "+cliente).append("\n");
            sql.append(" GROUP BY hp.dataconfirmacao,sw.nomecliente,sw.codigocliente,hp.codigo,b.nome");
            sql.append(" ORDER BY hp.codigo");
        }
        return con.prepareStatement(sql.toString());
    }

    @Override
    public void excluir(Integer codigo) throws Exception {
        try {
            con.setAutoCommit(false);
            excluirSemCommit(codigo);
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public void excluirSemCommit(Integer codigo) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" DELETE FROM historicopontos WHERE ((codigo = ?))");
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
            sqlExcluir.setInt(1, codigo);
            sqlExcluir.execute();
        }
    }

    @Override
    public void excluirPorContrato(Integer codigoContrato) throws Exception {
        try {
            con.setAutoCommit(false);
            StringBuilder sql = new StringBuilder();
            sql.append("DELETE FROM historicopontos WHERE ((contrato = ?))");
            try (PreparedStatement sqlExcluir = con.prepareStatement(sql.toString())) {
                sqlExcluir.setInt(1, codigoContrato);
                sqlExcluir.execute();
            }
            con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }

    @Override
    public HistoricoPontosVO obterHistoricoPorProduto(Integer cliente, Integer produto, Integer codigoVenda,Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from historicopontos WHERE cliente = "+cliente);
        sql.append(" AND produto = "+produto);
        if(codigoVenda!=null)
            sql.append(" AND codigovenda = "+codigoVenda);
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                if (!tabelaResultado.next()) {
                    return new HistoricoPontosVO();
                }
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }

    }

    @Override
    public List<HistoricoPontosVO> obterHistoricoPorContrato(TipoItemCampanhaEnum tipoDePontos,Integer cliente, Integer contrato,Integer nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * from historicopontos WHERE cliente = "+cliente);
        sql.append(" AND tipodepontos = "+tipoDePontos.getCodigo());
        sql.append(" AND contrato = "+contrato);
        List<HistoricoPontosVO> lstHistoricoPontos = new ArrayList();
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                while (tabelaResultado.next())
                lstHistoricoPontos.add( montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
        return lstHistoricoPontos;
    }

    public boolean existe(HistoricoPontosVO historicoPontosVO)  {
        String sql = "select * from historicopontos where descricao = ? and cliente = ? and dataaula = ? and tipodepontos = ?";

        try {
            try (PreparedStatement pstm = con.prepareStatement(sql)) {
                pstm.setString(1, historicoPontosVO.getDescricao());
                pstm.setInt(2, historicoPontosVO.getCliente().getCodigo());
                pstm.setTimestamp(3, Uteis.getDataJDBCTimestamp(historicoPontosVO.getDataaula()));
                pstm.setInt(4, historicoPontosVO.getTipoPonto().getCodigo());

                return pstm.executeQuery().next();
            }

        } catch (SQLException ignored) {
            return true; //Como se tivesse achado e não deixa inserir.
        }

    }

    @Override
    public void acresentarPontoPorAulaComfirmadaRobo(Date data) throws Exception {
        AulaConfirmada aulaConfirmada = new AulaConfirmada(con);
        List<AulaConfirmadaVO> listaAulaConfirmada = aulaConfirmada.consultarAulaConfirmadaPorData(data);
        Uteis.logar(null, "Inserindo Pontuação de aulas confirmadas...");
        for (AulaConfirmadaVO aulaConfirmadaVO : listaAulaConfirmada) {
            if(aulaConfirmadaVO.getClienteVO() != null) {
                HorarioTurma horarioTurma = new HorarioTurma(con);
                HorarioTurmaVO horarioTurmaVO = horarioTurma.consultarPorChavePrimaria(aulaConfirmadaVO.getHorarioTurmaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                Turma turma = new Turma(con);
                TurmaVO turmaVO = turma.consultarPorChavePrimaria(horarioTurmaVO.getTurma(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                CampanhaDuracaoVO maiorCampanhaAtiva = new CampanhaDuracao(con).campanhaVigenteMultiplicador(data, TipoItemCampanhaEnum.AULA, turmaVO.getEmpresa().getCodigo());
                if (turmaVO.getEmpresa().isTrabalharComPontuacao() && turmaVO.getPontosBonus() > 0 &&
                        (!turmaVO.getEmpresa().isPontuarApenasCategoriasEmCampanhasAtivas() ||
                            (turmaVO.getEmpresa().isPontuarApenasCategoriasEmCampanhasAtivas() && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())))) {
                    Cliente cliente = new Cliente(con);
                    ClienteVO clienteVO = cliente.consultarPorCodigo(aulaConfirmadaVO.getClienteVO().getCodigo(), false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    HistoricoPontosVO historicoPontosVO = new HistoricoPontosVO();
                    historicoPontosVO.setEntrada(true);
                    historicoPontosVO.setCliente(clienteVO);
                    Integer pontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * turmaVO.getPontosBonus() : turmaVO.getPontosBonus());
                    historicoPontosVO.setDescricao(horarioTurmaVO.getIdentificadorTurma()+maiorCampanhaAtiva.getTextoCampanhaApresentar());
                    historicoPontosVO.setPontos(pontos);
                    historicoPontosVO.setDataConfirmacao(aulaConfirmadaVO.getDataLancamento());
                    historicoPontosVO.setDataaula(Uteis.getDataJDBCTimestamp(Uteis.getDate(Uteis.getDataAplicandoFormatacao(aulaConfirmadaVO.getDia(), "yyyy-MM-dd")+ " "+ horarioTurmaVO.getHoraInicial(), "yyyy-MM-dd HH:mm")));
                    historicoPontosVO.setTipoPonto(TipoItemCampanhaEnum.AULA);
                    historicoPontosVO.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
                    if(!existe(historicoPontosVO)) {
                        incluir(historicoPontosVO);
                    }
                }
            }
        }
        Uteis.logar(null, "Pontuação de aulas inseridas com sucesso...");
    }

    @Override
    public void acresentarPontoPorAcessoRobo(Date data, List<EmpresaVO> listaEmpresa, String urlOamd, String key) throws Exception {
        if(StringUtils.isBlank(key)) {
            Uteis.logar(null, "Pontuaço de acesso - chamada incorreta, a chave está vazia, os pontos serão adicionados assim que o robo for executado no inicio do dia.");
        }else{
            try (Statement stm = con.createStatement()) {
                //Cliente cliente = new Cliente(con);
                Uteis.logar(null, "Verificar empresas que trabalham com pontuação por acesso...");
                for (EmpresaVO empresaVO : listaEmpresa) {
                    if (empresaVO.isTrabalharComPontuacao() && (empresaVO.getPontosAlunoAcesso() > 0 || empresaVO.getPontosAlunoAcessoChuva() > 0 ||
                            empresaVO.getPontosAlunoAcessoFrio() > 0 || empresaVO.getPontosAlunoAcessoCalor() > 0)) {
                        Uteis.logar(null, "Inserindo pontução ao cliente...");
                        StringBuilder sql = new StringBuilder();
                        sql.append(" SELECT ");
                        sql.append(" pessoa.codigo as pessoa, cliente, ");
                        if (empresaVO.isApenasPrimeiroAcessoClubeVantagens())
                            sql.append(" MIN(dthrentrada) as dataEntrada, ");
                        else
                            sql.append(" dthrentrada as dataentrada, ");
                        sql.append(" COUNT( acessocliente.codigo) as quantidade, ");
                        sql.append(" COUNT(CASE WHEN cliente.situacao like 'AT' THEN 1 ELSE null END) as quantidadeAtivos ");
                        sql.append(" FROM acessocliente ");
                        sql.append(" INNER JOIN cliente ON acessocliente.cliente = cliente.codigo ");
                        sql.append(" INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo ");
                        sql.append(" WHERE dthrentrada  BETWEEN '" + Uteis.getDataJDBC(data) + " 00:00:00'");
                        sql.append(" AND '" + Uteis.getDataJDBC(data) + " 23:59:59' ");
                        sql.append(" AND cliente.empresa = " + empresaVO.getCodigo() + " ");
                        if (empresaVO.isApenasPrimeiroAcessoClubeVantagens())
                            sql.append(" GROUP BY 1,2 ");
                        else
                            sql.append(" GROUP BY 1,2,3 ");
                        sql.append(" ORDER BY dataentrada");
                        try (ResultSet rs = stm.executeQuery(sql.toString())) {
                            List<Clima> arrayClima = null;
                            try {
                                arrayClima = montarDadosClima(data, empresaVO, urlOamd, key);
                            }catch (Exception e){
                                Uteis.logar(null, "Clima não encontrado.");
                            }
                            //Para VALIDAR tempo para proxima pontuação
                            Integer codigoPessoaAnterior = 0;
                            Date dataTimeRegistroAnterior = data;

                            while (rs.next()) {
                                Date dataTimeRegistro = rs.getTimestamp("dataentrada");
                                if(codigoPessoaAnterior!=rs.getInt("cliente"))
                                    dataTimeRegistroAnterior = data;
                                Date jaTemPontosDateTime = new HistoricoPontos(con).existeAcessoPorClienteHorarioRobo( rs.getInt("cliente"), dataTimeRegistro);
                                if (empresaVO.isApenasPrimeiroAcessoClubeVantagens() && jaTemPontosDateTime!=null){
                                        continue;
                                }else if (empresaVO.getMinutosCreditarProximoPontoClubeVantagens() > 0) {
                                    try {
                                        Integer codigoCliente = rs.getInt("cliente");
                                        dataTimeRegistroAnterior = getFacade().getAcessoCliente().obterUltimoAcessoAntesDe(codigoCliente, data);
                                        if (dataTimeRegistroAnterior == null)
                                            dataTimeRegistroAnterior = data;
                                    } catch (Exception ignore) {
                                        dataTimeRegistroAnterior = data;
                                    }

                                    if (jaTemPontosDateTime != null)
                                        dataTimeRegistroAnterior = jaTemPontosDateTime;

                                    long minutosEntreDatas = Math.abs(Calendario.diferencaEmMinutos(dataTimeRegistroAnterior, dataTimeRegistro));
                                    // Se o tempo entre as Datas for menor q o tempo estimado pela empresa continua vai pra o proximo item
                                    if (minutosEntreDatas <= empresaVO.getMinutosCreditarProximoPontoClubeVantagens()) {
                                        continue;
                                    }
                                }
                                codigoPessoaAnterior = rs.getInt("pessoa");
                                dataTimeRegistroAnterior = dataTimeRegistro;
                                ClienteVO clienteVO = new ClienteVO();
                                clienteVO.setCodigo(rs.getInt("cliente"));//cliente.consultarPorCodigoPessoa(codigoPessoaAnterior, Uteis.NIVELMONTARDADOS_MINIMOS);
                                try {
                                    acrescentarPontoPorAcesso(dataTimeRegistro, clienteVO, empresaVO);
                                } catch (Exception e) {
                                    Uteis.logar("ERRO AO ACRESCENTAR PONTO POR ACESSO (HistoricoPontos.java - linha 765): " + e);
                                }

                                validaEAddPontosClimaChuvosoTemperatura(arrayClima, dataTimeRegistro, clienteVO, empresaVO, urlOamd, key);
                            }
                        }

                    }
                }
            }
            Uteis.logar(null, "Pontuação de acesso inseridas com sucesso...");
        }
    }

    public static Comparator<Clima> getOrdenadorDataRegistroDecrescente() {
        return new Comparator<Clima>() {
            @Override
            public int compare(Clima o1, Clima o2) {
                return o2.getDataRegistro().compareTo(o1.getDataRegistro());
            }
        };
    }

    private List<Clima> montarDadosClima(Date data, EmpresaVO empresaVO, String urlOamd, String key) throws Exception {
        try{
            JSONArray array = new Empresa(con).obterInfoClima(urlOamd, key, empresaVO.getCodigo(), data.getTime());
            List<Clima> arrayClima = new ArrayList<Clima>();

            for(int i = 0; i < array.length(); i++){
                JSONObject json = array.getJSONObject(i);

                Clima clima = new Clima();
                clima.setDataRegistro(new Date(json.getLong("data_reg")));
                clima.setTemperatura(json.getInt("temperatura"));
                clima.setCondicao(json.getInt("condicao"));
                arrayClima.add(clima);
        }

            return arrayClima;

        }catch(JSONException ex){
            Uteis.logar(null, "Falha ao obter os dados do clima no oamd.");
            return null;
        }


    }

    private boolean validarDiasAtivosPontoPorAcesso(Date data, List<String> diasAtivos) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(data);

        String diaDaSemana = new SimpleDateFormat("EEE", new Locale("pt", "BR")).format(calendar.getTime()).toUpperCase();
        diaDaSemana = Normalizer.normalize(diaDaSemana, Normalizer.Form.NFD).replaceAll("[\\p{InCombiningDiacriticalMarks}]", "");

        if (diasAtivos.contains(diaDaSemana)) {
            return true;
        } else {
            String diasAtivosStr = String.join(",", diasAtivos);
            Uteis.logar(null, "Dias da semana que acrescentam pontos por acesso: " + diasAtivosStr);
            Uteis.logar(null, "A data " + data + " caiu em uma " + diaDaSemana + " e por isso os pontos não podem ser contabilizados");
            return false;
        }
    };

    private void acrescentarPontoPorAcesso(Date data, ClienteVO clienteVO, EmpresaVO empresaVO) throws Exception {
        List<String> diasAtivos = null;
        if(!UteisValidacao.emptyString(empresaVO.getDiasAtivosPontuacaoAcesso())) {
            diasAtivos = Arrays.asList(empresaVO.getDiasAtivosPontuacaoAcesso().split(","));
        }

        CampanhaDuracaoVO maiorCampanhaAtiva = new CampanhaDuracao(con).campanhaVigenteMultiplicador(data, TipoItemCampanhaEnum.ACESSO,empresaVO.getCodigo());

         Boolean podeIncluirPontuacao = empresaVO.getPontosAlunoAcesso() > 0
                 && empresaVO.isTrabalharComPontuacao()
                 && (!empresaVO.isPontuarApenasCategoriasEmCampanhasAtivas() || (empresaVO.isPontuarApenasCategoriasEmCampanhasAtivas()
                 && UteisValidacao.notEmptyNumber(maiorCampanhaAtiva.getCodigo())))
                 && !UteisValidacao.emptyList(diasAtivos)
                 && validarDiasAtivosPontoPorAcesso(data, diasAtivos);

        if(podeIncluirPontuacao) {
            HistoricoPontosVO historicoPontosVO = new HistoricoPontosVO();
            historicoPontosVO.setCliente(clienteVO);
            historicoPontosVO.setDataConfirmacao(data);
            historicoPontosVO.setDataaula(data);
            historicoPontosVO.setEntrada(true);
            historicoPontosVO.setTipoPonto(TipoItemCampanhaEnum.ACESSO);

            Integer pontos = (maiorCampanhaAtiva.getMultiplicador() > 0 ?
                    maiorCampanhaAtiva.getMultiplicador() * empresaVO.getPontosAlunoAcesso() : empresaVO.getPontosAlunoAcesso());
            historicoPontosVO.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
            historicoPontosVO.setDescricao("Acesso Catraca "+maiorCampanhaAtiva.getTextoCampanhaApresentar());
            historicoPontosVO.setPontos(pontos);
            if(!existe(historicoPontosVO))
                incluir(historicoPontosVO);
        }
    }

    private void validaEAddPontosClimaChuvosoTemperatura(List<Clima> arrayClima, Date data, ClienteVO clienteVO, EmpresaVO empresaVO, String urlOamd, String key) throws Exception {
        Integer temperatura = null;
        Integer condicao = null;

        if(arrayClima == null){
            return;
        }

        Collections.sort(arrayClima, getOrdenadorDataRegistroDecrescente());

        for(Clima clima : arrayClima){
            if(clima.getDataRegistro().getTime() < data.getTime()){
                temperatura = clima.getTemperatura();
                condicao = clima.getCondicao();
                break;
            }
        }

        if (temperatura == null || condicao == null) {
            Uteis.logar(null, "Temperatura e condição vazio, não será possivel adicionar os pontos de acesso clima.");
            return;
        }

        if (temperatura <= 15) {//FRIO
            acresentarPontoPorAcesso(data, clienteVO, empresaVO.getPontosAlunoAcessoFrio(), empresaVO.getCodigo(), TipoItemCampanhaEnum.ACESSOFRIO);
        }

        if (temperatura > 32) {//CALOR
            acresentarPontoPorAcesso(data, clienteVO, empresaVO.getPontosAlunoAcessoCalor(), empresaVO.getCodigo(), TipoItemCampanhaEnum.ACESSOCALOR);
        }

        if ((condicao >= 200 && condicao <= 232) || (condicao >= 300 && condicao <= 321) || (condicao >= 500 && condicao <= 531)) {//CHUVA
            acresentarPontoPorAcesso(data, clienteVO, empresaVO.getPontosAlunoAcessoChuva(), empresaVO.getCodigo(), TipoItemCampanhaEnum.ACESSOCHUVA);
        }

    }

    private void acresentarPontoPorAcesso(Date data, ClienteVO clienteVO, Integer ponto, Integer codEmpresa, TipoItemCampanhaEnum tipoCategoria) throws Exception {
        HistoricoPontosVO historicoPontosVO = new HistoricoPontosVO();
        historicoPontosVO.setCliente(clienteVO);
        historicoPontosVO.setDataConfirmacao(data);
        historicoPontosVO.setDataaula(data);
        historicoPontosVO.setEntrada(true);
        historicoPontosVO.setTipoPonto(tipoCategoria);
        CampanhaDuracaoVO maiorCampanhaAtiva = new CampanhaDuracao(con).campanhaVigenteMultiplicador(data, tipoCategoria, codEmpresa);
        historicoPontosVO.setCodigoCampanha(maiorCampanhaAtiva.getCodigo());
        historicoPontosVO.setPontos(maiorCampanhaAtiva.getMultiplicador() > 0 ? maiorCampanhaAtiva.getMultiplicador() * ponto : ponto);
        historicoPontosVO.setDescricao("Bonus " + tipoCategoria.getDescricao() + maiorCampanhaAtiva.getTextoCampanhaApresentar());
        incluir(historicoPontosVO);
    }


    private PreparedStatement getPSApp(boolean analitico,Integer cliente) throws SQLException, Exception{
        StringBuilder sql = new StringBuilder();
        if (!analitico) {
            sql.append(" SELECT sw.codigocliente as codigocliente,sw.nomecliente,max(hp.codigo) as codigohistorico from historicopontos hp").append("\n");
            sql.append(" inner join situacaoclientesinteticodw sw on sw.codigocliente = hp.cliente ").append("\n");
            sql.append(" where 1=1 ").append("\n");
            sql.append(" and hp.cliente = "+cliente).append("\n");
            sql.append(" GROUP BY sw.nomecliente,sw.codigocliente").append("\n");
            sql.append(" ORDER BY codigohistorico");
        }else{
            sql.append(" SELECT sw.codigocliente as codigocliente,sw.nomecliente,hp.codigo as codigohistorico,").append("\n");
            sql.append(" hp.descricao,hp.pontos,hp.dataconfirmacao,b.nome as nomebrinde,hp.entrada,hp.dataaula ").append("\n");
            sql.append(" from historicopontos hp ").append("\n");
            sql.append(" inner join situacaoclientesinteticodw sw on sw.codigocliente = hp.cliente ").append("\n");
            sql.append(" left join brinde b on b.codigo = hp.brinde ").append("\n");
            sql.append(" where 1=1 ").append("\n");
            sql.append(" and hp.cliente = "+cliente).append("\n");
            sql.append(" GROUP BY hp.dataconfirmacao,sw.nomecliente,sw.codigocliente,hp.codigo,b.nome,hp.descricao,hp.pontos,hp.entrada,hp.dataaula");
            sql.append(" ORDER BY hp.codigo");
        }
        return con.prepareStatement(sql.toString());
    }

    @Override
    public JSONArray consultaTotalPontosAlunosApp(String matriculaCliente,boolean analitico) throws Exception {
        Cliente cliente = new Cliente(con);
        ClienteVO clienteVO = cliente.consultarPorMatricula(matriculaCliente, false, Uteis.NIVELMONTARDADOS_MINIMOS);
        JSONArray array = new JSONArray();
        if (clienteVO != null) {
            try (ResultSet rs = getPSApp(analitico, clienteVO.getCodigo()).executeQuery()) {
                if (analitico) {
                    while (rs.next()) {
                        JSONObject json = new JSONObject();
                        json.put("codigocliente", rs.getInt("codigocliente"));
                        json.put("nomecliente", rs.getString("nomecliente"));
                        json.put("codigohistorico", rs.getInt("codigohistorico"));
                        json.put("descricao", rs.getString("descricao"));
                        json.put("pontos", rs.getInt("pontos"));
                        json.put("dataconfirmacao", rs.getDate("dataconfirmacao"));
                        json.put("dataaula", rs.getDate("dataaula"));
                        String nomeBrinde = rs.getString("nomebrinde");
                        if (nomeBrinde == null) {
                            json.put("nomebrinde", "-");
                        } else {
                            json.put("nomebrinde", nomeBrinde);
                        }
                        if (rs.getBoolean("entrada")) {
                            json.put("operacao", "Entrada");
                        } else {
                            json.put("operacao", "Saida");
                        }
                        array.put(json);
                    }
                } else {
                    while (rs.next()) {
                        JSONObject json = new JSONObject();
                        json.put("codigocliente", rs.getInt("codigocliente"));
                        json.put("nomecliente", rs.getString("nomecliente"));
                        json.put("totalpontos", obterPontosTotalPorCodigo(rs.getInt("codigohistorico")));
                        array.put(json);
                    }
                }
            }
        }

        return array;
    }

    /**
     * PROCESSO EXECUTADO DENTRO DO ROBO
     * de acordo com a configuração no cadastro da empresa pode ser zerado os pontos do usuário
     *
     * @see negocio.comuns.arquitetura.RoboVO
     * @param pessoa
     * @throws Exception
     */
    @Override
    public void zerarPontuacaoRobo(PessoaVO pessoa) throws Exception {
        try {
            StringBuilder sql = new StringBuilder("SELECT * FROM historicopontos h \n" +
                    " INNER JOIN cliente ON h.cliente = cliente.codigo \n" +
                    " INNER JOIN Pessoa ON cliente.pessoa = pessoa.codigo \n" +
                    " WHERE pessoa.codigo=" +pessoa.getCodigo()+
                    " ORDER BY 1 DESC LIMIT 1 ");
            HistoricoPontosVO historicoParaZerar = new HistoricoPontosVO();
            try (Statement stm = con.createStatement()) {
                try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                    if(tabelaResultado.next())
                        historicoParaZerar = montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
                }
            }
            // Esse processo roda dentro do ROBO caso ele fique zerado não fique gravando linhas zeradas infinitamente
            // no BD;
            if(historicoParaZerar.getPontostotal()<=0)
                return;
            historicoParaZerar.setCodigo(0);
            historicoParaZerar.setPontos(0);
            historicoParaZerar.setPontostotal(0);
            historicoParaZerar.setEntrada(false);
            historicoParaZerar.setDescricao("Reajuste de pontos por inativação do contrato");
            historicoParaZerar.setDataConfirmacao(Calendario.hoje());
            historicoParaZerar.setTipoPonto(TipoItemCampanhaEnum.AJUSTE_PONTO);
            incluir(historicoParaZerar);

        }catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<HistoricoPontosVO> consultarPontosXCategoria(ItemCampanhaVO campanha, Date dataInicial, Date dataFinal) throws Exception {
        StringBuilder sql = new StringBuilder();
        if (campanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PRODUTO) {
            sql.append(" select  p.descricao nome, tipodepontos , sum(h.pontos) pontos, 0 matricula \n" +
                    " from historicopontos h " +
                    " inner join cliente c on h.cliente = c.codigo\n" +
                    " inner join produto p on p.codigo=h.produto ");
        } else if (campanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANO) {
            sql.append(" select p.descricao nome, " + TipoItemCampanhaEnum.PLANO.getCodigo() + " tipodepontos , sum(h.pontos) pontos, 0 matricula \n" +
                    " from historicopontos h \n" +
                    " inner join cliente c on h.cliente = c.codigo \n" +
                    " inner join contrato ct on ct.codigo = h.contrato \n" +
                    " inner join plano p on p.codigo = ct.plano ");
        } else if (campanha.getTipoItemCampanha() == TipoItemCampanhaEnum.AULA) {
            sql.append(" select descricao nome, tipodepontos , sum(h.pontos) pontos, 0 matricula \n" +
                    " from historicopontos h\n" +
                    " inner join cliente c on c.codigo=h.cliente ");
        } else {
            sql.append(" select c.matricula, p.nome, h.tipodepontos,  sum(h.pontos) pontos from historicopontos h\n" +
                    " inner join cliente c on h.cliente=c.codigo\n" +
                    " inner join pessoa p on c.pessoa=p.codigo\n");
        }

        sql.append(" where ");
        sql.append(campanha.getTipoItemCampanha().equals(TipoItemCampanhaEnum.PLANO) ? " h.tipodepontos in (" + TipoItemCampanhaEnum.PLANO.getCodigo() + "," + TipoItemCampanhaEnum.PLANODURACAO.getCodigo() + ")" :
                " h.tipodepontos = " + campanha.getTipoItemCampanha().getCodigo());
        sql.append(" and h.dataconfirmacao between '" + Calendario.getDataComHoraZerada(dataInicial) + "' and '" + Calendario.getDataComUltimaHora(dataFinal) + "' ");
        sql.append((!UteisValidacao.emptyNumber(campanha.getEmpresa().getCodigo()) ? " and c.empresa=" + campanha.getEmpresa().getCodigo() : ""));
        sql.append(campanha.getTipoItemCampanha() == TipoItemCampanhaEnum.PLANO ? " group by 1 " : (
                campanha.getTipoItemCampanha() != TipoItemCampanhaEnum.PRODUTO &&
                        campanha.getTipoItemCampanha() != TipoItemCampanhaEnum.AULA ? " group by 1,2,3 " : " group by 1,2 "));
        sql.append(" order by nome ");
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_RESULTADOS_BI, con);
            }
        }
    }


    @Override
    public List<HistoricoPontosVO> consultarPontosXBrinde(BrindeVO brinde, Date dataInicial, Date dataFinal) throws Exception {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT\n");
        sql.append("\tc.matricula,\n");
        sql.append("\tp.nome,\n");
        sql.append("\th.codigo AS codigoHistorico,\n");
        sql.append("\th.tipodepontos,\n");
        sql.append("\th.pontos ,\n");
        sql.append("\th.dataconfirmacao\n");
        sql.append("FROM historicopontos h\n");
        sql.append("INNER JOIN cliente c ON h.cliente = c.codigo\n");
        sql.append("INNER JOIN pessoa p ON c.pessoa = p.codigo\n");
        sql.append("INNER JOIN brinde b ON h.brinde = b.codigo\n");
        sql.append("WHERE h.tipodepontos = ").append(TipoItemCampanhaEnum.RESGATE_BRINDE.getCodigo()).append("\n");
        sql.append("AND h.dataaula > '").append(Uteis.getDataJDBC(dataInicial)).append(" 00:00:00'").append("\n");
        sql.append("AND h.dataaula < '").append(Uteis.getDataJDBC(dataFinal)).append(" 23:59:59'").append("\n");
        sql.append("AND b.codigo = ").append(brinde.getCodigo()).append("\n");
        sql.append("AND b.empresa = ").append(brinde.getEmpresa().getCodigo()).append("\n");
        sql.append("ORDER BY dataconfirmacao DESC");

        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_RESULTADOS_BI, con);
            }
        }
    }

    @Override
    public List<HistoricoPontosVO> consultarPontosXCampanha(CampanhaDuracaoVO campanha, Date dataInicial, Date dataFinal) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select cli.matricula, p.nome, h.tipodepontos, sum(h.pontos) pontos from historicopontos h " +
                " inner join cliente cli on h.cliente=cli.codigo and cli.empresa=" +campanha.getEmpresa().getCodigo()+
                " inner join pessoa p on cli.pessoa=p.codigo " +
                " where tipodepontos in (select distinct tipoitem from itemcampanha i " +
                " inner join campanhaduracao  c on c.codigo=i.campanha and i.pontos>0  and c.codigo="+ campanha.getCodigo()+" ) " +
                " and h.dataconfirmacao between '"+Uteis.getData(dataInicial)+"' and '"+Uteis.getData(dataFinal)+"' "+
                " and pontos > 0 " +
                " and codigocampanha =" +campanha.getCodigo()+
                " group by 1,2,3 order by nome desc " );


        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sql.toString())) {
                return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_RESULTADOS_BI, con);
            }
        }
    }

    /**
     * Processo corrige processamento do Robo
     * @param args
     */
    public static void main(String... args) throws SQLException {
        Connection con = null;
        try {
            Uteis.debug = true;
            String chave = "pacto";
            if(args.length > 0){
                chave = args[0];
            }
            Date dataBase = Uteis.somarDias(Calendario.hoje(), -41);
            if(args.length > 1){
                dataBase = Uteis.getDate(args[1], "yyyyMMdd");
            }
            con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(con);
            con.setAutoCommit(false);
            Calendar c = Calendario.getInstance(dataBase);
            Uteis.logar(null, "Iniciando reprocessamento de pontos de aulas confirmadas a partir do dia: "+c.getTime());

            SuperFacadeJDBC.executarConsultaUpdate("delete from historicopontos where tipodepontos in (1) and dataaula >= '" + Calendario.getData(c.getTime(), "yyyy-MM-dd") +"'", con);

            HistoricoPontos hp = new HistoricoPontos(con);

            do{
                hp.acresentarPontoPorAulaComfirmadaRobo(c.getTime());
                c.add(Calendar.DAY_OF_MONTH, 1);
            }while (Calendario.menor(c.getTime(), Calendario.hoje()));
            con.commit();
            Uteis.logar(null, "Finalizando reprocessamento de pontos de aulas confirmadas a partir do dia: "+c.getTime());
        } catch (Exception ex) {
            if(con != null){
                con.rollback();
                con.close();
            }
            Uteis.logar(null, ex.getMessage());
        }
    }

}
