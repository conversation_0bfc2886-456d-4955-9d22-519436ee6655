package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ProdutoDevolverCancelamentoEmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.financeiro.ProdutoDevolverCancelamentoEmpresaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


public class ProdutoDevolverCancelamentoEmpresa extends SuperEntidade implements ProdutoDevolverCancelamentoEmpresaInterfaceFacade {

    public ProdutoDevolverCancelamentoEmpresa() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ProdutoDevolverCancelamentoEmpresa(Connection con) throws Exception {
    	super(con);        
        setIdEntidade("Empresa");
	}

    public ProdutoDevolverCancelamentoEmpresaVO novo() throws Exception {
        incluir(getIdEntidade());
        ProdutoDevolverCancelamentoEmpresaVO obj = new ProdutoDevolverCancelamentoEmpresaVO();
        return obj;
    }

    public void incluir(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception {
        try {
            ProdutoDevolverCancelamentoEmpresaVO.validarDados(obj);
            incluir(getIdEntidade());
            String sql = "INSERT INTO ProdutoDevolverCancelamentoEmpresa( empresa, produto ) VALUES ( ?, ? )";
            try (PreparedStatement sqlInserir = con.prepareStatement(sql)) {
                if (!UteisValidacao.emptyNumber(obj.getEmpresa())) {
                    sqlInserir.setInt(1, obj.getEmpresa());
                } else {
                    sqlInserir.setNull(1, 0);
                }
                if (!UteisValidacao.emptyNumber(obj.getProduto())) {
                    sqlInserir.setInt(2, obj.getProduto());
                } else {
                    sqlInserir.setNull(2, 0);
                }
                sqlInserir.execute();
            }
            obj.setCodigo(obterValorChavePrimariaCodigo());
            obj.setNovoObj(new Boolean(false));
        } catch (Exception e) {
            System.out.println(e.getMessage());
            throw e;
        }
    }

    public void alterar(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception {
        ProdutoDevolverCancelamentoEmpresaVO.validarDados(obj);
        alterar(getIdEntidade());
        String sql = "UPDATE ProdutoDevolverCancelamentoEmpresa set empresa=?, produto=? WHERE ((codigo = ?))";
        try (PreparedStatement sqlAlterar = con.prepareStatement(sql)) {
            if (!UteisValidacao.emptyNumber(obj.getEmpresa())) {
                sqlAlterar.setInt(1, obj.getEmpresa());
            } else {
                sqlAlterar.setNull(1, 0);
            }
            if (!UteisValidacao.emptyNumber(obj.getProduto())) {
                sqlAlterar.setInt(2, obj.getProduto());
            } else {
                sqlAlterar.setNull(2, 0);
            }
            sqlAlterar.setInt(3, obj.getCodigo().intValue());
            sqlAlterar.execute();
        }
    }

    public void excluir(ProdutoDevolverCancelamentoEmpresaVO obj) throws Exception {
        excluir(getIdEntidade());
        String sql = "DELETE FROM ProdutoDevolverCancelamentoEmpresa WHERE ((codigo = ?))";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, obj.getCodigo().intValue());
            sqlExcluir.execute();
        }
    }

    public List consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), true);
        String sqlStr = "SELECT * FROM ProdutoDevolverCancelamentoEmpresa WHERE empresa = "+empresa;
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con);
            }
        }
    }

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = "SELECT * FROM ContaCorrenteEmpresa WHERE codigo >= " + valorConsulta.intValue() + " ORDER BY codigo";
        try (Statement stm = con.createStatement()) {
            try (ResultSet tabelaResultado = stm.executeQuery(sqlStr)) {
                return (montarDadosConsulta(tabelaResultado, nivelMontarDados, this.con));
            }
        }
    }

    public static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            ProdutoDevolverCancelamentoEmpresaVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public static ProdutoDevolverCancelamentoEmpresaVO montarDados(ResultSet dadosSQL, int nivelMontarDados, Connection con) throws Exception {
        ProdutoDevolverCancelamentoEmpresaVO obj = new ProdutoDevolverCancelamentoEmpresaVO();
        obj.setCodigo(new Integer(dadosSQL.getInt("codigo")));
        obj.setEmpresa(new Integer(dadosSQL.getInt("empresa")));
        obj.setProduto(new Integer(dadosSQL.getInt("produto")));
        obj.setNovoObj(new Boolean(false));
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }

        montarDadosProduto(obj, nivelMontarDados, con);
        return obj;
    }

    public static void montarDadosProduto(ProdutoDevolverCancelamentoEmpresaVO obj, int nivelMontarDados, Connection con) throws Exception {
        if (obj.getProduto() == 0) {
            obj.setProdutoVO(new ProdutoVO());
            return;
        }
        Produto produtoDAO = new Produto(con);
        obj.setProdutoVO(produtoDAO.consultarPorChavePrimaria(obj.getProduto(), nivelMontarDados));
        produtoDAO = null;
    }

    public void alterarProdutosDevolverCancelEmpresas(Integer empresa,
                                                      List<ProdutoDevolverCancelamentoEmpresaVO> objetos) throws Exception {
        String codigoAtuais = "";
        if(!UteisValidacao.emptyList(objetos)) {
            for (ProdutoDevolverCancelamentoEmpresaVO obj : objetos) {
                if(UteisValidacao.emptyNumber(obj.getCodigo())){
                    incluir(obj);
                } else {
                    alterar(obj);
                }
                codigoAtuais += ","+obj.getCodigo();
            }
            excluirProdutosDevolverCancelEmpresaCodigosAtuais(empresa, codigoAtuais.replaceFirst(",", ""));
        }
    }

    public void excluirProdutosDevolverCancelEmpresaCodigosAtuais(final Integer codEmpresa,final String codigosAtuais) throws Exception {
        String sql = "DELETE FROM ProdutoDevolverCancelamentoEmpresa WHERE (empresa = ?) ";
        if(!UteisValidacao.emptyString(codigosAtuais)){
            sql += " and codigo not in ("+codigosAtuais+")";
        }
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, codEmpresa);
            sqlExcluir.execute();
        }
    }

    public void incluirProdutosDevolverCancelEmpresa(List objetos) throws Exception {
        Iterator e = objetos.iterator();
        while (e.hasNext()) {
            ProdutoDevolverCancelamentoEmpresaVO obj = (ProdutoDevolverCancelamentoEmpresaVO) e.next();
            incluir(obj);
        }
    }
}
