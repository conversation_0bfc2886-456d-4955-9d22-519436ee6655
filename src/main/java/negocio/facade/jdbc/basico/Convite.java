package negocio.facade.jdbc.basico;

import br.com.pactosolucoes.comuns.util.Declaracao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ConviteVO;
import negocio.comuns.financeiro.FreePassVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.plano.Produto;
import negocio.interfaces.basico.ConviteInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Convite extends SuperEntidade implements ConviteInterfaceFacade {

    public Convite() throws Exception {
        super();
    }

    public Convite(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(ConviteVO convite) throws Exception{
        PreparedStatement stm = con.prepareStatement("INSERT INTO convite(\n" +
                "            convidado, convidou, dia, plano, faltalancarfreepass)\n" +
                "    VALUES (?, ?, ?, ?, ?);");
        int i = 1;
        stm.setInt(i++, convite.getConvidado().getCodigo());
        stm.setInt(i++, convite.getConvidou().getCodigo());
        stm.setDate(i++, Uteis.getDataJDBC(convite.getDia()));
        stm.setInt(i++, convite.getPlano().getCodigo());
        stm.setBoolean(i++, convite.isFaltalancarfreepass());

        stm.execute();
    }


    public List<ConviteVO> historico(Integer convidou) throws Exception{
        List<ConviteVO> historico = new ArrayList<ConviteVO>();
        String sql = "SELECT pes.nome, c.dia, c.convidado, c.codigo, cli.situacao from convite c " +
                " inner join cliente cli on cli.codigo = c.convidado " +
                " inner join pessoa pes on pes.codigo = cli.pessoa " +
                " where convidou = " + convidou + " order by c.codigo desc ";
        ResultSet rs = criarConsulta(sql, con);
        while(rs.next()){
            ConviteVO convite = new ConviteVO();
            convite.setCodigo(rs.getInt("codigo"));
            convite.setDia(rs.getDate("dia"));
            convite.setSituacao(rs.getString("situacao"));
            convite.setConvidado(new ClienteVO());
            convite.getConvidado().setCodigo(rs.getInt("convidado"));
            convite.getConvidado().getPessoa().setNome(rs.getString("nome"));
            historico.add(convite);
        }
        return historico;
    }

    public Integer convitesDireito(Integer contrato) throws Exception{
        ResultSet rs = criarConsulta("SELECT convidadospormes from plano p inner join contrato c on c.plano = p.codigo where c.situacao = 'AT' AND c.codigo = " + contrato, con);
        Integer qtdConvitesPlano = rs.next() ? rs.getInt("convidadospormes") : 0 ;

        ResultSet rsDiaria = criarConsulta("select p.qtdconvites from movproduto mp\n" +
                "left join produto p ON mp.produto = p.codigo\n" +
                "left join contrato c ON mp.pessoa = c.pessoa\n" +
                "where c.codigo = " + contrato +"\n" +
                "and mp.datafinalvigencia::date = '" + Uteis.getDataJDBC(Calendario.hoje()) +"'", con);
        Integer qtdConvitesDiaria = rsDiaria.next() ? rsDiaria.getInt("qtdconvites") : 0 ;
        return qtdConvitesPlano + qtdConvitesDiaria;
    }

    public Boolean jaConvidou(Integer cliente) throws Exception{
        ResultSet rs = criarConsulta("select count(codigo) as cont from convite  where convidou  = " + cliente,
                con);
        return rs.next() ? rs.getInt("cont") > 0 : false ;

    }

    public void lancarConviteValidando(UsuarioVO logado, ClienteVO convidado, ClienteVO anfitriao) throws Exception{
        Cliente clienteDao = new Cliente(con);
        convidado = clienteDao.consultarPorChavePrimaria(convidado.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        validarConvite(anfitriao.getCodigo());
        lancarConvite(logado, convidado, anfitriao);

    }

    public void validarConvite(Integer cliente) throws Exception{
        ResultSet rs = criarConsulta("SELECT convidadospormes from plano p " +
                        " inner join contrato c on c.plano = p.codigo " +
                        " inner join situacaoclientesinteticodw sw on sw.codigocontrato = c.codigo " +
                        " where sw.codigocliente = " + cliente,
                con);
        Integer convitesDireito = rs.next() ? rs.getInt("convidadospormes") : 0;

        ResultSet rsDiaria = criarConsulta("select p.qtdconvites from movproduto mp\n" +
                "left join produto p ON mp.produto = p.codigo\n" +
                "left join cliente c ON mp.pessoa = c.pessoa\n" +
                "where c.codigo = " + cliente + "\n" +
                "and mp.datafinalvigencia::date = '" + Uteis.getDataJDBC(Calendario.hoje()) + "'", con);
        Integer qtdConvitesDiaria = rsDiaria.next() ? rsDiaria.getInt("qtdconvites") : 0;

        Integer totalConvitesDireito = convitesDireito + qtdConvitesDiaria;

        if (totalConvitesDireito == 0) {
            throw new Exception("Seu plano atual não te dá direito a convites.");
        }
        Integer convitesMes = totalizarMes(cliente);
        if (convitesMes >= totalConvitesDireito) {
            throw new Exception("Você não tem mais convites para distribuir esse mês.");
        }
    }

    public void lancarConvite(UsuarioVO logado, ClienteVO convidado, ClienteVO anfitriao) throws Exception {
        Cliente clienteDAO;
        Produto produtoDAO;
        try {
            clienteDAO = new Cliente(this.con);
            produtoDAO = new Produto(this.con);

            if (anfitriao.getEmpresa().getRestringirConvidadoUmaVezPorMes()) {
                boolean existeConviteMes = existeConviteNoMes(convidado, anfitriao, Calendario.hoje());
                if (existeConviteMes) {
                    throw new ConsistirException("O Aluno " + anfitriao.getNome_Apresentar() +
                            " já convidou " + convidado.getNome_Apresentar() + " este mês.");
                }
            }

            boolean convidadoRecebeuConviteHoje = validarSeConvidadoNoDia(convidado, Calendario.hoje());
            if (convidadoRecebeuConviteHoje) {
                throw new ConsistirException("O aluno convidado " + convidado.getNome_Apresentar() +
                        " já possui um convite lançado para o dia de hoje.");
            }

            convidado.setResponsavelFreePass(logado.getCodigo());
            convidado.setFreePass(produtoDAO.criarOuConsultarProdutoPorTipoNrDiasVigencia(TipoProduto.FREEPASS.getCodigo(), 1, "CONVITE", Uteis.NIVELMONTARDADOS_MINIMOS));
            ConviteVO convite = new ConviteVO();
            convite.setConvidado(convidado);
            convite.setConvidou(anfitriao);
            convite.setPlano(new PlanoVO());
            convite.setDia(Calendario.hoje());
            clienteDAO.incluirFreePass(convidado, Calendario.hoje(), null, null, null);
            incluir(convite);
        } finally {
            clienteDAO = null;
            produtoDAO = null;
        }
    }

    public Integer totalizarMes(Integer convidou) throws Exception{
        PreparedStatement stm = getCon().prepareStatement("select count(codigo) as total from convite where dia between ? and ? and convidou = ? ");
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(Uteis.obterPrimeiroDiaMes(Calendario.hoje()))));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Uteis.obterUltimoDiaMesUltimaHora(Calendario.hoje())));
        stm.setInt(3, convidou);
        ResultSet resultSet = stm.executeQuery();
        return resultSet.next() ? resultSet.getInt("total") : 0;
    }

    public List<ConviteVO> relatorio(Date inicio, Date fim, Integer empresa) throws Exception{
        List<ConviteVO> historico = new ArrayList<ConviteVO>();

        String sql = "SELECT pes.nome, c.dia, c.convidado, c.codigo, cli.situacao, pesa.nome as anfitriao from convite c " +
                " inner join cliente cli on cli.codigo = c.convidado " +
                " inner join pessoa pes on pes.codigo = cli.pessoa " +
                " inner join cliente clia on clia.codigo = c.convidou " +
                " inner join pessoa pesa on pesa.codigo = clia.pessoa " +
                " where dia between ? and ? " +
                ( empresa > 0 ? " and clia.empresa = ?" : "") +
                " order by c.codigo desc ";

        PreparedStatement stm = getCon().prepareStatement(sql);
        stm.setTimestamp(1, Uteis.getDataJDBCTimestamp(Calendario.getDataComHoraZerada(inicio)));
        stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Uteis.obterUltimoDiaMesUltimaHora(fim)));
        if(empresa > 0){
            stm.setInt(3, empresa);
        }

        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            ConviteVO convite = new ConviteVO();
            convite.setCodigo(rs.getInt("codigo"));
            convite.setDia(rs.getDate("dia"));
            convite.setConvidado(new ClienteVO());
            convite.setConvidou(new ClienteVO());
            convite.setSituacao(rs.getString("situacao"));
            convite.getConvidado().setCodigo(rs.getInt("convidado"));
            convite.getConvidado().getPessoa().setNome(rs.getString("nome"));
            convite.getConvidou().getPessoa().setNome(rs.getString("anfitriao"));
            historico.add(convite);
        }

        return historico;
    }

    public boolean validarSeConvidadoNoDia(ClienteVO convidado, Date hoje) throws SQLException {
        String sql = "select codigo from convite c \n" +
                "where dia::date = ?\n" +
                "and convidado = ?\n";

        try (PreparedStatement stm = getCon().prepareStatement(sql)) {
            stm.setDate(1, Uteis.getDataJDBC(hoje));
            stm.setInt(2, convidado.getCodigo());

            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean existeConviteNoMes(ClienteVO convidado, ClienteVO anfitriao, Date hoje) throws SQLException {
        String sql = "select codigo from convite c \n" +
                "where to_char(dia, 'MM/YYYY') = ?\n" +
                "and convidado = ?\n" +
                "and convidou = ?\n";

        try (PreparedStatement stm = getCon().prepareStatement(sql)) {
            stm.setString(1, Uteis.getMesReferenciaData(hoje));
            stm.setInt(2, convidado.getCodigo());
            stm.setInt(3, anfitriao.getCodigo());

            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.next();
                }
            }
        }

        return false;
    }

    public void excluir(FreePassVO freePassVO) throws SQLException {
        String sql = "DELETE FROM convite WHERE convidado = ? AND dia::date = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setInt(1, freePassVO.getClienteVO().getCodigo());
            sqlExcluir.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
            sqlExcluir.execute();
        }

    }

    public boolean faltaLancarAcessoConvidado(ClienteVO convidado) throws SQLException {
        String sql = "SELECT EXISTS\n" +
                "(\n" +
                "select codigo from convite where faltalancarfreepass = true\n" +
                "and convidado = ?" +
                ");";
        try (PreparedStatement stm = getCon().prepareStatement(sql)) {
            stm.setInt(1, convidado.getCodigo());
            try (ResultSet rs = stm.executeQuery()) {
                if (rs.next()) {
                    return rs.getBoolean("exists");
                }
            }
        }
        return false;
    }

    public void atualizarFaltaAcessoConvidado(ClienteVO convidado) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE convite SET faltalancarfreepass = false ");
        sql.append("WHERE convidado = ?");
        Declaracao stm = new Declaracao(sql.toString(), this.con);
        stm.setInt(1, convidado.getCodigo());
        stm.execute();
    }

}
