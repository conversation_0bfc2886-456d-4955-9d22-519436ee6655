package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ParceiroFidelidadeInterfaceFacade;
import negocio.interfaces.basico.ProdutoParceiroFidelidadeInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ProdutoParceiroFidelidade extends SuperEntidade implements ProdutoParceiroFidelidadeInterfaceFacade {

    public ProdutoParceiroFidelidade() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public ProdutoParceiroFidelidade(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    @Override
    public ProdutoParceiroFidelidadeVO novo() throws Exception {
        incluir(getIdEntidade());
        return new ProdutoParceiroFidelidadeVO();
    }

    @Override
    public void incluir(ProdutoParceiroFidelidadeVO obj) throws Exception {
        ProdutoParceiroFidelidadeVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO ProdutoParceiroFidelidade(descricao, pontos, valor, parceirofidelidade, codigoExterno) VALUES (?,?,?,?,?)";
        int i = 0;
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(++i, obj.getDescricao());
        ps.setInt(++i, obj.getPontos());
        ps.setDouble(++i, obj.getValor());
        ps.setInt(++i, obj.getParceiroFidelidade().getCodigo());
        ps.setString(++i, obj.getCodigoExterno());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    @Override
    public void alterar(ProdutoParceiroFidelidadeVO obj) throws Exception {
        ProdutoParceiroFidelidadeVO.validarDados(obj);
        obj.realizarUpperCaseDados();
        String sql = "UPDATE ProdutoParceiroFidelidade set descricao=?, pontos=?, valor=?, parceirofidelidade=?, codigoExterno = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setString(++i, obj.getDescricao());
        ps.setInt(++i, obj.getPontos());
        ps.setDouble(++i, obj.getValor());
        ps.setInt(++i, obj.getParceiroFidelidade().getCodigo());
        ps.setString(++i, obj.getCodigoExterno());
        ps.setInt(++i, obj.getCodigo());
        ps.execute();
    }

    @Override
    public void excluir(ProdutoParceiroFidelidadeVO obj) throws Exception {
        String sql = "DELETE FROM ProdutoParceiroFidelidade WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, obj.getCodigo());
        ps.execute();
    }

    @Override
    public List<ProdutoParceiroFidelidadeVO> consultarPorParceiro(final Integer codigoParceiro, int nivelMontarDados) throws Exception {
        String sqlStr = "SELECT * FROM ProdutoParceiroFidelidade WHERE parceirofidelidade = " + codigoParceiro + " ORDER BY pontos,valor ";
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public List<ProdutoParceiroFidelidadeVO> consultarPorParceiroMaximoPontos(final Integer codigoParceiro, final Integer maximoPontos, boolean somenteComCodigoExterno, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ProdutoParceiroFidelidade \n");
        sql.append("WHERE parceirofidelidade = ").append(codigoParceiro).append(" \n");
        if (somenteComCodigoExterno) {
            sql.append("and codigoExterno <> '' \n");
        }
        if (!UteisValidacao.emptyNumber(maximoPontos)) {
            sql.append("and pontos <= ").append(maximoPontos).append(" \n");
        }
        sql.append("ORDER BY pontos,valor ");
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sql.toString());
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }

    @Override
    public ProdutoParceiroFidelidadeVO consultarPorParceiroPontos(final Integer codigoParceiro, Integer pontos, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ProdutoParceiroFidelidade WHERE parceirofidelidade = ? and pontos = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, codigoParceiro);
        ps.setInt(++i, pontos);
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            return new ProdutoParceiroFidelidadeVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    @Override
    public ProdutoParceiroFidelidadeVO consultarPorParceiroPontosCodigoExterno(final Integer codigoParceiro, Integer pontos, String codigoExterno, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ProdutoParceiroFidelidade \n");
        sql.append("WHERE parceirofidelidade = ").append(codigoParceiro).append(" \n");
        if (!UteisValidacao.emptyNumber(pontos)) {
            sql.append("and pontos = ").append(pontos).append(" \n");
        }
        if (!UteisValidacao.emptyString(codigoExterno)) {
            sql.append("and codigoExterno = '").append(codigoExterno).append("' \n");
        }
        PreparedStatement ps = con.prepareStatement(sql.toString());
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            return new ProdutoParceiroFidelidadeVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public List<ProdutoParceiroFidelidadeVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<ProdutoParceiroFidelidadeVO> vetResultado = new ArrayList<ProdutoParceiroFidelidadeVO>();
        while (tabelaResultado.next()) {
            ProdutoParceiroFidelidadeVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public ProdutoParceiroFidelidadeVO montarDados(ResultSet rs, int nivelMontarDados) throws Exception {
        ProdutoParceiroFidelidadeVO obj = new ProdutoParceiroFidelidadeVO();
        obj.setCodigo(rs.getInt("codigo"));
        obj.setDescricao(rs.getString("descricao"));
        obj.setPontos(rs.getInt("pontos"));
        obj.setValor(rs.getDouble("valor"));
        obj.setCodigoExterno(rs.getString("codigoExterno"));

        ParceiroFidelidadeInterfaceFacade parceiroDao = new ParceiroFidelidade(con);
        obj.setParceiroFidelidade(parceiroDao.consultarPorChavePrimaria(rs.getInt("parceirofidelidade"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        parceiroDao = null;

        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }

    @Override
    public ProdutoParceiroFidelidadeVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM ProdutoParceiroFidelidade WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 0;
        ps.setInt(++i, codigoPrm);
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( ProdutoParceiroFidelidade ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }
}