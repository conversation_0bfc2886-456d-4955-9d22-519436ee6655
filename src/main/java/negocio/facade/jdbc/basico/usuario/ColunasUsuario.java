package negocio.facade.jdbc.basico.usuario;

import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * Coluna prefixável para consultas relacionada a entidade {@link Usuario}.
 *
 * <AUTHOR>
 * @since 21/01/2019
 */
public enum ColunasUsuario implements ColunaPrefixavel {

    CODIGO("codigo"),
    NOME("nome")
    ;

    private static final String NOME_TABELA = "Usuario";
    private static final String CONTRATO_PREFIXO = NOME_TABELA.toLowerCase();
    private final String label;

    ColunasUsuario(String label) {
        this.label = label;
    }

    public static String getPrefixo() {
        return CONTRATO_PREFIXO;
    }

    public static String getNomeTabela() {
        return NOME_TABELA;
    }

    public static String getNomeTabelaComPrefixo() {
        return getNomeTabela() + " " + getPrefixo();
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return CONTRATO_PREFIXO + "." + label;
    }

}
