
package negocio.facade.jdbc.basico;


import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.basico.TabelaParceiroFidelidadeItemVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TabelaParceiroFidelidadeItemInterfaceFacade;


/**
 * <AUTHOR>
 */

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados da classe <code>TabelaParceiroFidelidadeItemVO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar pertinentes a classe <code>TabelaParceiroFidelidadeItemVO</code>.
 * Encapsula toda a interação com o banco de dados.
 * @see ParceiroFidelidadeItemVO
 * @see SuperEntidade
 */
public class TabelaParceiroFidelidadeItem extends SuperEntidade implements TabelaParceiroFidelidadeItemInterfaceFacade {

    public TabelaParceiroFidelidadeItem() throws Exception {
        super();
        setIdEntidade("Empresa");
    }

    public TabelaParceiroFidelidadeItem(Connection con) throws Exception {
        super(con);
        setIdEntidade("Empresa");
    }

    /**
     * Operação responsável por retornar um novo objeto da classe <code>TabelaParceiroFidelidadeItemVO</code>.
     */
    @Override
    public TabelaParceiroFidelidadeItemVO novo() throws Exception {
        incluir(getIdEntidade());
        return new TabelaParceiroFidelidadeItemVO();
    }

    /**
     * Operação responsável por incluir no banco de dados um objeto da classe <code>TabelaParceiroFidelidadeItemVO</code>.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>incluir</code> da superclasse.
     * @param obj  Objeto da classe <code>TabelaParceiroFidelidadeItemVO</code> que será gravado no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void incluir(TabelaParceiroFidelidadeItemVO obj) throws Exception {
        TabelaParceiroFidelidadeItemVO.validarDados(obj);
        incluir(getIdEntidade());
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO TabelaParceiroFidelidadeItem(tabelaParceiroFidelidade, valorInicio, valorFim, multiplicador) VALUES ( ?,?,?,? )";
        int i = 1;
        PreparedStatement ps = con.prepareStatement(sql);
        ps = resolveFKNull(ps, i++, obj.getTabelaParceiro().getCodigo());
        ps.setDouble(i++, obj.getValorInicio());
        ps.setDouble(i++, obj.getValorFim());
        ps.setDouble(i++, obj.getMultiplicador());
        ps.execute();
        obj.setCodigo(obterValorChavePrimariaCodigo());
        obj.setNovoObj(false);
    }

    /**
     * Operação responsável por alterar no BD os dados de um objeto da classe <code>TabelaParceiroFidelidadeItemVO</code>.
     * Sempre utiliza a chave primária da classe como atributo para localização do registro a ser alterado.
     * Primeiramente valida os dados (<code>validarDados</code>) do objeto. Verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>alterar</code> da superclasse.
     * @param obj    Objeto da classe <code>TabelaParceiroFidelidadeItemVO</code> que será alterada no banco de dados.
     * @exception Exception Caso haja problemas de conexão, restrição de acesso ou validação de dados.
     */
    @Override
    public void alterar(TabelaParceiroFidelidadeItemVO obj) throws Exception {
        try {
            TabelaParceiroFidelidadeItemVO.validarDados(obj);
            alterar(getIdEntidade());
            obj.realizarUpperCaseDados();
            String sql = "UPDATE TabelaParceiroFidelidadeItem set tabelaParceiroFidelidade=?, valorInicio=?, valorFim=?, multiplicador=?  WHERE ((codigo = ?))";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps = resolveFKNull(ps, i++, obj.getTabelaParceiro().getCodigo());
            ps.setDouble(i++, obj.getValorInicio());
            ps.setDouble(i++, obj.getValorFim());
            ps.setDouble(i++, obj.getMultiplicador());
            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Operação responsável por excluir no BD um objeto da classe <code>TabelaParceiroFidelidadeItemVO</code>.
     * Sempre localiza o registro a ser excluído através da chave primária da entidade.
     * Primeiramente verifica a conexão com o banco de dados e a permissão do usuário
     * para realizar esta operacão na entidade.
     * Isto, através da operação <code>excluir</code> da superclasse.
     * @param obj    Objeto da classe <code>TabelaParceiroFidelidadeItemVO</code> que será removido no banco de dados.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public void excluir(TabelaParceiroFidelidadeItemVO obj) throws Exception {
        try {
            excluir(getIdEntidade());
            String sql = "DELETE FROM TabelaParceiroFidelidadeItem WHERE codigo = ?";
            PreparedStatement ps = con.prepareStatement(sql);
            int i = 1;
            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * Responsável por realizar uma consulta de <code>ParceiroFidelidadeItem</code> através do valor do atributo
     * <code>String nome</code>. Retorna os objetos, com início do valor do atributo idêntico ao parâmetro fornecido.
     * Faz uso da operação <code>montarDadosConsulta</code> que realiza o trabalho de prerarar o List resultante.
     * @param codigoTabelaParceiro
     * @param   controlarAcesso Indica se a aplicação deverá verificar se o usuário possui permissão para esta consulta ou não.
     * @param nivelMontarDados
     * @return List Contendo vários objetos da classe <code>TabelaParceiroFidelidadeItemVO</code> resultantes da consulta.
     * @exception Exception Caso haja problemas de conexão ou restrição de acesso.
     */
    @Override
    public List consultarPorTabelaParceiro(final Integer codigoTabelaParceiro, boolean controlarAcesso, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), controlarAcesso);
        String sqlStr = String.format("SELECT * FROM TabelaParceiroFidelidadeItem WHERE tabelaParceiroFidelidade = %s ORDER BY valorInicio, valorFim", codigoTabelaParceiro);
        Statement stm = con.createStatement();
        ResultSet tabelaResultado = stm.executeQuery(sqlStr);
        return montarDadosConsulta(tabelaResultado, nivelMontarDados);
    }


    /**
     * Responsável por montar os dados de vários objetos, resultantes de uma consulta ao banco de dados (<code>ResultSet</code>).
     * Faz uso da operação <code>montarDados</code> que realiza o trabalho para um objeto por vez.
     * @param tabelaResultado
     * @param nivelMontarDados
     * @return List Contendo vários objetos da classe <code>TabelaParceiroFidelidadeItemVO</code> resultantes da consulta.
     * @throws java.lang.Exception
     */
    public List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            TabelaParceiroFidelidadeItemVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    /**
     * Responsável por montar os dados resultantes de uma consulta ao banco de dados (<code>ResultSet</code>)
     * em um objeto da classe <code>TabelaParceiroFidelidadeItemVO</code>.
     * @param dadosSQL
     * @param nivelMontarDados
     * @return O objeto da classe <code>TabelaParceiroFidelidadeItemVO</code> com os dados devidamente montados.
     * @throws java.lang.Exception
     */
    public TabelaParceiroFidelidadeItemVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        TabelaParceiroFidelidadeItemVO obj = new TabelaParceiroFidelidadeItemVO();
        obj.setCodigo(dadosSQL.getInt("codigo"));
        obj.setValorInicio(dadosSQL.getDouble("valorInicio"));
        obj.setValorFim(dadosSQL.getDouble("valorFim"));
        obj.setMultiplicador(dadosSQL.getDouble("multiplicador"));
        TabelaParceiroFidelidade tabelaParceiroDao = new TabelaParceiroFidelidade(con);
        obj.setTabelaParceiro(tabelaParceiroDao.consultarPorChavePrimaria(dadosSQL.getInt("tabelaParceiroFidelidade"),
                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        tabelaParceiroDao = null;
        obj.setNovoObj(false);
        return obj;
    }

    /**
     * Operação responsável por localizar um objeto da classe <code>TabelaParceiroFidelidadeItemVO</code>
     * através de sua chave primária.
     * @param codigoPrm
     * @param nivelMontarDados
     * @return
     * @exception Exception Caso haja problemas de conexão ou localização do objeto procurado.
     */
    @Override
    public TabelaParceiroFidelidadeItemVO consultarPorChavePrimaria(Integer codigoPrm, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "SELECT * FROM TabelaParceiroFidelidadeItem WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, codigoPrm);
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException("Dados Não Encontrados ( TabelaParceiroFidelidadeItem ).");
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public TabelaParceiroFidelidadeItemVO consultarPorTabelaParceiroFidelidadeValor(Integer tabelaParceiroFidelidade, Double valorBase, int nivelMontarDados) throws Exception {
        consultar(getIdEntidade(), false);
        String sql = "select \n" +
                "*\n" +
                "from tabelaparceirofidelidadeitem   \n" +
                "where tabelaparceirofidelidade = ? \n" +
                "and ? between valorinicio and valorfim";
        PreparedStatement ps = con.prepareStatement(sql);
        int i = 1;
        ps.setInt(i++, tabelaParceiroFidelidade);
        ps.setDouble(i++, valorBase);
        ResultSet tabelaResultado = ps.executeQuery();
        if (!tabelaResultado.next()) {
            return new TabelaParceiroFidelidadeItemVO();
        }
        return (montarDados(tabelaResultado, nivelMontarDados));
    }

    public Integer calcularValorPontos(Integer tabelaParceiroFidelidade, Double valorBase) throws Exception {
        TabelaParceiroFidelidadeItemVO itemVO = consultarPorTabelaParceiroFidelidadeValor(tabelaParceiroFidelidade, valorBase, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (!UteisValidacao.emptyNumber(itemVO.getCodigo())) {
            Double valorPontos = Math.abs(valorBase * itemVO.getMultiplicador());
            return valorPontos.intValue();
        } else {
            throw new Exception("Tabela Dotz acumular: não foi encontrado um multiplicador equivalente para " + Formatador.formatarValorMonetario(valorBase));
        }
    }

}