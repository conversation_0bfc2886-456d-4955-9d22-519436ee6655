package negocio.facade.jdbc.basico;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import negocio.comuns.crm.FaixaHorarioAcessoClienteVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.FaixaHorarioAcessoClienteInterfaceFacade;

public class FaixaHorarioAcessoCliente extends SuperEntidade implements FaixaHorarioAcessoClienteInterfaceFacade {

	public FaixaHorarioAcessoCliente() throws Exception {
		super();
	}

    public FaixaHorarioAcessoCliente(Connection conexao) throws Exception {
        super(conexao);
    }

	@Override
	public List<FaixaHorarioAcessoClienteVO> consultarFaixas() throws Exception {
		ResultSet consulta = FaixaHorarioAcessoCliente.criarConsulta("SELECT * FROM faixahorarioacessocliente", getCon());
		
		return montarDadosConsulta(consulta);
	}
	
	public static List<FaixaHorarioAcessoClienteVO> montarDadosConsulta(ResultSet tabelaResultado) throws Exception {
		List<FaixaHorarioAcessoClienteVO> vetResultado = new ArrayList<FaixaHorarioAcessoClienteVO>();
		while (tabelaResultado.next()) {
			FaixaHorarioAcessoClienteVO obj = montarDados(tabelaResultado);
			vetResultado.add(obj);
		}
		return vetResultado;
	}
	
	/**
	 * <AUTHOR>
	 * 05/10/2011
	 */
	public static FaixaHorarioAcessoClienteVO montarDados(ResultSet rs) throws Exception{
		FaixaHorarioAcessoClienteVO faixa = new FaixaHorarioAcessoClienteVO();
		faixa.setCodigo(rs.getInt("codigo"));
		faixa.setNomePeriodo(rs.getString("descricao"));
		faixa.setHoraInicial(rs.getString("horarioinicio"));
		faixa.setHoraFinal(rs.getString("horariofim"));
		return faixa;
	}
	
	/**
	 *  
	 * <AUTHOR>
	 * 05/10/2011
	 */
	private void incluirSemCommit(FaixaHorarioAcessoClienteVO faixa) throws Exception{
		String sql = "INSERT INTO faixahorarioacessocliente (descricao, horarioinicio, horariofim) VALUES (?,?,?)";
		PreparedStatement sqlInserir = con.prepareStatement(sql);
		int i = 1;
		sqlInserir.setString(i++, faixa.getNomePeriodo());
		sqlInserir.setString(i++, faixa.getHoraInicial());
		sqlInserir.setString(i++, faixa.getHoraFinal());
		
		sqlInserir.execute();
		faixa.setCodigo(obterValorChavePrimariaCodigo());
		
	}

	/* (non-Javadoc)
	 * @see negocio.interfaces.basico.FaixaHorarioAcessoClienteInterfaceFacade#incluir(java.util.List)
	 */
	@Override
	public void incluir(List<FaixaHorarioAcessoClienteVO> faixas) throws Exception {
		try{
			con.setAutoCommit(false);
			FaixaHorarioAcessoCliente.executarConsulta("DELETE FROM faixahorarioacessocliente;", con);
			for(FaixaHorarioAcessoClienteVO faixa : faixas){
				incluirSemCommit(faixa);
			}
			con.commit();
		}catch (Exception e) {
			con.rollback();
			throw e;
		}finally{
			con.setAutoCommit(true);
		}

	}

}
