package negocio.facade.jdbc.basico.renovacaosintetico;

import negocio.facade.jdbc.arquitetura.consulta.ColunaPrefixavel;

/**
 * <AUTHOR>
 * @since 07/08/2018
 */
public enum ColunasContratoDuracao implements ColunaPrefixavel {

    CONTRATO("contrato"),
    CODIGO("codigo"),
    NUMERO_MESES("numeroMeses"),
    NR_MAXIMO_PARCELAS_PAGAMENTO("nrMaximoParcelasCondPagamento"),
    TIPO_VALOR("tipoValor"),
    TIPO_OPERACAO("tipoOperacao"),
    PERCENTUAL_DESCONTO("percentualDesconto"),
    VALOR_ESPECIFICO("valorEspecifico"),
    VALOR_DESEJADO("valorDesejado"),
    VALOR_DESEJADO_MENSAL("valorDesejadoMensal"),
    VALOR_DESEJADO_PARCELA("valorDesejadoParcela"),
    CARENCIA("carencia"),
    QUANTIDADE_DIAS_EXTRA("quantidadeDiasExtra");

    private static final String CONTRATO_DURACAO_PREFIXO = "contratodur";
    private static final String NOME_TABELA = "ContratoDuracao";
    private String label;

    ColunasContratoDuracao(String label) {
        this.label = label;
    }

    /**
     * @return O prefixo dessas colunas
     */
    public static String getPrefixo() {
        return CONTRATO_DURACAO_PREFIXO;
    }

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getLabelComPrefixo() {
        return CONTRATO_DURACAO_PREFIXO + "." + label;
    }

    public static String getNomeTabela() {
        return NOME_TABELA;
    }

    public static String getNomeTabelaComPrefixo() {
        return getNomeTabela() + " " + getPrefixo();
    }
}
