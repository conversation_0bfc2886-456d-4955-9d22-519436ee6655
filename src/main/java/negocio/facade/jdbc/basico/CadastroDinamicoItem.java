package negocio.facade.jdbc.basico;

import negocio.comuns.CadastroDinamicoItemVO;
import negocio.comuns.basico.CadastroDinamicoVO;
import negocio.comuns.basico.enumerador.negocio.comuns.basico.enumerador.cadastrodinamico.CadastroDinamicoEnumInterface;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.CadastroDinamicoItemInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 14/08/2015.
 */
public class CadastroDinamicoItem extends SuperEntidade implements CadastroDinamicoItemInterfaceFacade {

    public CadastroDinamicoItem() throws Exception {
        super();
    }

    public CadastroDinamicoItem(Connection conexao) throws Exception {
        super(conexao);
    }


    public void incluirSemCommit(CadastroDinamicoVO cadastroDinamicoVO, List<CadastroDinamicoEnumInterface> listaCadastroDinamicoEnumInterface) throws Exception{
        for (CadastroDinamicoEnumInterface cadastroDinamicoEnumInterface: listaCadastroDinamicoEnumInterface){
            incluirSemCommit(cadastroDinamicoVO.getCodigo(), cadastroDinamicoEnumInterface.toString(), cadastroDinamicoEnumInterface.getLabel());
        }
    }

    public void incluirSemCommit(Integer codigoCadastroDinamico, String nomeCampo, String labelCampo) throws Exception{
        String sqlInsert = "insert into CadastroDinamicoItem(cadastroDinamico, nomeCampo,labelCampo) values (?,?, ?)";
        PreparedStatement pst = con.prepareStatement(sqlInsert);
        pst.setInt(1, codigoCadastroDinamico);
        pst.setString(2, nomeCampo);
        pst.setString(3, labelCampo);
        pst.execute();
    }

    public void excluir(List<CadastroDinamicoItemVO> listaCadastroDinamicoItem) throws Exception{

    }

    public void alterar(List<CadastroDinamicoItemVO> listaCadastroDinamicoItem) throws Exception{
        String sql = "update CadastroDinamicoItem set mostrarCampo = ?, campoObrigatorio =? where codigo = ?";
        PreparedStatement pst = con.prepareStatement(sql);
        for (CadastroDinamicoItemVO cadastroDinamicoItemVO: listaCadastroDinamicoItem){
            pst.setBoolean(1, cadastroDinamicoItemVO.isMostrarCampo());
            pst.setBoolean(2, cadastroDinamicoItemVO.isCampoObrigatorio());
            pst.setInt(3, cadastroDinamicoItemVO.getCodigo());
            pst.execute();
        }
    }

    public void excluirSemCommit(CadastroDinamicoItemVO cadastroDinamicoItemVO) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("delete from CadastroDinamicoItem where codigo = ").append(cadastroDinamicoItemVO.getCodigo());
        PreparedStatement pst = con.prepareStatement(sql.toString());
        pst.execute();
    }

    public List<CadastroDinamicoItemVO> consultar(String nomeTabela, int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select it.* \n");
        sql.append("from CadastroDinamicoItem it \n");
        sql.append("inner join CadastroDinamico cd on cd.codigo = it.cadastroDinamico \n ");
        sql.append("where upper(cd.nomeTabela) = '").append(nomeTabela.toUpperCase()).append("'");
        sql.append(" order by it.codigo ");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        return montarDadosConsulta(rs, nivelMontarDados);
    }

    public List<String> consultarCamposMostrar(String nomeTabela, int nivelMontarDados) throws Exception{
        List<String> lista = new ArrayList<String>();
        List<CadastroDinamicoItemVO> listaCadastroDinamico = consultar(nomeTabela, nivelMontarDados);
        for (CadastroDinamicoItemVO cadastroDinamicoItemVO: listaCadastroDinamico){
            if (cadastroDinamicoItemVO.isMostrarCampo()){
                lista.add(cadastroDinamicoItemVO.getNomeCampo());
            }
        }
        return lista;
    }

    public List<String> consultarCamposObrigatorio(String nomeTabela, int nivelMontarDados) throws Exception{
        List<String> lista = new ArrayList<String>();
        List<CadastroDinamicoItemVO> listaCadastroDinamico = consultar(nomeTabela, nivelMontarDados);
        for (CadastroDinamicoItemVO cadastroDinamicoItemVO: listaCadastroDinamico){
            if (cadastroDinamicoItemVO.isCampoObrigatorio()){
                lista.add(cadastroDinamicoItemVO.getNomeCampo());
            }
        }
        return lista;
    }

    public List<CadastroDinamicoItemVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<CadastroDinamicoItemVO> lista = new ArrayList<CadastroDinamicoItemVO>();
        while (tabelaResultado.next()) {
            CadastroDinamicoItemVO obj = montarDados(tabelaResultado, nivelMontarDados);
            lista.add(obj);
        }
        return lista;
    }


    public CadastroDinamicoItemVO consultarPorCodigo(Integer codigo,  int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from CadastroDinamicoItem where codigo = ").append(codigo);
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;
    }

    public CadastroDinamicoItemVO consultarPorNome(Integer codigoCadastroDinamico, String nome,  int nivelMontarDados) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select it.* \n");
        sql.append("from CadastroDinamicoItem it \n");
        sql.append("where it.cadastroDinamico = ").append(codigoCadastroDinamico);
        sql.append(" and upper(nomeCampo) = '").append(nome.toUpperCase()).append("'");
        PreparedStatement pst = con.prepareStatement(sql.toString());
        ResultSet rs = pst.executeQuery();
        if (rs.next()){
            return montarDados(rs, nivelMontarDados);
        }
        return null;
    }

    public CadastroDinamicoItemVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        CadastroDinamicoItemVO cadastroDinamicoItemVO = new CadastroDinamicoItemVO();
        cadastroDinamicoItemVO.setCodigo(dadosSQL.getInt("codigo"));
        cadastroDinamicoItemVO.setNomeCampo(dadosSQL.getString("nomeCampo"));
        cadastroDinamicoItemVO.setLabelCampo(dadosSQL.getString("labelCampo"));
        cadastroDinamicoItemVO.setMostrarCampo(dadosSQL.getBoolean("mostrarCampo"));
        cadastroDinamicoItemVO.setCampoObrigatorio(dadosSQL.getBoolean("campoObrigatorio"));
        cadastroDinamicoItemVO.setCadastroDinamicoVO(getFacade().getCadastroDinamico().consultarPorCodigo(dadosSQL.getInt("cadastroDinamico"), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
        cadastroDinamicoItemVO.setNovoObj(false);
        return cadastroDinamicoItemVO;
    }


}
