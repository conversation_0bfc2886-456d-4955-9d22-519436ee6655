package negocio.facade.jdbc.basico;

import negocio.comuns.basico.GympassDiaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.GympassDiaInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class GympassDia extends SuperEntidade implements GympassDiaInterfaceFacade {

    public GympassDia() throws Exception {
        super();
    }

    public GympassDia(Connection con) throws Exception {
        super(con);
    }

    public GympassDiaVO novo() throws Exception {
        incluir(getIdEntidade());
        return new GympassDiaVO();
    }

    public void incluir(int codigo, GympassDiaVO obj) throws Exception {
        GympassDiaVO.validarDados(obj);
        int i = 1;
        String sql = "INSERT INTO gympassdia (codigogympass, sequencia, valordia) VALUES (?, ? ,?)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setInt(i++, codigo);
        sqlInserir.setInt(i++, obj.getSequencia());
        sqlInserir.setDouble(i++, obj.getValorDia());
        sqlInserir.execute();
    }

    public void alterar(int codigogympass, GympassDiaVO obj) throws Exception {
        GympassDiaVO.validarDados(obj);
        int i = 1;
        String sql = "UPDATE gympassdia set valordia=? WHERE codigogympass=? AND sequencia=?";
        PreparedStatement sqlAlterar = con.prepareStatement(sql);
        sqlAlterar.setDouble(i++, obj.getValorDia());
        sqlAlterar.setInt(i++, codigogympass);
        sqlAlterar.setInt(i++, obj.getSequencia());
        sqlAlterar.execute();
    }

    public List consultarPorCodigo(int codigoGympass, int nivelMontarDados) throws Exception {
        int i = 1;
        String sql = "SELECT * FROM gympassdia WHERE codigogympass = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(i++, codigoGympass);

        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        return (montarDadosConsulta(tabelaResultado, nivelMontarDados));
    }

    public List<GympassDiaVO> montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados) throws Exception {
        List<GympassDiaVO> vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            GympassDiaVO obj = montarDados(tabelaResultado, nivelMontarDados);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    public GympassDiaVO montarDados(ResultSet dadosSQL, int nivelMontarDados) throws Exception {
        GympassDiaVO obj = new GympassDiaVO();
        obj.setSequencia(dadosSQL.getInt("sequencia"));
        obj.setValorDia(dadosSQL.getDouble("valordia"));
        obj.setNovoObj(false);
        if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return obj;
        }
        return obj;
    }
}
