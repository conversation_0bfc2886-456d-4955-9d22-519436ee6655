package negocio.facade.jdbc.basico;

import controle.arquitetura.FavoritoEnum;
import controle.arquitetura.FuncionalidadeSistemaEnum;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.FavoritoVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

public class Favorito extends SuperEntidade  {

    public Favorito() throws Exception {
        super();
    }

    public Favorito(Connection conexao) throws Exception {
        super(conexao);
    }

    public void inserirAcessoFacil(Integer usuario, FuncionalidadeSistemaEnum funcionalidade) throws SQLException {
        FavoritoVO fv = new FavoritoVO(usuario,
                FavoritoEnum.ACESSO_FACIL_FAVORITO.getCodigo(),
                funcionalidade.getName());
        incluir(fv);
    }

    public void inserirAcessoFacilHistorico(Integer usuario, FuncionalidadeSistemaEnum funcionalidade) throws SQLException {
        FavoritoVO fv = new FavoritoVO();

        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
        try {
            fv = obterPorIdentificador(funcionalidade.getName(), loginControle.getUsuario());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if( fv.getCodigo() == null ){
            FavoritoVO nfv = new FavoritoVO(usuario,
                    FavoritoEnum.ACESSO_FACIL_HISTORICO.getCodigo(),
                    funcionalidade.getName());
            incluir(nfv);
        }
    }

    public void incluir(FavoritoVO favorito) throws SQLException {
       String sql = "INSERT INTO favorito (" +
               "  usuario," +
               "  tipo," +
               "  identificador" +
               ")" +
               "  VALUES (" +
               "    '"+favorito.getUsuario()+"'," +
               "    '"+favorito.getTipo()+"'," +
               "    '"+favorito.getIdentificador()+"'" +
               "  );";

        PreparedStatement sqlSt = con.prepareStatement(sql);
        sqlSt.execute();
    }

    public void excluirAcessoFacilHistorico(FuncionalidadeSistemaEnum fun, UsuarioVO usuarioLogado) throws SQLException{
        if (fun != null) {
            FavoritoVO fv = obterPorIdentificador(fun.getName(), usuarioLogado);
            if (fv.getCodigo() != null) {
                excluir(fv.getCodigo());
            }
        }
    }

    public void excluirAcessoFacil(FuncionalidadeSistemaEnum fun, UsuarioVO usuarioLogado) throws SQLException{
        FavoritoVO fv = obterPorIdentificador(fun.getName(), usuarioLogado);
        if( fv.getCodigo() != null ){
            excluir(fv.getCodigo());
        }
    }

    public void excluir(Integer codigo) throws SQLException {
        String sql = "DELETE FROM favorito WHERE codigo = "+codigo;

        PreparedStatement sqlSt = con.prepareStatement(sql);
        sqlSt.execute();
    }

    public FavoritoVO obterPorIdentificador(String identificador, UsuarioVO usuarioLogado) throws SQLException{

        String sql = "SELECT * FROM favorito WHERE identificador = '"+identificador+"' and usuario = "+ usuarioLogado.getCodigo();

        Statement st = con.createStatement();
        ResultSet rs = st.executeQuery(sql);

        FavoritoVO fv = new FavoritoVO();
        if(rs.next()){
            fv.setCodigo(rs.getInt("codigo"));
            fv.setNome(rs.getString("nome"));
            fv.setUsuario(rs.getInt("usuario"));
            fv.setIdentificador(rs.getString("identificador"));
        }

        return  fv;
    }

    public List<FuncionalidadeSistemaEnum> consultarAcessoFacil(Integer usuario, FavoritoEnum tipo, Integer limit) throws SQLException {
        String sql = "select * " +
                " from ( select distinct on (identificador) * from Favorito where identificador is not null and usuario = "+usuario+" and tipo = "+tipo.getCodigo()+" ) as f " +
                " order by f.codigo desc " +
                " limit "+limit;
        List<FuncionalidadeSistemaEnum> favoritos;
        try (Statement st = con.createStatement()) {
            try (ResultSet rs = st.executeQuery(sql)) {
                favoritos = new ArrayList<>();
                while (rs.next()) {
                    favoritos.add(FuncionalidadeSistemaEnum.obterPorNome(rs.getString("identificador")));
                }
            }
        }
        return favoritos;
    }
}
