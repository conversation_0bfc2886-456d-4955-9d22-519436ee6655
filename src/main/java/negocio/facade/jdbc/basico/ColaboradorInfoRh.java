package negocio.facade.jdbc.basico;

import negocio.comuns.basico.ColaboradorInfoRhVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.ColaboradorInfoRhInterface;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Created by ulisses on 12/11/2022.
 */
public class ColaboradorInfoRh extends SuperEntidade implements ColaboradorInfoRhInterface {

    public ColaboradorInfoRh() throws Exception {
        super();
    }

    public ColaboradorInfoRh(Connection conexao) throws Exception {
        super(conexao);
    }

    public void incluir(ColaboradorVO colaboradorVO)throws Exception{
        PreparedStatement pst = con.prepareStatement("insert into colaboradorInfoRH (colaborador,tamanhoUniformeCamisa,tamanhoUniformeCalca,valorSalario,observacao) values(?,?,?,?,?) ");
        pst.setInt(1, colaboradorVO.getColaboradorInfoRhVO().getColaboradorVO().getCodigo());
        resolveStringNull(pst,2,colaboradorVO.getColaboradorInfoRhVO().getTamanhoUniformeCamisa());
        resolveIntegerNull(pst,3,colaboradorVO.getColaboradorInfoRhVO().getTamanhoUniformeCalca());
        resolveDoubleNull(pst,4,colaboradorVO.getColaboradorInfoRhVO().getValorSalario());
        resolveStringNull(pst,5,colaboradorVO.getColaboradorInfoRhVO().getObservacao());
        pst.execute();
        colaboradorVO.getColaboradorInfoRhVO().setCodigo(obterValorChavePrimariaCodigo());
        colaboradorVO.getColaboradorInfoRhVO().setNovoObj(false);
    }

    public void alterar(ColaboradorVO colaboradorVO)throws Exception{
        PreparedStatement pst = con.prepareStatement("update colaboradorInfoRH set tamanhoUniformeCamisa=?,tamanhoUniformeCalca=?,valorSalario=?,observacao=? where codigo =?  ");
        resolveStringNull(pst,1,colaboradorVO.getColaboradorInfoRhVO().getTamanhoUniformeCamisa());
        resolveIntegerNull(pst,2,colaboradorVO.getColaboradorInfoRhVO().getTamanhoUniformeCalca());
        resolveDoubleNull(pst,3,colaboradorVO.getColaboradorInfoRhVO().getValorSalario());
        resolveStringNull(pst,4,colaboradorVO.getColaboradorInfoRhVO().getObservacao());
        pst.setInt(5,colaboradorVO.getColaboradorInfoRhVO().getCodigo());
        pst.execute();
    }

    public ColaboradorInfoRhVO consultar(ColaboradorVO colaboradorVO)throws Exception{
        ResultSet rs = con.createStatement().executeQuery("select * from colaboradorInfoRH where colaborador = " + colaboradorVO.getCodigo());
        if (rs.next()){
            ColaboradorInfoRhVO colaboradorInfoRhVO = montarDados(rs, colaboradorVO);
            colaboradorInfoRhVO.registrarObjetoVOAntesDaAlteracao();
            return colaboradorInfoRhVO;
        }
        return null;
    }

    private ColaboradorInfoRhVO montarDados(ResultSet rs,ColaboradorVO colaboradorVO)throws Exception{
        ColaboradorInfoRhVO obj = new ColaboradorInfoRhVO();
        obj.setColaboradorVO(colaboradorVO);
        obj.setCodigo(rs.getInt("codigo"));
        obj.setTamanhoUniformeCamisa(rs.getString("tamanhoUniformeCamisa"));
        obj.setTamanhoUniformeCalca(rs.getInt("tamanhoUniformeCalca"));
        obj.setValorSalario(rs.getDouble("valorSalario"));
        obj.setObservacao(rs.getString("observacao"));
        return obj;
    }

    public void excluir(Integer codigo)throws Exception{
        con.createStatement().execute("delete from colaboradorInfoRH where codigo = "+ codigo);
    }
}
