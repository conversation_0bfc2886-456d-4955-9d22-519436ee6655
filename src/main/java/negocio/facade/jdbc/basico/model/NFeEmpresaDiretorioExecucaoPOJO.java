package negocio.facade.jdbc.basico.model;

public final class NFeEmpresaDiretorioExecucaoPOJO {

    private final String identificador;
    private final String executavel;
    private final Integer qtdEmpresas;

    public NFeEmpresaDiretorioExecucaoPOJO(String identificador, String executavel, Integer qtdEmpresas) {
        this.identificador = identificador;
        this.executavel = executavel;
        this.qtdEmpresas = qtdEmpresas;
    }

    public String getIdentificador() {
        return identificador;
    }

    public String getExecutavel() {
        return executavel;
    }

    public Integer getQtdEmpresas() {
        return qtdEmpresas;
    }
}
