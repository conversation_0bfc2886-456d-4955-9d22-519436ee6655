package negocio.facade.jdbc.basico;

import br.com.pacto.priv.utils.Uteis;
import br.com.pactosolucoes.conviteaulaexperimental.json.TipoConviteAulaExperimentalJSON;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.interfaces.basico.TipoConviteAulaExperimentalInterfaceFacade;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by ulisses on 08/01/2016.
 */
public class TipoConviteAulaExperimental extends SuperEntidade implements TipoConviteAulaExperimentalInterfaceFacade {

    public TipoConviteAulaExperimental() throws Exception {
        super();
    }

    public TipoConviteAulaExperimental(Connection con) throws Exception {
        super(con);
    }

    public void incluir(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO)throws Exception{
        tipoConviteAulaExperimentalVO.validarDados(tipoConviteAulaExperimentalVO);
        con.setAutoCommit(false);
        try{
            StringBuilder sql = new StringBuilder();
            sql.append("insert into tipoConviteAulaExperimental (descricao, vigenciaInicial, vigenciaFinal, aulasAgendadasEmDiasSeguido, \n");
            sql.append("quantidadeAulaExperimental, quantidadeConviteAlunoPodeEnviar, alunoPodeEnviarConvite, colaboradorPodeEnviarConvite, \n");
            sql.append("empresa, dataLancamento) values(?,?,?,?,?,?,?,?,?,?) \n");
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                tipoConviteAulaExperimentalVO.setDescricao(tipoConviteAulaExperimentalVO.getDescricao().toUpperCase());
                pst.setString(1, tipoConviteAulaExperimentalVO.getDescricao());
                pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(tipoConviteAulaExperimentalVO.getVigenciaInicial()));
                pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(tipoConviteAulaExperimentalVO.getVigenciaFinal()));
                pst.setBoolean(4, tipoConviteAulaExperimentalVO.isAulasAgendadasEmDiasSeguido());
                pst.setInt(5, tipoConviteAulaExperimentalVO.getQuantidadeAulaExperimental());
                pst.setInt(6, tipoConviteAulaExperimentalVO.getQuantidadeConviteAlunoPodeEnviar());
                pst.setBoolean(7, tipoConviteAulaExperimentalVO.isAlunoPodeEnviarConvite());
                pst.setBoolean(8, tipoConviteAulaExperimentalVO.isColaboradorPodeEnviarConvite());
                pst.setInt(9, tipoConviteAulaExperimentalVO.getEmpresaVO().getCodigo());
                pst.setTimestamp(10, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
                pst.execute();
            }
            tipoConviteAulaExperimentalVO.setCodigo(obterValorChavePrimariaCodigo());
            tipoConviteAulaExperimentalVO.setNovoObj(false);

            getFacade().getTipoConviteAulaExperimentalModalidade().incluirSemCommit(tipoConviteAulaExperimentalVO.getListaModalidade());

            con.commit();
        } catch (Exception e){
            con.rollback();
            throw e;
        } finally{
            con.setAutoCommit(true);
        }
    }

    public void alterar(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO)throws Exception {
        tipoConviteAulaExperimentalVO.validarDados(tipoConviteAulaExperimentalVO);
        con.setAutoCommit(false);
        try{
            StringBuilder sql = new StringBuilder();
            sql.append("update tipoConviteAulaExperimental set descricao=?, vigenciaInicial =?, vigenciaFinal=?, aulasAgendadasEmDiasSeguido=?, \n");
            sql.append("quantidadeAulaExperimental=?, quantidadeConviteAlunoPodeEnviar=?, alunoPodeEnviarConvite=?, colaboradorPodeEnviarConvite=?, \n");
            sql.append("empresa=? \n");
            sql.append("where codigo = ?");
            try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
                pst.setString(1, tipoConviteAulaExperimentalVO.getDescricao());
                pst.setTimestamp(2, Uteis.getDataJDBCTimestamp(tipoConviteAulaExperimentalVO.getVigenciaInicial()));
                pst.setTimestamp(3, Uteis.getDataJDBCTimestamp(tipoConviteAulaExperimentalVO.getVigenciaFinal()));
                pst.setBoolean(4, tipoConviteAulaExperimentalVO.isAulasAgendadasEmDiasSeguido());
                pst.setInt(5, tipoConviteAulaExperimentalVO.getQuantidadeAulaExperimental());
                pst.setInt(6, tipoConviteAulaExperimentalVO.getQuantidadeConviteAlunoPodeEnviar());
                pst.setBoolean(7, tipoConviteAulaExperimentalVO.isAlunoPodeEnviarConvite());
                pst.setBoolean(8, tipoConviteAulaExperimentalVO.isColaboradorPodeEnviarConvite());
                pst.setInt(9, tipoConviteAulaExperimentalVO.getEmpresaVO().getCodigo());
                pst.setInt(10, tipoConviteAulaExperimentalVO.getCodigo());
                pst.execute();
            }

            getFacade().getTipoConviteAulaExperimentalModalidade().excluirSemComit(tipoConviteAulaExperimentalVO.getCodigo());
            getFacade().getTipoConviteAulaExperimentalModalidade().incluirSemCommit(tipoConviteAulaExperimentalVO.getListaModalidade());

            con.commit();
        }catch (Exception e){
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }


    public void excluir(TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO)throws Exception{
        con.setAutoCommit(false);
        try{
            getFacade().getTipoConviteAulaExperimentalModalidade().excluirSemComit(tipoConviteAulaExperimentalVO.getCodigo());

            String sql = "delete from tipoConviteAulaExperimental where codigo = ? ";
            try (PreparedStatement pst = con.prepareStatement(sql)) {
                pst.setInt(1, tipoConviteAulaExperimentalVO.getCodigo());
                pst.execute();
            }

            con.commit();
        }catch (Exception e){
            con.rollback();
            throw e;
        }finally {
            con.setAutoCommit(true);
        }
    }

    public List<TipoConviteAulaExperimentalJSON> consultarTipoConvitesVigenteAlunoPodeEnviar(Integer codigoCliente, Integer codigoEmpresa)throws Exception{
        /*
           Consulta os tipos de convites vigentes que o aluno pode enviar para seus amigos.
         */
        if ((codigoCliente == null) || (codigoCliente <= 0))
            throw new ConsistirException("O parâmetro codigoCliente é obrigatório.");
        if ((codigoEmpresa == null) || (codigoEmpresa <= 0))
            throw new ConsistirException("O parâmetro codigoEmpresa é obrigatório.");

        List<TipoConviteAulaExperimentalJSON> lista = new ArrayList<TipoConviteAulaExperimentalJSON>();
        StringBuilder sql = new StringBuilder();
        sql.append("select tConv.*, sqlConvite.totalConviteJaEnviado \n");
        sql.append("from tipoConviteAulaExperimental tConv \n");
        sql.append("left join( \n");
        sql.append("           select tipoConviteAulaExperimental, count(*) as totalConviteJaEnviado\n");
        sql.append("           from conviteAulaExperimental \n");
        sql.append("           where clienteConvidou = ").append(codigoCliente).append(" \n");
        sql.append("           group by tipoConviteAulaExperimental \n");
        sql.append("          )sqlConvite on sqlConvite.tipoConviteAulaExperimental = tConv.codigo \n");
        sql.append("where alunoPodeEnviarConvite = true and tConv.empresa = ").append(codigoEmpresa).append(" \n");
        sql.append("and current_date between vigenciaInicial and vigenciaFinal");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                while (rs.next()) {
                    TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO = montarDadosBasico(rs);
                    TipoConviteAulaExperimentalJSON conviteJson = new TipoConviteAulaExperimentalJSON(tipoConviteAulaExperimentalVO);
                    conviteJson.setTotalConvitesJaEnviado(rs.getInt("totalConviteJaEnviado"));
                    lista.add(conviteJson);
                }
            }
        }
        return lista;
    }

    private TipoConviteAulaExperimentalVO montarDadosBasico(ResultSet rs)throws Exception{
        TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO = new TipoConviteAulaExperimentalVO();
        tipoConviteAulaExperimentalVO.setCodigo(rs.getInt("codigo"));
        tipoConviteAulaExperimentalVO.setDescricao(rs.getString("descricao"));
        tipoConviteAulaExperimentalVO.setVigenciaInicial(rs.getTimestamp("vigenciaInicial"));
        tipoConviteAulaExperimentalVO.setVigenciaFinal(rs.getTimestamp("vigenciaFinal"));
        tipoConviteAulaExperimentalVO.setAulasAgendadasEmDiasSeguido(rs.getBoolean("aulasAgendadasEmDiasSeguido"));
        tipoConviteAulaExperimentalVO.setQuantidadeAulaExperimental(rs.getInt("quantidadeAulaExperimental"));
        tipoConviteAulaExperimentalVO.setQuantidadeConviteAlunoPodeEnviar(rs.getInt("quantidadeConviteAlunoPodeEnviar"));
        tipoConviteAulaExperimentalVO.setAlunoPodeEnviarConvite(rs.getBoolean("alunoPodeEnviarConvite"));
        tipoConviteAulaExperimentalVO.setColaboradorPodeEnviarConvite(rs.getBoolean("colaboradorPodeEnviarConvite"));
        EmpresaVO empresaVO = new EmpresaVO();
        empresaVO.setCodigo(rs.getInt("empresa"));
        tipoConviteAulaExperimentalVO.setEmpresaVO(empresaVO);
        tipoConviteAulaExperimentalVO.setDataLancamento(rs.getTimestamp("dataLancamento"));
        tipoConviteAulaExperimentalVO.setNovoObj(false);
        return tipoConviteAulaExperimentalVO;
    }

    private TipoConviteAulaExperimentalVO montarDados(ResultSet rs, int nivelMontarDados)throws Exception{
        TipoConviteAulaExperimentalVO tipoConviteAulaExperimentalVO = montarDadosBasico(rs);

        if (nivelMontarDados == negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) {
            return tipoConviteAulaExperimentalVO;
        }
        if (nivelMontarDados == negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
            return tipoConviteAulaExperimentalVO;
        }
        tipoConviteAulaExperimentalVO.setListaModalidade(getFacade().getTipoConviteAulaExperimentalModalidade().consultar(tipoConviteAulaExperimentalVO,negocio.comuns.utilitarias.Uteis.NIVELMONTARDADOS_TODOS));

        return tipoConviteAulaExperimentalVO;
    }

    public TipoConviteAulaExperimentalVO consultarPorCodigo(Integer codigo, int nivelMontarDados)throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("select * from tipoConviteAulaExperimental where codigo = ?");
        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            pst.setInt(1, codigo);
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados);
                }
            }
        }
        return null;
    }

    @Override
    public List<TipoConviteAulaExperimentalVO> consultarTodos(Integer codigoEmpresa) throws Exception{
        return consultarParaImpressao("", "", "", codigoEmpresa);
    }

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, int codigoEmpresa) throws SQLException {
        List lista;
        try (ResultSet rs = getPS(codigoEmpresa).executeQuery()) {
            lista = new ArrayList();
            while (rs.next()) {
                TipoConviteAulaExperimentalVO obj = new TipoConviteAulaExperimentalVO();
                String geral = rs.getString("codigo") + rs.getString("descricao") + rs.getString("nomeEmpresa") + rs.getString("vigenciaInicial") + rs.getString("vigenciaFinal");
                if (geral.toLowerCase().contains(filtro.toLowerCase())) {
                    obj.setCodigo(rs.getInt("codigo"));
                    obj.setDescricao(rs.getString("descricao"));
                    obj.setEmpresaVO(new EmpresaVO());
                    obj.getEmpresaVO().setNome(rs.getString("nomeEmpresa"));
                    obj.setVigenciaInicial(rs.getDate("vigenciaInicial"));
                    obj.setVigenciaFinal(rs.getDate("vigenciaFinal"));
                    lista.add(obj);
                }
            }
        }
        if (campoOrdenacao.equals("Código")) {
            Ordenacao.ordenarLista(lista, "codigo");
        } else if (campoOrdenacao.equals("Descrição")) {
            Ordenacao.ordenarLista(lista, "descricao");
        } else if (campoOrdenacao.equals("Início")) {
            Ordenacao.ordenarLista(lista, "vigenciaInicial");
        } else if (campoOrdenacao.equals("Fim")) {
            Ordenacao.ordenarLista(lista, "vigenciaFinal");
        } else if (campoOrdenacao.equals("Empresa")) {
            Ordenacao.ordenarLista(lista, "nomeEmpresa");
        }
        if (ordem.contains("desc")) {
            Collections.reverse(lista);
        }
        return lista;
    }

    public String consultarJSON(Integer codEmpresa) throws Exception {
        StringBuilder json;
        boolean dados;
        try (ResultSet rs = getPS(codEmpresa).executeQuery()) {
            json = new StringBuilder();
            json.append("{\"aaData\":[");
            dados = false;
            while (rs.next()) {
                dados = true;
                json.append("[\"").append(rs.getString("codigo")).append("\",");
                json.append("\"").append(negocio.comuns.utilitarias.Uteis.normalizarStringJSON(rs.getString("descricao")).trim()).append("\",");
                json.append("\"").append(Calendario.getData(rs.getDate("vigenciaInicial"), "dd/MM/yyyy")).append("\",");
                json.append("\"").append(Calendario.getData(rs.getDate("vigenciaFinal"), "dd/MM/yyyy")).append("\",");
                json.append("\"").append(rs.getString("nomeEmpresa")).append("\"],");
            }
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private PreparedStatement getPS(Integer empresa) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select tp.codigo, tp.descricao, tp.vigenciaInicial, tp.vigenciaFinal, emp.nome as nomeEmpresa \n ");
        sql.append("from tipoConviteAulaExperimental tp \n");
        sql.append("inner join empresa emp ON tp.empresa = emp.codigo");
        if ((empresa != null) && (empresa > 0)){
            sql.append(" where emp.codigo = ").append(empresa);
        }
        return con.prepareStatement(sql.toString());
    }
}
