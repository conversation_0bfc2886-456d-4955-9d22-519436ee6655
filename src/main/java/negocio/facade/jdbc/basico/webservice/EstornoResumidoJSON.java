package negocio.facade.jdbc.basico.webservice;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.ArrayList;
import java.util.List;

public class EstornoResumidoJSON extends SuperJSON {
    private int codigoRecibo;
    private String data;
    private String unidade;
    private Double pgtoEstornado;
    private String consultor;
    private List<EstornoResumidoParcelaJSON> estornoResumidoParcela = new ArrayList<>();

    public int getCodigoRecibo() {
        return codigoRecibo;
    }

    public void setCodigoRecibo(int codigoRecibo) {
        this.codigoRecibo = codigoRecibo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }

    public Double getPgtoEstornado() {
        return pgtoEstornado;
    }

    public void setPgtoEstornado(Double pgtoEstornado) {
        this.pgtoEstornado = pgtoEstornado;
    }

    public String getConsultor() {
        return consultor;
    }

    public void setConsultor(String consultor) {
        this.consultor = consultor;
    }

    public List<EstornoResumidoParcelaJSON> getEstornoResumidoParcela() {
        return estornoResumidoParcela;
    }

    public void setEstornoResumidoParcela(List<EstornoResumidoParcelaJSON> estornoResumidoParcela) {
        this.estornoResumidoParcela = estornoResumidoParcela;
    }
}