/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.basico;

import java.util.List;
import negocio.comuns.basico.AlunoComissaoColaboradorVO;

/**
 *
 * <AUTHOR>
 */
public interface AlunoComissaoColaboradorInterfaceFacade extends SuperInterface  {

    public void incluir(AlunoComissaoColaboradorVO obj) throws Exception;
    public void alterar(AlunoComissaoColaboradorVO obj) throws Exception;
    public void excluir(AlunoComissaoColaboradorVO obj) throws Exception;
    public void excluirAlunoComissao(Integer colaborador) throws Exception;
    public void alterarAlunoComissao(Integer colaborador, List<AlunoComissaoColaboradorVO> objetos) throws Exception;
    public List consultarAlunoComissao(Integer colaborador, int nivelMontarDados) throws Exception;
    public void incluirAlunosComissao(Integer colaboradorPrm, List<AlunoComissaoColaboradorVO> objetos) throws Exception ;
}

