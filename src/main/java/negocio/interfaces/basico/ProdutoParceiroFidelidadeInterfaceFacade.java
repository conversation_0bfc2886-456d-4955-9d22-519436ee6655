package negocio.interfaces.basico;

import negocio.comuns.basico.ProdutoParceiroFidelidadeVO;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface ProdutoParceiroFidelidadeInterfaceFacade extends SuperInterface {
    
    void setIdEntidade(String aIdEntidade);

    ProdutoParceiroFidelidadeVO novo() throws Exception;

    void incluir(ProdutoParceiroFidelidadeVO obj) throws Exception;

    void alterar(ProdutoParceiroFidelidadeVO obj) throws Exception;

    void excluir(ProdutoParceiroFidelidadeVO obj) throws Exception;

    ProdutoParceiroFidelidadeVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;
    
    List<ProdutoParceiroFidelidadeVO> consultarPorParceiro(final Integer codigoParceiro, int nivelMontarDados) throws Exception;

    List<ProdutoParceiroFidelidadeVO> consultarPorParceiroMaximoPontos(final Integer codigoParceiro, final Integer maximoPontos, boolean somenteComCodigoExterno, int nivelMontarDados) throws Exception;

    ProdutoParceiroFidelidadeVO consultarPorParceiroPontos(final Integer codigoParceiro, Integer pontos, int nivelMontarDados) throws Exception;

    ProdutoParceiroFidelidadeVO consultarPorParceiroPontosCodigoExterno(final Integer codigoParceiro, Integer pontos, String codigoExterno, int nivelMontarDados) throws Exception;

}