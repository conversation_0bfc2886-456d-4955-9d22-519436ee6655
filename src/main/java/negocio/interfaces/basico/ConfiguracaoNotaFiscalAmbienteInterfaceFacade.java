package negocio.interfaces.basico;

import negocio.comuns.basico.ConfiguracaoNotaFiscalAmbienteVO;
import negocio.comuns.basico.ConfiguracaoNotaFiscalVO;
import negocio.comuns.notaFiscal.AmbienteEmissaoNotaFiscalEnum;


public interface ConfiguracaoNotaFiscalAmbienteInterfaceFacade extends SuperInterface {

    void incluir(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception;

    void incluirSemCommit(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception;

    void alterar(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception;

    void alterarSemCommit(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception;

    void excluir(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception;

    void excluirSemCommit(ConfiguracaoNotaFiscalAmbienteVO obj) throws Exception;

    void incluirOuAlteraSemComitPorConfiguracaoNotaFiscal(ConfiguracaoNotaFiscalVO configuracaoNotaFiscalVO) throws Exception;

    ConfiguracaoNotaFiscalAmbienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    ConfiguracaoNotaFiscalAmbienteVO consultarPorAmbienteEmissaoConfiguracaoNotaFiscal(AmbienteEmissaoNotaFiscalEnum ambienteEmissaoNotaFiscalEnum,
                                                                                    Integer configuracaoNotaFiscal, int nivelMontarDados) throws Exception;

}
