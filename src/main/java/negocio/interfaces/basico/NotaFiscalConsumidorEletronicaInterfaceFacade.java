package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import org.json.JSONObject;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.nfe.RetornoEnvioNotaFiscalTO;
import negocio.comuns.notaFiscal.SituacaoNotaFiscalEnum;
import negocio.facade.jdbc.basico.model.NFeEmpresaDiretorioExecucaoPOJO;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

/*
 * Created by <PERSON><PERSON> on 27/03/2017.
 */
public interface NotaFiscalConsumidorEletronicaInterfaceFacade extends SuperInterface {

    NotaFiscalConsumidorEletronicaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    NotaFiscalConsumidorEletronicaVO consultarPorIdNFCe(Integer IdNFCe, int nivelMontarDados) throws Exception;

    String montarDescricaoExcluirNotaFiscal(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO) throws Exception;

    void excluirComLog(NotaFiscalConsumidorEletronicaVO obj, UsuarioVO usuarioLogado, ProcessoAjusteGeralEnum processoAjusteGeralEnum) throws SQLException;

    String consultarPorPessoaParaTela(Integer codigoPessoa, Integer limit) throws Exception;

    RetornoEnvioNotaFiscalTO reenviarNFCe(JSONObject jsonObject, Integer id_nfce);

    RetornoEnvioNotaFiscalTO inutilizarNFCe(JSONObject jsonObject, Integer id_nfce);

    NotaFiscalConsumidorEletronicaVO consultarPorReciboPagamento(Integer reciboPagamento, int nivelMontarDados) throws Exception;

    NotaFiscalConsumidorEletronicaVO consultaPorMovPagamento(Integer movPagamento, int nivelMontarDados) throws Exception;

    List<NotaFiscalConsumidorEletronicaVO> consultaListaPorCheque(String composicaoCheque) throws Exception;

    List<NotaFiscalConsumidorEletronicaVO> consultaListaPorCartao(String composicaoCartao) throws Exception;

    boolean isCertificadoVencido(String cnpj) throws Exception;

    List<NFeEmpresaDiretorioExecucaoPOJO> getExecutaveisDelphi() throws IOException;

    void adicionarEmpresa(String identificadorExecutavel, Integer idEmpresa) throws IOException;

    void removerEmpresa(String identificadorExecutavel, Integer idEmpresa) throws IOException;

    void reiniciarExecutavelDelphi(String identificadorExecutavel) throws IOException;

    void reiniciarTodosExecutaveisDelphi() throws IOException;

    boolean existeNFCeIdReferencia(String idReferencia) throws Exception;

    void atualizarJsonEnviarIdReferencia(NotaFiscalConsumidorEletronicaVO notaFiscalConsumidorEletronicaVO) throws Exception;

    void atualizarIdNFCe(SituacaoNotaFiscalEnum situacaoNotaFiscalEnum, Integer id_nfce, NotaFiscalConsumidorEletronicaVO obj) throws Exception;

    List<NotaFiscalConsumidorEletronicaVO> consultarNotasAguardandoEnvio() throws Exception;

    void atualizarDataEnvioSituacaoResultadoEnvio(NotaFiscalConsumidorEletronicaVO obj) throws Exception;

    void excluirComLogEnotas(Integer codNotaFiscal, NotaFiscalConsumidorEletronicaVO obj, UsuarioVO usuarioVO) throws SQLException;

    void excluirComLogEnotasSemCommit(Integer codNotaFiscal, NotaFiscalConsumidorEletronicaVO obj, UsuarioVO usuarioVO) throws Exception;

}
