package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.TipoClienteRestricaoEnum;
import servicos.impl.admCoreMs.ClienteRestricaoDTO;

import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ClienteRestricaoInterfaceFacade extends SuperInterface {

    void incluir(ClienteRestricaoDTO obj) throws Exception;

    void excluir(String cpf, TipoClienteRestricaoEnum tipoCliente) throws Exception;
    void excluirSemCommit(String cpf, TipoClienteRestricaoEnum tipoCliente) throws Exception;

    void excluir(List<String> cpfs, TipoClienteRestricaoEnum tipoCliente) throws Exception;

    List<ClienteRestricaoDTO> consultarClienteRestricaoPorCpf(String cpf) throws Exception;

    public void excluirSemCommit(List<String> cpfs, TipoClienteRestricaoEnum tipoCliente) throws Exception;
}
