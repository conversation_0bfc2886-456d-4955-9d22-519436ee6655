package negocio.interfaces.basico;

import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.LogAjusteGeralVO;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 14/04/2016
 */
public interface LogAjusteGeralInterfaceFacade extends SuperInterface {

    void incluir(LogAjusteGeralVO obj) throws Exception;

    void incluir(Date dataLancamento, String usuarioLogado, String usuarioOAMD, ProcessoAjusteGeralEnum processoAjusteGeralEnum, String parametros) throws Exception;

    List<LogAjusteGeralVO> consultarPorAjusteGeral(ProcessoAjusteGeralEnum processoAjusteGeralEnum, int nivelMontarDados) throws Exception;

    List<LogAjusteGeralVO> consultarPorAjusteGeral(Integer codigoProcessoAjusteGeral, int nivelMontarDados) throws Exception;

    List<LogAjusteGeralVO> consultarTodas(int nivelMontarDados) throws Exception;
}
