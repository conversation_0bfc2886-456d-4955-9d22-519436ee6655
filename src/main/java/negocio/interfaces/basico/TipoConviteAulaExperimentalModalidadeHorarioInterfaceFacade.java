package negocio.interfaces.basico;

import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeHorarioVO;
import negocio.comuns.conviteaulaexperimental.TipoConviteAulaExperimentalModalidadeVO;

import java.util.List;

/**
 * Created by ulisses on 14/01/2016.
 */
public interface TipoConviteAulaExperimentalModalidadeHorarioInterfaceFacade extends SuperInterface {

    void incluirSemCommit(List<TipoConviteAulaExperimentalModalidadeHorarioVO> listaConviteModalidadeHorario)throws Exception;
    void excluirSemCommit(Integer tipoConviteAulaExperimental)throws Exception;
    List<TipoConviteAulaExperimentalModalidadeHorarioVO> consultar(TipoConviteAulaExperimentalModalidadeVO tipoConviteAulaExperimentalModalidadeVO, int nivelMontarDados)throws Exception;

}
