package negocio.interfaces.pactoprint;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.pactoprint.CarteirinhaClienteVO;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.util.List;


/**
 * date : 12/05/2015 14:19:04
 * autor: Ulisses
 */
public interface CarteirinhaClienteInterfaceFacade extends SuperInterface {

	JSONObject consultarCarteirinhaParaImpressao(final String url, String key, Integer codigoEmpresa, String dataHoraIni, String dataHoraFim, String tipoConsulta, String tipoCarteirinha)throws Exception;
	void atualizarImpressaoCarteirinha(Integer codigoCarteirinha)throws Exception;
	void atualizarDataValidade(Integer codigoCarteirinha)throws Exception;
	List<CarteirinhaClienteVO> consultarCarteirinhas(ClienteVO clienteVO, int nivelMontarDados) throws Exception;
	void incluirNovaCarteirinha(CarteirinhaClienteVO carteirinhaClienteVO)throws Exception;
	boolean existeCarteirinhaPendenteDeImpressao(Integer codigoCliente) throws Exception;
	boolean possuiCarteirinha(Integer codigoCliente) throws Exception;
	String obterNomeEmpresa(Integer codigoCliente) throws Exception;
	JSONObject consultarCarteirinhaPorCodigoClienteFormatoJson(Integer codigoCliente, Integer codigoEmpresa, String key) throws Exception;
	void atualizarChaveArquivo(String chavearquivo, Integer codigoCarteirinha)throws Exception;

}
