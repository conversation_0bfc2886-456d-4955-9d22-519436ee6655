package negocio.interfaces.contrato;

import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.interfaces.basico.SuperInterface;
import relatorio.negocio.jdbc.contrato.ComissaoRel;

import java.util.Date;
import java.util.List;

/**
 * Created by glauco on 21/01/14.
 */
public interface ComissaoGeralConfiguracaoInterfaceFacade extends SuperInterface {

    public String consultarJSON(Integer empresa) throws Exception;

    public void incluir(ComissaoGeralConfiguracaoVO comissaoVO) throws Exception;

    public void alterar(ComissaoGeralConfiguracaoVO comissaoVO) throws Exception;

    public void excluir(ComissaoGeralConfiguracaoVO comissaoVO) throws Exception;

    public boolean existe(ComissaoGeralConfiguracaoVO comissaoVO) throws  Exception;

    public ComissaoGeralConfiguracaoVO consultarPorChavePrimaria(Integer codigoConsulta, int nivelMontarDados) throws Exception;

    public List<ComissaoRel> processarDados(TipoRelatorioDF tipoRelatorio, Integer codEmpresa,
                                            Date dataIniLanc, Date dataFimLanc, Date dataReceb, Date dataContratosLancadosAPartir, Date dataComp,
                                            String impressaoPor, String tipoValorComissoes,
                                            boolean aceitaMatriculaRematricula,
                                            boolean aceitaManutencaoModalidade,
                                            boolean calcularComissaoProdutos,
                                            boolean retiraEdicaoPagamento,
                                            boolean retirarRecebiveisComPendencia,
                                            boolean considerarCompensacaoOriginal) throws Exception;

    public List<ComissaoGeralConfiguracaoVO> consultarPorEmpresa(Integer empresa, int nivelMontarDados) throws Exception;

}
