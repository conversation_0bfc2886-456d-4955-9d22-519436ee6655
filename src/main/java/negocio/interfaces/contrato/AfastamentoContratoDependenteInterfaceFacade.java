package negocio.interfaces.contrato;

import negocio.comuns.contrato.AfastamentoContratoDependenteVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface AfastamentoContratoDependenteInterfaceFacade extends SuperInterface {

    void incluirSemCommit(AfastamentoContratoDependenteVO obj) throws Exception;

    void alterarSemCommit(AfastamentoContratoDependenteVO obj) throws Exception;

    void excluir(AfastamentoContratoDependenteVO obj) throws Exception;

    AfastamentoContratoDependenteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    List<AfastamentoContratoDependenteVO> consultarPorContratoDependente(Integer codigoContratoDependente, String tipoAfastamento, int nivelMontarDados) throws Exception;

    List<AfastamentoContratoDependenteVO> consultarPorContratoDependente(AfastamentoContratoDependenteVO afastamentoVO) throws Exception;

    boolean existeAfastamentoParaData(Integer codCliente, Date dataAvaliar) throws Exception;

    boolean existeAfastamentoParaData(Integer codCliente, Date dataAvaliar, String tipoAfastamento) throws Exception;

    public boolean existeAfastamentoPorContratoDependente(Integer codigoContratoDependente) throws Exception;

}
