package negocio.interfaces.contrato;

import java.util.Date;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.PeriodoAcessoClienteVO;

import java.util.List;

import negocio.comuns.contrato.ContratoVO;
import negocio.interfaces.basico.SuperInterface;
import servicos.integracao.enumerador.IntegracoesEnum;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface PeriodoAcessoClienteInterfaceFacade extends SuperInterface {

    public PeriodoAcessoClienteVO novo() throws Exception;

    public void incluir(PeriodoAcessoClienteVO obj) throws Exception;

    public void alterar(PeriodoAcessoClienteVO obj) throws Exception;

    public void excluir(PeriodoAcessoClienteVO obj) throws Exception;

    public PeriodoAcessoClienteVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorPessoa(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorContrato(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorTipoAcesso(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public PeriodoAcessoClienteVO consultarPorDataEspecificaECodigoContrato(Date data, Integer contrato, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean consultarPorDataEspecificaECodigoPessoaETipoAcesso(Date data, Integer pessoa, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados, boolean gympass) throws Exception;

    public PeriodoAcessoClienteVO consultarPorDataPessoaTipoAcesso(Date data, Integer pessoa, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorPessoaDataAtual(Integer pessoa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public Boolean existePeriodoAcessoHojeTotalPassPorPessoa(Integer pessoa) throws Exception;

    public List consultarPorDataInicioContrato(Date data, Integer contrato, int nivelMontarDados) throws Exception;

    public int contarQtdAcessosPorClienteComGympassAPartirData(int cliente, Date dataInicial) throws Exception;

    public int contarQtdAcessosDoClienteComGympassNoMes(int cliente, int mes) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void excluirPeriodoAcessoClienteContrato(Integer codigoContrato) throws Exception;

    public void incluirPeriodoAcessoClienteContrato(ContratoVO contrato, List objetos) throws Exception;

    public void incluirSemCommit(PeriodoAcessoClienteVO obj) throws Exception;

    public PeriodoAcessoClienteVO obterUltimoDiaPeriodoAcessoPessoa(Integer pessoa, int nivelMontarDados, boolean somenteVigente, boolean gympass) throws Exception;

    public PeriodoAcessoClienteVO obterUltimoDiaPeriodoAcessoContratoTipo(Integer contrato, String tipoAcesso, int nivelMontarDados) throws Exception;

    public PeriodoAcessoClienteVO obterUltimoDiaPeriodoAcessoContrato(Integer contrato, int nivelMontarDados) throws Exception;

    public void excluirSemCommit(PeriodoAcessoClienteVO obj) throws Exception;

    public void alterarSemCommit(PeriodoAcessoClienteVO obj) throws Exception;

    public void excluirPeriodoAcessoClienteAulaAvulsaDiaria(Integer codigoAulaAvulsaDiaria) throws Exception;

    public void excluirSemCommit(final String condicao) throws Exception;

    public List<PeriodoAcessoClienteVO> consultar(String sql, int nivelMontarDados) throws Exception;

    public Boolean possuiPeriodoAcesso(int pessoa, String tipo, Date dateInicio, Date dataFim, int nivelMontarDados) throws Exception;

    public Date consultarFinalPeriodoVigente(Integer codigoPessoa) throws Exception;

    public boolean existePermissaoAcessoPeriodo(Integer pessoa, Date datainicio, Date datafinal) throws Exception;

    public PeriodoAcessoClienteVO consultarUltimoPorDataPessoaTipoAcesso(Date data, Integer pessoa, String tipoAcesso, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    PeriodoAcessoClienteVO consultarExisteTokenGymPass(String tokenGymPass, Integer empresa) throws Exception;

    List<PeriodoAcessoClienteVO> consultarPorPessoaGymPass(Integer pessoa) throws Exception;

    List<PeriodoAcessoClienteVO> consultarPorPessoaGoGood(Integer pessoa) throws Exception;

    public PeriodoAcessoClienteVO consultarPorDataPessoa(Date data, Integer pessoa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    boolean existeCheckinGympassHojePorToken(String tokenGymPass) throws Exception;

    boolean existeTokenGymPass(String tokenGymPass) throws Exception;

    boolean existeTokenGoGood(String tokenGoGood, String tokenAcademy) throws Exception;

    public List consultarPorVigenteOuFuturoContrato(Date data, Integer contrato, int nivelMontarDados) throws Exception;

    boolean existePeriodoAcessoGympassVigente(Integer codigoPessoa) throws Exception;

    boolean existePeriodoAcessoGympassVigentePorCliente(Integer codigoCliente) throws Exception;

    boolean existePeriodoAcessoTotalpassVigente(Integer codigoPessoa) throws Exception;

    boolean existiuPeriodoAcessoTotalpass(Integer codigoPessoa) throws Exception;

    void gravarInfoCheckin(ClienteVO cliente,
                           EmpresaVO empresaVO,
                           String token,
                           PeriodoAcessoClienteVO periodoAcessoClienteVO, IntegracoesEnum integracao, String produtoGymPass) throws Exception;

    void gravarInfoCheckinGoGood(ClienteVO cliente,
                                 EmpresaVO empresaVO,
                                 String token,
                                 PeriodoAcessoClienteVO periodoAcessoClienteVO, IntegracoesEnum integracao, String retorno) throws Exception;

    Boolean validarCheckinEmpresa(Integer periodoAcesso, Integer empresa) throws Exception;

    String obterProdutoGymPassInfoChekin(Integer periodoAcesso, Integer empresa, Integer codigoCliente, boolean somenteCodigo) throws Exception;
}
