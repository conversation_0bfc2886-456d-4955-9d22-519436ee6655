/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package negocio.interfaces.acesso;

import negocio.comuns.acesso.AutorizacaoAcessoGrupoEmpresarialVO;
import negocio.comuns.basico.ClienteVO;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONArray;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AutorizacaoAcessoGrupoEmpresarialInterfaceFacade extends SuperInterface {
    void incluir(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception;

    void excluir(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception;

    void incluirSemCommit(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception;

    void alterar(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception;

    void alterarSemCommit(AutorizacaoAcessoGrupoEmpresarialVO obj) throws Exception;

    String consultarJSON(Integer empresa) throws Exception;

    JSONArray consultarParaAula(Integer empresa, String parametro) throws Exception;

    Integer consultarCriando(Integer matricula, String chaveAluno) throws Exception;

    AutorizacaoAcessoGrupoEmpresarialVO consultarPorCodigo(
            Integer codigo,
            Integer codigoMatricula,
            String codAcesso,
            String codAcessoAlternativo,
            String senhaAcesso,
            String codigoAutorizacao,
            String tipo,
            Integer codigoGenerico,
            Integer nivelMontarDados, String chaveRemota, Integer codEmpresaRemota) throws Exception;

    String validarExistenciaDados(AutorizacaoAcessoGrupoEmpresarialVO autorizacao) throws Exception;

    List<ClienteVO> consultarPorNomeMock(String nome) throws Exception;

    List<ClienteVO> consultarPorCpfMock(String cpf) throws Exception;

    void alterarSenhaAcesso(AutorizacaoAcessoGrupoEmpresarialVO obj, String senhaEncriptada) throws Exception;

    AutorizacaoAcessoGrupoEmpresarialVO consultarSimples(Integer codigo) throws Exception;

    void excluirAulaAutorizado(Integer matricula, String chaveAluno, Date dia, Integer codigoHorarioTurma) throws Exception;

    void excluirAulaAutorizadoGestaoRede(Integer matricula, String chaveAluno, Date dia, Integer codigoHorarioTurma) throws Exception;

    JSONArray aulasAutorizado(Integer matricula, String chaveAluno, Date dia) throws Exception;

    Integer statusFotoValida(Integer codAutorizacao) throws Exception;

}
