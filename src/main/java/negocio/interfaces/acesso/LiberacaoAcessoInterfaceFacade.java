/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.acesso;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import negocio.comuns.acesso.ColetorVO;
import negocio.comuns.acesso.LiberacaoAcessoVO;
import negocio.comuns.acesso.LocalAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.acesso.enumerador.DirecaoAcessoEnum;
import negocio.comuns.acesso.enumerador.TipoLiberacaoEnum;
import negocio.comuns.basico.PessoaVO;
import negocio.interfaces.basico.SuperInterface;
import relatorio.controle.basico.FechamentoAcessoRelTO;

/**
 *
 * <AUTHOR>
 */
public interface LiberacaoAcessoInterfaceFacade extends SuperInterface {

    public LiberacaoAcessoVO registrarAcesso(Date dataAcesso, DirecaoAcessoEnum direcao, TipoLiberacaoEnum tipoLiberacao, LocalAcessoVO local, <PERSON>tor<PERSON> coletor, UsuarioVO usuario, <PERSON>teger empresa, <PERSON>ess<PERSON><PERSON> pessoa, String situacaoCliente, String justificativa, String nomeGenerico) throws Exception;

    public void setIdEntidade(String idEntidade);

    public int contarTotalLiberacaoFiltros(List<TipoLiberacaoEnum> listaTipoLiberacao, int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException;

    public List<FechamentoAcessoRelTO> montarConsultaRelatorio(int totalLiberacoes, Double percentualLiberacoes, List<TipoLiberacaoEnum> listaTiposLiberacao,
            int empresa, java.sql.Date dataInicial, java.sql.Date dataFinal, String horaInicial, String horaFinal) throws SQLException, Exception;

    public List<LiberacaoAcessoVO> consultarTotalLiberacaoFiltros(TipoLiberacaoEnum tipoLiberacao, int empresa, java.sql.Date dataInicial,
            java.sql.Date dataFinal, String horaInicial, String horaFinal, int nivelMontarDados, boolean justificados, boolean naoJustificados) throws Exception;

    public void incluirJustificativa(LiberacaoAcessoVO obj, String nomeLabelPessoa) throws Exception;

   public int consultarUsuarioJustificou(int codigoLiberacao) throws SQLException ;
   
   public LiberacaoAcessoVO consultarPorChavePrimaria(int codigoLiberacao, int nivelMontarDados) throws SQLException, Exception;

   public List<LiberacaoAcessoVO> consultarTotalLiberacao(Date data,Integer empresa,int nivelMontarDados) throws Exception;

    /**
     *
     * @param data
     * @param empresa       0 para consultar de todas as empresas
     * @return
     * @throws Exception
     */
   Integer consultarTotalLiberacao(Date data,Integer empresa) throws Exception;
}
