package negocio.interfaces.plano;

import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import br.com.pactosolucoes.agendatotal.json.ClienteSintenticoJson;
import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import br.com.pactosolucoes.turmas.json.AlunoAulaAcessoJSON;
import negocio.comuns.basico.AgendaTotalTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MatriculaAlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.ContratoModalidadeReferenceException;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;

import java.sql.ResultSet;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface TurmaInterfaceFacade extends SuperInterface {

    public TurmaVO novo() throws Exception;

    public void incluir(TurmaVO obj) throws Exception;

    public void alterar(TurmaVO obj) throws Exception;

    public void alterarSemCommit(TurmaVO obj) throws Exception ;

    public void excluir(TurmaVO obj) throws ContratoModalidadeReferenceException,Exception;

    public TurmaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso,Boolean aulaColetiva, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorIdentificador(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeModalidade(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorNomeModalidadeIdade(String valorConsulta, Integer idade, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorNomeProfessor(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorDataInicialVigencia(Date prmIni, Date prmFim, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorIdadeMinima(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorIdadeMaxima(Integer valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public TurmaVO consultarPorDiaSemanaHoraInicialHoraFinalAmbienteNivelTurmaProfessorEmpresa(String diaSemana, String horaInicio, String horaFim, Integer ambiente, Integer professor, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorNivelTurma(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public TurmaVO consultarPorHorarioTurma(Integer horarioturma, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarPorCodigoModalidade(Integer codigoModalidade, Integer empresa, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoModalidade(Integer codigoModalidade, Integer empresa,  boolean excluiAulaColetiva, int nivelMontarDados) throws Exception;

    public List consultarPorCodigoModalidadeVigenciaMaiorQueHoje(Integer codigoModalidade, Integer empresa, int nivelMontarDados) throws Exception;

    public List<TurmaVO> consultarPorNomeTurmaProfessorComLimite(String valorConsulta, int professor) throws Exception;

    public List consultarPorDescricaoTurma(String valorConsulta, Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public boolean existemAlunosNaTurma(TurmaVO turmaVO) throws Exception;

    public String consultarJSON(Integer empresa,String tipoTurma) throws Exception;

    public List consultarParaImpressao(String filtro, String ordem, String campoOrdenacao, Integer empresa,String tipoTurma) throws Exception;

    public boolean existemMatriculasNaTurma(TurmaVO turmaVO) throws Exception;

    public List<TurmaVO> consultar(Integer empresa, int nivelMontarDados) throws Exception;

    public List consultar(boolean somenteVigentes, boolean somenteAulaCheia, Integer modalidade, Integer empresa, int nivelMontarDados, boolean somenteNaoVigentes) throws Exception;

    List<TurmaVO> consultar(boolean somenteVigentes, boolean somenteAulaCheia, Integer modalidade, Integer empresa, int nivelMontarDados, boolean somenteNaoVigentes, int idade, String modalidades, Integer codigoPessoa) throws Exception;
    
    public Map consultarHorariosTurmaParaAgenda(Date inicio, Integer empresa, List<Integer> modalidades, boolean somenteAulasColetivas, Integer codigoHorario, Integer matricula, boolean isApp) throws Exception;

    Map consultarHorariosTurmaParaAgenda(Date inicio, Integer empresa, List<Integer> modalidades, boolean somenteAulasColetivas, Integer codigoHorario, Integer matricula, boolean isApp, boolean isAgendaOnline) throws Exception;

    public Integer codigoNivelTurma() throws Exception;
    
    List<TurmaAulaCheiaJSON> obterAulasColetivasPaginada(Integer empresa, ListaPaginadaTO paginadorLista, JSONObject filtros) throws Exception;

    List<TurmaAulaCheiaJSON> obterAulasColetivasSemPontuacaoaAtivaPaginada(Integer empresa, ListaPaginadaTO paginadorLista) throws Exception;

    List<TurmaAulaCheiaJSON> obterAulasColetivas(Integer empresa)throws Exception;
    
    public String desativarAulaColetiva(Integer codigoAula, Integer usuario) throws Exception;

    void salvarFotoKeyTurma(Integer codigoTurma, String fotoKey) throws Exception;

    String obterFotoKeyTurma(Integer codigoTurma) throws Exception;

    public boolean existeVinculoFuturoAlunoAulaColetiva(Integer codigoAula) throws Exception;

    JSONArray consultarTodas() throws Exception;
    public TurmaVO consultar(String nomeTurma,Integer codigoModalidade, Integer codigoEmpresa, int nivelMontarDados) throws Exception ;
    
    public List<AgendaTotalTO> consultaAulasContrato(ClienteVO cliente,Integer contrato,boolean habilitarSomaDeAulaNaoVigente,List<MatriculaAlunoHorarioTurmaVO> listaMatriculaHorarioTurma) throws Exception ;

    /**
     * Calcula a lista de alunos que estão agendados para aquele horário de turma na data informada.
     * Os alunos podem ou não estar com sua presença confirmada.
     *
     * @param data               Data da aula que se deseja os alunos
     * @param codigoHorarioTurma Código do horário da turma exigido
     * @return Lista de alunos que estão na aula naquele horário
     * @throws Exception Caso ocorra algum problema na consulta
     */
    Map<String, List<AgendadoJSON>> consultarAulasAlunosNaoColetivas(Date data, Integer codigoHorarioTurma) throws Exception;

    /**
     * Calcula a quantidade de alunos que estão agendados para aquele horário de turma na data informada.
     * Os alunos podem ou não estar com sua presença confirmada.
     *
     * @param data               Data da aula que se deseja os alunos
     * @param codigoHorarioTurma Código do horário da turma exigido
     * @return A quantidade de alunos que estão na aula naquele horário
     * @throws Exception Caso ocorra algum problema na consulta
     */
    Integer contarAulasAlunosNaoColetivas(Date data, Integer codigoHorarioTurma, Integer nrVagas) throws Exception;

    public List<TurmaAulaCheiaJSON> obterAulasColetivasPorDiaSemana(Integer empresa, Integer dia, Date agora) throws Exception;

    public List<AlunoAulaAcessoJSON> obterAlunosDaAulaComAcesso(Integer horarioTurma,
                                                                String dataAula) throws Exception;
    List<Integer> getCodigoAlunaAulaDesmarcada(Integer codigoHoraAula, Date data, boolean desconsiderarMarcados) throws Exception;

    List<ClienteSintenticoJson> findClienteSintetico(List<AgendadoJSON> alunos) throws Exception;

    void inativarTurmas() throws Exception;

    List<TurmaAulaCheiaJSON> consultarPorDescricaoTurmaColetiva(String pref, Integer empresa) throws Exception;

    TurmaAulaCheiaJSON consultarPorCodigoTurmaColetiva(Integer codigo, Integer empresa) throws Exception;

    TurmaAulaCheiaJSON consultarPorCodigoTurmaColetiva(Integer codigo) throws Exception;

    void alterarPontos(Integer codigo, Integer pontos) throws Exception;

    List consultarPorCodigoModalidadeEOuProfessor(boolean turmaVigente, boolean somenteNaoVigente, int modalidadeSelecionada, int professorSelecionado, Integer codigo, int nivelmontardadosDadosbasicos) throws Exception;

    Boolean validarColetorPreenchido(Integer horarioTurma) throws Exception;

    /**
     * Consulta o código de todas as turmas cadatrados no sistema.
     * @return
     * @throws Exception
     */
    public List<Integer> consultarTodosCodigos() throws  Exception;

    /**
     * Atualiza o descrição da turma.
     * @param codigo
     * @param descricao
     * @throws Exception
     */
    void alterarDescricaoTurma(Integer codigo, String descricao) throws  Exception;

    boolean verificarTurmaParaContrato(Date dataNasc, Integer modalidade, Date dataContrato, Integer empresa) throws Exception;

    void alterarProdutoGymPass(Integer produtoGymPass, Integer codigo) throws Exception;

    void alterarIdClasseGymPass(Integer idClasseGymPass, Integer codigo) throws Exception;

    void enviarGymPassBooking(TurmaVO turmaVO) throws Exception;

    TurmaVO consultarUltimoCadastradoPorProdutoGymPass(Integer produtoGymPass, int nivelMontarDados) throws Exception;

    boolean consultarConfigNaoValidarModalidadeContratoAula(int codHorarioTurma) throws Exception;

    int contarTurmasPorDataInicialVigenciaModalidade(Date prmIni, Date prmFim, Integer empresa, Integer codModalidade) throws Exception;

    ResultSet consultarToleranciaTurma(Integer codigoHorarioTurma) throws Exception;

    JSONArray aulasCodigos(Integer empresa) throws Exception;

    void atualizarUsuarioDesativou(TurmaVO turma);

    HorarioTurmaVO obterAulaExperimentalPassivoIndicado(Date dia, Integer passivo, Integer indicado, Integer cliente) throws Exception;

    TurmaVO consultarPorIdexterno(String idExterno, int nivelmontardadosDadosbasicos);

    int contarTurmasAtivasPorDataInicialVigenciaVariasModalidade(Date prmIni, Date prmFim, Integer empresa, String modalidades, TurmaVO turma) throws Exception;

    int contarTurmasAtivasComAlunoPorDataInicialVigenciaVariasModalidade(Date prmIni, Date prmFim, Integer empresa, String modalidades, TurmaVO turma, Integer vezesSemana) throws Exception;

    long qtdHoraAulaAtivaComAlunosPeriodoEmMinutos(Date prmIni, Date prmFim, Integer empresa, String modalidades, TurmaVO turma, Integer vezesSemana) throws Exception;

}
