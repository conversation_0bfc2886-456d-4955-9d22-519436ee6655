package negocio.interfaces.plano;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.agendatotal.json.AgendadoJSON;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.plano.*;
import org.json.JSONArray;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.contrato.HorarioTurmaConcatenadoVO;
import negocio.comuns.plano.AlunoHorarioTurmaVO;
import negocio.comuns.plano.HorarioTurmaTO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.TurmaVO;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface HorarioTurmaInterfaceFacade extends SuperInterface {

    public HorarioTurmaVO novo() throws Exception;

    public void incluir(HorarioTurmaVO obj) throws Exception;

    public void alterar(HorarioTurmaVO obj) throws Exception;

    public void excluir(HorarioTurmaVO obj) throws Exception;

    public void desativar(HorarioTurmaVO obj) throws Exception;

    public List<HorarioTurmaVO> consultarTodos(int nivelMontarDados) throws Exception;

    public HorarioTurmaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public HorarioTurmaVO consultarPorCodigo(Integer valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorIdentificador(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorHoraInicial(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorSituacaoColaborador(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoAmbiente(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoNivelTurma(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorSituacao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluirHorarioTurmas(TurmaVO turmaPrm, List objetos) throws Exception;

    public void alterarHorarioTurmas(TurmaVO turma, List objetos, List objetosExclusao) throws Exception;

    public void excluirHorarioTurmas(Integer turma) throws Exception;

    public List consultarHorarioTurmas(Integer turma, int nivelMontarDados) throws Exception;

    public List consultarTodosHorariosTurma(Integer turma, int nivelMontarDados) throws Exception;

    public HorarioTurmaVO consultarPorDiaSemana(HorarioTurmaConcatenadoVO horarioTurmaConcatenadoVO, String diaSemana, boolean controlarAcesso, int nivelMontarDados)throws Exception;

    public HorarioTurmaVO consultarPorDiaSemanaHorarioInicialHorarioFinalAmbiente(HorarioTurmaVO horarioTurmaVO,Integer empresa, boolean controlarAcesso, int nivelMontarDados) throws Exception ;

    public HorarioTurmaVO consultarPorProfessorDiaDaSemanaHorarioInicialFinal(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception ;

    public List<HorarioTurmaVO> consultarPorDiaSemanaHorarioModalidadeTurmaProfessorEmpresa(String diaSemana, String horario, Integer modalidade, Integer turma, Integer professor, Integer empresa, int nivelMontarDados) throws Exception;

    public List<HorarioTurmaVO> consultarPorEmpresaModalidadeTurmaIdade(Integer empresa, Integer modalidade,Integer turma, Integer idade,List<String> diaSemana, List<String> horario, Date data, Boolean negociacao, int nivelMontarDados, boolean bloquearLotacaoFutura, Integer pessoaOperacao) throws Exception;

    public List<HorarioTurmaVO> consultarPorEmpresaModalidadeTurmaProfessorIdade(int empresa, int modalidade, int turma, int professor, int ambiente, int idade, int nivel, List<String> diaSemana, List<String> horario, Date datainicio, Date datafim, Boolean negociacao, int nivelMontarDados, boolean todas, boolean bloquearLotacaoFutura, Integer pessoaOperacao) throws Exception;

    public HorarioTurmaVO consultarPorProfessorDiaDaSemanaHorarioInicialFinalForaDoAmbiente(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception;

    public List<HorarioTurmaVO> consultarHorariosConflitantesParaProfessorSemAmbiente(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception;

    public List<HorarioTurmaVO> consultarHorariosIguais(HorarioTurmaVO horarioTurmaVO, Integer empresa, int nivelMontarDados) throws Exception;

    public List<HorarioTurmaVO> consultarColaboradorEmTurmas(ColaboradorVO colaboradorVO, int nivelMontarDados) throws Exception;

    public int nrAlunosMatriculadosRenovacaoRematricula(HorarioTurmaVO obj, Date dataInicio, Set<Integer> codClientes) throws Exception;
    public List<HorarioTurmaVO> consultarHorarioTurmaContratoCreditoTreino(Date dataBase)throws Exception;

    public void incluirAlunoAulaCheia(AlunoHorarioTurmaVO obj, AgendaTotalJSON json, String key) throws Exception;

    public void incluirAlunoAulaCheiaV2(AlunoHorarioTurmaVO obj, AgendaTotalJSON json, String key) throws Exception;

    void atualizarReposicao(AlunoHorarioTurmaVO obj) throws Exception;

    public void incluirAlunoFilaEspera(FilaDeEsperaVO obj) throws Exception;

    public void excluirAlunoAulaCheia(AlunoHorarioTurmaVO obj) throws Exception;

    public List<AgendadoJSON> consultarAgendadosAulaColetiva(Date inicio, Date fim, Integer empresa) throws Exception;

    public List<HorarioTurmaVO> consultarHorarioTurmaComBaseHorarios(Integer codigoHorarioTurma, String[] horarios,String[] dias, boolean excluir) throws Exception;

    public boolean alunoAgendadoHorarioDia(AlunoHorarioTurmaVO obj) throws Exception;

    boolean alunoAgendadoHorarioDiaPorTerminal(Integer codCliente, Date dataConsulta, String terminal) throws Exception;

    JSONArray consultarTodasHoraInicial() throws Exception;
    public int nrMatriculasFuturas(HorarioTurmaVO obj, Date dataInicio) throws Exception;

    public String horarioTurmaSimplificado(Integer contrato) throws Exception;

    List<HorarioTurmaVO> consultar(Integer codigoContrato, int nivelMontarDados)throws Exception;

    HorarioTurmaVO consultar(String horaInicial,String horaFinal, String diaSemana, Integer turma, Integer ambiente, Integer nivelTurma, Integer professor, int nivelMontarDados) throws Exception;

    HorarioTurmaVO consultarPorHorarioDiaSemanaCodTurma(String horaInicial,String horaFinal, String diaSemana, Integer codigoTurma, int nivelMontarDados) throws Exception;

    List<ClienteHorarioTurmaDescontoTO> processaDescontoHorario(Integer horario, Date dataInicio, Date dataTermino) throws Exception;

    HorarioTurmaTO processaFrequenciaPorHorario(HorarioTurmaVO horario, Date dataInicio, Date dataTermino, boolean exibirReposicoes) throws Exception;

    public JSONArray consultarAulaPeloClienteHorario(String matricula,Date data, String horaInicial, String horaFinal) throws Exception;

    public boolean temDiaria(Integer codigoCliente, Integer codigoModalidade, Date data) throws Exception;

    Integer produtoDiaria(Integer codigoCliente, Integer codigoModalidade, Date data) throws Exception;

    Integer obterModalidadeAulaAvulsaDiaria(Integer codigoCliente, Date data) throws Exception;

    Integer modalidadeDoHorario(Integer horarioTurma) throws Exception;

    public int nrAulasColetivasMarcadasAlunoPorModalidadePeriodoContabilizadoPorDia(int modalidade, int cliente,
                                                                 Date dataInicio, Date datafinal, boolean contabilizarAulaPorDia) throws Exception;

    public int nrAulasColetivasMarcadasAlunoPorModalidadePeriodo(int modalidade, Date vigenciaContrato, int cliente,
                                                                 Date dataAula,
                                                                 Date dataInicio, Date datafinal, boolean vezessemana) throws Exception;

    public int nrAulasColetivasMarcadasAlunoPorPeriodo(int cliente,
                                                       Date dataAula,
                                                       Date dataInicio, Date datafinal) throws Exception;

    public List<HorarioTurmaVO> horarioTurmaAlunoAgendadoHorarioDia(Integer codCliente, Date dataConsulta) throws Exception;

    Integer existeHorarioTurma(int codigo, HorarioTurmaVO ht) throws Exception;

    public List<HorarioTurmaVO> consultarGestaoTurma(boolean turmaAtiva, boolean turmaNaoVigente, int codigoEmpresa, int codigoTurma, int codigoProfessor, int codigoModalidade, int nivelMontarDados) throws Exception;

    public void alterarHorariosGestaoTurma(HorarioTurmaVO obj) throws Exception;

    List<HorarioTurmaVO> consultarPorTurma(TurmaVO turma, int nivelMontarDados) throws Exception;

    List<HorarioTurmaVO> consultarPorModalidade(ModalidadeVO modalidadeVO, int nivelMontarDados) throws Exception;

    List<HorarioTurmaVO> consultarPorModalidadeProfessorDiaColetivas(int modalidade, int professor, Date dia, int nivelMontarDados) throws Exception;

    public List<AlunoHorarioTurmaVO> consultarAulasColetivasAluno(Date dataAula, Integer codigoCliente, int nivelMontarDados)throws Exception;

    public AlunoHorarioTurmaVO montarDadosBasicoAlunoHorarioTurmaUltimaDataComAgendamento(Integer codigoTurma, Date inicioVigencia, Date fimVigencia) throws Exception;

    String alunoAgendadoHorarioDia(Date dia, Date agora, Integer codCliente, Integer empresa, boolean validacaoOffline) throws Exception;

    List<Integer> consultarClientesComAgendamentosNoHorario(Date dia, Integer codigoHorarioTurma, Integer codigoEmpresa, boolean validarFuturo) throws SQLException;

    void incluirAlunoAulaCheiaBooking(Integer codigo, String booking ) throws Exception;

    JSONObject consultarParaWS(Integer codigo) throws Exception;

    void gravarAlunoHorarioTurmaDesmarcado(Integer horarioTurma,
                                                  Integer cliente,
                                                  Integer contrato,
                                                  Date dia,
                                                  String origem,
                                                  Integer usuario,
                                                  Integer limiteReposicoes,
                                                  Boolean manterRenovacao,
                                                  Boolean bloquearGerarReposicaoAulaJaReposta);

    String modalidadeHorario(Integer codigoHorarioTurma);

    String tipoModalidadeHorario(Integer codigoHorarioTurma);

    int reposicoesDisponiveisAulaColetiva(Integer cliente) throws Exception;

    boolean apenasAulaColetiva(Integer[] codigosHorarios) throws Exception;

    List<AlunoHorarioTurmaVO> consultarHorarioTurmasCancelamentoContrato(Integer codigoCliente, Integer codigoEmpresa, int nivelMontarDados)throws Exception;

    void excluirAlunoHorarioturma(Integer codigoAlunoHorarioTurma) throws Exception;

    void validarHorarioTurmaPossuiVagasPorCapacidadeCategorias(String horarios, Integer pessoa) throws Exception;

    void atualizarNrAlunosMatriculadosAulaDoContrato(ContratoVO contratoVO, Date dataBase) throws Exception;
}
