package negocio.interfaces.plano;

import negocio.comuns.plano.ProdutoSugeridoVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface ProdutoSugeridoInterfaceFacade extends SuperInterface {

    public ProdutoSugeridoVO novo() throws Exception;

    public void incluir(ProdutoSugeridoVO obj) throws Exception;

    public void alterar(ProdutoSugeridoVO obj) throws Exception;

    public void excluir(ProdutoSugeridoVO obj) throws Exception;

    public ProdutoSugeridoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeModalidade(String valorConsulta, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoProduto(String valorConsulta, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    /**
     * Operação responsável por incluir objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Garantindo o relacionamento com a entidade principal <code>plano.Modalidade</code> através do atributo de vínculo.
     * @param objetos List contendo os objetos a serem gravados no BD da classe.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void incluirProdutoSugeridos(Integer modalidadePrm, List objetos) throws Exception;

    /**
     * Operação responsável por alterar todos os objetos da <code>ProdutoSugeridoVO</code> contidos em um Hashtable no BD.
     * Faz uso da operação <code>excluirProdutoSugeridos</code> e <code>incluirProdutoSugeridos</code> disponíveis na classe <code>ProdutoSugerido</code>.
     * @param objetos  List com os objetos a serem alterados ou incluídos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void alterarProdutoSugeridos(Integer modalidade, List objetos) throws Exception;

    /**
     * Operação responsável por excluir todos os objetos da <code>ProdutoSugeridoVO</code> no BD.
     * Faz uso da operação <code>excluir</code> disponível na classe <code>ProdutoSugerido</code>.
     * @param <code>modalidade</code> campo chave para exclusão dos objetos no BD.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public void excluirProdutoSugeridos(Integer modalidade) throws Exception;

    /**
     * Operação responsável por consultar todos os <code>ProdutoSugeridoVO</code> relacionados a um objeto da classe <code>plano.Modalidade</code>.
     * @param modalidade  Atributo de <code>plano.Modalidade</code> a ser utilizado para localizar os objetos da classe <code>ProdutoSugeridoVO</code>.
     * @return List  Contendo todos os objetos da classe <code>ProdutoSugeridoVO</code> resultantes da consulta.
     * @exception Exception  Erro de conexão com o BD ou restrição de acesso a esta operação.
     */
    public List consultarProdutoSugeridos(Integer modalidade, int nivelMontarDados) throws Exception;
}
