package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ItemGestaoNotasTO;
import negocio.comuns.financeiro.enumerador.TipoRelatorioDF;
import negocio.comuns.plano.PlanoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;


public interface GestaoNotasInterfaceFacade extends SuperInterface {

    List<ItemGestaoNotasTO> obterDados(
            TipoRelatorioDF tipoRelatorio, Integer codEmpresa, Integer formaPagamento,
            Date dataIniLanc, Date dataFimLanc, Integer codPessoa, boolean familia, boolean mostrarPorDiaACompetencia,
            boolean processoAutomatico, List<PlanoVO> planosFiltrar) throws Exception;

    List<ItemGestaoNotasTO> obterDadosNFCe(
            TipoRelatorioDF tipoRelatorio, Integer codEmpresa, Integer formaPagamento,
            Date dataIniLanc, Date dataFimLanc, Integer codPessoa, boolean processoAutomatico, List<PlanoVO> planosFiltrar) throws Exception;
}
