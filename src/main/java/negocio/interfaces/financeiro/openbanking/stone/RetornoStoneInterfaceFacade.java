package negocio.interfaces.financeiro.openbanking.stone;

import negocio.comuns.financeiro.openbanking.stone.RetornoStoneVO;
import negocio.interfaces.basico.SuperInterface;

public interface RetornoStoneInterfaceFacade extends SuperInterface {

    void incluir(RetornoStoneVO obj) throws Exception;

    void alterar(RetornoStoneVO obj) throws Exception;

    RetornoStoneVO buscarRetornoStoneByEventId(String eventId, Integer empresa);
}
