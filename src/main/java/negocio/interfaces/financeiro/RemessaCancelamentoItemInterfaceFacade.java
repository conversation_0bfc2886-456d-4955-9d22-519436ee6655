package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaCancelamentoItemVO;
import negocio.interfaces.basico.SuperInterface;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * Created by GlaucoT on 08/04/2016
 */
public interface RemessaCancelamentoItemInterfaceFacade extends SuperInterface {

    void incluir(RemessaCancelamentoItemVO obj) throws Exception;

    void alterar(RemessaCancelamentoItemVO obj) throws Exception;

    void excluir(RemessaCancelamentoItemVO obj) throws Exception;

    List<RemessaCancelamentoItemVO> consultarItensAutorizados(ConvenioCobrancaVO convenio, Date dataInicio, Date dataFim) throws Exception;

    List<RemessaCancelamentoItemVO> consultarPorCodigoRemessa(Integer codRemessa, int nivelMontarDados) throws Exception;

    List<RemessaCancelamentoItemVO> consultarPorCodigoRemessaItem(Integer codRemessaItem, int nivelMontarDados) throws Exception;

    void excluirPorCodigoRemessa(int codigoRemessa) throws SQLException;
}
