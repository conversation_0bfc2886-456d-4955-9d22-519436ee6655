package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.interfaces.basico.SuperInterface;

import javax.faces.model.SelectItem;
import java.sql.Date;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface FormaPagamentoInterfaceFacade extends SuperInterface {


    public FormaPagamentoVO novo() throws Exception;

    public void incluir(FormaPagamentoVO obj) throws Exception;

    public void incluirSemCommit(FormaPagamentoVO obj) throws Exception ;

    public void alterar(FormaPagamentoVO obj) throws Exception;

    public void excluir(FormaPagamentoVO obj) throws Exception;

    public FormaPagamentoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List<FormaPagamentoVO> consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<FormaPagamentoVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoTipoFormaPagamento(String valorConsulta, boolean controlarAcesso, boolean somenteZW, boolean naoRecorrencia, boolean somenteAtivo, int nivelMontarDados) throws Exception;

    public List consultarPorDescricaoTipoFormaPagamento(String valorConsulta, boolean controlarAcesso, boolean somenteZW, boolean naoRecorrencia, boolean somenteAtivo,Integer empresa,  int nivelMontarDados) throws Exception;

    List consultarPorDescricaoTipoFormaPagamento(String valorConsulta,
                                                        boolean controlarAcesso,
                                                        boolean somenteZW,
                                                        boolean naoRecorrencia,
                                                        boolean somenteAtivo,
                                                        Integer empresa,
                                                        int nivelMontarDados,
                                                        Boolean pinpad) throws Exception;
    public List consultarPorDescricaoConvenioCobranca(String valorConsulta, int nivelMontarDados) throws Exception;

    public FormaPagamentoVO consultarPorTipoFormaPagamento(String valorConsulta, int nivelMontarDados) throws Exception ;

    public FormaPagamentoVO consultarPrimeiraFormaPagamentoAVista(int nivelMontarDados) throws Exception ;

    public List<FormaPagamentoVO> consultarPorTipoFormaPagamento(String valorConsulta, boolean somenteZW, boolean noRecorrencia, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<FormaPagamentoVO> consultarPorTipoFormaPagamentoGestaoDeNotas(String valorConsulta, boolean somenteZW, boolean noRecorrencia, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    List<FormaPagamentoVO> consultarPorTipoFormaPagamento(String valorConsulta, boolean somenteZW, boolean noRecorrencia, boolean controlarAcesso, boolean considerarTodos, int nivelMontarDados) throws Exception;

    public List<FormaPagamentoVO> consultarPorTiposFormaPagamento(List<TipoFormaPagto> tipoFormaPagtos,
                                                                  Integer empresa,
                                                                  boolean somenteAtivos,
                                                                  boolean somenteZW,
                                                                  Boolean receberSomenteViaPinPad,
                                                                  int nivelMontarDados) throws Exception;

    List<FormaPagamentoVO> consultarPorTiposFormaPagamentoSimples(final TipoFormaPagto[] tipos,
                                                                         Integer empresa,
                                                                         boolean somenteAtivos,
                                                                         int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public void incluir(FormaPagamentoVO obj, boolean centralEventos) throws Exception;

    public void alterar(FormaPagamentoVO obj, boolean centralEventos) throws Exception;

    public void excluir(FormaPagamentoVO obj, boolean centralEventos) throws Exception;

    public boolean existeOutraFormaPagamentoDefault(FormaPagamentoVO obj, boolean recorrencia) throws SQLException;

    public FormaPagamentoVO criarOuConsultarSeExistePorDescricao(FormaPagamentoVO obj) throws Exception;
    
    public FormaPagamentoVO criarOuConsultarSeExistePorFlag(FormaPagamentoVO obj) throws Exception;

    public FormaPagamentoVO consultarAVista() throws Exception;

    public FormaPagamentoVO consultarFormaPorConvenio(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    FormaPagamentoVO consultarPorTipo(TipoFormaPagto tipoFormaPagto) throws Exception;

    public FormaPagamentoVO consultarPorTipoFormaPagamentoAtiva(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public String consultarJSON(String situacao) throws Exception;

    public List consultarParaImpressao(String situacao, String filtro, String ordem, String campoOrdenacao, int i) throws SQLException;
    
    public boolean existeOutraFormaPagamentoZW(FormaPagamentoVO obj) throws SQLException ;
    
    public void atualizarCorFormaPagamento(Integer codigo, String cor) throws Exception;

    List<FormaPagamentoVO> consultarFormaPagamentoComConvenioCobranca(boolean somenteAtivo, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<FormaPagamentoVO> consultarSimplesFormasPagamento(boolean exibirfinanceiro) throws Exception;

    public TipoFormaPagto consultarTipoFormasPagamento(Integer codigo) throws Exception;

    /**
     * Verifica se existe outra forma de pagamento cadastrada com o mesmo nome na base de dados.
     * @param obj
     * @return
     * @throws Exception
     */
    boolean existeOutraFormaPagamentoMesmaDescricao(FormaPagamentoVO obj) throws  Exception;

    public FormaPagamentoVO obterFormaPagamentoRapido(TipoFormaPagto tipo) throws Exception;

    List<SelectItem> consultarTipoDebitoOnlineCadastrados() throws Exception;

    List<SelectItem> opcoesFormaEmpresa(Integer empresa, TipoFormaPagto tipo) throws Exception;

    Set<String> consultarTipoTodosEmpresa(Integer codigo) throws Exception;

    List<FormaPagamentoVO> consultarPorTipoFormaPagamento(TipoFormaPagto tipoFormaPagto, Boolean situacao, int nivelMontarDados) throws Exception;

     String consultaTipoFormaPagamento(Integer codigo)throws Exception;
}
