package negocio.interfaces.financeiro;

import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.json.JSONObject;
import servicos.pix.*;

import java.io.IOException;


public interface PixServiceInterfaceFacade {

    PixRequisicaoDto cancelar(PixVO pixVO) throws Exception;

    HttpClient createConnector();

    HttpClient createConnector(String path, String senha);

    PixRequisicaoDto consultarCobranca(PixVO pixVO) throws Exception;

    PixRequisicaoDto consultarCobrancaE2EId(PixVO pixVO) throws Exception;

    PixRequisicaoDto consultarCobranca(String txId, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                   String nomeDevedor, String telefoneDevedor,
                                   Double valor, String descricao, Integer expiracao) throws Exception;

    PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, String cpfDevedor,
                                   String nomeDevedor, String telefoneDevedor,
                                   Double valor, String descricao, Integer expiracao, String chave) throws Exception;

    PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO,
                                   PessoaVO pessoaVO,
                                   Double valor, String descricao, Integer expiracao, EmpresaVO empresaVO) throws Exception;

    PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto) throws Exception;

    PixRequisicaoDto criarCobranca(ConvenioCobrancaVO convenioCobrancaVO, PixDto pixDto, String chave) throws Exception;

    PixResponseErrorDto responseErrorDto(String responseJson);

    String fixResponseErros(String responseJson);

    void responseCobrancaDto(PixRequisicaoDto pixRequisicaoDto, HttpResponse response) throws IOException, PixRequestException;

    String translateMessages(String message);

    void validateResponseError(String responseJsonString, int status) throws PixRequestException;

    TokenVO responseTokenDto(HttpResponse response) throws Exception;
    TokenVO responseTokenDto(HttpResponse response, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

     String token(PixVO pixVO) throws Exception;

     String token(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

     String token(String basicToken, AmbienteEnum ambiente) throws Exception;

    public String obterQRCode(String idExterno, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    JSONObject consultarWebhookAtivo(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    boolean configurarUrlCallback(ConvenioCobrancaVO convenioCobrancaVO, String urlCallback) throws Exception;

    boolean excluirUrlCallback(ConvenioCobrancaVO convenioCobrancaVO) throws Exception;

    String token(HttpClient httpClient, ConvenioCobrancaVO convenioCobrancaVO) throws Exception;
}
