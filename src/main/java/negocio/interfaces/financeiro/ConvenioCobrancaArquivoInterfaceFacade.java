package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.ConvenioCobrancaArquivoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 24/01/2024
 */

public interface ConvenioCobrancaArquivoInterfaceFacade extends SuperInterface {

    void incluir(ConvenioCobrancaArquivoVO obj) throws Exception;

    void excluir(int codConvenioCobranca) throws Exception;

    void excluirPorChavePrimaria(int codigo) throws Exception;

    ConvenioCobrancaArquivoVO consultarPorConvenioCobranca(int convenioCobranca, int nivelMontarDados) throws Exception;

    List<ConvenioCobrancaArquivoVO> consultarListaPorConvenioCobranca(int convenioCobranca, boolean montarArquivo) throws Exception;

    byte[] consultarArquivoBytePorChavePrimaria(int codigo) throws Exception;

}
