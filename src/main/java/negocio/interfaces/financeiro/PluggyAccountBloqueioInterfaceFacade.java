package negocio.interfaces.financeiro;

import negocio.comuns.financeiro.PluggyAccountBloqueioVO;
import negocio.interfaces.basico.SuperInterface;

public interface PluggyAccountBloqueioInterfaceFacade extends SuperInterface {

    void incluir(PluggyAccountBloqueioVO obj) throws Exception;

    boolean consultarById(String id) throws Exception;

    void excluirById(String id) throws Exception;

    void excluirByIdPluggyItem(String idPluggyItem) throws Exception;

}
