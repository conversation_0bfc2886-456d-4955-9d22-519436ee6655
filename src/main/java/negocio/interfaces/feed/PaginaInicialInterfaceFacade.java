/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package negocio.interfaces.feed;

import java.sql.ResultSet;
import negocio.comuns.feed.PaginaInicialFeedVO;
import negocio.interfaces.basico.SuperInterface;

/**
 *
 * <AUTHOR>
 */
public interface PaginaInicialInterfaceFacade extends SuperInterface{
    
    public PaginaInicialFeedVO incluirPaginaInicial(PaginaInicialFeedVO pagina) throws Exception;
    
    public void alterarPaginaInicial(PaginaInicialFeedVO pagina) throws Exception;
    
    public PaginaInicialFeedVO consultarPaginaInicial() throws Exception;
    
    public PaginaInicialFeedVO montarPaginaInicial(ResultSet rs) throws Exception; 
}
