package negocio.interfaces.crm;
import negocio.comuns.crm.DefinirLayoutVO;
import java.util.List;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface DefinirLayoutInterfaceFacade extends SuperInterface {
	

    public DefinirLayoutVO novo() throws Exception;
    public void incluir(DefinirLayoutVO obj) throws Exception;
    public void alterar(DefinirLayoutVO obj) throws Exception;
    public void excluir(DefinirLayoutVO obj) throws Exception;
    public DefinirLayoutVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados ) throws Exception;
    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados ) throws Exception;
    public List<DefinirLayoutVO> consultarPorUsuario(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados ) throws Exception;
    public void setIdEntidade(String aIdEntidade);
}