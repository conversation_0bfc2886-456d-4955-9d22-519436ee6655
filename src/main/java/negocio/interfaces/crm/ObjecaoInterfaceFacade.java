package negocio.interfaces.crm;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.crm.HistoricoContatoVO;
import negocio.comuns.crm.ObjecaoBICRMTO;
import negocio.comuns.crm.ObjecaoVO;
import negocio.interfaces.basico.SuperInterface;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
*/
public interface ObjecaoInterfaceFacade extends SuperInterface {
	

    public ObjecaoVO novo() throws Exception;

    public void incluir(ObjecaoVO obj) throws Exception;

    public void alterar(ObjecaoVO obj) throws Exception;

    public void excluir(ObjecaoVO obj) throws Exception;

    public ObjecaoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados ) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados ) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public List consultarObjecao(boolean controlarAcesso, String tipoGrupo, int nivelMontarDados) throws Exception;

    public List<ObjecaoVO> consultarTodas(int nivelMontarDados) throws Exception;

    public List consultarObjecao(Boolean vindoTelaMetaPerda) throws Exception;

    public List consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorGrupo(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void executarRegraNegocioParaFecharMetaAlteracao(Date dia, Integer colaboradorResponsavel, Integer passivo, Integer indicado, Integer cliente, HistoricoContatoVO hist) throws Exception;

    public List consultarObjecaoDefinitiva(boolean controlarAcesso,  int nivelMontarDados) throws Exception;

    public String consultarJSON() throws Exception;

    List<ObjecaoBICRMTO> consultarObjecoesBICRM(Date dataInicio, Date dataFim, List<UsuarioVO> usuarioVOList, boolean porFase) throws Exception;
}