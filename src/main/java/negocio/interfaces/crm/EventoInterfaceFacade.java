package negocio.interfaces.crm;

import negocio.comuns.crm.EventoVO;

import java.util.Date;
import java.util.List;

import negocio.comuns.financeiro.BIEventoDTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface EventoInterfaceFacade extends SuperInterface {

    public EventoVO novo() throws Exception;

    public void incluir(EventoVO obj) throws Exception;

    public void alterar(EventoVO obj) throws Exception;

    public void excluir(EventoVO obj) throws Exception;

    public EventoVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public void setIdEntidade(String aIdEntidade);

    public EventoVO consultarPorNomeEvento(String valorConsulta, Integer empresa, int nivelMontarDados) throws Exception;

    public List<EventoVO> consultarPorDescricao(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarTodosEventosComLimite(boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List consultarPorNomeEventoComLimite(String valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<EventoVO> consultarPorNomeEvento(String valorConsulta, int nivelMontarDados) throws Exception;

    public List<EventoVO> consultarPorNomeSituacaoEvento(String valorConsulta, Boolean situacao, int nivelMontarDados) throws Exception;

    public EventoVO consultarPorCodigo(Integer codigoPrm, int nivelMontarDados) throws Exception;

    public String consultarJSON() throws Exception;

    public List<EventoVO> consultarTodosEventosAtivos(boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public List<EventoVO> consultarPorNomeEventoAtivo(String valorConsulta, int nivelMontarDados) throws Exception;
    List<EventoVO>consultar(Date dataBaseConsulta)throws Exception;
    List<EventoVO> consultarEventosVigente()throws Exception;
}
