package negocio.interfaces.crm;

import br.com.pactosolucoes.enumeradores.FasesCRMEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.PermissaoAcessoMenuVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.*;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle
 * e camada de negócio (em especial com a classe Façade). Com a utilização desta interface 
 * é possível substituir tecnologias de uma camada da aplicação com mínimo de impacto nas demais.
 * Além de padronizar as funcionalidades que devem ser disponibilizadas pela camada de negócio, por intermédio
 * de sua classe Façade (responsável por persistir os dados das classes VO).
 */
public interface AberturaMetaInterfaceFacade extends SuperInterface {

    public AberturaMetaVO novo() throws Exception;

    public void incluir(AberturaMetaVO obj) throws Exception;

    public void incluirSemCommit(AberturaMetaVO obj) throws Exception;

    public void alterar(AberturaMetaVO obj) throws Exception;

    public void excluir(AberturaMetaVO obj) throws Exception;

    public AberturaMetaVO consultarPorChavePrimaria(Integer codigo, int nivelMontarDados) throws Exception;

    public List consultarPorCodigo(Integer valorConsulta, boolean controlarAcesso, int nivelMontarDados) throws Exception;

    public AberturaMetaVO consultarAberturaPorCodigoUsuario(Integer codigoUsuario, Date dia, Integer empresa, int nivelMontarDados) throws Exception;

    public void executarInclusaoAberturaMetaFecharMeta(AberturaMetaVO obj);

    public void setIdEntidade(String aIdEntidade);

    public String obterColaboradoresMarcados(List<GrupoColaboradorVO> lista);

    public Boolean consultarAberturaPorCodigoUsuario(Integer codigoColaborador, Integer empresa, Date dia) throws Exception;

    public void verificarAberturaMetaDia(Integer codigo, Date dia, Integer empresa) throws Exception;

    public List consultarPorDia(Date prmIni, Date prmFim, Boolean metaEmAberto, boolean controlarAcesso, int nivelMontarDados, Integer empresa, ConfPaginacao confPaginacao) throws Exception;

    public List consultarPorColaboradorResponsavel(String valorConsulta, Boolean metaEmAberto, boolean controlarAcesso, int nivelMontarDados, Integer empresa, ConfPaginacao confPaginacao) throws Exception;

    public List consultarPorResponsavelCadastro(String valorConsulta, Boolean metaEmAberto, boolean controlarAcesso, int nivelMontarDados, Integer empresa, ConfPaginacao confPaginacao) throws Exception;

    public AberturaMetaVO validarUsuarioLogadoParaAberturaMeta(UsuarioVO usuarioLogado, AberturaMetaVO abertura, Integer empresa, Boolean gravou) throws Exception;

    public void gravar(AberturaMetaVO aberturaMetaVO, Integer empresa, PermissaoAcessoMenuVO permissoes, boolean estudio, boolean commit) throws Exception;

    public void gravarAgendamento(AberturaMetaVO aberturaMetaVO, Integer empresa, PermissaoAcessoMenuVO permissoes, boolean estudio, boolean commit) throws Exception;

    public List consultarFechamentoDiaAberturaMeta(String valorConsulta, String campoConsulta, Date dataConsultaFechamentoDia, Boolean metaEmAberto, Integer empresa, ConfPaginacao confPaginacao) throws Exception;

    public AberturaMetaVO inicializarDadosAberturaMetaParaComecarTrabalhar(UsuarioVO usuarioLogadoVO, AberturaMetaVO abertura, Integer empresa) throws Exception;

    public void consultarPerfilAcessoColaborador(AberturaMetaVO obj, EmpresaVO empresa) throws Exception;

    public void executarPreenchimentoListaFechamentoDia(AberturaMetaVO aberturaMetaVO) throws Exception;

    public void validarPermissaoResponsavelTrocaColaboradorResponsavel(UsuarioVO obj, Integer empresa) throws Exception;

    public void inicializarEmpresaAberturaMeta(AberturaMetaVO obj, EmpresaVO empresa);

    public void executarDefinicaoDataParaCalcularAberturaDia(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes, boolean estudio, QuarentenaVO quarentenaCRM) throws Exception;

    public void executarCalculoMetaLeads(AberturaMetaVO obj, PermissaoAcessoMenuVO permissoes) throws Exception;

    public void atualizarMetaRetencaoDia(AberturaMetaVO obj) throws Exception;

    public void atualizarMetaRetencaoPorColaborador(AberturaMetaVO obj, Integer empresa) throws Exception;

    public void atualizarMetaVendaDia(AberturaMetaVO obj, Integer empresa) throws Exception;

    public void atualizarMetaVendaPorColaborador(AberturaMetaVO obj, Integer empresa) throws Exception;

    public void inicializarDadosNomeParticipanteSelecionado(AberturaMetaVO aberturaMetaVO, List<GrupoColaboradorVO> lista, Boolean isVenda);

    public void executarSelecaoTodosFecharMetaDetalhados(FecharMetaVO obj) throws Exception;

    public void executarEmailColetivo(MalaDiretaVO malaDiretaVO, Date diaMeta, EmpresaVO empresa) throws Exception;

    public AberturaMetaVO persistirAlteracaoFechamentoDiaAberturaMeta(AberturaMetaVO obj) throws Exception;

    public void alterarSomenteCampoFechaMetaDiaFechamentoJustificativa(AberturaMetaVO obj) throws Exception;

    public void adicionarFecharMetaJustificativa(FecharMetaVO fecharMetaVO, List<FecharMetaVO> listaJustificativa);

    public boolean verificarPermitirVisualizarTodasCarteiras() throws Exception;

    public boolean verificarPermitirVisualizarTodasCarteiras(UsuarioVO usuario) throws Exception;

    public Long consultarVerificacaoAberturaMetaDia(Integer codigo, Date dia, Integer empresa) throws Exception;

    public List<AberturaMetaVO> consultarPorResponsavelPeriodo(Boolean aberta, List<Integer> codigos, Date inicio, Date fim, Integer empresa, Integer nivelMontarDados) throws Exception;

    public List<AberturaMetaVO> consultarMetasAbertaPorDia(Date data, Integer empresa, Integer nivelMontarDados) throws Exception;
    
    public void atualizarMetaEstudioDia(AberturaMetaVO obj) throws Exception;

    public void atualizarMetaEstudioPorColaborador(AberturaMetaVO obj, Integer empresa) throws Exception;

    public List<AberturaMetaVO> consultarMetasFechadasPorDia(Date data, Integer empresa, Integer nivelMontarDados) throws Exception;

    AberturaMetaVO consultar(Integer codigoColaboradorResponsavel, Integer codigoEmpresa, Date dataAbertura, Integer nivelMontarDados) throws Exception;

    JSONObject consultarUltimaAberturaMeta() throws Exception;

    AberturaMetaVO consultarUltimaAberturaMeta(Integer codigoColaboradorResponsavel, Integer codigoEmpresa,Integer nivelMontarDados) throws Exception;

    AberturaMetaVO consultarAberturaPorFecharMeta(Integer codigoFecharMeta, int nivelMontarDados) throws Exception;

    Date obterDiaAberturaMeta(Integer codigoFecharMeta) throws Exception;

    List<NotificacaoLigacaoAgendadaJSON> consultarAgendamentosLigacao(UsuarioVO usuarioVO, EmpresaVO empresaVO, Date data) throws Exception;

    List<AberturaMetaVO> consultarMetasAbertaPorDiaPorFase(Date data, Integer empresa, FasesCRMEnum fasesCRMEnum, Integer nivelMontarDados) throws Exception;

    Boolean abriuMetaHoje() throws Exception;

    String obterSqlCalculoMetaQtdeFaltosos(Integer codColaborador, Date dia, Integer nrFaltaConf, Integer nrDuracaoPlano, Integer empresa, String tipoVinculos, String codigoClientesObjecoes, Date dataInicioFaltas, Integer codCliente, Boolean contarFaltaQuarentena) throws Exception;
}
