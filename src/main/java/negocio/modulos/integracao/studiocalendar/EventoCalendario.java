package negocio.modulos.integracao.studiocalendar;

import java.util.Date;

public class EventoCalendario {

    private int id;                     //Armazena o id do evento;
    private String titulo;              // Armazena o titulo que sera exibido no calendario da agenda do Google;
    private String nomeAcademia;        // E a localizacao do evento;
    private String nomeAluno;           // Sera integrado a descricao do evento junto ao tipo aula;
    private String nomeAmbiente;        //Nome do Ambiente em que será o evento.
    private String statusAula;          /*Status of the event. Optional. Possible values are:
                                            "confirmed" - The event is confirmed. This is the default status.
								            "tentative" - The event is tentatively confirmed.
								            "cancelled" - The event is cancelled. */
    private String tipoAula;            // Sera integrado a descricao do evento junto ao nome do launo;
    private Date horarioInicial;        // Horario inicial da sessao;
    private Date horarioFinal;          // Horario final da sessao;
    private String idEvento;            // Armazena o id oficial do evento de retorno do Google Calendar uma sequencia de caracteres;
    private String tokenUsuario;        // O token de autenticacao do usuaio para comunicacao com o Google;
    private String observacao;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitulo() {
        return titulo;
    }

    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }

    public String getNomeAcademia() {
        return nomeAcademia;
    }

    public void setNomeAcademia(String nomeAcademia) {
        this.nomeAcademia = nomeAcademia;
    }

    public String getNomeAluno() {
        return nomeAluno;
    }

    public void setNomeAluno(String nomeAluno) {
        this.nomeAluno = nomeAluno;
    }

    public String getStatusAula() {
        return statusAula;
    }

    public void setStatusAula(String statusAula) {
        this.statusAula = statusAula;
    }

    public String getTipoAula() {
        return tipoAula;
    }

    public void setTipoAula(String tipoAula) {
        this.tipoAula = tipoAula;
    }

    public Date getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(Date horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public Date getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(Date horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public String getIdEvento() {
        return idEvento;
    }

    public void setIdEvento(String idEvento) {
        this.idEvento = idEvento;
    }

    public String getTokenUsuario() {
        return tokenUsuario;
    }

    public void setTokenUsuario(String tokenUsuario) {
        this.tokenUsuario = tokenUsuario.replace("\\", "");
    }

    public String getDescricao() {
        return "Sessão: " + getTipoAula() + " para o(a) Aluno(a) " + getNomeAluno() + "\n" +
                "Ambiente: " + getNomeAmbiente() + "\n" +
                getObservacao();
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getNomeAmbiente() {
        return nomeAmbiente;
    }

    public void setNomeAmbiente(String nomeAmbiente) {
        this.nomeAmbiente = nomeAmbiente;
    }
}
