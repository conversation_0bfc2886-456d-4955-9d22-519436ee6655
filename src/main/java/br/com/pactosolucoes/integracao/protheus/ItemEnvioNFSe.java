/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

import negocio.comuns.basico.ClienteVO;

/**
 *
 * <AUTHOR>
 */
public class ItemEnvioNFSe {
    private ClienteVO clienteVO; 
    private String produto; 
    private Integer contrato; 
    private Integer rps; 

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public String getProduto() {
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public Integer getContrato() {
        return contrato;
    }

    public void setContrato(Integer contrato) {
        this.contrato = contrato;
    }

    public Integer getRps() {
        return rps;
    }

    public void setRps(Integer rps) {
        this.rps = rps;
    }
    
    
}
