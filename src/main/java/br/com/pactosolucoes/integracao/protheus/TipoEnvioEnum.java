/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.protheus;

/**
 *
 * <AUTHOR>
 */
public enum TipoEnvioEnum {
    CLIENTE("CL"),
    TITULO_PROVISORIO("PR"),
    TITULO_OPERADORA("OP"),
    NOTA_FISCAL("NF"),
    PROCESSAMENTO("");
    
    public static TipoEnvioEnum getFromOrdinal(int i){
        for(TipoEnvioEnum t : values()){
            if(t.ordinal() == i){
                return t;
            }
        }
        return null;
    }

    public static TipoEnvioEnum getFromSigla(String s){
        for(TipoEnvioEnum t : values()){
            if(t.getSigla().equals(s)){
                return t;
            }
        }
        return null;
    }
    
    private String sigla;

    private TipoEnvioEnum(String sigla) {
        this.sigla = sigla;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }
    
    
    
}
