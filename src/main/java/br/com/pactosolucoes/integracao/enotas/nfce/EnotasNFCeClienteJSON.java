package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.integracao.enotas.nfse.EnotasNFSeClienteEnderecoJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class EnotasNFCeClienteJSON extends SuperJSON {

    @JsonProperty("tipoPessoa")
    private String tipoPessoa;
    @JsonProperty("nome")
    private String nome;
    @JsonProperty("cpfCnpj")
    private String cpfCnpj;


    public EnotasNFCeClienteJSON(NotaTO notaTO) {
        this.tipoPessoa = notaTO.getCliTipo();
        this.nome = notaTO.getCliRazaoSocial();
        this.cpfCnpj = Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true);
    }

}
