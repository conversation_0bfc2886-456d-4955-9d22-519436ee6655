package br.com.pactosolucoes.integracao.enotas.to;

import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

public class InfoCidadeRegimeTributacaoTO extends SuperTO {

    private String codigo;
    private String nome;

    public InfoCidadeRegimeTributacaoTO(JSONObject jsonObject) {
        try {
            this.codigo = jsonObject.getString("codigo");
        } catch (Exception ignored){}

        try {
            this.nome = jsonObject.getString("nome");
        } catch (Exception ignored){}
    }

    public String getCodigo() {
        if (codigo == null) {
            codigo =  "";
        }
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        if (nome == null) {
            nome =  "";
        }
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
