package br.com.pactosolucoes.integracao.enotas.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaTO;
import negocio.comuns.utilitarias.Uteis;

public class EnotasNFCePagamentoJSON extends SuperJSON {

    @JsonProperty("presencaConsumidor")
    private String presencaConsumidor;
    @JsonProperty("nome")
    private String nome;
    @JsonProperty("cpfCnpj")
    private String cpfCnpj;


    public EnotasNFCePagamentoJSON(NotaTO notaTO) {
        this.presencaConsumidor = "OperacaoPresencial";
        this.nome = notaTO.getCliRazaoSocial();
        this.cpfCnpj = Uteis.tirarCaracteres(notaTO.getCliCPFCNPJ(), true);
    }

}
