package br.com.pactosolucoes.integracao.notasDelphi.nfce;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import org.json.JSONException;
import org.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import negocio.comuns.notaFiscal.NotaProcessarTO;
import negocio.comuns.notaFiscal.NotaProdutoTO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

public class ProdutoNFCeDelphiJSON extends SuperJSON {

    @JsonProperty("descricaoUnidade")
    private String descricaoUnidade;
    @JsonProperty("codigoProduto")
    private Integer codigoProduto;
    @JsonProperty("quantidade")
    private Integer quantidade;
    @JsonProperty("NCM")
    private String ncm;
    @JsonProperty("CFOP")
    private String cfop;
    @JsonProperty("descricao")
    private String descricao;
    @JsonProperty("valorUnitario")
    private Double valorUnitario;
    @JsonProperty("aliquotaPIS")
    private Double aliquotaPIS;
    @JsonProperty("baseCalculoPIS")
    private Double baseCalculoPIS;
    @JsonProperty("valorPIS")
    private Double valorPIS;
    @JsonProperty("aliquotaCOFINS")
    private Double aliquotaCOFINS;
    @JsonProperty("baseCalculoCofins")
    private Double baseCalculoCofins;
    @JsonProperty("valorCofins")
    private Double valorCofins;

    @JsonProperty("codigoListaServicoISSQN")
    private String codigoListaServicoISSQN;
    @JsonProperty("codigoMunicipioISSQN")
    private String codigoMunicipioISSQN;

    @JsonProperty("baseCalculoICMS")
    private Double baseCalculoICMS;
    @JsonProperty("aliquotaICMS")
    private Double aliquotaICMS;
    @JsonProperty("valorICMS")
    private Double valorICMS;

    @JsonProperty("baseCalculoISSQN")
    private Double baseCalculoISSQN;
    @JsonProperty("aliquotaISSQN")
    private Double aliquotaISSQN;
    @JsonProperty("valorISSQN")
    private Double valorISSQN;
    @JsonIgnore
    private NotaProdutoTO notaProdutoTO;


    public ProdutoNFCeDelphiJSON(NotaProdutoTO notaProdutoTO) {
        //UTILIZAR COMO VALOR TOTAL DO PRODUTO
        Double valorBase = Uteis.arredondarForcando2CasasDecimais(notaProdutoTO.getValorUnitario() * notaProdutoTO.getQuantidade());

        this.notaProdutoTO = notaProdutoTO;

        this.descricaoUnidade = "UN";
        this.codigoProduto = notaProdutoTO.getProdutoVO().getCodigo();
        this.quantidade = notaProdutoTO.getQuantidade();
        this.ncm = notaProdutoTO.getNcm();
        this.cfop = notaProdutoTO.getCfop();
        this.descricao = StringUtilities.doRemoverAcentos(notaProdutoTO.getDescricao());
        this.valorUnitario = valorBase;

        //aliquota PIS
        if (!UteisValidacao.emptyNumber(notaProdutoTO.getProdutoVO().getAliquotaPIS())) {

            this.aliquotaPIS = notaProdutoTO.getProdutoVO().getAliquotaPIS();
            this.baseCalculoPIS = valorBase;
            this.valorPIS = (valorBase * (notaProdutoTO.getProdutoVO().getAliquotaPIS() / 100.0));

        } else {
            this.aliquotaPIS = 0.0;
            this.baseCalculoPIS = 0.0;
            this.valorPIS = 0.0;
        }

        //aliquota COFINS
        if (!UteisValidacao.emptyNumber(notaProdutoTO.getProdutoVO().getAliquotaCOFINS())) {

            this.aliquotaCOFINS = notaProdutoTO.getProdutoVO().getAliquotaCOFINS();
            this.baseCalculoCofins = valorBase;
            this.valorCofins = (valorBase * (notaProdutoTO.getProdutoVO().getAliquotaCOFINS() / 100.0));

        } else {
            this.aliquotaCOFINS = 0.0;
            this.baseCalculoCofins = 0.0;
            this.valorCofins = 0.0;
        }

        //se for PRODUTO ESTOQUE calcula ICMS
        if (notaProdutoTO.getProdutoVO().getTipoProduto().equals(TipoProduto.PRODUTO_ESTOQUE.getCodigo())) {

            this.codigoListaServicoISSQN = "";
            this.codigoMunicipioISSQN = "";

            this.baseCalculoICMS = valorBase;
            this.aliquotaICMS = notaProdutoTO.getProdutoVO().getAliquotaICMS();
            this.valorICMS = (valorBase * (notaProdutoTO.getProdutoVO().getAliquotaICMS() / 100.0));

        } else {//se nao calcula ISSQN

            this.codigoListaServicoISSQN = notaProdutoTO.getProdutoVO().getCodigoListaServico();
            this.codigoMunicipioISSQN = notaProdutoTO.getProdutoVO().getCodigoTributacaoMunicipio();

            this.baseCalculoISSQN = valorBase;
            this.aliquotaISSQN = notaProdutoTO.getProdutoVO().getAliquotaISSQN();
            this.valorISSQN = (valorBase * (notaProdutoTO.getProdutoVO().getAliquotaISSQN() / 100.0));
        }
    }

    public String toJSONDelphi() throws JSONException {
        JSONObject jsonObject = new JSONObject(this.toJSON());

        if (notaProdutoTO.getProdutoVO().getTipoProduto().equals(TipoProduto.PRODUTO_ESTOQUE.getCodigo())) {

            jsonObject.remove("baseCalculoISSQN");
            jsonObject.remove("aliquotaISSQN");
            jsonObject.remove("valorISSQN");

        } else {

            jsonObject.remove("baseCalculoICMS");
            jsonObject.remove("aliquotaICMS");
            jsonObject.remove("valorICMS");

        }

        return jsonObject.toString();

    }
}
