package br.com.pactosolucoes.integracao.CadSis;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntegracaoPagamentoParcelaDTO extends SuperJSON {

    private Integer codigo;
    private Double valor;
    private String descricao;
    private String dataVencimento;

    public IntegracaoPagamentoParcelaDTO(MovParcelaVO movParcelaVO) {
        this.codigo = movParcelaVO.getCodigo();
        this.valor = movParcelaVO.getValorParcela();
        this.descricao = movParcelaVO.getDescricao();
        this.dataVencimento = Calendario.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy");
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }
}
