package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreditoPactoDTO extends SuperTO {

    private boolean utilizados = false;
    private Integer quantidade;
    private String descricao;
    private Integer remessa;
    private Integer transacao;
    private Integer pix;
    private Integer boletoOnline;
    private Integer boletoRemessa;

    public CreditoPactoDTO() {
    }

    public boolean isUtilizados() {
        return utilizados;
    }

    public void setUtilizados(boolean utilizados) {
        this.utilizados = utilizados;
    }

    public Integer getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Integer quantidade) {
        this.quantidade = quantidade;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getRemessa() {
        return remessa;
    }

    public void setRemessa(Integer remessa) {
        this.remessa = remessa;
    }

    public Integer getTransacao() {
        return transacao;
    }

    public void setTransacao(Integer transacao) {
        this.transacao = transacao;
    }

    public Integer getPix() {
        return pix;
    }

    public void setPix(Integer pix) {
        this.pix = pix;
    }

    public Integer getBoletoOnline() {
        return boletoOnline;
    }

    public void setBoletoOnline(Integer boletoOnline) {
        this.boletoOnline = boletoOnline;
    }

    public Integer getBoletoRemessa() {
        return boletoRemessa;
    }

    public void setBoletoRemessa(Integer boletoRemessa) {
        this.boletoRemessa = boletoRemessa;
    }
}
