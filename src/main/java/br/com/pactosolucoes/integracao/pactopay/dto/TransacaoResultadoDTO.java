package br.com.pactosolucoes.integracao.pactopay.dto;

public class TransacaoResultadoDTO {

    private String id;
    private Integer idReferencia;
    private Integer situacao;
    private String codigoRetorno;
    private String motivoRetorno;
    private String tid;
    private String idTransacaoAdquirente;
    private String autorizacao;
    private String nsu;
    private String novoCartaoAragorn;
    private String situacaoDescricao;
    private String dataPagamento;
    private String dataCredito;
    private String idCartaoAdquirente;
    private String idClienteAdquirente;


    public TransacaoResultadoDTO() {

    }

    public TransacaoResultadoDTO(TransacaoDTO dto) {
        this.id = dto.getId();
        this.idReferencia = dto.getIdReferencia();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getIdReferencia() {
        return idReferencia;
    }

    public void setIdReferencia(Integer idReferencia) {
        this.idReferencia = idReferencia;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public String getCodigoRetorno() {
        return codigoRetorno;
    }

    public void setCodigoRetorno(String codigoRetorno) {
        this.codigoRetorno = codigoRetorno;
    }

    public String getMotivoRetorno() {
        return motivoRetorno;
    }

    public void setMotivoRetorno(String motivoRetorno) {
        this.motivoRetorno = motivoRetorno;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public String getNovoCartaoAragorn() {
        return novoCartaoAragorn;
    }

    public void setNovoCartaoAragorn(String novoCartaoAragorn) {
        this.novoCartaoAragorn = novoCartaoAragorn;
    }

    public String getSituacaoDescricao() {
        return situacaoDescricao;
    }

    public void setSituacaoDescricao(String situacaoDescricao) {
        this.situacaoDescricao = situacaoDescricao;
    }

    public String getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getDataCredito() {
        return dataCredito;
    }

    public void setDataCredito(String dataCredito) {
        this.dataCredito = dataCredito;
    }

    public String getIdTransacaoAdquirente() {
        return idTransacaoAdquirente;
    }

    public void setIdTransacaoAdquirente(String idTransacaoAdquirente) {
        this.idTransacaoAdquirente = idTransacaoAdquirente;
    }

    public String getIdCartaoAdquirente() {
        return idCartaoAdquirente;
    }

    public void setIdCartaoAdquirente(String idCartaoAdquirente) {
        this.idCartaoAdquirente = idCartaoAdquirente;
    }

    public String getIdClienteAdquirente() {
        return idClienteAdquirente;
    }

    public void setIdClienteAdquirente(String idClienteAdquirente) {
        this.idClienteAdquirente = idClienteAdquirente;
    }
}
