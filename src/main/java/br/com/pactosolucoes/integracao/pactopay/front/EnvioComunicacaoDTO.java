package br.com.pactosolucoes.integracao.pactopay.front;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.MovParcelaVO;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EnvioComunicacaoDTO extends SuperTO {

    private PessoaVO pessoaVO;
    private ClienteVO clienteVO;
    private List<MovParcelaVO> parcelas;
    private boolean podeEnviarSMS = false;
    private boolean podeEnviarEmail = false;
    private Integer totalEmailEnviado;
    private Integer totalSMSEnviado;
    private boolean podeEnviarWhatsApp = false;
    private Integer totalWhatsAppEnviado;
    private boolean podeEnviarApp = false;
    private Integer totalAppEnviado;
    private boolean podeEnviarGymBotPro = false;
    private Integer totalGymBotProEnviado;

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    public ClienteVO getClienteVO() {
        return clienteVO;
    }

    public void setClienteVO(ClienteVO clienteVO) {
        this.clienteVO = clienteVO;
    }

    public List<MovParcelaVO> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<>();
        }
        return parcelas;
    }

    public void setParcelas(List<MovParcelaVO> parcelas) {
        this.parcelas = parcelas;
    }

    public boolean isPodeEnviarSMS() {
        return podeEnviarSMS;
    }

    public void setPodeEnviarSMS(boolean podeEnviarSMS) {
        this.podeEnviarSMS = podeEnviarSMS;
    }

    public boolean isPodeEnviarEmail() {
        return podeEnviarEmail;
    }

    public void setPodeEnviarEmail(boolean podeEnviarEmail) {
        this.podeEnviarEmail = podeEnviarEmail;
    }

    public Integer getTotalEmailEnviado() {
        if (totalEmailEnviado == null) {
            totalEmailEnviado = 0;
        }
        return totalEmailEnviado;
    }

    public void setTotalEmailEnviado(Integer totalEmailEnviado) {
        this.totalEmailEnviado = totalEmailEnviado;
    }

    public Integer getTotalSMSEnviado() {
        if (totalSMSEnviado == null) {
            totalSMSEnviado = 0;
        }
        return totalSMSEnviado;
    }

    public void setTotalSMSEnviado(Integer totalSMSEnviado) {
        this.totalSMSEnviado = totalSMSEnviado;
    }

    public boolean isPodeEnviarWhatsApp() {
        return podeEnviarWhatsApp;
    }

    public void setPodeEnviarWhatsApp(boolean podeEnviarWhatsApp) {
        this.podeEnviarWhatsApp = podeEnviarWhatsApp;
    }

    public Integer getTotalWhatsAppEnviado() {
        if (totalWhatsAppEnviado == null) {
            totalWhatsAppEnviado = 0;
        }
        return totalWhatsAppEnviado;
    }

    public void setTotalWhatsAppEnviado(Integer totalWhatsAppEnviado) {
        this.totalWhatsAppEnviado = totalWhatsAppEnviado;
    }

    public boolean isPodeEnviarApp() {
        return podeEnviarApp;
    }

    public void setPodeEnviarApp(boolean podeEnviarApp) {
        this.podeEnviarApp = podeEnviarApp;
    }

    public Integer getTotalAppEnviado() {
        if (totalAppEnviado == null) {
            totalAppEnviado = 0;
        }
        return totalAppEnviado;
    }

    public void setTotalAppEnviado(Integer totalAppEnviado) {
        this.totalAppEnviado = totalAppEnviado;
    }

    public boolean isPodeEnviarGymBotPro() {
        return podeEnviarGymBotPro;
    }

    public void setPodeEnviarGymBotPro(boolean podeEnviarGymBotPro) {
        this.podeEnviarGymBotPro = podeEnviarGymBotPro;
    }

    public Integer getTotalGymBotProEnviado() {
        if (totalGymBotProEnviado == null) {
            totalGymBotProEnviado = 0;
        }
        return totalGymBotProEnviado;
    }

    public void setTotalGymBotProEnviado(Integer totalGymBotProEnviado) {
        this.totalGymBotProEnviado = totalGymBotProEnviado;
    }
}
