package br.com.pactosolucoes.integracao.pactopay;

import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;

public enum SituacaoTransacaoPactoPayEnum {

    GERADA(0, "GERADA"),
    ENVIADA(1, "ENVIADA"),
    AGUARDANDO_CAPTURA(2, "AGUARDANDO_CAPTURA"),
    CONCLUIDA_COM_SUCESSO(3, "CONCLUIDA_COM_SUCESSO"),
    NAO_APROVADA(4, "NAO_APROVADA"),
    CANCELAMENTO_SOLICITADO(5, "CANCELAMENTO_SOLICITADO"),
    CANCELADA(6, "CANCELADA"),
    ERRO(7, "ERRO"),
    PENDENTE(8, "PENDENTE");

    private Integer codigo;
    private String descricao;

    SituacaoTransacaoPactoPayEnum(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public static SituacaoTransacaoPactoPayEnum obterPorCodigo(Integer codigo) {
        for (SituacaoTransacaoPactoPayEnum situacaoTransacaoEnum : SituacaoTransacaoPactoPayEnum.values()) {
            if (situacaoTransacaoEnum.getCodigo().equals(codigo)) {
                return situacaoTransacaoEnum;
            }
        }
        return null;
    }

    public static SituacaoTransacaoEnum obterSituacaoTransacaoPactoPay(SituacaoTransacaoPactoPayEnum situacaoTransacaoPactoPayEnum) {
        switch (situacaoTransacaoPactoPayEnum) {
            case GERADA:
                return SituacaoTransacaoEnum.APROVADA;
            case ENVIADA:
                return SituacaoTransacaoEnum.APROVADA;
            case AGUARDANDO_CAPTURA:
                return SituacaoTransacaoEnum.APROVADA;
            case CONCLUIDA_COM_SUCESSO:
                return SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO;
            case NAO_APROVADA:
                return SituacaoTransacaoEnum.NAO_APROVADA;
            case CANCELAMENTO_SOLICITADO:
                return SituacaoTransacaoEnum.CANCELADA;
            case CANCELADA:
                return SituacaoTransacaoEnum.CANCELADA;
            case ERRO:
                return SituacaoTransacaoEnum.COM_ERRO;
            case PENDENTE:
                return SituacaoTransacaoEnum.APROVADA;
        }
        return null;
    }
}
