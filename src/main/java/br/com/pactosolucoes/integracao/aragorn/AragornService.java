package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.legolas.LegolasService;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 22/01/2020
 */
public class AragornService {

    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_ACCEPT = "Accept";
    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String CHARSET_UTF8 = "UTF-8";
    private static final String CONTENT = "content";
    private String token = null;
    private boolean persistirCasoErro = false;

    public AragornService() {
    }

    public AragornService(boolean persistirCasoErro) {
        this.persistirCasoErro = persistirCasoErro;
    }

    public String obterUrl(String aplicacao) {
        JSONObject urls = LegolasService.obterUrls();
        return urls.optString(aplicacao);
    }

    public String obterToken() {
        //Obter token de autenticação no autenticacaoMS
        if (token == null) {
            try {
                String urlAutenticacaoMS = PropsService.getPropertyValue(PropsService.URL_AUTENTICACAO_MS); //Em produção, preenchido sempre
                if (UteisValidacao.emptyString(urlAutenticacaoMS)) {
                    //vai entrar aqui quando for local ou swarm
                    urlAutenticacaoMS = obterUrl("autenticacao");
                }
                token = LegolasService.obterToken(urlAutenticacaoMS, persistirCasoErro);
            } catch (Exception e) {
                Uteis.logar(e, AragornService.class);
            }
        }
        return token;
    }

    public NazgDTO enviarNazgMock(NazgDTO nazgDTO) throws Exception {
        nazgDTO.setToken("tokenTeste");
        return nazgDTO;
    }

    public NazgDTO obterNazgMock(String tokenAragorn) throws Exception {
        NazgDTO nazgDTO = new NazgDTO();
        nazgDTO.setName("TESTE DA PACTO SOLUCOES");
        nazgDTO.setCard("****************"); //real
        nazgDTO.setFlag(2); //mastercard
        nazgDTO.setMonth(02);
        nazgDTO.setYear(2030);
        nazgDTO.setCpf("00681669152");
        nazgDTO.setToken(tokenAragorn);
        return nazgDTO;
    }


    public NazgDTO enviarNazg(NazgDTO nazgDTO) throws Exception {
        try {
            if(Uteis.isAmbienteDesenvolvimentoTeste() && Uteis.isMockAragornCards()){
                return enviarNazgMock(nazgDTO);
            }
            nazgDTO.validarDados();

            //Obter token de autenticação no autenticacaoMS para realizar requisição no aragorn
            String tokenAutenticacaoRequisicao = obterToken();
            if (UteisValidacao.emptyString(tokenAutenticacaoRequisicao)) {
                throw new Exception();
            }

            //Obter url aragorn
            String urlAragornMS = PropsService.getPropertyValue(PropsService.URL_ARAGORN_MS); //Em produção, preenchido sempre
            if (UteisValidacao.emptyString(urlAragornMS)) {
                //vai entrar aqui quando for local ou swarm
                urlAragornMS = obterUrl("aragorn");
            }

            //Realizar a requisição no Aragorn
            StringEntity entity = new StringEntity(new JSONObject(nazgDTO).toString(), CHARSET_UTF8);
            HttpPost httpPost = new HttpPost(urlAragornMS);
            httpPost.setEntity(entity);
            httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
            httpPost.setHeader(HEADER_AUTHORIZATION, tokenAutenticacaoRequisicao); //getToken vai no autenticacao-ms
            HttpClient client = ExecuteRequestHttpService.createConnector();
            HttpResponse response = client.execute(httpPost);

            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject json = new JSONObject(responseBody);
            if (json.has(CONTENT) && !UteisValidacao.emptyString(json.optString(CONTENT))) {
                nazgDTO.setToken(json.optString(CONTENT));
            } else {
                throw new Exception(responseBody);
            }
            return nazgDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao gerar token do cartão");
        }
    }

    public NazgDTO obterNazg(String tokenAragorn) throws Exception {
        try {
            if(Uteis.isAmbienteDesenvolvimentoTeste() && Uteis.isMockAragornCards()){
                return obterNazgMock(tokenAragorn);
            }

            List<String> tokens = new ArrayList<>();
            tokens.add(tokenAragorn);

            Map<String, NazgDTO> mapaAragorn = obterMapaNazg(tokens);
            NazgDTO nazgDTO = mapaAragorn.get(tokenAragorn);
            if (nazgDTO == null) {
                throw new Exception("Erro ao obter os dados do cartão");
            }
            return nazgDTO;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao obter os dados do cartão");
        }
    }

    public Map<String, NazgDTO> obterMapaNazg(List<String> listaTokens) throws Exception {
        try {
            if (UteisValidacao.emptyList(listaTokens)) {
                throw new Exception("Nenhum token informado");
            }

            List<List<String>> listasRequi = new ArrayList<>();
            List<String> listaTemp = new ArrayList<>();

            int adicionado = 0;
            for (String token : listaTokens) {
                ++adicionado;
                listaTemp.add(token);
                if (listaTemp.size() == 100 || adicionado == listaTokens.size()) {
                    listasRequi.add(listaTemp);
                    listaTemp = new ArrayList<>();
                }
            }

            Map<String, NazgDTO> mapa = new HashMap<>();
            for (List<String> lista : listasRequi) {

                JSONArray array = new JSONArray();
                for (String token : lista) {
                    array.put(token);
                }

                JSONObject json = new JSONObject();
                json.put("tokens", array);

                //Obter token de autenticação no autenticacaoMS para realizar requisição no aragorn
                String tokenAutenticacaoRequisicao = obterToken();
                if (UteisValidacao.emptyString(tokenAutenticacaoRequisicao)) {
                    throw new Exception("Não será possível prosseguir...Token de autenticação para usar no Aragorn é inválido!");
                }

                //Realizar a requisição no Aragorn
                Uteis.logar("Vou buscar os tokens dos cartões no Aragorn...");
                StringEntity entity = new StringEntity(json.toString(), CHARSET_UTF8);

                //Obter url aragorn
                String urlAragornMS = PropsService.getPropertyValue(PropsService.URL_ARAGORN_MS); //Em produção, preenchido sempre
                if (UteisValidacao.emptyString(urlAragornMS)) {
                    //vai entrar aqui quando for local ou swarm
                    urlAragornMS = obterUrl("aragorn");
                }

                HttpPost httpPost = new HttpPost(urlAragornMS + "/list");
                httpPost.setEntity(entity);
                httpPost.setHeader(HEADER_CONTENT_TYPE, APPLICATION_JSON);
                httpPost.setHeader(HEADER_AUTHORIZATION, tokenAutenticacaoRequisicao);
                HttpClient client = ExecuteRequestHttpService.createConnector();
                HttpResponse response = client.execute(httpPost);
                String responseBody = EntityUtils.toString(response.getEntity());

                JSONObject jsonResponse = new JSONObject(responseBody);
                if (jsonResponse.has(CONTENT) && !UteisValidacao.emptyString(jsonResponse.optString(CONTENT))) {

                    JSONArray dadoArray = new JSONArray(jsonResponse.optString(CONTENT));
                    for (int e = 0; e < dadoArray.length(); e++) {
                        JSONObject obj = dadoArray.getJSONObject(e);

                        String token = obj.getString("token");
                        JSONObject nazg = obj.getJSONObject("nazg");

                        NazgDTO nazgDTO = new NazgDTO(nazg);
                        nazgDTO.setToken(token);
                        mapa.put(token, nazgDTO);
                    }
                } else {
                    throw new Exception(responseBody);
                }
            }

            if (mapa.size() == 0) {
                throw new Exception("Nenhum cartão encontrado");
            }

            return mapa;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(ex, this.getClass());
            Uteis.logar("AragornService | obterMapaNazg | ERRO: " + ex.getMessage());
            throw new Exception("Erro ao obter os dados do cartão " + ex);
        }
    }

    public void povoarCartaoCreditoTO(CartaoCreditoTO cartaoCreditoTO) throws Exception {

        //vindi pode enviar sem cartão.. e sem token ou código externo
        if (cartaoCreditoTO.getTipoTransacaoEnum().equals(TipoTransacaoEnum.VINDI) &&
                UteisValidacao.emptyString(cartaoCreditoTO.getTokenAragorn()) &&
                UteisValidacao.emptyString(cartaoCreditoTO.getNumero())){
            return;
        }

        //Cielo Token pode enviar sem cartão
        if (cartaoCreditoTO.getTipoTransacaoEnum().equals(TipoTransacaoEnum.CIELO_ONLINE) &&
                UteisValidacao.emptyString(cartaoCreditoTO.getTokenAragorn()) &&
                !UteisValidacao.emptyString(cartaoCreditoTO.getTokenCielo())){
            return;
        }

        //PagoLivre Token pode enviar sem cartão
        if ((cartaoCreditoTO.getTipoTransacaoEnum().equals(TipoTransacaoEnum.PAGOLIVRE) ||
                cartaoCreditoTO.getTipoTransacaoEnum().equals(TipoTransacaoEnum.FACILITEPAY)) &&
                UteisValidacao.emptyString(cartaoCreditoTO.getTokenAragorn()) &&
                !UteisValidacao.emptyString(cartaoCreditoTO.getTokenPagoLivre())){
            return;
        }

        NazgDTO nazgDTO;
        if (UteisValidacao.emptyString(cartaoCreditoTO.getTokenAragorn())) {
            nazgDTO = enviarNazg(new NazgDTO(cartaoCreditoTO));
        } else {
            nazgDTO = obterNazg(cartaoCreditoTO.getTokenAragorn());
        }

        cartaoCreditoTO.setTokenAragorn(nazgDTO.getToken());
        cartaoCreditoTO.setNumero(nazgDTO.getCard());
        cartaoCreditoTO.setNomeTitular(nazgDTO.getName());
        cartaoCreditoTO.setCpfCnpjPortador(nazgDTO.getCpf());
        cartaoCreditoTO.setBand(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
        cartaoCreditoTO.setMesValidade(nazgDTO.getMonth());
        cartaoCreditoTO.setAnoValidade(nazgDTO.getYear());
    }

    public void povoarAutorizacaoCobrancaVO(AutorizacaoCobrancaVO autoVO) throws Exception {
        povoarAutorizacaoCobrancaVO(autoVO, false,false);
    }

    public void povoarAutorizacaoCobrancaVO(AutorizacaoCobrancaVO autoVO, boolean preencerNazgDTO, boolean convertValidadeCartao) throws Exception {

        if (UteisValidacao.emptyString(autoVO.getTokenAragorn())) {
            throw new Exception("Não foi possivel obter os dados do cartão.");
        }

        NazgDTO nazgDTO = obterNazg(autoVO.getTokenAragorn());
        autoVO.setNumeroCartao(nazgDTO.getCard());
        autoVO.setNomeTitularCartao(nazgDTO.getName());
        autoVO.setCpfTitular(nazgDTO.getCpf());
        autoVO.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(nazgDTO.getFlag()));
        if (convertValidadeCartao){
            //converte o formato da validade do cartão para mostrar na tela do caixa em aberto quando carrega o objeto
            autoVO.setValidadeCartao(Uteis.getValidadeMMYYYY(nazgDTO.getMonth(), nazgDTO.getYear(), true).replace("/20","/"));
        } else {
            autoVO.setValidadeCartao(Uteis.getValidadeMMYYYY(nazgDTO.getMonth(), nazgDTO.getYear(), true));
        }
        if (preencerNazgDTO) {
            autoVO.setNazgDTO(nazgDTO);
        }
    }

}
