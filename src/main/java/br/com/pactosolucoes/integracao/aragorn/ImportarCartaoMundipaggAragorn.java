package br.com.pactosolucoes.integracao.aragorn;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import br.com.pactosolucoes.integracao.aragorn.uteis.ImportarTabelaAuxiliarImportacaoCartao;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ItemAuxiliarCartaoTO;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 19/06/2020
 */
public class ImportarCartaoMundipaggAragorn {

    private static Integer empresa;
    private static Integer convenioCobranca;
    private static boolean usarMatriculaExterna = false;
    private static Integer modeloTabelaAuxiliar = null;
    private static Integer tipoImportacao = null;
    private static boolean arquivoVindi = false;
    private static Map<String, Integer> mapaLog = new HashMap<>();
    private static List<String> listaErros = new ArrayList<>();
    private static Integer sucesso = 0;
    private static Integer falha = 0;
    private static Integer total = 0;


    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            Uteis.logarDebug("ImportarCartaoMundipaggAragorn | INICIO...");

            if (args != null && args.length > 0) {
                Uteis.logarDebug("ImportarCartaoMundipaggAragorn | ARGS...");
                int argI = 0;
                for (String arg : args) {
                    Uteis.logarDebug("Args[" + ++argI + "] | " + arg);
                }
            }

            String chave = args[0];
            empresa = Integer.parseInt(args[1]);
            convenioCobranca = Integer.parseInt(args[2]);
            String pathOrigem = args[3];
            String inicioArq = args[4];

            tipoImportacao = args.length > 5 ? Integer.parseInt(args[5]) : 0;
            //TipoImportacao
            // 0 - Padrão
            // 1 - Usando Matricula Externa (Considera que o "idClienteMundi" é a matriculaexterna no zw)
            // 2 - Usando Tabela Externa (Usa a "importacaocartaoauxiliar" que contem informação de nome e ultimos 4 digitos do cartão)
            // 3 - Arquivo da Vindi
            // 4 - Usando Tabela Externa Modelo Mouve
            // 5 - Busca o cliente pelo nome CustomerName
            // 6 - Busca padrão se não encontrar busca pelo CPF do Terceiro
            // 7 - Importacao EVO - Busca pelo nome CustomerName e caso não encontre então busca pela tabela auxiliar "ProcessoPreencherTabelaAuxiliarImportacaoCartaoEVO"
            if (tipoImportacao.equals(1)) {
                usarMatriculaExterna = true;
            } else if (tipoImportacao.equals(2)) {
                modeloTabelaAuxiliar = 0;
            } else if (tipoImportacao.equals(3)) {
                arquivoVindi = true;
            } else if (tipoImportacao.equals(4)) {
                modeloTabelaAuxiliar = 1;
            } else if (tipoImportacao.equals(5) || tipoImportacao.equals(6)) {
                arquivoVindi = false;
                modeloTabelaAuxiliar = null;
            } else if (tipoImportacao.equals(7)) {
                arquivoVindi = false;
                modeloTabelaAuxiliar = 2;
            }

            boolean desativarImportadas = args.length > 6 && Boolean.parseBoolean(args[6]);

            Uteis.logarDebug("Obter conexao para chave: " + chave);
            con = new DAO().obterConexaoEspecifica(chave);

            if (desativarImportadas) {
                desativarAutorizacoesImportadas(empresa, convenioCobranca, con);
            }

            ImportarTabelaAuxiliarImportacaoCartao.criarTabela(con); //criar a tabela de auxiliar para evitar erro no sql caso utilize
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN obsimportmundipaag text;", con);
            processarArquivos(inicioArq, pathOrigem, con);
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("main | Erro: " + ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Uteis.logarDebug("ImportarCartaoMundipaggAragorn | FIM...");
        }
    }

    private static void desativarAutorizacoesImportadas(Integer empresa, Integer convenioCobranca, Connection con) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("update autorizacaocobrancacliente set ativa = false where length(coalesce(obsimportmundipaag,'')) > 0 " +
                "and conveniocobranca = " + convenioCobranca + " " +
                "and cliente in (select codigo from cliente where empresa = " + empresa + ");", con);
    }

    private static List<AutorizacaoCobrancaClienteVO> processarArquivo(String nomeArquivo, String conteudoArquivo, Connection con) throws Exception {
        try {
            List<AutorizacaoCobrancaClienteVO> lista = new ArrayList<>();
            if (conteudoArquivo.length() > 2) {
                BufferedReader br = new BufferedReader(new StringReader(conteudoArquivo));
                String linha;

                int n = 0;
                AutorizacaoCobrancaClienteVO novo = null;
                while ((linha = br.readLine()) != null) {
                    if (linha.startsWith("CardId") || (arquivoVindi && linha.startsWith("id,"))) {
                        System.out.println(linha);
                        continue;
                    }
                    n++;

                    if (n == 1) {
                        System.out.println(linha);
                    }

                    novo = new AutorizacaoCobrancaClienteVO();
                    String idCardMundiPagg;
                    String card;
                    String titular;
                    String titularDoc = null;
                    String brand;
                    String anoVenc;
                    String mesVenc;
                    String idClienteMundi = null;
                    String nomeCliente;
                    String cpf = null;
                    String[] dados = linha.split(",");

                    if (arquivoVindi && !tipoImportacao.equals(5)) {
                        // id,created_at,name,full_number,expiration_month,expiration_year,payment_company_code
                        // 0015b13e-c13b-48ee-badd-a3c8b48e5e0b,2021-11-24 17:15:17 -0300,VICTOR OLIVEIRA DA SILVA,4824********1107,12,2023,visa

                        idCardMundiPagg = dados[0];
                        titular = dados[2];
                        card = dados[3];
                        mesVenc = dados[4];
                        anoVenc = dados[5];
                        brand = dados[6];
                        nomeCliente = dados[2];

                    } else {

                        // CardId-0,CardNumber-1,CardHolderName-2,CardHolderDocument-3,CardBrand-4,ExpirationDate-5,ExpirationYear-6,ExpirationMonth-7,CustomerId-8,CustomerCode-9,CustomerName-10,CustomerEmail-11,CustomerHomePhone-12,CustomerMobilePhone-13,CustomerType-14,CustomerBirthDate-15,CustomerDocument-16,CustomerAddressStreet-17,CustomerAddressComplement-18,CustomerAddressNeighborhood-19,CustomerAddressCity-20,CustomerAddressState-21,CustomerAddressCountry-22,CustomerAddressZipCode-23,BillingAddressStreet-24,BillingAddressComplement-25,BillingAddressNeighborhood-26,BillingAddressCity-27,BillingAddressState-28,BillingAddressCountry-29,BillingAddressZipCode-30
                        // 53C3FDEF-FA85-4F72-AFE1-CF72973C6FA8,****************,HENRIQUE SOARES DOS SANTOS,,Elo,202401,2024,01,53C3FDEF-FA85-4F72-AFE1-CF72973C6FA8,53C3FDEF-FA85-4F72-AFE1-CF72973C6FA8,HENRIQUE SOARES DOS SANTOS,,,,Individual,,,,,,,,,,,,,,,,
                        // idCardMundiPagg,card,titular,

                        idCardMundiPagg = dados[0];
                        card = dados[1];
                        titular = dados[2];
                        titularDoc = dados[3];
                        brand = dados[4];

                        String venc = dados[5];
                        anoVenc = venc.substring(0, 4);
                        mesVenc = venc.substring(4, 6);
                        idClienteMundi = dados[8];
                        nomeCliente = dados[10];
                        try {
                            cpf = dados[16];
                        } catch (Exception e) {
                            Uteis.logarDebug("Informação: CPF nao informado para " + nomeCliente);
                        }
                    }

                    try {
                        Uteis.logarDebug(String.format("Linha %s => Id_card: %s Card Suffix: %s Titular: %s Cliente Old Gateway: %s CPF: %s",
                                n, idCardMundiPagg, card.substring(12), titular, nomeCliente, cpf));
                        novo.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                        novo.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                        novo.setNumeroCartao(card);
                        novo.setNomeTitularCartao(titular);
                        novo.setImportacaoCPF(cpf);
                        novo.setImportacaoCliente(nomeCliente);
                        novo.setImportacaoTitular(titular);

                        if (tipoImportacao.equals(5) || tipoImportacao.equals(7)) {
                            if (UteisValidacao.emptyString(novo.getNomeTitularCartao().replaceAll(" ", "").trim())) {
                                //usar o nome do cliente caso o titular esteja vazio
                                novo.setNomeTitularCartao(nomeCliente);
                                Uteis.logarDebug("Vou usar o nome do cliente como nome do titular | " + novo.getNomeTitularCartao());
                            } else {
                                Uteis.logarDebug("NomeTitularCartao | Titular " + novo.getNomeTitularCartao());
                            }
                        }

                        if (!UteisValidacao.emptyString(titularDoc)) {
                            novo.setCpfTitular(titularDoc);
                        }

                        for (OperadorasExternasAprovaFacilEnum opeEnum : OperadorasExternasAprovaFacilEnum.values()) {
                            if (opeEnum.getDescricao().equalsIgnoreCase(brand)) {
                                novo.setOperadoraCartao(opeEnum);
                                break;
                            }
                        }

                        novo.setValidadeCartao(mesVenc + "/" + anoVenc);
                        novo.setConvenio(new ConvenioCobrancaVO());
                        novo.getConvenio().setCodigo(convenioCobranca);
                        novo.setCliente(new ClienteVO());

                        if (tipoImportacao.equals(6)) {
                            //buscar pelo cpf caso não encontre buscar pelo cpf do responsável

                            Integer cliente;
                            try {
                                cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
                            } catch (Exception ex) {
                                if (UteisValidacao.emptyString(Uteis.formatarCpfCnpj(cpf, true))) {
                                    throw new Exception("Não é possível buscar pelo CPF Terceiro | CPF não informado");
                                }
                                Uteis.logarDebug("INFORMAÇÃO: Buscar pelo CPF Terceiro | CPF " + cpf);
                                cliente = obterCliente(idClienteMundi, cpf, nomeCliente, true, con);
                            }
                            novo.getCliente().setCodigo(cliente);
                        } else if (tipoImportacao.equals(5)) {
                            Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
                            novo.getCliente().setCodigo(cliente);
                        } else if (tipoImportacao.equals(7)) {
                            String msgErro = "";
                            try {
                                Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
                                novo.getCliente().setCodigo(cliente);
                            } catch (Exception ex) {
                                msgErro = ex.getMessage();
                            }

                            if (UteisValidacao.emptyNumber(novo.getCliente().getCodigo()) &&
                                    modeloTabelaAuxiliar != null && modeloTabelaAuxiliar.equals(2)) {
                                obterClienteTabelaAuxiliarEvo(novo, con);
                            } else if (UteisValidacao.emptyNumber(novo.getCliente().getCodigo())) {
                                throw new Exception(msgErro);
                            }
                        } else if (modeloTabelaAuxiliar != null) {
                            String ultimos4Numeros = novo.getNumeroCartao().substring(novo.getNumeroCartao().length() - 4);
                            if (UteisValidacao.emptyString(ultimos4Numeros)) {
                                Uteis.logarDebug("ultimos4Numeros nao informado | Titular " + titular);
                                continue;
                            }

                            if (modeloTabelaAuxiliar.equals(0)) {
                                obterClienteTabelaAuxiliar(novo, ultimos4Numeros, con);
                            } else if (modeloTabelaAuxiliar.equals(1)) {
                                obterClienteTabelaAuxiliarMouve(novo, ultimos4Numeros, con);
                            }
                        } else {
                            Integer cliente = obterCliente(idClienteMundi, cpf, nomeCliente, false, con);
                            novo.getCliente().setCodigo(cliente);
                        }

                        if (novo.getCliente() == null ||
                                UteisValidacao.emptyNumber(novo.getCliente().getCodigo())) {
                            throw new Exception("Cliente nao encontrado | Final card " + card.substring(12) + " | Titular " + titular);
                        }

                        novo.getCliente().getEmpresa().setCodigo(empresa);

                        JSONObject jsonObs = new JSONObject();
                        jsonObs.put("tipo", "CARTÃO IMPORTADO");
                        jsonObs.put("data", Calendario.getDataAplicandoFormatacao(Calendario.hoje(), "dd/MM/yyyy HH:mm:ss"));
                        jsonObs.put("arq", nomeArquivo);

                        if (!UteisValidacao.emptyString(novo.getObservacao())) {
                            jsonObs.put("obs", novo.getObservacao());
                        }
                        if (!UteisValidacao.emptyString(idClienteMundi)) {
                            jsonObs.put("idCliente", idClienteMundi);
                        }
                        if (!UteisValidacao.emptyString(idCardMundiPagg)) {
                            jsonObs.put("idCard", idCardMundiPagg);
                        }
                        novo.getCliente().setObservacao(jsonObs.toString());

                        lista.add(novo);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        Uteis.logarDebug("processarArquivo | Linha " + n + " | Erro: " + ex.getMessage());
                        listaErros.add("OBTER CLIENTE | Cliente | " + novo.getImportacaoCliente() + " | CPF | " + novo.getImportacaoCPF() + " | Titular | " + novo.getImportacaoTitular() + " | ERRO | " + ex.getMessage());
                    }
                }

                Uteis.logarDebug("#######################");
                Uteis.logarDebug("Itens - Arquivo: " + n);
                Uteis.logarDebug("Itens - Identificados: " + lista.size());
                Uteis.logarDebug("#######################");
            }
            return lista;
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarArquivo | Erro: " + ex.getMessage());
            throw ex;
        }
    }

    private static Integer obterCliente(String idClienteMundi, String cpf, String nomeCliente,
                                        boolean buscarCpfTerceiro, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo \n");
        sql.append("from cliente cl \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where cl.empresa = ").append(empresa).append(" \n");

        String textoLog = "";
        if (tipoImportacao.equals(6) && buscarCpfTerceiro) {
            textoLog = " cpfcnpjterceiro \"" + cpf + "\" ";
            sql.append("and (regexp_replace(p.cpfcnpjterceiro, '\\D', '', 'g') = '").append(cpf).append("' or \n");
            sql.append(" p.cpfcnpjterceiro = '").append(Uteis.formatarCpfCnpj(cpf, false)).append("' or \n");
            sql.append(" p.cpfcnpjterceiro = '").append(Uteis.formatarCpfCnpj(cpf, true)).append("') \n");
        } else if (tipoImportacao.equals(5) || tipoImportacao.equals(7)) {
            textoLog = " nome \"" + nomeCliente + "\" ";

            sql.append("and ( \n");

            sql.append("p.nome ilike '").append(nomeCliente.toUpperCase()).append("' or \n");

            sql.append("(UPPER(translate(regexp_replace(p.nome, '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')) ilike ");
            sql.append("UPPER(translate(regexp_replace('").append(nomeCliente.toUpperCase()).append("', '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')) \n");
            sql.append(") or \n");

            sql.append("(replace(UPPER(translate(regexp_replace(p.nome, '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') ilike ");
            sql.append("replace(UPPER(translate(regexp_replace('").append(nomeCliente.toUpperCase()).append("', '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') \n");
            sql.append(") \n");

            sql.append(") \n");
        } else if (usarMatriculaExterna && !UteisValidacao.emptyString(idClienteMundi)) {
            textoLog = " matriculaexterna \"" + idClienteMundi + "\" ";
            sql.append("and cl.matriculaexterna = '").append(idClienteMundi).append("' \n");
        } else if (!UteisValidacao.emptyString(cpf)) {
            textoLog = " cpf \"" + cpf + "\" ";
            sql.append("and (regexp_replace(p.cfp, '\\D', '', 'g') = '").append(cpf).append("' or \n");
            sql.append(" p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, false)).append("' or \n");
            sql.append(" p.cfp = '").append(Uteis.formatarCpfCnpj(cpf, true)).append("') \n");
        } else {
            textoLog = " nome \"" + nomeCliente + "\" ";
            if (arquivoVindi) {
                sql.append("and ( \n");
                sql.append("p.nome ilike '").append(nomeCliente).append("' \n");
                sql.append("or \n");
                sql.append("p.nome ilike (select titular from importacaocartaoauxiliar where nome = '").append(nomeCliente).append("') \n");
                sql.append("or \n");
                sql.append("p.nome ilike (select nome from importacaocartaoauxiliar where titular = '").append(nomeCliente).append("') \n");
                sql.append("or \n");
                sql.append("(length(coalesce(regexp_replace(p.cfp, '[^0-9]+', '', 'g'),'')) > 0 \n");
                sql.append("and ( \n");
                sql.append("(regexp_replace(p.cfp, '[^0-9]+', '', 'g') = (select regexp_replace(t.cpf, '[^0-9]+', '', 'g') from importacaocartaoauxiliar t where t.nome = '").append(nomeCliente).append("')) \n");
                sql.append("or \n");
                sql.append("(regexp_replace(p.cfp, '[^0-9]+', '', 'g') = (select regexp_replace(t.cpf, '[^0-9]+', '', 'g') from importacaocartaoauxiliar t where t.titular = '").append(nomeCliente).append("')) \n");
                sql.append("))) \n");
            } else {
                sql.append("and p.nome ilike '").append(nomeCliente).append("' \n");
            }
        }

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            throw new Exception("Nao foi encontrado cliente com " + textoLog);
        }
        if (total > 1) {
            throw new Exception("Foi encontrado " + total + " clientes com " + textoLog);
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            return rs.getInt("codigo");
        }
        throw new Exception("Nao foi encontrado cliente com " + textoLog);
    }

    private static void incluirAutorizacoes(List<AutorizacaoCobrancaClienteVO> listaAuto, Connection con) {
        try {
            int atual = 0;
            for (AutorizacaoCobrancaClienteVO obj : listaAuto) {
                AutorizacaoCobrancaCliente autoDAO;
                try {
                    con.setAutoCommit(false);
                    autoDAO = new AutorizacaoCobrancaCliente(con);
                    Uteis.logarDebug("incluirAutorizacoes | Auto Cliente... " + ++atual + "/" + listaAuto.size());

                    if (tipoImportacao.equals(5) || tipoImportacao.equals(7)) {
                        obj.setValidarQtdCartoes(false);

                        String numeroCardMas = obj.getCartaoMascarado();
                        if (UteisValidacao.emptyString(numeroCardMas)) {
                            numeroCardMas = obj.getCartaoMascarado_Apresentar();
                        }
                        if (UteisValidacao.emptyString(numeroCardMas)) {
                            throw new Exception("Cartão mascarado não identificado.");
                        }

                        boolean existeCartao = SuperFacadeJDBC.existe("select codigo from autorizacaocobrancacliente where ativa and cartaomascaradointerno ilike '" + numeroCardMas + "' and cliente = " + obj.getCliente().getCodigo(), con);
                        if (existeCartao) {
                            throw new Exception("Já existe uma autorização de cobrança para esse cliente com o mesmo cartão " + numeroCardMas);
                        }
                    }
                    obj.setValidarAutorizacaoCobrancaSemelhante(false);
                    obj.setRealizandoImportacao(true); // para não validar o cvv na hora de incluir
                    autoDAO.incluir(obj);
                    if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                        SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacliente set obsimportmundipaag = '" + obj.getCliente().getObservacao() + "' where codigo = " + obj.getCodigo(), con);
                    } else {
                        throw new Exception("Token nao gerado!");
                    }
                    con.commit();
                    sucesso++;
                } catch (Exception ex) {
                    con.rollback();
                    ex.printStackTrace();
                    Uteis.logarDebug("incluirAutorizacoes 1 | Erro: " + ex.getMessage());
                    listaErros.add("INCLUIR AUTORIZAÇÃO | Cliente | " +obj.getImportacaoCliente()+ " | CPF | " + obj.getImportacaoCPF() + " | Titular | " + obj.getImportacaoTitular() + " | ERRO | " + ex.getMessage());
                    falha++;
                } finally {
                    autoDAO = null;
                    con.setAutoCommit(true);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("incluirAutorizacoes 2 | Erro: " + ex.getMessage());
        } finally {
            Uteis.logarDebug("FIM | AtualizarAutorizacao de Clientes...");
        }
    }

    public static void processarArquivos(String prefixoArq, String pathOrigem, Connection con) throws IOException {
        try {
            List<Map<String, File>> arquivos = FileUtilities.readListFilesDirectory(pathOrigem);
            for (Map<String, File> entrada : arquivos) {
                Set<String> s = entrada.keySet();
                for (String fileName : s) {
                    File f = entrada.get(fileName);
                    Uteis.logarDebug(String.format("Verificando prefixo %s do arquivo %s encontrado", prefixoArq, f.getName()));
                    if (!f.getName().startsWith(prefixoArq))
                        continue;
                    if (f.length() > 0) {
                        try {
                            listaErros = new ArrayList<>();
                            sucesso = 0;
                            falha = 0;
                            total = 0;
                            Uteis.logarDebug("Arquivo => " + f.length() + " => " + f.getAbsolutePath());
                            List<AutorizacaoCobrancaClienteVO> lista = processarArquivo(f.getName(), readContentFile(f.getAbsolutePath()).toString(), con);
                            total = lista.size();
                            incluirAutorizacoes(lista, con);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logarDebug("processarArquivos 1 | Erro: " + ex.getMessage());
                        }

                        if (!listaErros.isEmpty()) {
                            Uteis.logarDebug("###########################");
                            Uteis.logarDebug("########## ERROS ##########");
                            Uteis.logarDebug("###########################");
                            int er = 0;
                            for (String erro : listaErros) {
                                System.out.println(++er + "/" + listaErros.size() + " | " + erro);
                            }
                            Uteis.logarDebug("###########################");
                            Uteis.logarDebug("###########################");
                        }

                        Uteis.logarDebug("###########################");
                        Uteis.logarDebug("######## RESULTADO ########");
                        Uteis.logarDebug("###########################");
                        Uteis.logarDebug("Total: " + total);
                        Uteis.logarDebug("Sucesso: " + sucesso);
                        Uteis.logarDebug("Falha: " + falha);
                        Uteis.logarDebug("###########################");
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug("processarArquivos 2 | Erro: " + ex.getMessage());
            throw ex;
        }
    }

    private static void obterClienteTabelaAuxiliar(AutorizacaoCobrancaClienteVO autoVO, String ultimos, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo as cliente_codigo,  \n");
        sql.append("p.codigo as pessoa_codigo, \n");
        sql.append("p.nome as pessoa_nome, \n");
        sql.append("imp.* \n");
        sql.append("from importacaocartaoauxiliar imp \n");
        sql.append("inner join cliente cl on cl.matriculaexterna = imp.matricula::bigint \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
        sql.append("where coalesce(cartao_ultimos,'') <> '' \n");
        sql.append("and cartao_ultimos = '").append(ultimos).append("' \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nao foi encontrado nenhum registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos);
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        List<ItemAuxiliarCartaoTO> lista = new ArrayList<>();
        while (rs.next()) {
            ItemAuxiliarCartaoTO dto = new ItemAuxiliarCartaoTO();

            dto.setMatricula(rs.getString("matricula"));
            dto.setTitular(rs.getString("titular"));
            dto.setNome(rs.getString("nome"));

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
            pessoaVO.setNome(rs.getString("pessoa_nome"));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
            dto.setClienteVO(clienteVO);
            lista.add(dto);
        }

        //filtrar o cliente
        for (ItemAuxiliarCartaoTO dto : lista) {
            if (UteisValidacao.emptyString(dto.getTitular()) ||
                    UteisValidacao.emptyString(autoVO.getNomeTitularCartao()) ||
                    UteisValidacao.emptyString(dto.getClienteVO().getPessoa().getNome()) ||
                    UteisValidacao.emptyString(dto.getNome())) {
                continue;
            }

//            String titularCard = autoVO.getNomeTitularCartao();
//            String titularTabela = dto.getTitular();
//            String omeTabela = dto.getNome();
//            String clienteNomeTabela = dto.getClienteVO().getPessoa().getNome();

            //verificar se o nome do titular e o nome do cliente é o mesmo.
            //devido a importação que o cliente tinha 2 matriculas externa iguais
            if (dto.getTitular().trim().equalsIgnoreCase(autoVO.getNomeTitularCartao().trim()) &&
                    dto.getNome().trim().equalsIgnoreCase(dto.getClienteVO().getPessoa().getNome().trim())) {
                autoVO.setCliente(dto.getClienteVO());
                autoVO.setObservacao("Nome titular e do aluno correspondem");
                return;
            }
        }
//        System.out.println("aaaaa");
    }

    private static void obterClienteTabelaAuxiliarMouve(AutorizacaoCobrancaClienteVO autoVO, String ultimos, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("cl.codigo as cliente_codigo,  \n");
        sql.append("p.codigo as pessoa_codigo, \n");
        sql.append("p.nome as pessoa_nome, \n");
        sql.append("imp.* \n");
        sql.append("from importacaocartaoauxiliar imp \n");
        sql.append("inner join pessoa p on (p.nome ilike imp.nome or (length(coalesce(regexp_replace(p.cfp, '[^0-9]+', '', 'g'),'')) > 0 and regexp_replace(p.cfp, '[^0-9]+', '', 'g') = regexp_replace(imp.cpf, '[^0-9]+', '', 'g'))) \n");
        sql.append("inner join cliente cl on cl.pessoa = p.codigo \n");
        sql.append("where coalesce(cartao_ultimos,'') <> '' \n");
        sql.append("and imp.cartao_ultimos = '").append(ultimos).append("' \n");
        sql.append("and imp.titular ilike '").append(autoVO.getNomeTitularCartao()).append("' \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nao foi encontrado nenhum registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos);
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        List<ItemAuxiliarCartaoTO> lista = new ArrayList<>();
        while (rs.next()) {
            ItemAuxiliarCartaoTO dto = new ItemAuxiliarCartaoTO();

            dto.setMatricula(rs.getString("matricula"));
            dto.setTitular(rs.getString("titular"));
            dto.setNome(rs.getString("nome"));

            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
            pessoaVO.setNome(rs.getString("pessoa_nome"));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
            dto.setClienteVO(clienteVO);
            lista.add(dto);
        }

        //filtrar o cliente
        for (ItemAuxiliarCartaoTO dto : lista) {
            if (UteisValidacao.emptyString(dto.getTitular()) ||
                    UteisValidacao.emptyString(autoVO.getNomeTitularCartao()) ||
                    UteisValidacao.emptyString(dto.getClienteVO().getPessoa().getNome()) ||
                    UteisValidacao.emptyString(dto.getNome())) {
                continue;
            }

//            String titularCard = autoVO.getNomeTitularCartao();
//            String titularTabela = dto.getTitular();
//            String omeTabela = dto.getNome();
//            String clienteNomeTabela = dto.getClienteVO().getPessoa().getNome();

            //verificar se o nome do titular e o nome do cliente é o mesmo.
            //devido a importação que o cliente tinha 2 matriculas externa iguais
            if (dto.getTitular().trim().equalsIgnoreCase(autoVO.getNomeTitularCartao().trim()) &&
                    dto.getNome().trim().equalsIgnoreCase(dto.getClienteVO().getPessoa().getNome().trim())) {
                autoVO.setCliente(dto.getClienteVO());
                autoVO.setObservacao("Nome titular e do aluno correspondem");
                return;
            }
        }
    }

    private static void obterClienteTabelaAuxiliarEvo(AutorizacaoCobrancaClienteVO autoVO, Connection con) throws Exception {

        String ultimos = autoVO.getNumeroCartao().substring(autoVO.getNumeroCartao().length() - 4);
        if (UteisValidacao.emptyString(ultimos)) {
            throw new Exception("ultimos4Numeros nao informado | Titular " + autoVO.getNomeTitularCartao());
        }

        String nomeCliente = autoVO.getImportacaoCliente();
        if (UteisValidacao.emptyString(nomeCliente)) {
            throw new Exception("nomeCliente nao informado | Titular " + autoVO.getNomeTitularCartao());
        }

        String nomeTitular = autoVO.getNomeTitularCartao();
        if (UteisValidacao.emptyString(nomeTitular)) {
            throw new Exception("nomeTitular nao informado | Cartão: " + autoVO.getCartaoMascarado_Apresentar());
        }

        Uteis.logarDebug("Buscar obterClienteTabelaAuxiliarEvo | UltimosDigitos: " + ultimos + " | Nome Cliente: " + nomeTitular + " | Titular: " + nomeTitular);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("distinct \n");
        sql.append("cl.codigo as cliente_codigo, \n");
        sql.append("cl.matricula as cliente_matricula, \n");
        sql.append("p.codigo as pessoa_codigo, \n");
        sql.append("p.nome as pessoa_nome \n");
        sql.append("from importacaocartaoauxiliar imp \n");
        sql.append("inner join cliente cl on cl.matriculaexterna = imp.matricula::integer \n");
        sql.append("inner join pessoa p on p.codigo = cl.pessoa  \n");
        sql.append("where coalesce(imp.cartao_ultimos,'') <> '' \n");
        sql.append("and imp.cartao_ultimos = '").append(ultimos).append("' \n");
        sql.append("and (replace(UPPER(translate(regexp_replace(imp.titular, '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') ilike ");
        sql.append("replace(UPPER(translate(regexp_replace('").append(nomeTitular.toUpperCase()).append("', '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','')) \n");
        sql.append("and (replace(UPPER(translate(regexp_replace(split_part(imp.nome, ' ', 1), '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','') ilike ");
        sql.append("replace(UPPER(translate(regexp_replace(split_part('").append(autoVO.getNomeTitularCartao().toUpperCase()).append("', ' ', 1), '[\\.\\,\\-\\\\\\|]', ''), 'áéíóúâêîôûàèìòùãõäëïöüçÁÉÍÓÚÂÊÎÔÛÀÈÌÒÙÃÕÄËÏÖÜÇ', 'aeiouaeiouaeiouaoaeioucAEIOUAEIOUAEIOUAOAEIOUC')), ' ','')) \n");

        Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql + ") as qtd", con);
        if (UteisValidacao.emptyNumber(total)) {
            Uteis.logarDebug("Nao foi encontrado nenhum registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos + " | Titular: " + nomeTitular + " | Cliente: " + nomeCliente);
            return;
        }
        if (total > 1) {
            Uteis.logarDebug("Foi encontrado mais de 1 registro na tabela importacaocartaoauxiliar com os últimos dígitos: " + ultimos + " | Titular: " + nomeTitular + " | Cliente: " + nomeCliente);
            return;
        }

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        if (rs.next()) {
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setCodigo(rs.getInt("pessoa_codigo"));
            pessoaVO.setNome(rs.getString("pessoa_nome"));

            ClienteVO clienteVO = new ClienteVO();
            clienteVO.setPessoa(pessoaVO);
            clienteVO.setCodigo(rs.getInt("cliente_codigo"));
            clienteVO.setMatricula(rs.getString("cliente_matricula"));

            autoVO.setCliente(clienteVO);
            autoVO.setObservacao("Tabela auxiliar EVO");
        }
    }

    private static void adicionarLog(String msg) {
        Integer qtd = mapaLog.get(msg);
        if (qtd == null) {
            qtd = 0;
        }
        qtd++;

        mapaLog.put(msg, qtd);
    }

    public static StringBuilder readContentFile(final String fileName) throws IOException {
        FileInputStream fis = new FileInputStream(fileName);
        return new StringBuilder(Uteis.convertStreamToStringBuffer(fis, "UTF-8"));
    }
}
