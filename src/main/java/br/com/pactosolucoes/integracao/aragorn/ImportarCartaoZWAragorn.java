package br.com.pactosolucoes.integracao.aragorn;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class ImportarCartaoZWAragorn {

    private static void inserirAutorizacoes(final Connection conOrigem, final Connection conDestino,
                                            final Integer convenioDestino)
            throws Exception {
        try {
            try (ResultSet rsOrigem = SuperFacadeJDBC.criarConsulta(
                    "select distinct\n" +
                            "\tc.codigomatricula, c.matricula, a.*\n" +
                            "from\n" +
                            "\tautorizacaocobrancacliente a\n" +
                            "inner join cliente c on c.codigo = a.cliente \n" +
                            "where\n" +
                            "\ta.ativa\n" +
                            "\tand a.tokenaragorn <> ''\t\n" +
                            "order by\n" +
                            "\tcliente",
                    conOrigem)) {
                while (rsOrigem.next()) {
                    Integer idExterno = rsOrigem.getInt("codigomatricula");
                    //localizar o mesmo cliente no destino através do ID EXTERNO da Importação
                    ResultSet rsCodigoDestino = SuperFacadeJDBC.criarConsulta("select codigo from cliente where idexterno = "
                            + idExterno, conDestino);
                    if (rsCodigoDestino.next()) {
                        final int codClienteDestino = rsCodigoDestino.getInt(1);
                        Uteis.logar("%s - Encontrei CODIGO CLIENTE %s com ID EXTERNO %s",
                                conOrigem.getMetaData().getURL(), codClienteDestino, idExterno);
                        int tipoautorizacao = rsOrigem.getInt("tipoautorizacao");
                        String validadecartao = rsOrigem.getString("validadecartao");
                        int tipoacobrar = rsOrigem.getInt("tipoacobrar");
                        int conveniocobranca = rsOrigem.getInt("conveniocobranca");
                        int operadoracartao = rsOrigem.getInt("operadoracartao");
                        String cpftitular = rsOrigem.getString("cpftitular");
                        String nometitularcartao = rsOrigem.getString("nometitularcartao");
                        String cartaomascaradointerno = rsOrigem.getString("cartaomascaradointerno");
                        String tokenaragorn = rsOrigem.getString("tokenaragorn");
                        //Date dataRegistro
                        boolean ativa = true;

                        if (!SuperFacadeJDBC.existe(String.format("select codigo from autorizacaocobrancacliente where ativa and cliente = %s and tipoautorizacao = %s",
                                codClienteDestino, tipoautorizacao), conDestino)) {

                            final String msg = String.format("%s - insert Auth Code from ID EXTERNO %s to CLIENTE %s imported at %s",
                                    conOrigem.getMetaData().getURL(),
                                    idExterno,
                                    codClienteDestino,
                                    Calendario.hoje());

                            PreparedStatement ps = conDestino.prepareStatement("insert into autorizacaocobrancacliente(" +
                                    "cliente, " +
                                    "tipoautorizacao, " +
                                    "validadecartao, " +
                                    "tipoacobrar, " +
                                    "conveniocobranca, " +
                                    "operadoracartao, " +
                                    "cpftitular, " +
                                    "nometitularcartao, " +
                                    "cartaomascaradointerno," +
                                    "tokenaragorn, " +
                                    "dataRegistro, " +
                                    "ativa, " +
                                    "obsimportzw) " +
                                    "VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)");
                            int i = 1;
                            ps.setInt(i++, codClienteDestino);//1
                            ps.setInt(i++, tipoautorizacao);//2
                            ps.setString(i++, validadecartao);//3
                            ps.setInt(i++, tipoacobrar);//4
                            ps.setInt(i++, convenioDestino);//5
                            ps.setInt(i++, operadoracartao);//6
                            ps.setString(i++, cpftitular);//7
                            ps.setString(i++, nometitularcartao);//8
                            ps.setString(i++, cartaomascaradointerno);//9
                            ps.setString(i++, tokenaragorn);//10
                            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));//11
                            ps.setBoolean(i++, ativa);//12
                            ps.setString(i++, msg);//13
                            ps.execute();

                            Uteis.logar(msg);
                        } else {
                            Uteis.logar(String.format("Já existe uma autorização de cobrança ativa do tipo %s para o cliente %s",
                                    tipoautorizacao, codClienteDestino));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            System.err.println(ex.getMessage());
            throw ex;
        }
    }

    public static void main(String[] args) {
        Connection con = null;
        try {
            Uteis.debug = true;
            Uteis.logar(true, null, "ImportarCartaoZWAragorn | INICIO...");

            if (args.length == 0) {
                args = new String[]{"localhost", "5432", "bdzillyonorigym", "localhost", "5432", "bdzillyonpacer", "23"};
            }

            if (args != null && args.length > 0) {
                Uteis.logar(true, null, "ImportarCartaoZWAragorn | ARGS...");
                int argI = 0;
                for (String arg : args) {
                    Uteis.logar(true, null, "Args[" + ++argI + "] | " + arg);
                }
            }

            String hostOrigem = args[0];
            String portaOrigem = args[1];
            String bancoOrigem = args[2];
            String hostDestino = args[3];
            String portaDestino = args[4];
            String bancoDestino = args[5];
            String convenioDestino = args[6];
            //
            Connection conOrigem = new Conexao(String.format("jdbc:postgresql://%s:%s/%s", hostOrigem, portaOrigem, bancoOrigem),
                    "zillyonweb", "pactodb").getConexao();
            Connection conDestino = new Conexao(String.format("jdbc:postgresql://%s:%s/%s", hostDestino, portaDestino, bancoDestino),
                    "zillyonweb", "pactodb").getConexao();

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE autorizacaocobrancacliente ADD COLUMN obsimportzw text;",
                    conDestino);

            inserirAutorizacoes(conOrigem, conDestino, Integer.valueOf(convenioDestino));

        } catch (Exception ex) {
            System.err.println(ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            Uteis.logar(true, null, "ImportarCartaoZWAragorn | FIM...");
        }
    }

}
