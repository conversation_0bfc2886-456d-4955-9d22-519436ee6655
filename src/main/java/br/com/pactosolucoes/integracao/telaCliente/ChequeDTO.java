package br.com.pactosolucoes.integracao.telaCliente;

import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.ChequeVO;
import negocio.comuns.financeiro.LoteVO;
import negocio.comuns.financeiro.MovContaVO;
import negocio.comuns.financeiro.NFSeEmitidaVO;
import relatorio.negocio.jdbc.financeiro.ProdutoRatear;

import java.util.Date;
import java.util.List;

public class ChequeDTO {

    private Integer codigo;
    private BancoDTO banco;
    private String agencia;
    private String conta;
    private String numero;
    private String nomeNoCheque;
    private Long dataCompensacao;
    private String situacao;
    private Double valor;

    public ChequeDTO(ChequeVO cheque) {
        this.codigo = cheque.getCodigo();
        this.banco = new BancoDTO(cheque.getBanco());
        this.agencia = cheque.getAgencia();
        this.conta = cheque.getConta();
        this.numero = cheque.getNumero();
        this.nomeNoCheque = cheque.getNomeNoCheque();
        this.dataCompensacao = cheque.getDataCompensacao().getTime();
        this.situacao = cheque.getSituacao();
        this.valor = cheque.getValor();
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public BancoDTO getBanco() {
        return banco;
    }

    public void setBanco(BancoDTO banco) {
        this.banco = banco;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getNomeNoCheque() {
        return nomeNoCheque;
    }

    public void setNomeNoCheque(String nomeNoCheque) {
        this.nomeNoCheque = nomeNoCheque;
    }

    public Long getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Long dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public String getSituacao() {
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }
}
