package br.com.pactosolucoes.integracao.telaCliente;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlunoInfoGymPassDTO extends SuperTO {

    private String type;
    private String token;

    public AlunoInfoGymPassDTO() {

    }

    public AlunoInfoGymPassDTO(ClienteVO obj) {
        this.type = obj.getGympassTypeNumber();
        this.token = obj.getGympasUniqueToken();
    }

    public AlunoInfoGymPassDTO(String token) {
        this.token = token;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
