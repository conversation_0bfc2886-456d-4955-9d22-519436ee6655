/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.integracao.importacao;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class PagamentoJSON  extends SuperJSON{
    private Integer idExternoContrato;
    private Date dataCadastro;
    private Date dataCompensacao;
    private Double valor;
    private String formaPagamento;
    private String agencia;
    private String conta;
    private Integer banco;
    private String numero;
    private String nomeCheque;
    private String autorizacaoCartao;
    private String observacao;
    private String operadoraCartao;
    
    public PagamentoJSON() {
    }

    public PagamentoJSON(int idExternoContrato, JSONObject json) throws Exception {
        this.idExternoContrato = idExternoContrato;
        try {
            this.dataCadastro = Uteis.getDate(json.optString("dataCadastro"), "dd/MM/yyyy");
        } catch (Exception ex) {
            dataCadastro = null;
        }
        try {
            this.dataCompensacao = Uteis.getDate(json.optString("dataCompensacao"), "dd/MM/yyyy");
        } catch (Exception ex) {
            throw new Exception("Data de compensação informada está em formato errado. Deve ser dd/MM/yyyy");
        }
        this.valor = json.optDouble("valor");
        this.formaPagamento = json.optString("formaPagamento");
        this.agencia = json.optString("agencia");
        this.conta = json.optString("conta");
        this.banco = json.optInt("banco");
        this.numero = json.optString("numero");
        this.nomeCheque = json.optString("nomeCheque");
        this.autorizacaoCartao = json.optString("autorizacaoCartao");
        this.observacao = json.optString("observacao");
        this.operadoraCartao = json.optString("operadoraCartao");
    }

    public Integer getIdExternoContrato() {
        return idExternoContrato;
    }

    public void setIdExternoContrato(Integer idExternoContrato) {
        this.idExternoContrato = idExternoContrato;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public Date getDataCompensacao() {
        return dataCompensacao;
    }

    public void setDataCompensacao(Date dataCompensacao) {
        this.dataCompensacao = dataCompensacao;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getAgencia() {
        return agencia;
    }

    public void setAgencia(String agencia) {
        this.agencia = agencia;
    }

    public String getConta() {
        return conta;
    }

    public void setConta(String conta) {
        this.conta = conta;
    }

    public Integer getBanco() {
        return banco;
    }

    public void setBanco(Integer banco) {
        this.banco = banco;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(String formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public String getNomeCheque() {
        return nomeCheque;
    }

    public void setNomeCheque(String nomeCheque) {
        this.nomeCheque = nomeCheque;
    }

    public String getAutorizacaoCartao() {
        return autorizacaoCartao;
    }

    public void setAutorizacaoCartao(String autorizacaoCartao) {
        this.autorizacaoCartao = autorizacaoCartao;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public String getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(String operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }
}
