package br.com.pactosolucoes.comuns.to;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;
import java.util.List;

public class BoletoOnlineTO extends SuperVO {

    private TipoBoletoEnum tipoBoletoEnum;
    private Double valor;
    private Date dataVencimento;
    private EmpresaVO empresaVO;
    private PessoaVO pessoaVO;
    private UsuarioVO usuarioVO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private OrigemCobrancaEnum origemCobrancaEnum;
    private List<MovParcelaVO> listaParcelas;
    private boolean cobrarMultaJuros = false;
    private Double descontoValorFixo;
    private Double descontoPercentual;
    private boolean verificarBoletoExistente = false;
    private boolean registrarBoletoAgora = true;

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public PessoaVO getPessoaVO() {
        return pessoaVO;
    }

    public void setPessoaVO(PessoaVO pessoaVO) {
        this.pessoaVO = pessoaVO;
    }

    @Override
    public UsuarioVO getUsuarioVO() {
        return usuarioVO;
    }

    @Override
    public void setUsuarioVO(UsuarioVO usuarioVO) {
        this.usuarioVO = usuarioVO;
    }

    public ConvenioCobrancaVO getConvenioCobrancaVO() {
        return convenioCobrancaVO;
    }

    public void setConvenioCobrancaVO(ConvenioCobrancaVO convenioCobrancaVO) {
        this.convenioCobrancaVO = convenioCobrancaVO;
    }

    public OrigemCobrancaEnum getOrigemCobrancaEnum() {
        return origemCobrancaEnum;
    }

    public void setOrigemCobrancaEnum(OrigemCobrancaEnum origemCobrancaEnum) {
        this.origemCobrancaEnum = origemCobrancaEnum;
    }

    public TipoBoletoEnum getTipoBoletoEnum() {
        return tipoBoletoEnum;
    }

    public void setTipoBoletoEnum(TipoBoletoEnum tipoBoletoEnum) {
        this.tipoBoletoEnum = tipoBoletoEnum;
    }

    public Date getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(Date dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public List<MovParcelaVO> getListaParcelas() {
        return listaParcelas;
    }

    public void setListaParcelas(List<MovParcelaVO> listaParcelas) {
        this.listaParcelas = listaParcelas;
    }

    public EmpresaVO getEmpresaVO() {
        return empresaVO;
    }

    public void setEmpresaVO(EmpresaVO empresaVO) {
        this.empresaVO = empresaVO;
    }

    public void preencherValor() {
        Double soma = 0.0;
        for (MovParcelaVO parcela : getListaParcelas()) {
            if (this.isCobrarMultaJuros()) {
                soma += (parcela.getValorParcela() + parcela.getValorMulta() + parcela.getValorJuros());
            } else {
                soma += parcela.getValorParcela();
            }

        }
        this.setValor(soma);
    }

    public static void validarDados(BoletoOnlineTO obj) throws Exception {
        if (UteisValidacao.emptyNumber(obj.getPessoaVO().getCodigo())) {
            throw new ConsistirException("Pessoa não informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getEmpresaVO().getCodigo())) {
            throw new ConsistirException("Empresa não informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getConvenioCobrancaVO().getCodigo())) {
            throw new ConsistirException("Convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyList(obj.getListaParcelas())) {
            throw new ConsistirException("Nenhuma parcela informada.");
        }
    }

    public boolean isCobrarMultaJuros() {
        return cobrarMultaJuros;
    }

    public void setCobrarMultaJuros(boolean cobrarMultaJuros) {
        this.cobrarMultaJuros = cobrarMultaJuros;
    }

    public Double getDescontoValorFixo() {
        if (descontoValorFixo == null) {
            descontoValorFixo = 0.0;
        }
        return descontoValorFixo;
    }

    public void setDescontoValorFixo(Double descontoValorFixo) {
        this.descontoValorFixo = descontoValorFixo;
    }

    public Double getDescontoPercentual() {
        if (descontoPercentual == null) {
            descontoPercentual = 0.0;
        }
        return descontoPercentual;
    }

    public void setDescontoPercentual(Double descontoPercentual) {
        this.descontoPercentual = descontoPercentual;
    }

    public boolean isVerificarBoletoExistente() {
        return verificarBoletoExistente;
    }

    public void setVerificarBoletoExistente(boolean verificarBoletoExistente) {
        this.verificarBoletoExistente = verificarBoletoExistente;
    }

    public boolean isRegistrarBoletoAgora() {
        return registrarBoletoAgora;
    }

    public void setRegistrarBoletoAgora(boolean registrarBoletoAgora) {
        this.registrarBoletoAgora = registrarBoletoAgora;
    }

}
