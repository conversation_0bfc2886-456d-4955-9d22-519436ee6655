package br.com.pactosolucoes.comuns.json;

import br.com.pactosolucoes.comuns.util.Formatador;
import com.fasterxml.jackson.annotation.JsonIgnore;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.util.Date;

public class ItemCobrancaPactoJSON extends SuperJSON {

    private Integer movParcela;
    private String descricaoParcela;
    private Integer pessoa;
    private String nome;
    private Double valor;
    private String data;
    private Integer remessaItem;
    private Integer transacao;
    private Integer pix;
    private Integer boleto;

    public Integer getMovParcela() {
        return movParcela;
    }

    public void setMovParcela(Integer movParcela) {
        this.movParcela = movParcela;
    }

    public Integer getPessoa() {
        return pessoa;
    }

    public void setPessoa(Integer pessoa) {
        this.pessoa = pessoa;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Integer getTransacao() {
        return transacao;
    }

    public void setTransacao(Integer transacao) {
        this.transacao = transacao;
    }

    public String getDescricaoParcela() {
        return descricaoParcela;
    }

    public void setDescricaoParcela(String descricaoParcela) {
        this.descricaoParcela = descricaoParcela;
    }

    public Integer getRemessaItem() {
        return remessaItem;
    }

    public void setRemessaItem(Integer remessaItem) {
        this.remessaItem = remessaItem;
    }

    @JsonIgnore
    public String getTipoItem() {
        if (!UteisValidacao.emptyNumber(getPix())) {
            return "PIX";
        }
        if (!UteisValidacao.emptyNumber(getTransacao())) {
            return "TRANSAÇÃO";
        }
        if (!UteisValidacao.emptyNumber(getRemessaItem())) {
            return "REMESSA ITEM";
        }
        return "";
    }

    @JsonIgnore
    public String getValor_Apresentar() {
        if (UteisValidacao.emptyNumber(getValor())) {
            return "";
        } else {
            return Formatador.formatarValorMonetario(getValor());
        }
    }

    @JsonIgnore
    public Date getData_Ordenar() throws Exception {
        if (UteisValidacao.emptyString(getData())) {
            return null;
        } else {
            return Uteis.getDate(getData(), "dd/MM/yyyy HH:mm:ss");
        }
    }

    public Integer getPix() {
        return pix;
    }

    public void setPix(Integer pix) {
        this.pix = pix;
    }

    public Integer getBoleto() {
        return boleto;
    }

    public void setBoleto(Integer boleto) {
        this.boleto = boleto;
    }
}
