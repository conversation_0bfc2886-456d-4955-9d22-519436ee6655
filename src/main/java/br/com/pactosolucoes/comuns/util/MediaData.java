package br.com.pactosolucoes.comuns.util;

import java.awt.*;
import java.io.Serializable;

public class MediaData implements Serializable {

    private static final long serialVersionUID = -4765070567504155350L;
    Integer Width = 100;
    Integer Height = 40;
    Color Background = new Color(206, 223, 255);
    Color DrawColor = new Color(0, 0, 0);

    public MediaData() {
    }

    public Color getBackground() {
        return Background;
    }

    public void setBackground(Color background) {
        Background = background;
    }

    public Color getDrawColor() {
        return DrawColor;
    }

    public void setDrawColor(Color drawColor) {
        DrawColor = drawColor;
    }

    public Integer getHeight() {
        return Height;
    }

    public void setHeight(Integer height) {
        Height = height;
    }

    public Integer getWidth() {
        return Width;
    }

    public void setWidth(Integer width) {
        Width = width;
    }
}
