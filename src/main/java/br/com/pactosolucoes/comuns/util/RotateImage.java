/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.comuns.util;

import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import javax.imageio.ImageIO;

/**
 *
 * <AUTHOR>
 */
public class RotateImage{
    
    public static byte[] rotateImage(byte[] imageB, int quadrants, String extensao) throws IOException {
        if(quadrants == 0){
            return imageB;
        }
        // convert byte array back to BufferedImage
        InputStream in = new ByteArrayInputStream(imageB);
        BufferedImage image = ImageIO.read(in);

        int w0 = image.getWidth();
        int h0 = image.getHeight();
        int centerX = w0 / 2;
        int centerY = h0 / 2;
        /*System.out.println("Original dimensions: "+w0+", "+h0);
          System.out.println("Rotated dimensions: "+w1+", "+h1);*/
        if (quadrants % 4 == 1) {
            centerX = h0 / 2;
            centerY = h0 / 2;
        } else if (quadrants % 4 == 3) {
            centerX = w0 / 2;
            centerY = w0 / 2;
        }
        /*System.out.println("CenterX: "+centerX);
        System.out.println("CenterY: "+centerY);*/
        AffineTransform affineTransform = new AffineTransform();
        affineTransform.setToQuadrantRotation(quadrants, centerX, centerY);
        AffineTransformOp opRotated = new AffineTransformOp(affineTransform,
                AffineTransformOp.TYPE_BILINEAR);
        BufferedImage transformedImage = opRotated.filter(image, null);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
	ImageIO.write(transformedImage, extensao, baos );
	baos.flush();
	byte[] imageInByte = baos.toByteArray();
	baos.close();
        return imageInByte;
    }
    
    
    
}
