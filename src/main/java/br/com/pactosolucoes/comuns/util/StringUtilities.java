/**
 * 
 */
package br.com.pactosolucoes.comuns.util;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.Normalizer;
import java.text.SimpleDateFormat;
import java.util.Date;
import negocio.comuns.utilitarias.Calendario;
import org.apache.commons.io.FileUtils;
import org.checkerframework.checker.nullness.qual.NonNull;

/**
 * <AUTHOR>
 */
public class StringUtilities {

    private StringUtilities() {
    }

    /**
     * Substitui todas as ocorrências de um padrão de caracteres dentro de um texto.
     *
     * @param texto
     *            Texto cujo conteúdo terá o padrão substituído pelo texto de reposição fornecido.
     * @param padrao
     *            Padrão que será substituído.
     * @param reposicao
     *            Texto que substituirá o padrão de caracteres.
     */
    public static void substituirTodos(StringBuilder texto, String padrao, String reposicao) {
        int aux = texto.indexOf(padrao);
        int aux2;
        int aux3;
        //evitar que parametro nulo gere NPE
        if (reposicao == null) {
            reposicao = "";
        }

        while (aux >= 0) {
            aux2 = aux + padrao.length();
            aux3 = aux + reposicao.length();
            texto.replace(aux, aux2, reposicao);
            aux = texto.indexOf(padrao, aux3);
        }
    }

    public static String formatarCampo(final BigDecimal valor, int tamanho) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tamanho; i++) {
            sb.append("0");
        }
        DecimalFormat fmt = new DecimalFormat(sb.toString());
        return fmt.format(valor);
    }

    /**
     * Este método formata um número forçando-o a ficar com o tamanho especificado.
     * Além disso, se tamanho fixo é menor do que o valor formatado ele mantem apenas os números da Direita -> Esquerda.
     * Exemplo 1: 123, tamanho 2, resultado: 23
     * Exemplo 2: 123, tamanho 4, resultado: 0123
     * Exemplo e: 123, tamanho 3, resultado: 123
     * @param valor
     * @param tamanho
     * @return
     */
    public static String formatarCampoForcandoZerosAEsquerda(final Number valor, int tamanho) {
        String aux = valor.toString();
        if (valor != null && valor.longValue() > 0) {
            int ind = aux.length() - tamanho;
            if (ind < 0) {
                ind = ind < 0 ? 0 : ind;
                aux = aux.substring(ind);
                for (int n = tamanho - aux.length(); n > ind; n--) {
                    aux = "0" + aux;
                }
            } else {
                aux = aux.substring(ind);
            }
        }

        return aux;
    }

    public static String formatarCampoMonetario(final double valor, int tamanho) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < (tamanho - 2); i++) {
            sb.append("0");
        }

        DecimalFormat fmt = new DecimalFormat(sb.toString() + ".00");    //limita o número de casas decimais
        String s = fmt.format(valor);
        s = s.replace(String.valueOf(fmt.getDecimalFormatSymbols().getDecimalSeparator()), "");
        return s;
    }

    public static String formatarCampoMonetario(final double valor, int tamanho, int tamanhoV) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < (tamanho - 2); i++) {
            sb.append("0");
        }

        StringBuilder sbv = new StringBuilder();
        for (int i = 0; i < (tamanhoV); i++) {
            sbv.append("0");
        }

        DecimalFormat fmt = new DecimalFormat(sb.toString() + "." + sbv.toString());
        String s = fmt.format(valor);
        s = s.replace(String.valueOf(fmt.getDecimalFormatSymbols().getDecimalSeparator()), "");
        return s;
    }



    public static String formatarCampoMonetarioVirgula(final double valor, int tamanho, int tamanhoV) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < (tamanho); i++) {
            sb.append("0");
        }
        StringBuilder sbv = new StringBuilder();
        for (int i = 0; i < (tamanhoV); i++) {
            sbv.append("0");
        }

        DecimalFormat fmt = new DecimalFormat(sb.toString() + "."+sbv.toString());    //limita o número de casas decimais
        String s = fmt.format(valor);
        return s;
    }

    public static void main(String ... args){
        System.out.println(formatarCampoMonetarioVirgula(13.00002, 3, 5));
    }

    public static String formatarCampoEmBranco(final int tamanho) {
        StringBuilder linha = new StringBuilder();
        for (int i = 0; i < tamanho; i++) {
            linha.append(" ");
        }
        return linha.toString();
    }

    public static String formatarCampoEmBranco(final String texto, final int tamanho) {
        StringBuilder linha = new StringBuilder();
        final int treal = texto.length();
        if (treal >= tamanho) {
            linha.append(texto.substring(0, tamanho));
        } else {
            linha.append(texto);
            for (int i = 0; i < Math.abs(tamanho - treal); i++) {
                linha.append(" ");
            }
        }

        return linha.toString();
    }

     public static String formatarCpfCnjp(final String texto, final int tamanho) {
        StringBuilder linha = new StringBuilder();
        String aux = texto.replace(".", "").replace("-", "").replace("/", "");
        final int treal = aux.length();

        if (treal >= tamanho) {
            linha.append(aux.substring(0, tamanho));
        } else {
            for (int i = 0; i < Math.abs(tamanho - treal); i++) {
                linha.append("0");
            }
            linha.append(aux);
        }

        return linha.toString();
    }

    public static String formatarCampoZerado(int tamanho) {
        StringBuilder linha = new StringBuilder();
        for (int i = 0; i < tamanho; i++) {
            linha.append("0");
        }
        return linha.toString();
    }

    public static String formatarCampoData(Date data) {
        SimpleDateFormat formatador = new SimpleDateFormat("ddMMyyyy", Calendario.getDefaultLocale());
        return formatador.format(data);
    }

    public static String formatarCampoData(final Date data, final String pattern) {
        SimpleDateFormat formatador = new SimpleDateFormat(pattern, Calendario.getDefaultLocale());
        return formatador.format(data);
    }

    public static int readInt(final int posI, final int posF, final String texto) {
        return Integer.valueOf(texto.substring(posI, posF));
    }

    public static String readString(final int posI, final int posF, final String texto) {
        return texto.substring(posI, posF);
    }

    public static String readString(final int pos, final String texto) {
        return texto.substring(pos, pos + 1);
    }

    public static BigDecimal readBig(final int posI, final int posF, final String texto) {
        return new BigDecimal(texto.substring(posI, posF));
    }

    public static void adicionaLinha(StringBuilder conteudo, StringBuilder linha) {
        conteudo.append(linha).append(System.getProperty("line.separator"));
    }

    public static void saveToFile(StringBuilder texto, String fileName) throws IOException {
        saveToFile(texto, fileName, "UTF-8");
    }

    public static void saveToFile(StringBuilder texto, String fileName, String encode) throws IOException {
        File f = new File(fileName);
        FileUtils.writeStringToFile(f, texto.toString(), encode);
    }

    
    public static String formatarCampoForcandoZerosAEsquerda(String valor, int tamanho) {
        if (valor != null) {
            int ind = valor.length() - tamanho;
            if (ind < 0) {
                ind = ind < 0 ? 0 : ind;
                valor = valor.substring(ind);
                for (int n = tamanho - valor.length(); n > ind; n--) {
                    valor = "0" + valor;
                }
            } else {
                valor = valor.substring(ind);
            }
        }

        return valor;
    }
    
    public static String limitarTamanho(final String texto, final int tamanho) {
        return texto == null ? "" : texto.length() >= tamanho ? texto.substring(0, tamanho) : texto;
    }

    public static String doRemoverAcentos(String v){
        if(null != v) {
            String resultadoNormalizado = Normalizer.normalize(v, Normalizer.Form.NFD);
            return resultadoNormalizado.replaceAll("[^\\p{ASCII}]", "");
        }
        return v;
    }

    public static String normalize(@NonNull String input) {
        return Normalizer
                .normalize(input, Normalizer.Form.NFD)
                .replaceAll("[^\\p{ASCII}]", "")
                .replaceAll("'", " ")
                .toUpperCase();
    }

    public static String removeSpecialCharacters(String input) {
        return input == null ? "" : normalize(input).replaceAll("[^A-Za-z0-9]", "");
    }

    public static String vaildaApostofre(String palavra) {
        int posicao = palavra.indexOf("'");
        StringBuilder sb = new StringBuilder(palavra);
        if( posicao != -1) {
            for(int i = 0; i < palavra.length(); i++) {
                char c = palavra.charAt(i);
                if ( c == '\'' ){
                    palavra = sb.insert(i+1, "'").toString();
                    i++;
                }
            }
        }
        return  palavra;
    }
}
