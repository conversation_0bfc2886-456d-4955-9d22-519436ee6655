package br.com.pactosolucoes.estudio.converter;

import br.com.pactosolucoes.estudio.util.Validador;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class CategoriaProdutoConverter extends BaseConverter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        try {
            if (value != null && !value.isEmpty()) {
                CategoriaProdutoVO obj = new CategoriaProdutoVO();
                obj.setCodigo(Integer.valueOf(value));
                obj = getFacade().getCategoriaProduto().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                return (CategoriaProdutoVO) obj;
            }
        } catch (Exception e) {
            showMessage(e, context);
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            if (Validador.isValidaObject(value)) {
                return ((CategoriaProdutoVO) value).getCodigo().toString();
            }
        } catch (Exception e) {
            showMessage(e, context);
        }
        return null;
    }
}
