package br.com.pactosolucoes.estudio.converter;

import br.com.pactosolucoes.estudio.util.Validador;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class ProdutoConverter extends BaseConverter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        try {
            if (value != null && !value.isEmpty()) {
                ProdutoVO obj = new ProdutoVO();
                obj.setCodigo(Integer.valueOf(value));
                obj = getFacade().getProduto().consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);

                return (ProdutoVO) obj;
            }
        } catch (Exception e) {
            showMessage(e, context);
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            if (Validador.isValidaObject(value)) {
                return ((ProdutoVO) value).getCodigo().toString();
            }
        } catch (Exception e) {
            showMessage(e, context);
        }
        return null;
    }
}
