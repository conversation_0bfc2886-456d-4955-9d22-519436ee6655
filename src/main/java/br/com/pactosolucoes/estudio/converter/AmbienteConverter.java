package br.com.pactosolucoes.estudio.converter;

import br.com.pactosolucoes.estudio.util.Validador;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import negocio.comuns.plano.AmbienteVO;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class AmbienteConverter extends BaseConverter {

    @Override
    public Object getAsObject(FacesContext context, UIComponent component, String value) {
        try {
            if (value != null && !value.isEmpty()) {
                AmbienteVO obj = new AmbienteVO();
                obj.setCodigo(Integer.valueOf(value));
                obj = getFacade().getAmbiente().obterAmbiente(obj.getCodigo());

                return (AmbienteVO) obj;
            }
        } catch (Exception e) {
            showMessage(e, context);
        }
        return null;
    }

    @Override
    public String getAsString(FacesContext context, UIComponent component, Object value) {
        try {
            if (Validador.isValidaObject(value)) {
                return ((AmbienteVO) value).getCodigo().toString();
            }
        } catch (Exception e) {
            showMessage(e, context);
        }
        return null;
    }
}
