package br.com.pactosolucoes.estudio.modelo;

import annotations.arquitetura.NaoControlarLogAlteracao;
import java.math.BigDecimal;
import java.sql.Time;
import java.util.ArrayList;
import java.util.List;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.financeiro.ItemVendaAvulsaVO;

/**
 *
 * <AUTHOR> - GeoInova Soluções
 */
public class RelatorioFechamentoDiarioVO extends SuperVO {

    @NaoControlarLogAlteracao
    private String descColaborador;
    @NaoControlarLogAlteracao
    private String descCliente;
    @NaoControlarLogAlteracao
    private String descAmbiente;
    @NaoControlarLogAlteracao
    private String descProduto;
    @NaoControlarLogAlteracao
    private String descStatus;
    @NaoControlarLogAlteracao
    private String descTipoHorario;
    @NaoControlarLogAlteracao
    private String descPacote;
    @NaoControlarLogAlteracao
    private String dataAula;
    @NaoControlarLogAlteracao
    private String horaInicio;
    @NaoControlarLogAlteracao
    private String horaTermino;
    @NaoControlarLogAlteracao
    private String usuarioLancamento;
    @NaoControlarLogAlteracao
    private String dataLancamento;
    @NaoControlarLogAlteracao
    private Integer totalStatus;
    @NaoControlarLogAlteracao
    private Integer idAgenda;
    @NaoControlarLogAlteracao
    private Integer totalSessoesAgendadas;
    @NaoControlarLogAlteracao
    private Integer quantidadeItemVenda;
    @NaoControlarLogAlteracao
    private String valorUnitarioItemVenda;
    @NaoControlarLogAlteracao
    private String valorParcialItemVenda;
    @NaoControlarLogAlteracao
    private BigDecimal valorTotalFaturado;
    @NaoControlarLogAlteracao
    private String tipoFaturaItemVenda;
    private Integer idVenda;
    private String nomeComprador;
    private String dataRegistro;
    private String produtoDescricao;
    private String tipoComprador;
    private String valorTotal;
    private Integer itemQuantidade;
    private Integer cliente;
    private Integer colaborador;
    private PacoteVO pacoteVO;

    public String getDataAula() {
        return dataAula;
    }

    public void setDataAula(String dataAula) {
        this.dataAula = dataAula;
    }

    public String getDescAmbiente() {
        return descAmbiente;
    }

    public void setDescAmbiente(String descAmbiente) {
        this.descAmbiente = descAmbiente;
    }

    public String getDescCliente() {
        return descCliente;
    }

    public void setDescCliente(String descCliente) {
        this.descCliente = descCliente;
    }

    public String getDescColaborador() {
        return descColaborador;
    }

    public void setDescColaborador(String descColaborador) {
        this.descColaborador = descColaborador;
    }

    public String getDescProduto() {
        return descProduto;
    }

    public void setDescProduto(String descProduto) {
        this.descProduto = descProduto;
    }

    public String getDescStatus() {
        return descStatus;
    }

    public void setDescStatus(String descStatus) {
        this.descStatus = descStatus;
    }

    public String getHoraInicio() {
        return horaInicio;
    }

    public void setHoraInicio(String horaInicio) {
        this.horaInicio = horaInicio;
    }

    public Integer getTotalSessoesAgendadas() {
        return totalSessoesAgendadas;
    }

    public void setTotalSessoesAgendadas(Integer totalSessoesAgendadas) {
        this.totalSessoesAgendadas = totalSessoesAgendadas;
    }

    public Integer getTotalStatus() {
        return totalStatus;
    }

    public void setTotalStatus(Integer totalStatus) {
        this.totalStatus = totalStatus;
    }

    public Integer getIdAgenda() {
        return idAgenda;
    }

    public void setIdAgenda(Integer idAgenda) {
        this.idAgenda = idAgenda;
    }

    public String getDescTipoHorario() {
        return descTipoHorario;
    }

    public void setDescTipoHorario(String descTipoHorario) {
        this.descTipoHorario = descTipoHorario;
    }

    public Integer getQuantidadeItemVenda() {
        return quantidadeItemVenda;
    }

    public void setQuantidadeItemVenda(Integer quantidadeItemVenda) {
        this.quantidadeItemVenda = quantidadeItemVenda;
    }

    public String getTipoFaturaItemVenda() {
        return tipoFaturaItemVenda;
    }

    public void setTipoFaturaItemVenda(String tipoFaturaItemVenda) {
        this.tipoFaturaItemVenda = tipoFaturaItemVenda;
    }

    public String getValorParcialItemVenda() {
        return valorParcialItemVenda;
    }

    public void setValorParcialItemVenda(String valorParcialItemVenda) {
        this.valorParcialItemVenda = valorParcialItemVenda;
    }

    public String getValorUnitarioItemVenda() {
        return valorUnitarioItemVenda;
    }

    public void setValorUnitarioItemVenda(String valorUnitarioItemVenda) {
        this.valorUnitarioItemVenda = valorUnitarioItemVenda;
    }

    public BigDecimal getValorTotalFaturado() {
        return valorTotalFaturado;
    }

    public void setValorTotalFaturado(BigDecimal valorTotalFaturado) {
        this.valorTotalFaturado = valorTotalFaturado;
    }

    public String getDataLancamento() {
        return dataLancamento;
    }

    public void setDataLancamento(String dataLancamento) {
        this.dataLancamento = dataLancamento;
    }

    public String getUsuarioLancamento() {
        return usuarioLancamento;
    }

    public void setUsuarioLancamento(String usuarioLancamento) {
        this.usuarioLancamento = usuarioLancamento;
    }

    public String getDescPacote() {
        return descPacote;
    }

    public void setDescPacote(String descPacote) {
        this.descPacote = descPacote;
    }

    /**
     * @return the nomeComprador
     */
    public String getNomeComprador() {
        return nomeComprador;
    }

    /**
     * @param nomeComprador the nomeComprador to set
     */
    public void setNomeComprador(String nomeComprador) {
        this.nomeComprador = nomeComprador;
    }

    /**
     * @return the dataregistro
     */
    public String getDataRegistro() {
        return dataRegistro;
    }

    /**
     * @param dataregistro the dataregistro to set
     */
    public void setDataRegistro(String dataRegistro) {
        this.dataRegistro = dataRegistro;
    }

    /**
     * @return the produtoDescricao
     */
    public String getProdutoDescricao() {
        return produtoDescricao;
    }

    /**
     * @param produtoDescricao the produtoDescricao to set
     */
    public void setProdutoDescricao(String produtoDescricao) {
        this.produtoDescricao = produtoDescricao;
    }

    /**
     * @return the tipoComprador
     */
    public String getTipoComprador() {
        return tipoComprador;
    }

    /**
     * @param tipoComprador the tipoComprador to set
     */
    public void setTipoComprador(String tipoComprador) {
        this.tipoComprador = tipoComprador;
    }

    public String getTipoComprador_Apresentar() {
        if (tipoComprador == null) {
            return tipoComprador = "";
        }
        if (tipoComprador.equals("CN")) {
            return "Consumidor";
        }
        if (tipoComprador.equals("CO")) {
            return "Colaborador";
        }
        if (tipoComprador.equals("CI")) {
            return "Cliente";
        }
        return "";
    }

    /**
     * @return the valorTotal
     */
    public String getValorTotal() {
        return valorTotal;
    }

    /**
     * @param valorTotal the valorTotal to set
     */
    public void setValorTotal(String valorTotal) {
        this.valorTotal = valorTotal;
    }

    /**
     * @return the itemQuantidade
     */
    public Integer getItemQuantidade() {
        return itemQuantidade;
    }

    /**
     * @param itemQuantidade the itemQuantidade to set
     */
    public void setItemQuantidade(Integer itemQuantidade) {
        this.itemQuantidade = itemQuantidade;
    }

    /**
     * @return the idVenda
     */
    public Integer getIdVenda() {
        return idVenda;
    }

    /**
     * @param idVenda the idVenda to set
     */
    public void setIdVenda(Integer idVenda) {
        this.idVenda = idVenda;
    }

    /**
     * @return the cliente
     */
    public Integer getCliente() {
        return cliente;
    }

    /**
     * @param cliente the cliente to set
     */
    public void setCliente(Integer cliente) {
        this.cliente = cliente;
    }

    /**
     * @return the colaborador
     */
    public Integer getColaborador() {
        return colaborador;
    }

    /**
     * @param colaborador the colaborador to set
     */
    public void setColaborador(Integer colaborador) {
        this.colaborador = colaborador;
    }

    /**
     * @return the horaTermino
     */
    public String getHoraTermino() {
        return horaTermino;
    }

    /**
     * @param horaTermino the horaTermino to set
     */
    public void setHoraTermino(String horaTermino) {
        this.horaTermino = horaTermino;
    }

    public PacoteVO getPacoteVO() {
        if(pacoteVO==null){
            pacoteVO = new PacoteVO();
        }
        return pacoteVO;
    }

    public void setPacoteVO(PacoteVO pacoteVO) {
        this.pacoteVO = pacoteVO;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RelatorioFechamentoDiarioVO other = (RelatorioFechamentoDiarioVO) obj;
        if (this.pacoteVO.getCodigo() != other.getPacoteVO().getCodigo()) {
            return false;
        }
        return true;
    }
}
