package br.com.pactosolucoes.estudio.controle;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import br.com.pactosolucoes.estudio.modelo.IndisponibilidadeVO;
import br.com.pactosolucoes.estudio.util.CalendarioDataModel;
import controle.arquitetura.SuperControle;
import java.math.BigDecimal;
import java.util.*;
import javax.faces.context.FacesContext;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;

/**
 *
 * <AUTHOR> GeoI<PERSON>
 * <AUTHOR> - GeoInova Soluções
 */
public class AgendaMensalControle extends SuperControle {

    private Date dataSelecionada = Calendario.hoje();
    private boolean apresentarDetalhes = false;
    private CalendarioDataModel calendarioDataModel;
    private BigDecimal decimalProperty;
    private List<AgendaVO> listaDetalhes;
    private String[] week = {"Domingo", "Segunda", "Te<PERSON><PERSON>", "<PERSON>uarta", "<PERSON>uin<PERSON>", "<PERSON>ta", "Sabado"};

    /**
     * Método executado ao clicar no link da Agenda Mensal, gera as informações
     * necessárias pra carregar a página
     *
     * @return String representando a navegação da Agenda Mensal
     * @throws Exception
     */
    public String acaoEntrar() throws Exception {
        atualizarCalendario();
        return "agendaMensal";
    }

    public void atualizarCalendario() throws Exception {
        carregarItensCalendario(getDataSelecionada(), true);
        setDataSelecionada(Calendario.hoje());
    }

    /**
     * Método executado quando se muda o mês do calendário, busca os
     * totalizadores para o mês e associa aos itens do calendário
     *
     * @throws Exception
     */
    public void acaoMudarMes() throws Exception {

        FacesContext contexto = FacesContext.getCurrentInstance();
        String mes = contexto.getExternalContext().getRequestParameterMap().get("myCalendarInputCurrentDate");

        String primeiroDia = "01/" + mes;

        Date dataAnalise = Formatador.obterData(primeiroDia, "dd/MM/yyyy");

        carregarItensCalendario(dataAnalise, false);
    }

    /**
     * Método que vai no banco e busca totalizadores e os associa a cada item do
     * calendário
     *
     * @param Date dataAnalise
     * @param Boolean isAcaoEntrar
     * @throws Exception
     */
    private void carregarItensCalendario(Date dataAnalise, boolean isAcaoEntrar) throws Exception {
        boolean verTodosAgenda = false;
        
        try {
            permissaoFuncionalidade(getUsuarioLogado(), "PermitirVisualizarTodasAulasEstudio", "9.36 - Permite visualizar agenda de outros colaboradores");
            verTodosAgenda = true;
        } catch (Exception e) {
            verTodosAgenda = false;
        }
        
        List<AgendaVO> totalizadores = getFacade().getAgendaEstudio().totalizadores(Uteis.getSQLData(dataAnalise),getUsuarioLogado().getColaboradorVO().getCodigo(),verTodosAgenda);

        HashMap<String, List<AgendaVO>> mapeador = new HashMap<String, List<AgendaVO>>();

        Calendar c = Calendar.getInstance();
        for (AgendaVO total : totalizadores) {
            c.setTime(total.getDataAula());
            try {
                mapeador.get(Uteis.getDataAplicandoFormatacao(c.getTime(), "dd-MM-yyyy")).add(total);
            } catch (Exception e) {
                List<AgendaVO> listaAgendas = new ArrayList<AgendaVO>();
                listaAgendas.add(total);
                mapeador.put(Uteis.getDataAplicandoFormatacao(c.getTime(), "dd-MM-yyyy"), listaAgendas);
            }
        }
        Date primeiro = Uteis.obterPrimeiroDiaMes(dataAnalise);
        Date ultimo = Uteis.obterUltimoDiaMes(dataAnalise);
        //Busca dias da empresa fechada e os mapeia nas Celulas da Agenda
        List<IndisponibilidadeVO> listaIndisponibilidade;
        listaIndisponibilidade = getFacade().getDisponibilidade().buscarIndisponibilidadeFiltro("empresa",
                "empresa_fechada", getEmpresaLogado().getCodigo(), Uteis.getSQLData(primeiro), Uteis.getSQLData(ultimo));
        for (IndisponibilidadeVO agendaIndisponivel : listaIndisponibilidade) {
            c.setTime(agendaIndisponivel.getDiaMes());
            List<AgendaVO> listaAgendas = new ArrayList<AgendaVO>();
            mapeador.put(Uteis.getDataAplicandoFormatacao(c.getTime(), "dd-MM-yyyy"), listaAgendas);
        }
        //Busca os feriados e o mapeia nas Celulas da Agenda
        listaIndisponibilidade = getFacade().getDisponibilidade().buscarFeriados(Uteis.getSQLData(primeiro),
                Uteis.getSQLData(ultimo));
        for (IndisponibilidadeVO agendaIndisponivel : listaIndisponibilidade) {
            c.setTime(agendaIndisponivel.getDiaMes());
            List<AgendaVO> listaAgendas = new ArrayList<AgendaVO>();
            mapeador.put(Uteis.getDataAplicandoFormatacao(c.getTime(), "dd-MM-yyyy"), listaAgendas);
        }
        setCalendarioDataModel(new CalendarioDataModel());
        getCalendarioDataModel().setMapeador(mapeador);
        if (!isAcaoEntrar) {
            Date[] dataVetor = new Date[1];
            dataVetor[0] = dataAnalise;
            getCalendarioDataModel().setCurrentDate(dataVetor[0]);
            getCalendarioDataModel().setItems(null);
            getCalendarioDataModel().getData(dataVetor);
        }
    }

    /**
     * Método para fechar o modal de Detalhes.
     */
    public void acaoFecharDetalhes() {
        setApresentarDetalhes(Boolean.FALSE);
    }

    /**
     * Método para trazer detalhes e preencher o modal.
     */
    public void acaoBuscarDetalhes() {
        try {
            FacesContext facesContext = FacesContext.getCurrentInstance();
            String dataAux = facesContext.getExternalContext().getRequestParameterMap().get("myCalendarInputDate");
            setDataString(dataAux);
            setListaDetalhes(getFacade().getAgendaEstudio().buscarDetalhesMensal(Uteis.getSQLData(getDataSelecionada())));
            setApresentarDetalhes(Boolean.TRUE);
        } catch (Exception e) {
            setListaDetalhes(new ArrayList<AgendaVO>());
        }
    }

    /**
     * Transforma a Data String em Date com hora zerada já no atributo
     * dataSelecionada
     *
     * @param String data
     */
    private void setDataString(String data) {
        setDataSelecionada(Calendario.getDataComHoraZerada(Formatador.obterData(data, "dd/MM/yyyy")));
    }

    /**
     * Configura um BigDecimal para duas casas decimais
     *
     * @param decimal
     * @return String com decimal de 2 casas
     */
    public String setar2CasasDecimais() {
        return String.format("%.2f", getDecimalProperty());
    }

    //Getters and Setters
    public Date getDataSelecionada() {
        return dataSelecionada;
    }

    public void setDataSelecionada(Date dataSelecionada) {
        this.dataSelecionada = dataSelecionada;
    }

    public CalendarioDataModel getCalendarioDataModel() {
        return calendarioDataModel;
    }

    public void setCalendarioDataModel(CalendarioDataModel calendarioDataModel) {
        this.calendarioDataModel = calendarioDataModel;
    }

    public boolean isApresentarDetalhes() {
        return apresentarDetalhes;
    }

    public void setApresentarDetalhes(boolean apresentarDetalhes) {
        this.apresentarDetalhes = apresentarDetalhes;
    }

    public List<AgendaVO> getListaDetalhes() {
        return listaDetalhes;
    }

    public void setListaDetalhes(List<AgendaVO> listaDetalhes) {
        this.listaDetalhes = listaDetalhes;
    }

    public String[] getWeek() {
        return week;
    }

    public void setWeek(String[] week) {
        this.week = week;
    }

    public BigDecimal getDecimalProperty() {
        return decimalProperty;
    }

    public void setDecimalProperty(BigDecimal decimalProperty) {
        this.decimalProperty = decimalProperty;
    }
}
