package br.com.pactosolucoes.estudio.util;

import br.com.pactosolucoes.estudio.enumeradores.StatusEnum;
import br.com.pactosolucoes.estudio.modelo.AgendaVO;
import java.util.*;
import javax.sound.midi.SysexMessage;
import negocio.comuns.utilitarias.Uteis;
import org.richfaces.model.CalendarDataModel;
import org.richfaces.model.CalendarDataModelItem;

/**
 *
 * <AUTHOR>
 * <AUTHOR> - GeoInova Soluções
 */
public class CalendarioDataModel implements CalendarDataModel {

    /*
     * (non-Javadoc) @see
     * org.richfaces.component.CalendarDataModel#getData(java.util.Date[])
     */
    private CalendarDataModelItem[] items;
    private String currentDescription;
    private String currentShortDescription;
    private Date currentDate;
    private boolean currentDisabled;
    private HashMap<String, List<AgendaVO>> mapeador;

    /*
     * (non-Javadoc) @see
     * org.richfaces.model.CalendarDataModel#getData(java.util.Date[])
     */
    public CalendarDataModelItem[] getData(Date[] dateArray) {
        try {
            if (dateArray == null) {
                return null;
            }
            if (items == null) {
                Calendar c = Calendar.getInstance();
                c.setTime(Uteis.obterPrimeiroDiaMes(dateArray[0]));
                Calendar c2 = Calendar.getInstance();
                c2.setTime(Uteis.obterUltimoDiaMes(dateArray[0]));
                dateArray = new Date[c2.get(Calendar.DAY_OF_MONTH)];
                dateArray[0] = c.getTime();
                for (int i = 1; i < c2.get(Calendar.DAY_OF_MONTH); i++) {
                    c.add(Calendar.DAY_OF_MONTH, 1);
                    dateArray[i] = c.getTime();
                }
//            dateArray = new Date[2];
//            dateArray[0] = getDate2(l"2012-04-12");
//            dateArray[1] = getDate2("2012-04-13");
                items = new CalendarDataModelItem[dateArray.length];
                for (int i = 0; i < dateArray.length; i++) {
                    items[i] = createDataModelItem(dateArray[i]);
                }
            }
            return items;
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * @param date
     * @return CalendarDataModelItem for date
     */
    protected CalendarDataModelItem createDataModelItem(Date date) {
        CalendarDataModelItemImpl item = new CalendarDataModelItemImpl();
        Map data = new HashMap();
        List<AgendaVO> agendas = getMapeador().get(Uteis.getDataAplicandoFormatacao(date, "dd-MM-yyyy"));
        if (agendas == null) {
            data.put("bool", "hidden");
            data.put("naoPaga", " ");
            data.put("totalSessao", " ");
            data.put("dataDetalhes", null);
        } else {
            if (agendas.isEmpty()) {
                data.put("bool", "hidden");
                data.put("naoPaga", " ");
                data.put("totalSessao", "DIA/HORÁRIO INDISPONÍVEL");
                data.put("dataDetalhes", null);
            } else {
                int sessoes = 0;
                String tipos;
                for (int i = 0; i < agendas.size(); i++) {
                    StatusEnum status = agendas.get(i).getStatus();
                    if (status == null) {
                        data.put("naoPaga", String.valueOf(agendas.get(i).getCodigo()) + " - " + "$");
                    } else {
                        String descricao = status.getDescricao();
                        if (descricao == null || descricao.isEmpty()) {
                            descricao = "EM BRANCO";
                        }
                        int total = agendas.get(i).getCodigo();
                        sessoes += total;
                        tipos = String.valueOf(total) + " - " + descricao;
                        data.put(agendas.get(i).getStatus().getId().toLowerCase(), tipos);
                    }
                }
                try {
                    if (data.get("naoPaga") != null) {
                        data.put("bool", "visible");
                    } else {
                        data.put("bool", "hidden");
                    }
                } catch (Exception e) {
                    data.put("bool", "hidden");
                }
                data.put("dataDetalhes", date);
                data.put("totalSessao", String.valueOf(sessoes) + " - " + "SESSÕES");
            }
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        item.setDay(c.get(Calendar.DAY_OF_MONTH));
        item.setEnabled(true);
        item.setStyleClass("rel-hol");
        item.setData(data);
        return item;
    }

    /*
     * (non-Javadoc) @see
     * org.richfaces.model.CalendarDataModel#getToolTip(java.util.Date)
     */
    public Object getToolTip(Date date) {

        // TODO Auto-generated method stub
        return null;
    }

    /**
     * @return items
     */
    public CalendarDataModelItem[] getItems() {
        return items;
    }

    /**
     * @param setter for items
     */
    public void setItems(CalendarDataModelItem[] items) {
        this.items = items;
    }

    /**
     * @return currentDescription
     */
    public String getCurrentDescription() {
        return currentDescription;
    }

    /**
     * @param currentDescription
     */
    public void setCurrentDescription(String currentDescription) {
        this.currentDescription = currentDescription;
    }

    /**
     * @return currentDisabled
     */
    public boolean isCurrentDisabled() {
        return currentDisabled;
    }

    /**
     * @param currentDisabled
     */
    public void setCurrentDisabled(boolean currentDisabled) {
        this.currentDisabled = currentDisabled;
    }

    /**
     * @return currentShortDescription
     */
    public String getCurrentShortDescription() {
        return currentShortDescription;
    }

    /**
     * @param currentShortDescription
     */
    public void setCurrentShortDescription(String currentShortDescription) {
        this.currentShortDescription = currentShortDescription;
    }

    /**
     * @return currentDate
     */
    public Date getCurrentDate() {
        return currentDate;
    }

    /**
     * @param currentDate
     */
    public void setCurrentDate(Date currentDate) {
        this.currentDate = currentDate;
    }

    public HashMap<String, List<AgendaVO>> getMapeador() {
        return mapeador;
    }

    public void setMapeador(HashMap<String, List<AgendaVO>> mapeador) {
        this.mapeador = mapeador;
    }
}
