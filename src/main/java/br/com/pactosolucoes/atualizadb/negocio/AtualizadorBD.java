/**
 *
 */
package br.com.pactosolucoes.atualizadb.negocio;

import br.com.pactosolucoes.atualizadb.enumerador.Resultado;
import br.com.pactosolucoes.atualizadb.processo.ExecutarProcessos;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import br.com.pactosolucoes.atualizadb.processo.annotations.Processo;
import br.com.pactosolucoes.atualizadb.to.AtualizacaoTO;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.enumeradores.SinalizadorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.SistemaException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;
import servicos.propriedades.PropsService;
import servicos.util.LoggingOutputMascarade;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class AtualizadorBD extends SuperEntidade {

    private final static String ERRO_VERSAO_ATUAL = "A versão corrente do Banco de Dados do sistema já é a especificada.";
    private final static String ERRO_VERSAO_MAIS_ATUAL = "A versão corrente do Banco de Dados do sistema é mais atual que a especificada.";
    private final static String ERRO_VERSAO_ATUAL_NAO_DEFINIDA = "A versão corrente do Banco de Dados do sistema não está definida.";
    private final static String ERRO_EXECUCAO_SQL = "Erro na execução do statement SQL";
    private final static String ERRO_EXECUCAO_PROCESSO = "Erro na execução do processo";
    private final static String ERRO_LER_ARQUIVO_ATUALIZACAO = "O arquivo que contem o script de atualização não pode ser lido.";
    private final static String SUCESSO_ATUALIZACAO_EXECUTADA = "Atualização executada com sucesso.";
    private boolean disableLogOutputMascarade = false;

    /**
     * @throws Exception
     * <AUTHOR>
     */
    public AtualizadorBD() throws Exception {
        super();
    }

    public AtualizadorBD(boolean disableLogOutputMascarade) throws Exception {
        super();
        this.disableLogOutputMascarade = disableLogOutputMascarade;
    }

    public AtualizadorBD(Connection connection) throws Exception {
        super(connection);
    }

    public AtualizadorBD(Connection connection, boolean disableLogOutputMascarade) throws Exception {
        super(connection);
        this.disableLogOutputMascarade = disableLogOutputMascarade;
    }

    public class ResultadoProcesso {

        private String desc;
        private Processo anot;
        private ClasseProcesso anotClass;
        private ExecutarProcessos instanc;
        private Class cls;

        public ResultadoProcesso() {
        }
    }

    public List<String> processosARodar() throws SistemaException {
        List<String> classesAnotadas = this.listarClassesAnotadas();
        List<String> encontrados = new ArrayList<>();
        String inClause = String.join(",", classesAnotadas.stream().map(p -> "?").toArray(String[]::new));
        String sql = "SELECT script FROM bdatualizacao WHERE script IN (" + inClause + ")";
        try (PreparedStatement stmt = this.con.prepareStatement(sql)) {
            for (int i = 0; i < classesAnotadas.size(); i++) {
                stmt.setString(i + 1, classesAnotadas.get(i));
            }
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                encontrados.add(rs.getString("script"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        List<String> naoEncontrados = new ArrayList<>();
        for (String processo : classesAnotadas) {
            if (!encontrados.contains(processo)) {
                naoEncontrados.add(processo);
            }
        }
        return naoEncontrados;
    }

    public void persistirNaoEncontrados(List<String> naoEncontrados) {
        String sql = "INSERT INTO processosmigracao (script, versaoaplicacao, dataprocesso) " +
                " SELECT ?, ?, ? WHERE NOT EXISTS (\n" +
                "    SELECT 1\n" +
                "    FROM processosmigracao\n" +
                "    WHERE script = ? \n" +
                ");";

        try (PreparedStatement pstmt = this.con.prepareStatement(sql)) {
            for (String processo : naoEncontrados) {
                pstmt.setString(1, processo);
                pstmt.setString(2, PropsService.getPropertyValue(PropsService.VERSAO_SISTEMA));
                pstmt.setTimestamp(3, new Timestamp(Calendario.hoje().getTime()));
                pstmt.setString(4, processo);
                pstmt.executeUpdate();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * Obtem a versão atual do Banco de Dados do sistema.
     *
     * @return Versão atual do Banco de Dados.
     * @throws SistemaException
     * <AUTHOR>
     */
    public Integer obterVersaoAtual() throws SistemaException {
        Integer versao;
        try {
            // Montar query que obtem a versão atual do banco
            Declaracao dc = new Declaracao("SELECT versao FROM bdversaoatual", this.con);
            ResultSet resultado = dc.executeQuery();
            if (resultado.next()) {
                versao = resultado.getInt("versao");
            } else {
                throw new SistemaException(ERRO_VERSAO_ATUAL_NAO_DEFINIDA);
            }
        } catch (SQLException e) {
            throw criarSistemaException(ERRO_EXECUCAO_SQL, e);
        }

        return versao;
    }

    public List<String> executarClassesProcessos(Integer versaoAtual, Integer codigoUsuario, List<String> processosRodar) throws Exception {
        List<String> listaAtualizacoesExecutadas = new ArrayList<>();
        for (String classeScript : processosRodar) {
            try {
                // Desligar o autocommit da conexão
                this.getCon().setAutoCommit(false);
                // Obter os comentários da atualização e executar o processo via Reflection
                ResultadoProcesso resultadoProcesso = executarClasseProcesso(classeScript);
                // Incluir no banco o registro dessa execução da atualização
                listaAtualizacoesExecutadas.add(this.atualizarVersao(versaoAtual,
                        classeScript, resultadoProcesso.desc, Resultado.SUCESSO, SUCESSO_ATUALIZACAO_EXECUTADA, null,
                        codigoUsuario, true));
                // Executar a transação
                this.getCon().commit();
            } catch (SistemaException e) {
                Uteis.logar(e, AtualizadorBD.class);
                this.getCon().rollback();
            } finally {
                this.getCon().setAutoCommit(true);

            }
        }
        return listaAtualizacoesExecutadas;
    }

    /**
     * Atualiza o Banco de Dados do sistema.
     *
     * @param versao Versão objetivo, que foi especificada.
     * @param codigoUsuario Usuário que invocou a atualização do Banco de Dados.
     * @throws SQLException
     * @throws SistemaException
     * <AUTHOR>
     */
    public synchronized List<String> atualizar(Integer versao, Integer codigoUsuario) throws SQLException, SistemaException, Exception {
       Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Atualizando BD em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});
        List<String> listaAtualizacoesExecutadas;
        List<String> listaAtualizacoesRepetir = new ArrayList<>();

        boolean atualizouBD = false;

        // Obter versão atual do Banco de Dados
        Integer versaoBDAtual = this.obterVersaoAtual();
        List<String> processosRodar = this.processosARodar();

        // Atualizar a partir da versao 1930, disponivel no executar processos
        if (versaoBDAtual < 1929) {
            versaoBDAtual = 1929;
        }

        // Se a versão do Banco for igual à especificada
        if (versaoBDAtual.equals(versao) && processosRodar.isEmpty()) {
            // Lançar exceção dizendo que o banco já se encontra na versão especificada
            throw new SistemaException(ERRO_VERSAO_ATUAL);
        } else if (versaoBDAtual > versao && processosRodar.isEmpty()) {
            // Senão
            // Lançar exceção dizendo que o banco está numa versão mais atual que a especificada
            throw new SistemaException(ERRO_VERSAO_MAIS_ATUAL);
        }
        try {
           getFacade().getSinalizadorSistema().incluir(SinalizadorEnum.BDATUALIZANDO, true);

           listaAtualizacoesExecutadas = new ArrayList<>(this.executarClassesProcessos(versao, codigoUsuario, processosRodar));
           if (!listaAtualizacoesExecutadas.isEmpty()) {
               atualizouBD = true;
           }

        // Executar todos os scripts de atualização das versões entre a versão do banco e a versão especificada
            ResultadoProcesso resultadoProcesso = new ResultadoProcesso();
            for (versaoBDAtual++; versaoBDAtual <= versao; versaoBDAtual++) {
                StringBuilder atualizacao = new StringBuilder();

                LoggingOutputMascarade lom = null;
                if (!disableLogOutputMascarade && PropsService.isTrue(PropsService.loggingOutputMascarade)) {
                    lom = new LoggingOutputMascarade();
                    lom.setPrintDetails(false);
                    //REDIRECIONAR o Log do Sistema
                    lom.init();
                }
                String metodo = "";
                try {
                    // Desligar o autocommit da conexão
                    this.getCon().setAutoCommit(false);
                    metodo = "migracaoVersao" + versaoBDAtual;

                    atualizacao.append(metodo);

                    // Obter os comentários da atualização e executar o processo via Reflection
                    resultadoProcesso = executarProcesso(metodo, resultadoProcesso);

                    atualizouBD = true;

                    // Incluir no banco o registro dessa execução da atualização
                    listaAtualizacoesExecutadas.add(this.atualizarVersao(versaoBDAtual,
                            metodo, resultadoProcesso.desc, Resultado.SUCESSO, SUCESSO_ATUALIZACAO_EXECUTADA +
                                    "\n" + (lom != null ? lom.getResult().toString() : ""), null,
                            codigoUsuario, false));

                    // Executar a transação
                    this.getCon().commit();
                     atualizacao = null;
                } catch (SistemaException e) {
                    // Em caso de erro:
                    // Cancelar os statements até então presentes na transação
                    this.getCon().rollback();
                    try {
                        //em caso de exceção lançada por objetos que já existem
                        //devemos ignorar e inserir com sucesso esta atualização
                        if (resultadoProcesso != null && resultadoProcesso.anot != null
                                && resultadoProcesso.anot.repetirCasoErro()) {
                            listaAtualizacoesRepetir.add(metodo);
                        }
                        if (e.getMessage().contains("exist")) {
                            listaAtualizacoesExecutadas.add(this.atualizarVersao(versaoBDAtual, atualizacao.toString(), resultadoProcesso.desc,
                                    Resultado.SUCESSO, e.getMessage() + "\n" + (lom != null ? lom.getResult().toString() : ""), null,
                                    codigoUsuario, false));

                        } else {
                            // Obter stackTrace da exceção
                            StringBuilder stackTrace = new StringBuilder();
                            stackTrace.append(e.getStackTrace()[0]);
                            for (int i = 1; i < 5; i++) {
                                stackTrace.append("\n").append(e.getStackTrace()[i]);
                            }

                            // Incluir no banco o registro dessa tentativa de execução da atualização
                            listaAtualizacoesExecutadas.add(this.atualizarVersao(versaoBDAtual,
                                    atualizacao.toString(), resultadoProcesso.desc, Resultado.ERRO,
                                    e.getMessage() + "\n" + (lom != null ? lom.getResult().toString() : ""),
                                    stackTrace.toString(),
                                    codigoUsuario, false));
                        }
                        // Executar a transação
                        this.getCon().commit();
                    } catch (SistemaException e2) {
                        // Em caso de erro:
                        // Cancelar os statements até então presentes na transação
                        this.getCon().rollback();
                        // Jogar a exceção
                        throw e2;
                    }
                    //em caso de exceção lançada por objetos que já existem
                    //devemos ignorar e inserir com sucesso esta atualização
                    if (!e.getMessage().contains("exist")) {
                        throw e;
                    }
                } finally {
                    // Restaurar os Outs padrões!
                    if (lom != null) lom.finalizing();
                    lom = null;
                    // Ligar o autocommit da conexão
                    this.getCon().setAutoCommit(true);

                }
            }//for... atualizações
            /**
             * Se houve alguns processos com erros, deve repeti-los na sua ordem de
             * execução.
             *
             */
            for (String metodo : listaAtualizacoesRepetir) {
                try {
                    Uteis.logar(null, "Repetindo Processo " + metodo + " -> ");
                    executarProcesso(metodo, resultadoProcesso);
                    Uteis.logar(null, "Sucesso!");
                } catch (Exception e) {
                    Uteis.logar(e, AtualizadorBD.class);
                }
            }

            try {
                this.verificarSeEhBancoNovo();
            } catch (Exception e) {
                throw criarSistemaException(e.getMessage(), e);
            }

            if (atualizouBD) {
                executarProcesso("onPostAllProcess", resultadoProcesso);
            }
            getFacade().getSinalizadorSistema().incluir(SinalizadorEnum.BDATUALIZANDO, false);
        }catch (Exception ex) {
            getFacade().getSinalizadorSistema().incluir(SinalizadorEnum.BDATUALIZANDO, false);
            throw ex;
        }
        Logger.getLogger(this.getClass().getName()).log(
                Level.INFO, "Fim da atualização BD em: {0}", new Object[]{Uteis.getDataComHora(Calendario.hoje())});

        persistirNaoEncontrados(processosRodar);
        return listaAtualizacoesExecutadas;
    }

    public List<String> listarClassesAnotadas() {
        String pacote = "br.com.pactosolucoes.atualizadb.processo.executarprocessos";
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(pacote))
                .setScanners(new TypeAnnotationsScanner(), new SubTypesScanner(false))
                .filterInputsBy(new FilterBuilder().includePackage(pacote)));

        Set<Class<?>> classes = reflections.getTypesAnnotatedWith(ClasseProcesso.class);
        List<String> classNames = new ArrayList<>();
        classes = classes.stream().sorted(Comparator.comparing(this::extractDateFromAnnotation))
                .collect(Collectors.toCollection(LinkedHashSet::new));;
        for (Class<?> clazz : classes) {
            classNames.add(clazz.getSimpleName());
        }
        return classNames;
    }

    /**
     * Verifica por meio do campo rodarSqlsBancoInicial se o banco é novo.
     * (setado como true no Zillyon.sql)
     *
     * @return true (se banco novo) ou false(se banco antigo)
     * <AUTHOR> Pereira
     */
    public boolean verificarSeEhBancoNovo() throws Exception {
        boolean rodarSqlsBancoInicial = new ConfiguracaoSistema(con).consultarCampoRodarSqlsBancoInicial();
        if (rodarSqlsBancoInicial) {
            PovoadorDadosBancoInicial povoador = new PovoadorDadosBancoInicial();
            povoador.inserirDadosBancoInicial();
            return true;
        }
        return false;
    }

    /**
     * Obtem os comentários presentes no script de atualização.
     *
     * @param atualizacao Script de atualização.
     * @return Comentários.
     * <AUTHOR>
     */
    @Deprecated
    public static String obterDescricaoAtualizacao(StringBuilder atualizacao) {
        // Montar regex para encontrar comentários SQL
        Pattern comentario = Pattern.compile("--.*\\r?\\n");
        Matcher matcher = comentario.matcher(atualizacao);

        // Encontrar todos os comentários no script de atualização
        StringBuilder descricao = new StringBuilder();
        while (matcher.find()) {
            descricao.append(matcher.group().split("\\r?\\n")[0]);
        }

        matcher = comentario.matcher(atualizacao);

        // apagar os comentários no script de atualização para evitar Erro de SQL Encoding incompatível
        while (matcher.find(0)) {
            atualizacao = atualizacao.replace(matcher.start(), matcher.end(), "");
        }

        return descricao.toString();
    }

    /**
     * Metodo que executa um método por reflection. Retorna uma descrição
     * contendo as informações dos atributos anotados de cada processo pela
     * Annotation
     *
     * @Processo.
     * <AUTHOR> Maciel
     */
    private ResultadoProcesso executarProcesso(final String metodo, ResultadoProcesso resultado) throws SistemaException {
        try {
            if (resultado == null) {
                resultado = new ResultadoProcesso();
                ExecutarProcessos instancia = new ExecutarProcessos(this.con);
                resultado.instanc = instancia;
                Class clazz = Class.forName("br.com.pactosolucoes.atualizadb.processo.ExecutarProcessos");
                resultado.cls = clazz;
            } else if (resultado.instanc == null || resultado.cls == null) {
                ExecutarProcessos instancia = new ExecutarProcessos(this.con);
                Class clazz = Class.forName("br.com.pactosolucoes.atualizadb.processo.ExecutarProcessos");
                resultado.cls = clazz;
                resultado.instanc = instancia;
            }

            Method meth = resultado.cls.getMethod(metodo);
            Processo anotacao = meth.getAnnotation(Processo.class);
            String descricao = "";
            if (anotacao != null) {
                descricao = String.format("-- Descricao: %s<br/>"
                        + "-- Autor: %s<br />"
                        + "-- Data: %s<br/>"
                        + "-- Motivacao: %s<br/>"
                        + "-- RepetirCasoErro: %s<br/>",
                        new Object[]{
                    anotacao.descricao(),
                    anotacao.autor(),
                    anotacao.data(),
                    anotacao.motivacao(),
                    anotacao.repetirCasoErro()
                });
            }

            resultado.desc = descricao;
            resultado.anot = anotacao;

            meth.invoke(resultado.instanc);

            return resultado;
        } catch (Exception e) {
            throw criarSistemaException(ERRO_EXECUCAO_PROCESSO + ": \"" + metodo + "\"", e);
        }
    }

    private ResultadoProcesso executarClasseProcesso(final String classeProceso) throws SistemaException {
        try {
            String pacote = "br.com.pactosolucoes.atualizadb.processo.executarprocessos";
            ResultadoProcesso resultado = new ResultadoProcesso();
            Class<?> clazz = Class.forName(pacote + "."  + classeProceso);

            ClasseProcesso anotacao = clazz.getAnnotation(ClasseProcesso.class);
            String descricao = "";
            if (anotacao != null) {
                descricao = String.format("-- Descricao: %s<br/>"
                        + "-- Autor: %s<br />"
                        + "-- Data: %s<br/>"
                        + "-- Motivacao: %s<br/>"
                        + "-- RepetirCasoErro: %s<br/>",
                        new Object[]{
                    anotacao.descricao(),
                    anotacao.autor(),
                    anotacao.data(),
                    anotacao.motivacao(),
                    false
                });
            }

            resultado.desc = descricao;
            resultado.anotClass = anotacao;

            Object instancia = clazz.getDeclaredConstructor().newInstance();
            Method metodoExecutar = clazz.getMethod("executar", java.sql.Connection.class);
            metodoExecutar.invoke(instancia, this.getCon());

            return resultado;
        } catch (Exception e) {
            throw criarSistemaException(ERRO_EXECUCAO_PROCESSO + ": \"" + classeProceso + "\"", e);
        }
    }

    public static void executarUmProcessoQualquer(final String metodo, Connection con) throws SistemaException {
        try {
            String met = metodo.substring(metodo.lastIndexOf(".") + 1);
            Class cls = Class.forName(metodo.substring(0, metodo.lastIndexOf(".")));
            Method meth = cls.getMethod(met, new Class[]{Connection.class});
            meth.invoke(cls.newInstance(), new Object[]{con});
        } catch (Exception e) {
            throw criarSistemaException(ERRO_EXECUCAO_PROCESSO + ": \"" + metodo + "\"", e);
        }
    }


    public static void executarUmProcessoQualquer(final String metodo, String[] args) throws SistemaException {
        try {
            String met = metodo.substring(metodo.lastIndexOf(".") + 1);
            Class  cls = Class.forName(metodo.substring(0, metodo.lastIndexOf(".")));
            Method meth = cls.getMethod(met, String[].class);
            final Object[] argsMain = new Object[1];
            argsMain[0] = args;
            meth.invoke(null, argsMain);
        } catch (Exception e) {
            throw criarSistemaException(ERRO_EXECUCAO_PROCESSO + ": \"" + metodo + "\"", e);
        }
    }

    public static Runnable executarUmProcessoQualquerRunnable(final String metodo, Connection con)
            throws SistemaException {
        try {
            Class cls = Class.forName(metodo);
            Constructor constructor = cls.getConstructor(Connection.class);
            return (Runnable) constructor.newInstance(con);
        } catch (Exception e) {
            throw criarSistemaException(ERRO_EXECUCAO_PROCESSO + ": \"" + metodo + "\"", e);
        }
    }

    /**
     * Registra uma tentativa de atualização do Banco de Dados do sistema, ou
     * seja, caso a execução da atualização termine em erro ou em sucesso.
     *
     * @param versao Versão do script de atualização executado.
     * @param script Script de atualização.
     * @param descricao Comentários existentes no script.
     * @param resultado Resultado da execução do script. Erro ou Sucesso.
     * @param mensagem Mensagem.
     * @param stackTrace Pilha de erros gerada no caso de erro ao executar o
     * script de atualização.
     * @param codigoUsuario Código identificador do usuário que invocou a
     * atualização do Banco de Dados.
     * @return numero da versao inserida
     * @throws SistemaException
     * <AUTHOR>
     */
    private String atualizarVersao(Integer versao, String script, String descricao, Resultado resultado, String mensagem, String stackTrace,
            Integer codigoUsuario, boolean classe) throws SistemaException {
        try {
            // Montar SQL para inserir os dados de acordo com as suas disponibilidades            
            StringBuilder sql = new StringBuilder("INSERT INTO bdatualizacao (versao, ");
            if (script != null) {
                sql.append("script, ");
            }
            if (descricao != null) {
                sql.append("descricao, ");
            }
            sql.append("resultado, mensagem, ");
            if (resultado.equals(Resultado.ERRO) && stackTrace != null) {
                sql.append("stacktrace, ");
            }
            sql.append("data, usuario, classe) \n");
            sql.append("VALUES (?, ");
            if (script != null) {
                sql.append("?, ");
            }
            if (descricao != null) {
                sql.append("?, ");
            }
            sql.append("?, ?, ");
            if (resultado.equals(Resultado.ERRO) && stackTrace != null) {
                sql.append("?, ");
            }
            sql.append("?, ?, ?)");

            Declaracao dc = new Declaracao(sql.toString(), this.con);

            int numParam = 0;
            dc.setInt(++numParam, versao);
            if (script != null) {
                dc.setString(++numParam, script);
            }
            if (descricao != null) {
                dc.setString(++numParam, descricao);
            }
            dc.setInt(++numParam, resultado.getCodigo());
            dc.setString(++numParam, mensagem);
            if (resultado.equals(Resultado.ERRO) && stackTrace != null) {
                dc.setString(++numParam, stackTrace);
            }
            dc.setTimestamp(++numParam, new Timestamp(negocio.comuns.utilitarias.Calendario.hoje().getTime()));
            dc.setInt(++numParam, codigoUsuario);
            dc.setBoolean(++numParam, classe);

            // Executar inserção
            dc.execute();

            // Caso a atualização tenha sido executada com sucesso:
            if (resultado.equals(Resultado.SUCESSO)) {
                // Atualizar a versão atual do Banco de Dados do sistema
                dc = new Declaracao("UPDATE bdversaoatual SET versao = ?", this.con);
                dc.setInt(1, versao);
                dc.execute();

            }
            return script;
        } catch (SQLException e) {
            throw criarSistemaException(ERRO_EXECUCAO_SQL, e);
        }
    }

    /**
     * Cria exceção própria do sistema com a mensagem especificada e a exceção
     * causa.
     *
     * @param mensagem Mensagem da exceção do sistema a ser criada.
     * @param causa Exceção que é causa da geração desta exceção do sistema.
     * @return Exceção do sistema.
     * <AUTHOR>
     */
    private static SistemaException criarSistemaException(String mensagem, Exception causa) {
        String sCausa;
        if (causa instanceof InvocationTargetException) {
            sCausa = ((InvocationTargetException) causa).getTargetException().getMessage();
        } else {
            sCausa = causa.getMessage();
        }
        SistemaException sis = new SistemaException(mensagem + ": " + sCausa);
        sis.setStackTrace(causa.getStackTrace());
        sis.initCause(causa.getCause());
        return sis;
    }

    /**
     * Consulta todas as execuções de atualizações a partir da data
     * especificada.
     *
     * @param horarioAnteriorAtualizacao Data anterior às execuções das
     * atualizações.
     * @return Lista de execuções de atualizações;
     * @throws SQLException
     * <AUTHOR>
     */
    public List<AtualizacaoTO> consultarAtualizacoesDesde(Date horarioAnteriorAtualizacao) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT a.*, u.nome AS nomeusuario \n");
        sql.append("FROM bdatualizacao a \n");
        sql.append("INNER JOIN usuario u ON a.usuario = u.codigo \n");
        sql.append("WHERE data > ? \n");
        sql.append("ORDER BY versao DESC");

        Declaracao dc = new Declaracao(sql.toString(), this.con);
        dc.setTimestamp(1, new Timestamp(horarioAnteriorAtualizacao.getTime()));

        List<AtualizacaoTO> atualizacoes = new ArrayList<AtualizacaoTO>();

        ResultSet result = dc.executeQuery();
        while (result.next()) {
            atualizacoes.add(montarDados(result));
        }

        return atualizacoes;
    }

    /**
     * Consulta todas as execuções de atualizações com base num filtro.
     *
     * @param filtro Filtro da consulta.
     * @return Lista de execuções de atualizações.
     * @throws SQLException
     * <AUTHOR>
     */
    public List<AtualizacaoTO> consultar(AtualizacaoTO filtro) throws SQLException {
        StringBuilder sql = new StringBuilder("SELECT a.*, u.nome AS nomeusuario \n");
        sql.append("FROM bdatualizacao a \n");
        sql.append("INNER JOIN usuario u ON a.usuario = u.codigo \n");
        if (filtro != null) {
            sql.append("WHERE 1 = 1 \n");
            if (filtro.getCodigo() != null && !filtro.getCodigo().equals(0)) {
                sql.append("AND a.codigo = ?");
            }
            if (filtro.getScript() != null) {
                sql.append(" AND a.script = ? ");
            }
            if (filtro.getDataInicial() != null) {
                sql.append("AND a.data >= ? \n");
            }
            if (filtro.getDataFinal() != null) {
                sql.append("AND a.data <= ? \n");
            }
            if (filtro.getResultado() != null) {
                sql.append("AND a.resultado = ? \n");
            }
            if (filtro.getVersao() != null) {
                sql.append("AND a.versao = ? \n");
            }
            if (filtro.getCodigoUsuario() != null && !filtro.getCodigoUsuario().equals(0)) {
                sql.append("AND a.usuario = ? \n");
            }
            if (filtro.getNomeUsuario() != null && !filtro.getNomeUsuario().equals("")) {
                sql.append("AND UPPER(u.nome) LIKE ? \n");
            }
        }
        sql.append("ORDER BY data DESC");

        Declaracao dc = new Declaracao(sql.toString(), this.con);

        if (filtro != null) {
            int numParam = 0;
            if (filtro.getCodigo() != null && !filtro.getCodigo().equals(0)) {
                dc.setInt(++numParam, filtro.getCodigo());
            }
            if (filtro.getScript() != null) {
                dc.setString(++numParam, filtro.getScript());
            }
            if (filtro.getDataInicial() != null) {
                dc.setTimestamp(++numParam, new java.sql.Timestamp(Uteis.getDataComHoraZerada(filtro.getDataInicial()).getTime()));
            }
            if (filtro.getDataFinal() != null) {
                dc.setTimestamp(++numParam, new java.sql.Timestamp(Uteis.getDateTime(filtro.getDataFinal(), 23, 59, 59).getTime()));
            }
            if (filtro.getResultado() != null) {
                dc.setInt(++numParam, filtro.getResultado().getCodigo());
            }
            if (filtro.getVersao() != null) {
                dc.setInt(++numParam, filtro.getVersao());
            }
            if (filtro.getCodigoUsuario() != null && !filtro.getCodigoUsuario().equals(0)) {
                dc.setInt(++numParam, filtro.getCodigoUsuario());
            }
            if (filtro.getNomeUsuario() != null && !filtro.getNomeUsuario().equals("")) {
                dc.setString(++numParam, filtro.getNomeUsuario().toUpperCase() + "%");
            }
        }

        List<AtualizacaoTO> atualizacoes = new ArrayList<AtualizacaoTO>();

        ResultSet result = dc.executeQuery();
        while (result.next()) {
            atualizacoes.add(montarDados(result));
        }

        return atualizacoes;
    }

    /**
     * Monta os dados de uma execução de atualização.
     *
     * @param resultSet
     * @return AtualizacaoTO
     * @throws SQLException
     * <AUTHOR>
     */
    private AtualizacaoTO montarDados(ResultSet resultSet) throws SQLException {
        AtualizacaoTO atualizacao = new AtualizacaoTO();
        atualizacao.setCodigo(resultSet.getInt("codigo"));
        atualizacao.setData(resultSet.getTimestamp("data"));
        atualizacao.setDescricao(resultSet.getString("descricao"));
        atualizacao.setMensagem(resultSet.getString("mensagem"));
        atualizacao.setResultado(Resultado.getResultado(resultSet.getInt("resultado")));
        atualizacao.setScript(resultSet.getString("script"));
        atualizacao.setStackTrace(resultSet.getString("stacktrace"));
        atualizacao.setVersao(resultSet.getInt("versao"));
        atualizacao.setCodigoUsuario(resultSet.getInt("usuario"));
        atualizacao.setNomeUsuario(resultSet.getString("nomeusuario"));
        return atualizacao;
    }

    public String consultarJSON() throws Exception {
        String sql = "SELECT cidade.codigo, cidade.nome, estado.descricao as estado, pais.nome as pais\n"
                + "FROM cidade\n"
                + "left join estado on cidade.estado = estado.codigo\n"
                + "left join pais on cidade.pais = pais.codigo;";

        PreparedStatement sqlConsultar = con.prepareStatement(sql);

        ResultSet rs = sqlConsultar.executeQuery();

        StringBuilder json = new StringBuilder();
        json.append("{\"aaData\":[");
        boolean dados = false;
        while (rs.next()) {
            dados = true;
            json.append("[\"").append(rs.getString("codigo")).append("\",");
            json.append("\"").append(rs.getString("nome")).append("\",");
            json.append("\"").append(rs.getString("estado")).append("\",");
            json.append("\"").append(rs.getString("pais")).append("\"],");
        }
        if (dados) {
            json.deleteCharAt(json.toString().length() - 1);
        }
        json.append("]}");
        return json.toString();
    }

    private LocalDate extractDateFromAnnotation(Class<?> clazz) {
        ClasseProcesso annotation = clazz.getAnnotation(ClasseProcesso.class);
        return LocalDate.parse(
                annotation.data(),
                DateTimeFormatter.ofPattern("dd/MM/yyyy")
        );
    }

    public static void main(String... args) throws Exception {
        Uteis.debug = true;
        String chave = args.length > 0 ? args[0] : "kwpr";
        try{
            Connection c = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, c);
            SuperControle superControle = new SuperControle();
            AtualizadorBD atualizador = new AtualizadorBD(c);
            atualizador.atualizar(superControle.getVersaoBD(), 1);
        } catch(Exception e){
            e.printStackTrace(System.out);
        }
    }
    
}
