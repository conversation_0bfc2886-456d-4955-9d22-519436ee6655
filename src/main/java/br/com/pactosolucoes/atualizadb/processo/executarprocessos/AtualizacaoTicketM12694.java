package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "06/09/2024",
        descricao = "Recuperar registros de vínculo com Treino Web que foram excluídos pelo Robo",
        motivacao = "M1-2694")
public class AtualizacaoTicketM12694 implements MigracaoVersaoInterface{


    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)){
            Cliente clienteDAO = new Cliente(c);
            ZillyonWebFacade zwFacadeDAO = new ZillyonWebFacade(c);

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT\n");
            sb.append("'DELETE FROM historicovinculo WHERE codigo = ' || htv.codigo || ';");
            sb.append("INSERT INTO vinculo (colaborador, tipovinculo, cliente) VALUES (' || htv.colaborador || ', ''TW'', ' ||cli.codigo || ');' AS sqlCorrecao,\n");
            sb.append("cli.codigo as codCliente\n");
            sb.append("FROM historicovinculo htv\n");
            sb.append("INNER JOIN cliente cli ON cli.codigo = htv.cliente\n");
            sb.append("INNER JOIN contrato con ON con.codigo = (SELECT MAX(codigo) FROM contrato WHERE pessoa = cli.pessoa)\n");
            sb.append("INNER JOIN historicocontrato histc ON histc.contrato = con.codigo\n");
            sb.append("INNER JOIN empresa emp ON emp.codigo = con.empresa\n");
            sb.append("WHERE 1 = 1\n");
            sb.append("AND usuarioresponsavel = 1\n");
            sb.append("AND origem ILIKE 'PROCESSAMENTO DIÁRIO'\n");
            sb.append("AND tipohistorico IN ('DE')\n");
            sb.append("AND emp.removervinculosaposdesistencia\n");
            sb.append("AND emp.nrdiasdesistenteremovervinculotreino > 0\n");
            sb.append("AND emp.ativa\n");
            sb.append("AND htv.dataregistro - histc.datainiciosituacao < (emp.nrdiasdesistenteremovervinculotreino || ' days')::interval\n");
            sb.append("AND htv.dataregistro > NOW () - INTERVAL '3 months'");
            sb.append("ORDER BY htv.dataregistro DESC\n");

            ResultSet rs = SuperFacadeJDBC.criarConsulta(sb.toString(), c);
            while (rs.next()) {
                String sqlCorrecao = rs.getString("sqlCorrecao");
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlCorrecao, c);
                ClienteVO clienteVO = clienteDAO.consultarPorCodigo(rs.getInt("codCliente"), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                zwFacadeDAO.atualizarSintetico(clienteVO, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);
            }
            clienteDAO = null;
            zwFacadeDAO = null;
        }
    }

}
