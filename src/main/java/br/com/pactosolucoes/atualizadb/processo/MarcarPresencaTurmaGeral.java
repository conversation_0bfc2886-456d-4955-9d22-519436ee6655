package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class MarcarPresencaTurmaGeral {

    public static void main(String... args) {
        System.out.println("Processo iniciado...\n");

        try (Connection con = DriverManager.getConnection("**********************************************************************************", "postgres", "pactodb");) {
            ResultSet alunosAtivos = consultarAlunosAtivos(con);
            while (alunosAtivos.next()) {
                System.out.println("Codigo: " + alunosAtivos.getInt("codigo") + " - Pessoa: " + alunosAtivos.getInt("pessoa") + " - Situação: " + alunosAtivos.getString("situacao"));
                consultarTurmasAluno(con, alunosAtivos.getInt("pessoa"));
                System.out.println("\n*************************************************************************************************\n");
            }
            System.out.println("\nTodas presenças verificadas e atualizadas!");
        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    public static ResultSet consultarAlunosAtivos(Connection con) throws Exception {
        ResultSet consultarAlunosAtivos = SuperFacadeJDBC.criarConsulta("select * from cliente where situacao = 'AT'", con);
        return consultarAlunosAtivos;
    }

    public static void consultarTurmasAluno(Connection con, Integer codPessoa) throws Exception {
        ResultSet consultarTurmasAluno = SuperFacadeJDBC.criarConsulta(
                "select m.codigo, m.pessoa, m.datainicio, m.datafim, m.horarioturma, h.diasemana " +
                    "from matriculaalunohorarioturma m " +
                    "inner join horarioturma h on h.codigo = m.horarioturma " +
                    "where pessoa = " + codPessoa,
                con
        );
        while (consultarTurmasAluno.next()) {
            Date inicio = consultarTurmasAluno.getDate("datainicio");
            Date fim = consultarTurmasAluno.getDate("datafim");
            String diaSemana = transformarDiaSemana(consultarTurmasAluno.getString("diasemana"));

            System.out.println("-> Dados maht: " + consultarTurmasAluno.getInt("codigo") +
                    " - início maht: " + inicio +
                    " - fim maht: " + fim +
                    " - pessoa: " + consultarTurmasAluno.getInt("pessoa") +
                    " - dia da semana: " + diaSemana);

            List<Date> dias = Uteis.getDiasEntreDatas(inicio, fim);

            for (int i = 0; i < dias.size(); i++) {
                if (Uteis.retornaDescricaoDiaSemana(toCalendar(dias.get(i))).equals(diaSemana)) {
                    System.out.println("....... Dia: " + dias.get(i) + existePresenca(con, consultarTurmasAluno.getInt("codigo"), dias.get(i)));
                }
            }
        }
    }

    public static String existePresenca(Connection con, Integer codMatriculaAlunoHorarioTurma, Date data ) throws Exception {
        ResultSet presenca = SuperFacadeJDBC.criarConsulta(
                "select count(codigo) " +
                    "from presenca p " +
                    "where dadosturma = " + codMatriculaAlunoHorarioTurma +
                    "and datapresenca = '" + data + "'",
                con
        );

        // 28/02/2021 até 31/05/2021 período para lançamento das presenças
        Date inicioPeriodo = new SimpleDateFormat("yyyy-MM-dd").parse("2021-02-28");
        Date fimPeriodo    = new SimpleDateFormat("yyyy-MM-dd").parse("2021-05-31");

        while (presenca.next()) {
            if (presenca.getInt("count") == 0 && (inicioPeriodo.compareTo(data) * data.compareTo(fimPeriodo) >= 0)) {
                PreparedStatement stm = con.prepareStatement("insert into presenca (dadosturma, datachamada, datapresenca, datacadastro) values (?, ?, ?, ?)");
                stm.setInt(1, codMatriculaAlunoHorarioTurma);
                stm.setDate(2, Uteis.getDataJDBC(new Date()));
                stm.setDate(3, Uteis.getDataJDBC(data));
                stm.setDate(4, Uteis.getDataJDBC(new Date()));
                stm.execute();
                return " " + presenca.getInt("count") + " presenças, presença lançada com sucesso!";
            }
            return " " + presenca.getInt("count") + " presença";
        }
        return " 0 presenças";
    }

    public static String transformarDiaSemana(String diaSemana) {
        if (diaSemana.equals("DM")) {
            return "Domingo";
        } else if (diaSemana.equals("SG")) {
            return "Segunda";
        } else if (diaSemana.equals("TR")) {
            return "Terça";
        } else if (diaSemana.equals("QA")) {
            return "Quarta";
        } else if (diaSemana.equals("QI")) {
            return "Quinta";
        } else if (diaSemana.equals("SX")) {
            return "Sexta";
        } else if (diaSemana.equals("SB")) {
            return "Sábado";
        } else {
            return "";
        }
    }

    public static Calendar toCalendar(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal;
    }

}
