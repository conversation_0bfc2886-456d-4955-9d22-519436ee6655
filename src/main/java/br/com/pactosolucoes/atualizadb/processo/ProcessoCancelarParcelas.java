package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.MovParcelaControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class ProcessoCancelarParcelas {

    private final Connection con;
    private final boolean simular;

    public ProcessoCancelarParcelas(String chave,boolean simular) throws Exception {
        this.simular = simular;
        this.con = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(con);
    }

    public static void main(String[] args) throws Exception {

        boolean simular = false;
        String chave = "";
        String codigosParcelas = "";
        String justificativa = "";
        String userName = "";

        ProcessoCancelarParcelas processo = new ProcessoCancelarParcelas(chave, simular);
        processo.cancelarParcelas(codigosParcelas, userName, justificativa);
    }


    public void cancelarParcelas(String codigosParcelas, String userName, String justificativa) throws Exception {
        if (UteisValidacao.emptyString(codigosParcelas) || codigosParcelas.split(",").length == 0) {
            throw new Exception("Os códigos das parcelas não foram informados!");
        }
        if (UteisValidacao.emptyString(justificativa)) {
            throw new Exception("A justificativa não foi informada!");
        }

        if (UteisValidacao.emptyString(userName)) {
            throw new Exception("O username do usuário responsável pela operação não foi informado!");
        }

        Usuario usuarioDAO = new Usuario(con);
        UsuarioVO   usuarioResponsavel = usuarioDAO.consultarPorUsername(userName, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (usuarioResponsavel == null || UteisValidacao.emptyNumber(usuarioResponsavel.getCodigo())) {
            throw new Exception("Nenhum usuário encontrado para o username: " + userName);
        }
        usuarioDAO = null;

        MovParcela movParcelaDAO = new MovParcela(con);

        for (String codigoParcela : codigosParcelas.split(",")) {
            try {
                Integer codParcela = Integer.parseInt(codigoParcela);

                MovParcelaVO movParcelaVO = movParcelaDAO.consultarPorChavePrimaria(codParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (!movParcelaVO.getSituacao().equals("EA")) {
                    continue;
                }
                List<MovParcelaVO> parcelasCancelar = new ArrayList<>();
                parcelasCancelar.add(movParcelaVO);
                if (!simular) {
                    MovParcelaControle movParcelaControle = new MovParcelaControle(true);
                    movParcelaControle.setListaParcelasPagar(parcelasCancelar);
                    movParcelaControle.setJustificativaCancelamento(justificativa);
                    movParcelaControle.cancelarParcelas(usuarioResponsavel);
                    movParcelaControle = null;
                }

                System.out.println(String.format("%sCancelei a parcela: %d %s %s %s %s", simular ? "[MODO SIMULAÇÃO] " : "", movParcelaVO.getCodigo(), movParcelaVO.getDescricao(),
                        Calendario.getDataAplicandoFormatacao(movParcelaVO.getDataVencimento(), "dd/MM/yyyy"), movParcelaVO.getValorParcela_Apresentar(), movParcelaVO.getSituacao()));
            } catch (Exception e) {
                Uteis.logar("\tErro ao cancelar a parcela: " + codigoParcela + " - " + e.getMessage());
            }
        }
    }

}
