package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoAjustarParcelasZeradasEmAberto;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "09/12/2024",
        descricao = "Gerar pagamento para parcelas com valor zerado e sem pagamento, que são da nova tela de negociação",
        motivacao = "MJ-406")
public class AtualizacaoTicketMJ406 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            ProcessoAjustarParcelasZeradasEmAberto.ajustarParcelasZeradasEmAbertoProdutoMARERN(c);
        }
    }
}