package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import br.com.pactosolucoes.enumeradores.BIEnum;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import negocio.comuns.basico.ConfiguracaoBIVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.ConfigTotalPass;
import negocio.facade.jdbc.basico.ConfiguracaoBI;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

@ClasseProcesso(autor = "Matheus Cassimiro",
        data = "14/02/2025",
        descricao = "Agregadores no BI Movimentação de Contratos",
        motivacao = "M1-4335")
public class AtualizacaoTicketM14335 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            Empresa empresaDao = new Empresa(c);
            ConfigTotalPass configTotalPassDao = new ConfigTotalPass(c);
            ConfiguracaoBI configuracaoBIDao = new ConfiguracaoBI(c);

            List<EmpresaVO> listaEmpresas = empresaDao.consultarTodas(true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            for (EmpresaVO empresaVO : listaEmpresas) {
                if (!UteisValidacao.emptyString(empresaVO.getCodigoGymPass()) ||
                        !UteisValidacao.emptyString(empresaVO.getTokenApiGymPass()) ||
                        !UteisValidacao.emptyString(empresaVO.getTokenAcademyGoGood()) ||
                        configTotalPassDao.possuiConfiguracaoTotalPassAtiva(empresaVO.getCodigo())) {

                    List<ConfiguracaoBIVO> configuracoes = new ArrayList();
                    ConfiguracaoBIVO configuracao = new ConfiguracaoBIVO();
                    configuracao.setBi(BIEnum.ROTATIVIDADE_CONTRATO);
                    configuracao.setEmpresa(empresaVO.getCodigo());
                    configuracao.setConfiguracao(ConfiguracaoBIEnum.EXIBIR_AGREGADORES);
                    configuracao.setValor("true");
                    configuracoes.add(configuracao);

                    configuracao = new ConfiguracaoBIVO();
                    configuracao.setBi(BIEnum.ROTATIVIDADE_CONTRATO);
                    configuracao.setEmpresa(empresaVO.getCodigo());
                    configuracao.setConfiguracao(ConfiguracaoBIEnum.QTD_CHECKIN_CONSIDERAR_AGREGADOR);
                    configuracao.setValor("1");
                    configuracoes.add(configuracao);

                    configuracaoBIDao.incluirPorBI(BIEnum.ROTATIVIDADE_CONTRATO, empresaVO.getCodigo(), configuracoes);
                }
            }
        } catch (Exception e) {
            int a;
            e.printStackTrace();
        }
    }

}
