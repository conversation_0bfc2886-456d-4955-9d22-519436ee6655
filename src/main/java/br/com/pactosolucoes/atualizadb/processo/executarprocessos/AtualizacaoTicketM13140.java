package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Marcos Andre",
        data = "15/11/2024",
        descricao = "Correção nos historico de contratos transferidos por troca de plano",
        motivacao = "M1-3140")
public class AtualizacaoTicketM13140 implements MigracaoVersaoInterface{
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            StringBuilder sql = new StringBuilder();
            SuperFacadeJDBC.executarUpdateExecutarProcessos("update historicocontrato set datainiciosituacao = dataregistro where tipohistorico = 'TE';", c);
        }
    }
}
