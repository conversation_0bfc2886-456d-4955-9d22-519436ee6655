package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Anna Carolina",
        data = "08/07/2024",
        descricao = "Cria coluna ordem na tabela fila de espera",
        motivacao = "Utilizar a coluna ordem para ordenação da fila de espera")
public class CriarColunaOrdemFilaEspera implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE filaesperaturma ADD COLUMN ordem INTEGER;",
                    c
            );
        }
    }
}

