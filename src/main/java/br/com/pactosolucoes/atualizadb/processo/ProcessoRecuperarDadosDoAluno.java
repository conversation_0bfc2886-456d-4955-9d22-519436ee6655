package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.*;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoRecuperarDadosDoAluno {

    public static Connection conection() throws Exception{
        String host = "localhost";
        String porta = "5432";
        String nomeBD = "bdmusccems";
        String urlBD = "jdbc:postgresql://"+ host + ":" + porta + "/" + nomeBD;
        return DriverManager.getConnection(urlBD, "postgres",
                "pactodb");
    }

    public static void main(String[] args) throws Exception{
        Connection con = conection();
        PreparedStatement stm = con.prepareStatement("select sa.complemento, " +
                "sa.repeticao, sa.repeticaocomp, sa.repeticaoapp , s.codigo from serie s \n" +
                "inner join serie_aud sa on sa.codigo = s.codigo and sa.complemento <> ''\n" +
                "where (s.complemento is null or s.complemento = '')\n" +
                "and s.atividadeficha_codigo in (\n" +
                "select a.codigo  from programatreino p \n" +
                "inner join programatreinoficha p2 on p2.programa_codigo = p.codigo \n" +
                "inner join atividadeficha a on a.ficha_codigo = p2.ficha_codigo \n" +
                "where professormontou_codigo = 474\n" +
                "and datainicio > '2023-03-01')");
        ResultSet rs = stm.executeQuery();
        while (rs.next()){
            int codigo = rs.getInt("codigo");

            String complemento = rs.getString("complemento");
            int repeticao = rs.getInt("repeticao");
            String repeticaoComp = rs.getString("repeticaocomp");
            String repeticaoApp = rs.getString("repeticaoapp");

            String update = "update serie set complemento = ?, " +
                    "repeticao = ?, repeticaocomp = ?, " +
                    "repeticaoapp = ? where codigo = ?";
            PreparedStatement stm2 = con.prepareStatement(update);
            stm2.setString(1, complemento);
            stm2.setInt(2, repeticao);
            stm2.setString(3, repeticaoComp);
            stm2.setString(4, repeticaoApp);
            stm2.setInt(5, codigo);
            stm2.executeUpdate();

            System.out.println("atualizada a serie " + codigo);
        }
    }

    public static void processo(String... args) {
        System.out.println("Processo iniciado...\n");

        try (Connection con = DriverManager.getConnection("****************************************************", "postgres", "pactodb");) {
            ResultSet pessoa = consultarPessoa(con);

            while (pessoa.next()) {
                System.out.println("-------INSERINDO PESSOA------");
                inserirPessoa(pessoa, con);
                System.out.println("-------DEU CERTO------");
            }
            ResultSet clienteSintetico = consultarClienteSintetico(con);
            Integer codigoCliente = 0;
            while (clienteSintetico.next()) {
                codigoCliente = clienteSintetico.getInt("codigo");
                System.out.println("-------CLIENTE SINTETICO------");
                inserirCliente(clienteSintetico, con);
                System.out.println("-------DEU CERTO------");
            }
            ResultSet avaliacaoFisica = consultarAvaliacaoFisica(con,codigoCliente);
            while (avaliacaoFisica.next()) {
                System.out.println("-------INSERINDO AVALIACAO FISICA------");
                inserirAvaliacaoFisica(avaliacaoFisica, con);
                System.out.println("-------DEU CERTO------");
            }
            ResultSet programaDeTreino = consultarProgramaDeTreino(con,codigoCliente);
            while (programaDeTreino.next()) {
                System.out.println("-------INSERINDO PROGRAMA DE TREINO------");
                inserirProgramaDeTreino(programaDeTreino, con);
                System.out.println("-------DEU CERTO------");
            }

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static ResultSet consultarPessoa(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("codigo, rev, revtype, datanascimento, fotokey, nome, parq, sexo \n");
        sql.append("FROM pessoa_aud \n");
        sql.append("WHERE codigo=12118  \n");
        sql.append("AND rev=53309; \n");
        ResultSet pessoa = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        return pessoa;
    }

    public static ResultSet consultarProgramaDeTreino(Connection con, Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("codigo, rev, revtype, datainicio, datalancamento, dataproximarevisao, datarenovacao,\n");
        sql.append("dataterminoprevisto, dataultimaatualizacao, diasporsemana,  \n");
        sql.append("nome, nrtreinosrealizados, predefinido, programatreinorenovacao, programatreinorenovado,   \n");
        sql.append("situacao, totalaulasprevistas, treinorapido, versao,  \n");
        sql.append("cliente_codigo, nivel_codigo, professorcarteira_codigo, professormontou_codigo, genero \n");
        sql.append("FROM programatreino_aud \n");
        sql.append("WHERE cliente_codigo = " + codigoCliente +"\n");
        sql.append("order by dataterminoprevisto desc \n");
        sql.append("limit 1 \n");

        ResultSet pessoa = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        return pessoa;
    }

    public static void inserirPessoa(ResultSet pessoa, Connection con) throws Exception {

        String sql = "INSERT INTO pessoa\n" +
                "(codigo, datanascimento, fotokey, nome, parq, sexo)\n" +
                "VALUES(12118, ?, ?, ?, ?, ?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        sqlInserir.setDate(1, pessoa.getDate("datanascimento"));
        sqlInserir.setString(2, pessoa.getString("fotokey"));
        sqlInserir.setString(3, pessoa.getString("nome"));
        sqlInserir.setBoolean(4, pessoa.getBoolean("parq"));
        sqlInserir.setString(5, pessoa.getString("sexo"));

        sqlInserir.execute();;
    }


    public static ResultSet consultarClienteSintetico(Connection con) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT  \n");
        sql.append("codigo, cpf, rg, uf, ativo, bairro, cargo, cidade, codigoacesso, codigocliente, codigocontrato, \n");
        sql.append("codigoexterno, codigopessoa, codigoultimocontatocrm, colaboradores, \n");
        sql.append("crossfit, dadosavaliacao, dataatualizacaofoto, datacadastro, \n");
        sql.append("datafimperiodoacesso, datainicioperiodoacesso, datalancamentocontrato, \n");
        sql.append("datamatricula, datanascimento, datarematriculacontrato, datarenovacaocontrato,  \n");
        sql.append("dataultimarematricula, dataultimobv, dataultimocontatocrm, dataultimoacesso, \n");
        sql.append("datavigenciaate, datavigenciaateajustada, datavigenciade, descricaoduracao, \n");
        sql.append("descricoesmodalidades, dia, diasacessomes2, diasacessomes3, diasacessomes4, \n");
        sql.append("diasacessosemana2, diasacessosemana3, diasacessosemana4, diasacessosemanapassada, \n");
        sql.append("diasacessoultimomes, diasassiduidadeultrematriculaatehoje, diasfaltasemacesso, duracaocontratomeses, \n");
        sql.append("email, empresa, empresausafreepass, endereco, estadocivil, existeparcvencidacontrato, faseatualcrm, \n");
        sql.append("fcmaxima, fcrepouso, fotokeyapp, freepass, frequenciasemanal, gympassuniquetoken, idade, massamagraatual, \n");
        sql.append("massamagrainicio, matricula, mediadiasacesso4meses, mnemonicodocontrato, modalidades, nome, \n");
        sql.append("nomeconsulta, nomeplano, nraulasexperimentais, nrtreinosprevistos, nrtreinosrealizados, objetivos, parq, \n");
        sql.append("percentualgorduraatual, percentualgordurainicio, pesoatual, pesoinicio, pesorisco, profissao, responsavelultimocontatocrm, \n");
        sql.append("saldocontacorrentecliente, saldocreditotreino, sexo, situacao, situacaocontrato, situacaocontratooperacao, situacaomatriculacontrato, \n");
        sql.append("telefones, tipoperiodoacesso, totalcreditotreino, ultimavisita, valorpagocontrato, valorparcabertocontrato, valorfaturadocontrato, versao,  \n");
        sql.append("vezesporsemana, grupo_codigo, nivelaluno_codigo, pessoa_codigo, professorsintetico_codigo\n");
        sql.append("FROM clientesintetico_aud \n");
        sql.append("WHERE matricula = 18327\n");
        sql.append("order by datacadastro desc\n");
        sql.append("limit 1 \n");
        ResultSet consultarClienteSintetico = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        return consultarClienteSintetico;
    }

    public static ResultSet consultarAvaliacaoFisica(Connection con, Integer codigoCliente) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT \n");
        sql.append("codigo, abdominal, altura, antebracodir, antebracoesq, aumentopercentualgordura, axilarmedia, biceps, \n");
        sql.append("bracocontraidodir, bracocontraidoesq, bracorelaxadodir, bracorelaxadoesq, cargaastrand, \n");
        sql.append("categoriaavaliacaoimc, categoriapercentualgordura, cintura, circunferenciaabdominal, \n");
        sql.append("coxadistaldir, coxadistalesq, coxamediadir, coxamediaesq, coxamedial, \n");
        sql.append("coxaproximaldir, coxaproximalesq, dataavaliacao, dataproxima, distancia12,  \n");
        sql.append("ectomorfia, endomorfia, fcastrand, fcmaxima, fcqueens, flexibilidade, gluteo,  \n");
        sql.append("gorduraideal, gorduravisceral, idademetabolica, imc, massagorda, massamagra, mesomorfia,  \n");
        sql.append("metapercentualgordura, metapercentualgorduraanterior, movproduto, necessidadecalorica, necessidadefisica,  \n");
        sql.append("ombro, panturrilha, panturrilhadir, panturrilhaesq, peitoral, percentualagua, percentualgordura,  \n");
        sql.append("percentualmassamagra, pescoco, peso, pesomuscular, pesoosseo, protocolo, protocolovo, punho,  \n");
        sql.append("quadril, reatancia, recomendacoes, residual, resistencia, subescapular, supraespinhal, suprailiaca, \n");
        sql.append("tempo2400, tmb, toraxbusto, totaldobras, totalperimetria, triceps, urlassinatura, venda, vo2astrand,  \n");
        sql.append("vo2max12, vo2max2400, vo2maxastrand, vo2maxqueens, vomaxaerobico, agendamentoreavaliacao_codigo,   \n");
        sql.append("cliente_codigo, responsavellancamento_codigo,  \n");
        sql.append("logbalanca, fcastrand4, fcastrand5 \n");
        sql.append("from avaliacaofisica_aud where cliente_codigo  = "+ codigoCliente + "\n");
        sql.append("order by dataavaliacao desc \n");
        sql.append("limit 1 \n");

        ResultSet consultarClienteSintetico = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        return consultarClienteSintetico;
    }

    public static void inserirAvaliacaoFisica(ResultSet AvaliacaoFisica, Connection con) throws Exception {

        String sql = "INSERT INTO avaliacaofisica" +
                "(abdominal, altura, antebracodir, antebracoesq, aumentopercentualgordura, " +
                "axilarmedia, biceps, bracocontraidodir, bracocontraidoesq, " +
                "bracorelaxadodir, bracorelaxadoesq, cargaastrand, " +
                "categoriaavaliacaoimc, categoriapercentualgordura, " +
                "cintura, circunferenciaabdominal, coxadistaldir, " +
                "coxadistalesq, coxamediadir, coxamediaesq, " +
                "coxamedial, coxaproximaldir, coxaproximalesq, " +
                "dataavaliacao, dataproxima, distancia12, ectomorfia, " +
                "endomorfia, fcastrand, fcmaxima, fcqueens, flexibilidade, " +
                "gluteo, gorduraideal, gorduravisceral, idademetabolica, imc, " +
                "massagorda, massamagra, mesomorfia, metapercentualgordura, metapercentualgorduraanterior, " +
                "movproduto, necessidadecalorica, necessidadefisica, ombro, panturrilha, " +
                "panturrilhadir, panturrilhaesq, peitoral, percentualagua, percentualgordura, " +
                "percentualmassamagra, pescoco, peso, pesomuscular, pesoosseo, protocolo, protocolovo, " +
                "punho, quadril, reatancia, recomendacoes, residual, resistencia, subescapular, supraespinhal," +
                " suprailiaca, tempo2400, tmb, toraxbusto, totaldobras, totalperimetria, triceps, urlassinatura, " +
                "venda, vo2astrand, vo2max12, vo2max2400, vo2maxastrand, vo2maxqueens, vomaxaerobico, agendamentoreavaliacao_codigo, " +
                "cliente_codigo, responsavellancamento_codigo, logbalanca, fcastrand4, fcastrand5)\n" +
                "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                "?, NULL, ?, ?, ?, ?, ?);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        //linha 1
        sqlInserir.setInt(1, AvaliacaoFisica.getInt("abdominal"));
        sqlInserir.setInt(2, AvaliacaoFisica.getInt("altura"));
        sqlInserir.setInt(3, AvaliacaoFisica.getInt("antebracodir"));
        sqlInserir.setInt(4, AvaliacaoFisica.getInt("antebracoesq"));
        sqlInserir.setBoolean(5, AvaliacaoFisica.getBoolean("aumentopercentualgordura"));

        //linha 2
        sqlInserir.setInt(6, AvaliacaoFisica.getInt("axilarmedia"));
        sqlInserir.setInt(7, AvaliacaoFisica.getInt("biceps"));
        sqlInserir.setInt(8, AvaliacaoFisica.getInt("bracocontraidodir"));
        sqlInserir.setInt(9, AvaliacaoFisica.getInt("bracocontraidoesq"));

        //linha 3
        sqlInserir.setInt(10, AvaliacaoFisica.getInt("bracorelaxadodir"));
        sqlInserir.setInt(11, AvaliacaoFisica.getInt("bracorelaxadoesq"));
        sqlInserir.setInt(12, AvaliacaoFisica.getInt("cargaastrand"));

        //linha 4
        sqlInserir.setInt(13, AvaliacaoFisica.getInt("categoriaavaliacaoimc"));
        sqlInserir.setInt(14, AvaliacaoFisica.getInt("categoriapercentualgordura"));

        //linha 5
        sqlInserir.setInt(15, AvaliacaoFisica.getInt("cintura"));
        sqlInserir.setInt(16, AvaliacaoFisica.getInt("circunferenciaabdominal"));
        sqlInserir.setInt(17, AvaliacaoFisica.getInt("coxadistaldir"));

        //linha 6
        sqlInserir.setInt(18, AvaliacaoFisica.getInt("coxadistalesq"));
        sqlInserir.setInt(19, AvaliacaoFisica.getInt("coxamediadir"));
        sqlInserir.setInt(20, AvaliacaoFisica.getInt("coxamediaesq"));

        //linha 7
        sqlInserir.setInt(21, AvaliacaoFisica.getInt("coxamedial"));
        sqlInserir.setInt(22, AvaliacaoFisica.getInt("coxaproximaldir"));
        sqlInserir.setInt(23, AvaliacaoFisica.getInt("coxaproximalesq"));

        //linha 8
        sqlInserir.setDate(24, (AvaliacaoFisica.getDate("dataavaliacao") != null? AvaliacaoFisica.getDate("dataavaliacao"): Date.valueOf(Uteis.getDataAtual())));
        sqlInserir.setDate(25, (AvaliacaoFisica.getDate("dataproxima") != null? AvaliacaoFisica.getDate("dataproxima"): Date.valueOf(Uteis.getDataAtual())));
        sqlInserir.setInt(26, AvaliacaoFisica.getInt("distancia12"));
        sqlInserir.setInt(27, AvaliacaoFisica.getInt("ectomorfia"));

        //linha 9
        sqlInserir.setInt(28, AvaliacaoFisica.getInt("endomorfia"));
        sqlInserir.setInt(29, AvaliacaoFisica.getInt("fcastrand"));
        sqlInserir.setInt(30, AvaliacaoFisica.getInt("fcmaxima"));
        sqlInserir.setInt(31, AvaliacaoFisica.getInt("fcqueens"));
        sqlInserir.setInt(32, AvaliacaoFisica.getInt("flexibilidade"));

        //linha 10
        sqlInserir.setInt(33, AvaliacaoFisica.getInt("gluteo"));
        sqlInserir.setInt(34, AvaliacaoFisica.getInt("gorduraideal"));
        sqlInserir.setInt(35, AvaliacaoFisica.getInt("gorduravisceral"));
        sqlInserir.setInt(36, AvaliacaoFisica.getInt("idademetabolica"));
        sqlInserir.setInt(37, AvaliacaoFisica.getInt("imc"));

        //linha 11
        sqlInserir.setInt(38, AvaliacaoFisica.getInt("massagorda"));
        sqlInserir.setInt(39, AvaliacaoFisica.getInt("massamagra"));
        sqlInserir.setInt(40, AvaliacaoFisica.getInt("mesomorfia"));
        sqlInserir.setInt(41, AvaliacaoFisica.getInt("metapercentualgordura"));
        sqlInserir.setInt(42, AvaliacaoFisica.getInt("metapercentualgorduraanterior"));

        //linha 12
        sqlInserir.setInt(43, AvaliacaoFisica.getInt("movproduto"));
        sqlInserir.setInt(44, AvaliacaoFisica.getInt("necessidadecalorica"));
        sqlInserir.setInt(45, AvaliacaoFisica.getInt("necessidadefisica"));
        sqlInserir.setInt(46, AvaliacaoFisica.getInt("ombro"));
        sqlInserir.setInt(47, AvaliacaoFisica.getInt("panturrilha"));

        //linha 13
        sqlInserir.setInt(48, AvaliacaoFisica.getInt("panturrilhadir"));
        sqlInserir.setInt(49, AvaliacaoFisica.getInt("panturrilhaesq"));
        sqlInserir.setInt(50, AvaliacaoFisica.getInt("peitoral"));
        sqlInserir.setInt(51, AvaliacaoFisica.getInt("percentualagua"));
        sqlInserir.setInt(52, AvaliacaoFisica.getInt("percentualgordura"));

        sqlInserir.setInt(53, AvaliacaoFisica.getInt("percentualmassamagra"));
        sqlInserir.setInt(54, AvaliacaoFisica.getInt("pescoco"));
        sqlInserir.setInt(55, AvaliacaoFisica.getInt("peso"));
        sqlInserir.setInt(56, AvaliacaoFisica.getInt("pesomuscular"));
        sqlInserir.setInt(57, AvaliacaoFisica.getInt("pesoosseo"));
        sqlInserir.setInt(58, AvaliacaoFisica.getInt("protocolo"));
        sqlInserir.setInt(59, AvaliacaoFisica.getInt("protocolovo"));
        sqlInserir.setInt(60, AvaliacaoFisica.getInt("punho"));
        sqlInserir.setInt(61, AvaliacaoFisica.getInt("quadril"));
        sqlInserir.setInt(62, AvaliacaoFisica.getInt("reatancia"));
        sqlInserir.setInt(63, AvaliacaoFisica.getInt("recomendacoes"));
        sqlInserir.setInt(64, AvaliacaoFisica.getInt("residual"));
        sqlInserir.setInt(65, AvaliacaoFisica.getInt("resistencia"));
        sqlInserir.setBigDecimal(66, AvaliacaoFisica.getBigDecimal("subescapular"));
        sqlInserir.setBigDecimal(67, AvaliacaoFisica.getBigDecimal("supraespinhal"));
        sqlInserir.setBigDecimal(68, AvaliacaoFisica.getBigDecimal("suprailiaca"));
        sqlInserir.setString(69, AvaliacaoFisica.getString("tempo2400"));
        sqlInserir.setInt(70, AvaliacaoFisica.getInt("tmb"));
        sqlInserir.setInt(71, AvaliacaoFisica.getInt("toraxbusto"));
        sqlInserir.setInt(72, AvaliacaoFisica.getInt("totaldobras"));
        sqlInserir.setInt(73, AvaliacaoFisica.getInt("totalperimetria"));
        sqlInserir.setInt(74, AvaliacaoFisica.getInt("triceps"));
        sqlInserir.setInt(75, AvaliacaoFisica.getInt("urlassinatura"));
        sqlInserir.setInt(76, AvaliacaoFisica.getInt("venda"));
        sqlInserir.setInt(77, AvaliacaoFisica.getInt("vo2astrand"));
        sqlInserir.setInt(78, AvaliacaoFisica.getInt("vo2max12"));
        sqlInserir.setInt(79, AvaliacaoFisica.getInt("vo2max2400"));
        sqlInserir.setInt(80, AvaliacaoFisica.getInt("vo2maxastrand"));
        sqlInserir.setInt(81, AvaliacaoFisica.getInt("vo2maxqueens"));
        sqlInserir.setInt(82, AvaliacaoFisica.getInt("vomaxaerobico"));
        sqlInserir.setInt(83, AvaliacaoFisica.getInt("cliente_codigo"));
        sqlInserir.setInt(84, AvaliacaoFisica.getInt("responsavellancamento_codigo"));
        sqlInserir.setInt(85, AvaliacaoFisica.getInt("logbalanca"));
        sqlInserir.setInt(86, AvaliacaoFisica.getInt("fcastrand4"));
        sqlInserir.setInt(87, AvaliacaoFisica.getInt("fcastrand5"));

        sqlInserir.execute();;

    }

    public static void inserirCliente(ResultSet clienteSintetico, Connection con) throws Exception {

        String sql = "INSERT INTO clientesintetico\n" +
                "(codigo, cpf, rg, uf, ativo, bairro, cargo, cidade, codigoacesso, \n" +
                "codigocliente, codigocontrato, codigoexterno, codigopessoa, codigoultimocontatocrm, \n" +
                "colaboradores, crossfit, dadosavaliacao, dataatualizacaofoto, datacadastro, \n" +
                "datafimperiodoacesso, datainicioperiodoacesso, datalancamentocontrato, datamatricula, \n" +
                "datanascimento, datarematriculacontrato, datarenovacaocontrato, dataultimarematricula, \n" +
                "dataultimobv, dataultimocontatocrm, dataultimoacesso, datavigenciaate, datavigenciaateajustada, \n" +
                "datavigenciade, descricaoduracao, descricoesmodalidades, dia, diasacessomes2, diasacessomes3, \n" +
                "diasacessomes4, diasacessosemana2, diasacessosemana3, diasacessosemana4, diasacessosemanapassada,\n" +
                "diasacessoultimomes, diasassiduidadeultrematriculaatehoje, diasfaltasemacesso, duracaocontratomeses,\n" +
                "email, empresa, empresausafreepass, endereco, estadocivil, existeparcvencidacontrato, faseatualcrm,\n" +
                "fcmaxima, fcrepouso, fotokeyapp, freepass, frequenciasemanal, gympassuniquetoken, idade, massamagraatual, \n" +
                "massamagrainicio, matricula, mediadiasacesso4meses, mnemonicodocontrato, modalidades, nome, nomeconsulta, nomeplano, \n" +
                "nraulasexperimentais, nrtreinosprevistos, nrtreinosrealizados, objetivos, parq, percentualgorduraatual, percentualgordurainicio, \n" +
                "pesoatual, pesoinicio, pesorisco, profissao, responsavelultimocontatocrm, saldocontacorrentecliente, saldocreditotreino,\n" +
                "sexo, situacao, situacaocontrato, situacaocontratooperacao, situacaomatriculacontrato, telefones, tipoperiodoacesso, \n" +
                "totalcreditotreino, ultimavisita, valorpagocontrato, valorparcabertocontrato, valorfaturadocontrato, versao,\n" +
                "vezesporsemana, grupo_codigo, nivelaluno_codigo, pessoa_codigo, professorsintetico_codigo)\n" +
                "VALUES(12112, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n" +
                " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n" +
                " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NULL, ?, NULL);";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        //linha 1
        sqlInserir.setString(1, clienteSintetico.getString("cpf"));
        sqlInserir.setString(2, clienteSintetico.getString("rg"));
        sqlInserir.setString(3, clienteSintetico.getString("uf"));
        sqlInserir.setBoolean(4, clienteSintetico.getBoolean("ativo"));
        sqlInserir.setString(5, clienteSintetico.getString("bairro"));
        sqlInserir.setString(6, clienteSintetico.getString("cargo"));
        sqlInserir.setString(7, clienteSintetico.getString("cidade"));
        sqlInserir.setInt(8, clienteSintetico.getInt("codigoacesso"));

        //linha 2
        sqlInserir.setInt(9, clienteSintetico.getInt("codigocliente"));
        sqlInserir.setInt(10, clienteSintetico.getInt("codigocontrato"));
        sqlInserir.setString(11, clienteSintetico.getString("codigoexterno"));
        sqlInserir.setInt(12, clienteSintetico.getInt("codigopessoa"));
        sqlInserir.setInt(13, clienteSintetico.getInt("codigoultimocontatocrm"));

        //linha 3
        sqlInserir.setString(14, clienteSintetico.getString("colaboradores"));
        sqlInserir.setBoolean(15, clienteSintetico.getBoolean("crossfit"));
        sqlInserir.setInt(16, clienteSintetico.getInt("dadosavaliacao"));
        sqlInserir.setDate(17, clienteSintetico.getDate("dataatualizacaofoto"));
        sqlInserir.setDate(18, clienteSintetico.getDate("datacadastro"));

        //linha 4
        sqlInserir.setDate(19, clienteSintetico.getDate("datafimperiodoacesso"));
        sqlInserir.setDate(20, clienteSintetico.getDate("datainicioperiodoacesso"));
        sqlInserir.setDate(21, clienteSintetico.getDate("datalancamentocontrato"));
        sqlInserir.setDate(22, clienteSintetico.getDate("datamatricula"));

        //linha 5
        sqlInserir.setDate(23, clienteSintetico.getDate("datanascimento"));
        sqlInserir.setDate(24, clienteSintetico.getDate("datarematriculacontrato"));
        sqlInserir.setDate(25, clienteSintetico.getDate("datarenovacaocontrato"));
        sqlInserir.setDate(26, clienteSintetico.getDate("dataultimarematricula"));

        //linha 6
        sqlInserir.setDate(27, clienteSintetico.getDate("dataultimobv"));
        sqlInserir.setDate(28, clienteSintetico.getDate("dataultimocontatocrm"));
        sqlInserir.setDate(29, clienteSintetico.getDate("dataultimoacesso"));
        sqlInserir.setDate(30, clienteSintetico.getDate("datavigenciaate"));
        sqlInserir.setDate(31, clienteSintetico.getDate("datavigenciaateajustada"));

        //linha 7
        sqlInserir.setDate(32, clienteSintetico.getDate("datavigenciade"));
        sqlInserir.setString(33, clienteSintetico.getString("descricaoduracao"));
        sqlInserir.setString(34, clienteSintetico.getString("descricoesmodalidades"));
        sqlInserir.setDate(35, clienteSintetico.getDate("dia"));
        sqlInserir.setInt(36, clienteSintetico.getInt("diasacessomes2"));
        sqlInserir.setInt(37, clienteSintetico.getInt("diasacessomes3"));

        //linha 8
        sqlInserir.setInt(38, clienteSintetico.getInt("diasacessomes4"));
        sqlInserir.setInt(39, clienteSintetico.getInt("diasacessosemana2"));
        sqlInserir.setInt(40, clienteSintetico.getInt("diasacessosemana3"));
        sqlInserir.setInt(41, clienteSintetico.getInt("diasacessosemana4"));
        sqlInserir.setInt(42, clienteSintetico.getInt("diasacessosemanapassada"));


        //linha 9
        sqlInserir.setInt(43, clienteSintetico.getInt("diasacessoultimomes"));
        sqlInserir.setInt(44, clienteSintetico.getInt("diasassiduidadeultrematriculaatehoje"));
        sqlInserir.setInt(45, clienteSintetico.getInt("diasfaltasemacesso"));
        sqlInserir.setInt(46, clienteSintetico.getInt("duracaocontratomeses"));

        //linha 10
        sqlInserir.setString(47, clienteSintetico.getString("email"));
        sqlInserir.setInt(48, clienteSintetico.getInt("empresa"));
        sqlInserir.setBoolean(49, clienteSintetico.getBoolean("empresausafreepass"));
        sqlInserir.setString(50, clienteSintetico.getString("endereco"));
        sqlInserir.setString(51, clienteSintetico.getString("estadocivil"));
        sqlInserir.setBoolean(52, clienteSintetico.getBoolean("existeparcvencidacontrato"));
        sqlInserir.setString(53, clienteSintetico.getString("faseatualcrm"));

        //linha 11
        sqlInserir.setInt(54, clienteSintetico.getInt("fcmaxima"));
        sqlInserir.setInt(55, clienteSintetico.getInt("fcrepouso"));
        sqlInserir.setString(56, clienteSintetico.getString("fotokeyapp"));
        sqlInserir.setBoolean(57, clienteSintetico.getBoolean("freepass"));
        sqlInserir.setInt(58, clienteSintetico.getInt("frequenciasemanal"));
        sqlInserir.setString(59, clienteSintetico.getString("gympassuniquetoken"));
        sqlInserir.setInt(60, clienteSintetico.getInt("idade"));
        sqlInserir.setBigDecimal(61, clienteSintetico.getBigDecimal("massamagraatual"));

        //linha 12
        sqlInserir.setBigDecimal(62, clienteSintetico.getBigDecimal("massamagrainicio"));
        sqlInserir.setInt(63, clienteSintetico.getInt("matricula"));
        sqlInserir.setInt(64, clienteSintetico.getInt("mediadiasacesso4meses"));
        sqlInserir.setString(65, clienteSintetico.getString("mnemonicodocontrato"));
        sqlInserir.setString(66, clienteSintetico.getString("modalidades"));
        sqlInserir.setString(67, clienteSintetico.getString("nome"));
        sqlInserir.setString(68, clienteSintetico.getString("nomeconsulta"));
        sqlInserir.setString(69, clienteSintetico.getString("nomeplano"));

        //linha 13
        sqlInserir.setBigDecimal(70, clienteSintetico.getBigDecimal("nraulasexperimentais"));
        sqlInserir.setBigDecimal(71, clienteSintetico.getBigDecimal("nrtreinosprevistos"));
        sqlInserir.setInt(72, clienteSintetico.getInt("nrtreinosrealizados"));
        sqlInserir.setString(73, clienteSintetico.getString("objetivos"));
        sqlInserir.setBoolean(74, clienteSintetico.getBoolean("parq"));
        sqlInserir.setBigDecimal(75, clienteSintetico.getBigDecimal("percentualgorduraatual"));
        sqlInserir.setBigDecimal(76, clienteSintetico.getBigDecimal("percentualgordurainicio"));

        //linha 14
        sqlInserir.setDouble(77, clienteSintetico.getDouble("pesoatual"));
        sqlInserir.setDouble(78, clienteSintetico.getDouble("pesoinicio"));
        sqlInserir.setDouble(79, clienteSintetico.getDouble("pesorisco"));
        sqlInserir.setString(80, clienteSintetico.getString("profissao"));
        sqlInserir.setString(81, clienteSintetico.getString("responsavelultimocontatocrm"));
        sqlInserir.setDouble(82, clienteSintetico.getDouble("saldocontacorrentecliente"));
        sqlInserir.setDouble(83, clienteSintetico.getDouble("saldocreditotreino"));

        //linha 15
        sqlInserir.setString(84, clienteSintetico.getString("sexo"));
        sqlInserir.setString(85, clienteSintetico.getString("situacao"));
        sqlInserir.setString(86, clienteSintetico.getString("situacaocontrato"));
        sqlInserir.setString(87, clienteSintetico.getString("situacaocontratooperacao"));
        sqlInserir.setString(88, clienteSintetico.getString("situacaomatriculacontrato"));
        sqlInserir.setString(89, clienteSintetico.getString("telefones"));
        sqlInserir.setString(90, clienteSintetico.getString("tipoperiodoacesso"));

        //linha 16
        sqlInserir.setInt(91, clienteSintetico.getInt("totalcreditotreino"));
        sqlInserir.setDate(92, clienteSintetico.getDate("ultimavisita"));
        sqlInserir.setBigDecimal(93, clienteSintetico.getBigDecimal("valorpagocontrato"));
        sqlInserir.setBigDecimal(94, clienteSintetico.getBigDecimal("valorparcabertocontrato"));
        sqlInserir.setBigDecimal(95, clienteSintetico.getBigDecimal("valorfaturadocontrato"));
        sqlInserir.setInt(96, clienteSintetico.getInt("versao"));

        //linha 17
        sqlInserir.setInt(97, clienteSintetico.getInt("vezesporsemana"));
        sqlInserir.setInt(98, clienteSintetico.getInt("pessoa_codigo"));

        sqlInserir.execute();;

    }

    public static void inserirProgramaDeTreino(ResultSet programaDeTreino, Connection con) throws Exception {

        String sql = "INSERT INTO programatreino \n" +
                "(datainicio, datalancamento, dataproximarevisao, datarenovacao, \n" +
                "dataterminoprevisto, dataultimaatualizacao, diasporsemana, nome, nrtreinosrealizados, \n" +
                "predefinido, programatreinorenovacao, programatreinorenovado, situacao, totalaulasprevistas,\n" +
                "treinorapido, versao, cliente_codigo, nivel_codigo, professorcarteira_codigo, professormontou_codigo, genero)\n" +
                "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 12112, NULL, NULL, ?, ?);\n";

        PreparedStatement sqlInserir = con.prepareStatement(sql);
        //linha 1
        sqlInserir.setDate(1, programaDeTreino.getDate("datainicio"));
        sqlInserir.setDate(2, programaDeTreino.getDate("datalancamento"));
        sqlInserir.setDate(3, programaDeTreino.getDate("dataproximarevisao"));
        sqlInserir.setDate(4, programaDeTreino.getDate("datarenovacao"));

        //linha 2
        sqlInserir.setDate(5, programaDeTreino.getDate("dataterminoprevisto"));
        sqlInserir.setDate(6, programaDeTreino.getDate("dataultimaatualizacao"));
        sqlInserir.setInt(7, programaDeTreino.getInt("diasporsemana"));
        sqlInserir.setString(8, programaDeTreino.getString("nome"));
        sqlInserir.setInt(9, programaDeTreino.getInt("nrtreinosrealizados"));

        //linha 3
        sqlInserir.setBoolean(10, programaDeTreino.getBoolean("predefinido"));
        sqlInserir.setInt(11, programaDeTreino.getInt("programatreinorenovacao"));
        sqlInserir.setInt(12, programaDeTreino.getInt("programatreinorenovado"));
        sqlInserir.setInt(13, programaDeTreino.getInt("situacao"));
        sqlInserir.setInt(14, programaDeTreino.getInt("totalaulasprevistas"));

        //linha 4
        sqlInserir.setBoolean(15, programaDeTreino.getBoolean("treinorapido"));
        sqlInserir.setInt(16, programaDeTreino.getInt("versao"));
        sqlInserir.setInt(17, programaDeTreino.getInt("professormontou_codigo"));
        sqlInserir.setString(18, programaDeTreino.getString("genero"));

        sqlInserir.execute();;

    }

}
