
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Gaeque Luan",
        data = "30/06/2025",
        descricao = "Add coluna body na tabela detalhesrequestenviada",
        motivacao = "PAY-1064 - <PERSON>var detalhes da Request e Response ao utilizar pagamento via Pinbank")
public class CriarColunaBodyDetalhesRequestEnviada implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE detalhesrequestenviada ADD COLUMN body TEXT;", c);
        }
    }

}
