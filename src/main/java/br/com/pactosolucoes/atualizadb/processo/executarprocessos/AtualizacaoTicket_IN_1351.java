package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Kaio Ribeiro Sanchez",
        data = "03/07/2025",
        descricao = "Cria indices para as tabelas usuario e produto",
        motivacao = "IN-1351")
public class AtualizacaoTicket_IN_1351 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
        SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_usuario_codigo ON usuario (codigo);", c);
        SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_produto_codigo ON produto (codigo);", c);
        }
    }
}
