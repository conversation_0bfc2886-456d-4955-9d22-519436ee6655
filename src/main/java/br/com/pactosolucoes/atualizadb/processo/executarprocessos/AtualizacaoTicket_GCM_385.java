package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Matheus Cassimio",
        data = "23/06/2025",
        descricao = "GCM-385 - Permissão para tela de Imposto padrão para produtos. Permissão será aplicada para perfis de acesso do tipo ADMINISTRADOR ou com nome de ADMIN",
        motivacao = "GCM-385")
public class AtualizacaoTicket_GCM_385 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "INSERT INTO permissao (tipopermissao,tituloapresentacao,permissoes,nomeentidade,codperfilacesso,unificado,sistema)\n" +
                            "SELECT 2,'10.12 - Imposto padrão para produtos','(0)(1)(2)(3)(9)(12)','ImpostoProduto', pa.codigo ,false,'bdzillyon' \n" +
                            "FROM perfilacesso pa\n" +
                            "WHERE pa.tipo = 1 OR pa.nome ILIKE '%ADMIN%';", c);
        }
    }
}
