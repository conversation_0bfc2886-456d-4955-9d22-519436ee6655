package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.List;

import controle.basico.ConfiguracaoSistemaControle;

import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import negocio.facade.jdbc.utilitarias.Conexao;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import java.sql.Statement;

/**
 * <AUTHOR>
 *
 */
public class CorrigirMovProdutos extends SuperEntidade {

    public CorrigirMovProdutos() throws Exception {
        super();
    }

    public void executarProcesso() throws Exception {
    	Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con);
        corrigirProdutosContrato(c);
        corrigirProdutosPersonal(c);
        corrigirProdutosTrancamento(c);
        corrigirProdutosSaque(c);
        executarInclusaoEmpresaFinanceiro(c);
    }

    /**
     * Responsável por selecionar os contratos que possuem movprodutos sem relacionamento com movparcelas
     * <AUTHOR>
     * 04/10/2011
     * @throws Exception
     */
    public static void corrigirProdutosContrato(Connection c) throws Exception {

        try {
            StringBuilder sql = new StringBuilder();
            //obter todos os codigos de contratos que possuem movprodutos sem relacionamento com movparcelas
            sql.append("SELECT DISTINCT(movproduto.contrato) FROM movproduto \n");
            sql.append("INNER JOIN produto ON produto.codigo = movproduto.produto AND produto.tipoproduto IN ('PM', 'RE', 'RN','MA', 'PE', 'SE') \n");
            sql.append("WHERE movproduto.codigo NOT IN (SELECT movproduto FROM movprodutoparcela) AND contrato IS NOT NULL;");
            setarTotalAProcessar(sql.toString(),c);
            Declaracao declaracao = new Declaracao(sql.toString(), c);
            ResultSet rs = declaracao.executeQuery();
            ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
            while (rs.next()) {
                CorrigirMovProdutos.arranjarMovProdutosParcelasContrato(rs.getInt("contrato"), c);
                if(config != null){
                    config.setTotalProcessadosMovProdutoParcela(config.getTotalProcessadosMovProdutoParcela() + 1);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * <AUTHOR>
     * 10/10/2011
     */
    public static void setarTotalAProcessar(String sql,Connection c) throws Exception {
        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
        if (config != null) {
            ResultSet rs = SuperFacadeJDBC.criarConsultaRolavel(sql, c);
            if (rs.next()) {
                rs.last();
                config.setTotalAProcessarMovProdutoParcela(config.getTotalAProcessarMovProdutoParcela() + rs.getRow());
            } else {
                config.setTotalAProcessarMovProdutoParcela(config.getTotalAProcessarMovProdutoParcela() + 0);
            }
        }
    }

    /**
     * Responsável por corrigir relacionamento entre produtos e parcelas de um contrato
     * <AUTHOR>
     * 30/09/2011
     */
    @SuppressWarnings("unchecked")
    public static void arranjarMovProdutosParcelasContrato(Integer codigoContrato, Connection c) {
        try {

            ZillyonWebFacade zwFacade = new ZillyonWebFacade(c);
            ContratoVO contrato = zwFacade.getContrato().consultarPorChavePrimaria(codigoContrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            //obter parcelas originais do contrato
            ResultSet set = SuperFacadeJDBC.criarConsulta("SELECT * FROM movparcela WHERE contrato = " + codigoContrato + " AND descricao LIKE 'PARCELA%' ORDER BY MovParcela.datavencimento", c);
            contrato.setMovParcelaVOs(MovParcela.montarDadosConsulta(set, Uteis.NIVELMONTARDADOS_DADOSBASICOS, c));
            //obter produtos originais do contrato
            set = SuperFacadeJDBC.criarConsulta("SELECT mp.* FROM movproduto mp, produto p WHERE p.codigo = mp.produto AND mp.contrato =  " + codigoContrato + " AND p.tipoproduto IN ('PM', 'MA','RE', 'RN', 'PE', 'SE') ORDER BY codigo", c);
            List<MovProdutoVO> listMovProdutos = MovProduto.montarDadosConsulta(set, Uteis.NIVELMONTARDADOS_DADOSBASICOS, c);


            //é necessário deletar os relacionamentos que existem, visto que usarei o mesmo processo que é usado na criação
            //do contrato, portanto pode haver duplicidade
            SuperFacadeJDBC.executarConsulta("DELETE FROM movprodutoparcela WHERE movparcela IN (SELECT codigo FROM movparcela WHERE contrato = " + codigoContrato + ");", c);
            //para contratos cancelados ou inativos é necessario levar em conta o movproduto gerado no cancelamento
            if ((contrato.getSituacao().equals("CA") || contrato.getSituacao().equals("IN")) && contrato.getValorFinal() > 0.0) {
                set = SuperFacadeJDBC.criarConsulta("SELECT numeromeses FROM contratoduracao WHERE contrato = " + codigoContrato, c);
                // somar o nrmeses com 1 para considerar o produto matricula/renovacao/rematricula
                set.next();
                Integer nrMeses = set.getInt("numeromeses") + 1;
                MovProdutoVO produtoDoCancelamento = null;
                if (listMovProdutos.size() > nrMeses) {
                    produtoDoCancelamento = listMovProdutos.get(listMovProdutos.size() - 1);
                    listMovProdutos.remove(produtoDoCancelamento);
                }
                contrato.setMovProdutoVOs(listMovProdutos);
                gerarMovProdutoParcelas(contrato, c);
                if (produtoDoCancelamento != null) {
                    for (Object obj : contrato.getMovProdutoVOs()) {
                        MovProdutoVO mp = (MovProdutoVO) obj;
                        if (produtoDoCancelamento.getMesReferencia().equals(mp.getMesReferencia())) {
                            produtoDoCancelamento.setMovProdutoParcelaVOs(mp.getMovProdutoParcelaVOs());
                            break;
                        }
                    }
                    for (Object obj : produtoDoCancelamento.getMovProdutoParcelaVOs()) {
                        MovProdutoParcelaVO mpp = (MovProdutoParcelaVO) obj;
                        mpp.setMovProduto(produtoDoCancelamento.getCodigo());
                        zwFacade.getMovProdutoParcela().incluir(mpp);
                    }
                }
            } else {
                contrato.setMovProdutoVOs(listMovProdutos);
                //aqui verificar se o valor do contrato é 0, pois nestes casos todas os produtos estão associados a apenas uma parcela
                if (contrato.getValorFinal() > 0.0) {
                    gerarMovProdutoParcelas(contrato, c);
                } else {
                    for (Object obj : contrato.getMovProdutoVOs()) {
                        MovProdutoVO movProduto = (MovProdutoVO) obj;
                        movProduto.setQuitado(false);
                    }
                    //para contratos com valor zerado, deletar as parcelas deixando apenas a primeira
                    SuperFacadeJDBC.executarConsulta("DELETE FROM movparcela WHERE contrato = " + codigoContrato
                            + " AND descricao NOT LIKE 'PARCELA 1'", c);
                    PagamentoMovParcela.corrigirPagamentosSemMovParcelas(c);
                    MovParcelaVO movParcela = (MovParcelaVO) contrato.getMovParcelaVOs().get(0);
                    if (movParcela.getSituacao().equals("PG")) {
                        movParcela.setReciboPagamento(zwFacade.getReciboPagamento().consultarPorParcela(movParcela.getCodigo(),
                                Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                    }
                    zwFacade.parcela(contrato, movParcela, movParcela.getReciboPagamento());
                    zwFacade.getMovProdutoParcela().incluirMovProdutoParcelas(movParcela.getCodigo(), movParcela.getMovProdutoParcelaVOs());
                }
            }

            //para o caso de lancamento retroativo, a solução é apenas excluir os movprodutos sem parcela,
            //pois estes foram gerados de maneira errada
            if (Calendario.getDataComHoraZerada(contrato.getDataLancamento()).after(contrato.getVigenciaDe())) {
                SuperFacadeJDBC.executarConsulta("DELETE FROM movproduto WHERE contrato = " + codigoContrato + " AND codigo NOT IN (SELECT movproduto FROM movprodutoparcela); ", c);
            }
            System.out.println("Correção do relacionamento entre produtos e parcelas do contrato " + codigoContrato + " executada.");

        } catch (Exception e) {
            System.out.println("Problemas com a criação de relacionamento entre produtos e parcelas do contrato " + codigoContrato);
        }
    }

    /**
     * Responsável por gerar movprodutosparcelas, utilizando-se do mesmo método que o sistema usa ao gerar o contrato
     * <AUTHOR>
     * 30/09/2011
     */
    private static void gerarMovProdutoParcelas(ContratoVO contrato, Connection c) throws Exception {
        for (Object obj : contrato.getMovProdutoVOs()) {
            MovProdutoVO movProduto = (MovProdutoVO) obj;
            movProduto.setQuitado(false);
        }
        //para cada parcela percorrer produtos setando os relacionamentos
        for (Object obj : contrato.getMovParcelaVOs()) {
            MovParcelaVO movParcela = (MovParcelaVO) obj;
            movParcela.setValorBaseCalculo(movParcela.getValorParcela());
            if (movParcela.getSituacao().equals("PG")) {
                movParcela.setReciboPagamento(new ReciboPagamento(c).consultarPorParcela(movParcela.getCodigo(),
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }
            new ZillyonWebFacade(c).parcela(contrato, movParcela, movParcela.getReciboPagamento());
            new ZillyonWebFacade(c).getMovProdutoParcela().incluirMovProdutoParcelas(movParcela.getCodigo(), movParcela.getMovProdutoParcelaVOs());
        }
    }

    /**
     * Responsável por corrigir os relacionamentos dos produtos gerados no saque de CC
     * <AUTHOR>
     * 04/10/2011
     */
    public static void corrigirProdutosSaque(Connection c) {
        //selecionar os produtos de saque de cc sem relacionamento com parcela...
        StringBuilder sqlProdutos = new StringBuilder();
        sqlProdutos.append("SELECT * FROM movproduto mp \n");
        sqlProdutos.append("WHERE mp.codigo NOT IN (SELECT movproduto FROM movprodutoparcela) AND descricao LIKE 'SAQUE%' ORDER BY datalancamento ");
        //... e as parcelas do mesmo tipo sem produtos
        StringBuilder sqlParcelas = new StringBuilder();
        sqlParcelas.append("SELECT * FROM movparcela \n");
        sqlParcelas.append("WHERE codigo NOT IN (SELECT movparcela FROM movprodutoparcela) AND descricao LIKE 'SAQUE%' ORDER BY dataregistro ");

        executarCorrecao(sqlProdutos.toString(), sqlParcelas.toString(), c);

    }

    /**
     * Responsável por corrigir os relacionamentos dos produtos gerados no trancamento
     * <AUTHOR>
     * 04/10/2011
     */
    public static void corrigirProdutosTrancamento(Connection c) {
        //selecionar os produtos de trancamento sem relacionamento com parcela...
        StringBuilder sqlProdutos = new StringBuilder();
        sqlProdutos.append("SELECT movproduto.* FROM movproduto  \n");
        sqlProdutos.append("INNER JOIN produto ON produto.codigo = movproduto.produto AND produto.tipoproduto IN ('TR') \n");
        sqlProdutos.append("WHERE movproduto.codigo NOT IN (SELECT movproduto FROM movprodutoparcela) AND contrato IS NOT NULL ORDER BY movproduto.codigo ");
        //... e as parcelas do mesmo tipo sem produtos
        StringBuilder sqlParcelas = new StringBuilder();
        sqlParcelas.append("SELECT * FROM movparcela WHERE movparcela.codigo NOT IN (SELECT movparcela FROM movprodutoparcela) AND contrato IS NOT NULL \n");
        sqlParcelas.append("AND descricao LIKE 'TRA%' ORDER BY movparcela.codigo ");
        executarCorrecao(sqlProdutos.toString(), sqlParcelas.toString(), c);

    }

    /**
     * Responsável por corrigir os relacionamentos dos produtos relacionados a PERSONAL TRAINER
     * <AUTHOR>
     * 04/10/2011
     */
    public static void corrigirProdutosPersonal(Connection c) {
        StringBuilder sqlProdutos = new StringBuilder();
        //selecionar os produtos de PERSONAL sem relacionamento com parcela...
        sqlProdutos.append("SELECT movproduto.* FROM movproduto \n");
        sqlProdutos.append("INNER JOIN produto ON produto.codigo = movproduto.produto \n");
        sqlProdutos.append("WHERE movproduto.codigo NOT IN (SELECT movproduto FROM movprodutoparcela) AND contrato IS NULL \n");
        sqlProdutos.append("AND ( produto.tipoproduto IN ('TP') OR movproduto.descricao LIKE 'TAXA PERSONAL%') ORDER BY movproduto.codigo ");
        //... e as parcelas do mesmo tipo sem produtos
        StringBuilder sqlParcelas = new StringBuilder();
        sqlParcelas.append("SELECT * FROM movparcela WHERE movparcela.codigo NOT IN (SELECT movparcela FROM movprodutoparcela) AND contrato IS NULL \n");
        sqlParcelas.append("AND descricao LIKE 'PERSONAL TRAINER' ORDER BY movparcela.codigo ");

        executarCorrecao(sqlProdutos.toString(), sqlParcelas.toString(), c);
    }

    /**
     * Responsável por relacionar uma lista de parcelas a uma lista de produtos a partir de uma consulta ao banco de dados
     * <AUTHOR>
     * 04/10/2011
     */
    @SuppressWarnings("unchecked")
    private static void executarCorrecao(String sqlProduto, String sqlParcela, Connection c) {
        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
        try {
            List<MovProdutoVO> listProdutos = MovProduto.montarDadosConsulta(SuperFacadeJDBC.criarConsulta(sqlProduto, c), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, c);
            List<MovParcelaVO> listParcelas = MovParcela.montarDadosConsulta(SuperFacadeJDBC.criarConsulta(sqlParcela, c), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, c);
            //obter config do sistema para setar os totais

            if(config != null){
                config.setTotalAProcessarMovProdutoParcela(config.getTotalAProcessarMovProdutoParcela() + listParcelas.size());
            }

            for (MovProdutoVO movProduto : listProdutos) {
                movProduto.setQuitado(false);
            }
            //percorrer as parcelas preenchendo os relacionamentos corretos com produtos
            for (MovParcelaVO movParcela : listParcelas) {
                movParcela.setValorBaseCalculo(movParcela.getValorParcela());
                Iterator<MovProdutoVO> iterator = listProdutos.iterator();
                while (iterator.hasNext()) {
                    MovProdutoVO movProduto = iterator.next();
                    if (!movProduto.getQuitado()) {
                        MovProdutoParcelaVO movProdutoParcela = new MovProdutoParcelaVO();
                        movProdutoParcela.setMovProduto(movProduto.getCodigo());
                        Double valor = Uteis.arredondarForcando2CasasDecimais(movProduto.getTotalFinal());
                        movProdutoParcela.setValorPago(valor);
                        if (movParcela.getSituacao().equals("PG")) {
                            movProdutoParcela.setReciboPagamento(new ReciboPagamento(c).consultarPorParcela(movParcela.getCodigo(),
                                    Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                        }
                        movParcela.getMovProdutoParcelaVOs().add(movProdutoParcela);
                        movProduto.getMovProdutoParcelaVOs().add(movProdutoParcela);
                        movProduto.setQuitado(true);
                        Double valorBase = Uteis.arredondarForcando2CasasDecimais(movParcela.getValorBaseCalculo());
                        movParcela.setValorBaseCalculo(valorBase - valor);

                        if (movParcela.getValorBaseCalculo() == 0) {
                            break;
                        }
                    }
                }
                if(config != null){
                    config.setTotalProcessadosMovProdutoParcela(config.getTotalProcessadosMovProdutoParcela() + 1);
                }
                System.out.println("Restaurados os relacionamentos da parcela " + movParcela.getCodigo() + " com seus produtos.");
                new MovProdutoParcela(c).incluirMovProdutoParcelas(movParcela.getCodigo(), movParcela.getMovProdutoParcelaVOs());
            }
        } catch (Exception e) {
            if(config != null){
                config.setTotalProcessadosMovProdutoParcela(config.getTotalProcessadosMovProdutoParcela() + 1);
            }
            System.out.println("Problemas com a criação de relacionamento entre produtos e parcelas : \n" + e.getMessage());
        }
    }

    public void executarInclusaoEmpresaFinanceiro(Connection c) throws Exception {
        try {
            ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");
            if(config != null){
                config.setTotalAProcessarMovProdutoParcela(config.getTotalAProcessarMovProdutoParcela() + 100);
            }

            System.out.println("Iniciando a atualização do campo empresa nas tabelas alteradas.\n");
            Statement st = c.createStatement();
            ResultSet lista = st.executeQuery("SELECT movparcela.codigo, movproduto.empresa, movproduto.pessoa FROM movparcela "
                                            + "INNER JOIN movprodutoparcela ON movprodutoparcela.movparcela = movparcela.codigo "
                                            + "INNER JOIN movproduto ON movproduto.codigo = movprodutoparcela.movproduto "
                                            + "WHERE movparcela.empresa is null OR movparcela.pessoa is null");
            // atualiza todas as parcelas consultadas
            while(lista.next()) {
                if(lista.getInt("empresa") == 0 || lista.getInt("pessoa") == 0)
                    continue;
                SuperFacadeJDBC.executarConsulta("UPDATE movparcela SET empresa = "+lista.getInt("empresa")
                                               + ", pessoa = "+lista.getInt("pessoa")
                                               + " WHERE codigo = "+lista.getInt("codigo"), c);
                System.out.println("parcela "+lista.getInt("codigo")+" atualizada");
            }
            lista = st.executeQuery("SELECT DISTINCT movpagamento.codigo, movpagamento.recibopagamento, movparcela.empresa FROM movpagamento "
                                  + "INNER JOIN pagamentomovparcela ON pagamentomovparcela.movpagamento = movpagamento.codigo "
                                  + "INNER JOIN movparcela ON movparcela.codigo = pagamentomovparcela.movparcela "
                                  + "INNER JOIN recibopagamento ON recibopagamento.codigo = movpagamento.recibopagamento "
                                  + "WHERE movpagamento.empresa is null OR recibopagamento.empresa is null");
            // atualiza todos os pagamentos e recibos consultados
            while(lista.next()) {
                if(lista.getInt("empresa") == 0)
                    continue;
                SuperFacadeJDBC.executarConsulta("UPDATE movpagamento SET empresa = "+lista.getInt("empresa")
                                               + " WHERE codigo = "+lista.getInt("codigo"), c);
                System.out.println("pagamento "+lista.getInt("codigo")+" atualizado");
                SuperFacadeJDBC.executarConsulta("UPDATE recibopagamento SET empresa = "+lista.getInt("empresa")
                                               + " WHERE codigo = "+lista.getInt("recibopagamento"), c);
                System.out.println("recibo "+lista.getInt("recibopagamento")+" atualizado");
            }
            System.out.println("Atualização concluída.\n");
            if(config != null){
                config.setTotalProcessadosMovProdutoParcela(config.getTotalAProcessarMovProdutoParcela());
            }

        } finally {
            if (c != null) {
                c.close();
            }
        }
    }
}
