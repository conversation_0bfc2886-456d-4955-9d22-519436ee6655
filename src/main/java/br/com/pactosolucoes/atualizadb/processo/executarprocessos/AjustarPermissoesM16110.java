package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "Wenderson",
        data = "03/07/2025",
        descricao = "Corrigir número de permissões duplicadas",
        motivacao = "M1-6110")
public class AjustarPermissoesM16110 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sql = "UPDATE permissao SET tituloapresentacao='9.102 - Permite consultar Lotes de Pagamento' " +
                    "WHERE (nomeentidade='ConsultarLotesPagamento');";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            sql = "UPDATE permissao SET tituloapresentacao='9.103 - Permite criar Lotes de Pagamento' " +
                    "WHERE (nomeentidade='CriarLotesPagamento');";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
        }
    }
}
