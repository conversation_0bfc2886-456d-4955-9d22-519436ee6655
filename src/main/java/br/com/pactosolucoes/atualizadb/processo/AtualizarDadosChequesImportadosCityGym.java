/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

/**
 *
 * <AUTHOR>
 */
public class AtualizarDadosChequesImportadosCityGym {
     public static void main(String... args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "city");
            Conexao.guardarConexaoForJ2SE(c);
            inferirDataCompensacao(c);
            inferirContaAgenciaNumero(c);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarParcelas(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        inferirDataCompensacao(con);
    }

    public static void inferirDataCompensacao(Connection con) throws Exception {

        Uteis.logar(null, "******* Iniciando Processamento inferirDataCompensacao  ******\n");


        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select mp.codigo, mp.datalancamento from movpagamento mp inner join cheque ch on mp.codigo  = ch.movpagamento where ch.datacompesancao is null group by mp.codigo, mp.datalancamento", con);
        ResultSet rscheques = null;
        while (consulta.next()) {
            try {
                rscheques = SuperFacadeJDBC.criarConsulta("select codigo  from cheque where movpagamento = " + consulta.getInt("codigo") + " order by codigo", con);
                Date compensacao = consulta.getDate("datalancamento");
                String vistaouprazo = "AV";
                while (rscheques.next()) {
                    SuperFacadeJDBC.executarConsultaUpdate("update cheque set vistaouprazo = '" + vistaouprazo + "', datacompesancao = '" + Uteis.getDataJDBC(compensacao) + "' where codigo = " + rscheques.getInt("codigo"), con);
                    compensacao = Uteis.somarCampoData(compensacao, Calendar.MONTH, 1);
                    vistaouprazo = "PR";
                }
            } catch (Exception e) {
                Uteis.logar(null, " movpagamento: " + consulta.getInt("codigo") + " teve problemas: " + e.getMessage() + "\n");
            }
             Uteis.logar(null, "#movpagamento " + consulta.getInt("codigo") + " ajustado \n");
        }
        Uteis.logar(null, "******* finalizando Processamento inferirDataCompensacao  ******\n");
    }

    public static void inferirContaAgenciaNumero(Connection con) throws Exception {

        Uteis.logar(null, "******* Iniciando Processamento inferirContaAgenciaNumero  ******\n");
        String numeroAntigo = "";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select mp.codigo from movpagamento mp inner join cheque ch on mp.codigo  = ch.movpagamento where ch.agencia is null or ch.agencia = '' group by mp.codigo", con);
        ResultSet rscheques = null;
        while (consulta.next()) {
            try {
                rscheques = SuperFacadeJDBC.criarConsulta("select ch.codigo, char_length(numero)as tamanho, numero,mp.pessoa from cheque ch inner join movpagamento mp on mp.codigo = ch.movpagamento where movpagamento = " + consulta.getInt("codigo") + " order by ch.codigo", con);
                rscheques.next();
                numeroAntigo = rscheques.getString("numero");
                String agencia = rscheques.getString("pessoa");;
                String conta = rscheques.getString("pessoa");
                String numero = rscheques.getString("numero");
                if (rscheques.getInt("tamanho") < 2 || (numero.equals("Depósitado") || numero.equals("Depósi") || numero.equals("Depósito") || numero.startsWith("Dep")) ) {
                    agencia = rscheques.getString("pessoa");
                    numero = "0001";
                } else if (rscheques.getInt("tamanho") == 3) {
                    agencia = rscheques.getString("numero");
                    numero = "00001";
                } else if (numeroAntigo.contains("/")) {
                    String[] split = numeroAntigo.split("/");
                    agencia = split[0];
                    if (split.length == 1) {
                        numero = "00001";
                    } else if (split.length > 2) {
                        numero = split[1].trim();
                        if(numero.equals("")){
                            numero = split[2];
                        }
                    } else if (split[1].trim().contains(" ")) {
                        String[] splitEspaco = split[1].trim().split(" ");
                        numero = splitEspaco[0];
                    } else if (split[1].trim().contains("-") && split[1].lastIndexOf("-") > 3) {
                        numero = split[1].trim().substring(0, split[1].lastIndexOf("-"));
                    } else {
                        numero = split[1].trim();
                    }

                } else if (numeroAntigo.contains("-")) {
                    String[] split = numeroAntigo.split("-");
                    agencia = split[0];
                    if (split.length == 1) {
                        numero = "00001";
                    } else if (split.length > 2) {
                        numero = split[1].trim();
                        if (numero.length() < 4) {
                            numero = numero + "-" + split[2];
                        }
                    } else if (split[1].trim().contains(" ")) {
                        String[] splitEspaco = split[1].trim().split(" ");
                        numero = splitEspaco[0];
                    } else {
                        numero = split[1].trim();
                    }

                } else if (numero.contains("a") && numero.indexOf("a") > 2) {
                    numero = numero.substring(0, numero.indexOf("a"));
                } else if (numero.contains("á")) {
                    numero = numero.substring(0, numero.indexOf("á"));
                } else if(numero.contains("A") && numero.indexOf("A") > 2){
                    numero = numero.substring(0, numero.indexOf("A"));
                } else  if (numero.contains("à")) {
                    numero = numero.substring(0, numero.indexOf("à"));
                } else if (numero.contains("t")) {
                    numero = numero.substring(0, numero.indexOf("t"));
                } else if (numero.contains(",")) {
                    numero = numero.substring(0, numero.indexOf(","));
                } else if (numero.length() > 10) {
                    numero = numero.substring(0, numero.length()/2);
                }
                if(numero.contains("ATÉ") ){
                    numero = numero.substring(0, numero.indexOf("A"));
                }
                if(numero.contains(",")){
                    numero = numero.substring(0, numero.indexOf(","));
                }
                if(numero.contains("a") && numero.indexOf("a") > 2){
                    numero = numero.substring(0, numero.indexOf("a"));
                }
                if(numero.contains("A") && numero.indexOf("A") > 2){
                    numero = numero.substring(0, numero.indexOf("A"));
                }
                if (numero.contains("á")) {
                    numero = numero.substring(0, numero.indexOf("á"));
                }
                
                if (numero.contains("Á")) {
                    numero = numero.substring(0, numero.indexOf("Á"));
                }
                
                if (numero.contains("à")) {
                    numero = numero.substring(0, numero.indexOf("à"));
                }
                
                if(numero.contains("-") && numero.indexOf("-") > 2){
                    numero = numero.substring(0, numero.indexOf("-"));
                }
                if(numero.contains("c")&& numero.indexOf("c") > 2){
                    numero = numero.substring(0, numero.indexOf("c"));
                }
                if(numero.contains("C")&& numero.indexOf("C") > 2){
                    numero = numero.substring(0, numero.indexOf("C"));
                }
                if(numero.contains("i")&& numero.indexOf("i") > 2){
                    numero = numero.substring(0, numero.indexOf("i"));
                }
                if(numero.contains(" ")){
                    numero = numero.substring(0, numero.indexOf(" "));
                }
                if(numero.contains(".")){
                    numero = numero.substring(0, numero.indexOf("."));
                }
                if (numero.length() > 10){
                    numero = numero.substring(0, numero.length()/2);
                }
                numero = numero.trim();
                SuperFacadeJDBC.executarConsultaUpdate("update cheque set agencia = '" + agencia + "', conta = '" + conta + "', numero = '" + numero + "' where codigo = " + rscheques.getInt("codigo"), con);
                while (rscheques.next()) {
                    numero = Uteis.getIncrementarNumeroCheque(numero);
                    SuperFacadeJDBC.executarConsultaUpdate("update cheque set agencia = '" + agencia + "', conta = '" + conta + "', numero = '" + numero + "' where codigo = " + rscheques.getInt("codigo"), con);
                }
            } catch (Exception e) {
                Uteis.logar(null, " movpagamento: " + consulta.getInt("codigo") + " teve problemas: " + e.getMessage() + "\n");
            }
            Uteis.logar(null, "#movpagamento " + consulta.getInt("codigo") + " ajustado \n");
        }
        Uteis.logar(null, "******* finalizando Processamento inferirContaAgenciaNumero  ******\n");
    }
}
