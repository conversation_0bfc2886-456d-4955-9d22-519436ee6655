/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.basico.ConfiguracaoSistemaControle;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.HistoricoContratoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;

/**
 * <AUTHOR>
 */
public class AjustarLinhaDoTempoDeContratos {

    private static final String COLUNA_PESSOA = "pessoa";

    private static List<ContratoVO> linha1;
    private static List<ContratoVO> linha2;
    private static List<ContratoVO> linha3;
    private static List<ContratoVO> linha4;
    private static List<ContratoVO> linha5;
    private static List<ContratoVO> linha6;
    private static List<ContratoVO> linha7;
    private static List<ContratoVO> linha8;
    private static List<ContratoVO> linha9;
    private static List<ContratoVO> linha10;
    private static List<ContratoVO> linha11;
    private static List<ContratoVO> linha12;
    private static List<ContratoVO> linha13;
    private static List<ContratoVO> linha14;
    private static List<ContratoVO> linha15;
    private static List<ContratoVO> linha16;
    private static List<ContratoVO> linha17;
    private static List<ContratoVO> linha18;
    private static List<ContratoVO> linha19;
    private static List<ContratoVO> linha20;

    public static void main(String[] args) {
        try {
            Connection c = new DAO().obterConexaoEspecifica(args.length > 0 ? args[0] : "fit");
            Conexao.guardarConexaoForJ2SE(c);
            int empresa = (args.length > 1 ? Integer.parseInt(args[1]) : 0);
            ajustarContratos(c, empresa);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarContratos(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        ajustarContratos(con, 0);
    }

    public static void ajustarContratos(Connection con, int codigoempresa) throws Exception {
        Uteis.logarDebug("Iniciando processo: ajustar linha do tempo dos contratos");

        StringBuffer sbErros = new StringBuffer("Resumo de Erros: \n");
        String consultaNum = "select count(pessoa) as numero from cliente where situacao <> 'VI'";
        if (codigoempresa > 0) {
            consultaNum += " and empresa = " + codigoempresa;
        }

        ConfiguracaoSistemaControle config = (ConfiguracaoSistemaControle) JSFUtilities.getFromSession("ConfiguracaoSistemaControle");

        ResultSet consultaNumero = SuperFacadeJDBC.criarConsulta(consultaNum, con);
        consultaNumero.next();
        Integer total = consultaNumero.getInt("numero");
        String consultaClientes = "select pessoa from cliente where situacao <> 'VI'";
        if (codigoempresa > 0) {
            consultaClientes += " and empresa = " + codigoempresa;
        }
        consultaClientes += " order by pessoa";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(consultaClientes, con);
        List<ContratoVO> contratos;


        Integer cont = 1;
        Integer linhas = 0;

        while (consulta.next()) {
            if (config == null || config.isRodandoAjustarLinhaDoTempoDeContratos() || config.isRodandoExecutarProcessos()) {
                Integer pessoa = consulta.getInt(COLUNA_PESSOA);
                Date d1 = negocio.comuns.utilitarias.Calendario.hoje();
                contratos = getFacade().getContrato().consultarPorCodigoPessoa(consulta.getInt(COLUNA_PESSOA), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                linha1 = new ArrayList<>();
                linha2 = new ArrayList<>();
                linha3 = new ArrayList<>();
                linha4 = new ArrayList<>();
                linha5 = new ArrayList<>();
                linha6 = new ArrayList<>();
                linha7 = new ArrayList<>();
                linha8 = new ArrayList<>();
                linha9 = new ArrayList<>();
                linha10 = new ArrayList<>();
                linha11 = new ArrayList<>();
                linha12 = new ArrayList<>();
                linha13 = new ArrayList<>();
                linha14 = new ArrayList<>();
                linha15 = new ArrayList<>();
                linha16 = new ArrayList<>();
                linha17 = new ArrayList<>();
                linha18 = new ArrayList<>();
                linha19 = new ArrayList<>();
                linha20 = new ArrayList<>();

                Ordenacao.ordenarLista(contratos, "vigenciaDe");
                for (ContratoVO contrato : contratos) {
                    avaliarContrato(config, consulta, contrato);
                }

                linhas = avaliarLinhaGravarAlteracoes(linha1, 1, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha2, 2, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha3, 3, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha4, 4, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha5, 5, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha6, 6, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha7, 7, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha8, 8, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha9, 9, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha10, 10, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha11, 11, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha12, 12, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha13, 13, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha14, 14, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha15, 15, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha16, 16, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha17, 17, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha18, 18, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha19, 19, sbErros, linhas);
                linhas = avaliarLinhaGravarAlteracoes(linha20, 20, sbErros, linhas);

                Date d2 = negocio.comuns.utilitarias.Calendario.hoje();
                Uteis.logarDebug(informacaoContratoPessoa(total, cont, linhas, pessoa, d1, d2));
                if (config != null) {
                    config.setInformacaoAjustarLinhaDoTempoContratos(informacaoContratoPessoa(total, cont, linhas, pessoa, d1, d2));
                    config.setInformacaoExecutarProcessos(informacaoContratoPessoa(total, cont, linhas, pessoa, d1, d2));
                }
                cont++;
            } else {
                config.setRodandoAjustarLinhaDoTempoDeContratos(false);
                return;
            }
        }
        Uteis.logarDebug(sbErros.toString());
        if (config != null) {
            config.setInformacaoAjustarLinhaDoTempoContratos("Ajustes concluídos");

            //EXECUÇÃO DE TODOS OS PROCESSO
            config.setInformacaoExecutarProcessos("Ajustes concluídos");

            config.setRodandoAjustarLinhaDoTempoDeContratos(false);
        }
    }

    private static int avaliarLinhaGravarAlteracoes(List<ContratoVO> linha, int nrLinha, StringBuffer sbErros, int linhas) {
        if (!linha.isEmpty()) {
            gravarAlteracoes(linha, sbErros);
            return nrLinha;
        }
        return linhas;
    }

    private static void avaliarContrato(ConfiguracaoSistemaControle config, ResultSet consulta, ContratoVO contrato) throws Exception {
        // tenta colocar como renovação na linha mais recente
        if (UteisValidacao.emptyNumber(contrato.getContratoOrigemTransferencia())) {
            List<List<ContratoVO>> listaLinhas = new ArrayList<>();
            listaLinhas.add(linha20);
            listaLinhas.add(linha19);
            listaLinhas.add(linha18);
            listaLinhas.add(linha17);
            listaLinhas.add(linha16);
            listaLinhas.add(linha15);
            listaLinhas.add(linha14);
            listaLinhas.add(linha13);
            listaLinhas.add(linha12);
            listaLinhas.add(linha11);
            listaLinhas.add(linha10);
            listaLinhas.add(linha9);
            listaLinhas.add(linha8);
            listaLinhas.add(linha7);
            listaLinhas.add(linha6);
            listaLinhas.add(linha5);
            listaLinhas.add(linha4);
            listaLinhas.add(linha3);
            listaLinhas.add(linha2);
            listaLinhas.add(linha1);

            for (List<ContratoVO> linha : listaLinhas) {
                if (verificarRenovacaoNaLinha(linha, contrato)) {
                    return;
                }
            }
        }

        //tenta adicionar como rematricula da linha mais antiga, senão adiciona uma matricula numa nova linha.
        List<List<ContratoVO>> listaLinhas = new ArrayList<>();
        listaLinhas.add(linha1);
        listaLinhas.add(linha2);
        listaLinhas.add(linha3);
        listaLinhas.add(linha4);
        listaLinhas.add(linha5);
        listaLinhas.add(linha6);
        listaLinhas.add(linha7);
        listaLinhas.add(linha8);
        listaLinhas.add(linha9);
        listaLinhas.add(linha10);
        listaLinhas.add(linha11);
        listaLinhas.add(linha12);
        listaLinhas.add(linha13);
        listaLinhas.add(linha14);
        listaLinhas.add(linha15);
        listaLinhas.add(linha16);
        listaLinhas.add(linha17);
        listaLinhas.add(linha18);
        listaLinhas.add(linha19);
        listaLinhas.add(linha20);

        for (List<ContratoVO> linha : listaLinhas) {
            if (avaliarRematriculaLinha(linha, contrato)) return;
        }

        Uteis.logarDebug("## pessoa " + consulta.getInt(COLUNA_PESSOA) + " EXTRAPOLOU!");
        if (config != null) {
            config.setInformacaoAjustarLinhaDoTempoContratos("## pessoa " + consulta.getInt(COLUNA_PESSOA) + " EXTRAPOLOU!");
        }
    }

    private static boolean avaliarRematriculaLinha(List<ContratoVO> linha, ContratoVO contrato) {
        if (!linha.isEmpty()) {
            return UteisValidacao.emptyNumber(contrato.getContratoOrigemTransferencia()) && verificaRematricula(linha, contrato);
        } else {
            adicionarMatricula(linha, contrato);
            return true;
        }
    }

    private static String informacaoContratoPessoa(Integer total, Integer cont, Integer linhas, Integer pessoa, Date d1, Date d2) {
        return String.format("Ajustando contratos da pessoa %s . Linhas #%s. Andamento %s/%s. (%s ms) ", pessoa, linhas, cont, total, (d2.getTime() - d1.getTime()));
    }

    private static boolean verificarRenovacaoNaLinha(List<ContratoVO> linha, ContratoVO contrato) throws Exception {
        return !linha.isEmpty() && verificaRenovacao(linha, contrato);
    }

    public static void adicionarMatricula(List<ContratoVO> linha, ContratoVO contrato) {
        if (UteisValidacao.notEmptyNumber(contrato.getContratoOrigemTransferencia())) {
            contrato.setSituacaoContrato("TF");
        } else {
            contrato.setSituacaoContrato("MA");
        }
        contrato.setContratoBaseadoRematricula(0);
        contrato.setContratoBaseadoRenovacao(0);
        contrato.setContratoResponsavelRematriculaMatricula(0);
        contrato.setContratoResponsavelRenovacaoMatricula(0);
        contrato.setDataRematriculaRealizada(null);
        contrato.setDataRenovarRealizada(null);
        contrato.setDataMatricula(contrato.getDataLancamento());
        contrato.setSituacaoRenovacao("");
        linha.add(contrato);

    }

    public static boolean verificaRenovacao(List<ContratoVO> linha, ContratoVO contrato) throws Exception {
        ContratoVO aux = linha.get(linha.size() - 1);
        if (!contrato.getEmpresa().getCodigo().equals(aux.getEmpresa().getCodigo())
                || getFacade().getContratoOperacao().existeOperacaoParaEsteContrato(aux.getCodigo(), "CA")) {
            return false;
        }
        int diferenca = (int) Uteis.nrDiasEntreDatas(aux.getVigenciaAteAjustada(), contrato.getVigenciaDe());

        if (diferenca <= 1 && diferenca >= -1) {
            contrato.setSituacaoContrato("RN");
            contrato.setContratoBaseadoRematricula(0);
            contrato.setContratoBaseadoRenovacao(aux.getCodigo());
            contrato.setDataMatricula(aux.getDataMatricula());
            contrato.setContratoResponsavelRematriculaMatricula(0);
            contrato.setContratoResponsavelRenovacaoMatricula(0);
            contrato.setDataRematriculaRealizada(null);
            contrato.setDataRenovarRealizada(null);
            contrato.setSituacaoRenovacao("");
            linha.add(contrato);
            aux.setContratoResponsavelRenovacaoMatricula(contrato.getCodigo());
            aux.setDataRenovarRealizada(contrato.getDataLancamento());
            aux.setSituacaoRenovacao(obterSituacaoRenovacao(aux.getVigenciaAteAjustada(), contrato.getDataLancamento()));
            return true;
        }
        return false;

    }


    public static boolean verificaRematricula(List<ContratoVO> linha, ContratoVO contrato) {
        ContratoVO aux = linha.get(linha.size() - 1);
        if (!contrato.getEmpresa().getCodigo().equals(aux.getEmpresa().getCodigo())) {
            return false;
        }
        int diferenca = (int) Uteis.nrDiasEntreDatas(aux.getVigenciaAteAjustada(), contrato.getVigenciaDe());

        if (diferenca > 1) { // rematricula
            contrato.setSituacaoContrato("RE");
            contrato.setContratoBaseadoRematricula(aux.getCodigo());
            contrato.setDataMatricula(aux.getDataMatricula());
            contrato.setContratoBaseadoRenovacao(0);
            contrato.setContratoResponsavelRematriculaMatricula(0);
            contrato.setContratoResponsavelRenovacaoMatricula(0);
            contrato.setDataRematriculaRealizada(null);
            contrato.setDataRenovarRealizada(null);
            contrato.setSituacaoRenovacao("");
            linha.add(contrato);
            aux.setContratoResponsavelRematriculaMatricula(contrato.getCodigo());
            aux.setDataRematriculaRealizada(contrato.getDataLancamento());
            return true;
        }
        return false;

    }

    public static String obterSituacaoRenovacao(Date vigenciaAteAjustada, Date dataLancamentoRenovacao) {
        if (Calendario.igual(dataLancamentoRenovacao, vigenciaAteAjustada)) {
            return "ND";
        } else if (Calendario.menor(dataLancamentoRenovacao, vigenciaAteAjustada)) {
            return "AN";
        } else {
            return "AV";
        }
    }

    public static void gravarAlteracoes(List<ContratoVO> contratos, StringBuffer sbErros) {
        for (ContratoVO contrato : contratos) {
            try {
                getFacade().getContrato().alterarApenasDadosBasicos(contrato);
                HistoricoContratoVO primeiro = getFacade().getHistoricoContrato().obterHistoricoContratoPorDataEspecifica(contrato.getCodigo(), contrato.getVigenciaDe(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                if (primeiro == null) {
                    getFacade().getHistoricoContrato().incluirPrimeiroBaseadoSituacaoContrato(contrato, true);
                } else if (!primeiro.getTipoHistorico().equals(contrato.getSituacaoContrato()) && !primeiro.getTipoHistorico().equals("CT")) {
                    primeiro.setTipoHistorico(contrato.getSituacaoContrato());
                    primeiro.setDescricao(getDescricaoHistorico(primeiro.getTipoHistorico()));
                    getFacade().getHistoricoContrato().alterarSemCommit(primeiro, Boolean.FALSE);
                }
            } catch (Exception ex) {
                String msgErro = "O contrato " + contrato.getCodigo() + " da pessoa " + contrato.getPessoa() + " esta com problemas no historico";
                sbErros.append(msgErro).append("/n");
                Uteis.logarDebug(msgErro);
                Logger.getLogger(AjustarLinhaDoTempoDeContratos.class.getName()).log(Level.SEVERE, msgErro, ex);
            }
        }
    }

    public static String getDescricaoHistorico(String tipoHistorico) {
        if (tipoHistorico.equals("MA")) {
            return "MATRICULADO";
        }
        if (tipoHistorico.equals("RE")) {
            return "REMATRICULA";
        }
        if (tipoHistorico.equals("RN")) {
            return "RENOVADO";
        }
        if (tipoHistorico.equals("TF")) {
            return "TRANSFENCIA";
        }
        return "";
    }
}
