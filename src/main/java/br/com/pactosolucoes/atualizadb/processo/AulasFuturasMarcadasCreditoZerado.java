package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoHorarioCreditoTreinoEnum;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.Date;
import java.sql.DriverManager;
import java.sql.ResultSet;

public class AulasFuturasMarcadasCreditoZerado {


    public static void main(String[] args) throws Exception{
        Connection con = DriverManager.getConnection("********************************************************", "postgres", "pactodb");
        String sql = "select cli.codigomatricula, contrato, count(r.codigo) as aulas, min(r.datalancamento) as lancamento  from reposicao r" +
                " inner join cliente cli on cli.codigo = r.cliente " +
                "        where datareposicao > current_date\n" +
                "        group by codigomatricula, contrato";

        ResultSet rs = con.prepareStatement(sql).executeQuery();
        int i = 0;
        TurmasServiceImpl service = new TurmasServiceImpl(con);
        while (rs.next()){
            Date lancamento = rs.getDate("lancamento");
            Integer codigomatricula = rs.getInt("codigomatricula");
            Integer contrato = rs.getInt("contrato");
            Integer aulas = rs.getInt("aulas");

            Integer saldo = consultarSaldoAluno(codigomatricula, contrato, service, con);
            if(saldo > 0){
                continue;
            }
            System.out.println(i++);
            System.out.println("matricula: " + codigomatricula);
            System.out.println("contrato: " + contrato);
            System.out.println("saldo: " + saldo);
            System.out.println("aulas: " + aulas);
            System.out.println("lancamento: " + Calendario.getDataAplicandoFormatacao(lancamento, "dd/MM/yyyy"));
            System.out.println("------------------------------");
        }


    }

    public static Integer consultarSaldoAluno(Integer matricula, Integer contrato, TurmasServiceImpl service, Connection con) throws Exception {

        try (ResultSet rs = SuperFacadeJDBC.criarConsulta(" select tipohorario, quantidadecreditodisponivel from contratoduracaocreditotreino cdc \n"
                + " INNER JOIN contratoduracao cd ON cd.codigo = cdc.contratoduracao \n"
                + " where cd.contrato = " + contrato, con)) {
            if (rs.next()) {
                TipoHorarioCreditoTreinoEnum tipo = TipoHorarioCreditoTreinoEnum.getTipo(rs.getInt("tipohorario"));
                if (tipo.equals(TipoHorarioCreditoTreinoEnum.HORARIO_TURMA)) {
                    return service.nrAulasARepor(matricula);
                }
                Integer creditoDisponivel = service.getControleCreditoDao().consultarSaldoCredito(contrato);
                Integer marcacoesFuturas = service.numeroAulasExtraMarcadas(contrato); //reposicões futuras não vinculadas a desmarcações(ainda não debitaram crédito)
                Integer nrMarcacoesNoDia = service.nrReposicoesDoDia(contrato);//reposições de hoje que podem ter tido utilizacao
                Integer nrCreditosUsadosNoDia = service.nrCreditosUsadosDiaFaltaUtilizacao(contrato, Calendario.hoje()); //calculo leva em considaração aulas do dia, caso essa aulas já tenha abatido o crédito, elas devem ser retiradas do cálculo
                nrMarcacoesNoDia -= nrCreditosUsadosNoDia;
                if (nrMarcacoesNoDia <= 0) {
                    nrMarcacoesNoDia = 0; //reposições foram consumidas, não devem afetar cálculo;
                }
                Integer saldoVirtual = creditoDisponivel - marcacoesFuturas - nrMarcacoesNoDia;
                return saldoVirtual;
            }
        }
        return 0;
    }


}
