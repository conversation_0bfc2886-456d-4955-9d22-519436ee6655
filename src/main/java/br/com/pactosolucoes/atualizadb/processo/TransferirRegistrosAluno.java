/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class TransferirRegistrosAluno {


    public static void transferir(Connection con, Integer matriculaDestino, Integer  ... matriculasOrigens){
        try {
            String mats = "";
            for(Integer mat : matriculasOrigens){
                mats += ","+mat;
            }
            mats = mats.replaceFirst(",", "");

            ResultSet rsCliente = SuperFacadeJDBC.criarConsulta("SELECT codigo, pessoa FROM cliente WHERE codigomatricula = " + matriculaDestino, con);
            Integer codigoClienteDestino = 0;
            Integer pessoaDestino = 0;
            if(rsCliente.next()){
                codigoClienteDestino = rsCliente.getInt("codigo");
                pessoaDestino = rsCliente.getInt("pessoa");
            }

            String codigosClientesOrigens = "";
            String codigosPessoasOrigens = "";

            ResultSet rsClientes = SuperFacadeJDBC.criarConsulta("SELECT codigo, pessoa FROM cliente WHERE codigomatricula IN (" + mats+")", con);
            while(rsClientes.next()){
               codigosClientesOrigens +=","+rsClientes.getInt("codigo");
               codigosPessoasOrigens += ","+rsClientes.getInt("pessoa");
            }
            codigosClientesOrigens = codigosClientesOrigens.replaceFirst(",", "");
            codigosPessoasOrigens = codigosPessoasOrigens.replaceFirst(",", "");

            ResultSet fkCliente = obterChavesEstrangeirasETabelas(con, "cliente");
            while(fkCliente.next()){

                if(fkCliente.getString("relname").equals("historicovinculo")
                        || fkCliente.getString("relname").equals("questionariocliente")
                        || fkCliente.getString("relname").equals("risco")
                        || fkCliente.getString("relname").equals("situacaocontratoanaliticodw")
                        || fkCliente.getString("relname").equals("vinculo")){
                    continue;
                }
                System.out.println(fkCliente.getString("relname") + " - "+fkCliente.getString("child_column"));

                SuperFacadeJDBC.executarConsulta("UPDATE "+fkCliente.getString("nspname") +"."+fkCliente.getString("relname")
                        +" set "+fkCliente.getString("child_column") + " = "+codigoClienteDestino
                        +" where "+fkCliente.getString("child_column") +" in ("+codigosClientesOrigens+")", con);
            }


            ResultSet fkPessoa = obterChavesEstrangeirasETabelas(con, "pessoa");
            while(fkPessoa.next()){
                if(fkPessoa.getString("relname").equals("pessoafotolocalacesso")
                        || fkPessoa.getString("relname").equals("movimentocontacorrentecliente")
                        || fkPessoa.getString("relname").equals("email")
                        || fkPessoa.getString("relname").equals("telefone")
                        || fkPessoa.getString("relname").equals("cliente")){
                    continue;
                }
                SuperFacadeJDBC.executarConsulta("UPDATE "+fkPessoa.getString("nspname") +"."+fkPessoa.getString("relname")
                        +" set "+fkPessoa.getString("child_column") + " = "+pessoaDestino
                        +" where "+fkPessoa.getString("child_column") +" in ("+codigosPessoasOrigens+")", con);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static ResultSet obterChavesEstrangeirasETabelas(Connection con,String tabela) throws Exception{
        StringBuilder sql = new StringBuilder("       select \n");
        sql.append("att2.attname as child_column, \n");
        sql.append("cl.relname as parent_table, \n");
        sql.append("att.attname as parent_column,\n");
        sql.append("con.relname, con.nspname \n");
        sql.append("from\n");
        sql.append("(select \n");
        sql.append("unnest(con1.conkey) as parent, \n");
        sql.append("unnest(con1.confkey) as child, \n");
        sql.append("con1.confrelid, \n");
        sql.append("con1.conrelid,\n");
        sql.append("cl.*, ns.nspname\n");
        sql.append("from \n");
        sql.append("pg_class cl\n");
        sql.append("join pg_namespace ns on cl.relnamespace = ns.oid\n");
        sql.append("join pg_constraint con1 on con1.conrelid = cl.oid\n");
        sql.append(") con       \n");
        sql.append("join pg_attribute att on\n");
        sql.append("att.attrelid = con.confrelid and att.attnum = con.child\n");
        sql.append("join pg_class cl on\n");
        sql.append("cl.oid = con.confrelid\n");
        sql.append("join pg_attribute att2 on\n");
        sql.append("att2.attrelid = con.conrelid and att2.attnum = con.parent\n");
        sql.append("where cl.relname = '").append(tabela).append("' ");
        sql.append("ORDER BY con.relname");

        return SuperFacadeJDBC.criarConsulta(sql.toString(), con);
    }

    public static void deletar(Connection con, Integer... matriculasOrigens) throws Exception {
        String mats = "";
        for (Integer mat : matriculasOrigens) {
            mats += "," + mat;
        }
        mats = mats.replaceFirst(",", "");
        String whereCliente = "where cliente IN (SELECT CODIGO FROM cliente WHERE codigomatricula IN (" + mats + "))";
        SuperFacadeJDBC.executarConsulta("DELETE FROM risco "+whereCliente, con);
        SuperFacadeJDBC.executarConsulta("DELETE FROM cliente WHERE codigomatricula IN (" + mats + ");", con);

    }
    public static void main(String ... args) throws SQLException{
        Connection con = DriverManager.getConnection("******************************************************", "postgres", "pactodb");
        try {
            //        1.ANA CRISTINA DE ALMEIDA COUTINHO BANDEIRA
            con.setAutoCommit(false);
            transferir(con, 23558, 9279, 13909);
            deletar(con, 9279, 13909);
//            2.ANA LUCIA DE CASTRO LOUREIRO
            transferir(con, 10421,13935);
            deletar(con, 13935);
//3.ANDERSON OLIVEIRA DA SILVA
            transferir(con, 11142,5470,13936);
            deletar(con, 5470,13936);
//4.CARLA BORGES SETTE
            transferir(con, 13824,9099);
            deletar(con, 9099);
//5.FERNANDA PINHEIRO GROSS
            transferir(con, 12385, 14095);
            deletar(con, 14095);
//6.LUCIANA LYRA WANDERLEY MASCARENHAS
            transferir(con, 11098,13860);
            deletar(con, 13860);
//7.MARIA CLARA KREMER FALLER
            transferir(con, 11916,14010);
            deletar(con, 14010);
//8.MARIA HELENA MARECHAL
            transferir(con, 14090,12350);
            deletar(con, 12350);
//9.MARTHA FROHMULLER
            transferir(con, 11984,23342);
            deletar(con, 23342);
//10.PATRICIA COHEN
            transferir(con, 11737,14066);
            deletar(con, 14066);
//11.PEDRO HENRIQUE DE PARANAGUA
            transferir(con, 11905,13973);
            deletar(con, 13973);
//12.RUDOLF HEFTI
            transferir(con, 8180, 14048);
            deletar(con, 14048);
//13.VALDIR DA COSTA REIS
            transferir(con, 6003,13822);
            deletar(con, 13822);
//14.MARIANA MOSTARDEIR
            transferir(con, 23477, 21803);
            deletar(con, 21803);
//15.VERA LUCIA SENNA D'ALMEIDA
            transferir(con, 23479,2261);
            deletar(con, 2261);
            con.commit();
        } catch (Exception e) {
            e.printStackTrace();
            con.rollback();
        }finally{
            con.setAutoCommit(true);
        }
        
    }
}
