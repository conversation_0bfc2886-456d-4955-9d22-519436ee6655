package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.contrato.MovProdutoModalidade;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.utilitarias.Conexao;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAlterarContratoDuracao {

    public static void main(String... args) {
        try (Connection connection = DriverManager.getConnection("**************************************************************", "postgres", "pactodb")){
            // CRIAR TABELA PARA ARMAZENAR O LOG DA EXECUÇÃO DO PROCESSO
            criarTabelaResultadoAlterarContratoIronberg(connection);
            passarParaQuadrimestral(connection);
            passarParaSemestral(connection);
            passarParaAnual(connection);

        } catch (Exception ex) {
            Logger.getLogger(ProcessoAlterarContratoDuracao.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void criarTabelaResultadoAlterarContratoIronberg(Connection con){
        try{
            SuperFacadeJDBC.executarUpdate("create table ResultadoAlterarContratoIronberg (tipo varchar(20), matriculacliente int, contrato int, descricao text)", con);
        } catch (Exception ex) {
            ex.printStackTrace();
            System.out.println(ex.getMessage());
        }
    }

    public static void passarParaQuadrimestral(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        String sql = "SELECT\n" +
                "\tcon.codigo AS contrato,\n" +
                "\tcli.codigomatricula,\n" +
                "\tcon.situacaocontrato,\n" +
                "\tcon.pessoa,\n" +
                "\tcon.datamatricula,\n" +
                "\tcon.consultor,\n" +
                "\tcrc.diavencimentocartao,\n" +
                "\tcon.plano,\n" +
                "\tprc.valormensal,\n" +
                "\tpln.produtopadraogerarparcelascontrato,\n" +
                "\tcdr.numeromeses,\n" +
                "\tcon.renovavelautomaticamente,\n" +
                "\tpln.renovavelautomaticamente ,\n" +
                "\tcon.vigenciaateajustada,\n" +
                "\tcon.vigenciaate,\n" +
                "\tpes.nome\n" +
                "FROM\n" +
                "\tcontrato con\n" +
                "INNER JOIN cliente cli ON\n" +
                "\tcli.pessoa = con.pessoa\n" +
                "INNER JOIN pessoa pes ON\n" +
                "\tcli.pessoa = pes.codigo\n" +
                "INNER JOIN contratorecorrencia crc ON\n" +
                "\tcrc.contrato = con.codigo\n" +
                "INNER JOIN plano pln ON\n" +
                "\tpln.codigo = con.plano\n" +
                "INNER JOIN planorecorrencia prc ON\n" +
                "\tprc.plano = pln.codigo\n" +
                "INNER JOIN contratoduracao cdr ON\n" +
                "\tcdr.contrato = con.codigo\n" +
                "WHERE\n" +
                "\t1=1 \n" +
                "\tAND (con.contratoresponsavelrenovacaomatricula IS NULL\n" +
                "\t\tOR con.contratoresponsavelrenovacaomatricula = 0)\n" +
                "\tAND con.situacao = 'AT'\n" +
                "\tAND pln.recorrencia = TRUE\n" +
                "\tAND prc.renovavelautomaticamente\n" +
                "\tAND cdr.numeromeses = 1\n" +
                "\tAND NOT EXISTS (\n" +
                "\tSELECT\n" +
                "\t\tcodigo\n" +
                "\tFROM\n" +
                "\t\tcontratooperacao co\n" +
                "\tWHERE\n" +
                "\t\tco.contrato = con.codigo\n" +
                "\t\tAND co.tipooperacao = 'CA')\n" +
                "\tAND prc.duracaoplano = 4\n" +
                "ORDER BY\n" +
                "\t1";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sql, con);
        MovProduto movProdutoDAO = new MovProduto(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        MovProdutoModalidade mpmDAO = new MovProdutoModalidade(con);
        ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
        List<MovProdutoVO> produtoContrato = new ArrayList<MovProdutoVO>();
        List<MovProdutoModalidadeVO> movProdModalidade = new ArrayList<MovProdutoModalidadeVO>();
        MovParcelaVO parcelaContrato;
        MovProdutoParcelaVO movPPVO;
        String resultadoAlteracoes = "";
        int count = 0;
        while (consulta.next()) {
            con.setAutoCommit(false);
            try {
                int numeroMeses = 1;
                String situacaoContrato = consulta.getString("situacaocontrato");
                int contratoAvaliar = consulta.getInt("contrato");
                while (situacaoContrato.equals("RN")){
                    ResultSet consultaContratoAnterior = SuperFacadeJDBC.criarConsulta("select c.codigo, c.situacaocontrato, cd.numeromeses from contrato c inner join contratoduracao cd on c.codigo = cd.contrato where c.contratoresponsavelrenovacaomatricula = "+contratoAvaliar, con);
                    if(consultaContratoAnterior.next()){
                        situacaoContrato = consultaContratoAnterior.getString("situacaocontrato");
                        numeroMeses += consultaContratoAnterior.getInt("numeromeses");
                        contratoAvaliar = consultaContratoAnterior.getInt("codigo");
                    } else {
                        throw new Exception("Contrato anterior não encontrado do contrato: "+consulta.getInt("contrato"));
                    }
                }
                if(numeroMeses >= 4){
                    resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - não foi alterado pois tem "+numeroMeses+" meses de vigência. No caso, o contrato com a nova duração será lançado durante a sua renovação automática.\n";
                    SuperFacadeJDBC.executarUpdate("UPDATE contratoduracao SET nrmaximoparcelascondpagamento = 4,numeromeses = 4 WHERE contrato = "+ consulta.getInt("contrato") + ";", con);
                    con.commit();
                    continue;
                }
                Double valorMensal = consulta.getDouble("valormensal");
                int indice = 1;
                int mesesAdicionar = 4 - numeroMeses;
                Date novaVigencia = Uteis.somarCampoData(consulta.getDate("vigenciaateajustada"), Calendar.MONTH,  mesesAdicionar);
                Date vigenciaAteAjustadaNovoContrato = Uteis.somarCampoData(consulta.getDate("vigenciaateajustada"), Calendar.MONTH,  mesesAdicionar);
                Date vigenciaDeNovoContrato = Uteis.somarDias(consulta.getDate("vigenciaateajustada"), 1);
                Double valorFinalNovoContrato = valorMensal * mesesAdicionar;

                String sqlInsertContrato = "INSERT INTO contrato (\n" +
                        "  bolsa, naopermitirrenovacaorematriculadecontratoanteriores, \n" +
                        "  valordescontoespecifico, valordescontoporcentagem, \n" +
                        "  nomemodalidades, somaproduto, contratoresponsavelrenovacaomatricula, \n" +
                        "  contratoresponsavelrematriculamatricula, \n" +
                        "  datalancamento, datamatricula, dataprevistarenovar, \n" +
                        "  datarenovarrealizada, dataprevistarematricula, \n" +
                        "  datarematricularealizada, situacaocontrato, \n" +
                        "  situacaorenovacao, situacaorematricula, \n" +
                        "  contratobaseadorenovacao, contratobaseadorematricula, \n" +
                        "  pagarcomboleto, responsavelcontrato, \n" +
                        "  observacao, responsavelliberacaocondicaopagamento, \n" +
                        "  valorfinal, valorbasecalculo, vigenciaate, \n" +
                        "  vigenciaateajustada, vigenciade, \n" +
                        "  estendecoberturafamiliares, situacao, \n" +
                        "  plano, pessoa, consultor, empresa, \n" +
                        "  conveniodesconto, dividirprodutosnasparcelas, \n" +
                        "  desconto, tipodesconto, valordesconto, \n" +
                        "  diavencimentoprorata, dataalteracaomanual, \n" +
                        "  responsaveldatabase, regimerecorrencia, \n" +
                        "  origemcontrato, importacao, valorarredondamento, \n" +
                        "  renovavelautomaticamente, quantidademaximafrequencia, \n" +
                        "  vendacreditotreino, numerocupomdesconto, \n" +
                        "  id_externo, crossfit, permiterenovacaoautomatica, \n" +
                        "  origemsistema, grupo, nomeconveniodesconto, \n" +
                        "  valorconveniodesconto, percentualconveniodesconto, \n" +
                        "  idexterno, vendacreditosessao, cancelamentointegrado, \n" +
                        "  contratoorigemtransferencia, valorbasecalculomanutencaomodalidade, \n" +
                        "  dataenviocontrato, emailrecebimento, \n" +
                        "  ipassinaturacontrato, dataassinaturacontrato, \n" +
                        "  vendavitio, evento, pessoaoriginal, \n" +
                        "  primeirocontratobaseadorenovacao, \n" +
                        "  dataassinaturacancelamento, xnumpro, \n" +
                        "  valorbasenegociado, datasincronizacaointegracaofoguete\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    false, false, 0.0, 0.0, 'M', 0.0, 0, 0, \n" +
                        "    '" + Uteis.getDataJDBC(Calendario.hoje()) + "', '" + Uteis.getDataJDBC(consulta.getDate("datamatricula")) + "', \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', NULL, \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', NULL, \n" +
                        "    'RN', '', '', "+ consulta.getInt("contrato") +", 0, false, 3, 'CONTRATO GERADO PELO AJUSTE GCM-399', \n" +
                        "    NULL, "+valorFinalNovoContrato+", "+valorFinalNovoContrato+", '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', '" + Uteis.getDataJDBC(vigenciaDeNovoContrato) + "', \n" +
                        "    false, 'AT', "+consulta.getInt("plano")+", "+consulta.getInt("pessoa")+", "+consulta.getInt("consultor")+", 1, NULL, \n" +
                        "    false, NULL, '', NULL, 0, NULL, NULL, \n" +
                        "    true, 1, false, 0.0, false, 7, false, \n" +
                        "    NULL, NULL, false, true, 1, NULL, NULL, \n" +
                        "    NULL, NULL, NULL, false, false, 0, NULL, \n" +
                        "    NULL, NULL, NULL, NULL, false, NULL, \n" +
                        "    NULL, NULL, NULL, NULL, "+valorFinalNovoContrato+", NULL\n" +
                        "  );";

                SuperFacadeJDBC.executarUpdate(sqlInsertContrato, con);
                SuperFacadeJDBC.executarUpdate("UPDATE contrato SET contratoresponsavelrenovacaomatricula = (SELECT MAX(codigo) FROM contrato WHERE pessoa = " +consulta.getInt("pessoa")+ ") WHERE codigo = "+ consulta.getInt("contrato") + ";", con);

                Integer codigoContratoGerado = 0;
                ResultSet consultaCodigoContratoGerado = SuperFacadeJDBC.criarConsulta("SELECT MAX(codigo) as codigoContrato FROM contrato WHERE pessoa = " +consulta.getInt("pessoa"), con);
                if (consultaCodigoContratoGerado.next()){
                    codigoContratoGerado = consultaCodigoContratoGerado.getInt("codigoContrato");
                }

                if (UteisValidacao.emptyNumber(codigoContratoGerado)){
                    throw new Exception("Erro ao gerar novo contrato com renovação proporcional.");
                }


                String sqlInserirContratoRecorrencia = "INSERT INTO contratorecorrencia (\n" +
                        "  contrato, pessoa, fidelidade, numerocartao, \n" +
                        "  diavencimentocartao, ultimatransacaoaprovada, \n" +
                        "  valormensal, diasbloqueioacesso, \n" +
                        "  diascancelamentoautomatico, renovavelautomaticamente, \n" +
                        "  valoranuidade, diavencimentoanuidade, \n" +
                        "  mesvencimentoanuidade, datainutilizada, \n" +
                        "  anuidadenaparcela, parcelaanuidade, \n" +
                        "  cancelamentoproporcional, qtddiascobrarproximaparcela, \n" +
                        "  qtddiascobraranuidadetotal, parcelaranuidade, \n" +
                        "  cancelamentoproporcionalsomenterenovacao, \n" +
                        "  valormensalnegociado\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    "+codigoContratoGerado+", "+consulta.getInt("pessoa")+", 4, '', "+consulta.getInt("diavencimentocartao")+", '', "+valorMensal+", 0, 0, \n" +
                        "    true, 0.0, 1, 1, NULL, false, 0, false, \n" +
                        "    0, 0, false, false, "+valorMensal+"\n" +
                        "  );";
                SuperFacadeJDBC.executarUpdate(sqlInserirContratoRecorrencia, con);

                String sqlInsertContratoCondicaoPagamento = "INSERT INTO contratocondicaopagamento (\n" +
                        "  percentualdesconto, condicaopagamento, \n" +
                        "  tipooperacao, tipovalor, valorespecifico, \n" +
                        "  contrato\n" +
                        ") \n" +
                        "  (SELECT 0.0, codigo, '', '', 0.0, "+codigoContratoGerado+" FROM condicaopagamento WHERE descricao ILIKE '%EM 4 VEZES%RECORR%' LIMIT 1)";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoCondicaoPagamento, con);

                String sqlInsertContratoDuracao = "INSERT INTO contratoduracao (\n" +
                        "  valordesejadomensal, valordesejadoparcela, \n" +
                        "  valordesejado, tipooperacao, tipovalor, \n" +
                        "  valorespecifico, percentualdesconto, \n" +
                        "  nrmaximoparcelascondpagamento, \n" +
                        "  contrato, numeromeses, carencia, \n" +
                        "  quantidadediasextra, carenciaalterada\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    "+valorMensal+", "+valorMensal+", "+valorFinalNovoContrato+", 'RE', 'VE', 0.0, \n" +
                        "    0.0, 4, "+codigoContratoGerado+", 4, 0, 0, false\n" +
                        "  );";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoDuracao, con);

                String sqlInsertContratoHorario = "INSERT INTO contratohorario (\n" +
                        "  tipooperacao, tipovalor, valorespecifico, \n" +
                        "  percentualdesconto, contrato, horario\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  ('', '', 0.0, 0.0, "+codigoContratoGerado+", 1);";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoHorario, con);

                String sqlInsertContratoPlanoProdutoSugerido = "INSERT INTO contratoplanoprodutosugerido (\n" +
                        "  valorfinalproduto, contrato, planoprodutosugerido\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (0.0, "+codigoContratoGerado+", 52);";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoPlanoProdutoSugerido, con);

                String sqlInsertContratoModalidade = "INSERT INTO contratomodalidade (\n" +
                        "  vezessemana, valormodalidade, valorfinalmodalidade, \n" +
                        "  valorbasecalculo, modalidade, contrato\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (7, "+valorMensal+", "+valorMensal+", NULL, 2, "+codigoContratoGerado+");";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoModalidade, con);

                String sqlInsertContratoModalidadeVezesSemana = "INSERT INTO contratomodalidadevezessemana (nrvezes, contratomodalidade) (\n" +
                        "  SELECT \n" +
                        "    7, \n" +
                        "    MAX(codigo) \n" +
                        "  FROM \n" +
                        "    contratomodalidade\n" +
                        ")";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoModalidadeVezesSemana, con);

                String sqlInsertHistoricoContrato = "INSERT INTO historicocontrato (\n" +
                        "  datafinalsituacao, tipohistorico, \n" +
                        "  datainiciosituacao, situacaorelativahistorico, \n" +
                        "  dataregistro, responsavelliberacaomudancahistorico, \n" +
                        "  responsavelregistro, descricao, \n" +
                        "  contrato, retornomanual, datainiciotemporal\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    '"+Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato)+"', 'RN', \n" +
                        "    '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', '', '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', \n" +
                        "    NULL, 3, 'RENOVADO', "+codigoContratoGerado+", false, \n" +
                        "    NULL\n" +
                        "  );\n";
                SuperFacadeJDBC.executarUpdate(sqlInsertHistoricoContrato, con);

                String sqlInsertPeriodoAcessoCliente = "INSERT INTO periodoacessocliente (\n" +
                        "  tipoacesso, datafinalacesso, datainicioacesso, \n" +
                        "  aulaavulsadiaria, contrato, contratobaseadorenovacao, \n" +
                        "  pessoa, responsavel, datalancamento, \n" +
                        "  tokengympass, reposicao, tipogympass, \n" +
                        "  valorgympass, tipototalpass, tokengogood, \n" +
                        "  produto\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    'CA', '"+Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato)+"', \n" +
                        "    '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', NULL, \n" +
                        "    "+codigoContratoGerado+", "+consulta.getInt("contrato")+", "+consulta.getInt("pessoa")+", NULL, '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', \n" +
                        "    '', NULL, '', 0.0, false, NULL, NULL\n" +
                        "  );\n";
                SuperFacadeJDBC.executarUpdate(sqlInsertPeriodoAcessoCliente, con);


                produtoContrato = movProdutoDAO.consultar("select * from movproduto where contrato = "+consulta.getInt("contrato")+" and produto = "+consulta.getInt("produtopadraogerarparcelascontrato")+" order by codigo desc limit 1 ", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                parcelaContrato = movParcelaDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                movProdModalidade = mpmDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo(),  Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                Date novaVigenciaAjustada = novaVigencia;
                if(!Calendario.igual(consulta.getDate("vigenciaate"),consulta.getDate("vigenciaateajustada")) ){
                    int diferenca = (int) (Uteis.nrDiasEntreDatas(consulta.getDate("vigenciaate"), consulta.getDate("vigenciaateajustada")) );
                    novaVigenciaAjustada = Uteis.somarDias(novaVigencia, diferenca);
                }
                Date competencia = produtoContrato.get(0).getDataInicioVigencia();
                if(Calendario.menor(Uteis.obterDataFuturaParcela(competencia, 1), Calendario.hoje())){
                    competencia =  Uteis.somarMeses(Calendario.hoje(), -1);
                }
                Date vencimentoBase = parcelaContrato.getDataVencimento();
                int mesAtual = 1;
                while(indice <= mesesAdicionar ){
                    competencia = Uteis.obterDataFuturaParcela(competencia, 1);
                    Date vencimento =  Uteis.obterDataFuturaParcela(vencimentoBase, mesAtual++);
                    parcelaContrato.setDataVencimento(vencimento);
                    parcelaContrato.setValorParcela(valorMensal);
                    parcelaContrato.setDescricao("PARCELA "+(indice+ 1));
                    parcelaContrato.setSituacao("EA");
                    parcelaContrato.getContrato().setCodigo(codigoContratoGerado);
                    parcelaContrato.getContrato().setRegimeRecorrencia(true);
                    movParcelaDAO.incluirSemCommit(parcelaContrato);
                    produtoContrato.get(0).setAnoReferencia(Uteis.getAnoData(competencia));
                    produtoContrato.get(0).setMesReferencia(Uteis.getMesReferenciaData(competencia));
                    produtoContrato.get(0).setDescricao(produtoContrato.get(0).getDescricao().substring(0, (produtoContrato.get(0).getDescricao().length() - 7))+ Uteis.getMesReferenciaData(competencia)) ;
                    produtoContrato.get(0).setPrecoUnitario(valorMensal);
                    produtoContrato.get(0).setTotalFinal(valorMensal);
                    produtoContrato.get(0).setValorDesconto(0.0);
                    produtoContrato.get(0).setSituacao("EA");
                    produtoContrato.get(0).setMovProdutoModalidades(movProdModalidade);
                    produtoContrato.get(0).getContrato().setCodigo(codigoContratoGerado);
                    produtoContrato.get(0).getContrato().setVigenciaDe(vigenciaDeNovoContrato);
                    produtoContrato.get(0).getContrato().setVigenciaAteAjustada(vigenciaAteAjustadaNovoContrato);
                    produtoContrato.get(0).getContrato().setVigenciaAte(vigenciaAteAjustadaNovoContrato);
                    produtoContrato.get(0).getContrato().setRegimeRecorrencia(true);
                    movProdutoDAO.incluirSemCommit(produtoContrato.get(0));
                    movPPVO = new MovProdutoParcelaVO();
                    movPPVO.setMovParcela(parcelaContrato.getCodigo());
                    movPPVO.setMovProdutoVO(produtoContrato.get(0));
                    movPPVO.setMovProduto(produtoContrato.get(0).getCodigo());
                    movPPVO.setValorPago(valorMensal);
                    mppDAO.incluir(movPPVO);
                    indice++;
                }

                resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - foi gerado um novo Contrato com Duração de " + mesesAdicionar +" - Código do contrato Gerado:" + codigoContratoGerado +"\n";
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve sua vigencia  alterada de   " + Uteis.getData(consulta.getDate("vigenciaateajustada")) +" para "+ Uteis.getData(novaVigenciaAjustada));
                con.commit();
                ClienteVO cliente = zillyonWebFacade.getCliente().consultarPorCodigoPessoa(consulta.getInt("pessoa"), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                zillyonWebFacade.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            } catch (Exception e){
                con.rollback();
                resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - teve erro na alteração:  + " + e.getMessage()+"\n";
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve erro na alteração:  " + e.getMessage());
            } finally{
                SuperFacadeJDBC.executarUpdate("INSERT INTO ResultadoAlterarContratoIronberg (tipo, matriculacliente, contrato, descricao) VALUES ('QUADRIMESTRAL', "+consulta.getInt("codigomatricula")+", "+consulta.getInt("contrato")+",'"+resultadoAlteracoes+"')", con);
                con.setAutoCommit(true);
            }
        }
    }

    public static void passarParaSemestral(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        String sql = "SELECT\n" +
                "\tcon.codigo AS contrato,\n" +
                "\tcli.codigomatricula,\n" +
                "\tcon.situacaocontrato,\n" +
                "\tcon.pessoa,\n" +
                "\tcon.datamatricula,\n" +
                "\tcon.consultor,\n" +
                "\tcrc.diavencimentocartao,\n" +
                "\tcon.plano,\n" +
                "\tprc.valormensal,\n" +
                "\tpln.produtopadraogerarparcelascontrato,\n" +
                "\tcdr.numeromeses,\n" +
                "\tcon.renovavelautomaticamente,\n" +
                "\tpln.renovavelautomaticamente ,\n" +
                "\tcon.vigenciaateajustada,\n" +
                "\tcon.vigenciaate,\n" +
                "\tpes.nome\n" +
                "FROM\n" +
                "\tcontrato con\n" +
                "INNER JOIN cliente cli ON\n" +
                "\tcli.pessoa = con.pessoa\n" +
                "INNER JOIN pessoa pes ON\n" +
                "\tcli.pessoa = pes.codigo\n" +
                "INNER JOIN contratorecorrencia crc ON\n" +
                "\tcrc.contrato = con.codigo\n" +
                "INNER JOIN plano pln ON\n" +
                "\tpln.codigo = con.plano\n" +
                "INNER JOIN planorecorrencia prc ON\n" +
                "\tprc.plano = pln.codigo\n" +
                "INNER JOIN contratoduracao cdr ON\n" +
                "\tcdr.contrato = con.codigo\n" +
                "WHERE\n" +
                "\t1=1 \n" +
                "\tAND (con.contratoresponsavelrenovacaomatricula IS NULL\n" +
                "\t\tOR con.contratoresponsavelrenovacaomatricula = 0)\n" +
                "\tAND con.situacao = 'AT'\n" +
                "\tAND pln.recorrencia = TRUE\n" +
                "\tAND prc.renovavelautomaticamente\n" +
                "\tAND cdr.numeromeses = 1\n" +
                "\tAND NOT EXISTS (\n" +
                "\tSELECT\n" +
                "\t\tcodigo\n" +
                "\tFROM\n" +
                "\t\tcontratooperacao co\n" +
                "\tWHERE\n" +
                "\t\tco.contrato = con.codigo\n" +
                "\t\tAND co.tipooperacao = 'CA')\n" +
                "\tAND prc.duracaoplano = 6\n" +
                "ORDER BY\n" +
                "\t1";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sql, con);
        MovProduto movProdutoDAO = new MovProduto(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        MovProdutoModalidade mpmDAO = new MovProdutoModalidade(con);
        ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
        List<MovProdutoVO> produtoContrato = new ArrayList<MovProdutoVO>();
        List<MovProdutoModalidadeVO> movProdModalidade = new ArrayList<MovProdutoModalidadeVO>();
        MovParcelaVO parcelaContrato;
        MovProdutoParcelaVO movPPVO;
        String resultadoAlteracoes = "";
        int count = 0;
        while (consulta.next()) {
            con.setAutoCommit(false);
            try {
                int numeroMeses = 1;
                String situacaoContrato = consulta.getString("situacaocontrato");
                int contratoAvaliar = consulta.getInt("contrato");
                while (situacaoContrato.equals("RN")){
                    ResultSet consultaContratoAnterior = SuperFacadeJDBC.criarConsulta("select c.codigo, c.situacaocontrato, cd.numeromeses from contrato c inner join contratoduracao cd on c.codigo = cd.contrato where c.contratoresponsavelrenovacaomatricula = "+contratoAvaliar, con);
                    if(consultaContratoAnterior.next()){
                        situacaoContrato = consultaContratoAnterior.getString("situacaocontrato");
                        numeroMeses += consultaContratoAnterior.getInt("numeromeses");
                        contratoAvaliar = consultaContratoAnterior.getInt("codigo");
                    } else {
                        throw new Exception("Contrato anterior não encontrado do contrato: "+consulta.getInt("contrato"));
                    }
                }
                if(numeroMeses >= 6){
                    resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - não foi alterado pois tem "+numeroMeses+" meses de vigência. No caso, o contrato com a nova duração será lançado durante a sua renovação automática.\n";
                    SuperFacadeJDBC.executarUpdate("UPDATE contratoduracao SET nrmaximoparcelascondpagamento = 6,numeromeses = 6 WHERE contrato = "+ consulta.getInt("contrato") + ";", con);
                    con.commit();
                    continue;
                }
                Double valorMensal = consulta.getDouble("valormensal");
                int indice = 1;
                int mesesAdicionar = 6 - numeroMeses;
                Date novaVigencia = Uteis.somarCampoData(consulta.getDate("vigenciaateajustada"), Calendar.MONTH,  mesesAdicionar);
                Date vigenciaAteAjustadaNovoContrato = Uteis.somarCampoData(consulta.getDate("vigenciaateajustada"), Calendar.MONTH,  mesesAdicionar);
                Date vigenciaDeNovoContrato = Uteis.somarDias(consulta.getDate("vigenciaateajustada"), 1);
                Double valorFinalNovoContrato = valorMensal * mesesAdicionar;

                String sqlInsertContrato = "INSERT INTO contrato (\n" +
                        "  bolsa, naopermitirrenovacaorematriculadecontratoanteriores, \n" +
                        "  valordescontoespecifico, valordescontoporcentagem, \n" +
                        "  nomemodalidades, somaproduto, contratoresponsavelrenovacaomatricula, \n" +
                        "  contratoresponsavelrematriculamatricula, \n" +
                        "  datalancamento, datamatricula, dataprevistarenovar, \n" +
                        "  datarenovarrealizada, dataprevistarematricula, \n" +
                        "  datarematricularealizada, situacaocontrato, \n" +
                        "  situacaorenovacao, situacaorematricula, \n" +
                        "  contratobaseadorenovacao, contratobaseadorematricula, \n" +
                        "  pagarcomboleto, responsavelcontrato, \n" +
                        "  observacao, responsavelliberacaocondicaopagamento, \n" +
                        "  valorfinal, valorbasecalculo, vigenciaate, \n" +
                        "  vigenciaateajustada, vigenciade, \n" +
                        "  estendecoberturafamiliares, situacao, \n" +
                        "  plano, pessoa, consultor, empresa, \n" +
                        "  conveniodesconto, dividirprodutosnasparcelas, \n" +
                        "  desconto, tipodesconto, valordesconto, \n" +
                        "  diavencimentoprorata, dataalteracaomanual, \n" +
                        "  responsaveldatabase, regimerecorrencia, \n" +
                        "  origemcontrato, importacao, valorarredondamento, \n" +
                        "  renovavelautomaticamente, quantidademaximafrequencia, \n" +
                        "  vendacreditotreino, numerocupomdesconto, \n" +
                        "  id_externo, crossfit, permiterenovacaoautomatica, \n" +
                        "  origemsistema, grupo, nomeconveniodesconto, \n" +
                        "  valorconveniodesconto, percentualconveniodesconto, \n" +
                        "  idexterno, vendacreditosessao, cancelamentointegrado, \n" +
                        "  contratoorigemtransferencia, valorbasecalculomanutencaomodalidade, \n" +
                        "  dataenviocontrato, emailrecebimento, \n" +
                        "  ipassinaturacontrato, dataassinaturacontrato, \n" +
                        "  vendavitio, evento, pessoaoriginal, \n" +
                        "  primeirocontratobaseadorenovacao, \n" +
                        "  dataassinaturacancelamento, xnumpro, \n" +
                        "  valorbasenegociado, datasincronizacaointegracaofoguete\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    false, false, 0.0, 0.0, 'M', 0.0, 0, 0, \n" +
                        "    '" + Uteis.getDataJDBC(Calendario.hoje()) + "', '" + Uteis.getDataJDBC(consulta.getDate("datamatricula")) + "', \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', NULL, \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', NULL, \n" +
                        "    'RN', '', '', "+ consulta.getInt("contrato") +", 0, false, 3, 'CONTRATO GERADO PELO AJUSTE GCM-399', \n" +
                        "    NULL, "+valorFinalNovoContrato+", "+valorFinalNovoContrato+", '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', '" + Uteis.getDataJDBC(vigenciaDeNovoContrato) + "', \n" +
                        "    false, 'AT', "+consulta.getInt("plano")+", "+consulta.getInt("pessoa")+", "+consulta.getInt("consultor")+", 1, NULL, \n" +
                        "    false, NULL, '', NULL, 0, NULL, NULL, \n" +
                        "    true, 1, false, 0.0, false, 7, false, \n" +
                        "    NULL, NULL, false, true, 1, NULL, NULL, \n" +
                        "    NULL, NULL, NULL, false, false, 0, NULL, \n" +
                        "    NULL, NULL, NULL, NULL, false, NULL, \n" +
                        "    NULL, NULL, NULL, NULL, "+valorFinalNovoContrato+", NULL\n" +
                        "  );";

                SuperFacadeJDBC.executarUpdate(sqlInsertContrato, con);
                SuperFacadeJDBC.executarUpdate("UPDATE contrato SET contratoresponsavelrenovacaomatricula = (SELECT MAX(codigo) FROM contrato WHERE pessoa = " +consulta.getInt("pessoa")+ ") WHERE codigo = "+ consulta.getInt("contrato") + ";", con);

                Integer codigoContratoGerado = 0;
                ResultSet consultaCodigoContratoGerado = SuperFacadeJDBC.criarConsulta("SELECT MAX(codigo) as codigoContrato FROM contrato WHERE pessoa = " +consulta.getInt("pessoa"), con);
                if (consultaCodigoContratoGerado.next()){
                    codigoContratoGerado = consultaCodigoContratoGerado.getInt("codigoContrato");
                }

                if (UteisValidacao.emptyNumber(codigoContratoGerado)){
                    throw new Exception("Erro ao gerar novo contrato com renovação proporcional.");
                }


                String sqlInserirContratoRecorrencia = "INSERT INTO contratorecorrencia (\n" +
                        "  contrato, pessoa, fidelidade, numerocartao, \n" +
                        "  diavencimentocartao, ultimatransacaoaprovada, \n" +
                        "  valormensal, diasbloqueioacesso, \n" +
                        "  diascancelamentoautomatico, renovavelautomaticamente, \n" +
                        "  valoranuidade, diavencimentoanuidade, \n" +
                        "  mesvencimentoanuidade, datainutilizada, \n" +
                        "  anuidadenaparcela, parcelaanuidade, \n" +
                        "  cancelamentoproporcional, qtddiascobrarproximaparcela, \n" +
                        "  qtddiascobraranuidadetotal, parcelaranuidade, \n" +
                        "  cancelamentoproporcionalsomenterenovacao, \n" +
                        "  valormensalnegociado\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    "+codigoContratoGerado+", "+consulta.getInt("pessoa")+", 6, '', "+consulta.getInt("diavencimentocartao")+", '', "+valorMensal+", 0, 0, \n" +
                        "    true, 0.0, 1, 1, NULL, false, 0, false, \n" +
                        "    0, 0, false, false, "+valorMensal+"\n" +
                        "  );";
                SuperFacadeJDBC.executarUpdate(sqlInserirContratoRecorrencia, con);

                String sqlInsertContratoCondicaoPagamento = "INSERT INTO contratocondicaopagamento (\n" +
                        "  percentualdesconto, condicaopagamento, \n" +
                        "  tipooperacao, tipovalor, valorespecifico, \n" +
                        "  contrato\n" +
                        ") \n" +
                        "  (SELECT 0.0, codigo, '', '', 0.0, "+codigoContratoGerado+" FROM condicaopagamento WHERE descricao ILIKE '%EM 6 VEZES%RECORR%' LIMIT 1)";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoCondicaoPagamento, con);

                String sqlInsertContratoDuracao = "INSERT INTO contratoduracao (\n" +
                        "  valordesejadomensal, valordesejadoparcela, \n" +
                        "  valordesejado, tipooperacao, tipovalor, \n" +
                        "  valorespecifico, percentualdesconto, \n" +
                        "  nrmaximoparcelascondpagamento, \n" +
                        "  contrato, numeromeses, carencia, \n" +
                        "  quantidadediasextra, carenciaalterada\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    "+valorMensal+", "+valorMensal+", "+valorFinalNovoContrato+", 'RE', 'VE', 0.0, \n" +
                        "    0.0, 6, "+codigoContratoGerado+", 6, 0, 0, false\n" +
                        "  );";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoDuracao, con);

                String sqlInsertContratoHorario = "INSERT INTO contratohorario (\n" +
                        "  tipooperacao, tipovalor, valorespecifico, \n" +
                        "  percentualdesconto, contrato, horario\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  ('', '', 0.0, 0.0, "+codigoContratoGerado+", 1);";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoHorario, con);

                String sqlInsertContratoPlanoProdutoSugerido = "INSERT INTO contratoplanoprodutosugerido (\n" +
                        "  valorfinalproduto, contrato, planoprodutosugerido\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (0.0, "+codigoContratoGerado+", 52);";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoPlanoProdutoSugerido, con);

                String sqlInsertContratoModalidade = "INSERT INTO contratomodalidade (\n" +
                        "  vezessemana, valormodalidade, valorfinalmodalidade, \n" +
                        "  valorbasecalculo, modalidade, contrato\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (7, "+valorMensal+", "+valorMensal+", NULL, 2, "+codigoContratoGerado+");";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoModalidade, con);

                String sqlInsertContratoModalidadeVezesSemana = "INSERT INTO contratomodalidadevezessemana (nrvezes, contratomodalidade) (\n" +
                        "  SELECT \n" +
                        "    7, \n" +
                        "    MAX(codigo) \n" +
                        "  FROM \n" +
                        "    contratomodalidade\n" +
                        ")";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoModalidadeVezesSemana, con);

                String sqlInsertHistoricoContrato = "INSERT INTO historicocontrato (\n" +
                        "  datafinalsituacao, tipohistorico, \n" +
                        "  datainiciosituacao, situacaorelativahistorico, \n" +
                        "  dataregistro, responsavelliberacaomudancahistorico, \n" +
                        "  responsavelregistro, descricao, \n" +
                        "  contrato, retornomanual, datainiciotemporal\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    '"+Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato)+"', 'RN', \n" +
                        "    '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', '', '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', \n" +
                        "    NULL, 3, 'RENOVADO', "+codigoContratoGerado+", false, \n" +
                        "    NULL\n" +
                        "  );\n";
                SuperFacadeJDBC.executarUpdate(sqlInsertHistoricoContrato, con);

                String sqlInsertPeriodoAcessoCliente = "INSERT INTO periodoacessocliente (\n" +
                        "  tipoacesso, datafinalacesso, datainicioacesso, \n" +
                        "  aulaavulsadiaria, contrato, contratobaseadorenovacao, \n" +
                        "  pessoa, responsavel, datalancamento, \n" +
                        "  tokengympass, reposicao, tipogympass, \n" +
                        "  valorgympass, tipototalpass, tokengogood, \n" +
                        "  produto\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    'CA', '"+Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato)+"', \n" +
                        "    '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', NULL, \n" +
                        "    "+codigoContratoGerado+", "+consulta.getInt("contrato")+", "+consulta.getInt("pessoa")+", NULL, '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', \n" +
                        "    '', NULL, '', 0.0, false, NULL, NULL\n" +
                        "  );\n";
                SuperFacadeJDBC.executarUpdate(sqlInsertPeriodoAcessoCliente, con);


                produtoContrato = movProdutoDAO.consultar("select * from movproduto where contrato = "+consulta.getInt("contrato")+" and produto = "+consulta.getInt("produtopadraogerarparcelascontrato")+" order by codigo desc limit 1 ", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                parcelaContrato = movParcelaDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                movProdModalidade = mpmDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo(),  Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                Date novaVigenciaAjustada = novaVigencia;
                if(!Calendario.igual(consulta.getDate("vigenciaate"),consulta.getDate("vigenciaateajustada")) ){
                    int diferenca = (int) (Uteis.nrDiasEntreDatas(consulta.getDate("vigenciaate"), consulta.getDate("vigenciaateajustada")) );
                    novaVigenciaAjustada = Uteis.somarDias(novaVigencia, diferenca);
                }
                Date competencia = produtoContrato.get(0).getDataInicioVigencia();
                if(Calendario.menor(Uteis.obterDataFuturaParcela(competencia, 1), Calendario.hoje())){
                    competencia =  Uteis.somarMeses(Calendario.hoje(), -1);
                }
                Date vencimentoBase = parcelaContrato.getDataVencimento();
                int mesAtual = 1;
                while(indice <= mesesAdicionar ){
                    competencia = Uteis.obterDataFuturaParcela(competencia, 1);
                    Date vencimento =  Uteis.obterDataFuturaParcela(vencimentoBase, mesAtual++);
                    parcelaContrato.setDataVencimento(vencimento);
                    parcelaContrato.setValorParcela(valorMensal);
                    parcelaContrato.setDescricao("PARCELA "+(indice+ 1));
                    parcelaContrato.setSituacao("EA");
                    parcelaContrato.getContrato().setCodigo(codigoContratoGerado);
                    parcelaContrato.getContrato().setRegimeRecorrencia(true);
                    movParcelaDAO.incluirSemCommit(parcelaContrato);
                    produtoContrato.get(0).setAnoReferencia(Uteis.getAnoData(competencia));
                    produtoContrato.get(0).setMesReferencia(Uteis.getMesReferenciaData(competencia));
                    produtoContrato.get(0).setDescricao(produtoContrato.get(0).getDescricao().substring(0, (produtoContrato.get(0).getDescricao().length() - 7))+ Uteis.getMesReferenciaData(competencia)) ;
                    produtoContrato.get(0).setPrecoUnitario(valorMensal);
                    produtoContrato.get(0).setTotalFinal(valorMensal);
                    produtoContrato.get(0).setValorDesconto(0.0);
                    produtoContrato.get(0).setSituacao("EA");
                    produtoContrato.get(0).setMovProdutoModalidades(movProdModalidade);
                    produtoContrato.get(0).getContrato().setCodigo(codigoContratoGerado);
                    produtoContrato.get(0).getContrato().setVigenciaDe(vigenciaDeNovoContrato);
                    produtoContrato.get(0).getContrato().setVigenciaAteAjustada(vigenciaAteAjustadaNovoContrato);
                    produtoContrato.get(0).getContrato().setVigenciaAte(vigenciaAteAjustadaNovoContrato);
                    produtoContrato.get(0).getContrato().setRegimeRecorrencia(true);
                    movProdutoDAO.incluirSemCommit(produtoContrato.get(0));
                    movPPVO = new MovProdutoParcelaVO();
                    movPPVO.setMovParcela(parcelaContrato.getCodigo());
                    movPPVO.setMovProdutoVO(produtoContrato.get(0));
                    movPPVO.setMovProduto(produtoContrato.get(0).getCodigo());
                    movPPVO.setValorPago(valorMensal);
                    mppDAO.incluir(movPPVO);
                    indice++;
                }

                resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - foi gerado um novo Contrato com Duração de " + mesesAdicionar +" - Código do contrato Gerado:" + codigoContratoGerado +"\n";
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve sua vigencia  alterada de   " + Uteis.getData(consulta.getDate("vigenciaateajustada")) +" para "+ Uteis.getData(novaVigenciaAjustada));
                con.commit();
                ClienteVO cliente = zillyonWebFacade.getCliente().consultarPorCodigoPessoa(consulta.getInt("pessoa"), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                zillyonWebFacade.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            } catch (Exception e){
                con.rollback();
                resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - teve erro na alteração:  + " + e.getMessage()+"\n";
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve erro na alteração:  " + e.getMessage());
            } finally{
                SuperFacadeJDBC.executarUpdate("INSERT INTO ResultadoAlterarContratoIronberg (tipo, matriculacliente, contrato, descricao) VALUES ('SEMESTRAL', "+consulta.getInt("codigomatricula")+", "+consulta.getInt("contrato")+",'"+resultadoAlteracoes+"')", con);
                con.setAutoCommit(true);
            }
        }
    }

    public static void passarParaAnual(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        String sql = "SELECT\n" +
                "\tcon.codigo AS contrato,\n" +
                "\tcli.codigomatricula,\n" +
                "\tcon.situacaocontrato,\n" +
                "\tcon.pessoa,\n" +
                "\tcon.datamatricula,\n" +
                "\tcon.consultor,\n" +
                "\tcrc.diavencimentocartao,\n" +
                "\tcon.plano,\n" +
                "\tprc.valormensal,\n" +
                "\tpln.produtopadraogerarparcelascontrato,\n" +
                "\tcdr.numeromeses,\n" +
                "\tcon.renovavelautomaticamente,\n" +
                "\tpln.renovavelautomaticamente ,\n" +
                "\tcon.vigenciaateajustada,\n" +
                "\tcon.vigenciaate,\n" +
                "\tpes.nome\n" +
                "FROM\n" +
                "\tcontrato con\n" +
                "INNER JOIN cliente cli ON\n" +
                "\tcli.pessoa = con.pessoa\n" +
                "INNER JOIN pessoa pes ON\n" +
                "\tcli.pessoa = pes.codigo\n" +
                "INNER JOIN contratorecorrencia crc ON\n" +
                "\tcrc.contrato = con.codigo\n" +
                "INNER JOIN plano pln ON\n" +
                "\tpln.codigo = con.plano\n" +
                "INNER JOIN planorecorrencia prc ON\n" +
                "\tprc.plano = pln.codigo\n" +
                "INNER JOIN contratoduracao cdr ON\n" +
                "\tcdr.contrato = con.codigo\n" +
                "WHERE\n" +
                "\t1=1 \n" +
                "\tAND (con.contratoresponsavelrenovacaomatricula IS NULL\n" +
                "\t\tOR con.contratoresponsavelrenovacaomatricula = 0)\n" +
                "\tAND con.situacao = 'AT'\n" +
                "\tAND pln.recorrencia = TRUE\n" +
                "\tAND prc.renovavelautomaticamente\n" +
                "\tAND cdr.numeromeses = 1\n" +
                "\tAND NOT EXISTS (\n" +
                "\tSELECT\n" +
                "\t\tcodigo\n" +
                "\tFROM\n" +
                "\t\tcontratooperacao co\n" +
                "\tWHERE\n" +
                "\t\tco.contrato = con.codigo\n" +
                "\t\tAND co.tipooperacao = 'CA')\n" +
                "\tAND prc.duracaoplano = 12\n" +
                "ORDER BY\n" +
                "\t1";
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(sql, con);
        MovProduto movProdutoDAO = new MovProduto(con);
        MovParcela movParcelaDAO = new MovParcela(con);
        MovProdutoParcela mppDAO = new MovProdutoParcela(con);
        MovProdutoModalidade mpmDAO = new MovProdutoModalidade(con);
        ZillyonWebFacade zillyonWebFacade = new ZillyonWebFacade(con);
        List<MovProdutoVO> produtoContrato = new ArrayList<MovProdutoVO>();
        List<MovProdutoModalidadeVO> movProdModalidade = new ArrayList<MovProdutoModalidadeVO>();
        MovParcelaVO parcelaContrato;
        MovProdutoParcelaVO movPPVO;
        String resultadoAlteracoes = "";
        int count = 0;
        while (consulta.next()) {
            con.setAutoCommit(false);
            try {
                int numeroMeses = 1;
                String situacaoContrato = consulta.getString("situacaocontrato");
                int contratoAvaliar = consulta.getInt("contrato");
                while (situacaoContrato.equals("RN")){
                    ResultSet consultaContratoAnterior = SuperFacadeJDBC.criarConsulta("select c.codigo, c.situacaocontrato, cd.numeromeses from contrato c inner join contratoduracao cd on c.codigo = cd.contrato where c.contratoresponsavelrenovacaomatricula = "+contratoAvaliar, con);
                    if(consultaContratoAnterior.next()){
                        situacaoContrato = consultaContratoAnterior.getString("situacaocontrato");
                        numeroMeses += consultaContratoAnterior.getInt("numeromeses");
                        contratoAvaliar = consultaContratoAnterior.getInt("codigo");
                    } else {
                        throw new Exception("Contrato anterior não encontrado do contrato: "+consulta.getInt("contrato"));
                    }
                }
                if(numeroMeses >= 12){
                    resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - não foi alterado pois tem "+numeroMeses+" meses de vigência. No caso, o contrato com a nova duração será lançado durante a sua renovação automática.\n";
                    SuperFacadeJDBC.executarUpdate("UPDATE contratoduracao SET nrmaximoparcelascondpagamento = 12,numeromeses = 12 WHERE contrato = "+ consulta.getInt("contrato") + ";", con);
                    con.commit();
                    continue;
                }
                Double valorMensal = consulta.getDouble("valormensal");
                int indice = 1;
                int mesesAdicionar = 12 - numeroMeses;
                Date novaVigencia = Uteis.somarCampoData(consulta.getDate("vigenciaateajustada"), Calendar.MONTH,  mesesAdicionar);
                Date vigenciaAteAjustadaNovoContrato = Uteis.somarCampoData(consulta.getDate("vigenciaateajustada"), Calendar.MONTH,  mesesAdicionar);
                Date vigenciaDeNovoContrato = Uteis.somarDias(consulta.getDate("vigenciaateajustada"), 1);
                Double valorFinalNovoContrato = valorMensal * mesesAdicionar;

                String sqlInsertContrato = "INSERT INTO contrato (\n" +
                        "  bolsa, naopermitirrenovacaorematriculadecontratoanteriores, \n" +
                        "  valordescontoespecifico, valordescontoporcentagem, \n" +
                        "  nomemodalidades, somaproduto, contratoresponsavelrenovacaomatricula, \n" +
                        "  contratoresponsavelrematriculamatricula, \n" +
                        "  datalancamento, datamatricula, dataprevistarenovar, \n" +
                        "  datarenovarrealizada, dataprevistarematricula, \n" +
                        "  datarematricularealizada, situacaocontrato, \n" +
                        "  situacaorenovacao, situacaorematricula, \n" +
                        "  contratobaseadorenovacao, contratobaseadorematricula, \n" +
                        "  pagarcomboleto, responsavelcontrato, \n" +
                        "  observacao, responsavelliberacaocondicaopagamento, \n" +
                        "  valorfinal, valorbasecalculo, vigenciaate, \n" +
                        "  vigenciaateajustada, vigenciade, \n" +
                        "  estendecoberturafamiliares, situacao, \n" +
                        "  plano, pessoa, consultor, empresa, \n" +
                        "  conveniodesconto, dividirprodutosnasparcelas, \n" +
                        "  desconto, tipodesconto, valordesconto, \n" +
                        "  diavencimentoprorata, dataalteracaomanual, \n" +
                        "  responsaveldatabase, regimerecorrencia, \n" +
                        "  origemcontrato, importacao, valorarredondamento, \n" +
                        "  renovavelautomaticamente, quantidademaximafrequencia, \n" +
                        "  vendacreditotreino, numerocupomdesconto, \n" +
                        "  id_externo, crossfit, permiterenovacaoautomatica, \n" +
                        "  origemsistema, grupo, nomeconveniodesconto, \n" +
                        "  valorconveniodesconto, percentualconveniodesconto, \n" +
                        "  idexterno, vendacreditosessao, cancelamentointegrado, \n" +
                        "  contratoorigemtransferencia, valorbasecalculomanutencaomodalidade, \n" +
                        "  dataenviocontrato, emailrecebimento, \n" +
                        "  ipassinaturacontrato, dataassinaturacontrato, \n" +
                        "  vendavitio, evento, pessoaoriginal, \n" +
                        "  primeirocontratobaseadorenovacao, \n" +
                        "  dataassinaturacancelamento, xnumpro, \n" +
                        "  valorbasenegociado, datasincronizacaointegracaofoguete\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    false, false, 0.0, 0.0, 'M', 0.0, 0, 0, \n" +
                        "    '" + Uteis.getDataJDBC(Calendario.hoje()) + "', '" + Uteis.getDataJDBC(consulta.getDate("datamatricula")) + "', \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', NULL, \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', NULL, \n" +
                        "    'RN', '', '', "+ consulta.getInt("contrato") +", 0, false, 3, 'CONTRATO GERADO PELO AJUSTE GCM-399', \n" +
                        "    NULL, "+valorFinalNovoContrato+", "+valorFinalNovoContrato+", '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', \n" +
                        "    '" + Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato) + "', '" + Uteis.getDataJDBC(vigenciaDeNovoContrato) + "', \n" +
                        "    false, 'AT', "+consulta.getInt("plano")+", "+consulta.getInt("pessoa")+", "+consulta.getInt("consultor")+", 1, NULL, \n" +
                        "    false, NULL, '', NULL, 0, NULL, NULL, \n" +
                        "    true, 1, false, 0.0, false, 7, false, \n" +
                        "    NULL, NULL, false, true, 1, NULL, NULL, \n" +
                        "    NULL, NULL, NULL, false, false, 0, NULL, \n" +
                        "    NULL, NULL, NULL, NULL, false, NULL, \n" +
                        "    NULL, NULL, NULL, NULL, "+valorFinalNovoContrato+", NULL\n" +
                        "  );";

                SuperFacadeJDBC.executarUpdate(sqlInsertContrato, con);
                SuperFacadeJDBC.executarUpdate("UPDATE contrato SET contratoresponsavelrenovacaomatricula = (SELECT MAX(codigo) FROM contrato WHERE pessoa = " +consulta.getInt("pessoa")+ ") WHERE codigo = "+ consulta.getInt("contrato") + ";", con);

                Integer codigoContratoGerado = 0;
                ResultSet consultaCodigoContratoGerado = SuperFacadeJDBC.criarConsulta("SELECT MAX(codigo) as codigoContrato FROM contrato WHERE pessoa = " +consulta.getInt("pessoa"), con);
                if (consultaCodigoContratoGerado.next()){
                    codigoContratoGerado = consultaCodigoContratoGerado.getInt("codigoContrato");
                }

                if (UteisValidacao.emptyNumber(codigoContratoGerado)){
                    throw new Exception("Erro ao gerar novo contrato com renovação proporcional.");
                }


                String sqlInserirContratoRecorrencia = "INSERT INTO contratorecorrencia (\n" +
                        "  contrato, pessoa, fidelidade, numerocartao, \n" +
                        "  diavencimentocartao, ultimatransacaoaprovada, \n" +
                        "  valormensal, diasbloqueioacesso, \n" +
                        "  diascancelamentoautomatico, renovavelautomaticamente, \n" +
                        "  valoranuidade, diavencimentoanuidade, \n" +
                        "  mesvencimentoanuidade, datainutilizada, \n" +
                        "  anuidadenaparcela, parcelaanuidade, \n" +
                        "  cancelamentoproporcional, qtddiascobrarproximaparcela, \n" +
                        "  qtddiascobraranuidadetotal, parcelaranuidade, \n" +
                        "  cancelamentoproporcionalsomenterenovacao, \n" +
                        "  valormensalnegociado\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    "+codigoContratoGerado+", "+consulta.getInt("pessoa")+", 12, '', "+consulta.getInt("diavencimentocartao")+", '', "+valorMensal+", 0, 0, \n" +
                        "    true, 0.0, 1, 1, NULL, false, 0, false, \n" +
                        "    0, 0, false, false, "+valorMensal+"\n" +
                        "  );";
                SuperFacadeJDBC.executarUpdate(sqlInserirContratoRecorrencia, con);

                String sqlInsertContratoCondicaoPagamento = "INSERT INTO contratocondicaopagamento (\n" +
                        "  percentualdesconto, condicaopagamento, \n" +
                        "  tipooperacao, tipovalor, valorespecifico, \n" +
                        "  contrato\n" +
                        ") \n" +
                        "  (SELECT 0.0, codigo, '', '', 0.0, "+codigoContratoGerado+" FROM condicaopagamento WHERE descricao ILIKE '%EM 12 VEZES%RECORR%' LIMIT 1)";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoCondicaoPagamento, con);

                String sqlInsertContratoDuracao = "INSERT INTO contratoduracao (\n" +
                        "  valordesejadomensal, valordesejadoparcela, \n" +
                        "  valordesejado, tipooperacao, tipovalor, \n" +
                        "  valorespecifico, percentualdesconto, \n" +
                        "  nrmaximoparcelascondpagamento, \n" +
                        "  contrato, numeromeses, carencia, \n" +
                        "  quantidadediasextra, carenciaalterada\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    "+valorMensal+", "+valorMensal+", "+valorFinalNovoContrato+", 'RE', 'VE', 0.0, \n" +
                        "    0.0, 12, "+codigoContratoGerado+", 12, 0, 0, false\n" +
                        "  );";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoDuracao, con);

                String sqlInsertContratoHorario = "INSERT INTO contratohorario (\n" +
                        "  tipooperacao, tipovalor, valorespecifico, \n" +
                        "  percentualdesconto, contrato, horario\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  ('', '', 0.0, 0.0, "+codigoContratoGerado+", 1);";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoHorario, con);

                String sqlInsertContratoPlanoProdutoSugerido = "INSERT INTO contratoplanoprodutosugerido (\n" +
                        "  valorfinalproduto, contrato, planoprodutosugerido\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (0.0, "+codigoContratoGerado+", 52);";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoPlanoProdutoSugerido, con);

                String sqlInsertContratoModalidade = "INSERT INTO contratomodalidade (\n" +
                        "  vezessemana, valormodalidade, valorfinalmodalidade, \n" +
                        "  valorbasecalculo, modalidade, contrato\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (7, "+valorMensal+", "+valorMensal+", NULL, 2, "+codigoContratoGerado+");";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoModalidade, con);

                String sqlInsertContratoModalidadeVezesSemana = "INSERT INTO contratomodalidadevezessemana (nrvezes, contratomodalidade) (\n" +
                        "  SELECT \n" +
                        "    7, \n" +
                        "    MAX(codigo) \n" +
                        "  FROM \n" +
                        "    contratomodalidade\n" +
                        ")";
                SuperFacadeJDBC.executarUpdate(sqlInsertContratoModalidadeVezesSemana, con);

                String sqlInsertHistoricoContrato = "INSERT INTO historicocontrato (\n" +
                        "  datafinalsituacao, tipohistorico, \n" +
                        "  datainiciosituacao, situacaorelativahistorico, \n" +
                        "  dataregistro, responsavelliberacaomudancahistorico, \n" +
                        "  responsavelregistro, descricao, \n" +
                        "  contrato, retornomanual, datainiciotemporal\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    '"+Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato)+"', 'RN', \n" +
                        "    '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', '', '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', \n" +
                        "    NULL, 3, 'RENOVADO', "+codigoContratoGerado+", false, \n" +
                        "    NULL\n" +
                        "  );\n";
                SuperFacadeJDBC.executarUpdate(sqlInsertHistoricoContrato, con);

                String sqlInsertPeriodoAcessoCliente = "INSERT INTO periodoacessocliente (\n" +
                        "  tipoacesso, datafinalacesso, datainicioacesso, \n" +
                        "  aulaavulsadiaria, contrato, contratobaseadorenovacao, \n" +
                        "  pessoa, responsavel, datalancamento, \n" +
                        "  tokengympass, reposicao, tipogympass, \n" +
                        "  valorgympass, tipototalpass, tokengogood, \n" +
                        "  produto\n" +
                        ") \n" +
                        "VALUES \n" +
                        "  (\n" +
                        "    'CA', '"+Uteis.getDataJDBC(vigenciaAteAjustadaNovoContrato)+"', \n" +
                        "    '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', NULL, \n" +
                        "    "+codigoContratoGerado+", "+consulta.getInt("contrato")+", "+consulta.getInt("pessoa")+", NULL, '"+Uteis.getDataJDBC(vigenciaDeNovoContrato)+"', \n" +
                        "    '', NULL, '', 0.0, false, NULL, NULL\n" +
                        "  );\n";
                SuperFacadeJDBC.executarUpdate(sqlInsertPeriodoAcessoCliente, con);


                produtoContrato = movProdutoDAO.consultar("select * from movproduto where contrato = "+consulta.getInt("contrato")+" and produto = "+consulta.getInt("produtopadraogerarparcelascontrato")+" order by codigo desc limit 1 ", Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                parcelaContrato = movParcelaDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo().intValue(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                movProdModalidade = mpmDAO.consultarPorMovProduto(produtoContrato.get(0).getCodigo(),  Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

                Date novaVigenciaAjustada = novaVigencia;
                if(!Calendario.igual(consulta.getDate("vigenciaate"),consulta.getDate("vigenciaateajustada")) ){
                    int diferenca = (int) (Uteis.nrDiasEntreDatas(consulta.getDate("vigenciaate"), consulta.getDate("vigenciaateajustada")) );
                    novaVigenciaAjustada = Uteis.somarDias(novaVigencia, diferenca);
                }
                Date competencia = produtoContrato.get(0).getDataInicioVigencia();
                if(Calendario.menor(Uteis.obterDataFuturaParcela(competencia, 1), Calendario.hoje())){
                    competencia =  Uteis.somarMeses(Calendario.hoje(), -1);
                }
                Date vencimentoBase = parcelaContrato.getDataVencimento();
                int mesAtual = 1;
                while(indice <= mesesAdicionar ){
                    competencia = Uteis.obterDataFuturaParcela(competencia, 1);
                    Date vencimento =  Uteis.obterDataFuturaParcela(vencimentoBase, mesAtual++);
                    parcelaContrato.setDataVencimento(vencimento);
                    parcelaContrato.setValorParcela(valorMensal);
                    parcelaContrato.setDescricao("PARCELA "+(indice+ 1));
                    parcelaContrato.setSituacao("EA");
                    parcelaContrato.getContrato().setCodigo(codigoContratoGerado);
                    parcelaContrato.getContrato().setRegimeRecorrencia(true);
                    movParcelaDAO.incluirSemCommit(parcelaContrato);
                    produtoContrato.get(0).setAnoReferencia(Uteis.getAnoData(competencia));
                    produtoContrato.get(0).setMesReferencia(Uteis.getMesReferenciaData(competencia));
                    produtoContrato.get(0).setDescricao(produtoContrato.get(0).getDescricao().substring(0, (produtoContrato.get(0).getDescricao().length() - 7))+ Uteis.getMesReferenciaData(competencia)) ;
                    produtoContrato.get(0).setPrecoUnitario(valorMensal);
                    produtoContrato.get(0).setTotalFinal(valorMensal);
                    produtoContrato.get(0).setValorDesconto(0.0);
                    produtoContrato.get(0).setSituacao("EA");
                    produtoContrato.get(0).setMovProdutoModalidades(movProdModalidade);
                    produtoContrato.get(0).getContrato().setCodigo(codigoContratoGerado);
                    produtoContrato.get(0).getContrato().setVigenciaDe(vigenciaDeNovoContrato);
                    produtoContrato.get(0).getContrato().setVigenciaAteAjustada(vigenciaAteAjustadaNovoContrato);
                    produtoContrato.get(0).getContrato().setVigenciaAte(vigenciaAteAjustadaNovoContrato);
                    produtoContrato.get(0).getContrato().setRegimeRecorrencia(true);
                    movProdutoDAO.incluirSemCommit(produtoContrato.get(0));
                    movPPVO = new MovProdutoParcelaVO();
                    movPPVO.setMovParcela(parcelaContrato.getCodigo());
                    movPPVO.setMovProdutoVO(produtoContrato.get(0));
                    movPPVO.setMovProduto(produtoContrato.get(0).getCodigo());
                    movPPVO.setValorPago(valorMensal);
                    mppDAO.incluir(movPPVO);
                    indice++;
                }

                resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - foi gerado um novo Contrato com Duração de " + mesesAdicionar +" - Código do contrato Gerado:" + codigoContratoGerado +"\n";
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve sua vigencia  alterada de   " + Uteis.getData(consulta.getDate("vigenciaateajustada")) +" para "+ Uteis.getData(novaVigenciaAjustada));
                con.commit();
                ClienteVO cliente = zillyonWebFacade.getCliente().consultarPorCodigoPessoa(consulta.getInt("pessoa"), Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);
                zillyonWebFacade.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_CONTRATO, false);
            } catch (Exception e){
                con.rollback();
                resultadoAlteracoes = "Contrato " + consulta.getInt("contrato") + " do aluno " + consulta.getInt("codigomatricula") + " - " + consulta.getString("nome").toUpperCase() + " - teve erro na alteração:  + " + e.getMessage()+"\n";
                System.out.println(++count + " - contrato " + consulta.getInt("contrato") + " teve erro na alteração:  " + e.getMessage());
            } finally{
                SuperFacadeJDBC.executarUpdate("INSERT INTO ResultadoAlterarContratoIronberg (tipo, matriculacliente, contrato, descricao) VALUES ('ANUAL', "+consulta.getInt("codigomatricula")+", "+consulta.getInt("contrato")+",'"+resultadoAlteracoes+"')", con);
                con.setAutoCommit(true);
            }
        }
    }

}
