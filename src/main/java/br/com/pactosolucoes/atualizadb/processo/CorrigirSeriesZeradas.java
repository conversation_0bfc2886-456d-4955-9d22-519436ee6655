/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class CorrigirSeriesZeradas {

    public static void main(String... args) throws Exception {
        Uteis.debug = true;
        String[] hosts = new String[]{
            "************************************************/",
            "************************************************/",            
            "************************************************/",
            "************************************************/",
            "************************************************/",
            "************************************************/",
            "************************************************/",
            "************************************************/",
            "************************************************/",
            "************************************************/",
            "************************************************/"
        };
        for (String host : hosts) {
            Uteis.logar("######################### " + host);
            Connection oamd2 = DriverManager.getConnection(host + "OAMD2", "postgres", "pactodb");
            ResultSet rsOAMD2 = SuperFacadeJDBC.criarConsulta(" SELECT * FROM empresa", oamd2);
            while (rsOAMD2.next()) {
                try {
                    String nomeBD = rsOAMD2.getString("nomeBD");
                    Connection conBancoTreino = DriverManager.getConnection(host + rsOAMD2.getString("nomeBD"),
                            "zillyonweb", "pactodb");
                    verificarSeriesZeradas(nomeBD, conBancoTreino, true);
                    conBancoTreino.close();
                    conBancoTreino = null;
                    //                verificarConfiguracao( nomeBD,  conBancoTreino, false);
                } catch (Exception e) {
                    //                System.err.println("erro " + e.getMessage());
                }
            }
            oamd2.close();
            oamd2 = null;
        }

    }

    public static void verificarConfiguracao(String nomeBD, Connection con, boolean update) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select valor from configuracaosistema  where configuracao = 56 ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        while (rs.next()) {
            if (Boolean.valueOf(rs.getString("valor"))) {
                Uteis.logar(nomeBD);
            }
        }
    }

    public static void verificarSeriesZeradas(String nomeBD, Connection con, boolean update) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select af.codigo as atividadeficha, emp.nome as empresa, cli.matricula, cli.nome,pt.nome as programa, ");
        sql.append(" a.nome as atividade, f.nome as ficha, serie.codigo as serie from serie \n");
        sql.append("inner join atividadeficha af on af.codigo = serie.atividadeficha_codigo\n");
        sql.append("inner join atividade a on a.codigo = af.atividade_codigo\n");
        sql.append("inner join ficha f on f.codigo = af.ficha_codigo\n");
        sql.append("inner join programatreinoficha ptf on ptf.ficha_codigo = af.ficha_codigo\n");
        sql.append("inner join programatreino pt on pt.codigo = ptf.programa_codigo\n");
        sql.append("inner join clientesintetico cli on cli.codigo = pt.cliente_codigo\n");
        sql.append("inner join empresa emp on cli.empresa = emp.codzw\n");
        sql.append("where carga  = 0\n");
        sql.append("and serie.descanso = 0\n");
        sql.append("and serie.ordem = 1\n");
        sql.append("and serie.repeticao = 0\n");
        sql.append("and serie.duracao = 0\n");
        sql.append("and serie.velocidade = 0\n");
        sql.append("and serie.atividadeficha_codigo in (\n");
        sql.append("select atividadeficha_codigo from serie where ordem > 1\n");
        sql.append("and (repeticao > 0\n");
        sql.append("or duracao > 0\n");
        sql.append("or carga > 0\n");
        sql.append("or velocidade > 0)) \n");
        sql.append("and pt.datainicio > '2017-07-01'\n");
        sql.append("order by atividadeficha_codigo desc ");
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rs = stm.executeQuery();
        boolean first = true;
        while (rs.next()) {
            if (first) {
                Uteis.logar("---------------------- " + nomeBD + " ---------------------- ");
                first = false;
            }
            Uteis.logar(rs.getString("empresa") + "-" + rs.getString("nome") + "-" + rs.getString("programa") + "-" + rs.getString("ficha"));
            if (!update) {
                continue;
            }
            ResultSet rsSerie = con.prepareStatement("select * from serie where atividadeficha_codigo = " + rs.getInt("atividadeficha")
                    + " and ordem = (select max(s.ordem) from serie s where s.atividadeficha_codigo = " + rs.getInt("atividadeficha") + ")").executeQuery();
            if (rsSerie.next()) {
                PreparedStatement stmUpdateSerie = con.prepareStatement("UPDATE serie SET carga = ?, "
                        + "repeticao = ?, "
                        + "cadencia = ?, "
                        + "cargacomp = ?, "
                        + "complemento = ?, "
                        + "descanso = ?, "
                        + "distancia = ?, "
                        + "duracao = ?, "
                        + "repeticaocomp = ?, "
                        + "velocidade = ? where atividadeficha_codigo = ? ");
                stmUpdateSerie.setInt(1, rsSerie.getInt("carga"));
                stmUpdateSerie.setInt(2, rsSerie.getInt("repeticao"));
                stmUpdateSerie.setString(3, rsSerie.getString("cadencia"));
                stmUpdateSerie.setString(4, rsSerie.getString("cargacomp"));
                stmUpdateSerie.setString(5, rsSerie.getString("complemento"));
                stmUpdateSerie.setInt(6, rsSerie.getInt("descanso"));
                stmUpdateSerie.setInt(7, rsSerie.getInt("distancia"));
                stmUpdateSerie.setInt(8, rsSerie.getInt("duracao"));
                stmUpdateSerie.setString(9, rsSerie.getString("repeticaocomp"));
                stmUpdateSerie.setInt(10, rsSerie.getInt("velocidade"));
                stmUpdateSerie.setInt(11, rs.getInt("atividadeficha"));
                stmUpdateSerie.execute();
            }
        }
    }
}
