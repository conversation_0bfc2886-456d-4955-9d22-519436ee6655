/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperEntidade;

/**
 *
 * <AUTHOR>
 */
public class CorrigirColaboradoresDuplicados {

    public static void main(String... args) {
        try {
            Connection con = DriverManager.getConnection("*****************************************************", "zillyonweb", "pactodb");
            String sql = "SELECT p.nome, count(colaborador.codigo), "
                    + "ARRAY_TO_STRING(ARRAY(select codigo from colaborador where pessoa IN "
                    + "(SELECT codigo from pessoa where nome = p.nome)), ';') as cods"
                    + " FROM colaborador  inner join pessoa p on p.codigo = colaborador.pessoa "
                    + " GROUP BY p.nome HAVING COUNT(colaborador.codigo) > 1";
            ResultSet rs = SuperEntidade.criarConsulta(sql, con);
            while (rs.next()) {
                String[] arr = rs.getString("cods").split(";");
                ResultSet rsUs = SuperEntidade.criarConsulta("SELECT codigo from usuario where colaborador = " + arr[0], con);
                Integer vaiFicar;
                Integer vaiExcluir;
                if (rsUs.next()) {
                    vaiFicar = Integer.valueOf(arr[1]);
                    vaiExcluir = Integer.valueOf(arr[0]);
                } else {
                    vaiFicar = Integer.valueOf(arr[1]);
                    vaiExcluir = Integer.valueOf(arr[0]);
                }

                SuperEntidade.executarConsulta("UPDATE usuario SET colaborador = "
                        + vaiFicar + " where colaborador = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("UPDATE questionariocliente SET consultor = "
                        + vaiFicar + " where consultor = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("UPDATE reciboclienteconsultor SET consultor = "
                        + vaiFicar + " where consultor = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("UPDATE vinculo SET colaborador = "
                        + vaiFicar + " where colaborador = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("UPDATE risco SET colaborador = "
                        + vaiFicar + " where colaborador = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("UPDATE historicovinculo SET colaborador = "
                        + vaiFicar + " where colaborador = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("UPDATE contrato SET consultor = "
                        + vaiFicar + " where consultor = " + vaiExcluir, con);
                SuperEntidade.executarConsulta("DELETE FROM colaborador where codigo = " + vaiExcluir, con);
            }
        } catch (SQLException ex) {
            Logger.getLogger(CorrigirColaboradoresDuplicados.class.getName()).log(Level.SEVERE, null, ex);
        } catch (Exception ex) {
            Logger.getLogger(CorrigirColaboradoresDuplicados.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
