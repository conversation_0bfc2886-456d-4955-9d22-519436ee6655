/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.postgresql.util.PSQLException;

/**
 *
 * <AUTHOR>
 */
public class CorrigirContratosDuplicados {

    public static void main(String[] args) {
        try {
            Connection con = DriverManager.getConnection("***************************************", "postgres", "pactodb");
            agruparContratos(con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void agruparContratos(Connection con) throws SQLException {
        try {
            con.setAutoCommit(false);
            StringBuilder var1 = new StringBuilder();
            var1.append("SELECT c.pessoa, \n");
            var1.append("       (SELECT nome \n");
            var1.append("        FROM   pessoa \n");
            var1.append("        WHERE  codigo = c.pessoa) AS nome, \n");
            var1.append("       c.vigenciade, \n");
            var1.append("       c.vigenciaateajustada, \n");
            var1.append("       Count(codigo)              AS contratos, \n");
            var1.append("       Sum(valorfinal)            as valorcontratos \n");
            var1.append("FROM   contrato c \n");
            var1.append("GROUP  BY c.vigenciade,c.vigenciaateajustada,c.pessoa \n");
            var1.append("HAVING Count(codigo) > 1 \n");
            var1.append("ORDER  BY c.pessoa ");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(var1.toString(), con);
            while (rs.next()) {
                Date vigenciade = rs.getDate("vigenciade");
                Date vigenciaateajustada = rs.getDate("vigenciaateajustada");
                Integer pessoa = rs.getInt("pessoa");
                Double valorAgrupado = rs.getDouble("valorcontratos");
                String sql = "SELECT codigo FROM contrato WHERE pessoa = " + pessoa
                        + " and vigenciade = '" + Uteis.getDataJDBC(vigenciade)
                        + "' and vigenciaateajustada = '" + Uteis.getDataJDBC(vigenciaateajustada) + "' order by codigo ";
                Integer primeiro = null;
                String codigos = "";
                ResultSet rsCodigos = SuperFacadeJDBC.criarConsulta(sql, con);
                while (rsCodigos.next()) {
                    if (primeiro == null) {
                        primeiro = rsCodigos.getInt("codigo");
                    } else {
                        codigos += "," + rsCodigos.getInt("codigo");
                    }
                }
                ResultSet rsNrMeses = SuperFacadeJDBC.criarConsulta("SELECT numeromeses FROM contratoduracao WHERE contrato = " + primeiro, con);
                if (rsNrMeses.next()) {
                    Integer nrMeses = rsNrMeses.getInt("numeromeses");
                    Double valorMensal = valorAgrupado / nrMeses;

                    SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET totalfinal = "
                            + valorMensal+", precounitario = "+valorMensal
                            + " WHERE contrato = " + primeiro
                            + " AND descricao NOT LIKE '%MATRICULA%'", con);
                    SuperFacadeJDBC.executarConsulta("UPDATE contrato SET valorfinal = "
                            + valorAgrupado+ ", valorbasecalculo = "+valorAgrupado
                            + " WHERE codigo = " + primeiro, con);
                    SuperFacadeJDBC.executarConsulta("UPDATE movparcela SET valorparcela = " + valorAgrupado + " WHERE contrato = " + primeiro, con);
                    
                    SuperFacadeJDBC.executarConsulta("UPDATE pagamentomovparcela SET valorpago = " 
                            + valorAgrupado + " WHERE movparcela in "
                            + "(select codigo from movparcela where contrato = " + primeiro+")", con);
                    
                    SuperFacadeJDBC.executarConsulta("UPDATE movpagamento SET valor = " + valorAgrupado 
                                                    +", valortotal = " + valorAgrupado 
                                                    +" where codigo in  "
                                                    +"(SELECT movpagamento FROM pagamentomovparcela WHERE movparcela IN "
                                                    +"(SELECT codigo FROM movparcela WHERE contrato = "+ primeiro+"));",con); 
                    
                    SuperFacadeJDBC.executarConsulta("UPDATE recibopagamento SET valortotal = " + valorAgrupado 
                                                    +" WHERE contrato = "+primeiro,con); 
                    
                    SuperFacadeJDBC.executarConsulta("UPDATE contratomodalidade SET valormodalidade = "
                            + valorMensal + ", valorfinalmodalidade = "
                            + valorMensal + " WHERE contrato = " + primeiro, con);
                    
                    SuperFacadeJDBC.executarConsulta("UPDATE movprodutoparcela SET valorpago = "
                            + valorMensal + " WHERE movproduto IN (SELECT codigo FROM movproduto WHERE contrato = "
                            + primeiro + ") ", con);
                }
                
                SuperFacadeJDBC.executarConsulta("DELETE FROM movpagamento WHERE codigo in"
                        + "(SELECT movpagamento FROM pagamentomovparcela WHERE movparcela IN "
                        + "(SELECT codigo FROM movparcela WHERE contrato IN ("+codigos.replaceFirst(",", "")+")));", con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM periodoacessocliente where contratobaseadorenovacao in("+codigos.replaceFirst(",", "")+")", con);
                SuperFacadeJDBC.executarConsulta("DELETE FROM contrato WHERE codigo in("+codigos.replaceFirst(",", "")+")", con);
                

            }
             con.commit();
        } catch (Exception e) {
            con.rollback();
            con.setAutoCommit(true);
            e.printStackTrace();
        } finally {
            con.setAutoCommit(true);
        }

    }
}
