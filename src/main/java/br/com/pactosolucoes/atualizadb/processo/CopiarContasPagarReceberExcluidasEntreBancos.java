package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.CaixaMovConta;
import negocio.facade.jdbc.financeiro.MovConta;
import negocio.facade.jdbc.financeiro.MovContaRateio;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class CopiarContasPagarReceberExcluidasEntreBancos {

    public static void main(String... args) throws Exception {

        //Processo responsável por copiar contas a pagar/receber excluídas indevidamente entre bancos.
        // Também copia caixamovconta e movcontarateio

        Uteis.logarDebug("########## INÍCIO PROCESSO COPIAR CONTAS A PAGAR/RECEBER ##########");

//        Connection conOrigem = DriverManager.getConnection("********************************************************************************", "postgres", "pactodb");
//        Connection conDestino = DriverManager.getConnection("**************************************************************************", "postgres", "pactodb");

        Connection conBancoAntigo = DriverManager.getConnection("**********************************************************************antiga", "postgres", "pactodb");
        Connection conBancoAtual = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");

        String dataInicio = "2024-03-01";
        String dataFim = "2024-03-01";

        //MOVCONTA
        List<MovContaVO> movContaVOSCopiar = consultarMovContasExcluidas(dataInicio, dataFim, conBancoAntigo);
        validacoesPrincipais(movContaVOSCopiar, conBancoAtual);
        copiarMovConta(movContaVOSCopiar, conBancoAtual);

        //MOVCONTARATEIO
        List<MovContaRateioVO> movContasRateioVOSCopiar = consultarMovContasRateioExcluidas(movContaVOSCopiar, conBancoAntigo);
        copiarMovContaRateio(movContasRateioVOSCopiar, conBancoAtual);

        //CAIXAMOVCONTA
        List<CaixaMovContaVO> caixasMovContaVOSCopiar = consultarCaixasMovContaExcluidas(movContaVOSCopiar, conBancoAntigo);
        copiarCaixaMovConta(caixasMovContaVOSCopiar, conBancoAtual);
    }

    private static List<MovContaVO> consultarMovContasExcluidas(String dataInicio, String dataFim, Connection conBancoAntigo) throws Exception {
        List<MovContaVO> movContaVOS = new ArrayList<MovContaVO>();
        StringBuilder sql = new StringBuilder();
        sql.append("select * from movconta where tipooperacao in (1,2)  \n");
        sql.append("and datalancamento::date >= '" + dataInicio + "' \n");
        sql.append("and datalancamento::date <= '" + dataFim + "' \n");
        Uteis.logarDebug("Consultando MovContas no banco de ORIGEM (antigo): " + conBancoAntigo.getCatalog() + " a serem copiadas");
        try (Statement stm = conBancoAntigo.createStatement()) {
            try (ResultSet dadosSQL = stm.executeQuery(sql.toString())) {
                while (dadosSQL.next()) {
                    MovContaVO obj = new MovContaVO();
                    obj.setCodigo(dadosSQL.getInt("codigo"));
                    obj.setPessoaVO(new PessoaVO());
                    obj.getPessoaVO().setCodigo(dadosSQL.getInt("pessoa"));
                    obj.setUsuarioVO(new UsuarioVO());
                    obj.getUsuarioVO().setCodigo(dadosSQL.getInt("usuario"));
                    obj.setEmpresaVO(new EmpresaVO());
                    obj.getEmpresaVO().setCodigo(dadosSQL.getInt("empresa"));
                    obj.setContaVO(new ContaVO());
                    obj.getContaVO().setCodigo(dadosSQL.getInt("conta"));
                    obj.setDescricao(dadosSQL.getString("descricao"));
                    obj.setObservacoes(dadosSQL.getString("observacoes"));
                    obj.setValor(dadosSQL.getDouble("valor"));
                    obj.setDataQuitacao(dadosSQL.getTimestamp("dataquitacao"));
                    obj.setDataLancamento(dadosSQL.getTimestamp("datalancamento"));
                    obj.setTipoOperacaoLancamento(TipoOperacaoLancamento.getTipoOperacaoLancamento(dadosSQL.getInt("tipooperacao")));
                    obj.setDataVencimento(dadosSQL.getTimestamp("datavencimento"));
                    obj.setDataCompetencia(dadosSQL.getTimestamp("datacompetencia"));
                    obj.setAgendamentoFinanceiro(dadosSQL.getInt("agendamentofinanceiro"));
                    obj.setNrParcela(dadosSQL.getInt("nrparcela"));
                    obj.setLote(new LoteVO());
                    obj.getLote().setCodigo(dadosSQL.getInt("lote"));
                    obj.setContaOrigem(dadosSQL.getInt("contaorigem"));
                    obj.setAutorizacaoCartao(dadosSQL.getString("autorizacaocartao"));
                    obj.setApresentarNoCaixa(dadosSQL.getBoolean("apresentarnocaixa"));
                    obj.setConjuntoPagamento(dadosSQL.getString("conjuntopagamento"));
                    obj.setMovProduto(dadosSQL.getInt("movproduto"));
                    obj.setNfseEmitida(dadosSQL.getBoolean("nfseemitida"));
                    obj.setApp(dadosSQL.getBoolean("app"));
                    obj.setLotePagouConta(new LoteVO());
                    obj.getLotePagouConta().setCodigo(dadosSQL.getInt("lotepagouconta"));
                    obj.setDataUltimaAlteracao(dadosSQL.getTimestamp("dataultimaalteracao"));
                    obj.setNfceEmitida(dadosSQL.getBoolean("nfceemitida"));
                    obj.setCodigoBarras(dadosSQL.getString("codigobarras"));
                    obj.setChaveArquivoConta(dadosSQL.getString("chavearquivoconta"));
                    obj.setChaveArquivoComprovante(dadosSQL.getString("chavearquivocomprovante"));
                    obj.setExtensaoArquivoComprovanteMovConta(dadosSQL.getString("extencaoarquivocomprovante"));
                    obj.setExtensaoArquivoContaMovConta(dadosSQL.getString("extencaoarquivoconta"));
                    obj.setCompraEstoque(dadosSQL.getInt("compraestoque"));
                    obj.setNumeroDocumento(dadosSQL.getString("numerodocumento"));
                    obj.setTaxaAntecipacao(dadosSQL.getDouble("taxaantecipacao"));
                    obj.setIdentificadorOrigem(dadosSQL.getString("identificadororigem"));
                    obj.setValorPago(dadosSQL.getDouble("valorpago"));
                    obj.setValorOriginalAlterado(dadosSQL.getDouble("valororiginalalterado"));
                    obj.setValorOriginalAntesDaConciliacao(dadosSQL.getDouble("valororiginalantesdaconciliacao"));
                    obj.setIdentificadorOrigemTipo(dadosSQL.getInt("identificadororigemtipo"));
                    obj.setIdentificadorDados(dadosSQL.getString("identificadordados"));
                    obj.setRetiradaAutomaticaRecebivelOrigemCancelamento(dadosSQL.getBoolean("retiradaautomaticarecebivelorigemcancelamento"));
                    movContaVOS.add(obj);
                }
            }
        }
        return movContaVOS;
    }

    private static void copiarMovConta(List<MovContaVO> movContaVOSCopiar, Connection conBancoAtual) throws Exception {
        MovConta movContaDAOBancoAtual;
        try {
            int atual = 1;

            try {
                Uteis.logarDebug("Vou conectar no banco destino para iniciar a cópia das movContas...");
                //INCLUIR NO BANCO DESTINO
                movContaDAOBancoAtual = new MovConta(conBancoAtual);
                for (MovContaVO movContaVO : movContaVOSCopiar) {
                    Uteis.logarDebug("Copiando MovConta " + atual + "/" + movContaVOSCopiar.size() + " | Cód. " + movContaVO.getCodigo() + " | " + movContaVO.getDescricao());
                    try {
                        atual++;
                        movContaDAOBancoAtual.incluirOrigemProcessoCopiarContas(movContaVO);
                        Uteis.logarDebug("Sucesso!");
                    } catch (Exception ex) {
                        Uteis.logarDebug("Erro ao copiar movContaVO: " + ex.getMessage());
                    }
                }
            } catch (Exception ex) {
            } finally {
                movContaDAOBancoAtual = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static List<MovContaRateioVO> consultarMovContasRateioExcluidas(List<MovContaVO> movContaVOS, Connection conBancoAntigo) throws Exception {
        List<MovContaRateioVO> movContasRateioVOS = new ArrayList<MovContaRateioVO>();

        String codMovContas = "";
        for (MovContaVO movContaVO : movContaVOS) {
            codMovContas += movContaVO.getCodigo() + ",";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select * from movcontarateio where movconta in (" + Uteis.removerUltimoCaractere(codMovContas) + ")");
        Uteis.logarDebug("Consultando MovContasRateio no banco de ORIGEM (antigo): " + conBancoAntigo.getCatalog() + " a serem copiadas");
        try (Statement stm = conBancoAntigo.createStatement()) {
            try (ResultSet dadosSQL = stm.executeQuery(sql.toString())) {
                while (dadosSQL.next()) {
                    MovContaRateioVO obj = new MovContaRateioVO();
                    obj.setCodigo(dadosSQL.getInt("codigo"));
                    obj.setMovContaVO(dadosSQL.getInt("movconta"));
                    obj.setPlanoContaVO(new PlanoContaTO());
                    obj.getPlanoContaVO().setCodigo(dadosSQL.getInt("planoconta"));
                    obj.setCentroCustoVO(new CentroCustoTO());
                    obj.getCentroCustoVO().setCodigo(dadosSQL.getInt("centrocusto"));
                    obj.setTipoDocumentoVO(new TipoDocumentoVO());
                    obj.getTipoDocumentoVO().setCodigo(dadosSQL.getInt("tipodocumento"));
                    obj.setFormaPagamentoVO(new FormaPagamentoVO());
                    obj.getFormaPagamentoVO().setCodigo(dadosSQL.getInt("formapagamento"));
                    obj.setTipoES(TipoES.getTipoPadrao(dadosSQL.getInt("tipoes")));
                    obj.setDescricao(dadosSQL.getString("descricao"));
                    obj.setValor(dadosSQL.getDouble("valor"));
                    movContasRateioVOS.add(obj);
                }
            }
        }
        return movContasRateioVOS;
    }

    private static void copiarMovContaRateio(List<MovContaRateioVO> movContasRateioVOSCopiar, Connection conBancoAtual) throws Exception {
        MovContaRateio movContaRateioDAOBancoAtual;
        try {
            Uteis.logarDebug("Encontrei " + movContasRateioVOSCopiar.size() + " movContasRateioVOS a serem copiadas");
            int atual = 1;

            try {
                Uteis.logarDebug("Vou conectar no banco destino para iniciar a cópia dos movContasRateios...");
                //INCLUIR NO BANCO DESTINO
                movContaRateioDAOBancoAtual = new MovContaRateio(conBancoAtual);
                for (MovContaRateioVO movContaRateioVO : movContasRateioVOSCopiar) {
                    Uteis.logarDebug("Copiando MovContaRateio " + atual + "/" + movContasRateioVOSCopiar.size() + " | Cód. " + movContaRateioVO.getCodigo() + " | " + movContaRateioVO.getDescricao() + " | MovConta: " + movContaRateioVO.getMovContaVO());
                    try {
                        atual++;
                        movContaRateioDAOBancoAtual.incluirOrigemProcessoCopiarContas(movContaRateioVO);
                        Uteis.logarDebug("Sucesso!");
                    } catch (Exception ex) {
                        Uteis.logarDebug("Erro ao copiar movContaVO: " + ex.getMessage());
                    }
                }
            } catch (Exception ex) {
            } finally {
                movContaRateioDAOBancoAtual = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static List<CaixaMovContaVO> consultarCaixasMovContaExcluidas(List<MovContaVO> movContaVOS, Connection conBancoAntigo) throws Exception {
        List<CaixaMovContaVO> caixaMovContaVOS = new ArrayList<CaixaMovContaVO>();

        String codCaixasMovContas = "";
        for (MovContaVO movContaVO : movContaVOS) {
            codCaixasMovContas += movContaVO.getCodigo() + ",";
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select * from caixamovconta where movconta in (" + Uteis.removerUltimoCaractere(codCaixasMovContas) + ")");
        Uteis.logarDebug("Consultando CaixaMovConta no banco de ORIGEM (antigo): " + conBancoAntigo.getCatalog() + " a serem copiadas");
        try (Statement stm = conBancoAntigo.createStatement()) {
            try (ResultSet dadosSQL = stm.executeQuery(sql.toString())) {
                while (dadosSQL.next()) {
                    CaixaMovContaVO obj = new CaixaMovContaVO();
                    obj.setCodigo(dadosSQL.getInt("codigo"));
                    obj.setCaixaVo(new CaixaVO());
                    obj.getCaixaVo().setCodigo(dadosSQL.getInt("caixa"));
                    obj.setMovContaVo(new MovContaVO());
                    obj.getMovContaVo().setCodigo(dadosSQL.getInt("movconta"));
                    obj.setDescricao(dadosSQL.getString("descricao"));
                    obj.setValor(dadosSQL.getDouble("valor"));
                    obj.setDataMovimento(dadosSQL.getTimestamp("datamovimento"));
                    caixaMovContaVOS.add(obj);
                }
            }
        }
        return caixaMovContaVOS;
    }

    private static void copiarCaixaMovConta(List<CaixaMovContaVO> caixasMovContaVOSCopiar, Connection conBancoAtual) throws Exception {
        CaixaMovConta caixaMovContaDAOBancoAtual;
        try {
            Uteis.logarDebug("Encontrei " + caixasMovContaVOSCopiar.size() + " caixaMovConta a serem copiadas");
            int atual = 1;

            try {
                Uteis.logarDebug("Vou conectar no banco destino para iniciar a cópia dos caixaMovConta...");
                //INCLUIR NO BANCO DESTINO
                caixaMovContaDAOBancoAtual = new CaixaMovConta(conBancoAtual);
                for (CaixaMovContaVO caixaMovContaVO : caixasMovContaVOSCopiar) {
                    Uteis.logarDebug("Copiando caixaMovConta " + atual + "/" + caixasMovContaVOSCopiar.size() + " | Cód. " + caixaMovContaVO.getCodigo() + " | " + caixaMovContaVO.getDescricao() + " | MovConta: " + caixaMovContaVO.getMovContaVo().getCodigo());
                    try {
                        atual++;
                        caixaMovContaDAOBancoAtual.incluirOrigemProcessoCopiarContas(caixaMovContaVO);
                        Uteis.logarDebug("Sucesso!");
                    } catch (Exception ex) {
                        Uteis.logarDebug("Erro ao copiar movContaVO: " + ex.getMessage());
                    }
                }
            } catch (Exception ex) {
            } finally {
                caixaMovContaDAOBancoAtual = null;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void validacoesPrincipais(List<MovContaVO> movContaVOSCopiar, Connection conBancoAtual) throws SQLException {
        if (UteisValidacao.emptyList(movContaVOSCopiar)) {
            Uteis.logarDebug("Nada encontrado para ser copiado para o banco DESTINO: " + conBancoAtual.getCatalog());
            throw new SQLException("Nada encontrado para ser copiado para o banco DESTINO: " + conBancoAtual.getCatalog());
        } else {
            Uteis.logarDebug("Encontrei " + movContaVOSCopiar.size() + " movContaVOS a serem copiadas");
        }
    }
}
