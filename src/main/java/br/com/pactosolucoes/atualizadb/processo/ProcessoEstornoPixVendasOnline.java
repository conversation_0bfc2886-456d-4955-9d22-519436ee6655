package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PixVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.Pix;
import negocio.facade.jdbc.vendas.VendasOnlineVenda;
import org.json.JSONObject;
import servicos.pix.PixStatusEnum;
import servicos.vendasonline.dto.RetornoVendaTO;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class ProcessoEstornoPixVendasOnline {

    public static void main(String[] args) {
        try {
//            Connection con = DriverManager.getConnection("******************************************", "zillyonweb", "pactodb");
            Connection con = new DAO().obterConexaoEspecifica("teste");
            ProcessoEstornoPixVendasOnline.processar(con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void processar(Connection con) {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("v.pix, \n");
            sql.append("p.status, \n");
            sql.append("v.dados \n");
            sql.append("from vendasonlinevenda v \n");
            sql.append("inner join pix p on p.codigo = v.pix \n");
            sql.append("where p.status not in ('ATIVA','CONCLUIDA') \n");
            sql.append("order by v.pix \n");
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
            while (rs.next()) {
                Pix pixDAO;
                VendasOnlineVenda vendasOnlineVendaDAO;
                PixVO pixVO = null;
                try {
                    pixDAO = new Pix(con);
                    vendasOnlineVendaDAO = new VendasOnlineVenda(con);

                    pixVO = pixDAO.consultarPorCodigo(rs.getInt("pix"));
                    Uteis.logarDebug("VERIFICAR DEVE ESTORNAR | PIX " + pixVO.getCodigo() + " | STATUS " + pixVO.getStatus());


                    if (pixVO.getStatusEnum().equals(PixStatusEnum.ATIVA) ||
                            pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                        continue;
                    }

                    JSONObject dados = new JSONObject(rs.getString("dados"));
                    RetornoVendaTO retDTO = new RetornoVendaTO(dados);

                    Integer contrato = UteisValidacao.emptyNumber(retDTO.getContrato()) ? null : retDTO.getContrato();
                    Integer vendaAvulsa = UteisValidacao.emptyNumber(retDTO.getVendaAvulsa()) ? null : retDTO.getVendaAvulsa();
                    Integer aulaavulsadiaria = (retDTO.getDiariaVO() != null && !UteisValidacao.emptyNumber(retDTO.getDiariaVO().getCodigo())) ? retDTO.getDiariaVO().getCodigo() : null;

                    verificarSeTodasParcelasEmAberto(contrato, vendaAvulsa, aulaavulsadiaria, con);

                    vendasOnlineVendaDAO.estornarVendaVendasOnline(pixVO);
                    Uteis.logarDebug("ESTORNADO --> PIX " +  pixVO.getCodigo());
                } catch (Exception ex) {
                    Uteis.logarDebug("ERRO --> PIX " +  (pixVO != null ? pixVO.getCodigo() : "") + " | " + ex.getMessage());
                } finally {
                    pixDAO = null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void verificarSeTodasParcelasEmAberto(Integer contrato, Integer vendaAvulsa,
                                                         Integer aulaavulsadiaria, Connection con) throws Exception {
        MovParcela movParcelaDAO;
        String log = "";
        try {
            movParcelaDAO = new MovParcela(con);

            StringBuilder sqlContrato = new StringBuilder();
            sqlContrato.append("select \n");
            sqlContrato.append("count(mp.codigo) as qtd \n");
            sqlContrato.append("from movparcela mp\n");
            sqlContrato.append("where mp.situacao not in ('EA')\n");
            if (!UteisValidacao.emptyNumber(contrato)) {
                sqlContrato.append("and mp.contrato = ").append(contrato).append("\n");
                log += (" | Contrato " + contrato);
            } else if (!UteisValidacao.emptyNumber(vendaAvulsa)) {
                sqlContrato.append("and mp.vendaAvulsa = ").append(vendaAvulsa).append("\n");
                log += (" | VendaAvulsa " + vendaAvulsa);
            } else {
                sqlContrato.append("and mp.aulaavulsadiaria = ").append(aulaavulsadiaria).append("\n");
                log += (" | AulaAvulsaDiaria " + aulaavulsadiaria);
            }

            ResultSet rsCont = SuperFacadeJDBC.criarConsulta(sqlContrato.toString(), con);
            if (rsCont.next()) {
                if (UteisValidacao.emptyNumber(rsCont.getInt("qtd"))) {
                    //verificar se todas as parcelas estão em aberto e sem pendencia de retorno
                    List<MovParcelaVO> parcelas = new ArrayList<>();
                    if (!UteisValidacao.emptyNumber(contrato)) {
                        parcelas = movParcelaDAO.consultarPorContrato(contrato, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    } else if (!UteisValidacao.emptyNumber(vendaAvulsa)) {
                        parcelas = movParcelaDAO.consultarPorCodigoVendaAvulsaLista(vendaAvulsa, null, null, null, false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    } else {
                        parcelas = movParcelaDAO.consultarPorAulaAvulsaDiaria(aulaavulsadiaria, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                    }

                    if (UteisValidacao.emptyList(parcelas)) {
                        throw new Exception("Nenhuma parcela encontrada" + log);
                    }

                    for (MovParcelaVO movParcelaVO : parcelas) {
                        Boolean bloq = movParcelaDAO.parcelaEstaBloqueadaPorCobranca(movParcelaVO);
                        if (bloq != null && bloq) {
                            throw new Exception("Parcela pendente | Cod " + movParcelaVO.getCodigo() + log);
                        }
                    }
                } else {
                    throw new Exception("Existe parcela que não está em aberto" + log);
                }
            }
        } finally {
            movParcelaDAO = null;
        }
    }
}
