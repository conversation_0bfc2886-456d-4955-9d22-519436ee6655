package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "14/11/2024",
        descricao = "Add columns member, sales, receivables",
        motivacao = "GC-1135")
public class AtualizacaoTicketGC1135 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE \"member\" ADD COLUMN memberjson json;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE \"member\" ADD COLUMN salesjson json;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE \"member\" ADD COLUMN receivablesjson json;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE \"member\" ADD COLUMN sincronizado BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX member_idmember_idx ON public.member USING btree (idmember);", c);
        }
    }
}
