package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Luiz Felipe",
        data = "13/07/2024",
        descricao = "FP-32 - Régua de Cobrança",
        motivacao = "FP-32 - Régua de Cobrança")
public class AtualizacaoTicketFP51 implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            //
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pactopaycomunicacao ADD COLUMN pactopayconfig integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaycomunicacao_pactopayconfig ON public.pactopaycomunicacao USING btree (pactopayconfig);", c);
            //pactopayenvioemail
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.pactopayenvioemail (\n" +
                    "\tcodigo serial4 NOT NULL,\n" +
                    "\tdataregistro timestamp DEFAULT now() NULL,\n" +
                    "\tpactopayconfig int4 NULL,\n" +
                    "\tempresa int4 NULL,\n" +
                    "\tassunto text NULL,\n" +
                    "\tmensagem text NULL,\n" +
                    "\tstatus int4 NULL,\n" +
                    "\tdados text NULL,\n" +
                    "\tenvio text NULL,\n" +
                    "\tresposta text NULL,\n" +
                    "\tjobexcluido bool DEFAULT false NULL,\n" +
                    "\tdataexecucao timestamp DEFAULT now() NULL,\n" +
                    "\tCONSTRAINT pactopayenvio_pkey PRIMARY KEY (codigo)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX pactopayenvioemail_pactopayconfig_idx ON public.pactopayenvioemail USING btree (pactopayconfig);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX pactopayenvioemail_status_idx ON public.pactopayenvioemail USING btree (status);", c);
            //pactopayenvioemailcomunicacao
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.pactopayenvioemailcomunicacao (\n" +
                    "\tcodigo serial4 NOT NULL,\n" +
                    "\tpactopayenvioemail int4 NULL,\n" +
                    "\tpactopaycomunicacao int4 NULL,\n" +
                    "\tCONSTRAINT pactopayenvioemailcomunicacao_pkey PRIMARY KEY (codigo)\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX pactopayenvioemailcomunicacao_pactopayenvioemail_idx ON public.pactopayenvioemailcomunicacao USING btree (pactopayenvioemail);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX pactopayenvioemailcomunicacao_pactopaycomunicacao_idx ON public.pactopayenvioemailcomunicacao USING btree (pactopaycomunicacao);", c);
            //pactopaylog
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE TABLE public.pactopaylog (\n" +
                    "\tcodigo serial primary key,\n" +
                    "\tdataregistro TIMESTAMP WITHOUT TIME zone,\n" +
                    "\tpactopaycomunicacao integer,\n" +
                    "\tpactopayenvioemail integer,\n" +
                    "\toperacao text,\n" +
                    "\tdados text\n" +
                    ");", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaylog_pactopaycomunicacao ON public.pactopaylog USING btree (pactopaycomunicacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_pactopaylog_pactopayenvioemail ON public.pactopaylog USING btree (pactopayenvioemail);", c);
        }
    }
}
