package br.com.pactosolucoes.atualizadb.processo;

import controle.contrato.ContratoControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.*;
import negocio.comuns.financeiro.*;
import negocio.comuns.plano.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.contrato.*;
import negocio.facade.jdbc.financeiro.*;
import negocio.facade.jdbc.plano.CondicaoPagamento;
import negocio.facade.jdbc.plano.Horario;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.facade.jdbc.utilitarias.Conexao;
import negocio.interfaces.contrato.ContratoInterfaceFacade;

import java.sql.*;
import java.text.ParseException;
import java.util.*;
import java.util.Date;

/**
 * Created by Joao Alcides on 12/10/2016.
 */
public class GerarContratoDePlano {

    static PlanoDuracao planoD;
    static Contrato contrato;
    static MovPagamento movPagamento;
    static PagamentoMovParcela pagamentoMovParcela;
    static ReciboPagamento reciboPagamento;
    static MovParcela movParcela;
    static Connection con;
    static ContratoCondicaoPagamento contratoCondicaoPagamento;
    static ContratoComposicao contratoComposicao;
    static ContratoTextoPadrao contratoTextoPadrao;
    static ContratoModalidade contratoModalidade;
    static ContratoPlanoProdutoSugerido contratoPlanoProdutoSugerido;
    static ContratoDuracao contratoDuracao;
    static ContratoHorario contratoHorario;
    static Colaborador colaborador;
    static HistoricoContrato historicoContrato;
    static PeriodoAcessoCliente periodoAcessoCliente;
    static MovProduto movProduto;
    static MovProdutoParcela movProdutoParcelaDao;

    static Map<String, PlanoDuracaoVO> planosDuracoes = new HashMap<String, PlanoDuracaoVO>();

    static CondicaoPagamentoVO condPag;
    static HorarioVO horarioVOFix;
    static FormaPagamentoVO formaPagamentoVO;
    static ProdutoVO prodMatricula;
    static PlanoVO planovo;
    static UsuarioVO usuarioVO;
    static PlanoDuracaoVO planoDuracao;


    public static void main(String [] args) throws Exception{
        Connection cons = DriverManager.getConnection("***************************************************", "postgres", "pactodb");
        con = cons;

        gerarContrato(con, 3, Uteis.getDate("14/12/2014"), Uteis.getDate("13/12/2015"), 3756, 12, 1, 600.0, 1);
    }

    public static void gerarContrato(Connection con1, Integer plano, Date inicio, Date fim, Integer matricula,
                                     Integer nrMeses, Integer empresa, Double valor,
                                     Integer formaPagamentoCod) throws Exception{
        con = con1;
        criarColuna(con1);
        contrato = new Contrato(con1);
        movPagamento = new MovPagamento(con1);
        pagamentoMovParcela = new PagamentoMovParcela(con1);
        reciboPagamento = new ReciboPagamento(con1);
        movParcela = new MovParcela(con1);
        contratoCondicaoPagamento = new ContratoCondicaoPagamento(con1);
        contratoComposicao = new ContratoComposicao(con1);
        contratoTextoPadrao = new ContratoTextoPadrao(con1);
        contratoModalidade = new ContratoModalidade(con1);
        contratoPlanoProdutoSugerido = new ContratoPlanoProdutoSugerido(con1);
        contratoDuracao = new ContratoDuracao(con1);
        contratoHorario = new ContratoHorario(con1);
        colaborador = new Colaborador(con1);
        historicoContrato = new HistoricoContrato(con1);
        periodoAcessoCliente = new PeriodoAcessoCliente(con1);
        movProduto = new MovProduto(con1);
        movProdutoParcelaDao = new MovProdutoParcela(con1);
        planovo = new PlanoVO();
        planovo.setCodigo(plano);
        formaPagamentoVO = new FormaPagamentoVO();
        formaPagamentoVO.setCodigo(formaPagamentoCod);

        ResultSet rsPlanoDuracao = SuperFacadeJDBC.criarConsulta("select codigo from planoduracao where plano = " + plano
                + " and numeromeses = " + nrMeses, con1);
        if(rsPlanoDuracao.next()){
            planoDuracao = new PlanoDuracaoVO();
            planoDuracao.setCodigo(rsPlanoDuracao.getInt("codigo"));
        }

        ResultSet rsMatricula = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM produto WHERE tipoproduto  = 'MA'", con1);
        if(rsMatricula.next()){
            prodMatricula = new ProdutoVO();
            prodMatricula.setCodigo(rsMatricula.getInt("codigo"));
        }
        condPag = new CondicaoPagamentoVO();
        condPag.setCodigo(1);

        Usuario usuarioDao = new Usuario(con1);
        usuarioVO = usuarioDao.consultarPorNomeUsuario("PACTO - MÉTODO DE GESTÃO", false, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

        ResultSet rsCliente = SuperFacadeJDBC.criarConsulta("SELECT cliente.codigo, pessoa, nome FROM cliente " +
                " inner join pessoa on pessoa.codigo = cliente.pessoa " +
                "WHERE codigomatricula = " + matricula, con1);
        if(rsCliente.next()){
            Integer codigoPessoa = rsCliente.getInt("pessoa");
            ResultSet rsModalidade = SuperFacadeJDBC.criarConsulta("select modalidade from planomodalidade  where plano = " + plano + " order by codigo limit 1 ", con1);
            if(rsModalidade.next()){
                ResultSet rsHorario = SuperFacadeJDBC.criarConsulta("select horario from planohorario  where plano = " + plano + " order by codigo limit 1 ", con1);
                if(rsHorario.next()){

                    Integer codigoPrimeiroContrato = null;
                    ResultSet rsPrimeiroContrato = SuperFacadeJDBC.criarConsulta("select codigo from contrato where situacaocontrato = 'MA' AND pessoa = " + codigoPessoa, con1);
                    if(rsPrimeiroContrato.next()){
                        codigoPrimeiroContrato = rsPrimeiroContrato.getInt("codigo");
                    }

                    horarioVOFix = new HorarioVO();
                    horarioVOFix.setCodigo(rsHorario.getInt("horario"));
                    ContratoVO contrato = montarContrato(inicio, fim, codigoPessoa, nrMeses, plano, empresa, rsModalidade.getInt("modalidade"), valor,
                            rsCliente.getString("nome"));
                    contrato = incluir(contrato, valor, con1);
                    if(codigoPrimeiroContrato != null){
                        SuperFacadeJDBC.executarConsulta("UPDATE contrato set situacaocontrato = 'RN', contratobaseadorenovacao = "+contrato.getCodigo()
                                +" where codigo = "+codigoPrimeiroContrato, con1);
                        SuperFacadeJDBC.executarConsulta("UPDATE contrato set contratoresponsavelrenovacaomatricula = "+codigoPrimeiroContrato
                                +" where codigo = "+contrato.getCodigo(), con1);
                    }

                }
            }
        }
    }

    public static ContratoVO montarContrato(Date inicio, Date fim, Integer codigoPessoa, Integer nrMeses,
                                     Integer planoCod, Integer codigoEmpresa, Integer modalidade,
                                     Double valorContrato,
                                            String nome) throws Exception {
        ContratoVO contrato = new ContratoVO();
        contrato.getPessoa().setCodigo(codigoPessoa);
        contrato.getPessoa().setNome(nome);
        ContratoDuracaoVO cDuracao = new ContratoDuracaoVO();
        Date lancamento = inicio;
        contrato.setVigenciaDe(inicio);
        cDuracao.setNumeroMeses(nrMeses);
        cDuracao.setCarencia(60);
        contrato.setContratoDuracao(cDuracao);
        contrato.setVigenciaAte(fim);
        contrato.setDataLancamento(lancamento);
        contrato.setVigenciaAteAjustada(fim);
        contrato.setDataMatricula(inicio);
        contrato.setSituacao("RN");

        contrato.setDataPrevistaRematricula(fim);
        contrato.setDataPrevistaRenovar(fim);

        contrato = criar(contrato, planoCod, codigoEmpresa, modalidade);
        contrato.setValorFinal(valorContrato);
        contrato.setValorBaseCalculo(valorContrato);
        contrato.setResponsavelContrato(usuarioVO);
        preencher(contrato, 0, 0.0, modalidade,
                planoDuracao,
                condPag,
                horarioVOFix);
        return contrato;
    }

    public static ContratoVO criar(ContratoVO contratoP, Integer planoCod, Integer codigoEmpresa, Integer modalidade){
        try {

            //responsavel
            ContratoVO contrato = contratoP;
            //empresa
            EmpresaVO empresa = new EmpresaVO();
            empresa.setCodigo(codigoEmpresa);
            contrato.setEmpresa(empresa);

            //plano
            PlanoVO plano = new PlanoVO();
            plano.setCodigo(planoCod);
            contrato.setPlano(plano);

            return contrato;
        } catch (Exception e) {
            return contratoP;
        }

    }
    public static ContratoVO preencher(ContratoVO contrato, Integer codigoHorario, Double valorMatricula, Integer modalidade,
                                PlanoDuracaoVO planoDuracao,
                                CondicaoPagamentoVO condPag,
                                HorarioVO horarioVOFix) throws SQLException, Exception{
        //contrato modalidade
        List contratoModalidadeVOs = new ArrayList();
        ContratoModalidadeVO contMod = new ContratoModalidadeVO();
        ModalidadeVO obj = new ModalidadeVO();
        obj.setCodigo(modalidade);
        obj.setNome("modalidade");
        obj.setModalidadeEscolhida(true);
        Double valor = (contrato.getValorFinal()-valorMatricula)/contrato.getContratoDuracao().getNumeroMeses();

        //modalidade
        contMod.setModalidade(obj);
        contMod.setNrVezesSemana(7);
        contMod.setValorModalidade(valor);
        contMod.setValorFinalModalidade(valor);


        //vezes semana
        PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
        planoVezesSemanaVO.setNrVezes(1);
        planoVezesSemanaVO.setVezeSemanaEscolhida(true);
        contMod.setPlanoVezesSemanaVO(planoVezesSemanaVO );



        //utilizar turma
        //modalidade escolhida
        contMod.getModalidade().setUtilizarTurma(false);
        contMod.getModalidade().setModalidadeEscolhida(true);

        contratoModalidadeVOs.add(contMod);

        if(UteisValidacao.emptyNumber(planoDuracao.getCodigo())){
            planoDuracao.setCodigo(1);
            contrato.setPlanoDuracao(planoDuracao);
            contrato.getPlanoDuracao().setDuracaoEscolhida(true);
            contrato.getPlanoDuracao().setNrMaximoParcelasCondPagamento(1);
            contrato.getPlanoDuracao().setNumeroMeses(contrato.getContratoDuracao().getNumeroMeses());
            contrato.getPlanoDuracao().setValorDesejado(0.0);
            contrato.getPlanoDuracao().setCarencia(60);
            contrato.getPlanoDuracao().setTipoValor("PD");
            contrato.getPlanoDuracao().setTipoOperacao("AC");
        }else{
            planoDuracao.setCarencia(60);
            contrato.setPlanoDuracao(planoDuracao);
        }


        PlanoCondicaoPagamentoVO planoCondicaoPagamento = new PlanoCondicaoPagamentoVO();
        planoCondicaoPagamento.setCodigo(1);
        planoCondicaoPagamento.setCondicaoPagamento(condPag);
        contrato.setPlanoCondicaoPagamento(planoCondicaoPagamento);

        //horario contrato

        ContratoHorarioVO contratoHorario = new ContratoHorarioVO();
        HorarioVO horarioVO = new HorarioVO();
        if(UteisValidacao.emptyNumber(codigoHorario)){
            contratoHorario.setHorario(horarioVOFix);
            contrato.setContratoHorario(contratoHorario);
            PlanoHorarioVO planoHorario = new PlanoHorarioVO();
            planoHorario.setCodigo(1);
            planoHorario.setHorario(horarioVOFix);
            contrato.setPlanoHorario(planoHorario);
        }else{
            horarioVO.setCodigo(codigoHorario);
            contratoHorario.setHorario(horarioVO);
            contrato.setContratoHorario(contratoHorario);
            PlanoHorarioVO planoHorario = new PlanoHorarioVO();
            planoHorario.setCodigo(1);
            planoHorario.setHorario(horarioVO);
            contrato.setPlanoHorario(planoHorario);
        }
        contrato.setContratoModalidadeVOs(contratoModalidadeVOs);
        return contrato;
    }


    /**
     * @param contratoVO
     * @return
     * @throws Exception
     */
    public ReciboPagamentoVO montarReciboPagamento(ContratoVO contratoVO) throws Exception{

        ReciboPagamentoVO reciboPagamento = new ReciboPagamentoVO();
        reciboPagamento.setValorTotal(contratoVO.getValorFinal());
        reciboPagamento.setNomePessoaPagador(contratoVO.getPessoa().getNome());
        reciboPagamento.setPessoaPagador(contratoVO.getPessoa());
        reciboPagamento.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        reciboPagamento.setContrato(contratoVO);
//	    reciboPagamento.setda
        reciboPagamento.setData(contratoVO.getDataLancamento());
        reciboPagamento.getEmpresa().setCodigo(contratoVO.getEmpresa().getCodigo());
        return reciboPagamento;

    }

    /**
     * Montar um VO de historico contrato
     * @param contratoVO
     * @return
     */
    public static HistoricoContratoVO montarHistoricoContrato(ContratoVO contratoVO){
        HistoricoContratoVO historicoContrato = new HistoricoContratoVO();
        historicoContrato.setContrato(contratoVO.getCodigo());
        historicoContrato.setDescricao("MATRICULADO");
        historicoContrato.setResponsavelRegistro(contratoVO.getResponsavelContrato());
        historicoContrato.setDataFinalSituacao(contratoVO.getVigenciaAteAjustada());
        historicoContrato.setDataRegistro(contratoVO.getDataLancamento());
        historicoContrato.setDataInicioSituacao(contratoVO.getVigenciaDe());
        historicoContrato.setTipoHistorico("MA");
        return historicoContrato;
    }
    /**
     * Montar um vo de movparcela
     * @param contratoVO
     * @param recibo
     * @return
     * @throws Exception
     * @throws SQLException
     */

    public static MovParcelaVO montarMovParcela(ContratoVO contratoVO, ReciboPagamentoVO recibo, boolean debito, boolean valorDoContrato) throws SQLException, Exception{

        MovParcelaVO movParcelaVO = new MovParcelaVO();
        movParcelaVO.setContrato(contratoVO);
        movParcelaVO.setDataRegistro(contratoVO.getDataLancamento());
        movParcelaVO.setDataVencimento(contratoVO.getDataLancamento());
        if(debito){
            ResultSet resultSet = MovParcela.criarConsulta("SELECT count(codigo) FROM movparcela WHERE contrato = "+contratoVO.getCodigo(), con);
            if(resultSet.next()){
                int count = resultSet.getInt(1);
                movParcelaVO.setDescricao("PARCELA "+(count+1));
            }
        }else
            movParcelaVO.setDescricao("PARCELA 1");
        movParcelaVO.setReciboPagamento(recibo);
        movParcelaVO.setResponsavel(contratoVO.getResponsavelContrato());
        movParcelaVO.setSituacao("PG");
        movParcelaVO.setValorParcela(valorDoContrato ? contratoVO.getValorFinal() : recibo.getValorTotal());
        movParcelaVO.setPercentualJuro(0.0);
        movParcelaVO.setPercentualMulta(0.0);
        movParcelaVO.setPessoa(contratoVO.getPessoa());
        movParcelaVO.setEmpresa(contratoVO.getEmpresa());
        return movParcelaVO;
    }

    /**
     * Montar um VO de periodo acesso cliente
     * @param contratoVO
     * @return
     */
    public static PeriodoAcessoClienteVO montarPeriodoAcesso(ContratoVO contratoVO){
        PeriodoAcessoClienteVO periodoAcesso = new PeriodoAcessoClienteVO();
        periodoAcesso.setPessoa(contratoVO.getPessoa().getCodigo());
        periodoAcesso.setContrato(contratoVO.getCodigo());
        periodoAcesso.setDataInicioAcesso(contratoVO.getVigenciaDe());
        periodoAcesso.setDataFinalAcesso(contratoVO.getVigenciaAteAjustada());

        periodoAcesso.setTipoAcesso("CA");
        return periodoAcesso;

    }

    public static List<MovProdutoVO> montarMovProdutos(ContratoVO contratoVO, Double valorMatricula){
        List<MovProdutoVO> movProdutos = new ArrayList<MovProdutoVO>();

        Integer duracao = contratoVO.getContratoDuracao().getNumeroMeses();
        Date dataInicio = contratoVO.getVigenciaDe();
        Date dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);
        movProdutos.add(montarMatricula(contratoVO, dataInicio, valorMatricula, prodMatricula));
        if(duracao>1){
            Double valorMensalidade = (contratoVO.getValorFinal()-valorMatricula)/duracao;

            movProdutos.add(montarMensalidade(contratoVO, dataInicio, dataFim, 5, valorMensalidade));
            for(int i = 1; i<duracao; i++){
                dataInicio = dataFim;
                dataFim = Uteis.somarCampoData(dataInicio, Calendar.MONTH, 1);

                movProdutos.add(montarMensalidade(contratoVO, dataInicio, dataFim, 5, valorMensalidade));
            }
        }else{

            movProdutos.add(montarMensalidade(contratoVO, dataInicio, contratoVO.getVigenciaAte(), 5, (contratoVO.getValorFinal()-valorMatricula)));
        }
        return movProdutos;

    }

    /**
     * Monta um VO de mov produto
     * @param contratoVO
     * @return
     */
    public static MovProdutoVO montarMensalidade(ContratoVO contratoVO, Date inicio, Date fim,
                                                 Integer codProduto, Double valor){

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao("PG");
        movProduto.setDataFinalVigencia(fim);
        movProduto.setDataInicioVigencia(inicio);
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes+"/"+ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setQuantidade(1);
        movProduto.setDescricao("FITNESS SPORT CENTER | PLANO SOLO - " + mes+"/"+ano);
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        ProdutoVO produto = new ProdutoVO();
        produto.setCodigo(codProduto);
        movProduto.setProduto(produto);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        return movProduto;
    }


    public static MovProdutoVO montarMatricula(ContratoVO contratoVO, Date inicio, Double valor, ProdutoVO matricula){

        Integer ano = Uteis.getAnoData(inicio);
        String mes = Uteis.getMesReferencia(inicio);

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao("PG");
        movProduto.setAnoReferencia(ano);
        movProduto.setMesReferencia(mes+"/"+ano);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(valor);
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(valor);
        movProduto.setQuantidade(1);
        movProduto.setDescricao("MATRICULA");
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        movProduto.setProduto(matricula);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        return movProduto;
    }

    /**
     * Monta um VO de mov produto
     * @param contratoVO
     * @return
     */
    public MovProdutoVO montarMovProduto(ContratoVO contratoVO, ProdutoVO plano){

        String mesReferencia = Uteis.getMesReferencia(new Date());

        MovProdutoVO movProduto = new MovProdutoVO();
        movProduto.setSituacao("PG");
        movProduto.setDataFinalVigencia(contratoVO.getVigenciaAteAjustada());
        movProduto.setDataInicioVigencia(contratoVO.getVigenciaDe());
        movProduto.setAnoReferencia(Uteis.getAnoData(new Date()));
        movProduto.setMesReferencia(mesReferencia);
        movProduto.setResponsavelLancamento(contratoVO.getResponsavelContrato());
        movProduto.setDataLancamento(contratoVO.getDataLancamento());
        movProduto.setTotalFinal(contratoVO.getValorFinal());
        movProduto.setValorDesconto(0.0);
        movProduto.setPrecoUnitario(contratoVO.getValorFinal());
        movProduto.setQuantidade(1);
        movProduto.setDescricao("FITNESS SPORT CENTER | PLANO SOLO - " + mesReferencia+"/"+Uteis.getAnoData(new Date()));
        movProduto.setEmpresa(contratoVO.getEmpresa());
        movProduto.setPessoa(contratoVO.getPessoa());
        movProduto.setContrato(contratoVO);
        ProdutoVO produto = new ProdutoVO();
        produto.setCodigo(plano.getCodigo());
        movProduto.setProduto(produto);
        movProduto.setApresentarMovProduto(true);
        movProduto.setQuitado(true);
        return movProduto;
    }
    /**
     * Montar um VO de mov pagamento
     * @param contratoVO
     * @return
     * @throws Exception
     * @throws SQLException
     */
    public static MovPagamentoVO montarMovPagamento(ContratoVO contratoVO, ReciboPagamentoVO recibo, int id_recebe, int id_venda, FormaPagamentoVO formaPagamentoVO) throws SQLException, Exception{
        MovPagamentoVO movPagamento = new MovPagamentoVO();
        movPagamento.setPessoa(contratoVO.getPessoa());
        movPagamento.setDataLancamento(contratoVO.getDataLancamento());
        movPagamento.setDataPagamento(contratoVO.getDataLancamento());
        movPagamento.setDataQuitacao(contratoVO.getDataLancamento());
        movPagamento.setResponsavelPagamento(contratoVO.getResponsavelContrato());
        movPagamento.setReciboPagamento(recibo);
        movPagamento.setValor(recibo.getValorTotal());
        movPagamento.setValorTotal(recibo.getValorTotal());
        movPagamento.setEmpresa(contratoVO.getEmpresa());
        movPagamento.setFormaPagamento(formaPagamentoVO);
        movPagamento.setNomePagador(contratoVO.getPessoa().getNome());
        movPagamento.setMovPagamentoEscolhida(true);
        movPagamento.setNrParcelaCartaoCredito(0);
        return movPagamento;
    }

    public static PagamentoMovParcelaVO montarPagamentoMovParcela(Integer movPagamento, Double valorPago,
                                                           MovParcelaVO movParcela, ReciboPagamentoVO reciboPagamento){
        PagamentoMovParcelaVO pagMovParcela = new PagamentoMovParcelaVO();
        pagMovParcela.setMovPagamento(movPagamento);
        pagMovParcela.setMovParcela(movParcela);
        pagMovParcela.setValorPago(valorPago);
        pagMovParcela.setReciboPagamento(reciboPagamento);
        return pagMovParcela;


    }
    public static List<MovProdutoParcelaVO> montarMovProdutoParcela(
            Double valor, List<MovProdutoVO> movProdutos,
            Integer movParcela, ReciboPagamentoVO reciboPagamento, Connection con) throws SQLException, Exception{
        List<MovProdutoParcelaVO> mpps = new ArrayList<MovProdutoParcelaVO>();
        Ordenacao.ordenarLista(movProdutos, "codigo");
        for(MovProdutoVO movProduto : movProdutos){
            if(valor <= 0.0){
                break;
            }
            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT sum(valorpago) as valorpago from movprodutoparcela where movproduto = "+movProduto.getCodigo(),
                    con);
            rs.next();
            Double valorpago = rs.getDouble("valorpago");
            MovProdutoParcelaVO movProd = new MovProdutoParcelaVO();
            movProd.setMovProduto(movProduto.getCodigo());
            Double valorDoProduto = valorpago == null ? movProduto.getTotalFinal() : movProduto.getTotalFinal()  - valorpago;

            if(valorDoProduto <= 0.0){
                continue;
            }

            if (valor >= valorDoProduto) {
                movProd.setValorPago(valorDoProduto);
                valor = valor - movProduto.getTotalFinal();
                SuperFacadeJDBC.executarConsulta("UPDATE movproduto SET situacao = 'PG' WHERE codigo = "+movProduto.getCodigo(), con);
            } else {
                movProd.setValorPago(valor);
                valor = 0.0;
            }
            movProd.setMovParcela(movParcela);
            movProd.setReciboPagamento(reciboPagamento);
            mpps.add(movProd);

        }
        return mpps;
    }



    public static void gerarTodoPagamentoParcelas(ContratoVO obj, Double valorPago, boolean debito, int id_recebe,int id_venda, Double valorMatricula, Connection con) throws Exception{
        List<MovPagamentoVO> listaSelectItemMovPagamentoVOs = new ArrayList<MovPagamentoVO>();
        if(debito){
            ReciboPagamentoVO reciboPagamentoVO = verificarPagamentoExiste(id_recebe, valorPago, con);
            MovPagamentoVO movPagamento = consultarMovPagamento(id_recebe, valorPago, con);
            //movpagamento
            montarPagamentos(obj,reciboPagamentoVO, movPagamento.getCodigo(), valorPago, debito, true, valorMatricula, con);
        }else{
            ReciboPagamentoVO recibo = gerarRecibo(obj, valorPago);
            listaSelectItemMovPagamentoVOs.add(montarMovPagamento(obj, recibo, id_recebe,id_venda, formaPagamentoVO));
            movPagamento.incluirMovPagamentos(listaSelectItemMovPagamentoVOs);
            //movpagamento
            MovPagamentoVO movP = listaSelectItemMovPagamentoVOs.get(0);
            montarPagamentos(obj,recibo, movP.getCodigo(), valorPago, debito, false, valorMatricula, con);
        }

    }

    public static ContratoVO incluir(ContratoVO contratoVO, Double valor, Connection con) throws Exception {

        try {
            con.setAutoCommit(false);
            contrato.setListaSelectItemMovPagamentoVOs(new ArrayList());
            contratoVO = incluirOutOfCommit(contratoVO, 0, valor, con);
            gerarTodoPagamentoParcelas(contratoVO,valor,false, 0,0, 0.0, con);
            con.commit();
        } catch (Exception e) {
            con.setAutoCommit(true);
            con.rollback();
            throw e;
        }finally{
            con.setAutoCommit(true);
        }
        return contratoVO;
    }

    /**
     * @param contratoVO
     * @throws Exception
     */
    private static void montarPagamentos(ContratoVO contratoVO,
                                  ReciboPagamentoVO reciboVO, Integer movPagamento, Double valor,
                                  boolean debito, boolean valorDoContrato, Double valorMatricula, Connection con) throws Exception {

//		reciboVO.setCodigo(getFacade().getReciboPagamento()
//				.obterValorChavePrimariaCodigo());
        // incluir mov parcelas
        MovParcelaVO movParcelaVO = montarMovParcela(contratoVO, reciboVO, debito, valorDoContrato);
        movParcela.incluirSemCommit(movParcelaVO);
        // incluir pagamentomovparcela
        pagamentoMovParcela.incluir(
                montarPagamentoMovParcela(movPagamento, reciboVO.getValorTotal(), movParcelaVO, reciboVO));
        List<MovProdutoVO> movProdutos = new ArrayList<MovProdutoVO>();

        if (debito) {
            movProdutos = movProduto.consultarPorCodigoContrato(contratoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        }else{
            // incluir historico
            historicoContrato.incluirSemCommit(
                    montarHistoricoContrato(contratoVO), true);
            // incluir periodoacessocliente
            periodoAcessoCliente.incluirSemCommit(
                    montarPeriodoAcesso(contratoVO));
            // incluir movproduto
            movProdutos = montarMovProdutos(contratoVO, valorMatricula);
            movProduto.incluirLista(movProdutos);
        }
        // incluir movprodutoparcela
        List<MovProdutoParcelaVO> movProdutoParcela = montarMovProdutoParcela(
                valor, movProdutos, movParcelaVO.getCodigo(), reciboVO, con);
        for (MovProdutoParcelaVO mpp : movProdutoParcela) {
            movProdutoParcelaDao.incluir(mpp);
        }
    }

    public static ReciboPagamentoVO gerarRecibo(ContratoVO obj, Double valorPago) throws Exception {
        ReciboPagamentoVO recibo = new ReciboPagamentoVO();
        recibo.setContrato(obj);
        recibo.setData(obj.getDataLancamento());
        recibo.setNomePessoaPagador(obj.getPessoa().getNome());
        recibo.setPessoaPagador(obj.getPessoa());
        recibo.setResponsavelLancamento(obj.getResponsavelContrato());
        recibo.setValorTotal(valorPago);
        recibo.setEmpresa(obj.getEmpresa());
        reciboPagamento.incluir(recibo);
        return recibo;
    }

    private static void inicializarDadosConsultor(ContratoVO obj) throws Exception {
        obj.setConsultor(colaborador.consultarColaboradorPorPessoaVinculada(obj.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
    }

    public static void criarColuna(Connection con) throws SQLException{
        try{
            con.createStatement().execute("ALTER TABLE CONTRATO ADD COLUMN id_externo int;");
        }catch (Exception e) {
            // TODO: handle exception
        }
        try{
            con.createStatement().execute("ALTER TABLE movpagamento ADD COLUMN id_recebe int;");
        }catch (Exception e) {
            // TODO: handle exception
        }
        try{
            con.createStatement().execute("ALTER TABLE CONTRATO ADD COLUMN importacao boolean;");
        }catch (Exception e) {
            // TODO: handle exception
        }
        try{
            con.createStatement().execute("ALTER TABLE plano ADD correspondencia_zd character varying (100);");
            con.createStatement().execute("ALTER TABLE horario ADD correspondencia_zd character varying (100);");
        }catch (Exception e) {
            // TODO: handle exception
        }
        try{
            con.createStatement().execute("ALTER TABLE movpagamento ADD COLUMN id_venda int;");
        }catch (Exception e) {
            // TODO: handle exception
        }
        con.commit();

        criarColunaVendaProdutos();
        criarColunaVendaAvulsa();

    }

    public static void criarColunaVendaProdutos() throws SQLException{
        try{
            con.createStatement().execute("ALTER TABLE produto ADD COLUMN id_externo int;");
        }catch (Exception e) {
            // TODO: handle exception
        }

//		con.commit();

    }

    public static void criarColunaVendaAvulsa() throws SQLException{
        try{
            con.createStatement().execute("ALTER TABLE vendaavulsa ADD COLUMN id_movimento int;");
        }catch (Exception e) {
            // TODO: handle exception
        }
        try{
            con.createStatement().execute("ALTER TABLE movpagamento ADD COLUMN id_movimento int;");
        }catch (Exception e) {
            // TODO: handle exception
        }

        con.commit();

    }

    public static ContratoVO incluirOutOfCommit(ContratoVO obj, int id_recebe, Double valorPago, Connection con) throws ConsistirException, Exception, SQLException, ParseException {
        inicializarDadosConsultor(obj);
        obj.setResponsavelContrato(usuarioVO);
        obj.realizarUpperCaseDados();
        String sql = "INSERT INTO Contrato( empresa, pessoa, plano, situacao, "
                + "estendeCoberturaFamiliares, vigenciaDe, vigenciaAte, valorBaseCalculo, valorFinal, responsavelLiberacaoCondicaoPagamento, convenioDesconto, "
                + "observacao, responsavelContrato, pagarComBoleto, dividirProdutosNasParcelas, desconto, tipoDesconto, valorDescontoEspecifico, "
                + "dataLancamento, situacaoContrato, dataMatricula, dataPrevistaRenovar, situacaoRenovacao, "
                + "contratoBaseadoRenovacao, dataPrevistaRematricula, situacaoRematricula, "
                + "contratoBaseadoRematricula, vigenciaAteAjustada, contratoResponsavelRenovacaoMatricula, "
                + "contratoResponsavelRematriculaMatricula, somaProduto,nomeModalidades, valorDescontoPorcentagem, "
                + "bolsa, naoPermitirRenovacaoRematriculaDeContratoAnteriores, consultor, diaVencimentoProrata, datarematricularealizada, id_externo, importacao) "
                + "VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, true)";
        PreparedStatement sqlInserir = con.prepareStatement(sql);
        if (obj.getEmpresa().getCodigo().intValue() != 0) {
            sqlInserir.setInt(1, obj.getEmpresa().getCodigo().intValue());
        } else {
            sqlInserir.setNull(1, 0);
        }
        if (obj.getPessoa().getCodigo().intValue() != 0) {
            sqlInserir.setInt(2, obj.getPessoa().getCodigo().intValue());
        } else {
            sqlInserir.setNull(2, 0);
        }
        if (obj.getPlano().getCodigo().intValue() != 0) {
            sqlInserir.setInt(3, obj.getPlano().getCodigo().intValue());
        } else {
            sqlInserir.setNull(3, 0);
        }
        sqlInserir.setString(4, obj.getSituacao());
        sqlInserir.setBoolean(5, obj.getEstendeCoberturaFamiliares());
        sqlInserir.setDate(6, Uteis.getDataJDBC(obj.getVigenciaDe()));
        sqlInserir.setDate(7, Uteis.getDataJDBC(obj.getVigenciaAte()));
        sqlInserir.setDouble(8, obj.getValorBaseCalculo().doubleValue());
        sqlInserir.setDouble(9, obj.getValorFinal().doubleValue());
        if (obj.getResponsavelLiberacaoCondicaoPagamento().getCodigo().intValue() != 0) {
            sqlInserir.setInt(10, obj.getResponsavelLiberacaoCondicaoPagamento().getCodigo().intValue());
        } else {
            sqlInserir.setNull(10, 0);
        }
        if (obj.getConvenioDesconto().getCodigo().intValue() != 0) {
            sqlInserir.setInt(11, obj.getConvenioDesconto().getCodigo().intValue());
        } else {
            sqlInserir.setNull(11, 0);
        }
        sqlInserir.setString(12, obj.getObservacao());
        if (obj.getResponsavelContrato().getCodigo().intValue() != 0) {
            sqlInserir.setInt(13, obj.getResponsavelContrato().getCodigo().intValue());
        } else {
            sqlInserir.setNull(13, 0);
        }
        sqlInserir.setBoolean(14, obj.getPagarComBoleto());
        sqlInserir.setBoolean(15, obj.getDividirProdutosNasParcelas());
        if (obj.getDesconto().getCodigo().intValue() != 0) {
            sqlInserir.setInt(16, obj.getDesconto().getCodigo().intValue());
        } else {
            sqlInserir.setNull(16, 0);
        }
        sqlInserir.setString(17, obj.getTipoDesconto());
        sqlInserir.setDouble(18, obj.getValorDescontoEspecifico().doubleValue());
        sqlInserir.setTimestamp(19, Uteis.getDataJDBCTimestamp(obj.getDataLancamento()));
        sqlInserir.setString(20, obj.getSituacaoContrato());
        sqlInserir.setDate(21, Uteis.getDataJDBC(obj.getDataMatricula()));
        sqlInserir.setDate(22, Uteis.getDataJDBC(obj.getDataPrevistaRenovar()));
        sqlInserir.setString(23, obj.getSituacaoRenovacao());
        sqlInserir.setInt(24, obj.getContratoBaseadoRenovacao().intValue());
        sqlInserir.setDate(25, Uteis.getDataJDBC(obj.getDataPrevistaRematricula()));
        sqlInserir.setString(26, obj.getSituacaoRematricula());
        sqlInserir.setInt(27, obj.getContratoBaseadoRematricula().intValue());
        sqlInserir.setDate(28, Uteis.getDataJDBC(obj.getVigenciaAteAjustada()));
        sqlInserir.setInt(29, obj.getContratoResponsavelRenovacaoMatricula().intValue());
        sqlInserir.setInt(30, obj.getContratoResponsavelRematriculaMatricula().intValue());
        sqlInserir.setDouble(31, obj.getSomaProduto().doubleValue());
        obj.obterNomeModalidadeCliente();
        sqlInserir.setString(32, obj.getNomeModalidades());
        sqlInserir.setDouble(33, obj.getValorDescontoPorcentagem());
        sqlInserir.setBoolean(34, obj.getBolsa());
        sqlInserir.setBoolean(35, obj.getNaoPermitirRenovacaoRematriculaDeContratoAnteriores());
        if (obj.getConsultor().getCodigo().intValue() != 0) {
            sqlInserir.setInt(36, obj.getConsultor().getCodigo().intValue());
        } else {
            sqlInserir.setNull(36, 0);
        }
        sqlInserir.setInt(37, obj.getDiaVencimentoProrata());
        sqlInserir.setDate(38, Uteis.getDataJDBC(obj.getDataRematriculaRealizada()));
        sqlInserir.setInt(39,id_recebe);
        sqlInserir.execute();

        ResultSet criarConsulta = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM contrato WHERE id_externo = "+id_recebe
                +" and empresa = "+obj.getEmpresa().getCodigo().intValue(), con);
        if(criarConsulta.next()){
            obj.setCodigo(criarConsulta.getInt("codigo"));
        }else{
            return null;
        }

        obj.setNovoObj(false);
        obj.getContratoDuracao().setNrMaximoParcelasCondPagamento(1);
        obj.getContratoDuracao().setTipoValor("PD");
        obj.getContratoDuracao().setTipoOperacao("PD");
        obj.getContratoDuracao().setContrato(obj.getCodigo());
        obj.getContratoDuracao().setContratoVO(obj);
//        obj.inicializarDadosContrato();
        contratoDuracao.incluir(obj.getContratoDuracao());
        if (obj.getContratoHorario().getHorario().getCodigo().intValue() > 0) {
            obj.getContratoHorario().setContrato(obj.getCodigo());
            contratoHorario.incluir(obj.getContratoHorario());
        }
        obj.getContratoCondicaoPagamento().getCondicaoPagamento().setCodigo(1);
        obj.getContratoCondicaoPagamento().setContrato(obj.getCodigo());
        obj.getContratoCondicaoPagamento().setTipoValor("");
        obj.getContratoCondicaoPagamento().setTipoOperacao("");
        obj.getContratoCondicaoPagamento().setPercentualDesconto(0.0);
        contratoCondicaoPagamento.incluir(obj.getContratoCondicaoPagamento());
        contratoComposicao.incluirContratoComposicaos(obj.getCodigo(), obj.getContratoComposicaoVOs());
        obj.getContratoTextoPadrao().setPlanoTextoPadrao(new PlanoTextoPadraoVO());
        obj.getContratoTextoPadrao().getPlanoTextoPadrao().setCodigo(1);
        obj.getContratoTextoPadrao().setContrato(obj.getCodigo());

        contratoTextoPadrao.incluir(obj.getContratoTextoPadrao());

        contratoModalidade.incluirContratoModalidades(obj.getCodigo(), obj.getContratoModalidadeVOs());

        contratoPlanoProdutoSugerido.incluirContratoPlanoProdutoSugerido(obj.getCodigo(), obj.getContratoPlanoProdutoSugeridoVOs());


        historicoContrato.incluirHistoricoContratos(obj.getCodigo(), obj.getHistoricoContratoVOs());
        periodoAcessoCliente.incluirPeriodoAcessoClienteContrato(obj, obj.getPeriodoAcessoClienteVOs());
        movProduto.incluirMovProdutoContratos(obj, obj.getMovProdutoVOs());


        if (obj.isContratoMatricula()) {
            contrato.gerarMatriculaAlunoTurma(obj);
        }

        return obj;
    }

    public List<String> produtosEmBanco(Connection con) throws SQLException{
        List<String> lista = new ArrayList<String>();
        ResultSet set = con.createStatement().executeQuery("SELECT descricao FROM produto");
        while(set.next()){
            lista.add(set.getString("descricao"));
        }
        return lista;
    }

    public Map<Integer, Map<String, Object>> mapaProdutos(Connection con) throws SQLException{
        Map<Integer,  Map<String, Object>> mapa = new HashMap<Integer,  Map<String, Object>>();
        ResultSet set = con.createStatement().executeQuery("SELECT id_externo, codigo, descricao FROM produto");
        while(set.next()){
            Map<String, Object> dados = new HashMap<String, Object>();
            dados.put("codigo", set.getInt("codigo"));
            dados.put("nome", set.getString("descricao"));
            mapa.put(set.getInt("id_externo"), dados);
        }
        return mapa;

    }

    public Map<String, Integer> mapaDadosUsuario(Connection con) throws SQLException{
        Map<String, Integer> mapa = new HashMap<String, Integer>();
        ResultSet set = con.createStatement().executeQuery("select colaborador, codigo from usuario WHERE username like 'PACTOBR'");
        while(set.next()){
            mapa.put("usuario", set.getInt("codigo"));
            mapa.put("colaborador", set.getInt("colaborador"));
        }
        return mapa;

    }

    public Map<Integer, Map<String, Object>> mapaClientes(Connection con) throws SQLException{
        Map<Integer, Map<String, Object>> mapa = new HashMap<Integer, Map<String, Object>>();
        ResultSet set = con.createStatement().executeQuery("select p.nome, idexterno, cli.codigo, p.codigo as pessoa from cliente cli "+
                " inner join pessoa p on p.codigo = cli.pessoa"+
                " where idexterno is not null");
        while(set.next()){
            Map<String, Object> pessoa = new HashMap<String, Object>();
            pessoa.put("nome", set.getString("nome"));
            pessoa.put("codigo", set.getInt("codigo"));
            pessoa.put("pessoa", set.getInt("pessoa"));
            mapa.put(set.getInt("idexterno"), pessoa);
        }
        return mapa;
    }

    public void gravarProduto(String tipoproduto, Double valor, String descricao, int id, boolean desativado, Connection con) throws SQLException{

        PreparedStatement stm = con.prepareStatement("INSERT INTO produto (tipoproduto, valorfinal, descricao, id_externo, desativado)" +
                " VALUES (?, ?, ?, ?, ?)");
        stm.setString(1, tipoproduto);
        stm.setDouble(2, valor);
        stm.setString(3, descricao);
        stm.setInt(4, id);
        stm.setBoolean(5, desativado);

        stm.execute();
    }

    public static ReciboPagamentoVO verificarPagamentoExiste(int id_recebe, Double valor, Connection con) throws Exception{
        ResultSet resultSet = con.prepareStatement("SELECT * FROM movpagamento WHERE id_recebe  = "+id_recebe).executeQuery();
        if(resultSet.next()){
            int codigoRecibo = resultSet.getInt("recibopagamento");
            con.prepareStatement("UPDATE recibopagamento set valortotal = (valortotal + "+valor+") " +
                    " WHERE codigo  = "+codigoRecibo).execute();
            ReciboPagamento reciboPagamento = new ReciboPagamento(con);
            return reciboPagamento.consultarPorChavePrimaria(codigoRecibo, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        }else{
            return null;
        }
    }

    public static MovPagamentoVO consultarMovPagamento(int id_recebe, Double valor, Connection con) throws Exception{
        ResultSet resultSet = con.prepareStatement("SELECT * FROM movpagamento WHERE id_recebe  = "+id_recebe).executeQuery();
        if(resultSet.next()){
            int codigo = resultSet.getInt("codigo");
            double valorTotal = resultSet.getDouble("valor")+valor;

            con.prepareStatement("UPDATE movpagamento set valortotal = "+valorTotal+",valor = "+valorTotal+" " +
                    " WHERE codigo  = "+codigo).execute();
            MovPagamento movPagamento = new MovPagamento(con);
            return movPagamento.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TELACONSULTA);
        }else{
            return null;
        }
    }

    public void executarUpdateMovProduto(Connection con) throws Exception{
        SuperFacadeJDBC.executarConsulta("update movproduto set valorfaturado  = totalfinal;", con);

    }

    public void executarUpdatesClientesVisitantes(Connection con) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE cliente SET situacao = 'AT' \n");
        sql.append("WHERE codigo IN ( \n");
        sql.append("select cliente.codigo from cliente \n");
        sql.append("inner join pessoa on pessoa.codigo = cliente.pessoa \n");
        sql.append("where situacao = 'VI' \n");
        sql.append("	AND pessoa IN (select pessoa from contrato )) \n");
        sql.append("AND pessoa IN (select pessoa from contrato where vigenciaateajustada > '"+Uteis.getDataJDBC(Calendario.hoje())+"'); \n");

        sql.append("UPDATE cliente SET situacao = 'IN' \n");
        sql.append("WHERE codigo IN ( \n");
        sql.append("	select cliente.codigo from cliente \n");
        sql.append("inner join pessoa on pessoa.codigo = cliente.pessoa \n");
        sql.append("where situacao = 'VI' \n");
        sql.append("AND pessoa IN (select pessoa from contrato )) \n");
        sql.append("AND pessoa IN (select pessoa from contrato where vigenciaateajustada <= '"+Uteis.getDataJDBC(Calendario.hoje())+"');");

        SuperFacadeJDBC.executarConsulta(sql.toString(), con);

    }

    public Map<Integer, Integer> obterIdVendaRecibo(Integer empresa, Connection con) throws Exception{
        Map<Integer, Integer> mapa = new HashMap<Integer, Integer>();
        String sql = "SELECT recibopagamento, id_venda FROM movpagamento where id_venda is not null and id_venda > 0 and empresa = "+empresa;
        ResultSet dados = SuperFacadeJDBC.criarConsulta(sql, con);
        while(dados.next()){
            mapa.put(dados.getInt("id_venda"), dados.getInt("recibopagamento"));
        }
        return mapa;
    }

    public Map<Integer, Double> obterValorRecibo(Integer empresa, Connection con) throws Exception{
        Map<Integer, Double> mapa = new HashMap<Integer, Double>();
        String sql = "SELECT codigo, valortotal FROM recibopagamento where empresa = "+empresa;
        ResultSet dados = SuperFacadeJDBC.criarConsulta(sql, con);
        while(dados.next()){
            mapa.put(dados.getInt("codigo"), dados.getDouble("valortotal"));
        }
        return mapa;
    }
}
