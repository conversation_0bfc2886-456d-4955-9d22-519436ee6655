package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON>",
        data = "21/05/2025",
        descricao = "Adicionar novas colunas para as configurações de convites",
        motivacao = "GC-1585")
public class AtualizacaoTicketGC1585 implements MigracaoVersaoInterface {
    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaosistema add tituloConvite varchar(100);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaosistema add descricaoConvite varchar(255);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE public.configuracaosistema add tempoReabilitacaoExAluno integer;", c);
        }
    }
}
