package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.sql.ResultSet;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "25/06/2025",
        descricao = "Editar permissao de operadora de cartao para usuários com permissão limitada edição",
        motivacao = "PAY-1047")
public class PAY1047IncluirPermissoesOperadoraCartaoUsuariosSemPermissao {
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            String sqlDelete = "DELETE FROM permissao WHERE tituloapresentacao = '4.15 - Operadora de Cartão';";
            SuperFacadeJDBC.executarUpdateExecutarProcessos(sqlDelete, c);

            ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM perfilacesso WHERE codigo NOT IN (" +
                    "SELECT codperfilacesso FROM permissao WHERE tituloapresentacao = '4.15 - Operadora de Cartão'" +
                    ");", c);
            while (rs.next()) {
                String sql = "INSERT INTO permissao (tipopermissao, tituloapresentacao, permissoes, nomeentidade, codperfilacesso) "
                        + " VALUES (1, '4.15 - Operadora de Cartão','(0)(1)(2)(9)(12)', "
                        + " 'OperadoraCartao', " + rs.getInt("codigo") + ")";
                SuperFacadeJDBC.executarUpdateExecutarProcessos(sql, c);
            }
        }

    }
}
