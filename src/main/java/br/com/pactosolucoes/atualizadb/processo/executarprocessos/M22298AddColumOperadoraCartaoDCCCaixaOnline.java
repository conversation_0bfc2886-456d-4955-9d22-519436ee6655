package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "20/09/2024",
        descricao = "Add coluna codigointegracaodcccaixaonline na tabela operadoracartao",
        motivacao = "M2-2298")
public class M22298AddColumOperadoraCartaoDCCCaixaOnline implements MigracaoVersaoInterface{

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "ALTER TABLE operadoracartao ADD COLUMN codigointegracaodcccaixaonline INTEGER;",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao \n" +
                    "ALTER COLUMN codigoexterno TYPE VARCHAR(100);", c);

        }
    }
}
