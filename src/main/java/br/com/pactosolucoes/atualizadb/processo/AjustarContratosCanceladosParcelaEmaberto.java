/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import br.com.pacto.priv.utils.Uteis;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.PagamentoMovParcelaVO;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.financeiro.PagamentoMovParcela;
import negocio.facade.jdbc.financeiro.ProdutosPagosServico;

/**
 *
 * <AUTHOR>
 */
public class AjustarContratosCanceladosParcelaEmaberto {
     public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("*********************************************", "postgres", "pactodb");
            corrigirParcelasContratos(con1);

        } catch (Exception ex) {
            Logger.getLogger(CorrigirDataCompensacaoMovPagamento.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void corrigirParcelasContratos(Connection con) throws Exception {
        ResultSet contratos= SuperFacadeJDBC.criarConsulta("select c.codigo, c.vigenciade,c.vigenciaateajustada, array_agg(distinct p.codigo||'|'||p.descricao||'|'||p.datavencimento)as parcelas \n" +
            "from contrato c inner join movparcela p on p.contrato = c.codigo \n" +
            "inner join movprodutoparcela mpp on mpp.movparcela = p.codigo\n" +
            "inner join movproduto mp on mp.codigo = mpp.movproduto\n" +
            "inner join produto pro on pro.codigo = mp.produto  and pro.tipoproduto in ('PM', 'MA', 'RE', 'RN', 'AH', 'MM', 'TD')\n" +
            "where c.situacao = 'CA' and p.situacao = 'EA'   group by 1,2,3 ", con);
        PagamentoMovParcela pagamentoMovParcela = new PagamentoMovParcela(con);
        Map<Integer, Double> valoresParcelas;
        List<Integer> codigosParcelas;
        List<MovPagamentoVO> listaPagamentos;
        List<Integer> recibos;
        ProdutosPagosServico pps = new ProdutosPagosServico();
        while (contratos.next()) {
            try{
                con.setAutoCommit(false);
                valoresParcelas = new HashMap<Integer, Double>();
                codigosParcelas = new ArrayList<Integer>();
                listaPagamentos = new ArrayList<MovPagamentoVO>();
                recibos = new ArrayList<Integer>();
                ResultSet parcelas = SuperFacadeJDBC.criarConsulta("select distinct p.codigo,p.valorparcela,p.descricao,p.datavencimento\n" +
                    "from movparcela p \n" +
                    "inner join movprodutoparcela mpp on mpp.movparcela = p.codigo\n" +
                    "inner join movproduto mp on mp.codigo = mpp.movproduto\n" +
                    "inner join produto pro on pro.codigo = mp.produto  and pro.tipoproduto in ('PM', 'MA', 'RE', 'RN', 'AH', 'MM', 'TD')\n" +
                    "where p.contrato = "+contratos.getInt("codigo")+" and p.situacao <> 'RG' order by datavencimento", con);
                while (parcelas.next()) {
                    valoresParcelas.put(parcelas.getInt("codigo"),Uteis.arredondarForcando2CasasDecimais(parcelas.getDouble("valorparcela")));
                    codigosParcelas.add(parcelas.getInt("codigo"));
                }
                ResultSet pagamentos = SuperFacadeJDBC.criarConsulta("select distinct pag.codigo,pag.valortotal,pag.valor,pag.recibopagamento from movpagamento pag where recibopagamento in (select recibopagamento from pagamentomovparcela where movparcela in (select p.codigo \n" +
                    "from movparcela p \n" +
                    "inner join movprodutoparcela mpp on mpp.movparcela = p.codigo\n" +
                    "inner join movproduto mp on mp.codigo = mpp.movproduto\n" +
                    "inner join produto pro on pro.codigo = mp.produto  and pro.tipoproduto in ('PM', 'MA', 'RE', 'RN', 'AH', 'MM', 'TD')\n" +
                    "where p.contrato = "+contratos.getInt("codigo")+" order by datavencimento)) and valor > 0 order by codigo", con);
                while (pagamentos.next()) {
                    MovPagamentoVO pagamento = new MovPagamentoVO();
                    pagamento.setCodigo(pagamentos.getInt("codigo"));
                    pagamento.getReciboPagamento().setCodigo(pagamentos.getInt("recibopagamento"));
                    pagamento.setValorTotal(Uteis.arredondarForcando2CasasDecimais(pagamentos.getDouble("valortotal")));
                    pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamentos.getDouble("valortotal")));
                    listaPagamentos.add(pagamento);
                    if(!recibos.contains(pagamentos.getInt("recibopagamento"))){
                        recibos.add(pagamentos.getInt("recibopagamento"));
                    }
                }
                pagamentos = SuperFacadeJDBC.criarConsulta("select distinct pag.codigo,pag.valortotal,pag.valor,pag.recibopagamento from movpagamento pag where recibopagamento in (select recibopagamento from pagamentomovparcela where movparcela in (select p.codigo \n" +
                    "from movparcela p \n" +
                    "inner join movprodutoparcela mpp on mpp.movparcela = p.codigo\n" +
                    "inner join movproduto mp on mp.codigo = mpp.movproduto\n" +
                    "inner join produto pro on pro.codigo = mp.produto  and pro.tipoproduto in ('PM', 'MA', 'RE', 'RN', 'AH', 'MM', 'TD')\n" +
                    "where p.contrato = "+contratos.getInt("codigo")+" order by datavencimento)) and valor = 0 order by codigo", con);
                while (pagamentos.next()) {
                    MovPagamentoVO pagamento = new MovPagamentoVO();
                    pagamento.setCodigo(pagamentos.getInt("codigo"));
                    pagamento.getReciboPagamento().setCodigo(pagamentos.getInt("recibopagamento"));
                    pagamento.setValorTotal(Uteis.arredondarForcando2CasasDecimais(pagamentos.getDouble("valortotal")));
                    pagamento.setValor(Uteis.arredondarForcando2CasasDecimais(pagamentos.getDouble("valortotal")));
                    listaPagamentos.add(pagamento);
                    if(!recibos.contains(pagamentos.getInt("recibopagamento"))){
                        recibos.add(pagamentos.getInt("recibopagamento"));
                    }
                }
                for(Integer recibo: recibos){
                    SuperFacadeJDBC.executarConsultaUpdate("delete from pagamentomovparcela where recibopagamento= "+recibo, con);   
                }
                for(MovPagamentoVO pagamentoVO: listaPagamentos){
                    for(Integer parcela: codigosParcelas){
                        if(pagamentoVO.getValor() == 0.0){
                            break;
                        }
                        if(valoresParcelas.get(parcela) > 0.0){
                            PagamentoMovParcelaVO novoPMP = new PagamentoMovParcelaVO();
                            novoPMP.setMovPagamento(pagamentoVO.getCodigo());
                            novoPMP.setReciboPagamento(pagamentoVO.getReciboPagamento());
                            MovParcelaVO parcelaPMP = new MovParcelaVO();
                            parcelaPMP.setCodigo(parcela);
                            novoPMP.setMovParcela(parcelaPMP);
                            Double valorpago = 0.0;
                            if(valoresParcelas.get(parcela) > pagamentoVO.getValor()){
                                valorpago = pagamentoVO.getValor();
                                valoresParcelas.put(parcela, Uteis.arredondarForcando2CasasDecimais(valoresParcelas.get(parcela) - pagamentoVO.getValor()));
                                pagamentoVO.setValor(0.0);
                            } else {
                                valorpago = valoresParcelas.get(parcela);
                                pagamentoVO.setValor(Uteis.arredondarForcando2CasasDecimais(pagamentoVO.getValor() - valoresParcelas.get(parcela)));
                                valoresParcelas.put(parcela,0.0);
                            }
                            novoPMP.setValorPago(valorpago);
                            pagamentoMovParcela.incluir(novoPMP);
                            
                        }
                    }
                    
                }
                for(Integer parcela: codigosParcelas){
                    String situacaoParcela = "PG";
                    if(valoresParcelas.get(parcela) > 0.0){
                        situacaoParcela = "CA";
                    } 
                    SuperFacadeJDBC.executarConsultaUpdate("update movparcela set situacao = '"+situacaoParcela+"' where codigo = "+parcela, con); 
                    SuperFacadeJDBC.executarConsultaUpdate("delete from clientemensagem where movparcela = "+parcela, con); 
                }
                
                for(Integer recibo: recibos){
                   pps.setarProdutosPagos(con, recibo);    
                }
            }catch(Exception e){
                con.rollback();
            } finally{
                con.setAutoCommit(true);
            }
        }
    }
}
