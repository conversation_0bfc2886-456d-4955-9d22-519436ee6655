package br.com.pactosolucoes.atualizadb.processo;

import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: glauco
 * Date: 30/07/13
 * Time: 09:25
 */
public class AtualizarNumerosDeCelular {

    public static void atualizarNumeros(Connection c, Integer[] ddds) throws Exception {
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE telefone SET numero = replace(numero, '-', '')", c);

        SuperFacadeJDBC.executarConsultaUpdate("UPDATE indicado SET telefone = replace(telefone, '-', '')", c);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE indicado SET telefoneindicado = replace(telefoneindicado, '-', '')", c);

        SuperFacadeJDBC.executarConsultaUpdate("UPDATE interessado SET telefone = replace(telefone, '-', '')", c);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE interessado SET telefonecomercial = replace(telefonecomercial, '-', '')", c);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE interessado SET celular = replace(celular, '-', '')", c);

        SuperFacadeJDBC.executarConsultaUpdate("UPDATE passivo SET telefoneresidencial = replace(telefoneresidencial, '-', '')", c);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE passivo SET telefonetrabalho = replace(telefonetrabalho, '-', '')", c);
        SuperFacadeJDBC.executarConsultaUpdate("UPDATE passivo SET telefonecelular = replace(telefonecelular, '-', '')", c);
        for (Integer ddd : ddds) {
            SuperFacadeJDBC.executarConsultaUpdate("UPDATE telefone SET numero = replace(numero, '(" + ddd + ")', '(" + ddd + ")9') WHERE length(numero) = 12 AND tipotelefone='CE'", c);
            SuperFacadeJDBC.executarConsultaUpdate("UPDATE telefone SET numero = replace(numero, '(" + ddd + ")97', '(" + ddd + ")7') WHERE length(numero) = 13 AND tipotelefone='CE'", c);

            SuperFacadeJDBC.executarConsultaUpdate("UPDATE interessado SET celular = replace(celular, '(" + ddd + ")', '(" + ddd + ")9') WHERE length(celular) = 12", c);
            SuperFacadeJDBC.executarConsultaUpdate("UPDATE interessado SET celular = replace(celular, '(" + ddd + ")97', '(" + ddd + ")7') WHERE length(celular) = 13", c);

            SuperFacadeJDBC.executarConsultaUpdate("UPDATE passivo SET telefonecelular = replace(telefonecelular, '(" + ddd + ")', '(" + ddd + ")9') WHERE length(telefonecelular) = 12", c);
            SuperFacadeJDBC.executarConsultaUpdate("UPDATE passivo SET telefonecelular = replace(telefonecelular, '(" + ddd + ")97', '(" + ddd + ")7') WHERE length(telefonecelular) = 13", c);
        }
    }
}
