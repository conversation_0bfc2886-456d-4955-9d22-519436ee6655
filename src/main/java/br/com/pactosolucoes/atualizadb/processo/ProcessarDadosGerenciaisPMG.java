package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.enumerador.IndicadoresDadosGerencialEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.DadosGerencialPmg;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Rafael on 03/04/2017.
 */
public class ProcessarDadosGerenciaisPMG {

    public static void main(String... args) {
        try {
            Uteis.debug = true;
            final String chave = args.length > 0 ? args[0] : "";
            Date dataBase = Calendario.hoje();
            if (!UteisValidacao.emptyString(args[1])) {
                dataBase = Uteis.getDate(args[1]);
            }
            Connection c = new DAO().obterConexaoEspecifica(chave);
            DadosGerencialPmg dadosGerencialPmg = new DadosGerencialPmg(c);
            Conexao.guardarConexaoForJ2SE(c);
            dadosGerencialPmg.gerarDadosPMG(0, chave, dataBase, null);
        } catch (Exception ex) {
            Logger.getLogger(CorrigirColaboradoresDuplicados.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void mainEngenharia(String... args) {
        try {
            Uteis.debug = true;

            List<String> chaves = new ArrayList<>();
            chaves.add("a855c5c74458a07ac7301a1a8df5b7ed");
            chaves.add("1c81b0d9bb3b592b05ee1558e154137c");
            chaves.add("58316609e0ec7adf0522ad113ecb4a1c");
            chaves.add("e67f2c42432bca6984b44aa063e0bfff");
            chaves.add("33fa79e54690bc45beb83343f7e3c730");
            chaves.add("7afbb0b4fcdb873a42afdab75236a6b");
            chaves.add("c8a81a93526bd96d745f4ddc0821d3e");
            chaves.add("f4bc544c4f6d26707d2e3a3efa9e8893");
            chaves.add("4804ab48b3ee697ce33425b6ac929000");
            chaves.add("e82cdafdf149e399e18ae3eb096bd873");
            chaves.add("628519d73cf98a91c81fa2492e0a9189");
            chaves.add("b25c87c8369dbd7255724e185eddbb52");
            chaves.add("879bead18e259ee69db884f7843833d4");
            chaves.add("9ed30f653142db8ade0c1dd047265e58");
            chaves.add("ef9f2365f60ac8e2356e254c8c91488f");
            chaves.add("704ee44b0ad0da5b7c1e195fd0c6dc32");
            chaves.add("4b73780d38bffd089704a82d3961720a");
            chaves.add("48cdbf3f086c9fba71013394d13a3284");

            Date dataProcessar = Uteis.getDate("12/04/2023", "dd/MM/yyyy");
            boolean ateDataAtual = false;

            for (String chave : chaves) {

                Uteis.logarDebug("########################################################################################");
                Uteis.logarDebug("########################################################################################");
                Uteis.logarDebug("######################## CHAVE " + chave + " ########################");
                Uteis.logarDebug("########################################################################################");
                Uteis.logarDebug("########################################################################################");

                try (Connection c = new DAO().obterConexaoEspecifica(chave)) {
                    DadosGerencialPmg dadosGerencialPmg = new DadosGerencialPmg(c);
                    Conexao.guardarConexaoForJ2SE(c);

//                    String delete = "delete from dadosgerencialpmg where identificador in ('CDI','CDF')";
//                    SuperFacadeJDBC.executarUpdate(delete, c);

                    List<Date> listaDias = new ArrayList<>();
                    if (ateDataAtual) {
                        listaDias = Uteis.getDiasEntreDatas(dataProcessar, Calendario.hoje());
                    } else {
                        listaDias.add(dataProcessar);
                    }

                    List<IndicadoresDadosGerencialEnum> lista = new ArrayList<>();
                    lista.add(IndicadoresDadosGerencialEnum.MOVIMENTACAO_CONTRATO);

                    for (Date dataBase : listaDias) {
//                    Calendario.dia = dataBase;
                        Uteis.logarDebug("######################## DIA " + Calendario.getDataAplicandoFormatacao(dataBase, "dd/MM/yyyy") + " ########################");
                        dadosGerencialPmg.gerarDadosPMG(0, chave, dataBase, lista);
//                        dadosGerencialPmg.gerarDadosDiaPMG(0, chave, dataBase);
                    }
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(CorrigirColaboradoresDuplicados.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

}
