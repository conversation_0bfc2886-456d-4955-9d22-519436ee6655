
package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Athos Feitosa",
        data = "02/08/2024",
        descricao = "Cria coluna cancelarparcelasemaberto na tabela operacaocoletiva",
        motivacao = "Ticket GC-787: [Customização] Excluir parcelas em aberto nas operaes coletivas - SESC - PE")
public class CriarColunaCancelarParcelasEmAberto implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operacaocoletiva ADD COLUMN cancelarparcelasemaberto BOOLEAN DEFAULT false;", c);
        }
    }

}
