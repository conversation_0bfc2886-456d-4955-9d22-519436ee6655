/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.pactosolucoes.atualizadb.processo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class VerificarComposicaoLote {

    public static void main(String ... args){
        try {
            Connection con1 = DriverManager.getConnection("*************************************", "postgres", "pactodb");
            verificarCartoesDeveriamEstarEmLote(con1);
            verificarChequesDeveriamEstarEmLote(con1);
            verificarGeralOutroCriterio(con1);
        } catch (Exception ex) {
            Logger.getLogger(VerificarComposicaoLote.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void verificarCartoesDeveriamEstarEmLote(Connection con) throws Exception{
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(" SELECT mp.nomepagador, cc.composicao, cc.codigo from cartaocredito cc "+
                            "inner join movpagamento mp on mp.codigo = cc.movpagamento"+
                            " where cc.composicao is not null and cc.composicao not like ''"+
                            " AND NOT EXISTS(SELECT * FROM chequecartaolote where cartao = cc.codigo)", con);
        int i = 1;
        while(consulta.next()){
            ResultSet criarConsulta = SuperFacadeJDBC.criarConsulta("SELECT * FROM cartaocredito cc "
                    + " INNER JOIN chequecartaolote ccl ON ccl.cartao = cc.codigo "
                    + " WHERE cc.codigo IN (" + consulta.getString("composicao") + ")", con);
            if(criarConsulta.next())

                System.out.println("Cod. Cartão: "+consulta.getInt("codigo")+"-"+consulta.getString("nomepagador"));
        }

    }

        public static void verificarChequesDeveriamEstarEmLote(Connection con) throws Exception{
        ResultSet consulta = SuperFacadeJDBC.criarConsulta(" SELECT mp.nomepagador, ch.composicao, ch.codigo from cheque ch "+
                            "inner join movpagamento mp on mp.codigo = ch.movpagamento"+
                            " where ch.composicao is not null and ch.composicao not like '' "+
                            " AND NOT EXISTS(SELECT * FROM chequecartaolote where cheque = ch.codigo)", con);
        int i = 1;
        while(consulta.next()){
            ResultSet criarConsulta = SuperFacadeJDBC.criarConsulta("SELECT * FROM cheque ch "
                    + " INNER JOIN chequecartaolote ccl ON ccl.cheque = ch.codigo "
                    + " WHERE ch.codigo IN (" + consulta.getString("composicao") + ")", con);
            if(criarConsulta.next())

                System.out.println("Cod. Cheque: "+consulta.getInt("codigo")+"-"+consulta.getString("nomepagador"));
        }

    }

        public static void verificarGeralOutroCriterio(Connection con)throws Exception{
            ResultSet consulta = SuperFacadeJDBC.criarConsulta(" SELECT mp.codigo, mp.nomepagador, cc.codigo as cartao, "
                    + " ch.codigo as cheque FROM movpagamento mp "
                    + " LEFT JOIN cartaocredito cc ON cc.movpagamento = mp.codigo "
                    + " LEFT JOIN cheque ch ON ch.movpagamento = mp.codigo"
                    + " WHERE movpagamentoorigemcredito is not null "
                    + " AND (ch.codigo is not null or cc.codigo is not null)"
                    + " AND EXISTS"
                    + " (SELECT * FROM movpagamento mov"
                    + " LEFT JOIN cartaocredito ccr ON ccr.movpagamento = mov.codigo "
                    + " LEFT JOIN cheque che ON che.movpagamento = mov.codigo"
                    + " WHERE mov.codigo = mp.movpagamentoorigemcredito"
                    + " AND (che.codigo is not null or ccr.codigo is not null)"
                    + " and exists(select * from chequecartaolote ccl where cartao = ccr.codigo or cheque = che.codigo))", con);
            while (consulta.next()) {
                Integer codCartao = consulta.getInt("cartao");
                if(!UteisValidacao.emptyNumber(codCartao)){
                     System.out.println("Cod. Cartão: " + codCartao + "-" + consulta.getString("nomepagador"));
                }
                Integer codCheque = consulta.getInt("cheque");
                if(!UteisValidacao.emptyNumber(codCheque)){
                     System.out.println("Cod. Cheque: " + codCheque + "-" + consulta.getString("nomepagador"));
                }


            }
        }
}
