package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Rodrigo Estulano",
        data = "14/12/2024",
        descricao = "Cria tabelas necessárias para funcionamento da migração da Stone para a Pagar_me v5",
        motivacao = "M2-1222")
public class M21222MigracaoStonePagarMe implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE operadoracartao ADD COLUMN codigointegracaostoneonlinev5 Integer;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN idStone VARCHAR(50);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE pessoa ADD COLUMN dataAlteracaoStone TIMESTAMP WITHOUT TIME ZONE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN usaSplitPagamentoStoneV5 BOOLEAN DEFAULT FALSE;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE conveniocobranca ADD COLUMN tipoCredencialStoneEnum INT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem ADD COLUMN idExterno VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE convenioCobrancaRateioItem ADD COLUMN recebedorPrincipal boolean;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao ADD COLUMN codigoNSU VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE transacao ADD COLUMN gateway_id BIGINT;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE extratodiarioitem ADD COLUMN idExterno2 VARCHAR;", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_transacao_gateway_id ON transacao(gateway_id);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_cartaotentativa_cartao_data_codigoRetorno_transacao \n" +
                    "ON cartaotentativa (cartao, data, codigoRetorno, transacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("CREATE INDEX idx_transacao_codigo_situacao \n" +
                    "ON transacao (codigo, situacao);", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE operadoracartao SET codigointegracaostoneonlinev5 = codigointegracaostoneonline where (codigointegracaostoneonline is not null and codigointegracaostoneonline <> 0)", c);
        }
    }
}
