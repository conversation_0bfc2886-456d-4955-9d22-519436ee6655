package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "<PERSON><PERSON><PERSON>",
        data = "18/12/2024",
        descricao = "Alterar descricao, dataregistro e datavencimento na tabela movparcela para Venda Avulsa da tela nova.",
        motivacao = "M2-2774")
public class M22929AlterarHoraEDescricaoVendaAvulsa implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("UPDATE movparcela \n" +
                    "SET descricao = UPPER(descricao), dataregistro = dataregistro::DATE, datavencimento = datavencimento::DATE \n" +
                    "WHERE descricao != UPPER(descricao) \n" +
                    "AND dataregistro::DATE != dataregistro \n" +
                    "AND datavencimento::DATE != datavencimento \n" +
                    "AND descricao ILIKE 'venda avulsa';", c);
        }
    }
}
