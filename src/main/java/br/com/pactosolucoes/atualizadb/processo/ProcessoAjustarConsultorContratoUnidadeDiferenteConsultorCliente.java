package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessoAjustarConsultorContratoUnidadeDiferenteConsultorCliente {

    public static void main(String... args) {
        try {
            String chave = args.length > 0 ? args[0] : "ciafit";
            Connection c = new DAO().obterConexaoEspecifica(chave);
            ajustarConsultores(c);
        } catch (Exception ex) {
            Logger.getLogger(ProcessoAjustarConsultorContratoUnidadeDiferenteConsultorCliente.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarConsultores(Connection con) {
        try {
            Uteis.logarDebug("INÍCIO | ProcessoAjustarConsultorContratoUnidadeDiferenteConsultorCliente");

            StringBuilder sqlComum = new StringBuilder("select\n")
                    .append(" distinct pesConsCon.codigo as codigo_pessoa_consultor,\n")
                    .append(" empConsCon.codigo as codigo_empresa_consultor_contrato,\n")
                    .append(" empConsCli.codigo as codigo_empresa_consultor_cliente,\n")
                    .append(" cli.codigo as codigo_cliente,\n")
                    .append(" cli.codigomatricula as matricula_cliente,\n")
                    .append(" con.codigo as codigo_contrato,\n")
                    .append(" empCli.codigo as codigo_empresa_cli\n")
                    .append("from historicovinculo h\n")
                    .append("inner join colaborador col on col.codigo = h.colaborador\n")
                    .append("inner join cliente cli on cli.codigo = h.cliente\n")
                    .append(" left join vinculo vin on vin.cliente = cli.codigo\n")
                    .append(" left join contrato con on con.pessoa = cli.pessoa\n")
                    .append(" left join colaborador consCon on con.consultor = consCon.codigo\n")
                    .append(" left join pessoa pesConsCon on pesConsCon.codigo = consCon.pessoa\n")
                    .append(" left join empresa empConsCon on empConsCon.codigo = consCon.empresa\n")
                    .append(" left join colaborador consCli on consCli.codigo = vin.colaborador\n")
                    .append(" left join pessoa pesConsCli on pesConsCli.codigo = consCli.pessoa\n")
                    .append(" left join empresa empConsCli on consCli.empresa = empConsCli.codigo\n")
                    .append(" left join empresa empCli on cli.empresa = empCli.codigo\n")
                    .append("where h.dataregistro::date between '2024-10-01'::date and '2024-10-30'::date \n")
                    .append("  and h.origem = 'TRANSFERÊNCIA DE EMPRESA' and vin.tipovinculo = 'CO'\n")
                    .append("  and pesConsCon.codigo = pesConsCli.codigo\n")
                    .append("  and con.codigo is not null\n");

            ajustarConsConDifEmpConsCliEIgualEmpCli(con, sqlComum);

            Uteis.logarDebug("FIM | ProcessoAjustarConsultorContratoUnidadeDiferenteConsultorCliente");
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO | ProcessoAjustarConsultorContratoUnidadeDiferenteConsultorCliente - " + ex.getMessage());
        }
    }

    /**
     * Corrige o consultor do contrato que a empresa é diferente da empresa do consultor do cliente mas a empresa
     * do consultor do cliente é a mesma do cliente
     * @param con
     * @param sqlComum
     * @throws SQLException
     */
    private static void ajustarConsConDifEmpConsCliEIgualEmpCli(Connection con, StringBuilder sqlComum) throws SQLException {
        StringBuilder sqlEmpConsConDifEmpConCliEEmpConsCliIgualEmpCli = new StringBuilder(sqlComum);
        sqlEmpConsConDifEmpConCliEEmpConsCliIgualEmpCli.append("and consCon.empresa != consCli.empresa\n")
                .append(" and consCli.empresa = empCli.codigo");

        List<Integer> contratosAfetados = new ArrayList<>();
        List<String> matriculasAfetadas = new ArrayList<>();

        try (Statement stm = con.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sqlEmpConsConDifEmpConCliEEmpConsCliIgualEmpCli.toString())) {
                while (rs.next()) {
                    Integer codigoPesCons = rs.getInt("codigo_pessoa_consultor");
                    Integer codigoEmpConsCon = rs.getInt("codigo_empresa_consultor_contrato");
                    Integer codigoEmpConsCli = rs.getInt("codigo_empresa_consultor_cliente");
                    Integer codigoCliente = rs.getInt("codigo_cliente");
                    String matriculaCliente = rs.getString("matricula_cliente");
                    Integer codigoEmpCli = rs.getInt("codigo_empresa_cli");
                    Integer codigoContrato = rs.getInt("codigo_contrato");

                    String sqlColaboradorMesmaEmpresaCliente = "SELECT codigo FROM colaborador WHERE pessoa = ? AND empresa = ?";

                    try (PreparedStatement stmColaboradorEmpresaCliente = con.prepareStatement(sqlColaboradorMesmaEmpresaCliente)) {
                        stmColaboradorEmpresaCliente.setInt(1, codigoPesCons);
                        stmColaboradorEmpresaCliente.setInt(2, codigoEmpCli);
                        try (ResultSet rsColaboradorEmpresaCliente = stmColaboradorEmpresaCliente.executeQuery()) {
                            if (rsColaboradorEmpresaCliente.next()) {
                                Integer codigoColaboradorMesmaEmpresaCliente = rsColaboradorEmpresaCliente.getInt("codigo");

                                if (!UteisValidacao.emptyNumber(codigoColaboradorMesmaEmpresaCliente)) {
                                    String sqlAlterarConsCon = "UPDATE contrato SET consultor = ? WHERE codigo = ?";

                                    try (PreparedStatement stmAlterarConsCon = con.prepareStatement(sqlAlterarConsCon)) {
                                        stmAlterarConsCon.setInt(1, codigoColaboradorMesmaEmpresaCliente);
                                        stmAlterarConsCon.setInt(2, codigoContrato);
                                        stmAlterarConsCon.execute();
                                        contratosAfetados.add(codigoContrato);
                                        matriculasAfetadas.add(matriculaCliente);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }

        Uteis.logarDebug("ProcessoAjustarConsultorContratoUnidadeDiferenteConsultorCliente.ajustarConsConDifEmpConsCliEIgualEmpCli");
        Uteis.logarDebug("Matrículas afetadas: " + matriculasAfetadas);
        Uteis.logarDebug("Contratos afetados: " + contratosAfetados);

    }

}
