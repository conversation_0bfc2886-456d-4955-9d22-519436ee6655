package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.ProcessoPovoarConfigsIntegracoes;
import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(
        autor = "Douglas Soares",
        data = "13/03/2025",
        descricao = "Criação da tabela 'configuracaointegracaogenericaleadsgymbot'",
        motivacao = "Ticket M1-4780 - Necessidade de criar a tabela para a configuração da nova integração leads (generica) do Gymbot"
)
public class AtualizacaoTicketM14780 implements MigracaoVersaoInterface {

    @Override
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {

             StringBuilder sql = new StringBuilder();
             sql.append("CREATE TABLE  configuracaointegracaogenericaleadsgymbot (\n")
                    .append("  codigo SERIAL4 NOT NULL,\n")
                    .append("  habilitada BOOL NULL,\n")
                    .append("  empresa INT4 NULL,\n")
                    .append("  horalimite VARCHAR(5) NULL,\n")
                    .append("  responsavelpadrao INT4 NULL,\n")
                    .append("  acaoobjecao INT4 NULL,\n")
                    .append("  CONSTRAINT configuracaointegracaogenericaleadsgymbot_pkey PRIMARY KEY (codigo),\n")
                    .append("  CONSTRAINT configuracaointegracaogenericaleadsgymbot_empresa_fkey ")
                    .append("    FOREIGN KEY (empresa) REFERENCES empresa(codigo),\n")
                    .append("  CONSTRAINT configuracaointegracaogenericaleadsgymbot_usuario_fkey ")
                    .append("    FOREIGN KEY (responsavelpadrao) REFERENCES usuario(codigo)\n")
                    .append(");\n");


             SuperFacadeJDBC.executarUpdateExecutarProcessos(sql.toString(), c);

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX configuracaointegracaogenericaleadsgymbot_empresa_idx " +
                            "ON configuracaointegracaogenericaleadsgymbot USING btree (empresa);",
                    c
            );

            SuperFacadeJDBC.executarUpdateExecutarProcessos(
                    "CREATE INDEX configuracaointegracaogenericaleadsgymbot_responsavelpadrao_idx " +
                            "ON configuracaointegracaogenericaleadsgymbot USING btree (responsavelpadrao);",
                    c
            );
            try {
                ProcessoPovoarConfigsIntegracoes.povoarConfiguracaoIntegracaoGenericaLeadsGymbot(c);
            } catch (Exception e) {
            }
        }
    }
}
