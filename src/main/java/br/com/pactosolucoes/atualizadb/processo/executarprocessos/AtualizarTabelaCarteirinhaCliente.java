package br.com.pactosolucoes.atualizadb.processo.executarprocessos;

import br.com.pactosolucoes.atualizadb.processo.annotations.ClasseProcesso;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;

import java.sql.Connection;

@ClasseProcesso(autor = "Lucas Araujo",
        data = "26/11/2024",
        descricao = "Add column chavearquivo, drop coluns base64 and key amazon from carteirinhacliente table",
        motivacao = "M1-2255")
public class AtualizarTabelaCarteirinhaCliente implements MigracaoVersaoInterface{
    public void executar(Connection con) throws Exception {
        try (Connection c = Conexao.getInstance().obterNovaConexaoBaseadaOutra(con)) {
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE carteirinhacliente ADD COLUMN chaveArquivo varchar", c);

            //OBS: Como é um recurso novo e nenhum cliente está utilizando é seguro remover essas colunas, sem necessidade de migração de dados
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE carteirinhacliente DROP COLUMN IF EXISTS keyamazon", c);
            SuperFacadeJDBC.executarUpdateExecutarProcessos("ALTER TABLE carteirinhacliente DROP COLUMN IF EXISTS base64", c);
        }
    }
}
