package br.com.pactosolucoes.atualizadb.processo;

import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

public class SimularImpressaoFicha {

    private String urlTreino = "http://localhost:8085/treino";

    private void imprimirTreino(String chave,
                            Integer idPrograma,
                            Integer idFicha,
                            Integer matricula,
                            String username,
                                String dia) throws Exception{

        String uri = urlTreino + "/prest/programa/"+chave+ "/submittreinoCommentAPP?" +
                "username=" + username +
                "&idPrograma=" + idPrograma +
                "&idFicha=" + idFicha +
                "&dia=" + dia +
                "&nota=5&tempo=0&comentario=&matricula="+matricula;
        System.out.println(post(uri));

    }

    public void imprimirThread(String chave,
                               Integer idPrograma,
                               Integer idFicha,
                               Integer matricula,
                               String username,
                               String dia){
//        new Thread() {
//            @Override
//            public void run() {
                try {
                    System.out.println("imprimindo ... " + matricula);
                    imprimirTreino(chave,
                            idPrograma,
                            idFicha,
                            matricula,
                            username,
                            dia);
                }catch (Exception e){
                    e.printStackTrace();
                }
//            }
//        }.start();
    }

    private String post(String uri) throws Exception{
        CloseableHttpClient client = ExecuteRequestHttpService.createConnector();
        HttpPost httpPost = new HttpPost(uri);
        CloseableHttpResponse response = client.execute(httpPost);
        ResponseHandler<String> handler = new BasicResponseHandler();
        String body = handler.handleResponse(response);
        client.close();
        return body;
    }

    public static void main(String[] args) throws Exception{
        SimularImpressaoFicha mesmoTempoProcesso = new SimularImpressaoFicha();
        String chave = "redfitnessmandaqui";
        String dia = "27/11/2021";
        Connection con = DriverManager.getConnection("*********************************************************", "postgres", "pactodb");
        PreparedStatement stm = con.prepareStatement("select c.matricula, p.codigo as programa, " +
                "u.username, (select ficha_codigo from programatreinoficha ptf where ptf.programa_codigo = p.codigo limit 1) as idficha\n" +
                "        from programatreino p\n" +
                "        inner join clientesintetico c on c.codigo = p.cliente_codigo\n" +
                "        inner join usuario u on u.cliente_codigo  = p.cliente_codigo\n" +
                "        where current_date between p.datainicio and p.dataterminoprevisto\n");
        ResultSet rs = stm.executeQuery();
        while(rs.next()){
            mesmoTempoProcesso.imprimirThread(chave,
                    rs.getInt("programa"),
                    rs.getInt("idficha"),
                    rs.getInt("matricula"),
                    rs.getString("username"),
                    dia);
        }
//        String uri = "http://localhost:8085/treino/prest/programa/"+chave+
//                "/submittreinoCommentAPP?username=42621&idPrograma=18315&idFicha=47276&dia=24/11/2021&nota=5&tempo=0&comentario=&matricula=42621";
//        System.out.println(mesmoTempoProcesso.post(uri));
    }
}
