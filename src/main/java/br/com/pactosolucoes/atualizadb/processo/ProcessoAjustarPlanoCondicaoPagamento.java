package br.com.pactosolucoes.atualizadb.processo;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.plano.CondicaoPagamento;
import negocio.facade.jdbc.plano.PlanoCondicaoPagamento;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;

public class ProcessoAjustarPlanoCondicaoPagamento {

    public static void main(String... args) {
        try {
            String chave = null;
            if (args.length == 0) {
                args = new String[]{"greenlifeguararapes"};
            }
            if (args.length > 0) {
                chave = args[0];
                Uteis.logar(null, "Obter conexão para chave: " + chave);
            }
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada");
            }
            Uteis.debug = true;

            Connection con = new DAO().obterConexaoEspecifica(chave);
            ProcessoAjustarPlanoCondicaoPagamento.ajustarPlanoCondicaoPagamento(con);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void padronizarCondicaoPagamentoRecorrencia(Connection con) throws Exception {
        String sqlAlt = "UPDATE condicaopagamento SET descricao = REPLACE(descricao, '  ', ' ') WHERE descricao ILIKE '%RECORR%'";

        try (Statement stm = con.createStatement()) {
            stm.executeUpdate(sqlAlt);
        }
    }

    public static void ajustarPlanoCondicaoPagamento(Connection con) throws Exception {
        int contadorPlanosAfetados = 0;
        try {

            padronizarCondicaoPagamentoRecorrencia(con);

            CondicaoPagamento condicaoPagamentoDAO = new CondicaoPagamento(con);
            PlanoCondicaoPagamento planoCondicaoPagamentoDAO = new PlanoCondicaoPagamento(con);

            String sqlGetPlanosSemCondicaoPagamento = "SELECT pln.codigo AS codPlano, pln.descricao AS nomePlano, pln.recorrencia, pln.site, pln.empresa, pdr.numeromeses AS numeroMeses, pdr.codigo AS codigoDuracao\n" +
                    "FROM planoduracao pdr\n" +
                    "INNER JOIN plano pln ON pln.codigo = pdr.plano\n" +
                    "LEFT JOIN planocondicaopagamento pcp ON pcp.planoduracao = pdr.codigo\n" +
                    "WHERE pcp.codigo IS NULL\n" +
                    "AND pln.recorrencia ";
            try (Statement stm = con.createStatement()) {
                try (ResultSet rs = stm.executeQuery(sqlGetPlanosSemCondicaoPagamento)) {
                    Uteis.logar("INÍCIO | ProcessoAjustarPlanoCondicaoPagamento");
                    while (rs.next()) {
                        Integer codPlano = rs.getInt("codPlano");
                        String nomePlano = rs.getString("nomePlano");
                        Integer duracaoPlano = rs.getInt("numeroMeses");
                        Integer planoDuracao = rs.getInt("codigoDuracao");
                        Uteis.logar("INSERINDO CONDIÇÃO PAGAMENTO PARA O PLANO: " + codPlano + " - " + nomePlano);

                        if (!UteisValidacao.emptyNumber(duracaoPlano)) {
                            CondicaoPagamentoVO condicao = new CondicaoPagamentoVO();

                            try {
                                condicao = criarOuConsultarSeExistePorNome(duracaoPlano, condicaoPagamentoDAO);
                            } catch (Exception exception) {
                                Uteis.logar("CONDIÇÃO DE PAGAMENTO NÃO ENCONTRADA PARA O PLANO: " + codPlano + " - " + nomePlano);
                                continue;
                            }

                            PlanoCondicaoPagamentoVO planoCondPagamento = new PlanoCondicaoPagamentoVO();

                            planoCondPagamento.setPercentualDesconto(0.0);
                            planoCondPagamento.setQtdParcela(duracaoPlano);
                            planoCondPagamento.setCondicaoPagamento(condicao);
                            planoCondPagamento.setPlanoDuracao(planoDuracao);
                            planoCondPagamento.setValorEspecifico(0.0);

                            planoCondicaoPagamentoDAO.incluir(planoCondPagamento);

                            contadorPlanosAfetados++;
                        }
                    }
                }
            }

        } catch (Exception ignore) {
            ignore.printStackTrace();
        } finally {
            Uteis.logar("QUANTIDADE PLANOS AFETADOS: " + contadorPlanosAfetados);
            Uteis.logar("FIM | ProcessoAjustarPlanoCondicaoPagamento");
        }
    }

    private static CondicaoPagamentoVO criarOuConsultarSeExistePorNome(int nrParcelas, CondicaoPagamento condicaoPagamentoDao) throws Exception {
        CondicaoPagamentoVO condicao = new CondicaoPagamentoVO();
        String plural = nrParcelas == 1 ? " VEZ " : " VEZES ";
        condicao.setDescricao("EM " + nrParcelas + plural + "- CARTÃO RECORRÊNCIA");
        condicao.setIntervaloEntreParcela(30);
        condicao.setEntrada(true);
        condicao.setNrParcelas(nrParcelas);
        List<CondicaoPagamentoVO> lista = condicaoPagamentoDao.consultarPorDescricao(
                condicao.getDescricao(), false, Uteis.NIVELMONTARDADOS_TODOS);
        if (lista.isEmpty()) {
            condicao.setDescricao("EM " + nrParcelas + plural + "- CARTAO RECORRENCIA");
            lista = condicaoPagamentoDao.consultarPorDescricao(
                    condicao.getDescricao(), false, Uteis.NIVELMONTARDADOS_TODOS);
            if (lista.isEmpty()){
                throw new Exception("Condição de pagamento não encontrada: " + condicao.getDescricao());
            }
            return lista.get(0);
        } else {
            return lista.get(0);
        }
    }

}
