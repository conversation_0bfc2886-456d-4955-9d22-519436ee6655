package br.com.pactosolucoes.atualizadb.processo;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

import static negocio.facade.jdbc.financeiro.Transacao.obterObjetoTransacaoPorTipo;

public class ProcessoCorrecaoAutorizacaoVindi {

    public static void corrigirAutorizacaoEmBranco(Connection c) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("t.codigo, \n");
        sql.append("t.situacao, \n");
        sql.append("t.tipo, \n");
        sql.append("t.tipoOrigem, \n");
        sql.append("t.paramsresposta, \n");
        sql.append("t.dataprocessamento as data, \n");
        sql.append("t.codigoautorizacao as transacao_codigoautorizacao, \n");
        sql.append("mp.codigo as movpagamento, \n");
        sql.append("mp.autorizacaocartao as movpagamento_autorizacao, \n");
        sql.append("mp.nsu as movpagamento_nsu \n");
        sql.append("from transacao t \n");
        sql.append("inner join movpagamento mp on mp.codigo = t.movpagamento \n");
        sql.append("where t.movpagamento is not null \n");
        sql.append("and coalesce(mp.autorizacaocartao,'') = '' \n");
        sql.append("and t.tipo = ").append(TipoTransacaoEnum.VINDI.getId()).append(" \n");
        sql.append("and t.dataprocessamento::date >= '01/01/2021' \n");
        sql.append("order by t.codigo \n");

        Integer total = SuperFacadeJDBC.contar("select count(*) as qtd from ( " + sql + " ) as sql", c);
        Integer atual = 0;
        try (Statement stm = c.createStatement()) {
            try (ResultSet rs = stm.executeQuery(sql.toString())) {
                while (rs.next()) {
                    try {
                        Uteis.logarDebug("ProcessoCorrecaoAutorizacaoVindi | " + ++atual + "/" + total);
                        TipoTransacaoEnum tipo = TipoTransacaoEnum.getTipoTransacaoEnum(rs.getInt("tipo"));
                        TransacaoVO obj = obterObjetoTransacaoPorTipo(tipo);
                        obj.setCodigo(rs.getInt("codigo"));
                        obj.setSituacao(SituacaoTransacaoEnum.getSituacaoTransacaoEnum(rs.getInt("situacao")));
                        obj.setParamsResposta(rs.getString("paramsresposta"));
                        obj.setTipo(tipo);
                        obj.setTipoOrigem(TipoTransacaoEnum.getTipoTransacaoEnum(rs.getInt("tipoOrigem")));

                        Integer movpagamento = rs.getInt("movpagamento");
                        String movpagamento_autorizacao = rs.getString("movpagamento_autorizacao");
                        String movpagamento_nsu = rs.getString("movpagamento_nsu");
                        String transacao_codigoautorizacao = rs.getString("transacao_codigoautorizacao");

                        if (!UteisValidacao.emptyNumber(movpagamento)) {
                            if (UteisValidacao.emptyString(movpagamento_autorizacao) && !UteisValidacao.emptyString(obj.getAutorizacao())) {
                                SuperFacadeJDBC.executarUpdate("update movpagamento set autorizacaocartao = '" + obj.getAutorizacao() + "' where codigo = " + movpagamento, c);
                            }

                            if (UteisValidacao.emptyString(movpagamento_nsu) && !UteisValidacao.emptyString(obj.getNSU())) {
                                SuperFacadeJDBC.executarUpdate("update movpagamento set nsu = '" + obj.getNSU() + "' where codigo = " + movpagamento, c);
                            }
                        }

                        if (!UteisValidacao.emptyNumber(obj.getCodigo()) && !UteisValidacao.emptyString(obj.getAutorizacao()) && UteisValidacao.emptyString(transacao_codigoautorizacao)) {
                            SuperFacadeJDBC.executarUpdate("update transacao set codigoautorizacao = '" + obj.getAutorizacao() + "' where codigo = " + obj.getCodigo(), c);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        }
    }

//    public static void main(String[] args) {
//        try {
//            Connection con = DriverManager.getConnection("****************************************************", "zillyonweb", "pactodb");
//            ProcessoCorrecaoAutorizacaoVindi.corrigirAutorizacaoEmBranco(con);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//        }
//    }
}
