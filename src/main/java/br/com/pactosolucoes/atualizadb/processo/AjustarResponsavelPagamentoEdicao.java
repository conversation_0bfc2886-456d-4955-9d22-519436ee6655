/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.atualizadb.processo;

import static br.com.pactosolucoes.atualizadb.processo.AjustarMovimentacaoMovPagamentoCC.ajustarMovContas;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.logging.Level;
import java.util.logging.Logger;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;

/**
 *
 * <AUTHOR>
 */
public class AjustarResponsavelPagamentoEdicao {
        public static void main(String... args) {
        try {
            Connection con1 = DriverManager.getConnection("**********************************************************************", "postgres", "pactodb");
            ajustarResponsavel(con1);

        } catch (Exception ex) {
            Logger.getLogger(VerificaCarenciaUtilizada.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public static void ajustarResponsavel(Connection con) throws Exception {
        ResultSet consulta = SuperFacadeJDBC.criarConsulta("select rc.responsavellancamento,m.codigo from recibopagamento rc inner join movpagamento m on m.recibopagamento = rc.codigo where m.responsavelpagamento <> rc.responsavellancamento", con);

        while (consulta.next()) {
           
            String sql = "update movpagamento set responsavelpagamento = ? where codigo = ?;";

            PreparedStatement sqlAlterar = con.prepareStatement(sql);

            sqlAlterar.setInt(1, consulta.getInt("responsavellancamento"));
            sqlAlterar.setInt(2, consulta.getInt("codigo"));
            sqlAlterar.execute();
        }
    }
}
