/**
 * 
 */
package br.com.pactosolucoes.ecf.cupomfiscal.comuns.to;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Classe para servir ao consultas de cupons fiscas.
 * 
 * Serve ao WS-ECF - web service de cupons fiscias
 * O cliente será uma aplicação desktop controladora de ECF
 * 
 * <AUTHOR>
 */
public class CupomFiscalTO implements Serializable {
    //código do cupom

    private int codigo;
    //usuario responsável pela impresão
    private int responsavel;
    //valor total do cupom
    private double valor;
    //hora em que a venda foi realizada no zillyon web
    private Date horaVenda;
    //hora em que o cupom foi emitido na aplicação cliete
    private Date horaEmissao;
    private Date dataPagamento;
    //status do cupom dentro do zillyon web
    private int statusCupom;
    //status da impressão no cliente
    private int statusImpressao;
    //codigo retornado pela impressora para o cliente
    private Integer co_cupom;
    //reciboVO associado ao cupom fiscal (RECIBO OU CHEQUE DEVE SER INFORMADOS)
    private int recibo;
    private int pagamento;
    private int cartao;
    //cheque associado ao cupom fiscal (RECIBO OU CHEQUE DEVE SER INFORMADOS)
    private int cheque;
    //itens do cupom
    private List<CupomFiscalItensTO> itens;
    //formas de pagamento
    private List<CupomFiscalFormasPagamentoTO> formasPagamento;
    //local de impressão
    private int local;
    //codigo do cliente
    private int codCliente;
    //nome do cliente
    private String nomeCliente;
    //CPF do Cliente
    private String cpfCliente;
    private String matricula;

    ////////////
    // Getters and Setters
    ///////////
    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getResponsavel() {
        return responsavel;
    }

    public void setResponsavel(int responsavel) {
        this.responsavel = responsavel;
    }

    public double getValor() {
        return valor;
    }

    public void setValor(double double1) {
        this.valor = double1;
    }

    public Date getHoraVenda() {
        return horaVenda;
    }

    public void setHoraVenda(Date horaVenda) {
        this.horaVenda = horaVenda;
    }

    public Date getHoraEmissao() {
        return horaEmissao;
    }

    public void setHoraEmissao(Date horaEmissao) {
        this.horaEmissao = horaEmissao;
    }

    public int getStatusCupom() {
        return statusCupom;
    }

    public void setStatusCupom(int statusCupom) {
        this.statusCupom = statusCupom;
    }

    public int getStatusImpressao() {
        return statusImpressao;
    }

    public void setStatusImpressao(int statusImpressao) {
        this.statusImpressao = statusImpressao;
    }

    public Integer getCo_cupom() {
        return co_cupom;
    }

    public void setCo_cupom(Integer coCupom) {
        co_cupom = coCupom;
    }

    public int getRecibo() {
        return recibo;
    }

    public void setRecibo(int recibo) {
        this.recibo = recibo;
    }

    public int getCheque() {
        return cheque;
    }

    public void setCheque(int cheque) {
        this.cheque = cheque;
    }

    public List<CupomFiscalItensTO> getItens() {
        return itens;
    }

    public void setItens(List<CupomFiscalItensTO> itens) {
        this.itens = itens;
    }

    public List<CupomFiscalFormasPagamentoTO> getFormasPagamento() {
        return formasPagamento;
    }

    public void setFormasPagamento(
            List<CupomFiscalFormasPagamentoTO> formasPagamento) {
        this.formasPagamento = formasPagamento;
    }

    public int getLocal() {
        return local;
    }

    public void setLocal(int local) {
        this.local = local;
    }

    public int getCodCliente() {
        return codCliente;
    }

    public void setCodCliente(int codCliente) {
        this.codCliente = codCliente;
    }

    public String getNomeCliente() {
        return nomeCliente;
    }

    public void setNomeCliente(String nomeCliente) {
        this.nomeCliente = nomeCliente;
    }

    public String getCpfCliente() {
        return cpfCliente;
    }

    public void setCpfCliente(String cpfCliente) {
        this.cpfCliente = cpfCliente;
    }

    public Date getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(Date dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public void setPagamento(int pagamento) {
        this.pagamento = pagamento;
    }

    public int getPagamento() {
        return pagamento;
    }

    public void setCartao(int cartao) {
        this.cartao = cartao;
    }

    public int getCartao() {
        return cartao;
    }

    public String getMatricula() {
        return matricula;
    }

    public void setMatricula(String matricula) {
        this.matricula = matricula;
    }
}
