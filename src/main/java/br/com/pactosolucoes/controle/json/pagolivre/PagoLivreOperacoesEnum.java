package br.com.pactosolucoes.controle.json.pagolivre;

public enum PagoLivreOperacoesEnum {
    ENVIAREMAILUSER
    ;

    
    public static PagoLivreOperacoesEnum obterOperacao(String o){
        if(o == null){
            return null;
        }
        for(PagoLivreOperacoesEnum op : values()){
            if(op.name().toLowerCase().equals(o.toLowerCase())){
                return op;
            }
        }
        return null;
    }
}
