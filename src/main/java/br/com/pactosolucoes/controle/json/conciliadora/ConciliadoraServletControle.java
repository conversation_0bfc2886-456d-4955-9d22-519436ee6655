package br.com.pactosolucoes.controle.json.conciliadora;

import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import org.json.JSONObject;
import servicos.integracao.impl.conciliadora.ConciliadoraServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.Date;

public class ConciliadoraServletControle extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        try {
            try {
                String key = obterParametroString(request, "key");
                String operacao = obterParametroString(request, "operacao");
                ConciliadoraOperacoesEnum operacaoEnum = ConciliadoraOperacoesEnum.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }

                Connection con = new DAO().obterConexaoEspecifica(key);

                switch(operacaoEnum){
                    case processar: {
                        ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(con);
                        Integer codigoEmpresa = obterParametroInt(request, "codigoEmpresa");
                        Integer codigoRecibo = obterParametroInt(request, "codigoRecibo");
                        Date dataInicial = obterParametroDate(request, "dataInicial", "yyyy-MM-dd");
                        Date dataFinal = obterParametroDate(request, "dataFinal", "yyyy-MM-dd");
                        Boolean reprocessar = obterParametroBoolean(request, "reprocessar");
                        Boolean manual = obterParametroBoolean(request, "manual");
                        conciliadoraService.processarConciliadora(codigoEmpresa, codigoRecibo, dataInicial, dataFinal, reprocessar, manual);
                        jsonRetorno.put(RETURN, "ok");
                        break;
                    }
                    case estornar: {
                        Integer codigoRecibo = obterParametroInt(request, "codigoRecibo");
                        ConciliadoraServiceImpl conciliadoraService = new ConciliadoraServiceImpl(con);
                        conciliadoraService.estornarReciboConciliadora(codigoRecibo);
                        jsonRetorno.put(RETURN, "ok");
                        break;
                    }
                }
            } catch (Exception e) {
                jsonRetorno.put(STATUS_ERRO, e.getMessage());
            }
            out.println(jsonRetorno);
        } catch (Exception e) {
            out.println(e.getMessage());
        }


    }
}
