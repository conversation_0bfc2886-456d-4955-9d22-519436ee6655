package br.com.pactosolucoes.controle.json.pactobank;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.amazonaws.util.IOUtils;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.pactobank.impl.StoneOpenBankServiceImpl;
import servicos.pactobank.intf.StoneOpenBankServiceInterface;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.util.Enumeration;

public class StoneOpenBankServletControle extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        String retorno = "Nada aconteceu.";
        try {
            try {
                String headers = "";
                   try {
                        if (request.getHeaderNames() != null) {
                            Enumeration<String> headerNames = request.getHeaderNames();
                            if (headerNames != null) {
                                JSONObject jsonHeader = new JSONObject();
                                while (headerNames.hasMoreElements()) {
                                    String headerName = headerNames.nextElement();
                                    jsonHeader.put(headerName, request.getHeader(headerName));
                                }
                                headers = jsonHeader.toString();
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                if (UteisValidacao.emptyString(headers)) {
                    throw new Exception("Não foi possível validar o header.");
                }
                JSONObject jsonHeader = new JSONObject(headers);
                String body = IOUtils.toString(request.getInputStream());
                Connection con = new DAO().obterConexaoEspecifica(jsonHeader.getString("chavezw"));
                String operacao = obterParametroString(request, "operacao");
                OperacoesStoneOpenBank operacaoEnum = OperacoesStoneOpenBank.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }
                StoneOpenBankServiceInterface stoneOpenBankServiceInterface = new StoneOpenBankServiceImpl(con);
                switch(operacaoEnum){
                    case gravarContaStoneFinanceiro:
                        retorno = stoneOpenBankServiceInterface.gravarContaStoneFinanceiro(body, jsonHeader.getString("chavezw"), jsonHeader.getInt("empresazw"));
                        break;
                    case atualizarTransPagRespostaStone:
                        retorno = stoneOpenBankServiceInterface.atualizarTransPagRespostaStone(body, jsonHeader.getString("chavezw"), jsonHeader.getInt("empresazw"),
                                jsonHeader.getString("eventidwebhook"), jsonHeader.getString("eventtypewebhook"));
                        break;
                }
                con.close();
                con = null;
                jsonRetorno.put(STATUS_SUCESSO, retorno);
            } catch (Exception e) {
                jsonRetorno.put(STATUS_ERRO, "erro" + e.getMessage());
            }
            out.println(jsonRetorno);
        } catch (Exception e) {
            out.println(e.getMessage());
        }
    }

}
