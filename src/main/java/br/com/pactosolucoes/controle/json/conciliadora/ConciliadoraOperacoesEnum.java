package br.com.pactosolucoes.controle.json.conciliadora;

public enum ConciliadoraOperacoesEnum {
    processar,
    estornar;

    
    public static ConciliadoraOperacoesEnum obterOperacao(String o){
        if(o == null){
            return null;
        }
        for(ConciliadoraOperacoesEnum op : values()){
            if(op.name().toLowerCase().equals(o.toLowerCase())){
                return op;
            }
        }
        return null;
    }
}
