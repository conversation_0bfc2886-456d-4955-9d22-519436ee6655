package br.com.pactosolucoes.controle.json.empresa;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import org.json.JSONObject;
import servicos.util.ExecuteRequestHttpService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by alcides on 24/11/2017.
 */
public class ToggleSimplesServlet extends SuperServletControle {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        String key = obterParametroString(request, "key");
        String operacao = obterParametroString(request, "op");
        String empresa = obterParametroString(request, "cod");
        try {
            boolean bloqueio = DaoAuxiliar.retornarAcessoControle(key).getEmpresaDao().toggleBloqueioTemporario(
                    Integer.valueOf(empresa), operacao.equals("consulta"));
            if(!operacao.equals("consulta") && bloqueio){
                Map<String, String> params = new HashMap<String, String>();
                params.put("op", "invalidateIdleSessionsKey");
                params.put("propagable", "s");
                params.put("chave", key);
                params.put("empresa", empresa);
                ExecuteRequestHttpService.executeHttpRequest(request.getRequestURL().toString().replace("prest/toggle", "UpdateServlet"), params);
            }
            jsonRetorno.put("simples", bloqueio);
            out.println(jsonRetorno);
        } catch (Exception e) {
            out.println(e.getMessage());
        }
    }


}
