package br.com.pactosolucoes.ce.provaconceito.simonides;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import br.com.pactosolucoes.ce.controle.CEControle;

/**
 * Classe responsável por implementar a interação entre os componentes JSF MANAGED BEANS
 * 
 * <AUTHOR>
 * 
 */
public class ProvaConceito_Simonides_Controle extends CEControle {

	private static final long serialVersionUID = 5764387750922215145L;
	private ProvaConceito_Simonides_TO provaConceito_Simonides = new ProvaConceito_Simonides_TO();
	private List<ProvaConceito_Simonides_TO> listaProvaConceito_Simonides;

	// ---------------------- INICIO - GETTERS AND SETTERS----------------------------------//
	/**
	 * @param provaconceito_Simonides
	 *            the provaconceito_Simonides to set
	 */
	public void setProvaConceito_Simonides(final ProvaConceito_Simonides_TO provaconceito_Simonides) {
		this.provaConceito_Simonides = provaconceito_Simonides;
	}

	/**
	 * @return O campo provaconceito_Simonides
	 */
	public ProvaConceito_Simonides_TO getProvaConceito_Simonides() {
		return this.provaConceito_Simonides;
	}

	/**
	 * @return O campo listaProvaConceito_Simonides
	 */
	public List<ProvaConceito_Simonides_TO> getListaProvaConceito_Simonides() {
		return this.listaProvaConceito_Simonides;
	}

	/**
	 * @param listaProvaConceito_Simonides
	 *            the listaProvaConceito_Simonides to set
	 */
	public void setListaProvaConceito_Simonides(final List<ProvaConceito_Simonides_TO> listaProvaConceito_Simonides) {
		this.listaProvaConceito_Simonides = listaProvaConceito_Simonides;
	}

	// ---------------------- FIM - GETTERS AND SETTERS ----------------------------------//
	// ---------------------- INICIO - DEMAIS MÉTODOS----------------------------------//

	/**
	 * Rotina responsável por verificar se a lista de prova de conceito simonides é nula
	 * 
	 * @return
	 */
	public boolean getApresentarResultadoConsulta_Simonides() {
		return !(this.getListaProvaConceito_Simonides() == null);
	}

	/**
	 * metodo responsavel por selecionar o objeto correspondente a linha do datatable no provaConceito_Simonides.jsp
	 */
	public String seleciona() {
		// pega o map enviado pelo botão editar da tabela de dados
		Map<String, Object> contexto = FacesContext.getCurrentInstance().getExternalContext().getRequestMap();
		// faz um cast, colocando o objeto selecionado na tabela dentro do
		// atributo provaconceito
		this.provaConceito_Simonides = (ProvaConceito_Simonides_TO) contexto.get("provaConceito_Simonides");
		return "provaConceito_Simonides_Novo";
	}

	/**
	 * limpa o TO local e direciona para a pagina de novo
	 * 
	 * @return string de direcionamento
	 */
	public String novo() {
		this.provaConceito_Simonides = new ProvaConceito_Simonides_TO();
		this.provaConceito_Simonides.setCodigo(0);
		return "provaConceito_Simonides_Novo";
	}

	/**
	 * deleta o registro selecionado na tabela
	 * 
	 * @return
	 */
	public String deleteProva_Simonides() {
		// receber o objeto contido na linha selecionada
		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
		// setar o objeto na instancia do TO local
		this.setProvaConceito_Simonides((ProvaConceito_Simonides_TO) request.getAttribute("provaConceito_Simonides"));
		try {
			this.excluir();
			// retorna para a tela de consulta
			// seta as mensagens de sucesso
			this.setSucesso(true);
			this.setErro(false);
			this.setMensagemID("msg_dados_excluidos");
			return "provaConceito_Simonides";
			// se houverem erros na operação
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemDetalhada("msg_erro", e.getMessage());
			return "provaConceito_Simonides";
		}

	}

	/**
	 * efetua operação de exclusão
	 * 
	 * @throws Exception
	 */
	public void excluir() throws Exception {
		// Executa o método do facade reponsável pela exclusão, passando como
		// parametro o objeto desse bean
		CEControle.getCEFacade().excluirProvaConceito_Simonides(this.getProvaConceito_Simonides());
		// após a exclusão, limpa o objeto
		this.setProvaConceito_Simonides(new ProvaConceito_Simonides_TO());
	}

	/**
	 * Método responsável por persistir os dados de provaConceito_Simonides_Form.jsp
	 * 
	 * @return
	 */
	public String salvar() {

		try {
			// manda o objeto para o método no Facade que persiste os dados
			CEControle.getCEFacade().salvarProvaConceito_Simonides(this.getProvaConceito_Simonides());
			this.setSucesso(true);
			this.setErro(false);
			this.setMensagemID("msg_dados_gravados");
			return "provaConceito_Simonides";
		} catch (Exception e) {
			// se houverem erros na gravação
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemDetalhada("msg_erro", e.getMessage());
			return "provaConceito_Simonides_Novo";
		}
	}

	/**
	 * consulta dados na tabela provaconceito_Simonides
	 * 
	 * @throws Exception
	 */
	public void listar() {
		try {
			this.setListaProvaConceito_Simonides(CEControle.getCEFacade().consultarProvaConceito_Simonides(
					this.getProvaConceito_Simonides()));
			// se a lista conter pelo menos um resultado, define os links de
			// navegação, apresenta e seta as mensagens de sucesso
			if (this.getListaProvaConceito_Simonides().size() > 0) {
				this.setMensagemID("operacoes.consulta.sucesso");
				this.setSucesso(true);
				this.setErro(false);
				// senão, seta mensagens acusando nenhum resultado obtido
			} else {
				this.setMensagemID("operacoes.consulta.nenhumResultado");
				this.setSucesso(true);
				this.setErro(false);
			}

			// se houverem erros na operação
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setListaProvaConceito_Simonides(new ArrayList<ProvaConceito_Simonides_TO>());
		}
	}

}
