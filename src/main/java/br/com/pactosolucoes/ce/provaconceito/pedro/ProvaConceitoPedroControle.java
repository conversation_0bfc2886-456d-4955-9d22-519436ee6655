/**
 * 
 */
package br.com.pactosolucoes.ce.provaconceito.pedro;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletRequest;

import br.com.pactosolucoes.ce.controle.CEControle;

/**
 * <AUTHOR>
 *
 */
public class ProvaConceitoPedroControle extends CEControle {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5669121883620073378L;


	private ProvaConceitoPedroVO provaConceitoPedroVO = new ProvaConceitoPedroVO();
	private List<ProvaConceitoPedroVO> listaProvaConceitoPedroVO;
	
	public ProvaConceitoPedroControle(){
		this.listar();
	}
	
	/**
	 * @return O campo provaConceitoPedroVO.
	 */
	public ProvaConceitoPedroVO getProvaConceitoPedroVO() {
		return provaConceitoPedroVO;
	}
	/**
	 * @param provaConceitoPedroVO O novo valor de provaConceitoPedroVO.
	 */
	public void setProvaConceitoPedroVO(ProvaConceitoPedroVO provaConceitoPedroVO) {
		this.provaConceitoPedroVO = provaConceitoPedroVO;
	}
	/**
	 * @return O campo listaProvaConceitoPedroVO.
	 */
	public List<ProvaConceitoPedroVO> getListaProvaConceitoPedroVO() {
		return listaProvaConceitoPedroVO;
	}
	/**
	 * @param listaProvaConceitoPedroVO O novo valor de listaProvaConceitoPedroVO.
	 */
	public void setListaProvaConceitoPedroVO(List<ProvaConceitoPedroVO> listaProvaConceitoPedroVO) {
		this.listaProvaConceitoPedroVO = listaProvaConceitoPedroVO;
	}
	
	//=======================================================================================================
	
	public void listar() {
		try {
			ProvaConceitoPedroVO provaObj = this.getProvaConceitoPedroVO();
			List<ProvaConceitoPedroVO> listaProvas = CEControle.getCEFacade().consultarProvaPedroConceito(provaObj);
			this.setListaProvaConceitoPedroVO(listaProvas);
			
			if (this.getListaProvaConceitoPedroVO().size() > 0) {
				this.setMensagemID("operacoes.consulta.sucesso");
				this.setSucesso(true);
				this.setErro(false);
			} else {
				this.setMensagemID("operacoes.consulta.nenhumResultado");
				this.setSucesso(true);
				this.setErro(false);
			}
			
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setListaProvaConceitoPedroVO(new ArrayList<ProvaConceitoPedroVO>());

		}
	}
	
	public String novo() {
		this.provaConceitoPedroVO = new ProvaConceitoPedroVO();
		this.provaConceitoPedroVO.setCodigo(0);
		return "provaConceitoPedroNovo";
	}
	
	public String salvar() {
		try {
			CEControle.getCEFacade().salvarProvaConceitoPedro(this.getProvaConceitoPedroVO());
			this.setSucesso(true);
			this.setErro(false);
			this.setMensagemID("msg_dados_gravados");
			return "provaConceitoPedro";
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemDetalhada("msg_erro", e.getMessage());
			return "provaConceitoPedroNovo";
		}
	}
	
	public String seleciona() {
		Map<String, Object> contexto = FacesContext.getCurrentInstance().getExternalContext().getRequestMap();
		
		this.provaConceitoPedroVO = (ProvaConceitoPedroVO) contexto.get("provaConceitoPedro");
		return "provaConceitoPedroNovo";
	}
	
	public String deleteProva() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
		
		try {
			this.setProvaConceitoPedroVO((ProvaConceitoPedroVO) request.getAttribute("provaConceitoPedro"));
			
			CEControle.getCEFacade().excluirProvaConceitoPedro(this.getProvaConceitoPedroVO());
			this.setProvaConceitoPedroVO(new ProvaConceitoPedroVO());
			
			this.setSucesso(true);
			this.setErro(false);
			this.setMensagemID("msg_dados_excluidos");
			return "provaConceitoPedro";
		} catch (Exception e) {
			this.setSucesso(false);
			this.setErro(true);
			this.setMensagemDetalhada("msg_erro", e.getMessage());
			return "provaConceitoPedro";
		}

	}
	
	public String voltar() {
		return "provaConceitoPedro";
	}
}
