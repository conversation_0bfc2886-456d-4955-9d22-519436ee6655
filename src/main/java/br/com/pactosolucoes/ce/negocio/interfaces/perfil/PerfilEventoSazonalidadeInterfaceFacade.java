/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.perfil;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.PerfilEventoSazonalidadeTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 */
public interface PerfilEventoSazonalidadeInterfaceFacade extends SuperInterface {

	/**
	 * Consulta as sazonalidades disponíveis para um perfil de evento ambiente.
	 * 
	 * @param codigoPerfilEventoAmbiente
	 *            Código identificador do perfil de evento ambiente cujos as sazonalidades estão sendo consultadas
	 * @return Lista de de sazonalidades disponíveis para o perfil de evento ambiente
	 * @throws Exception
	 */
	List<PerfilEventoSazonalidadeTO> consultarPorPerfilEventoAmbiente(Integer codigoPerfilEventoAmbiente) throws Exception;

	/**
	 * Inclui uma sazonalidade disponível para um perfil de evento ambiente.
	 * 
	 * @param sazonalidade
	 *            Sazonalidade a ser incluído
	 * @param codigoPerfilEventoAmbiente
	 *            Código identificador do perfil de evento ambiente.
	 * @throws Exception
	 */
	void incluir(PerfilEventoSazonalidadeTO sazonalidade, Integer codigoPerfilEventoAmbiente) throws Exception;

	/**
	 * Altera uma sazonalidadee disponível para um perfil de evento ambiente.
	 * 
	 * @param sazonalidade
	 *            Sazonalidade a ser alterada
	 * @param codigoPerfilEventoAmbiente
	 *            Código identificador do perfil de evento ambiente
	 * @throws Exception
	 */
	void alterar(PerfilEventoSazonalidadeTO sazonalidade, Integer codigoPerfilEventoAmbiente) throws Exception;

	/**
	 * Exclui um grupo de sazonalidades disponíveis para perfis de evento ambiente.
	 * 
	 * @param codigos
	 *            Códigos identificadores de sazonalidades disponíveis para perfis de evento ambiente.
	 * @throws Exception
	 */
	void excluir(List<Integer> codigos) throws Exception;

	/**
	 * Exclui os ambientes disponíveis para um determinado perfil de evento ambiente.
	 * 
	 * @param codigoAmbiente
	 *            Código identificador do perfil de evento ambiente
	 * @throws Exception
	 */
	void excluirPorAmbiente(Integer codigoAmbiente) throws Exception;

	/**
	 * Exclui os perfis de evento ambiente disponíveis para um determinado perfil de evento.
	 * 
	 * @param codigoPerfilEvento
	 *            Código identificador do perfil de evento.
	 * @throws Exception
	 */
	void excluirPorPerfilEvento(Integer codigoPerfilEvento) throws Exception;

}
