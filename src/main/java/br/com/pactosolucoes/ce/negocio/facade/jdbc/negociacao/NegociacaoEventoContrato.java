package br.com.pactosolucoes.ce.negocio.facade.jdbc.negociacao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import negocio.comuns.basico.EmailVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.CaixaAbertoTO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.basico.Email;
import negocio.facade.jdbc.financeiro.MovPagamento;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.ReciboPagamento;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoTO;
import br.com.pactosolucoes.ce.comuns.to.ReciboEventoTO;
import br.com.pactosolucoes.ce.negocio.facade.jdbc.arquitetura.CEDao;
import br.com.pactosolucoes.ce.negocio.interfaces.negociacao.NegociacaoEventoContratoInterfaceFacade;
import br.com.pactosolucoes.comuns.util.Declaracao;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;

/**
 * Classe de persistência que encapsula todas as operações de manipulação dos dados dda classe <code>NegociacaoEventoContratoTO</code>.
 * Responsável por implementar operações como incluir, alterar, excluir e consultar. Encapsula toda a interação com o banco de dados.
 * 
 * <AUTHOR>
 * 
 */
public class NegociacaoEventoContrato extends CEDao implements NegociacaoEventoContratoInterfaceFacade {
	/**
	 * Construtor padrão da classe
	 * 
	 * @throws Exception
	 */
	public NegociacaoEventoContrato() throws Exception {
		super();
		setIdEntidade("NegociacaoEvento");
	}
	private ZillyonWebFacade zwFacade;

	@Override
	public void alterar(final NegociacaoEventoContratoTO negEvContrato) throws Exception {
		super.alterar(this.getIdEntidade());

	}

	@Override
	public void excluir(final Integer codigoEvento) throws Exception {
		super.excluir(this.getIdEntidade());
		//obter codigo do contrato
		Integer contrato = this.obterCodigoContrato(codigoEvento);
		//excluir parcelas relacionadas
		NegociacaoEventoContratoParcelas necp = new NegociacaoEventoContratoParcelas();
		necp.excluir(contrato);
		//excluir recibos
		excluirRecibos(contrato);
		//excluir contrato
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM negociacaoeventocontrato WHERE eventointeresse = ? ");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoEvento);
		dc.execute();
	}

	/**
	 * @param codigoEvento
	 * @return código do contrato do evento
	 * @throws Exception
	 */
	public Integer obterCodigoContrato(final Integer codigoEvento)throws Exception {
		
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT CODIGO FROM negociacaoeventocontrato WHERE eventointeresse = ? ");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoEvento);
		ResultSet rs = dc.executeQuery();
		Integer retorno = new Integer(0);
		if (rs.next())
			retorno = rs.getInt("codigo");

		return retorno;
		
	}
	/**
	 * @param codigoEvento
	 * @return código do contrato do evento
	 * @throws Exception
	 */
	public Integer obterCodigoEvento(final Integer codigoContratoEvento)throws Exception {
		
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT eventointeresse FROM negociacaoeventocontrato WHERE codigo = ? ");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		dc.setInt(1, codigoContratoEvento);
		ResultSet rs = dc.executeQuery();
		Integer retorno = new Integer(0);
		if (rs.next())
			retorno = rs.getInt("eventointeresse");

		return retorno;
		
	}
	/**
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacio.NegociacaoEventoContratoInterfaceFacade#incluir(br.com.pactosolucoes.ce.negocio.evento.NegociacaoEventoContratoTO)
	 */
	
	public void incluirSemParcelas(final NegociacaoEventoTO negociacaoEvento, final NegociacaoEventoContratoTO negEvContrato) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO negociacaoeventocontrato( ");
		if (negEvContrato.getValorDesconto() != null) {
			sql.append("valordesconto, valordescontoespecifico, valordescontopercentual,");
		}
		sql.append(" nomeevento, somaproduto, datapagamento, dataevento, pagarcomboleto, responsavelcontrato, observacao, valorfinal, valorbasecalculo,");
		sql.append(" vigenciade, vigenciaate, vigenciaateajustada, perfilevento, pessoa, empresa, dividirprodutosnasparcelas, eventointeresse)");
		sql.append(" VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?");
		if (negEvContrato.getValorDesconto() != null) {
			sql.append(", ?, ?, ?)");
		} else {
			sql.append(" )");
		}

		int i = 0;
		Declaracao dc = new Declaracao(sql.toString(), this.con);

		if (negEvContrato.getValorDesconto() != null) {
			dc.setDouble(++i, negEvContrato.getValorDesconto());
			dc.setDouble(++i, negEvContrato.getValorDescontoEspecifico());
			dc.setDouble(++i, negEvContrato.getValorDescontoPercentual());
		}
		dc.setString(++i, negEvContrato.getNomeEvento());
		dc.setDouble(++i, negEvContrato.getSomaProduto() == null ? 0D : negEvContrato.getSomaProduto());
		dc.setDate(++i, new java.sql.Date(negEvContrato.getDataPagamento().getTime()));
		dc.setDate(++i, new java.sql.Date(negEvContrato.getDataEvento().getTime()));
		dc.setBoolean(++i, negEvContrato.getPagarComBoleto());
		dc.setInt(++i, negEvContrato.getResponsavelContrato().getCodigo());
		dc.setString(++i, negEvContrato.getObservacao());
		dc.setDouble(++i, negEvContrato.getValorFinal());
		dc.setDouble(++i, negEvContrato.getValorBaseCalculo());
		dc.setDate(++i, new java.sql.Date(negEvContrato.getVigenciaDe().getTime()));
		dc.setDate(++i, new java.sql.Date(negEvContrato.getVigenciaAte().getTime()));
		dc.setDate(++i, new java.sql.Date(negEvContrato.getVigenciaAteAjustada().getTime()));

		dc.setInt(++i, negEvContrato.getPerfilEvento());
		dc.setInt(++i, negEvContrato.getPessoa());
		dc.setInt(++i, 1);
		dc.setBoolean(++i, negEvContrato.getDividirProdutoNasParcelas());
		dc.setInt(++i, negEvContrato.getCodigoEvento());
//		dc.setInt(++i, negEvContrato.getSituacao());
		dc.execute();
		negEvContrato.setCodigo(this.obterValorChavePrimariaCodigo("negociacaoeventocontrato"));
		// incluir movproduto
		FacadeManager.getFacade().getMovProduto().incluirSemValidar(this.inicializarMovProduto(negociacaoEvento, negEvContrato));
		// incluir parcelas
	}
	
	
	/* (non-Javadoc)
	 * @see br.com.pactosolucoes.ce.negocio.interfaces.negociacao.NegociacaoEventoContratoInterfaceFacade#incluir(br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoTO, br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO)
	 */
	public void incluir(final NegociacaoEventoTO negociacaoEvento, final NegociacaoEventoContratoTO negEvContrato) 
				throws Exception{
		if(negEvContrato.getCodigo()==null || negEvContrato.equals(0)){
			this.incluirSemParcelas(negociacaoEvento, negEvContrato);
		}
		this.gerarParcelas(negociacaoEvento, negEvContrato);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<NegociacaoEventoContratoTO> consultar(final NegociacaoEventoContratoTO parametro) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT codigo, valordesconto, valordescontoespecifico, valordescontopercentual,nomeevento, ");
		sql.append(" somaproduto, datapagamento, dataevento, pagarcomboleto,");
		sql.append(" responsavelcontrato, observacao, valorfinal, valorbasecalculo,");
		sql.append(" vigenciade, vigenciaate, vigenciaateajustada, perfilevento,");
		sql.append(" pessoa, empresa, dividirprodutosnasparcelas, eventointeresse ");
		sql.append(" FROM negociacaoeventocontrato");
		sql.append(" WHERE 1=1");
		// TODO adicionar demais filtros
		if (parametro != null) {
			if (parametro.getCodigo() != null) {
				sql.append("AND codigo = ?");
			}
			if (parametro.getCodigoEvento() != null) {
				sql.append("AND eventointeresse = ?");
			}
		}
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		if (parametro.getCodigo() != null) {
			dc.setInt(++i, parametro.getCodigo());
		}
		if (parametro.getCodigoEvento() != null) {
			dc.setInt(++i, parametro.getCodigoEvento());
		}
		ResultSet dadosSQL = dc.executeQuery();
		return this.montarDadosConsulta(dadosSQL);
	}

	@Override
	public NegociacaoEventoContratoTO montarDados(final ResultSet dadosSQL) throws Exception {
		NegociacaoEventoContratoTO negEvContrato = new NegociacaoEventoContratoTO();
		negEvContrato.setCodigo(dadosSQL.getInt("codigo"));
		negEvContrato.setValorDesconto(dadosSQL.getDouble("valordesconto"));
		negEvContrato.setValorDescontoEspecifico(dadosSQL.getDouble("valordescontoespecifico"));
		negEvContrato.setValorDescontoPercentual(dadosSQL.getDouble("valordescontopercentual"));
		negEvContrato.setNomeEvento(dadosSQL.getString("nomeevento"));
		negEvContrato.setSomaProduto(dadosSQL.getDouble("somaproduto"));
		negEvContrato.setValorFinal(dadosSQL.getDouble("valorfinal"));
		negEvContrato.setDataEvento(dadosSQL.getDate("dataevento"));
		negEvContrato.setDataPagamento(dadosSQL.getDate("datapagamento"));
		negEvContrato.setPagarComBoleto(dadosSQL.getBoolean("pagarcomboleto"));
		negEvContrato.getResponsavelContrato().setCodigo(dadosSQL.getInt("responsavelcontrato"));
		negEvContrato.setObservacao(dadosSQL.getString("observacao"));
		negEvContrato.setValorBaseCalculo(dadosSQL.getDouble("valorbasecalculo"));
		negEvContrato.setVigenciaDe(dadosSQL.getDate("vigenciade"));
		negEvContrato.setVigenciaAte(dadosSQL.getDate("vigenciaate"));
		negEvContrato.setVigenciaAteAjustada(dadosSQL.getDate("vigenciaateajustada"));
//		negEvContrato.setSituacao(dadosSQL.getInt("situacao"));
		negEvContrato.setPessoa(dadosSQL.getInt("pessoa"));
		negEvContrato.setPerfilEvento(dadosSQL.getInt("perfilevento"));
		negEvContrato.setEmpresa(dadosSQL.getInt("empresa"));
		negEvContrato.setDividirProdutoNasParcelas(dadosSQL.getBoolean("dividirprodutosnasparcelas"));
		negEvContrato.setCodigoEvento(dadosSQL.getInt("eventointeresse"));
		return negEvContrato;
	}

	/**
	 * Método responsavel por criar as parcelas de pagamento do contrato de evento
	 * 
	 * @param negociacaoEvento
	 * @param negociacaoContrato
	 * @throws Exception
	 */
	public void gerarParcelas(final NegociacaoEventoTO negociacaoEvento, final NegociacaoEventoContratoTO negociacaoContrato)
			throws Exception {
		// data da primeira parcela é a data do evento
		Date dataPrimeiraParcela = negociacaoEvento.getDataEvento();
		// pegar o numero de parcelas escolhido na cond de pagamento
		int condicao = negociacaoEvento.getCondicaoPagamento().getNrParcelas();
		// a diferença entre duas parcelas
		int diferencaEntreDias = negociacaoEvento.getCondicaoPagamento().getIntervaloEntreParcela();
		// o valor de cada parcela
		double valorParcelas = negociacaoEvento.getValorFinal() / condicao;

		
		for (int i = 0; i < condicao; i++) {
			MovParcelaVO parcela = new MovParcelaVO();
			// data de registro é a data atual
			parcela.setDataRegistro(negocio.comuns.utilitarias.Calendario.hoje());
			// data da primeira mais os dias de diferença
			parcela.setDataVencimento(dataPrimeiraParcela = Uteis.somarDias(dataPrimeiraParcela, diferencaEntreDias));
			parcela.setDataAlteracaoManual(dataPrimeiraParcela);
			// descrição padrão do zillyon web
			parcela.setDescricao("PARCELA " + (i + 1));
			// parcela em aberto
			parcela.setSituacao("EA");
			parcela.setResponsavel(negociacaoContrato.getResponsavelContrato());
			parcela.setValorBaseCalculo(negociacaoEvento.getValorFinal());
			parcela.setValorParcela(valorParcelas);
			parcela.setEmpresa(negociacaoEvento.getEmpresa());
			parcela.getPessoa().setCodigo(negociacaoContrato.getPessoa());
			// incluir a parcela
			getZwFacade().incluirMovParcelaSemValidar(parcela);
			NegociacaoEventoContratoParcelas necp = new NegociacaoEventoContratoParcelas();
			// fazer a relação entre a parcela e o contrato do ce
			necp.incluir(negociacaoContrato.getCodigo(), parcela.getCodigo());
		}

	}
	/**
	 * Salvar uma lista de parcelas relacionando com evento
	 * @param parcelas
	 * @param codigoNegociacao
	 * @throws Exception
	 */
	public void salvarParcelas(List<MovParcelaVO> parcelas, Integer codigoNegociacao) throws Exception{
		MovParcela movParcela = new MovParcela();
		NegociacaoEventoContratoParcelas necp = new NegociacaoEventoContratoParcelas();
		for(MovParcelaVO parcela : parcelas){
			// incluir a parcela
			getZwFacade().incluirMovParcelaSemValidar(parcela);
			// fazer a relação entre a parcela e o contrato do ce
			necp.incluir(codigoNegociacao, movParcela.obterValorChavePrimariaCodigo());
		}
	}
	/**
	 * Salvar uma parcela relacionando com evento
	 * @param parcelas
	 * @param codigoNegociacao
	 * @throws Exception
	 */
	public void salvarParcela(MovParcelaVO parcela, Integer codigoNegociacao) throws Exception{
		MovParcela movParcela = new MovParcela();
		NegociacaoEventoContratoParcelas necp = new NegociacaoEventoContratoParcelas();
		// incluir a parcela
		getZwFacade().incluirMovParcelaSemValidar(parcela);
		// fazer a relação entre a parcela e o contrato do ce
		necp.incluir(codigoNegociacao, movParcela.obterValorChavePrimariaCodigo());
		
	}

	/**
	 * Método responsavel por inicializar um objeto movprodutovo, para inserir o registro no banco.
	 * 
	 * <AUTHOR>
	 */
	public MovProdutoVO inicializarMovProduto(final NegociacaoEventoTO negociacaoEvento, final NegociacaoEventoContratoTO negEvContrato) {

		MovProdutoVO movProdutoVO = new MovProdutoVO();
		movProdutoVO.inicializarDados();
		// codigo do produto relacionado no perfilevento
		movProdutoVO.getProduto().setCodigo(negociacaoEvento.getPerfilEventoTO().getProduto().getCodigoProduto());
		// descricao do produto
		movProdutoVO.setDescricao(negociacaoEvento.getPerfilEventoTO().getProduto().getDescricaoProduto());
		// situação inicial "em aberto"
		movProdutoVO.setSituacao("EA");
		// TODO pegar empresa logado, o valor 1 está aqui para facilitar nos testes
		movProdutoVO.getEmpresa().setCodigo(1);
		// pessoa do contrato
		movProdutoVO.getPessoa().setCodigo(negEvContrato.getPessoa());
		// mes de referencia
		movProdutoVO.setMesReferencia(Uteis.getMesReferenciaData(negEvContrato.getVigenciaDe()));
		// quantidade sempre será 1 ( um evento )
		movProdutoVO.setQuantidade(1);
		// ano de referencia
		movProdutoVO.setAnoReferencia(Uteis.getAnoData(negEvContrato.getVigenciaDe()));
		// datas de vigencia elançamento
		movProdutoVO.setDataInicioVigencia(negEvContrato.getVigenciaDe());
		movProdutoVO.setDataFinalVigencia(negEvContrato.getVigenciaAte());
		movProdutoVO.setDataLancamento(negocio.comuns.utilitarias.Calendario.hoje());
		// responsavel do contrato, ou seja, o responsavel logado no sistema
		movProdutoVO.setResponsavelLancamento(negEvContrato.getResponsavelContrato());
		// valor do produto
		movProdutoVO.setPrecoUnitario(negociacaoEvento.getValorFinal());
		return movProdutoVO;
	}

	/**
	 * Método que retorna dados de contrato de negociação de evento montados em objetos da classe ContratoVO. Utilizado no
	 * MovParcelaControle, para fazer a integração entre a view desse controle, que é do ZillyonWeb, e os dados do Central de Eventos.
	 * 
	 * @param parametro
	 * <AUTHOR>
	 * @return Lista de contratos com dados de contratos de eventos
	 * @throws Exception
	 */
	public ContratoVO consultarContratoEvento(final NegociacaoEventoContratoTO parametro) throws Exception {
		return this.montarContratoEvento(this.consultar(parametro));
	}

	/**
	 * Monta dados de uma lista de contratos de eventos numa lista de contratos
	 * 
	 * @param contratosEvento
	 * @return
	 * @throws Exception
	 */
	private ContratoVO montarContratoEvento(final List<NegociacaoEventoContratoTO> contratosEvento) throws Exception {
		if ((contratosEvento == null) || (contratosEvento.size() == 0)) {
			return new ContratoVO();
		}
		// como o filtro é a chave primária, a busca deve retornar apenas um resultado
		NegociacaoEventoContratoTO negEvContrato = contratosEvento.get(0);
		// montar dados do contrato a ser retornado
		ContratoVO contrato = new ContratoVO();
		contrato.inicializarDadosContrato();
		contrato.setPessoa(FacadeManager.getFacade().getPessoa().consultarPorChavePrimaria(negEvContrato.getPessoa(),
				Uteis.NIVELMONTARDADOS_DADOSBASICOS));
		contrato.setCodigo(negEvContrato.getCodigo());
		contrato.setDataLancamento(negEvContrato.getVigenciaDe());
		contrato.setValorFinal(negEvContrato.getValorFinal());
		contrato.getPlano().setDescricao(negEvContrato.getNomeEvento());
		// inserir neste objeto contrato uma lista de parcelas
		List<MovParcelaVO> parcelas = FacadeManager.getFacade().getMovParcela().consultarPorContratoEvento(negEvContrato.getCodigo(),
				Uteis.NIVELMONTARDADOS_DADOSBASICOS);
		// percorrer a lista de parcelas, preenchendo dados básicos de pessoa que são usados na view de pagamento de parcelas
		for (MovParcelaVO parcela : parcelas) {
			parcela.setContrato(new ContratoVO());
			parcela.getContrato().getPessoa().setCodigo(contrato.getPessoa().getCodigo());
			parcela.getContrato().getPessoa().setNome(contrato.getPessoa().getNome());
			contrato.getMovParcelaVOs().add(parcela);
		}
		return contrato;
	}
	/**
	 * Excluir os recibos de pagamentos relacionados ao evento
	 * @param codigoContrato
	 * @throws Exception 
	 */
	private void excluirRecibos(Integer codigoContrato) throws Exception{
		super.excluir(this.getIdEntidade());
		//guardar parcelas do contrato - evento
		List<Integer> recibos= obterRecibos(codigoContrato);
		//excluir relacionamento 
		StringBuilder sql = new StringBuilder();
		sql.append("DELETE FROM negociacaoeventocontratorecibo WHERE contrato = ?");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setInt(++i, codigoContrato);
		dc.execute();
		//excluir parcelas 
		for(Integer codigo : recibos){
			sql = new StringBuilder();
			sql.append("DELETE FROM recibopagamento WHERE codigo = ?");
			dc = new Declaracao(sql.toString(), this.con);
			i = 0;
			dc.setInt(++i, codigo);
			dc.execute();
		}
		
	}
	/**
	 * Obter código dos recibos  relacionados ao contrato do evento
	 * @param codigoContrato
	 * @return lista de codigos de parcelas relacionadas
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List<Integer> obterRecibos(Integer codigoContrato) throws Exception{
		List<Integer> recibos = new ArrayList<Integer>();
		StringBuilder sql = new StringBuilder();
		//pesquisar codigo dos recibos  relacionados com evento
		sql.append("SELECT r.codigo FROM recibopagamento r INNER JOIN negociacaoeventocontratorecibo ne ");
		sql.append("ON r.codigo = ne.recibo ");
		//onde o codigo do contrato do evento seja igual ao recebido como parametro
		sql.append("WHERE ne.contrato = ? ");
		Declaracao dc = new Declaracao(sql.toString(), this.con);
		int i = 0;
		dc.setInt(++i, codigoContrato);
		ResultSet rs = dc.executeQuery();
		while(rs.next()){
			//adicionar a lista
			recibos.add(rs.getInt("codigo"));
		}
		return recibos;
	}


	/**
	 * @return the zwFacade
	 * @throws Exception 
	 */
	public ZillyonWebFacade getZwFacade() throws Exception {
		if(zwFacade == null){
			zwFacade = new ZillyonWebFacade(this.con);
		}
		return zwFacade;
	}
	
	public List<CaixaAbertoTO> obterParcelasCaixaAberto(Integer contratoEvento, String nomePessoa, 
														Date inicio, Date fim, ConfPaginacao confPaginacao, 
														boolean ordenarPorLancamento, List<Integer> parcelasSelecionadas) throws Exception{
    	
    	//1- CONFIGURANDO A NAVEGACAO DA PAGINACAO
        confPaginacao.configurarNavegacao();
        
        //2 - CONFIGURA A INICIALIZACAO DA PAGINACAO PARA ESTA DAO.
        confPaginacao.iniciarPaginacao(this);
        
        //sql principal
        StringBuffer sql = new StringBuffer();
		sql.append(" SELECT necp.contrato, p.nome, p.codigo AS pessoa, ne.vigenciade, ne.nomeevento, ne.valorfinal, ne.eventoInteresse \n");
		sql.append(" FROM negociacaoeventocontratoparcelas necp, movparcela mp, pessoa p, negociacaoeventocontrato ne \n");
		sql.append(" WHERE mp.codigo = necp.parcela \n");
		sql.append(" AND p.codigo = mp.pessoa \n");
		sql.append(" AND ne.codigo = necp.contrato \n");
		
		
		if(UteisValidacao.emptyNumber(contratoEvento)){
			if(inicio != null && fim != null){
	        	sql.append(" AND mp.datavencimento >= '"+Uteis.getDataJDBC(inicio)+" 00:00:00' \n");
	            sql.append(" AND mp.datavencimento <= '"+Uteis.getDataJDBC(fim)+"  23:59:59' \n");	
	        }
			if(!UteisValidacao.emptyString(nomePessoa)){
				sql.append(" AND p.nome LIKE '"+nomePessoa.toUpperCase()+"%' \n");
			}
			sql.append(" AND mp.situacao= 'EA' ");
		}else{
			sql.append(" AND necp.contrato = "+contratoEvento);
		}
		
		sql.append(" GROUP BY necp.contrato, p.nome, p.codigo, ne.vigenciade, ne.nomeevento, ne.valorfinal, ne.eventoInteresse ");
		if(ordenarPorLancamento){
        	sql.append(" ORDER BY ne.vigenciade DESC");
        }else{
        	sql.append(" ORDER BY nome");
        }
        String sqlCount = "SELECT COUNT(*) FROM ("+sql.toString()+") AS consultaCount";
        ResultSet rsCount = criarConsulta(sqlCount, con);
        rsCount.next();
        
        //3 - ADICIONA PAGINACAO NA CONSULTA
        confPaginacao.addPaginacao(sql);

        //4 - REALIZA A CONSULTA COM PAGINACAO
        ResultSet rs = confPaginacao.consultaPaginada(rsCount.getInt(1));
        return montarDadosCaixaAberto(rs,parcelasSelecionadas);
	}
	
    public List<CaixaAbertoTO> montarDadosCaixaAberto(ResultSet rs, List<Integer> parcelasSelecionadas) throws Exception{
    	List<CaixaAbertoTO> dados = new ArrayList<CaixaAbertoTO>();
    	while(rs.next()){
    		CaixaAbertoTO obj = new CaixaAbertoTO();
    		obj.setPessoa(rs.getInt("pessoa"));
    		obj.setContrato(rs.getInt("contrato"));
    		obj.setNomeCliente(rs.getString("nome"));
    		obj.setDescricao(rs.getString("nomeevento"));
    		obj.setLancamento(rs.getDate("vigenciade"));
    		obj.setValorTotal(rs.getDouble("valorfinal"));
    		obj.setCodigoEvento(rs.getInt("eventoInteresse"));
    		MovParcela parcelaDAO = new MovParcela();
    		List<MovParcelaVO> list = parcelaDAO.consultarPorContratoEvento(obj.getContrato(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    		Ordenacao.ordenarLista(list, "dataVencimento");
    		obj.setMarcarTodas(true);
    		for(MovParcelaVO parcela : list){
    			parcela.getPessoa().setCodigo(obj.getPessoa());
    			parcela.getPessoa().setNome(obj.getNomeCliente());
    			if(parcelasSelecionadas.contains(parcela.getCodigo())){
    				parcela.setParcelaEscolhida(true);
    			}else{
    				obj.setMarcarTodas(false);
    			}
    			if(parcela.getSituacao().equals("EA")){
    				NegociacaoEventoContratoTO parametro = new NegociacaoEventoContratoTO();
    				parametro.setCodigo(rs.getInt("contrato"));
    				parcela.setContrato(montarContratoEvento(consultar(parametro)));
    				obj.getParcelas().add(parcela);
    			}else
    				if(parcela.getSituacao().equals("PG")){
    					obj.getParcelasPagasCE().add(parcela);
    			}
    		}
    		obj.setPagamentosCE(new MovPagamento().consultarPagamentosEvento(obj.getCodigoEvento(), Uteis.NIVELMONTARDADOS_TELACONSULTA));
    		obj.setExibeParcelas(false);
    		dados.add(obj);
    	}
    	return dados;
    }
    
    public List<ReciboEventoTO> consultarRecibos(Integer codigoEventoInteresse) throws Exception{
    	List<ReciboEventoTO> recibos = new ArrayList<ReciboEventoTO>();
    	StringBuilder sql = new StringBuilder();
    	sql.append(" select distinct rp.* from recibopagamento rp \n");
    	sql.append(" inner join movpagamento mp on mp.recibopagamento = rp.codigo \n");
    	sql.append(" inner join pagamentomovparcela pmp on pmp.movpagamento = mp.codigo \n");
    	sql.append(" inner join negociacaoeventocontratoparcelas necp on pmp.movparcela = necp.parcela \n");
    	sql.append(" inner join negociacaoeventocontrato nec on nec.codigo = necp.contrato \n"); 
    	sql.append(" WHERE nec.eventointeresse = "+codigoEventoInteresse);
    	
    	ResultSet consulta = criarConsulta(sql.toString(),con);
    	while(consulta.next()){
    		ReciboEventoTO re = new ReciboEventoTO();
    		re.setCodigoRecibo(consulta.getInt("codigo"));
    		re.setDataRecibo(consulta.getTimestamp("data"));
    		re.setNomePessoaPagadora(consulta.getString("nomepessoapagador"));
    		re.setValor(consulta.getDouble("valortotal"));
    		re.setRecibo(ReciboPagamento.montarDados(consulta, Uteis.NIVELMONTARDADOS_TODOS, con));
    		re.setCodigoEvento(codigoEventoInteresse);
    		recibos.add(re);
    	}
    	return recibos;
    }
    
    public List<EmailVO> consultaEmails(Integer codigoEventoInteresse) throws Exception{
    	 String sql = "select e.* from negociacaoeventocontrato nec inner join email e on e.pessoa = nec.pessoa where nec.eventointeresse = "+codigoEventoInteresse;
    	 return Email.montarDadosConsulta(criarConsulta(sql, con), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
    }
    

}
