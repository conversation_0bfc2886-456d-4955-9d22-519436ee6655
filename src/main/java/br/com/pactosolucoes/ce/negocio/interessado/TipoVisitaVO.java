package br.com.pactosolucoes.ce.negocio.interessado;

import negocio.comuns.arquitetura.SuperVO;

/**
 * Reponsável por manter os dados da entidade TipoVisita. Classe do tipo VO - Value Object composta pelos atributos da entidade com
 * visibilidade protegida e os métodos de acesso a estes atributos. Classe utilizada para apresentar e manter em memória os dados desta
 * entidade.
 * 
 * @see SuperVO
 * <AUTHOR>
 */
public class TipoVisitaVO extends SuperVO {
	private Integer codigo;
	private String nome;
	private Integer duracaoMin;

	/**
	 * @return the codigo
	 */
	@Override
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            the codigo to set
	 */
	@Override
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the nome
	 */
	public String getNome() {
		return this.nome;
	}

	/**
	 * @param nome
	 *            the nome to set
	 */
	public void setNome(final String nome) {
		this.nome = nome;
	}

	/**
	 * @return the duracao
	 */
	public Integer getDuracaoMin() {
		return this.duracaoMin;
	}

	/**
	 * @param duracao
	 *            the duracao to set
	 */
	public void setDuracaoMin(final Integer duracao) {
		this.duracaoMin = duracao;
	}

}
