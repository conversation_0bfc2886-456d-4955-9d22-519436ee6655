package br.com.pactosolucoes.ce.negocio.interfaces.negociacao;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoContratoTO;
import br.com.pactosolucoes.ce.comuns.to.NegociacaoEventoTO;
import br.com.pactosolucoes.ce.comuns.to.ReciboEventoTO;
import negocio.comuns.basico.EmailVO;
import negocio.interfaces.basico.SuperInterface;

/**
 * Interface reponsável por criar uma estrutura padrão de comunidação entre a camada de controle e camada de negócio.
 * 
 * <AUTHOR>
 * 
 */
public interface NegociacaoEventoContratoInterfaceFacade extends SuperInterface {

	/**
	 * Operação responsável por incluir no banco de dados um objeto da classe <code>NegociacaoEventoContrato</code>.
	 * 
	 * @param negEvContrato
	 *            Objeto da classe <code>NegociacaoEventoContrato</code> que será gravado no banco de dados.
	 * @throws Exception.
	 */
	void incluir(NegociacaoEventoTO negociacaoEvento, NegociacaoEventoContratoTO negEvContrato) throws Exception;

	void alterar(NegociacaoEventoContratoTO negEvContrato) throws Exception;

	void excluir(Integer codigoNegEvContrato) throws Exception;

	/**
	 * Consulta todos os contratos de negociacao de um objeto<code>NegociacaoEventoContratoTO</code>
	 * 
	 * @param parametro
	 *            Objeto da classe <code>NegociacaoEventoContratoTO</code> que será consultado no banco de dados.
	 * @return montar dados da consulta
	 * @throws Exception
	 */
	List<NegociacaoEventoContratoTO> consultar(NegociacaoEventoContratoTO parametro) throws Exception;
	
	public List<ReciboEventoTO> consultarRecibos(Integer codigoContratoEvento) throws Exception;
	
	public List<EmailVO> consultaEmails(Integer codigoEventoInteresse) throws Exception;

}
