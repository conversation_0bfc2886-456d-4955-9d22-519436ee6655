/**
 * 
 */
package br.com.pactosolucoes.ce.negocio.interfaces.negociacao;

import java.util.List;

import br.com.pactosolucoes.ce.comuns.to.AmbienteTO;
import br.com.pactosolucoes.ce.comuns.to.TipoLayoutTO;
import negocio.interfaces.basico.SuperInterface;

/**
 * <AUTHOR>
 * 
 */
public interface TipoLayoutInterfaceFacade extends SuperInterface {

	/**
	 * Operação responsável por consultar um ambiente no banco de dados um objeto da classe <code>Ambiente</code>.
	 * 
	 * @param ambiente
	 *            Objeto da classe <code>Ambiente</code> que será consultado no banco de dados.
	 * @throws Exception.
	 * @return montar dados da consulta
	 */
	List<TipoLayoutTO> consultarPorAmbiente(AmbienteTO ambiente) throws Exception;

}
