/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.FaixaQuantidade;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Negociação perfil evento com serviço.
 * 
 * <AUTHOR>
 */
public class NegEvPerfilEventoServicoTO implements Serializable, Cloneable {
    private static final long serialVersionUID = -1731590549299802375L;


	
	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private Integer codigoServico;
    private Integer codigoFornecedor;
	@NaoControlarLogAlteracao
	private String descricaoServico;
    @NaoControlarLogAlteracao
    private String descricaoFornecedor;
	private String textoLivre;
	private Integer quantidade;
	private Boolean obrigatorio;


	private Double valor;
	private Double valorUnitario;

	public NegEvPerfilEventoServicoTO clone() throws CloneNotSupportedException{
		return (NegEvPerfilEventoServicoTO) super.clone();
	}
	
	/**
	 * guarda a o valor unitario por faixa de quantidade do produto
	 */
	@NaoControlarLogAlteracao
	private Map<FaixaQuantidade, Double> faixasValor;

	private Boolean selecionado;
	private Boolean extra;
	private Boolean alteracaoValor;
	/**
	 * @return O campo codigo.
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            O novo valor de codigo.
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return O campo codigoServico.
	 */
	public Integer getCodigoServico() {
		return this.codigoServico;
	}

	/**
	 * @param codigoServico
	 *            O novo valor de codigoServico.
	 */
	public void setCodigoServico(final Integer codigoServico) {
		this.codigoServico = codigoServico;
	}

	/**
	 * @return O campo descricaoServico.
	 */
	public String getDescricaoServico() {
		return this.descricaoServico;
	}

	/**
	 * @param descricaoServico
	 *            O novo valor de descricaoServico.
	 */
	public void setDescricaoServico(final String descricaoServico) {
		this.descricaoServico = descricaoServico;
	}
	/**
	 * @param valorUnitario
	 *            O novo valor de valorUnitario.
	 */
	public void setValorUnitarioFormatado(final String valorUnitario) {
		this.valorUnitario = Formatador.obterValorNumerico(valorUnitario);
	}
	/**
	 * @return O campo valorUnitario.
	 */
	public String getValorUnitarioFormatado() {
		return Formatador.formatarValorNumerico(this.getValorUnitario());
	}

	/**
	 * @return O campo textoLivre.
	 */
	public String getTextoLivre() {
		return this.textoLivre;
	}

	/**
	 * @param textoLivre
	 *            O novo valor de textoLivre.
	 */
	public void setTextoLivre(final String textoLivre) {
		this.textoLivre = textoLivre;
	}

	/**
	 * @return O campo quantidade.
	 */
	public Integer getQuantidade() {
		// Se quantidade for igual a nulo
		// quantidade recebe quantidade mínima
		if (this.quantidade == null) {
			this.quantidade = 0;
		}
		return this.quantidade;
	}

	/**
	 * @param quantidade
	 *            O novo valor de quantidade.
	 */
	public void setQuantidade(final Integer quantidade) {
		this.quantidade = quantidade;
	}


	/**
	 * @return O campo valor.
	 */
	public Double getValor() {
		// Se o campo for igual a nulo
		// o valor recebe zero
		if (this.valor == null) {
			this.valor = 0.0;
		}
		return this.valor;
	}

	/**
	 * @return O campo valor.
	 */
	public String getValorMonetario() {
		return Formatador.formatarValorMonetario(this.getValor());
	}

	/**
	 * @return O campo valor.
	 */
	public String getValorFormatado() {
		return Formatador.formatarValorNumerico(this.getValor());
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValor(final Double valor) {
		this.valor = valor;
	}

	/**
	 * @param valor
	 *            O novo valor de valor.
	 */
	public void setValorFormatado(final String valor) {
		this.valor = Formatador.obterValorNumerico(valor);
	}

	/**
	 * @return O campo valorUnitario.
	 */
	public Double getValorUnitario() {
		// Se valorUnitario for igual a nulo
		// o valor recebe zero
		if (this.valorUnitario == null) {
			this.valorUnitario = 0.0;
		}
		return this.valorUnitario;
	}

	/**
	 * @return O campo valorUnitario.
	 */
	public String getValorUnitarioMonetario() {
		return Formatador.formatarValorMonetario(this.getValorUnitario());
	}

	/**
	 * @param valorUnitario
	 *            O novo valor de valorUnitario.
	 */
	public void setValorUnitario(final Double valorUnitario) {
		this.valorUnitario = valorUnitario;
	}

	/**
	 * @return O campo faixasValor.
	 */
	public Map<FaixaQuantidade, Double> getFaixasValor() {
		// Se o campo faixasValor for igual a nulo
		// faixasValor cria um novo objeto do tipo HashMap
		if (this.faixasValor == null) {
			this.faixasValor = new HashMap<FaixaQuantidade, Double>();
		}
		return this.faixasValor;
	}

	/**
	 * @param faixasValor
	 *            O novo valor de faixasValor.
	 */
	public void setFaixasValor(final Map<FaixaQuantidade, Double> faixasValor) {
		this.faixasValor = faixasValor;
	}

	/**
	 * @return O campo selecionado.
	 */
	public Boolean getSelecionado() {
		// Se o campo selecionado for igual a nulo
		// o campo selecionado recebe falso
		if (this.selecionado == null) {
			this.selecionado = Boolean.FALSE;
		}
		return this.selecionado;
	}

	/**
	 * @param selecionado
	 *            O novo valor de selecionado.
	 */
	public void setSelecionado(final Boolean selecionado) {
		this.selecionado = selecionado;
	}

	/**
	 * @param extra the extra to set
	 */
	public void setExtra(Boolean extra) {
		this.extra = extra;
	}

	/**
	 * @return the extra
	 */
	public Boolean getExtra() {
		// Se o campo extra for igual a nulo
		// o campo extra recebe falso
		if (this.extra == null) {
			this.extra = Boolean.FALSE;
		}
		return this.extra;
	}

	/**
	 * @param alteracaoValor the alteracaoValor to set
	 */
	public void setAlteracaoValor(Boolean alteracaoValor) {
		this.alteracaoValor = alteracaoValor;
	}

	/**
	 * @return the alteracaoValor
	 */
	public Boolean getAlteracaoValor() {
		if(alteracaoValor == null)
			alteracaoValor = Boolean.FALSE;
		return alteracaoValor;
	}

	/**
	 * @return O campo obrigatorio.
	 */
	public Boolean getObrigatorio() {
		// Se o campo obrigatório for igual a nulo
		// o valor obrigatório recebe falso
		if (this.obrigatorio == null) {
			this.obrigatorio = false;
		}
		return this.obrigatorio;
	}

	/**
	 * @param obrigatorio
	 *            O novo valor de obrigatorio.
	 */
	public void setObrigatorio(final Boolean obrigatorio) {
		this.obrigatorio = obrigatorio;
	}

    public Integer getCodigoFornecedor() {
        return codigoFornecedor;
    }

    public void setCodigoFornecedor(Integer codigoFornecedor) {
        this.codigoFornecedor = codigoFornecedor;
    }

    public String getDescricaoFornecedor() {
        return descricaoFornecedor;
    }

    public void setDescricaoFornecedor(String descricaoFornecedor) {
        this.descricaoFornecedor = descricaoFornecedor;
    }
}
