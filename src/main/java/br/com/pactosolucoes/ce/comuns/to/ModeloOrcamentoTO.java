/**
 * 
 */
package br.com.pactosolucoes.ce.comuns.to;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import annotations.arquitetura.ChavePrimaria;
import annotations.arquitetura.NaoControlarLogAlteracao;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Modelo de orçamento.
 * 
 * <AUTHOR>
 */
public class ModeloOrcamentoTO extends SuperTO implements Cloneable {

    private static final long serialVersionUID = 5919122572002868120L;
    // Atributos
    @ChavePrimaria
    private Integer codigo;
    private String nomeArquivo;
    private String descricao;
    private File arquivo;
    @NaoControlarLogAlteracao
    private List<ModeloImagemTO> imagens;
    private Date dataImpressao;
    private Date dataEnvio;
    private boolean possuiImpressao = false;

    // Getters and Setters
    /**
     * @return the dataEnvio
     */
    public Date getDataEnvio() {
        return dataEnvio;
    }

    public String getDataEnvioFormatada() {
        return Formatador.formatarData(this.getDataEnvio(), Formatador.FORMATO_DATA);
    }

    public ModeloOrcamentoTO clone() throws CloneNotSupportedException {
        return (ModeloOrcamentoTO) super.clone();
    }

    /**
     * @param dataEnvio the dataEnvio to set
     */
    public void setDataEnvio(Date dataEnvio) {
        this.dataEnvio = dataEnvio;
    }

    /**
     * @return the dataImpressao
     */
    public Date getDataImpressao() {
        return dataImpressao;
    }

    public String getDataImpressaoFormatada() {
        return Formatador.formatarData(this.getDataImpressao(), Formatador.FORMATO_DATA);
    }

    /**
     * @param dataImpressao the dataImpressao to set
     */
    public void setDataImpressao(Date dataImpressao) {
        this.dataImpressao = dataImpressao;
    }

    /**
     * @return O campo codigo.
     */
    public Integer getCodigo() {
        return this.codigo;
    }

    /**
     * @param codigo
     *            O novo valor de codigo.
     */
    public void setCodigo(final Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return O campo nomeArquivo.
     */
    public String getNomeArquivo() {
        return this.nomeArquivo;
    }

    /**
     * @param nomeArquivo
     *            O novo valor de nomeArquivo.
     */
    public void setNomeArquivo(final String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    /**
     * @return O campo descricao.
     */
    public String getDescricao() {
        return this.descricao;
    }

    /**
     * @param descricao
     *            O novo valor de descricao.
     */
    public void setDescricao(final String descricao) {
        this.descricao = descricao;
    }

    /**
     * @return O campo arquivo.
     */
    public File getArquivo() {
        return this.arquivo;
    }

    /**
     * @param arquivo
     *            O novo valor de arquivo.
     */
    public void setArquivo(final File arquivo) {
        this.arquivo = arquivo;
    }

    /**
     * @return O campo imagens.
     */
    public List<ModeloImagemTO> getImagens() {
        // Se o objeto imagem for igual a nulo
        // cria um novo objeto de vetores de modelo de imagem
        if (this.imagens == null) {
            this.imagens = new ArrayList<ModeloImagemTO>();
        }
        return this.imagens;
    }

    /**
     * @param imagens
     *            the imagens to set
     */
    public void setImagens(final List<ModeloImagemTO> imagens) {
        this.imagens = imagens;
    }

    /**
     * @param possuiImpressao the possuiImpressao to set
     */
    public void setPossuiImpressao(boolean possuiImpressao) {
        this.possuiImpressao = possuiImpressao;
    }

    /**
     * @return the possuiImpressao
     */
    public boolean getPossuiImpressao() {
        return possuiImpressao;
    }
}
