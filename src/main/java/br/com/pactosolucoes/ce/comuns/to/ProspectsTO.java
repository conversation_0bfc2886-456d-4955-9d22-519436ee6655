package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.Date;

import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.utilitarias.Uteis;
import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Transfer Object para encapsulamento de dados relacionados à consulta de Prospects
 * 
 * <AUTHOR>
 * 
 */
public class ProspectsTO implements Serializable {

	

	/**
	 * 
	 */
	private static final long serialVersionUID = -158842972755893281L;
	// Atributos
	private Integer codigoInteresse;
	private Integer codigoSituacao;
	private Integer codTipoSituacao;
	private String cliente;
	private String evento;
	private Date data;
	private Date dataInicio;
	private Date dataFim;
	private Boolean possuiEvento;
	private Boolean possuiConversa;
	private AmbienteVO ambiente;
	private Situacao situacao;
	private Date dataProxContato;
	private Integer interessado;
	private String tipoEvento;
	private Integer prioridade;
	private String cssColor;
	private String mensagem;
	private Integer tipoContato;

	// Getters and Setters

	/**
	 * @return the tipoEvento
	 */
	public String getTipoEvento() {
		return tipoEvento;
	}

	/**
	 * @param tipoEvento the tipoEvento to set
	 */
	public void setTipoEvento(String tipoEvento) {
		this.tipoEvento = tipoEvento;
	}
	
	/**
	 * @return the dataInicio
	 */
	public Date getDataInicio() {
		return this.dataInicio;
	}


	/**
	 * @param dataInicio
	 *            the dataInicio to set
	 */
	public void setDataInicio(final Date dataInicio) {
		this.dataInicio = dataInicio;
	}

	/**
	 * @return the dataFim
	 */
	public Date getDataFim() {
		return this.dataFim;
	}

	/**
	 * @param dataFim
	 *            the dataFim to set
	 */
	public void setDataFim(final Date dataFim) {
		this.dataFim = dataFim;
	}

	/**
	 * @return the interessado
	 */
	public Integer getInteressado() {
		return this.interessado;
	}

	/**
	 * @param interessado
	 *            the interessado to set
	 */
	public void setInteressado(final Integer interessado) {
		this.interessado = interessado;
	}

	/**
	 * @param codigoInteresse
	 *            the codigoInteresse to set
	 */
	public void setCodigoInteresse(final Integer codigoInteresse) {
		this.codigoInteresse = codigoInteresse;
	}

	/**
	 * @return the codigoTipoSituacao
	 */
	public Integer getCodigoTipoSituacao() {
		return this.codTipoSituacao;
	}

	/**
	 * @param codTipoSituacao
	 *            the codigoTipoSituacao to set
	 */
	public void setCodigoTipoSituacao(final Integer codTipoSituacao) {
		this.codTipoSituacao = codTipoSituacao;
	}

	/**
	 * @return the codigoSituacao
	 */
	public Integer getCodigoSituacao() {
		return this.codigoSituacao;
	}

	/**
	 * @param codigoSituacao
	 *            the codigoSituacao to set
	 */
	public void setCodigoSituacao(final Integer codigoSituacao) {
		this.codigoSituacao = codigoSituacao;
	}

	/**
	 * @return the situacao
	 */
	public Situacao getSituacao() {
		return this.situacao;
	}

	/**
	 * @param situacao
	 *            the situacao to set
	 */
	public void setSituacao(final Situacao situacao) {
		this.situacao = situacao;
	}

	/**
	 * @return the codigoInteresse
	 */
	public int getCodigoInteresse() {
		return this.codigoInteresse;
	}

	/**
	 * @param codigoInteresse
	 *            the codigoInteresse to set
	 */
	public void setCodigoInteresse(final int codigoInteresse) {
		this.codigoInteresse = codigoInteresse;
	}

	/**
	 * @return the cliente
	 */
	public String getCliente() {
		return this.cliente;
	}

	/**
	 * @param cliente
	 *            the cliente to set
	 */
	public void setCliente(final String cliente) {
		this.cliente = cliente;
	}

	/**
	 * @return the evento
	 */
	public String getEvento() {
		return this.evento;
	}

	/**
	 * @param evento
	 *            the evento to set
	 */
	public void setEvento(final String evento) {
		this.evento = evento;
	}

	/**
	 * @return the data
	 */
	public Date getData() {
		return this.data;
	}

	/**
	 * @param data
	 *            the data to set
	 */
	public void setData(final Date data) {
		this.data = data;
	}

	/**
	 * @return the ambiente
	 */
	public AmbienteVO getAmbiente() {
		// Se ambiete igual a nulo
		// ambiente cria um novo objetodo tipo AmbienteVO
		if (this.ambiente == null) {
			this.ambiente = new AmbienteVO();
		}
		return this.ambiente;
	}

	/**
	 * @param ambiente
	 *            the ambiente to set
	 */
	public void setAmbiente(final AmbienteVO ambiente) {
		this.ambiente = ambiente;
	}

	/**
	 * @return O campo dataInicio formatado.
	 */
	public String getDataFormatada() {
		return Formatador.formatarDataPadrao(this.data);
	}

	/**
	 * @param dataProxContato
	 *            the dataProximoContato to set
	 */
	public void setDataProximoContato(final Date dataProxContato) {
		this.dataProxContato = dataProxContato;
	}

	/**
	 * @return the dataProximoContato
	 */
	public Date getDataProximoContato() {
		return this.dataProxContato;
	}
	/**
	 * @return
	 */
	public String getExpiraEm(){
		String expira = "";
		if(dataProxContato == null){ 
			expira = "-";
		}else{
			long dias = Uteis.nrDiasEntreDatas(negocio.comuns.utilitarias.Calendario.hoje(), dataProxContato);
			expira = dias + " dias.";
		}
		return expira;
	}
	/**
	 * @return the dataProximoContato
	 */
	public String getDataProximoContatoFormatada() {
		return Formatador.formatarDataPadrao(this.dataProxContato);
	}

	/**
	 * @param possuiEvento
	 *            the possuiEvento to set
	 */
	public void setPossuiEvento(final Boolean possuiEvento) {
		this.possuiEvento = possuiEvento;
	}

	/**
	 * Se codigoInteresse maior que zero true se não false
	 * 
	 * @return the possuiEvento
	 */
	public Boolean getPossuiEvento() {
		// Se codigoInteresse maior que zero
		// possuiEvento recebe true
		// se possuiEvento recebe false
		if (this.codigoInteresse > 0) {
			this.possuiEvento = Boolean.TRUE;
		} else {
			this.possuiEvento = Boolean.FALSE;
		}
		return this.possuiEvento;
	}

	/**
	 * @param possuiConversa
	 *            the possuiConversa to set
	 */
	public void setPossuiConversa(final Boolean possuiConversa) {
		this.possuiConversa = possuiConversa;
	}

	/**
	 * Se possuiConversa igual a nulo
	 * 
	 * @return possuiConversa = false se não
	 * @return possuiConversa
	 */
	public Boolean getPossuiConversa() {
		// Se possuiConversa igual a nulo
		// retorna falso
		// se não retorna true
		return (this.possuiConversa == null) ? this.possuiConversa = Boolean.FALSE : this.possuiConversa;
	}

	/**
	 * @param ordA the ordA to set
	 */
	public void setPrioridade(Integer ordA) {
		this.prioridade = ordA;
	}

	/**
	 * @return the ordA
	 */
	public Integer getPrioridade() {
		return prioridade;
	}

	/**
	 * @return O campo cssColor.
	 */
	public String getCssColor() {
		return this.cssColor;
	}

	/**
	 * @param cssColor O novo valor de cssColor.
	 */
	public void setCssColor(String cssColor) {
		this.cssColor = cssColor;
	}

	/**
	 * @param mensagem the mensagem to set
	 */
	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}

	/**
	 * @return the mensagem
	 */
	public String getMensagem() {
		return mensagem;
	}

	/**
	 * @param tipoContato the tipoContato to set
	 */
	public void setTipoContato(Integer tipoContato) {
		this.tipoContato = tipoContato;
	}

	/**
	 * @return the tipoContato
	 */
	public Integer getTipoContato() {
		return tipoContato;
	}

}
