package br.com.pactosolucoes.ce.comuns.to;

import java.util.Date;

import negocio.comuns.arquitetura.UsuarioVO;
import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.ce.comuns.enumerador.Situacao;
import br.com.pactosolucoes.ce.comuns.enumerador.TipoContato;
import br.com.pactosolucoes.ce.negocio.evento.EventoInteresseVO;
import br.com.pactosolucoes.comuns.util.Formatador;
import negocio.comuns.arquitetura.SuperTO;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade Conversas
 * 
 * <AUTHOR>
 */
public class ConversaTO extends SuperTO {

    /**
     *
     */
    private static final long serialVersionUID = 7821688098584922447L;
    private static final String FORMATO_DATA = "dd/MM/yy HH:mm";
    private static final String FORMATO_DATA_SEM_HORA = "dd/MM/yy";
    // Atributos
    @ChavePrimaria
    private int codigo;
    private String descricao;
    private Date dataConversa;
    private Date dataProxConversa;
    private String formaContato;
    private String eventoInteresse;
    private int codigoInteressado;
    private int codUserRespons;
    private Integer codigoEvento;
    private Situacao situacao;
    private EventoInteresseVO evento;
    private Integer forma;
    protected PessoaTO pessoa;
    private UsuarioVO usuario;
    private TipoContato tipoContato;

    // Gettres and Setters
    /**
     * @param forma
     *            the forma to set
     */
    public void setForma(final Integer forma) {
        this.forma = forma;
    }

    /**
     * @return the forma
     */
    public Integer getForma() {
        return this.forma;
    }

    /**
     * @return the dataProxConversa
     */
    public Date getDataProxConversa() {
        return this.dataProxConversa;
    }

    /**
     * @param dataProxConversa
     *            the dataProxConversa to set
     */
    public void setDataProxConversa(final Date dataProxConversa) {
        this.dataProxConversa = dataProxConversa;
    }

    /**
     * @return the evento
     */
    public EventoInteresseVO getEvento() {
        return this.evento;
    }

    /**
     * @param evento
     *            the evento to set
     */
    public void setEvento(final EventoInteresseVO evento) {
        this.evento = evento;
    }

    /**
     * @return the eventoInteresse
     */
    public String getEventoInteresse() {
        return this.eventoInteresse;
    }

    /**
     * @param eventoInteresse
     *            the eventoInteresse to set
     */
    public void setEventoInteresse(final String eventoInteresse) {
        this.eventoInteresse = eventoInteresse;
    }

    /**
     * @return the situacao
     */
    public Situacao getSituacao() {
        return this.situacao;
    }

    /**
     * @param situacao
     *            the situacao to set
     */
    public void setSituacao(final Situacao situacao) {
        this.situacao = situacao;
    }

    /**
     * @return the codigoEvento
     */
    public Integer getCodigoEvento() {
        return this.codigoEvento;
    }

    /**
     * @param codigoEvento
     *            the codigoEvento to set
     */
    public void setCodigoEvento(final Integer codigoEvento) {
        this.codigoEvento = codigoEvento;
    }

    /**
     * @return the codigo
     */
    public Integer getCodigo() {
        return this.codigo;
    }

    /**
     * @param codigo
     *            the codigo to set
     */
    public void setCodigo(final Integer codigo) {
        this.codigo = codigo;
    }

    /**
     * @return the descricao
     */
    public String getDescricao() {
        return this.descricao;
    }

    /**
     * @param descricao
     *            the descricao to set
     */
    public void setDescricao(final String descricao) {
        this.descricao = descricao;
    }

    /**
     * @return the dataConversa
     */
    public Date getDataConversa() {
        // Se dataConversa for igual a nulo
        // dataconversa recebe a data atual
        if (this.dataConversa == null) {
            this.dataConversa = negocio.comuns.utilitarias.Calendario.hoje();
        }
        return this.dataConversa;
    }

    public String getDataConversaFormatada() {
        return Formatador.formatarData(this.getDataConversa(), ConversaTO.FORMATO_DATA);
    }

    public String getDataConversaFormatadaSemHora() {
        return Formatador.formatarData(this.getDataConversa(), ConversaTO.FORMATO_DATA_SEM_HORA);
    }

    public String getProxContatoFormatada() {
        return Formatador.formatarData(this.getDataProxConversa(), ConversaTO.FORMATO_DATA_SEM_HORA);
    }

    /**
     * @param dataConversa
     *            the dataConversa to set
     */
    public void setDataConversa(final Date dataConversa) {
        this.dataConversa = dataConversa;
    }

    /**
     * @return the codigoFormaContato
     */
    public String getFormaContato() {
        return this.formaContato;
    }

    /**
     * @param codigoFormaContato
     *            the codigoFormaContato to set
     */
    public void setFormaContato(final String FormaContato) {
        this.formaContato = FormaContato;
    }

    /**
     * @return the codigoInteressado
     */
    public int getCodigoInteressado() {
        return this.codigoInteressado;
    }

    /**
     * @param codigoInteressado
     *            the codigoInteressado to set
     */
    public void setCodigoInteressado(final int codigoInteressado) {
        this.codigoInteressado = codigoInteressado;
    }

    /**
     * @return the codigoUsuarioResponsavel
     */
    public int getCodigoUsuarioResponsavel() {
        return this.codUserRespons;
    }

    /**
     * @param codUserRespons
     *            the codigoUsuarioResponsavel to set
     */
    public void setCodigoUsuarioResponsavel(final int codUserRespons) {
        this.codUserRespons = codUserRespons;
    }

    /**
     * @return O campo pessoa.
     */
    public PessoaTO getPessoa() {
        return this.pessoa;
    }

    /**
     * @param pessoa
     *            O novo valor de pessoa.
     */
    public void setPessoa(final PessoaTO pessoa) {
        this.pessoa = pessoa;
    }

    /**
     * @param tipoContato the tipoContato to set
     */
    public void setTipoContato(TipoContato tipoContato) {
        this.tipoContato = tipoContato;
    }

    /**
     * @return the tipoContato
     */
    public TipoContato getTipoContato() {
        if (tipoContato == null) {
            tipoContato = TipoContato.CONVERSA;
        }
        return tipoContato;
    }

    /**
     * @param usuario the usuario to set
     */
    public void setUsuario(UsuarioVO usuario) {
        this.usuario = usuario;
    }

    /**
     * @return the usuario
     */
    public UsuarioVO getUsuario() {
        if (usuario == null) {
            usuario = new UsuarioVO();
        }
        return usuario;
    }
}
