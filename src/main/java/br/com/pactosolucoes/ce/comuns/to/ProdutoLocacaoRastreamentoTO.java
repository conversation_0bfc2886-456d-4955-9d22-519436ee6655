package br.com.pactosolucoes.ce.comuns.to;

import java.io.Serializable;
import java.util.Date;

import annotations.arquitetura.ChavePrimaria;
import br.com.pactosolucoes.comuns.util.Formatador;

/**
 * Transfer Object para encapsulamento de dados relacionados à entidade ProdutoLocacaoRastreamento.
 * 
 * <AUTHOR>
 * 
 */
public class ProdutoLocacaoRastreamentoTO implements Serializable, Cloneable {
    private static final long serialVersionUID = 1217389577507445379L;

	

	// Atributos
	@ChavePrimaria
	private Integer codigo;
	private String patrimonio;
	private Integer eventoInteresse;
	private String observacao;

	private String nomeEvento;
	private String nomeInteressado;
	private String descricaoAmbiente;
	private Date dataEvento;
	private Date horarioInicial;
	private Date horarioFinal;

	public ProdutoLocacaoRastreamentoTO clone() throws CloneNotSupportedException{
		return (ProdutoLocacaoRastreamentoTO) super.clone();
	}
	
	// Getters and setters
	/**
	 * @return the codigo
	 */
	public Integer getCodigo() {
		return this.codigo;
	}

	/**
	 * @param codigo
	 *            the codigo to set
	 */
	public void setCodigo(final Integer codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the patrimonio
	 */
	public String getPatrimonio() {
		return this.patrimonio;
	}

	/**
	 * @param patrimonio
	 *            the patrimonio to set
	 */
	public void setPatrimonio(final String patrimonio) {
		this.patrimonio = patrimonio;
	}

	/**
	 * @return the eventoInteresse
	 */
	public Integer getEventoInteresse() {
		return this.eventoInteresse;
	}

	/**
	 * @param eventoInteresse
	 *            the eventoInteresse to set
	 */
	public void setEventoInteresse(final Integer eventoInteresse) {
		this.eventoInteresse = eventoInteresse;
	}

	/**
	 * @return the observacao
	 */
	public String getObservacao() {
		return this.observacao;
	}

	/**
	 * @param observacao
	 *            the observacao to set
	 */
	public void setObservacao(final String observacao) {
		this.observacao = observacao;
	}

	/**
	 * @return O campo nomeEvento.
	 */
	public String getNomeEvento() {
		return this.nomeEvento;
	}

	/**
	 * @param nomeEvento
	 *            O novo valor de nomeEvento.
	 */
	public void setNomeEvento(final String nomeEvento) {
		this.nomeEvento = nomeEvento;
	}

	/**
	 * @return O campo nomeInteressado.
	 */
	public String getNomeInteressado() {
		return this.nomeInteressado;
	}

	/**
	 * @param nomeInteressado
	 *            O novo valor de nomeInteressado.
	 */
	public void setNomeInteressado(final String nomeInteressado) {
		this.nomeInteressado = nomeInteressado;
	}

	/**
	 * @return O campo descricaoAmbiente.
	 */
	public String getDescricaoAmbiente() {
		return this.descricaoAmbiente;
	}

	/**
	 * @param descricaoAmbiente
	 *            O novo valor de descricaoAmbiente.
	 */
	public void setDescricaoAmbiente(final String descricaoAmbiente) {
		this.descricaoAmbiente = descricaoAmbiente;
	}

	/**
	 * @return O campo dataEvento.
	 */
	public Date getDataEvento() {
		return this.dataEvento;
	}

	/**
	 * @return O campo dataEvento.
	 */
	public String getDataEventoFormatada() {
		return Formatador.formatarDataPadrao(this.dataEvento);
	}

	/**
	 * @param dataEvento
	 *            O novo valor de dataEvento.
	 */
	public void setDataEvento(final Date dataEvento) {
		this.dataEvento = dataEvento;
	}

	/**
	 * @return O campo horarioInicial.
	 */
	public Date getHorarioInicial() {
		return this.horarioInicial;
	}

	/**
	 * @return O campo horarioInicial.
	 */
	public String getHorarioInicialFormatado() {
		return Formatador.formatarHorario(this.getHorarioInicial());
	}

	/**
	 * @param horarioInicial
	 *            O novo valor de horarioInicial.
	 */
	public void setHorarioInicial(final Date horarioInicial) {
		this.horarioInicial = horarioInicial;
	}

	/**
	 * @return O campo horarioFinal.
	 */
	public Date getHorarioFinal() {
		return this.horarioFinal;
	}

	/**
	 * @return O campo horarioInicial.
	 */
	public String getHorarioFinalFormatado() {
		return Formatador.formatarHorario(this.getHorarioFinal());
	}

	/**
	 * @param horarioFinal
	 *            O novo valor de horarioFinal.
	 */
	public void setHorarioFinal(final Date horarioFinal) {
		this.horarioFinal = horarioFinal;
	}

}
