/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.dao;

import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaClienteInterfaceFacade;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.JSFUtilities;
import br.com.pactosolucoes.enumeradores.ProcessoAjusteGeralEnum;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import controle.arquitetura.security.LoginControle;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.ConfiguracaoReenvioMovParcelaEmpresaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.CartaoTentativaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ClienteMensagem;
import negocio.facade.jdbc.basico.ConfiguracaoReenvioMovParcelaEmpresa;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.LogAjusteGeral;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import org.json.JSONObject;
import servicos.impl.apf.APF;
import servicos.impl.maxiPago.AdquirenteMaxiPagoEnum;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
public class AutorizacaoCobrancaCliente extends SuperEntidade implements AutorizacaoCobrancaClienteInterfaceFacade {

    public AutorizacaoCobrancaCliente(Connection c) throws Exception {
        super(c);
    }

    public AutorizacaoCobrancaCliente() throws Exception {
        super();
    }

    @Override
    public void incluir(AutorizacaoCobrancaClienteVO obj) throws Exception {
        incluir(obj, null);
    }

    public void incluir(AutorizacaoCobrancaClienteVO obj, UsuarioVO usuarioVO) throws Exception {
        verificaValidarVencimentoCartao(obj);
        if (obj.isTipoCartao()) {
            obj.setNumeroCartao(obj.getNumeroCartao().replaceAll(" ", ""));

            if (!UteisValidacao.emptyString(obj.getValidadeCartao()) && obj.getValidadeCartao().length() == 5) {
                //salva no banco no formato mm/yyyy
                obj.setValidadeCartao(obj.getValidadeCartao().replace("/", "/20"));
            }
        }
        AutorizacaoCobrancaClienteVO.validarDados(obj);
        validarMesmoTipo(obj);
        validarClienteTitularCartao(obj);
        validarMesmoCartao(obj);

        obj.verificarDadosParaEnviarAragorn();

        StringBuilder sql = new StringBuilder("INSERT INTO AutorizacaoCobrancaCliente( ");
        sql.append("cliente, tipoAutorizacao, banco, agencia, agenciaDV, contaCorrente, contaCorrenteDV, tipoACobrar, listaObjetosACobrar, conveniocobranca, codigooperacao, cpftitular, ");
        sql.append("identificacaoClienteBanco, nomeTitularCartao, AdquirenteMaxiPago, tokenExterno, ativa, cartaomascaradointerno, assinaturaDigitalBiometria, ");
        sql.append("autorizarDebito, operadoracartao, validadecartao, tokenAragorn, vencimentoFatura, clienteTitularCartao, codigoExterno, cartaoVerificado, ");
        sql.append("idPinBank, ordem, tokenCielo, origem, tokenPagoLivre) ");
        sql.append(" VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(i++, obj.getCliente().getCodigo());
            ps.setInt(i++, obj.getTipoAutorizacao().getId());
            AutorizacaoCobrancaCliente.resolveFKNull(ps, i++, obj.getBanco().getCodigo());
            ps.setInt(i++, obj.getAgencia());
            ps.setString(i++, obj.getAgenciaDV().trim());
            ps.setLong(i++, obj.getContaCorrente());
            ps.setString(i++, obj.getContaCorrenteDV().trim());
            ps.setInt(i++, obj.getTipoACobrar().getId());
            ps.setString(i++, obj.getListaObjetosACobrar());
            AutorizacaoCobrancaCliente.resolveFKNull(ps, i++, obj.getConvenio().getCodigo());
            ps.setString(i++, obj.getCodigoOperacao());
            ps.setString(i++, obj.getCpfTitular());
            ps.setString(i++, obj.getIdentificacaoClienteBanco());
            if (obj.getNomeTitularCartao() == null) {
                ps.setNull(i++, Types.NULL);
            } else {
                ps.setString(i++, Uteis.retirarAcentuacao(obj.getNomeTitularCartao().toUpperCase()));
            }
            ps.setInt(i++, obj.getAdquirenteMaxiPago().getId());
            ps.setString(i++, obj.getTokenExterno());
            ps.setBoolean(i++, obj.isAtiva());
            ps.setString(i++, obj.getCartaoMascarado());
            ps.setString(i++, obj.getAssinaturaDigitalBiometria());
            ps.setBoolean(i++, obj.isAutorizarClienteDebito());

            AutorizacaoCobrancaCliente.resolveEnumNull(ps, i++, obj.getOperadoraCartao(), "id");
            ps.setString(i++, obj.getValidadeCartao());

            if (!obj.isUsarIdVindiPessoa()) {
                obterTokenAragorn(obj);
            }

            ps.setString(i++, obj.getTokenAragorn());
            ps.setInt(i++, obj.getVencimentoFatura());
            ps.setBoolean(i++, obj.isClienteTitularCartao());

            if (obj.isGravarCodigoExterno()) {
                ps.setString(i++, obj.getCodigoExterno());
            } else {
                ps.setString(i++, "");
            }
            ps.setBoolean(i++, obj.isCartaoVerificado());
            ps.setString(i++, obj.getIdPinBank());
            ps.setInt(i++, obj.getOrdem());
            ps.setString(i++, obj.getTokenCielo());
            ps.setInt(i++, obj.getOrigemCobrancaEnum().getCodigo());
            ps.setString(i++, obj.getTokenPagoLivre());

            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());

        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(this.con);
            movParcelaDAO.atualizarParcelaDCC(null, obj.getCliente().getPessoa().getCodigo(), obj.getCliente().getCodigo(), null);
        } catch (Exception ex) {
            Uteis.logarDebug("Erro ao atualizar parcelas DCC no fluxo de incluir autorização de cobrança: " + ex.getMessage());
        } finally {
            movParcelaDAO = null;
        }

        //INCLUIR LOG
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setNomeEntidadeDescricao("Inclusão de Autorização de Cobrança");
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setNomeCampo("INCLUSÃO");
            log.setValorCampoAnterior("");
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao("INCLUSÃO");
            log.setPessoa(obj.getCliente().getPessoa().getCodigo());

            LinkedHashMap<String, Object> orderedMap = new LinkedHashMap<>();; //Necessário usar dessa forma pra respeitar/manter  a ordem desejada dos objetos do log

            orderedMap.put("CÓD.", obj.getCodigo());
            orderedMap.put("TipoAutorizacao", obj.getTipoAutorizacao().getDescricao());
            if (!UteisValidacao.emptyString(obj.getNomeTitularCartao())) {
                orderedMap.put("TitularCartao", obj.getNomeTitularCartao().toUpperCase());
            }
            if (!UteisValidacao.emptyString(obj.getCartaoMascarado())) {
                orderedMap.put("Cartao", obj.getCartaoMascarado());
            }
            if (!UteisValidacao.emptyString(obj.getValidadeCartao())) {
                orderedMap.put("ValidadeCartao", obj.getValidadeCartao());
            }
            if (!UteisValidacao.emptyString(obj.getCpfTitular())) {
                orderedMap.put("CPFTitularCartao", obj.getCpfTitular());
            }
            if (!UteisValidacao.emptyString(obj.getOperadoraCartaoApresentar())) {
                orderedMap.put("Operadora", obj.getOperadoraCartaoApresentar());
            }
            if (!UteisValidacao.emptyString(obj.getTokenExterno())) {
                orderedMap.put("TokenExterno", obj.getTokenExterno());
            }
            if (!UteisValidacao.emptyString(obj.getCodigoExterno())) {
                orderedMap.put("CodigoExterno", obj.getCodigoExterno());
            }
            if (!UteisValidacao.emptyString(obj.getTokenPagoLivre())) {
                orderedMap.put("TokenPagoLivre", obj.getTokenPagoLivre());
            }
            orderedMap.put("ConvênioCobrança", obj.getConvenio().getDescricao() + " | Cód. " + obj.getConvenio().getCodigo());
            orderedMap.put("Ordem", obj.getOrdem());
            orderedMap.put("TipoCobrar", obj.getTipoACobrar().getDescricao());
            orderedMap.put("Situação", obj.isAtiva() ? "Ativa" : "Inativa");
            if (!UteisValidacao.emptyString(obj.getListaObjetosACobrar())) {
                orderedMap.put("TiposProdutosCobrar", obj.getListaObjetosACobrar());
            }
            orderedMap.put("ClienteTitularCartao", obj.isClienteTitularCartao() ? "Sim" : "Não");
            if (UteisValidacao.emptyString(obj.getOrigemCobrancaEnum().getDescricao())) {
                orderedMap.put("Origem", "Desconhecido");
            } else {
                orderedMap.put("Origem", obj.getOrigemCobrancaEnum().getDescricao());
            }
            log.setValorCampoAlterado(UteisJSON.convertLinkedHashMapToJsonString(orderedMap));

            try {
                if (usuarioVO == null) {
                    if (context() != null) { //pegar da sessão
                        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        usuarioVO = loginControle.getUsuarioLogado();
                    } else {
                        //se chegou a entrar aqui é porque é de uma chamada de fora do sistema, não tem sessão para reutilizar,
                        // como vendas online ou totem, app do aluno por exemplo
                        UsuarioVO userAdmin = new UsuarioVO();
                        usuarioVO = userAdmin.getUsuarioAdmin();
                    }
                }
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("Não identificado");
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao tentar identificar o usuário logado no fluxo de incluir autorização de cobrança: " + ex.getMessage());
            }

            Log logDAO;
            try {
                logDAO = new Log(con);
                logDAO.incluirSemCommit(log);
            } catch (Exception ex) {
            } finally {
                logDAO = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao gravar Log no fluxo de incluir autorização de cobrança: " + e.getMessage());
        }

        obj.setNovoObj(false);

        //adicionar a autorização que foi adicionada como primeiro na lista
        processarOrdemAutorizacoes(obj.getCliente(), obj, usuarioVO);
    }

    public void atribuirParcelasDCC(Integer codigoAutorizacao,int pessoaAutorizacao,int clienteAutorizacao,boolean alterarParcelasDCC) throws Exception {

        MovParcela movParcela = new MovParcela(this.con);
        ResultSet rs = movParcela.consultarParcelasDCC(codigoAutorizacao,clienteAutorizacao);
        HashMap<String,RemessaItemVO> parcelasEmDCC = new HashMap<String, RemessaItemVO>();
        while(rs.next()){
           RemessaItemVO item = new RemessaItemVO();
                item.getRemessa().setCodigo(rs.getInt("codigoRemessa"));
                item.getMovParcela().setCodigo(rs.getInt("codigo"));
                item.getMovParcela().setParcelaDCC(rs.getBoolean("parcelaDCC"));
                item.getMovParcela().setSituacao(rs.getString("situacao"));
            parcelasEmDCC.put(item.getMovParcela().getCodigo().toString(),item);
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo,parcelaDCC,situacao FROM MovParcela\n");
        sql.append("  WHERE pessoa = ").append(pessoaAutorizacao);
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rsTodasParcelas = stm.executeQuery()) {

                while (rsTodasParcelas.next()) {

                    Boolean parcelaDCC = rsTodasParcelas.getBoolean("parcelaDCC");
                    Integer codigoParcela = rsTodasParcelas.getInt("codigo");
                    String situacaoParcela = rsTodasParcelas.getString("situacao");

                    if (alterarParcelasDCC || !parcelaDCC) {

                        RemessaItemVO parcelaEmDCC = parcelasEmDCC.get(codigoParcela.toString());

                        if (parcelaEmDCC != null && !UteisValidacao.emptyNumber(parcelaEmDCC.getMovParcela().getCodigo())) {
                                if (parcelaEmDCC.getMovParcela().getSituacao().equals("EA")) {
                                    if (!parcelaEmDCC.getMovParcela().getParcelaDCC()) {
                                        MovParcelaVO parcelaAlterar = parcelaEmDCC.getMovParcela();
                                        parcelaAlterar.setParcelaDCC(true);
                                        movParcela.alterarRegimeDCCParcela(parcelaAlterar.getCodigo(), parcelaAlterar.getParcelaDCC());
                                    }
                                } else if (parcelaEmDCC.getMovParcela().getSituacao().equals("PG") && !UteisValidacao.emptyNumber(parcelaEmDCC.getRemessa().getCodigo())) {
                                    if (!parcelaEmDCC.getMovParcela().getParcelaDCC()) {
                                        MovParcelaVO parcelaAlterar = parcelaEmDCC.getMovParcela();
                                        parcelaAlterar.setParcelaDCC(true);
                                        movParcela.alterarRegimeDCCParcela(parcelaAlterar.getCodigo(), parcelaAlterar.getParcelaDCC());
                                    }
                                }
                            } else if (parcelaEmDCC == null && parcelaDCC && situacaoParcela.equals("EA")) {
                                parcelaDCC = false;
                                movParcela.alterarRegimeDCCParcela(codigoParcela, parcelaDCC);
                            }
                    }

                }
            }
        }
    }

    @Override
    public void alterar(AutorizacaoCobrancaClienteVO obj) throws Exception{
        alterar(obj, true, null);
    }

    public void alterar(AutorizacaoCobrancaClienteVO obj, UsuarioVO usuarioVO) throws Exception{
        alterar(obj, true, usuarioVO);
    }

    public void alterar(AutorizacaoCobrancaClienteVO obj, Boolean atribuirParcelaDCC, UsuarioVO usuarioVO) throws Exception {
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            obj.setValidarBinCartao(obj.getNumeroCartao() != null && !obj.getNumeroCartao().contains("******"));
        }

        AutorizacaoCobrancaClienteVO.validarDados(obj);
        validarMesmoTipo(obj);
        validarClienteTitularCartao(obj);
        validarMesmoCartao(obj);

        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            boolean alterarNumeroCartao = obj.getNumeroCartao() != null && !obj.getNumeroCartao().contains("******");
            if (alterarNumeroCartao) {
                alterarSituacaoAutorizacaoCobranca(false, obj, "Alterou o número do cartão. Será gerada uma nova autorização", usuarioVO);

                obj.setCartaoMascaradoAnterior(((AutorizacaoCobrancaClienteVO) obj.getObjetoVOAntesAlteracao()).getCartaoMascarado());
                obj.setObjetoVOAntesAlteracao(new AutorizacaoCobrancaClienteVO());
                obj.setAlterouNumeroCartao(true);
                obj.setCodigo(0);
                obj.setNovoObj(true);
                obj.setAtiva(true);
                obj.setTokenAragorn("");
                incluir(obj, usuarioVO);
                return;
            }
            if (!UteisValidacao.emptyString(obj.getValidadeCartao()) && obj.getValidadeCartao().length() == 5) {
                obj.setValidadeCartao(obj.getValidadeCartao().replace("/", "/20"));
            }
        }

        obj.verificarDadosParaEnviarAragorn();

        StringBuilder sql = new StringBuilder("UPDATE AutorizacaoCobrancaCliente ");
        sql.append("set cliente=?, tipoAutorizacao=?, banco=?, agencia=?, agenciaDV=?, contaCorrente=?, contaCorrenteDV=?,");
        sql.append("tipoACobrar=?, listaObjetosACobrar=?, conveniocobranca=?, codigooperacao=?, cpftitular=?, identificacaoClienteBanco=?, nomeTitularCartao=?, AdquirenteMaxiPago = ?, tokenExterno = ?, ");
        sql.append("ativa = ?, codigoExterno = ?, cartaomascaradointerno = ?, assinaturaDigitalBiometria = ?, autorizardebito = ?, operadoracartao = ?, validadecartao = ?, tokenAragorn = ?, dataAlteracao = ?, ");
        sql.append("idCardMundipagg = ?, idCardPagarMe = ?, vencimentoFatura = ?, clienteTitularCartao = ?, cartaoVerificado = ?, idPinBank = ?, ordem = ?, tokenCielo = ?, origem = ?, tokenPagoLivre = ? ");
        sql.append("WHERE codigo = ? ");

        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(i++, obj.getCliente().getCodigo());
            ps.setInt(i++, obj.getTipoAutorizacao().getId());
            resolveFKNull(ps, i++, obj.getBanco().getCodigo());
            ps.setInt(i++, obj.getAgencia());
            ps.setString(i++, obj.getAgenciaDV().trim());
            ps.setLong(i++, obj.getContaCorrente());
            ps.setString(i++, obj.getContaCorrenteDV().trim());
            ps.setInt(i++, obj.getTipoACobrar().getId());
            ps.setString(i++, obj.getListaObjetosACobrar());
            resolveFKNull(ps, i++, obj.getConvenio().getCodigo());
            ps.setString(i++, obj.getCodigoOperacao());
            ps.setString(i++, obj.getCpfTitular());
            ps.setString(i++, obj.getIdentificacaoClienteBanco());

            if (obj.getNomeTitularCartao() == null) {
                ps.setNull(i++, Types.NULL);
            } else {
                ps.setString(i++, Uteis.retirarAcentuacao(obj.getNomeTitularCartao().toUpperCase()));
            }

            ps.setInt(i++, obj.getAdquirenteMaxiPago().getId());
            ps.setString(i++, obj.getTokenExterno());
            ps.setBoolean(i++, obj.isAtiva());

            //apagar o código externo caso tenha cartão...
            //no processo de envio para a vindi que deve ser atualizado o codigo externo
            if (!UteisValidacao.emptyString(obj.getCartaoMascarado())) {
                ps.setString(i++, "");
            } else {
                ps.setString(i++, obj.getCodigoExterno());
            }

            ps.setString(i++, obj.getCartaoMascarado());
            ps.setString(i++, obj.getAssinaturaDigitalBiometria());
            ps.setBoolean(i++, obj.isAutorizarClienteDebito());

            AutorizacaoCobrancaCliente.resolveEnumNull(ps, i++, obj.getOperadoraCartao(), "id");
            ps.setString(i++, obj.getValidadeCartao());

            obterTokenAragorn(obj);
            ps.setString(i++, obj.getTokenAragorn());
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));

            //limpar assim na próxima cobrança os dados setão enviados novamente..
            //mundipagg
            ps.setString(i++, "");
            //pagarme
            ps.setString(i++, "");
            ps.setInt(i++, obj.getVencimentoFatura());
            ps.setBoolean(i++, obj.isClienteTitularCartao());
            ps.setBoolean(i++, obj.isCartaoVerificado());
            ps.setString(i++, obj.getIdPinBank());
            ps.setInt(i++, obj.getOrdem());
            ps.setString(i++, obj.getTokenCielo());
            ps.setInt(i++, obj.getOrigemCobrancaEnum().getCodigo());
            ps.setString(i++, obj.getTokenPagoLivre());

            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        }

        if (atribuirParcelaDCC) {
            MovParcela movParcelaDAO;
            try {
                movParcelaDAO = new MovParcela(con);
                movParcelaDAO.atualizarParcelaDCC(null, obj.getCliente().getPessoa().getCodigo(), obj.getCliente().getCodigo(), null);
            } catch (Exception ex) {
                Uteis.logarDebug("Erro ao atualizar parcelas DCC no fluxo de alterar autorização de cobrança: " + ex.getMessage());
            } finally {
                movParcelaDAO = null;
            }
        }


        List lista = new ArrayList();
        if (JSFUtilities.isJSFContext()) {
            lista = obj.gerarLogAlteracaoObjetoVO();
        } else {
            lista = obj.gerarLogAlteracaoObjetoVO(false, usuarioVO);
        }
        Iterator it = lista.iterator();
        while (it.hasNext()) {
            LogVO log = (LogVO) it.next();
            if (log.getValorCampoAnterior().equals(log.getValorCampoAlterado().replace("/20", "/"))) { //não houve alteração na data de vencimento neste caso
                continue;
            }
            log.setOperacao("ALTERAÇÃO");
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setValorCampoAlterado(log.getValorCampoAlterado());
            log.setPessoa(obj.getCliente().getPessoa().getCodigo());


            StringBuilder msg = new StringBuilder();

            if (!obj.getOrigemCobrancaEnum().equals(OrigemCobrancaEnum.NENHUM)) {
                msg.append(" \r\n - ").append(obj.getOrigemCobrancaEnum().getDescricao());
            }

            try {
                if (usuarioVO == null) {
                    if (context() != null) { //pegar da sessão
                        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        usuarioVO = loginControle.getUsuarioLogado();
                    } else {
                        //se chegou a entrar aqui é porque é de uma chamada de fora do sistema, não tem sessão para reutilizar,
                        // como vendas online ou totem, app do aluno por exemplo
                        UsuarioVO userAdmin = new UsuarioVO();
                        usuarioVO = userAdmin.getUsuarioAdmin();
                    }
                }
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("Não identificado");
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao tentar identificar o usuário logado no fluxo de incluir autorização de cobrança: " + ex.getMessage());
            }

            Log logDao = new Log(con);
            logDao.incluirSemCommit(log);
            logDao = null;
        }
    }

    @Override
    public void incluirLogAlteracaoAutorizacaoCobrancaFeitoViaSite(AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO, String origem, boolean inclusao, UsuarioVO usuarioVO) throws Exception{
        Log logDao;
        Usuario usuarioDao;
        try {
            logDao = new Log(con);
            if (usuarioVO == null) {
                usuarioDao = new Usuario(con);
                usuarioVO = usuarioDao.getUsuarioRecorrencia();
            }

            String operacao = inclusao ? "INCLUSÃO" : "ALTERAÇÃO";

            LogVO obj = new LogVO();
            obj.setChavePrimaria(autorizacaoCobrancaClienteVO.getCodigo().toString());
            obj.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            obj.setNomeEntidadeDescricao("AUTORIZACAOCOBRANCACLIENTE");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setNomeCampo("MENSAGEM");

            try {
                if (!inclusao && autorizacaoCobrancaClienteVO.getObjetoVOAntesAlteracao() != null) {
                    obj.setValorCampoAnterior(((AutorizacaoCobrancaVO) autorizacaoCobrancaClienteVO.getObjetoVOAntesAlteracao()).getDescricaoParaLog().toString());
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            obj.setOperacao(operacao);
            StringBuilder msg = new StringBuilder();
            msg.append("Cliente ").append(inclusao ? "incluiu" : "alterou").append(" a autorização de cobrança pelo ").append(origem).append(". \n");
            msg.append(autorizacaoCobrancaClienteVO.getDescricaoParaLog());

            obj.setValorCampoAlterado(msg.toString());
            obj.setDataAlteracao(Calendario.hoje());
            obj.setPessoa(autorizacaoCobrancaClienteVO.getCliente().getPessoa().getCodigo());
            logDao.incluirSemCommit(obj);
        } finally {
            logDao = null;
            usuarioDao = null;
        }
    }

    private void incluirLogAlteracaoAutorizacaoCobrancaRegistroB(AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO) throws Exception {
        LogVO obj = new LogVO();
        Usuario usuarioDao = new Usuario(con);
        UsuarioVO usuarioVO = usuarioDao.getUsuarioRecorrencia();
        usuarioDao = null;
        obj.setChavePrimaria(autorizacaoCobrancaClienteVO.getCodigo().toString());
        obj.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
        obj.setNomeEntidadeDescricao("AUTORIZACAOCOBRANCACLIENTE");
        obj.setResponsavelAlteracao(usuarioVO.getNome());
        obj.setNomeCampo("MENSAGEM");
        obj.setValorCampoAlterado("");
        StringBuilder msg = new StringBuilder();
        obj.setOperacao("ALTERAÇÃO");
        msg.append("Cliente alterou a autorização de cobrança pelo registro B. \n");
        msg.append("CPF titular: ").append(autorizacaoCobrancaClienteVO.getCpfTitular()).append(" \n");
        msg.append("Nome titular: ").append(autorizacaoCobrancaClienteVO.getNomeTitularCartao()).append(" \n");
        msg.append("Tipo autorização cobrança: ").append(autorizacaoCobrancaClienteVO.getTipoAutorizacao().getDescricao()).append(" \n");
        msg.append("Convênio de cobrança: ").append(autorizacaoCobrancaClienteVO.getConvenio().getCodigo()).append(" \n");

        obj.setValorCampoAlterado(msg.toString());
        obj.setDataAlteracao(Calendario.hoje());
        obj.setPessoa(autorizacaoCobrancaClienteVO.getCliente().getPessoa().getCodigo());
        Log logDao = new Log(con);
        logDao.incluirSemCommit(obj);
    }

    private static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AutorizacaoCobrancaClienteVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static AutorizacaoCobrancaClienteVO montarDados(ResultSet ds, int nivelMontarDados, Connection con) throws Exception {
        Cliente clienteFacade;
        Banco bancoFacade;
        ConvenioCobranca convenioFacade;
        AutorizacaoCobrancaClienteVO obj = new AutorizacaoCobrancaClienteVO(false);
        obj.setNovoObj(false);
        try {
            clienteFacade = new Cliente(con);
            bancoFacade = new Banco(con);
            convenioFacade = new ConvenioCobranca(con);
            //
            obj.setCodigo(ds.getInt("codigo"));
            obj.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.valueOf(ds.getInt("tipoautorizacao")));
            obj.setAgencia(ds.getInt("agencia"));
            obj.setAgenciaDV(ds.getString("agenciadv"));
            obj.setContaCorrente(ds.getLong("contacorrente"));
            obj.setContaCorrenteDV(ds.getString("contacorrentedv"));
            obj.setTipoACobrar(TipoObjetosCobrarEnum.valueOf(ds.getInt("tipoacobrar")));
            obj.setListaObjetosACobrar(ds.getString("listaobjetosacobrar"));
            obj.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(ds.getInt("operadoracartao")));
            obj.setIdentificacaoClienteBanco(ds.getString("identificacaoclientebanco"));
            obj.getConvenio().setCodigo(ds.getInt("conveniocobranca"));
            obj.setNomeTitularCartao(ds.getString("nomeTitularCartao"));
            obj.setAdquirenteMaxiPago(AdquirenteMaxiPagoEnum.valueOff(ds.getInt("AdquirenteMaxiPago")));
            obj.setTokenExterno(ds.getString("tokenExterno"));
            obj.setAtiva(ds.getBoolean("ativa"));
            obj.setCodigoExterno(ds.getString("codigoExterno"));
            obj.setIdCardMundiPagg(ds.getString("idCardMundiPagg"));
            obj.setIdCardPagarMe(ds.getString("idCardPagarMe"));
            obj.setAssinaturaDigitalBiometria(ds.getString("assinaturaDigitalBiometria"));
            obj.setAutorizarClienteDebito(ds.getBoolean("autorizarDebito"));

            obj.setValidadeCartao(ds.getString("validadecartao"));
            obj.setCartaoMascarado(ds.getString("cartaomascaradointerno"));
            obj.setNumeroCartao(ds.getString("cartaomascaradointerno"));
            obj.setTokenAragorn(ds.getString("tokenAragorn"));
            obj.setClienteTitularCartao(ds.getBoolean("clienteTitularCartao"));
            obj.setIdPinBank(ds.getString("idPinBank"));
            obj.setOrdem(ds.getInt("ordem"));
            obj.setTokenCielo(ds.getString("tokenCielo"));
            obj.setOrigemCobrancaEnum(OrigemCobrancaEnum.obterPorCodigo(ds.getInt("origem")));
            obj.setTokenPagoLivre(ds.getString("tokenPagoLivre"));

            try {
                obj.setVencimentoFatura(ds.getInt("vencimentoFatura"));
            } catch (Exception ignored) {
            }

            try {
                obj.setCartaoVerificado(ds.getBoolean("cartaoVerificado"));
            } catch (Exception ignored) {
            }


            if(nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS){
                obj.setCliente(clienteFacade.consultarPorChavePrimaria(
                        ds.getInt("cliente"), Uteis.NIVELMONTARDADOS_MINIMOS));
                return obj;
            }

            ClienteVO clienteVO = clienteFacade.consultarPorChavePrimaria(ds.getInt("cliente"), Uteis.NIVELMONTARDADOS_AUTORIZACAO_COBRANCA);
            Integer empresaCliente = clienteVO.getEmpresa().getCodigo();

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS) {
                obj.setConvenio(convenioFacade.consultarPorCodigoEmpresa(ds.getInt("conveniocobranca"), empresaCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
                return obj;
            }

            obj.setCliente(clienteVO);
            obj.setBanco(ds.getInt("banco") != 0 ? bancoFacade.consultarPorChavePrimaria(
                    ds.getInt("banco"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) : new BancoVO());

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
                obj.setConvenio(convenioFacade.consultarPorCodigoEmpresa(ds.getInt("conveniocobranca"), empresaCliente, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                try {
                    obj.setCodigoOperacao(ds.getString("codigooperacao"));
                    obj.setCpfTitular(ds.getString("cpftitular"));
                } catch (Exception ignored) {
                }
                return obj;
            }

            obj.setConvenio(convenioFacade.consultarPorCodigoEmpresa(ds.getInt("conveniocobranca"), empresaCliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            try {
                obj.setCodigoOperacao(ds.getString("codigooperacao"));
                obj.setCpfTitular(ds.getString("cpftitular"));
            } catch (Exception ignored) {
            }

        } finally {
            clienteFacade = null;
            bancoFacade = null;
        }

        return obj;
    }

    @Override
    public AutorizacaoCobrancaClienteVO consultarPorChavePrimaria(final int codigo) throws Exception {
        return consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
    }

    public AutorizacaoCobrancaClienteVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaCliente WHERE codigo = ?";
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql)) {
            sqlConsultar.setInt(1, codigo);
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    throw new ConsistirException(String.format("Dados Não Encontrados ( AutorizacaoCobrancaCliente %s)",
                            new Object[]{
                                    codigo
                            }));
                }
                return (montarDados(tabelaResultado, nivelMontarDados, con));
            }
        }
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> consultarPorCliente(final int codigoCliente, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaCliente WHERE ativa = true and cliente = ? order by tipoautorizacao,ordem";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List<AutorizacaoCobrancaClienteVO> consultarPorClienteAutoCobAtivaAndTipoAuto(final int codigoCliente, boolean ativa, TipoAutorizacaoCobrancaEnum tipoAuto, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaCliente " +
                "WHERE cliente = ? and ativa = ? and tipoautorizacao = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            ps.setBoolean(2, ativa);
            ps.setInt(3, tipoAuto.getId());
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public Integer qtdAutorizacaoAtivaPorCliente(final int codigoCliente) throws Exception {
        String sql = "SELECT count(*) as qtd FROM AutorizacaoCobrancaCliente WHERE ativa = true and cliente = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("qtd");
                } else {
                    return 0;
                }
            }
        }
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> consultarPorClienteTipoAutorizacao(final int codigoCliente, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaCliente WHERE ativa = true AND cliente = ? AND tipoAutorizacao = ? ";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoCliente);
            ps.setInt(2, tipoAutorizacaoCobrancaEnum.getId());
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoaEmpresa(final int codigoPessoa, final int codigoEmpresa, int nivelMontarDados) throws Exception {
        String sql = "SELECT auto.* FROM AutorizacaoCobrancaCliente auto " +
                "inner join Cliente cli on cli.codigo = auto.cliente " +
                "inner join Pessoa pes on pes.codigo = cli.pessoa " +
                "WHERE auto.ativa = true " +
                "   and auto.cliente = cli.codigo " +
                "   and pes.codigo = ? " +
                "   and (SELECT count(*) FROM conveniocobrancaempresa cce WHERE cce.empresa = ? and cce.codigo = auto.conveniocobranca) > 0";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPessoa);
            ps.setInt(2, codigoEmpresa);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoa(final int codigoPessoa, int nivelMontarDados) throws Exception {
        String sql = "SELECT auto.* FROM AutorizacaoCobrancaCliente auto "
                + "inner join Cliente cli on cli.codigo = auto.cliente "
                + "inner join Pessoa pes on pes.codigo = cli.pessoa "
                + "WHERE auto.ativa = true and auto.cliente = cli.codigo and pes.codigo = ? ORDER BY auto.ordem";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPessoa);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoAutorizacao(final int codigoPessoa, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum, int nivelMontarDados) throws Exception{
        String sql = "SELECT auto.* FROM AutorizacaoCobrancaCliente auto "
                + "inner join Cliente cli on cli.codigo = auto.cliente "
                + "inner join Pessoa pes on pes.codigo = cli.pessoa "
                + "WHERE auto.ativa = true and auto.cliente = cli.codigo and pes.codigo = ? AND auto.tipoAutorizacao = ? ORDER BY auto.ordem";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPessoa);
            ps.setInt(2, tipoAutorizacaoCobrancaEnum.getId());
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> consulta(String condicao,int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaCliente ";
        sql += condicao != null ? " WHERE " + condicao : "" + " order by cliente";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public boolean existeOutraAutorizacaoParecidaParaOMesmoTipo(final AutorizacaoCobrancaClienteVO obj) throws Exception {
        return existe(String.format("select auto.codigo from AutorizacaoCobrancaCliente auto inner join ConvenioCobranca conv ON conv.codigo = auto.conveniocobranca "
                        + "where auto.ativa = true and auto.cliente = %s and auto.tipoautorizacao = %s and auto.codigo <> %s and tipoACobrar = %s ",
                obj.getCliente().getCodigo(), obj.getTipoAutorizacao().getId(), obj.getCodigo(),
                obj.getTipoACobrar().getId()), con);
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> obterOutrasAutorizacoesParecidasParaOMesmoTipo(final AutorizacaoCobrancaClienteVO obj) throws Exception {
        String sql = String.format("select * from AutorizacaoCobrancaCliente "
                        + "where ativa = true and cliente = %s and tipoautorizacao = %s and codigo <> %s and tipoACobrar = %s",
                obj.getCliente().getCodigo(), obj.getTipoAutorizacao().getId(), obj.getCodigo(),
                obj.getTipoACobrar().getId());
        return montarDadosConsulta(criarConsulta(sql, con), Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public List<AutorizacaoCobrancaClienteVO> consultarListaCartoesVencidos(EmpresaVO empresa, List<ColaboradorVO> colaboradores, Integer cliente) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT \n");
        sql.append("auto.* \n");
        sql.append("FROM AutorizacaoCobrancaCliente auto \n");
        sql.append("INNER JOIN cliente c on c.codigo = auto.cliente \n");
        sql.append("WHERE auto.ativa = true \n");
        sql.append("AND auto.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sql.append("AND LENGTH(validadecartao) = 7 \n");
        sql.append("AND to_date(auto.validadecartao, 'MM/YYYY') < to_date(to_char(CURRENT_DATE, 'MM/YYYY'), 'MM/YYYY') ");

        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append(" and auto.cliente = ").append(cliente).append(" \n");
        }

        if (empresa != null && empresa.getCodigo() != 0) {
            sql.append(" and c.empresa = ").append(empresa.getCodigo()).append(" ");
        }
        if (colaboradores != null && !colaboradores.isEmpty()) {
            sql.append(" and c.codigo in (select cliente from vinculo where colaborador in (").
                    append(Uteis.splitFromList("codigo", colaboradores, false)).append(")) ");
        }
        sql.append("order by auto.cliente");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        return montarDadosConsulta(rs, Uteis.NIVELMONTARDADOS_MINIMOS, con);
    }

    @Override
    public boolean trocarCartao(final int cliente, final CartaoCreditoTO cartao,
            final String cartaoMascaradoAnterior) throws Exception {
        ClienteMensagem clienteMensagemDAO;
        try {
            clienteMensagemDAO = new ClienteMensagem(con);

            List<AutorizacaoCobrancaClienteVO> autorizacoes = this.consultarPorCliente(cliente, Uteis.NIVELMONTARDADOS_TODOS);
            boolean trocarealizada = false;
            for (AutorizacaoCobrancaClienteVO auto : autorizacoes) {
                if (auto.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                    if (validarBandeiraConvenio(cartao.getBand(), auto.getConvenio().getTipo()))
                        if (auto.getCartaoMascarado().equals(cartaoMascaradoAnterior)) {
                            auto.setObjetoVOAntesAlteracao(auto.getClone(false));
                            auto.setNumeroCartao(cartao.getNumero());
                            auto.setValidadeCartao(cartao.getValidade());
                            auto.setOperadoraCartao(cartao.getBand());
                            alterar(auto);
                            clienteMensagemDAO.excluirClienteMensagemCartaoVencido(cartaoMascaradoAnterior, auto.getCliente().getCodigo());
                            trocarealizada = true;
                        }
                }
            }
            return trocarealizada;
        } finally {
            clienteMensagemDAO = null;
        }
    }

    public AutorizacaoCobrancaClienteVO consultar(Integer codigoCliente, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum)throws Exception{
        String sql = "select * from autorizacaocobrancacliente where ativa = true and cliente = ? and tipoAutorizacao = ?";
        try (PreparedStatement pst = con.prepareStatement(sql)) {
            pst.setInt(1, codigoCliente);
            pst.setInt(2, tipoAutorizacaoCobrancaEnum.getId());
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, con);
                }
            }
        }
        return null;
    }

    public boolean validarBandeiraConvenio(OperadorasExternasAprovaFacilEnum band, TipoConvenioCobrancaEnum tipo) {
        boolean valido = false;
        List<OperadorasExternasAprovaFacilEnum> vet = OperadorasExternasAprovaFacilEnum.operadorasConvenio(tipo);

        for (OperadorasExternasAprovaFacilEnum operadora: vet) {
            if (operadora.equals(band)) {
                return true;
            }
        }
        return valido;
    }

    @Override
    public void clonarAutorizacoesNovaEmpresa(Integer cliente, Integer empresaNova, Integer empresaAntiga, Connection con) throws Exception {
        AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDAO = new AutorizacaoCobrancaCliente(con);
        List<AutorizacaoCobrancaClienteVO> autorizacoes = autorizacaoCobrancaClienteDAO.consultarPorCliente(cliente, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        autorizacaoCobrancaClienteDAO = null;
        transferirAutorizacoesCliente(clientePossuiParcelaEmAberto(cliente, empresaAntiga), autorizacoes, empresaNova, empresaAntiga, con);
    }

    /**
     * Realiza a transferência ou a clonagem das {@link AutorizacaoCobrancaClienteVO} pertencentes a empresaAntiga para a empresa nova.
     * @param clonarAutorizacao Caso seja true, realiza a clonagem da {@link AutorizacaoCobrancaClienteVO}, se false, somente troca o {@link negocio.comuns.financeiro.ConvenioCobrancaVO}
     *                          da {@link AutorizacaoCobrancaClienteVO}
     * @param autorizacoes {@link List} de {@link AutorizacaoCobrancaClienteVO} pertencente ao cliente.
     * @param empresaNova Código da {@link EmpresaVO} para qual o cliente será transferido.
     * @param empresaAntiga Código da {@link EmpresaVO} atual do cliente.
     */
    private void transferirAutorizacoesCliente(Boolean clonarAutorizacao, List<AutorizacaoCobrancaClienteVO> autorizacoes, Integer empresaNova, Integer empresaAntiga, Connection con) throws Exception{
        Map<Integer, List<AutorizacaoCobrancaClienteVO>> autorizacaoPorEmpresa = separarAutorizacaoCobrancaPorEmpresa(autorizacoes);

        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
        List<ConvenioCobrancaVO> conveniosEmpresaNova = convenioCobrancaDAO.consultarPorEmpresa(empresaNova, SituacaoConvenioCobranca.ATIVO, false, false, Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
        convenioCobrancaDAO = null;

        List<AutorizacaoCobrancaClienteVO> autorizacoesEmpresaAntiga = autorizacaoPorEmpresa.get(empresaAntiga);
        if(autorizacoesEmpresaAntiga != null){
            for(AutorizacaoCobrancaClienteVO autorizacao : autorizacoesEmpresaAntiga){
                transferirAutorizacaoCliente(clonarAutorizacao, autorizacao, conveniosEmpresaNova, autorizacaoPorEmpresa, empresaAntiga, empresaNova);
            }
        }
    }

    /**
     * Realiza a transferência/clonagem de uma {@link AutorizacaoCobrancaClienteVO} da empresaAntiga para empresaNova.
     * @param clonarAutorizacao Caso true, realiza a transferência da autorização, se false, realiza a clonagem para a nova empresa.
     * @param autorizacao {@link AutorizacaoCobrancaClienteVO} que sera transferida/clonada.
     * @param conveniosEmpresaNova {@link List} de {@link ConvenioCobrancaVO} cadastrados para a nova empresa.
     * @param autorizacaoPorEmpresa {@link Map} das {@link AutorizacaoCobrancaClienteVO} que o cliente possui dividida por {@link EmpresaVO}
     * @param empresaAntiga Código da {@link EmpresaVO} da empresa que o cliente esta.
     * @param empresaNova Código da {@link EmpresaVO} para qual o cliente vai ser transferido.
     * @throws Exception
     */
    private void transferirAutorizacaoCliente(Boolean clonarAutorizacao, AutorizacaoCobrancaClienteVO autorizacao, List<ConvenioCobrancaVO> conveniosEmpresaNova, Map<Integer, List<AutorizacaoCobrancaClienteVO>> autorizacaoPorEmpresa, Integer empresaAntiga, Integer empresaNova) throws  Exception{
        if(!possuiTipoAutorizacaoCadastrado(autorizacao.getTipoAutorizacao(), autorizacaoPorEmpresa.get(empresaNova))){
            List<ConvenioCobrancaVO> conveniosCompativeis = new ArrayList<ConvenioCobrancaVO>();
            ConvenioCobrancaVO convenioPreferencial = descobrirConveniosCompativeis(autorizacao.getConvenio(), conveniosCompativeis, conveniosEmpresaNova);
            if(convenioPreferencial != null){
                cadastrarAutorizacaoEmpresaNova(clonarAutorizacao, autorizacao, convenioPreferencial);
            }else{
                cadastrarAutorizacaoConvenioCompativel(clonarAutorizacao, autorizacao, conveniosCompativeis);
            }
        }
    }

    /**
     * Caso a {@link AutorizacaoCobrancaClienteVO} seja <code>TipoAutorizacaoCobrancaEnum.CARTAOCREDITO</code>, verifica se existe uma bandeira compativel na lista de {@link ConvenioCobrancaVO} compatíveis.
     * Caso tenha realiza o cadastro da {@link AutorizacaoCobrancaClienteVO}
     * @param clonarAutorizacao Caso true, realiza a transferência da autorização, se false, realiza a clonagem para a nova empresa.
     * @param autorizacao {@link AutorizacaoCobrancaClienteVO} que sera transferida/clonada.
     * @param conveniosCompativeis {@link ConvenioCobrancaVO} que são compatíveis com a {@link AutorizacaoCobrancaClienteVO};
     * @throws Exception
     */
    private void cadastrarAutorizacaoConvenioCompativel(Boolean clonarAutorizacao, AutorizacaoCobrancaClienteVO autorizacao, List<ConvenioCobrancaVO> conveniosCompativeis) throws  Exception {
        if(TipoAutorizacaoCobrancaEnum.getPorTipoConvenioCobraca(autorizacao.getConvenio().getTipo()).equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)){
            for(ConvenioCobrancaVO convenioCompativel : conveniosCompativeis){
                List<OperadorasExternasAprovaFacilEnum> bandeirasConvenioCompativeis = getBandeirasConvenioCobranca(convenioCompativel);
                if(bandeirasConvenioCompativeis.indexOf(autorizacao.getOperadoraCartao()) != -1){
                    cadastrarAutorizacaoEmpresaNova(clonarAutorizacao, autorizacao, convenioCompativel);
                    break;
                }
            }
        }
    }

    /**
     * Retorna as {@link OperadorasExternasAprovaFacilEnum} de um determinado {@link ConvenioCobrancaVO}
     * @param convenio {@link ConvenioCobrancaVO} para qual será consultado as {@link OperadorasExternasAprovaFacilEnum}
     * @return
     */
    private List<OperadorasExternasAprovaFacilEnum> getBandeirasConvenioCobranca(ConvenioCobrancaVO convenio) throws Exception{
        OperadoraCartao operadoraCartaoDAO;
        try {
            operadoraCartaoDAO = new OperadoraCartao(con);

            List<OperadorasExternasAprovaFacilEnum> bandeirasCartao = new ArrayList<OperadorasExternasAprovaFacilEnum>();
            List<OperadorasExternasAprovaFacilEnum> vet = OperadorasExternasAprovaFacilEnum.operadorasConvenio(convenio.getTipo());
            List<OperadoraCartaoVO> lista = operadoraCartaoDAO.consultarPorCodigo(0, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            for (OperadoraCartaoVO operadoraCartaoVO : lista) {
                for (OperadorasExternasAprovaFacilEnum operadora : vet) {
                    if (operadora == operadoraCartaoVO.getCodigoIntegracaoAPF()) {
                        bandeirasCartao.add(operadora);
                        break;
                    }

                }

            }
            return bandeirasCartao;
        } finally {
            operadoraCartaoDAO = null;
        }
    }

    /**
     * Realiza a alteração no banco de dados da {@link AutorizacaoCobrancaClienteVO}.
     * Caso clonarAutorizacao seja true, sera feito um novo cadastro da autorização, caso false, sera alterado o {@link ConvenioCobrancaVO} da autorizacaoCobrancaClienteVO
     * @param clonarAutorizacao true/false
     * @param autorizacao {@link AutorizacaoCobrancaClienteVO} que será manipulada.
     * @param convenio {@link ConvenioCobrancaVO} para qual vai a {@link AutorizacaoCobrancaClienteVO}
     * @throws Exception
     */
    private void cadastrarAutorizacaoEmpresaNova(Boolean clonarAutorizacao, AutorizacaoCobrancaClienteVO autorizacao, ConvenioCobrancaVO convenio) throws Exception{
        autorizacao.setConvenio(convenio);
        autorizacao.setCodigoExterno("");
        autorizacao.setIdCardMundiPagg("");
        autorizacao.setIdCardPagarMe("");
        if(clonarAutorizacao){
            if (autorizacao.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                AragornService aragornService = new AragornService();
                aragornService.povoarAutorizacaoCobrancaVO(autorizacao);
                aragornService = null;
            }
            autorizacao.setCodigo(null);
            autorizacao.setNovoObj(true);
            incluir(autorizacao);
        }else{
            autorizacao.setValidarAutorizacaoCobrancaSemelhante(false);
            alterar(autorizacao, false, null);
        }
    }

    /**
     * Descobre os convênios compatíveis e o convênio preferencial de um determinado {@link ConvenioCobrancaVO}.
     * Convênio preferencial = {@link ConvenioCobrancaVO} que possui o mesmo <code>ConvenioCobrancaVO.tipo</code>
     * Convênio compativel = {@link ConvenioCobrancaVO} que seja compatível com o mesmo <code>TipoAutorizacaoCobrancaEnum</code>
     * Caso se encontre um convênio preferencial, a busca para.
     * @param convenio {@link ConvenioCobrancaVO} base da consulta.
     * @param conveniosCompativeis {@link List} que será adicionado os convênios compatíveis.
     * @param conveniosPesquisar {@link List} de convênio que sera pesquisada.
     * @return Convênio preferencial.
     */
    private ConvenioCobrancaVO descobrirConveniosCompativeis(ConvenioCobrancaVO convenio, List<ConvenioCobrancaVO> conveniosCompativeis, List<ConvenioCobrancaVO> conveniosPesquisar) {
        ConvenioCobrancaVO convenioPreferencial = null;
        for(ConvenioCobrancaVO convenioPesquisa : conveniosPesquisar){
            if(convenioPesquisa.getTipo().equals(convenio.getTipo())){
                convenioPreferencial = convenioPesquisa;
                break;
            }else{
                if(TipoAutorizacaoCobrancaEnum.getPorTipoConvenioCobraca(convenio.getTipo()).equals(TipoAutorizacaoCobrancaEnum.getPorTipoConvenioCobraca(convenioPesquisa.getTipo()))){
                    conveniosCompativeis.add(convenioPesquisa);
                }
            }
        }
        return convenioPreferencial;
    }

    /**
     * Verifica se existe uma {@link AutorizacaoCobrancaClienteVO} do {@link TipoAutorizacaoCobrancaEnum} definido.
     * @param tipoAutorizacao {@link TipoAutorizacaoCobrancaEnum} que sera procurado.
     * @param autorizacoes {@link List} de {@link AutorizacaoCobrancaClienteVO} que serão consultadas.
     * @return true possua alguma {@link AutorizacaoCobrancaClienteVO} do {@link TipoAutorizacaoCobrancaEnum} definido. False caso contrário.
     */
    private Boolean possuiTipoAutorizacaoCadastrado(TipoAutorizacaoCobrancaEnum tipoAutorizacao, List<AutorizacaoCobrancaClienteVO> autorizacoes) {
        Boolean possui = false;
        if(autorizacoes != null && !autorizacoes.isEmpty()){
            for(AutorizacaoCobrancaClienteVO autorizacao : autorizacoes){
                if(autorizacao.getTipoAutorizacao().equals(tipoAutorizacao)){
                    possui = true;
                    break;
                }
            }
        }
        return possui;
    }

    /**
     *Realiza a separação das {@link AutorizacaoCobrancaClienteVO} a parti da {@link EmpresaVO} cadastradas no {@link negocio.comuns.financeiro.ConvenioCobrancaVO}
     * @param autorizacoes {@link List} de {@link AutorizacaoCobrancaClienteVO} que serão separadas por codigo da {@link EmpresaVO}
     * @return Map em que a chave é o codigo da {@link EmpresaVO} e com a lista de {@link AutorizacaoCobrancaClienteVO} que são dela.
     */
    private Map<Integer, List<AutorizacaoCobrancaClienteVO>> separarAutorizacaoCobrancaPorEmpresa(List<AutorizacaoCobrancaClienteVO> autorizacoes) {
        Map<Integer, List<AutorizacaoCobrancaClienteVO>> separacao = new HashMap<Integer, List<AutorizacaoCobrancaClienteVO>>();
        for(AutorizacaoCobrancaClienteVO autorizacao : autorizacoes){
            if(separacao.containsKey(autorizacao.getConvenio().getEmpresa().getCodigo())){
                separacao.get(autorizacao.getConvenio().getEmpresa().getCodigo()).add(autorizacao);
            }else{
                List<AutorizacaoCobrancaClienteVO> list = new ArrayList<AutorizacaoCobrancaClienteVO>();
                list.add(autorizacao);
                separacao.put(autorizacao.getConvenio().getEmpresa().getCodigo(), list);
            }
        }
        return separacao;
    }

    /**
     * Verifica se para o código do {@link negocio.comuns.basico.ClienteVO} definido, existe uma {@link MovParcelaVO} que esta na situação "EA"
     * @param cliente Código do {@link negocio.comuns.basico.ClienteVO} que será verificado.
     * @param empresa Código da {@link EmpresaVO} que pertence a {@link MovParcela}
     * @return true se existe {@link MovParcelaVO} para a situação "EA".
     * @throws Exception
     */
    private Boolean clientePossuiParcelaEmAberto(Integer cliente, Integer empresa) throws  Exception{
        MovParcela movParcelaDAO;
        try {
            movParcelaDAO = new MovParcela(con);
            return movParcelaDAO.existeParcelaEmSituacao(cliente, "EA", empresa);
        } finally {
            movParcelaDAO = null;
        }
    }

    public void alterarOperadoraCartao(AutorizacaoCobrancaClienteVO auto) throws Exception{
        String sql = "UPDATE  autorizacaocobrancacliente SET operadoracartao = ? WHERE codigo = ?";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setInt(1, auto.getOperadoraCartao().getId());
            ps.setInt(2, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarTipoACobrar(AutorizacaoCobrancaClienteVO auto) throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("UPDATE autorizacaocobrancacliente\n");
        sb.append(" SET tipoACobrar = ?\n");
        sb.append(", listaobjetosacobrar = ?\n");
        sb.append(" WHERE codigo = ?");

        try (PreparedStatement ps = getCon().prepareStatement(sb.toString())) {
            int i = 1;
            ps.setInt(i++, auto.getTipoACobrar().getId());
            if (TipoObjetosCobrarEnum.TIPOS_PRODUTOS.equals(auto.getTipoACobrar())) {
                ps.setString(i++, auto.getListaObjetosACobrar());
            } else {
                ps.setString(i++, "");
            }
            ps.setInt(i++, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarConvenioCobranca(AutorizacaoCobrancaClienteVO auto) throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("UPDATE autorizacaocobrancacliente\n");
        sb.append(" SET conveniocobranca = ?\n");
        sb.append(" WHERE codigo = ?");

        try (PreparedStatement ps = getCon().prepareStatement(sb.toString())) {
            int i = 1;
            ps.setInt(i++, auto.getConvenio().getCodigo());
            ps.setInt(i++, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarCodigoExterno(String codigoExternoNovo, Integer convenioCobranca, Integer pessoa, String codigoExternoAnterior) throws Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET codigoExterno = ? WHERE codigo in " +
                "(select au.codigo from autorizacaocobrancacliente au inner join cliente cl on cl.codigo = au.cliente " +
                "where cl.pessoa = ? " +
                "and au.conveniocobranca = ? " +
                "and au.codigoExterno = ?)";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setString(1, codigoExternoNovo);
            ps.setInt(2, pessoa);
            ps.setInt(3, convenioCobranca);
            ps.setString(4, codigoExternoAnterior);
            ps.executeUpdate();
        }
    }

    public void alterarCodigoExterno(AutorizacaoCobrancaClienteVO auto) throws Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET codigoExterno = ? WHERE codigo = ?";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setString(1, auto.getCodigoExterno());
            ps.setInt(2, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarIdPinBank(AutorizacaoCobrancaClienteVO auto) throws Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET idPinBank = ? WHERE codigo = ?";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setString(1, auto.getIdPinBank());
            ps.setInt(2, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoConvenio(Integer pessoa, TipoConvenioCobrancaEnum tipoConvenioCobranca, int nivelMontarDados) throws Exception {
        return consultarPorPessoaTipoConvenio(pessoa, new TipoConvenioCobrancaEnum[]{tipoConvenioCobranca}, false, nivelMontarDados);
    }

    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoCobranca(Integer pessoa, TipoCobrancaEnum[] arrayTipoCobrancaEnum, Boolean somenteAtivas, int nivelMontarDados) throws Exception {
        TipoConvenioCobrancaEnum[] tipos = null;
        if (arrayTipoCobrancaEnum != null) {
            List<TipoConvenioCobrancaEnum> listaTipoConvenio = new ArrayList<>();
            for (TipoCobrancaEnum tipoCobrancaEnum : arrayTipoCobrancaEnum) {
                listaTipoConvenio.addAll(TipoConvenioCobrancaEnum.obterListaTipoCobranca(tipoCobrancaEnum));
            }
            tipos = new TipoConvenioCobrancaEnum[listaTipoConvenio.size()];
            tipos = listaTipoConvenio.toArray(tipos);
        }
        return consultarPorPessoaTipoConvenio(pessoa, tipos, somenteAtivas, nivelMontarDados);
    }

    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoConvenio(Integer pessoa, TipoConvenioCobrancaEnum tipoConvenioCobranca, Boolean somenteAtivas, int nivelMontarDados) throws Exception {
        return consultarPorPessoaTipoConvenio(pessoa, new TipoConvenioCobrancaEnum[]{tipoConvenioCobranca}, somenteAtivas, nivelMontarDados);
    }

    public List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoConvenio(Integer pessoa, TipoConvenioCobrancaEnum[] arrayTiposConvenioCobranca, Boolean somenteAtivas, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("ac.* \n");
        sql.append("from autorizacaocobrancacliente ac \n");
        sql.append("inner join conveniocobranca cc on ac.conveniocobranca = cc.codigo \n");
        sql.append("inner join cliente c on c.codigo = ac.cliente \n");
        sql.append("where c.pessoa = ? \n");
        if (arrayTiposConvenioCobranca != null) {
            String tipos = "";
            for (TipoConvenioCobrancaEnum tipo : arrayTiposConvenioCobranca) {
                tipos += ("," + tipo.getCodigo());
            }
            if (!UteisValidacao.emptyString(tipos)) {
                sql.append("and cc.tipoconvenio in (").append(tipos.replaceFirst(",", "")).append(") \n");
            }
        }

        if (somenteAtivas != null && somenteAtivas) {
            sql.append("and ac.ativa \n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(1, pessoa);
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    @Override
    public JSONObject obterUltimoCartaoAtivoAluno(Integer cliente) throws Exception{
        return obterUltimoCartaoAtivoAluno(cliente, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
    }

    public JSONObject obterUltimoCartaoAtivoAluno(Integer cliente, TipoAutorizacaoCobrancaEnum tipo) throws Exception{
        ResultSet rs = criarConsulta("select validadecartao, cartaomascaradointerno, nometitularcartao, operadoracartao, " +
                "banco, agencia, contacorrente, agenciadv, cpftitular, contacorrentedv, ativa " +
                " from autorizacaocobrancacliente where cliente = " + cliente
                + " and tipoautorizacao = " + tipo.getId()
                + " and ativa = true"
                + " order by codigo desc limit 1", con);
        JSONObject cartao = new JSONObject();
        if(rs.next()){
            if(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.equals(tipo)){
                cartao.put("cartao", rs.getString("cartaomascaradointerno"));
                cartao.put("validade", rs.getString("validadecartao"));
                cartao.put("nome", rs.getString("nometitularcartao"));
                cartao.put("bandeira", OperadorasExternasAprovaFacilEnum.valueOf(rs.getInt("operadoracartao")));
            }else{
                cartao.put("cpf", rs.getString("cpftitular"));
                cartao.put("agenciadv", rs.getString("agenciadv"));
                cartao.put("contacorrentedv", rs.getString("contacorrentedv"));
                cartao.put("contacorrente", rs.getInt("contacorrente"));
                cartao.put("agencia", rs.getInt("agencia"));
                cartao.put("banco", rs.getInt("banco"));
            }
        }
        return cartao;
    }

    public void alterarSituacaoAutorizacaoCobranca(boolean ativa, AutorizacaoCobrancaClienteVO auto, String descricao) throws  Exception {
        alterarSituacaoAutorizacaoCobranca(ativa, auto, descricao, null);
    }

    public void alterarSituacaoAutorizacaoCobranca(boolean ativa, AutorizacaoCobrancaClienteVO auto, String descricao, UsuarioVO usuarioVO) throws Exception {
        String sql = "UPDATE autorizacaocobrancacliente SET ativa = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, ativa);
            ps.setInt(2, auto.getCodigo());
            ps.executeUpdate();
        }

        //INCLUIR LOG
        try {

            LogVO log = new LogVO();
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setNomeEntidadeDescricao("Inclusão de Autorização de Cobrança");
            log.setChavePrimaria(auto.getCodigo().toString());
            log.setNomeCampo(ativa ? "REATIVAÇÃO" : "EXCLUSÃO");
            log.setValorCampoAnterior("");
            log.setDataAlteracao(Calendario.hoje());
            log.setOperacao(ativa ? "REATIVAÇÃO" : "EXCLUSÃO");
            log.setPessoa(auto.getCliente().getPessoa().getCodigo());

            LinkedHashMap<String, Object> orderedMap = new LinkedHashMap<>();
            ; //Necessário usar dessa forma pra respeitar/manter  a ordem desejada dos objetos do log

            orderedMap.put("CÓD.", auto.getCodigo());
            orderedMap.put("TipoAutorizacao", auto.getTipoAutorizacao().getDescricao());
            if (!UteisValidacao.emptyString(auto.getNomeTitularCartao())) {
                orderedMap.put("TitularCartao", auto.getNomeTitularCartao().toUpperCase());
            }
            if (!UteisValidacao.emptyString(auto.getCartaoMascarado())) {
                orderedMap.put("Cartao", auto.getCartaoMascarado());
            }
            if (!UteisValidacao.emptyString(auto.getValidadeCartao())) {
                orderedMap.put("ValidadeCartao", auto.getValidadeCartao());
            }
            if (!UteisValidacao.emptyString(auto.getCpfTitular())) {
                orderedMap.put("CPFTitularCartao", auto.getCpfTitular());
            }
            if (!UteisValidacao.emptyString(auto.getOperadoraCartaoApresentar())) {
                orderedMap.put("Operadora", auto.getOperadoraCartaoApresentar());
            }
            if (!UteisValidacao.emptyString(auto.getTokenExterno())) {
                orderedMap.put("TokenExterno", auto.getTokenExterno());
            }
            if (!UteisValidacao.emptyString(auto.getCodigoExterno())) {
                orderedMap.put("CodigoExterno", auto.getCodigoExterno());
            }
            if (!UteisValidacao.emptyString(auto.getTokenPagoLivre())) {
                orderedMap.put("TokenPagoLivre", auto.getTokenPagoLivre());
            }
            orderedMap.put("ConvênioCobrança", auto.getConvenio().getDescricao() + " | Cód. " + auto.getConvenio().getCodigo());
            orderedMap.put("Ordem", auto.getOrdem());
            orderedMap.put("TipoCobrar", auto.getTipoACobrar().getDescricao());
            orderedMap.put("Situação", auto.isAtiva() ? "Ativa" : "Inativa");
            if (!UteisValidacao.emptyString(auto.getListaObjetosACobrar())) {
                orderedMap.put("TiposProdutosCobrar", auto.getListaObjetosACobrar());
            }
            orderedMap.put("ClienteTitularCartao", auto.isClienteTitularCartao() ? "Sim" : "Não");

            orderedMap.put("Origem", auto.getOrigemCobrancaEnum().getDescricao());
            if (!UteisValidacao.emptyString(descricao)) {
                orderedMap.put("Detalhes Adicionais", descricao);
            }
            log.setValorCampoAlterado(UteisJSON.convertLinkedHashMapToJsonString(orderedMap));

            try {
                if (usuarioVO == null) {
                    if (context() != null) { //pegar da sessão
                        LoginControle loginControle = (LoginControle) context().getExternalContext().getSessionMap().get("LoginControle");
                        usuarioVO = loginControle.getUsuarioLogado();
                    } else {
                        //se chegou a entrar aqui é porque é de uma chamada de fora do sistema, não tem sessão para reutilizar,
                        // como vendas online ou totem, app do aluno por exemplo
                        UsuarioVO userAdmin = new UsuarioVO();
                        usuarioVO = userAdmin.getUsuarioAdmin();
                    }
                }
                log.setUsuarioVO(usuarioVO);
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } catch (Exception ex) {
                log.setResponsavelAlteracao("Não identificado");
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao tentar identificar o usuário logado no fluxo de incluir autorização de cobrança: " + ex.getMessage());
            }

            Log logDAO;
            try {
                logDAO = new Log(con);
                logDAO.incluirSemCommit(log);
            } catch (Exception ex) {
            } finally {
                logDAO = null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("Erro ao gravar Log no fluxo de incluir autorização de cobrança: " + e.getMessage());
        }
    }

    public void marcarRenovadoAutomatico(AutorizacaoCobrancaClienteVO autorizacaoAntiga, AutorizacaoCobrancaClienteVO autorizacaoNova) throws  Exception {
        String sql = "UPDATE autorizacaocobrancacliente SET renovadoAutomaticoDescricao = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, "AUTORIZACAO ANTIGA: " + autorizacaoAntiga.getCodigo());
            ps.setInt(2, autorizacaoNova.getCodigo());
            ps.executeUpdate();
        }

        try {
            StringBuilder log = new StringBuilder();
            log.append("\nRenova Fácil - CIELO:");
            log.append("\nCartão antigo: ").append(APF.getCartaoMascarado(autorizacaoAntiga.getNumeroCartao()));
            log.append("\nCartão novo: ").append(APF.getCartaoMascarado(autorizacaoNova.getNumeroCartao()));
            gerarLogRenovaFacilCielo(autorizacaoNova, log.toString());
        } catch (Exception ex ){
            ex.printStackTrace();
        }
    }

    public void gerarLogRenovaFacilCielo(AutorizacaoCobrancaClienteVO auto, String descricao) {
        try {
            LogVO log = new LogVO();
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setNomeEntidadeDescricao("AUTORIZACAOCOBRANCACLIENTE");
            log.setOperacao("INCLUSÃO");
            log.setNomeCampo("RENOVA-FACIL");
            log.setResponsavelAlteracao("RECORRENCIA");
            log.setChavePrimaria(auto.getCodigo().toString());
            log.setPessoa(auto.getCliente().getPessoa().getCodigo());
            log.setValorCampoAlterado(descricao);
            log.setValorCampoAnterior("");
            Log logEntidade = new Log(con);
            logEntidade.incluirSemCommit(log);
            logEntidade = null;
        } catch (Exception e) {
            Uteis.logar(null, "Erro ao gerarLogRenovaFacilCielo: " +e.getMessage());
        }
    }

    public Integer contarPendenciaCartaoProblema(Integer codigoEmpresa, List<Integer> consultores) throws Exception {
        ResultSet rs = consultarPendenciaClienteCartaoComProblema(codigoEmpresa, consultores, null, true);
        if (rs.next()) {
            return rs.getInt("qtd");
        }
        return 0;
    }

    public ResultSet contarPendenciaCartaoProblemaRS(Integer codigoEmpresa, List<Integer> consultores) throws Exception {
        return consultarPendenciaClienteCartaoComProblema(codigoEmpresa, consultores, null, true);
    }

    public ResultSet consultarPendenciaClienteCartaoComProblema(Integer codigoEmpresa, List<Integer> consultores, ConfPaginacao paginacao, boolean count) throws Exception {
        StringBuilder joinCondicaoColab = new StringBuilder();
        StringBuilder condicaoColab = new StringBuilder();
        if (!UteisValidacao.emptyList(consultores)) {
            joinCondicaoColab.append("LEFT JOIN vinculo v ON a.cliente = v.cliente ");
            condicaoColab.append(" AND v.colaborador in (");
            for (Integer consultor : consultores) {
                condicaoColab.append(consultor).append(",");
            }
            condicaoColab.deleteCharAt(condicaoColab.length() - 1);
            condicaoColab.append(") ");
        }
        StringBuilder sqlStr = new StringBuilder();

        sqlStr.append("SELECT \n");
        if (count) {
            sqlStr.append("count(dw.codigocliente) as qtd \n");
        } else {
            sqlStr.append("dw.codigocliente as cli, \n");
            sqlStr.append("dw.codigocontrato as codContrato, \n");
            sqlStr.append("dw.nomecliente as nome, \n");
            sqlStr.append("dw.nomePlano, \n");
            sqlStr.append("dw.codigopessoa as codPessoa, \n");
            sqlStr.append("dw.matricula as matriculacli, \n");
            sqlStr.append("dw.situacao as situacaoCliente, \n");
            sqlStr.append("dw.datavigenciade as dataInicio, \n");
            sqlStr.append("dw.datavigenciaateajustada as dataFim, \n");
            sqlStr.append("cd.numeroMeses as duracaoContrato, \n");
            sqlStr.append("ct.nomemodalidades, \n");
            sqlStr.append("dw.telefonescliente, \n");
            sqlStr.append("a.cartaomascaradointerno, \n");
            sqlStr.append("a.validadecartao, \n");
            sqlStr.append("dw.telefonescliente, \n");
            sqlStr.append("CASE \n");
            sqlStr.append("WHEN coalesce(a.nometitularcartao, '') = '' THEN 'FALTA TITULAR' \n");
            sqlStr.append("ELSE '' END AS situacaoCartao \n");
        }
        sqlStr.append("FROM autorizacaocobrancacliente a \n");
        sqlStr.append(joinCondicaoColab.toString()).append("\n");
        sqlStr.append("INNER JOIN Situacaoclientesinteticodw dw ON dw.codigocliente = a.cliente \n");
        if (!count) {
            sqlStr.append("LEFT JOIN Contrato ct ON  ct.codigo = dw.codigocontrato \n");
            sqlStr.append("LEFT JOIN ContratoDuracao cd ON cd.contrato = ct.codigo \n");
        }
        sqlStr.append("WHERE a.ativa \n");
        sqlStr.append("and a.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sqlStr.append("and coalesce(a.nometitularcartao, '') = '' \n");
        sqlStr.append("and (dw.situacao = 'AT' or exists (select codigo from movparcela where situacao = 'EA' and pessoa = dw.codigopessoa)) \n");
        sqlStr.append(condicaoColab.toString()).append(" \n");
        if(codigoEmpresa != 0){
            sqlStr.append("and dw.empresacliente = ").append(codigoEmpresa).append(" \n");
        }

        if (!count) {
            if(paginacao != null && paginacao.getOrdernar()){
                sqlStr.append(" ORDER BY ").append(paginacao.getColunaOrdenacao()).append(" ").append(paginacao.getDirecaoOrdenacao());
            }
            if(paginacao != null && paginacao.isExistePaginacao()){
                sqlStr.append(" limit ").append(paginacao.getItensPorPagina())
                        .append(" offset ").append((paginacao.getPaginaAtual() * paginacao.getItensPorPagina()));
            }
        }
        return criarConsulta(sqlStr.toString(), con);
    }

    private void verificaValidarVencimentoCartao(AutorizacaoCobrancaClienteVO obj){
        try {
            if (obj.isProcessoImportacao()) {
                obj.setDesabilitarValidacaoValidade(true);
                return;
            }

            String sql = "select \n" +
                    "validarVencimentoCartaoAutorizacao \n" +
                    "from empresa e\n" +
                    "inner join cliente c on c.empresa = e.codigo\n" +
                    "where c.codigo = " + obj.getCliente().getCodigo();
            ResultSet rs = criarConsulta(sql, con);;
            if (rs.next()) {
                obj.setDesabilitarValidacaoValidade(!rs.getBoolean(1));
            }
        } catch (Exception ignored) {
        }
    }

    public void atualizarAutorizacaoCobrancaClienteCartaoCreditoUsandoConfigReenvioCobrancaAutomatica(Integer codEmpresa, Integer cliente) throws Exception {

        Empresa empresaDAO = new Empresa(con);
        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        empresaDAO = null;

        if (!UteisValidacao.emptyNumber(empresaVO.getCodigo()) && empresaVO.isHabilitarReenvioAutomaticoRemessa()) {
            ConfiguracaoReenvioMovParcelaEmpresa configuracaoReenvioMovParcelaEmpresaDAO = new ConfiguracaoReenvioMovParcelaEmpresa(con);
            List<ConfiguracaoReenvioMovParcelaEmpresaVO> listaConfiguracaoRetentativa = configuracaoReenvioMovParcelaEmpresaDAO.consultarConfiguracaoReenvioMovParcelaEmpresa(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            configuracaoReenvioMovParcelaEmpresaDAO = null;

            if (!UteisValidacao.emptyList(listaConfiguracaoRetentativa)) {
                ConfiguracaoReenvioMovParcelaEmpresaVO confg = listaConfiguracaoRetentativa.get(0);

                if (UteisValidacao.emptyNumber(cliente)) {
                    String sql = "update autorizacaocobrancacliente set conveniocobranca = ? where coalesce(tokenaragorn,'') <> '' and ativa = true and tipoautorizacao = ? and cliente in (select codigo from cliente where empresa = ?)";
                    try (PreparedStatement stm = con.prepareStatement(sql)) {
                        stm.setInt(1, confg.getConvenioCobrancaVO().getCodigo());
                        stm.setInt(2, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId());
                        stm.setInt(3, empresaVO.getCodigo());
                        stm.execute();
                    }
                } else {
                    String sql = "update autorizacaocobrancacliente set conveniocobranca = ? where coalesce(tokenaragorn,'') <> '' and ativa = true and tipoautorizacao = ? and cliente = ?";
                    try (PreparedStatement stm = con.prepareStatement(sql)) {
                        stm.setInt(1, confg.getConvenioCobrancaVO().getCodigo());
                        stm.setInt(2, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId());
                        stm.setInt(3, cliente);
                        stm.execute();
                    }
                }

            }
        }
    }

    public void atualizarAssinaturaDigitalBiometria(Integer codigo, String assinatura) throws SQLException {
        String sql = "UPDATE autorizacaocobrancacliente " +
                "set assinaturaDigitalBiometria = ? " +
                "WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setString(1, assinatura);
            ps.setInt(2, codigo);
            ps.executeUpdate();
        }
    }

    @Override
    public void possuiPermissao(String tipo) throws Exception {
        if(tipo.equals("ALTERAR")){
            alterar(getIdEntidade());
        }else if (tipo.equals("EXCLUIR")){
            excluir(getIdEntidade());
        }else if (tipo.equals("INCLUIR")){
            incluir(getIdEntidade());
        }
        consultar(getIdEntidade());
    }

    @Deprecated
    public List<Integer> consultarCodigosAutorizacaoCobrancaCartao() throws  Exception{
        String sql = "SELECT codigo FROM autorizacaocobrancacliente WHERE numerocartao IS NOT NULL";
        List<Integer> codigos = new ArrayList<Integer>();
        ResultSet rs = con.prepareStatement(sql).executeQuery();
        while(rs.next()){
            codigos.add(rs.getInt("codigo"));
        }
        return codigos;
    }

    @Deprecated
    public void alterarNumeroCartao(Integer codigo, String numeroCartao) throws  Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET numerocartao = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, numeroCartao);
        ps.setInt(2, codigo);
        ps.executeUpdate();
    }

    public void atualizarAutorizacoesProdutosEspecificados(EmpresaVO empresaVO) throws SQLException {
        String sql = "UPDATE autorizacaocobrancacliente \n" +
                "SET listaobjetosacobrar = '" + empresaVO.getTiposProduto() + "'\n" +
                "WHERE codigo IN (\n" +
                "   SELECT acc.codigo FROM autorizacaocobrancacliente acc\n" +
                "   LEFT JOIN cliente cli ON acc.cliente = cli.codigo\n" +
                "   WHERE cli.empresa = " + empresaVO.getCodigo() + "\n" +
                "   AND tipoacobrar = " + TipoObjetosCobrarEnum.TIPOS_PRODUTOS.getId() + ")";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.executeUpdate();
    }

    public boolean necessarioTrocarConvenioCobranca(Integer cliente, Integer empresaDestino) {
        try {
            if (UteisValidacao.emptyNumber(cliente) || UteisValidacao.emptyNumber(empresaDestino)) {
                return false;
            }

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("cc.codigo \n");
            sql.append("from autorizacaocobrancacliente au \n");
            sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
            sql.append("inner join conveniocobranca cc on cc.codigo = au.conveniocobranca \n");
            sql.append("inner join conveniocobrancaempresa ce on ce.conveniocobranca = cc.codigo \n");
            sql.append("where cl.codigo = ").append(cliente).append(" \n");
            sql.append("and ce.empresa = ").append(empresaDestino);
            boolean temConvenioNaEmpresaDestino = SuperFacadeJDBC.existe(sql.toString(), con);
            if (temConvenioNaEmpresaDestino) {
                return false;
            } else {
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
    }

    public void alterarConvenioCobrancaTrocaEmpresa(Integer cliente,
                                                     ConvenioCobrancaVO convenioNovoVO, UsuarioVO usuarioVO, Integer empresaAnterior) throws Exception {

        List<AutorizacaoCobrancaClienteVO> autorizacaoLista = consultarPorCliente(cliente, Uteis.NIVELMONTARDADOS_MINIMOS);
        if (UteisValidacao.emptyList(autorizacaoLista)) {
            return;
        }

        AutorizacaoCobrancaClienteVO autorizacaoClienteVO = autorizacaoLista.get(0);

        ConvenioCobranca convenioDAO = new ConvenioCobranca(con);
        ConvenioCobrancaVO convenioAtualVO = convenioDAO.consultarPorChavePrimaria(autorizacaoClienteVO.getConvenio().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        convenioNovoVO = convenioDAO.consultarPorChavePrimaria(convenioNovoVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        convenioDAO = null;
        MovParcela movparcelaDAO = new MovParcela(con);

        if(movparcelaDAO.existeParcelaEmSituacao(cliente, "EA",  empresaAnterior)){
            autorizacaoClienteVO.setConvenio(convenioNovoVO);
            if (autorizacaoClienteVO.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                AragornService aragornService = new AragornService();
                aragornService.povoarAutorizacaoCobrancaVO(autorizacaoClienteVO);
                aragornService = null;
            }
            incluir(autorizacaoClienteVO);
        } else {
            String update = "update autorizacaocobrancacliente set conveniocobranca = ? where codigo = ?";
            try (PreparedStatement stm = con.prepareStatement(update)) {
                stm.setInt(1, convenioNovoVO.getCodigo());
                stm.setInt(2, autorizacaoClienteVO.getCodigo());
                stm.execute();
            }
            gerarLogAlteracaoAutorizacaoCobranca(convenioAtualVO, convenioNovoVO, usuarioVO, autorizacaoClienteVO.getCodigo(), autorizacaoClienteVO.getCliente().getPessoa().getCodigo());
        }
        movparcelaDAO = null;
    }

    private void gerarLogAlteracaoAutorizacaoCobranca(ConvenioCobrancaVO convenioAtualVO, ConvenioCobrancaVO convenioNovoVO,
                                                      UsuarioVO usuarioVO, Integer autorizacao, Integer pessoa) {
        try {
            if (usuarioVO == null) {
                Usuario usuarioDao = new Usuario(con);
                usuarioVO = usuarioDao.getUsuarioRecorrencia();
                usuarioDao = null;
            }

            LogVO obj = new LogVO();
            obj.setChavePrimaria(autorizacao.toString());
            obj.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            obj.setNomeEntidadeDescricao("AUTORIZACAOCOBRANCACLIENTE");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setNomeCampo("MENSAGEM");
            obj.setValorCampoAlterado("");
            obj.setOperacao("ALTERAÇÃO");

            StringBuilder msg = new StringBuilder();
            msg.append("Cliente trocou de empresa com isso sua autorização de cobrança foi alterada: \n" );
            msg.append("Cod Autorizacao: ").append(autorizacao).append(". \n");
            msg.append("Convênio Anterior: ").append(convenioAtualVO.getDescricao()).append(". \n");
            msg.append("Convênio Novo: ").append(convenioNovoVO.getDescricao()).append(". \n");

            obj.setValorCampoAlterado(msg.toString());
            obj.setDataAlteracao(Calendario.hoje());
            obj.setPessoa(pessoa);
            Log logDAO = new Log(con);
            logDAO.incluirSemCommit(obj);
            logDAO = null;
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logar(null, "Erro ao gerarLogDesfazerCancelamento " + e.getMessage());
        }
    }

    public void processoAjustarAutorizacaoDeCobrancaEmpresaDiferenteEmpresaAluno() throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("cl.empresa as empresaAluno, \n");
        sql.append("ce.empresa as empresaConvenio, \n");
        sql.append("cc.tipoconvenio as tipoConveniocobranca, \n");
        sql.append("au.tipoautorizacao, \n");
        sql.append("au.conveniocobranca as conveniocobrancaatual, \n");
        sql.append("au.codigo as autorizacao, \n");
        sql.append("cl.pessoa \n");
        sql.append("from autorizacaocobrancacliente  au \n");
        sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
        sql.append("inner join conveniocobranca cc on cc.codigo = au.conveniocobranca \n");
        sql.append("inner join conveniocobrancaempresa ce on ce.conveniocobranca = cc.codigo \n");
        sql.append("where au.ativa \n");
        sql.append("and not exists (select codigo from configuracaosistema where justfit) \n");
        sql.append("and ce.empresa <> cl.empresa \n");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);

        Map<Integer, ConvenioCobrancaVO> mapaConvenio = new HashMap<>();
        ConvenioCobranca convenioDAO = new ConvenioCobranca(con);
        List<ConvenioCobrancaVO> listaConvenios = convenioDAO.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        convenioDAO = null;
        for (ConvenioCobrancaVO convenioCobrancaVO : listaConvenios) {
            mapaConvenio.put(convenioCobrancaVO.getCodigo(), convenioCobrancaVO);
        }

        while (rs.next()) {
            try {
                con.setAutoCommit(false);

                Integer empresaAluno = rs.getInt("empresaAluno");
                Integer tipoConveniocobranca = rs.getInt("tipoConveniocobranca");
                Integer tipoautorizacao = rs.getInt("tipoautorizacao");
                Integer autorizacao = rs.getInt("autorizacao");
                Integer pessoa = rs.getInt("pessoa");
                Integer convenioCobrancaAtual = rs.getInt("conveniocobrancaatual");

                List<ConvenioCobrancaVO> listaConvenioEmpresaAluno = new ArrayList<>();
                for (ConvenioCobrancaVO convenioCobrancaVO : listaConvenios) {
                    if (convenioCobrancaVO.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                            convenioCobrancaVO.getEmpresa().getCodigo().equals(empresaAluno) &&
                            convenioCobrancaVO.getTipo().getCodigo() == tipoConveniocobranca) {
                        listaConvenioEmpresaAluno.add(convenioCobrancaVO);
                    }
                }

                //se não encontrou do mesmo tipo de convenio buscar convenio do mesmo tipo de autorizacao
                if (UteisValidacao.emptyList(listaConvenioEmpresaAluno)) {
                    listaConvenioEmpresaAluno = new ArrayList<>();
                    for (ConvenioCobrancaVO convenioCobrancaVO : listaConvenios) {
                        if (convenioCobrancaVO.getSituacao().equals(SituacaoConvenioCobranca.ATIVO) &&
                                convenioCobrancaVO.getEmpresa().getCodigo().equals(empresaAluno) &&
                                convenioCobrancaVO.getTipo().getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.valueOf(tipoautorizacao))) {
                            listaConvenioEmpresaAluno.add(convenioCobrancaVO);
                        }
                    }
                }

                if (!UteisValidacao.emptyList(listaConvenioEmpresaAluno)) {
                    ConvenioCobrancaVO convenioNovoVO = listaConvenioEmpresaAluno.get(0);
                    String update = "update autorizacaocobrancacliente set conveniocobranca = ? where codigo = ?";
                    try (PreparedStatement stm = con.prepareStatement(update)) {
                        stm.setInt(1, convenioNovoVO.getCodigo());
                        stm.setInt(2, autorizacao);
                        stm.execute();
                    }
                    ConvenioCobrancaVO convenioAtualVO = mapaConvenio.get(convenioCobrancaAtual);
                    gerarLogAlteracaoAutorizacaoCobranca(convenioAtualVO, convenioNovoVO, null, autorizacao, pessoa);
                }

                con.commit();
            } catch (Exception ex) {
                Uteis.logar("Erro Processo Ajustar Autorizacao: " + ex.getMessage());
                con.rollback();
                con.setAutoCommit(true);
            } finally {
                con.setAutoCommit(true);
            }
        }
    }

    private void obterTokenAragorn(AutorizacaoCobrancaClienteVO obj) throws Exception {
        if (obj.isProcessoImportacao() || obj.isImportacaoTokenCartao()) {
            return;
        }
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            obj.setTokenAragorn(new AragornService().enviarNazg(new NazgDTO(obj)).getToken());
        }
    }

    public void alterarIdCardMundiPagg(AutorizacaoCobrancaClienteVO auto) throws Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET idCardMundiPagg = ? WHERE codigo = ?";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setString(1, auto.getIdCardMundiPagg());
            ps.setInt(2, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public void alterarIdCardPagarMe(AutorizacaoCobrancaClienteVO auto) throws Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET idCardPagarMe = ? WHERE codigo = ?";
        try (PreparedStatement ps = getCon().prepareStatement(sql)) {
            ps.setString(1, auto.getIdCardPagarMe());
            ps.setInt(2, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public boolean clienteTemAutorizacaoCobrancaAtiva(Integer cliente) throws Exception {
        String sql = "select exists(select codigo from autorizacaocobrancacliente where ativa and cliente = ?) as existe";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, cliente);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return rs.getBoolean(1);
                } else {
                    return false;
                }
            }
        }
    }

    public List<String> obterTokensAragornParaRemessa(List<Integer> listaPessoas) throws Exception {

        StringBuilder codigos = new StringBuilder();

        boolean primeiro = true;
        for (Integer pessoa : listaPessoas) {
            if (primeiro) {
                primeiro = false;
            } else {
                codigos.append(",");
            }
            codigos.append(pessoa);
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("ac.tokenaragorn as token \n");
        sql.append("from autorizacaocobrancacolaborador ac \n");
        sql.append("inner join colaborador co on co.codigo = ac.colaborador \n");
        sql.append("where ac.ativa \n");
        sql.append("and ac.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sql.append("and co.pessoa in (").append(codigos).append(") \n");
        sql.append("union \n");
        sql.append("select  \n");
        sql.append("ac.tokenaragorn as token \n");
        sql.append("from autorizacaocobrancacliente ac \n");
        sql.append("inner join cliente cl on cl.codigo = ac.cliente \n");
        sql.append("where ac.ativa  \n");
        sql.append("and ac.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sql.append("and cl.pessoa in (").append(codigos).append(") \n");

        List<String> listaToken = new ArrayList<>();
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    String tokenAragorn = rs.getString("token");
                    if (!UteisValidacao.emptyString(tokenAragorn)) {
                        listaToken.add(tokenAragorn);
                    }
                }
            }
        }
        return listaToken;
    }

    public void validarClienteTitularCartao(AutorizacaoCobrancaVO obj) throws Exception {
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                (!UteisValidacao.emptyString(obj.getNumeroCartao()) || !UteisValidacao.emptyString(obj.getCartaoMascarado())) &&
                obj.isClienteTitularCartao() && obj.isValidarClienteTitularCartao()) {

            String numeroCartaoMask = "";
            if (!UteisValidacao.emptyString(obj.getNumeroCartao())) {
                numeroCartaoMask = obj.getNumeroCartao();
            }
            if (UteisValidacao.emptyString(numeroCartaoMask) && !UteisValidacao.emptyString(obj.getCartaoMascarado())) {
                numeroCartaoMask = obj.getCartaoMascarado();
            }

            if (!numeroCartaoMask.contains("***")) {
                numeroCartaoMask = APF.getCartaoMascarado(numeroCartaoMask.replaceAll(" ", ""));
            }

            if (UteisValidacao.emptyString(numeroCartaoMask) || !numeroCartaoMask.contains("***")) {
                return;
            }

            String validade = obj.getValidadeCartao();
            if (!UteisValidacao.emptyString(validade) && validade.length() == 5) {
                //salva no banco no formato mm/yyyy
                validade = validade.replace("/", "/20");
            }

            //validar se o cartão já tem titular marcado em algum aluno
            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("p.nome \n");
            sql.append("from autorizacaocobrancacliente au \n");
            sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
            sql.append("inner join pessoa p on p.codigo = cl.pessoa \n");
            sql.append("where au.ativa \n");
            sql.append("and au.clienteTitularCartao \n");
            sql.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
            sql.append("and au.cartaomascaradointerno = '").append(numeroCartaoMask).append("' \n");
            sql.append("and au.validadecartao = '").append(validade).append("' \n");
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql.append("and au.codigo <> ").append(obj.getCodigo());
            }

            try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                try (ResultSet rs = ps.executeQuery()) {
                    if (rs.next()) {
                        throw new Exception("O(a) cliente \"" + rs.getString("nome") + "\" já está com esse cartão marcado como titular.");
                    }
                }
            }
            //validar se o cartão já tem titular marcado em algum colaborador
            StringBuilder sql2 = new StringBuilder();
            sql2.append("select  \n");
            sql2.append("p.nome \n");
            sql2.append("from autorizacaocobrancacolaborador auc  \n");
            sql2.append("inner join colaborador col on col.codigo = auc.colaborador \n");
            sql2.append("inner join pessoa p on p.codigo = col.pessoa \n");
            sql2.append("where auc.ativa \n");
            sql2.append("and auc.clienteTitularCartao \n");
            sql2.append("and auc.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
            sql2.append("and auc.cartaomascaradointerno = '").append(numeroCartaoMask).append("' \n");
            sql2.append("and auc.validadecartao = '").append(validade).append("' \n");
            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql2.append("and auc.codigo <> ").append(obj.getCodigo());
            }

            try (PreparedStatement ps2 = con.prepareStatement(sql2.toString())) {
                try (ResultSet rs = ps2.executeQuery()) {
                    if (rs.next()) {
                        throw new Exception("O(a) colaborador(a) \"" + rs.getString("nome") + "\" já está com esse cartão marcado como titular.");
                    }
                }
            }
        }
    }


    public String cartaoMascaradoPessoa(Integer pessoa) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" select cartaomascaradointerno from autorizacaocobrancacliente a");
        sql.append(" inner join cliente cli on cli.codigo = a.cliente ");
        sql.append(" where cli.pessoa = ").append(pessoa);
        sql.append(" and tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId());
        sql.append(" and ativa = true ");
        sql.append(" order by a.codigo desc limit 1");
        ResultSet rs = criarConsulta(sql.toString(), con);
        return rs.next() ? rs.getString("cartaomascaradointerno") : "";
    }

    public List<AutorizacaoCobrancaClienteVO> consultarProcessoAlterarTipoACobrar(Integer empresa, Integer convenioCobranca,
                                                                                  TipoObjetosCobrarEnum tipoObjetosCobrarEnumDiferente,
                                                                                  int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("au.* \n");
        sql.append("from autorizacaocobrancacliente au \n");
        sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
        sql.append("where au.ativa \n");
        if (!UteisValidacao.emptyNumber(empresa)) {
            sql.append("and cl.empresa = ").append(empresa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(convenioCobranca)) {
            sql.append("and au.conveniocobranca = ").append(convenioCobranca).append(" \n");
        }
        if (tipoObjetosCobrarEnumDiferente != null) {
            sql.append("and au.tipoacobrar <> ").append(tipoObjetosCobrarEnumDiferente.getId()).append(" \n");
        }
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public StringBuilder processoAlterarTipoACobrar(List<AutorizacaoCobrancaClienteVO> lista,
                                             TipoObjetosCobrarEnum tipoObjetosCobrarEnum,
                                             UsuarioVO usuarioVO) throws Exception {
        LogAjusteGeral logAjusteGeralDAO = null;
        try {
            logAjusteGeralDAO = new LogAjusteGeral(con);

            if (tipoObjetosCobrarEnum == null || tipoObjetosCobrarEnum.equals(TipoObjetosCobrarEnum.NENHUM)) {
                throw new Exception("Informe o TipoObjetosCobrarEnum");
            }

            if (usuarioVO == null || UteisValidacao.emptyNumber(usuarioVO.getCodigo())) {
                throw new Exception("Informe o usuário");
            }

            Integer sucesso = 0;
            Integer erro = 0;
            for (AutorizacaoCobrancaClienteVO autoVO : lista) {
                String msg = "";
                boolean sucessoBoo = false;
                TipoObjetosCobrarEnum tipoObjetosCobrarEnum_ANTERIOR = null;
                try {
                    con.setAutoCommit(false);

                    tipoObjetosCobrarEnum_ANTERIOR = autoVO.getTipoACobrar();
                    autoVO.setTipoACobrar(tipoObjetosCobrarEnum);
                    alterarTipoACobrar(autoVO);
                    registrarLog(autoVO.getCodigo(), autoVO.getCliente().getPessoa().getCodigo(), "TipoACobrar - Processo ConfiguracaoSistema",
                            (tipoObjetosCobrarEnum_ANTERIOR.getId() + " - " + tipoObjetosCobrarEnum_ANTERIOR.getDescricao()),
                            (tipoObjetosCobrarEnum.getId() + " - " + tipoObjetosCobrarEnum.getDescricao()), usuarioVO);
                    ++sucesso;
                    sucessoBoo = true;
                    msg = ("Autorizacao " + autoVO.getCodigo() + " | Anterior: " + tipoObjetosCobrarEnum_ANTERIOR.getId() + " | Novo: " + tipoObjetosCobrarEnum.getId());

                    con.commit();
                } catch (Exception ex) {
                    msg = ex.getMessage();
                    ex.printStackTrace();
                    sucessoBoo = false;
                    ++erro;
                    con.rollback();
                } finally {
                    con.setAutoCommit(true);
                    try {
                        JSONObject json = new JSONObject();
                        json.put("autorizacaoCliente", autoVO.getCodigo());
                        json.put("cliente", autoVO.getCliente().getCodigo());
                        json.put("sucesso", sucessoBoo);
                        json.put("msg", msg);
                        logAjusteGeralDAO.incluir(Calendario.hoje(), usuarioVO.getNome(), usuarioVO.getUserOamd(),
                                ProcessoAjusteGeralEnum.ALTERAR_TIPO_A_COBRAR, json.toString());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            StringBuilder msg = new StringBuilder();
            msg.append("Total: ").append(lista.size()).append(" autorizações.<br/>");
            msg.append("Falha: ").append(erro).append(" autorizações.<br/>");
            msg.append("Sucesso: ").append(sucesso).append(" autorizações.<br/>");
            return msg;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            logAjusteGeralDAO = null;
        }
    }

    public void registrarLog(Integer chavePrimaria, Integer pessoa, String nomeCampo, String valorCampoAnterior,
                              String valorCampoAlterado, UsuarioVO usuarioVO) throws Exception {
        Log logDAO = null;
        try {
            logDAO = new Log(this.con);
            //criar log
            LogVO obj = new LogVO();
            obj.setChavePrimaria(chavePrimaria.toString());
            if (UteisValidacao.emptyNumber(pessoa)) {
                obj.setPessoa(0);
            } else {
                obj.setPessoa(pessoa);
            }
            obj.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            obj.setNomeEntidadeDescricao("AUTORIZACAOCOBRANCACLIENTE");
            obj.setOperacao("ALTERACAO");
            obj.setResponsavelAlteracao(usuarioVO.getNome());
            obj.setUserOAMD(usuarioVO.getUserOamd());
            obj.setDataAlteracao(Calendario.hoje());
            obj.setNomeCampo(nomeCampo);
            obj.setValorCampoAnterior(valorCampoAnterior);
            obj.setValorCampoAlterado(valorCampoAlterado);
            logDAO.incluirSemCommit(obj);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            logDAO = null;
        }
    }

    public AutorizacaoCobrancaClienteVO obterUltimaAutorizacao(Integer cliente, Integer pessoa,
                                                               TipoAutorizacaoCobrancaEnum tipoAutorizacao, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("au.* \n");
        sql.append("from autorizacaocobrancacliente au \n");
        sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
        sql.append("where au.ativa \n");
        if (tipoAutorizacao != null) {
            sql.append("and au.tipoautorizacao = ").append(tipoAutorizacao.getId()).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append("and au.cliente = ").append(cliente).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and cl.pessoa = ").append(pessoa).append(" \n");
        }
        sql.append("order by au.codigo desc limit 1");
        try (PreparedStatement sqlConsultar = con.prepareStatement(sql.toString())) {
            try (ResultSet tabelaResultado = sqlConsultar.executeQuery()) {
                if (!tabelaResultado.next()) {
                    return null;
                }
                return montarDados(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public boolean gerarAutorizacaoCobrancaBoleto(Integer empresa, Integer pessoa, Integer cliente) throws Exception {
        Empresa empresaDAO = null;
        Cliente clienteDAO = null;
        AutorizacaoCobrancaCliente autoDAO = null;
        try {
            empresaDAO = new Empresa(con);

            EmpresaVO emp = empresaDAO.consultarPorChavePrimaria(empresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (emp.isPermitirMaillingGerarAutorizacaoCobrancaBoleto() && emp.getConvenioBoletoPadrao() != null &&
                    emp.getConvenioBoletoPadrao().getCodigo() != null &&
                    !UteisValidacao.emptyNumber(emp.getConvenioBoletoPadrao().getCodigo())) {

                autoDAO = new AutorizacaoCobrancaCliente(con);
                clienteDAO = new Cliente(con);

                AutorizacaoCobrancaClienteVO autoVO = new AutorizacaoCobrancaClienteVO();
                ClienteVO clienteVO = null;
                if (!UteisValidacao.emptyNumber(cliente)) {
                    clienteVO = clienteDAO.consultarPorChavePrimaria(cliente, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                }
                if (!UteisValidacao.emptyNumber(pessoa)) {
                    clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_CONSULTA_WS);
                }
                if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    throw new Exception("Cliente não identificado");
                }

                autoVO.setCliente(clienteVO);
                autoVO.setConvenio(emp.getConvenioBoletoPadrao());
                autoVO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
                autoVO.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);
                autoVO.setValidarAutorizacaoCobrancaSemelhante(false);
                autoDAO.incluir(autoVO);
                return true;
            }
            return false;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            empresaDAO = null;
            clienteDAO = null;
            autoDAO = null;
        }
    }

    public void clonarAutorizacaoCobrancaParaOutroCliente(Integer codigoAutorizacaoConsultar, Integer clienteReceber){

        StringBuilder sql = new StringBuilder("INSERT INTO AutorizacaoCobrancaCliente( ");
        sql.append(" cliente, tipoautorizacao, numerocartao, validadecartao, banco, agencia, agenciadv, contacorrente, contacorrentedv, tipoacobrar,");
        sql.append(" listaobjetosacobrar, conveniocobranca, operadoracartao, codigooperacao, cpftitular, identificacaoclientebanco, nometitularcartao, cvv,");
        sql.append(" adquirentemaxipago, tokenexterno, ativa, renovadoautomatico, codigoexterno, renovadoautomaticodescricao, cartaomascaradointerno, ");
        sql.append(" assinaturadigitalbiometria, autorizardebito, tokenaragorn, dataregistro, dataalteracao, idcardmundipagg, idcardpagarme, vencimentofatura, clientetitularcartao, cartaoVerificado, origem) \n");
        sql.append(" select " + clienteReceber+", tipoautorizacao, numerocartao, validadecartao, banco, agencia, agenciadv, contacorrente, contacorrentedv, tipoacobrar, ");
        sql.append(" listaobjetosacobrar, conveniocobranca, operadoracartao, codigooperacao, cpftitular, identificacaoclientebanco, nometitularcartao, cvv, ");
        sql.append(" adquirentemaxipago, tokenexterno, ativa, renovadoautomatico, codigoexterno, renovadoautomaticodescricao, cartaomascaradointerno, ");
        sql.append(" assinaturadigitalbiometria, autorizardebito, tokenaragorn, dataregistro, dataalteracao, idcardmundipagg, idcardpagarme, vencimentofatura, ");
        sql.append("clientetitularcartao, cartaoVerificado, origem from autorizacaocobrancacliente ");
        sql.append("where codigo = ");
        sql.append(codigoAutorizacaoConsultar);

        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.execute();
        }catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public AutorizacaoCobrancaClienteVO obterAutorizacaoMaisRecente(TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum,
                                                                    Integer cliente, Integer pessoa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("au.* \n");
        sql.append("from autorizacaocobrancacliente au \n");
        sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
        sql.append("where au.ativa \n");
        if (tipoAutorizacaoCobrancaEnum != null) {
            sql.append("and au.tipoautorizacao = ").append(tipoAutorizacaoCobrancaEnum.getId()).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and cl.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append("and cl.codigo = ").append(cliente).append(" \n");
        }
        sql.append("order by au.codigo desc  \n");
        sql.append("limit 1 \n");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
                return null;
            }
        }
    }

    public void alterarSomenteCartaoVerificado(AutorizacaoCobrancaClienteVO autoVO) throws Exception {
        String sql = "UPDATE autorizacaocobrancacliente SET cartaoverificado = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, autoVO.isCartaoVerificado());
            ps.setInt(2, autoVO.getCodigo());
            ps.executeUpdate();
        }
    }

    public void processarCartaoVerificado(TransacaoVO transacaoVO) {
        processarCartaoVerificadoGeral(transacaoVO, null);
    }

    public void processarCartaoVerificado(RemessaItemVO remessaItemVO) {
        processarCartaoVerificadoGeral(null, remessaItemVO);
    }

    private void processarCartaoVerificadoGeral(TransacaoVO transacaoVO, RemessaItemVO remessaItemVO) {
        try {
            String tokenAragorn = "";
            String cartaoMascarado = "";
            boolean cartaoVerificado = false;
            boolean processar = false;


            if (transacaoVO != null) {
                tokenAragorn = transacaoVO.getTokenAragorn();
                cartaoMascarado = transacaoVO.getCartaoMascarado();

                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) ||
                        (!transacaoVO.getTipo().equals(TipoTransacaoEnum.VINDI) && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA))) {
                    cartaoVerificado = true;
                    processar = true;
                } else if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.NAO_APROVADA) || transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO)) {
                    CodigoRetornoPactoEnum codigoRetornoPactoEnum = CartaoTentativaVO.obterCodigoRetornoPacto(transacaoVO, transacaoVO.getConvenioCobrancaVO().getTipo(), transacaoVO.getCodigoRetornoGestaoTransacao());
                    if (codigoRetornoPactoEnum != null && codigoRetornoPactoEnum.isIrreversivel()) {
                        processar = true;
                    }
                }
            } else if (remessaItemVO != null) {
                tokenAragorn = remessaItemVO.getValorTokenAragorn();
                cartaoMascarado = remessaItemVO.getValorCartaoMascaradoOuAgenciaConta();

                if (remessaItemVO.getMovPagamento() != null && !UteisValidacao.emptyNumber(remessaItemVO.getMovPagamento().getCodigo())) {
                    cartaoVerificado = true;
                    processar = true;
                } else {
                    CodigoRetornoPactoEnum codigoRetornoPactoEnum = CartaoTentativaVO.obterCodigoRetornoPacto(remessaItemVO, remessaItemVO.getRemessa().getConvenioCobranca().getTipo(), remessaItemVO.getCodigoStatus());
                    if (codigoRetornoPactoEnum != null && codigoRetornoPactoEnum.isIrreversivel()) {
                        processar = true;
                    }
                }
            }

            if (!processar || UteisValidacao.emptyString(tokenAragorn) || UteisValidacao.emptyString(cartaoMascarado)) {
                return;
            }

            //transacao
            if (transacaoVO != null && !UteisValidacao.emptyNumber(transacaoVO.getCodigo())) {
                //autorização cobranda de cliente
                StringBuilder sql = new StringBuilder();
                sql.append("update autorizacaocobrancacliente set cartaoverificado = ? where codigo in ( \n");
                sql.append("select \n");
                sql.append("aut.codigo \n");
                sql.append("from transacao t \n");
                sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
                sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
                sql.append("inner join cliente cl on cl.pessoa = mp.pessoa \n");
                sql.append("inner join autorizacaocobrancacliente aut on aut.cliente = cl.codigo and aut.ativa \n");
                sql.append("where t.tokenaragorn = ? \n");
                sql.append("and t.codigo = ? \n");
                sql.append("and aut.cartaomascaradointerno = ?) \n");
                try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                    ps.setBoolean(1, cartaoVerificado);
                    ps.setString(2, tokenAragorn);
                    ps.setInt(3, transacaoVO.getCodigo());
                    ps.setString(4, cartaoMascarado);
                    ps.executeUpdate();
                }

                //transacao
                //autorização cobranda de colaborador
                sql = new StringBuilder();
                sql.append("update autorizacaocobrancacolaborador set cartaoverificado = ? where codigo in ( \n");
                sql.append("select \n");
                sql.append("aut.codigo \n");
                sql.append("from transacao t \n");
                sql.append("inner join transacaomovparcela tm on tm.transacao = t.codigo \n");
                sql.append("inner join movparcela mp on mp.codigo = tm.movparcela \n");
                sql.append("inner join colaborador co on co.pessoa = mp.pessoa \n");
                sql.append("inner join autorizacaocobrancacolaborador aut on aut.colaborador = co.codigo and aut.ativa \n");
                sql.append("where t.tokenaragorn = ? \n");
                sql.append("and t.codigo = ? \n");
                sql.append("and aut.cartaomascaradointerno = ?) \n");
                try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                    ps.setBoolean(1, cartaoVerificado);
                    ps.setString(2, tokenAragorn);
                    ps.setInt(3, transacaoVO.getCodigo());
                    ps.setString(4, cartaoMascarado);
                    ps.executeUpdate();
                }
            }


            //remessaitem
            if (remessaItemVO != null && !UteisValidacao.emptyNumber(remessaItemVO.getCodigo())) {
                String tipoRemessa = TipoRemessaEnum.EDI_CIELO.getId() + "," + TipoRemessaEnum.GET_NET.getId() + "," + TipoRemessaEnum.DCC_BIN.getId();

                //autorização cobranda de cliente
                StringBuilder sql = new StringBuilder();
                sql.append("update autorizacaocobrancacliente set cartaoverificado = ? where codigo in ( \n");
                sql.append("select \n");
                sql.append("distinct(aut.codigo) \n");
                sql.append("from remessaitem ri \n");
                sql.append("inner join remessa re on re.codigo = ri.remessa and re.tipo in (").append(tipoRemessa).append(") \n");
                sql.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
                sql.append("inner join movparcela mp on (mp.codigo = rim.movparcela or mp.codigo = ri.movparcela) \n");
                sql.append("inner join cliente cl on cl.pessoa = mp.pessoa \n");
                sql.append("inner join autorizacaocobrancacliente aut on aut.cliente = cl.codigo and aut.ativa \n");
                sql.append("where split_part(split_part(split_part(ri.props, 'TokenAragorn=', 2),',',1),'}',1) = ? \n");
                sql.append("and ri.codigo = ? \n");
                sql.append("and aut.cartaomascaradointerno = ?) \n");
                try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                    ps.setBoolean(1, cartaoVerificado);
                    ps.setString(2, tokenAragorn);
                    ps.setInt(3, remessaItemVO.getCodigo());
                    ps.setString(4, cartaoMascarado);
                    ps.executeUpdate();
                }


                //remessaitem
                //autorização cobranda de colaborador
                sql = new StringBuilder();
                sql.append("update autorizacaocobrancacolaborador set cartaoverificado = ? where codigo in ( \n");
                sql.append("select \n");
                sql.append("distinct(aut.codigo) \n");
                sql.append("from remessaitem ri \n");
                sql.append("inner join remessa re on re.codigo = ri.remessa and re.tipo in (").append(tipoRemessa).append(") \n");
                sql.append("left join remessaitemmovparcela rim on rim.remessaitem = ri.codigo \n");
                sql.append("inner join movparcela mp on (mp.codigo = rim.movparcela or mp.codigo = ri.movparcela) \n");
                sql.append("inner join colaborador co on co.pessoa = mp.pessoa \n");
                sql.append("inner join autorizacaocobrancacolaborador aut on aut.colaborador = co.codigo and aut.ativa \n");
                sql.append("where split_part(split_part(split_part(ri.props, 'TokenAragorn=', 2),',',1),'}',1) = ? \n");
                sql.append("and ri.codigo = ?  \n");
                sql.append("and aut.cartaomascaradointerno = ?) \n");
                try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
                    ps.setBoolean(1, cartaoVerificado);
                    ps.setString(2, tokenAragorn);
                    ps.setInt(3, remessaItemVO.getCodigo());
                    ps.setString(4, cartaoMascarado);
                    ps.executeUpdate();
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void validarMesmoTipo(AutorizacaoCobrancaClienteVO obj) throws Exception {
        boolean utilizaRetentativa = false;
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            Empresa empDAO;
            try {
                empDAO = new Empresa(this.con);
                EmpresaVO empresaVO = empDAO.consultarPorChavePrimaria(obj.getCliente().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                utilizaRetentativa = empresaVO.isHabilitarReenvioAutomaticoRemessa();
            } finally {
                empDAO = null;
            }
        }

        if (obj.isValidarAutorizacaoCobrancaSemelhante() &&
                !obj.getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                existeOutraAutorizacaoParecidaParaOMesmoTipo(obj) && !utilizaRetentativa) {
            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                    !obj.getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {
                throw new ConsistirException("Já existe uma Autorização do mesmo Tipo com os dados informados. Para utilizar múltiplos cartões é necessário ter um convênio de cobrança ativo do tipo ONLINE.");
            } else {
                throw new ConsistirException("Já existe uma Autorização do mesmo Tipo com os dados informados (Autorização Cobrança)");
            }
        }
    }

    public void validarMesmoCartao(AutorizacaoCobrancaVO obj) throws Exception {
        if (obj.isValidarQtdCartoes() &&
                obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

            //validar quantidade de cartões que o cliente já tem
            // máximo de 3
            validarQuantidadeCartoesCadastrados(((AutorizacaoCobrancaClienteVO) obj).getCliente().getCodigo(), obj);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("auto.codigo \n");
            sql.append("from AutorizacaoCobrancaCliente auto \n");
            sql.append("where auto.ativa \n");
            sql.append("and auto.cliente = ").append(((AutorizacaoCobrancaClienteVO) obj).getCliente().getCodigo()).append(" \n");
            sql.append("and auto.tipoautorizacao = ").append(obj.getTipoAutorizacao().getId()).append(" \n");

            String numeroCartao = obj.getNumeroCartao();
            if (UteisValidacao.emptyString(numeroCartao)) {
                numeroCartao = obj.getCartaoMascarado();
            }
            sql.append("and auto.cartaomascaradointerno = '").append(APF.getCartaoMascarado(numeroCartao.replace(" ", ""))).append("' \n");

            String validade = obj.getValidadeCartao();
            if (validade != null && validade.length() == 5) {
                validade = (validade.replace("/", "/20"));
            }

            sql.append("and auto.validadecartao = '").append(validade).append("' \n");

            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql.append("and auto.codigo <> ").append(obj.getCodigo()).append(" \n");
            }
            if (existe(sql.toString(), con)) {
                throw new ConsistirException("Já existe uma autorização com o mesmo cartão informado (Autorização Cobrança)");
            }
        }
    }

    public void processarOrdemAutorizacoes(ClienteVO clienteVO,
                                            AutorizacaoCobrancaClienteVO autoColocarPrimeiro,
                                            UsuarioVO usuarioVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("au.codigo, \n");
        sql.append("au.ordem \n");
        sql.append("from autorizacaocobrancacliente au \n");
        sql.append("where au.ativa \n");
        sql.append("and au.cliente = ").append(clienteVO.getCodigo()).append(" \n");
        sql.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sql.append("order by ordem \n");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                int ord = 0;
                while (rs.next()) {

                    Integer codigo = rs.getInt("codigo");
                    Integer ordemAnterior = rs.getInt("ordem");
                    Integer ordemNova = ordemAnterior + 1;

                    //quando não informa autorização então reprocessa todas
                    if (autoColocarPrimeiro == null) {
                        ordemNova = ++ord;
                    } else if (autoColocarPrimeiro.getCodigo().equals(codigo)) {
                        ordemNova = 1;
                    }

                    if (ordemNova.equals(ordemAnterior)) {
                        continue;
                    }

                    alterarOrdem(ordemAnterior, ordemNova, codigo, clienteVO, usuarioVO);
                }
            }
        }
    }

    public void alterarOrdem(Integer ordemAnterior, Integer ordemNova, Integer codigo,
                              ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(this.con);

            SuperFacadeJDBC.executarUpdate("update autorizacaocobrancacliente set ordem = " + ordemNova + " where codigo = " + codigo, this.con);

            LogVO log = new LogVO();
            log.setPessoa(clienteVO.getPessoa().getCodigo());
            log.setOperacao("ALTERAÇÃO");
            log.setChavePrimaria(codigo.toString());
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setNomeCampo("Ordem");
            log.setNomeEntidadeDescricao("Ordem de autorização de cobrança");
            log.setValorCampoAlterado(ordemNova.toString());
            log.setValorCampoAnterior(ordemAnterior.toString());

            if (usuarioVO != null) {
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } else {
                log.setResponsavelAlteracao("AUTOMATICO");
            }

            logDAO.incluirSemCommit(log);
        } finally {
            logDAO = null;
        }
    }

    public void validarQuantidadeCartoesCadastrados(Integer cliente, AutorizacaoCobrancaVO autorizacaoCobrancaVO) throws Exception {
        //validar quantidade de cartões que o cliente já tem
        // máximo de 3
        StringBuilder sqlC = new StringBuilder();
        sqlC.append("select \n");
        sqlC.append("count(au.codigo) as qtd \n");
        sqlC.append("from autorizacaocobrancacliente au \n");
        sqlC.append("where au.ativa \n");
        sqlC.append("and au.cliente = ").append(cliente).append(" \n");
        sqlC.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        if (autorizacaoCobrancaVO != null && !UteisValidacao.emptyNumber(autorizacaoCobrancaVO.getCodigo())) {
            sqlC.append("and au.codigo <> ").append(autorizacaoCobrancaVO.getCodigo()).append(" \n");
        }
        if (SuperFacadeJDBC.contar(sqlC.toString(), this.con) >= 3) {
            throw new ConsistirException("O máximo de cartões cadastros que o cliente pode ter são 3 cartões.");
        }
    }

    public void alterarCartaoMascaradoInterno(AutorizacaoCobrancaClienteVO autoVO) throws  Exception{
        String sql = "UPDATE autorizacaocobrancacliente SET cartaomascaradointerno = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, APF.getCartaoMascarado(autoVO.getCartaoMascarado()));
        ps.setInt(2, autoVO.getCodigo());
        ps.executeUpdate();
    }

    public boolean existeAutorizacaoCobranca(Integer cliente, Integer pessoa,
                                             TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum) throws Exception {
        StringBuilder  sql = new StringBuilder();
        sql.append("select \n");
        sql.append("au.codigo \n");
        sql.append("from autorizacaocobrancacliente au \n");
        sql.append("inner join cliente cl on cl.codigo = au.cliente \n");
        sql.append("where au.ativa \n");
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and cl.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(cliente)) {
            sql.append("and au.cliente = ").append(cliente).append(" \n");
        }
        if (tipoAutorizacaoCobrancaEnum != null) {
            sql.append("and au.tipoautorizacao = ").append(tipoAutorizacaoCobrancaEnum.getId()).append(" \n");
        }
        return existe(sql.toString(), con);
    }

    public void ajustarAutorizacaoCobrancaCartaoCliente(int codPessoa) throws SQLException {
        String sql = "select aut.codigo, aut.ordem  FROM AutorizacaoCobrancaCliente aut \n"
                + " inner join cliente cli on cli.codigo = aut.cliente \n"
                + " where cli.pessoa = " + codPessoa + " \n"
                + " and tipoautorizacao = " + TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId() + "\n"
                + " and ativa \n"
                + " order by ordem asc, dataregistro desc";

        try (PreparedStatement ps = con.prepareStatement(sql)) {
            try (ResultSet rs = ps.executeQuery()) {
                int ordem = 1;
                while (rs.next()) {
                    int codigo = rs.getInt("codigo");
                    int ordemAtual = rs.getInt("ordem");

                    if (ordemAtual != ordem) {
                        String update2 = "UPDATE autorizacaocobrancacliente set ordem = " + ordem + " where codigo = " + codigo;
                        try (PreparedStatement ps2 = con.prepareStatement(update2)) {
                            ps2.executeUpdate();
                        } catch (Exception ignore) {
                        }
                    }
                    ++ordem;
                }
            }
        }
    }

    private boolean obterConfigPrimeiraCobrancaPixEGuardarCartao(Integer empresa) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("select primeiraCobrancaPixEGuardarCartao from vendasonlineconfig where empresa = " + empresa, this.con);
        return rs.next() ? rs.getBoolean(1) : false;
    }

}
