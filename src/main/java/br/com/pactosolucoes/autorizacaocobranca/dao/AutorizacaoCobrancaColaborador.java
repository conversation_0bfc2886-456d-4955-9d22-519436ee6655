/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.dao;

import br.com.pactosolucoes.autorizacaocobranca.interfaces.AutorizacaoCobrancaColaboradorInterfaceFacade;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.integracao.aragorn.AragornService;
import br.com.pactosolucoes.integracao.aragorn.NazgDTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.Banco;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.Transacao;
import servicos.impl.apf.APF;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AutorizacaoCobrancaColaborador extends SuperEntidade implements AutorizacaoCobrancaColaboradorInterfaceFacade {

    public AutorizacaoCobrancaColaborador(Connection c) throws Exception {
        super(c);
    }

    public AutorizacaoCobrancaColaborador() throws Exception {
        super();
    }

    private static List montarDadosConsulta(ResultSet tabelaResultado, int nivelMontarDados, Connection con) throws Exception {
        List vetResultado = new ArrayList();
        while (tabelaResultado.next()) {
            AutorizacaoCobrancaColaboradorVO obj = montarDados(tabelaResultado, nivelMontarDados, con);
            vetResultado.add(obj);
        }
        return vetResultado;
    }

    private static AutorizacaoCobrancaColaboradorVO montarDados(ResultSet ds, int nivelMontarDados, Connection con) throws Exception {
        Colaborador colaboradorFacade;
        Banco bancoFacade;
        ConvenioCobranca convenioFacade;
        AutorizacaoCobrancaColaboradorVO obj = new AutorizacaoCobrancaColaboradorVO(true);
        obj.setNovoObj(false);
        try {
            colaboradorFacade = new Colaborador(con);
            bancoFacade = new Banco(con);
            convenioFacade = new ConvenioCobranca(con);
            //
            obj.setCodigo(ds.getInt("codigo"));
            obj.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.valueOf(ds.getInt("tipoautorizacao")));
            obj.setAgencia(ds.getInt("agencia"));
            obj.setAgenciaDV(ds.getString("agenciadv"));
            obj.setContaCorrente(ds.getLong("contacorrente"));
            obj.setContaCorrenteDV(ds.getString("contacorrentedv"));
            obj.setTipoACobrar(TipoObjetosCobrarEnum.valueOf(ds.getInt("tipoacobrar")));
            obj.setListaObjetosACobrar(ds.getString("listaobjetosacobrar"));
            obj.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(ds.getInt("operadoracartao")));
            obj.setIdentificacaoClienteBanco(ds.getString("identificacaoclientebanco"));
            obj.getConvenio().setCodigo(ds.getInt("conveniocobranca"));
            obj.setAtiva(ds.getBoolean("ativa"));
            obj.setNomeTitularCartao(ds.getString("nomeTitularCartao"));

            obj.setValidadeCartao(ds.getString("validadecartao"));
            obj.setCartaoMascarado(ds.getString("cartaomascaradointerno"));
            obj.setNumeroCartao(ds.getString("cartaomascaradointerno"));
            obj.setTokenAragorn(ds.getString("tokenAragorn"));
            obj.setClienteTitularCartao(ds.getBoolean("clienteTitularCartao"));
            obj.setOrdem(ds.getInt("ordem"));
            obj.setTokenCielo(ds.getString("tokenCielo"));
            obj.setOrigemCobrancaEnum(OrigemCobrancaEnum.obterPorCodigo(ds.getInt("origem")));

            try {
                obj.setCartaoVerificado(ds.getBoolean("cartaoVerificado"));
            } catch (Exception ignored) {
            }

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_MINIMOS) {
                obj.setColaborador(colaboradorFacade.consultarPorChavePrimaria(ds.getInt("colaborador"), Uteis.NIVELMONTARDADOS_MINIMOS));
                return obj;
            }

            obj.setColaborador(colaboradorFacade.consultarPorChavePrimaria(ds.getInt("colaborador"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            Integer codEmpresaColaborador = obj.getColaborador().getEmpresa().getCodigo();
            obj.setBanco(ds.getInt("banco") != 0 ? bancoFacade.consultarPorChavePrimaria(ds.getInt("banco"), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA) : new BancoVO());

            if (nivelMontarDados == Uteis.NIVELMONTARDADOS_DADOSBASICOS) {
                obj.setConvenio(convenioFacade.consultarPorCodigoEmpresa(ds.getInt("conveniocobranca"), codEmpresaColaborador, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                try {
                    obj.setCodigoOperacao(ds.getString("codigooperacao"));
                    obj.setCpfTitular(ds.getString("cpftitular"));
                } catch (Exception e) {
                }
                return obj;
            }

            obj.setConvenio(convenioFacade.consultarPorCodigoEmpresa(ds.getInt("conveniocobranca"), codEmpresaColaborador, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA));
            try {
                obj.setCodigoOperacao(ds.getString("codigooperacao"));
                obj.setCpfTitular(ds.getString("cpftitular"));
            } catch (Exception e) {
            }

        } finally {
            colaboradorFacade = null;
            bancoFacade = null;
            convenioFacade = null;
        }

        return obj;
    }

    @Override
    public void incluir(AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        if (obj.isTipoCartao()) {
            obj.setNumeroCartao(obj.getNumeroCartao().replaceAll(" ", ""));

            if (!UteisValidacao.emptyString(obj.getValidadeCartao()) && obj.getValidadeCartao().length() == 5) {
                //salva no banco no formato mm/yyyy
                obj.setValidadeCartao(obj.getValidadeCartao().replace("/", "/20"));
            }
        }
        AutorizacaoCobrancaColaboradorVO.validarDados(obj);
        validarMesmoTipo(obj);
        validarClienteTitularCartao(obj);
        validarMesmoCartao(obj);

        obj.verificarDadosParaEnviarAragorn();

        StringBuilder sql = new StringBuilder("INSERT INTO AutorizacaoCobrancaColaborador( ");
        sql.append("colaborador, tipoAutorizacao, validadeCartao, banco, ");
        sql.append("agencia, agenciaDV, contaCorrente, contaCorrenteDV, tipoACobrar, ");
        sql.append("listaObjetosACobrar, conveniocobranca, operadoracartao, codigooperacao, ");
        sql.append("cpftitular, identificacaoClienteBanco, ativa, nomeTitularCartao, tokenAragorn, ");
        sql.append("clienteTitularCartao, cartaomascaradointerno, cartaoVerificado, ordem, tokenCielo, origem)");
        sql.append(" VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(i++, obj.getColaborador().getCodigo());
            ps.setInt(i++, obj.getTipoAutorizacao().getId());
            ps.setString(i++, obj.getValidadeCartao());
            AutorizacaoCobrancaColaborador.resolveFKNull(ps, i++, obj.getBanco().getCodigo());
            ps.setInt(i++, obj.getAgencia());
            ps.setString(i++, obj.getAgenciaDV().trim());
            ps.setLong(i++, obj.getContaCorrente());
            ps.setString(i++, obj.getContaCorrenteDV().trim());
            ps.setInt(i++, obj.getTipoACobrar().getId());
            ps.setString(i++, obj.getListaObjetosACobrar());
            AutorizacaoCobrancaColaborador.resolveFKNull(ps, i++, obj.getConvenio().getCodigo());
            AutorizacaoCobrancaColaborador.resolveEnumNull(ps, i++, obj.getOperadoraCartao(), "id");
            ps.setString(i++, obj.getCodigoOperacao());
            ps.setString(i++, obj.getCpfTitular());
            ps.setString(i++, obj.getIdentificacaoClienteBanco());
            ps.setBoolean(i++, obj.isAtiva());
            ps.setString(i++, obj.getNomeTitularCartao().toUpperCase());

            if (!obj.isUsarIdVindiPessoa()) {
                obterTokenAragorn(obj);
            }

            ps.setString(i++, obj.getTokenAragorn());
            ps.setBoolean(i++, obj.isClienteTitularCartao());
            ps.setString(i++, obj.getCartaoMascarado());
            ps.setBoolean(i++, obj.isCartaoVerificado());
            ps.setInt(i++, obj.getOrdem());
            ps.setString(i++, obj.getTokenCielo());
            ps.setInt(i++, obj.getOrigemCobrancaEnum().getCodigo());
            ps.execute();
        }
        obj.setCodigo(obterValorChavePrimariaCodigo());
        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.atualizarParcelaDCC(null, obj.getColaborador().getPessoa().getCodigo(), null, obj.getColaborador().getCodigo());
        movParcelaDAO = null;
        try {
            List lista = obj.gerarLogAlteracaoObjetoVO();
            Iterator it = lista.iterator();
            while (it.hasNext()) {
                LogVO log = (LogVO) it.next();
                log.setOperacao("INCLUSÃO");
                log.setChavePrimaria(obj.getCodigo().toString());
                log.setNomeEntidade("AUTORIZACAOCOBRANCACOLABORADOR");
                log.setValorCampoAlterado(log.getValorCampoAlterado() + " ID: " + obj.getCodigo());
                log.setPessoa(obj.getColaborador().getPessoa().getCodigo());
                Log logEntidade = new Log(con);
                logEntidade.incluirSemCommit(log);
                logEntidade = null;
            }
        } catch (Exception e) {
            //pode gerar exceção fora do contexto JSF porque o Log está acoplado ao JSF
        }


        obj.setNovoObj(false);

        //adicionar a autorização que foi adicionada como primeiro na lista
        processarOrdemAutorizacoes(obj.getColaborador(), obj, null);
    }

    public void atribuirParcelasDCC(Integer codigoAutorizacao, int pessoaAutorizacao, int codigoCliente, boolean alterarParcelasDCC) throws Exception {

        MovParcela movParcela = new MovParcela(this.con);
        ResultSet rs = movParcela.consultarParcelasDCC(codigoAutorizacao, codigoCliente);
        HashMap<String, RemessaItemVO> parcelasEmDCC = new HashMap<String, RemessaItemVO>();
        while (rs.next()) {
            RemessaItemVO item = new RemessaItemVO();
            item.getRemessa().setCodigo(rs.getInt("codigoRemessa"));
            item.getMovParcela().setCodigo(rs.getInt("codigo"));
            item.getMovParcela().setParcelaDCC(rs.getBoolean("parcelaDCC"));
            item.getMovParcela().setSituacao(rs.getString("situacao"));
            parcelasEmDCC.put(item.getMovParcela().getCodigo().toString(), item);
        }

        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT codigo,parcelaDCC,situacao FROM MovParcela\n");
        sql.append("  WHERE pessoa = ").append(pessoaAutorizacao);
        PreparedStatement stm = con.prepareStatement(sql.toString());
        ResultSet rsTodasParcelas = stm.executeQuery();

        while (rsTodasParcelas.next()) {

            Boolean parcelaDCC = rsTodasParcelas.getBoolean("parcelaDCC");
            Integer codigoParcela = rsTodasParcelas.getInt("codigo");
            String situacaoParcela = rsTodasParcelas.getString("situacao");

            if (alterarParcelasDCC || !parcelaDCC) {

                RemessaItemVO parcelaEmDCC = parcelasEmDCC.get(codigoParcela.toString());

                if (parcelaEmDCC != null && !UteisValidacao.emptyNumber(parcelaEmDCC.getMovParcela().getCodigo())) {
                    if (parcelaEmDCC.getMovParcela().getSituacao().equals("EA")) {
                        if (!parcelaEmDCC.getMovParcela().getParcelaDCC()) {
                            MovParcelaVO parcelaAlterar = parcelaEmDCC.getMovParcela();
                            parcelaAlterar.setParcelaDCC(true);
                            movParcela.alterarRegimeDCCParcela(parcelaAlterar.getCodigo(), parcelaAlterar.getParcelaDCC());
                        }
                    } else if (parcelaEmDCC.getMovParcela().getSituacao().equals("PG") && !UteisValidacao.emptyNumber(parcelaEmDCC.getRemessa().getCodigo())) {
                        if (!parcelaEmDCC.getMovParcela().getParcelaDCC()) {
                            MovParcelaVO parcelaAlterar = parcelaEmDCC.getMovParcela();
                            parcelaAlterar.setParcelaDCC(true);
                            movParcela.alterarRegimeDCCParcela(parcelaAlterar.getCodigo(), parcelaAlterar.getParcelaDCC());
                        }
                    }
                } else if (parcelaEmDCC == null && parcelaDCC && situacaoParcela.equals("EA")) {
                    parcelaDCC = false;
                    movParcela.alterarRegimeDCCParcela(codigoParcela, parcelaDCC);
                }
            }

        }
    }

    @Override
    public void alterar(AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        AutorizacaoCobrancaColaboradorVO.validarDados(obj);
        validarMesmoTipo(obj);
        validarClienteTitularCartao(obj);
        validarMesmoCartao(obj);

        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            boolean alterarNumeroCartao = obj.getNumeroCartao() != null && !obj.getNumeroCartao().contains("******");
            if (alterarNumeroCartao) {
                desativar(obj, true);

                obj.setCartaoMascaradoAnterior(((AutorizacaoCobrancaColaboradorVO) obj.getObjetoVOAntesAlteracao()).getCartaoMascarado());
                obj.setObjetoVOAntesAlteracao(new AutorizacaoCobrancaColaboradorVO());
                obj.setAlterouNumeroCartao(true);
                obj.setCodigo(0);
                obj.setNovoObj(true);
                obj.setAtiva(true);
                incluir(obj);
                return;
            }
        }

        obj.verificarDadosParaEnviarAragorn();

        StringBuilder sql = new StringBuilder("UPDATE AutorizacaoCobrancaColaborador set ");
        sql.append("colaborador=?, tipoAutorizacao=?, validadeCartao=?, banco=?, ");
        sql.append("agencia=?, agenciaDV=?, contaCorrente=?, contaCorrenteDV=?, tipoACobrar=?,");
        sql.append("listaObjetosACobrar=?, conveniocobranca=?, operadoracartao=?, codigooperacao=?, ");
        sql.append("cpftitular=?, identificacaoClienteBanco=?, ativa=?, nomeTitularCartao=?, tokenAragorn = ?, ");
        sql.append("dataAlteracao = ?, clienteTitularCartao = ?, cartaoVerificado = ?, ordem = ?, tokenCielo = ?, origem = ? ");
        sql.append(" WHERE codigo = ? ");

        int i = 1;
        try (PreparedStatement ps = con.prepareStatement(sql.toString())) {
            ps.setInt(i++, obj.getColaborador().getCodigo());
            ps.setInt(i++, obj.getTipoAutorizacao().getId());
            ps.setString(i++, obj.getValidadeCartao());
            resolveFKNull(ps, i++, obj.getBanco().getCodigo());
            ps.setInt(i++, obj.getAgencia());
            ps.setString(i++, obj.getAgenciaDV().trim());
            ps.setLong(i++, obj.getContaCorrente());
            ps.setString(i++, obj.getContaCorrenteDV().trim());
            ps.setInt(i++, obj.getTipoACobrar().getId());
            ps.setString(i++, obj.getListaObjetosACobrar());
            resolveFKNull(ps, i++, obj.getConvenio().getCodigo());
            resolveEnumNull(ps, i++, obj.getOperadoraCartao(), "id");
            ps.setString(i++, obj.getCodigoOperacao());
            ps.setString(i++, obj.getCpfTitular());
            ps.setString(i++, obj.getIdentificacaoClienteBanco());
            ps.setBoolean(i++, obj.isAtiva());
            ps.setString(i++, obj.getNomeTitularCartao().toUpperCase());

            obterTokenAragorn(obj);

            ps.setString(i++, obj.getTokenAragorn());
            ps.setTimestamp(i++, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            ps.setBoolean(i++, obj.isClienteTitularCartao());
            ps.setBoolean(i++, obj.isCartaoVerificado());
            ps.setInt(i++, obj.getOrdem());
            ps.setString(i++, obj.getTokenCielo());
            ps.setInt(i++, obj.getOrigemCobrancaEnum().getCodigo());

            ps.setInt(i++, obj.getCodigo());
            ps.execute();
        }

        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.atualizarParcelaDCC(null, obj.getColaborador().getPessoa().getCodigo(), null, obj.getColaborador().getCodigo());
        movParcelaDAO = null;

        List lista = obj.gerarLogAlteracaoObjetoVO();
        Iterator it = lista.iterator();
        while (it.hasNext()) {
            LogVO log = (LogVO) it.next();
            log.setOperacao("ALTERAÇÃO");
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setNomeEntidade("AUTORIZACAOCOBRANCACOLABORADOR");
            log.setValorCampoAlterado(log.getValorCampoAlterado() + " ID: " + obj.getCodigo());
            log.setPessoa(obj.getColaborador().getPessoa().getCodigo());
            getFacade().getLog().incluirSemCommit(log);
        }
    }

    public void desativar(AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        desativar(obj, false);
    }

    private void desativar(AutorizacaoCobrancaColaboradorVO obj, boolean alterouCartao) throws Exception {
        obj.setAtiva(false);
        String sql = "UPDATE AutorizacaoCobrancaColaborador SET ativa = ? WHERE codigo = ?";
        try (PreparedStatement sqlExcluir = con.prepareStatement(sql)) {
            sqlExcluir.setBoolean(1, obj.isAtiva());
            sqlExcluir.setInt(2, obj.getCodigo());
            sqlExcluir.execute();
        }

        MovParcela movParcelaDAO = new MovParcela(con);
        movParcelaDAO.atualizarParcelaDCC(null, obj.getColaborador().getPessoa().getCodigo(), null, obj.getColaborador().getCodigo());
        movParcelaDAO = null;

        List lista = obj.gerarLogExclusaoObjetoVO();
        Iterator it = lista.iterator();
        while (it.hasNext()) {
            LogVO log = (LogVO) it.next();
            log.setChavePrimaria(obj.getCodigo().toString());
            log.setNomeEntidade("AUTORIZACAOCOBRANCACOLABORADOR");
            StringBuilder msg = new StringBuilder();
            if (alterouCartao) {
                msg.append("Alterou o número do cartão. Será gerada uma nova autorização. \n");
            }
            msg.append("AUTORIZAÇÃO DE COBRANÇA EXCLUIDA! ").append(log.getValorCampoAlterado()).append(" ID: ").append(obj.getCodigo());
            log.setValorCampoAlterado(msg.toString());
            log.setPessoa(obj.getColaborador().getPessoa().getCodigo());
            getFacade().getLog().incluirSemCommit(log);
        }

        Transacao transacaoDAO = new Transacao(con);
        transacaoDAO.excluirCartaoVindi(obj.getCodigoExterno(), obj.getConvenio());
        transacaoDAO = null;

        processarOrdemAutorizacoes(obj.getColaborador(), null, null);
    }

    @Override
    public AutorizacaoCobrancaColaboradorVO consultarPorChavePrimaria(final int codigo) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaColaborador WHERE codigo = ?";
        PreparedStatement sqlConsultar = con.prepareStatement(sql);
        sqlConsultar.setInt(1, codigo);
        ResultSet tabelaResultado = sqlConsultar.executeQuery();
        if (!tabelaResultado.next()) {
            throw new ConsistirException(String.format("Dados Não Encontrados ( AutorizacaoCobrancaColaborador %s)", codigo));
        }
        return (montarDados(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, con));
    }

    @Override
    public List<AutorizacaoCobrancaColaboradorVO> consultarPorColaborador(final int codigoColaborador, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaColaborador WHERE ativa = true and colaborador = ? order by tipoautorizacao,ordem";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, codigoColaborador);
        ResultSet tabelaResultado = ps.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public List<AutorizacaoCobrancaColaboradorVO> consultarPorPessoa(final int codigoPessoa) throws Exception {
        String sql = "SELECT auto.* FROM AutorizacaoCobrancaColaborador auto "
                + "inner join Colaborador col on col.codigo = auto.colaborador "
                + "inner join Pessoa pes on pes.codigo = col.pessoa "
                + "WHERE auto.ativa and auto.colaborador = col.codigo and pes.codigo = ? order by auto.ordem ";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setInt(1, codigoPessoa);
        ResultSet tabelaResultado = ps.executeQuery();
        return montarDadosConsulta(tabelaResultado, Uteis.NIVELMONTARDADOS_TODOS, con);
    }

    @Override
    public List<AutorizacaoCobrancaColaboradorVO> consulta(String condicao, int nivelMontarDados) throws Exception {
        String sql = "SELECT * FROM AutorizacaoCobrancaColaborador ";
        sql += condicao != null ? " WHERE " + condicao : "" + " order by colaborador";
        PreparedStatement ps = con.prepareStatement(sql);
        ResultSet tabelaResultado = ps.executeQuery();
        return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
    }

    @Override
    public boolean existeOutraAutorizacaoParecidaParaOMesmoTipo(final AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        return existe(String.format("select auto.codigo from AutorizacaoCobrancaColaborador auto "
                        + "inner join ConvenioCobranca conv ON conv.codigo = auto.conveniocobranca "
                        + "where auto.ativa = true and auto.colaborador = %s and auto.tipoautorizacao = %s and auto.codigo <> %s and tipoACobrar = %s",
                obj.getColaborador().getCodigo(), obj.getTipoAutorizacao().getId(), obj.getCodigo(),
                obj.getTipoACobrar().getId()), con);
    }

    @Override
    public List<AutorizacaoCobrancaColaboradorVO> obterOutrasAutorizacoesParecidasParaOMesmoTipo(final AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        String sql = String.format("select * from AutorizacaoCobrancaColaborador where ativa = true and colaborador = %s and tipoautorizacao = %s and codigo <> %s and tipoACobrar = %s",
                obj.getColaborador().getCodigo(), obj.getTipoAutorizacao().getId(), obj.getCodigo(), obj.getTipoACobrar().getId());
        return montarDadosConsulta(criarConsulta(sql, con), Uteis.NIVELMONTARDADOS_DADOSBASICOS, con);
    }

    @Override
    public void trocarCartao(final int colaborador, final CartaoCreditoTO cartao, final String cartaoMascaradoAnterior) throws Exception {
        List<AutorizacaoCobrancaColaboradorVO> autorizacoes = this.consultarPorColaborador(colaborador, Uteis.NIVELMONTARDADOS_TODOS);

        for (AutorizacaoCobrancaColaboradorVO auto : autorizacoes) {
            if (auto.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
                if (auto.getCartaoMascarado().equals(cartaoMascaradoAnterior)) {
                    auto.setNumeroCartao(cartao.getNumero());
                    auto.setValidadeCartao(cartao.getValidade());
                    auto.setOperadoraCartao(cartao.getBand());
                    alterar(auto);
                }
            }
        }
    }

    private void obterTokenAragorn(AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        if (obj.getTipoAutorizacao() != null && obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            obj.setTokenAragorn(new AragornService().enviarNazg(new NazgDTO(obj)).getToken());
        }
    }

    public List<AutorizacaoCobrancaColaboradorVO> consultarPorPessoaTipoAutorizacao(final int codigoPessoa, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum, int nivelMontarDados) throws Exception{
        String sql = "SELECT auto.* FROM AutorizacaoCobrancaColaborador auto "
                + "inner join colaborador col on col.codigo = auto.colaborador "
                + "inner join Pessoa pes on pes.codigo = col.pessoa "
                + "WHERE auto.ativa = true and pes.codigo = ? AND auto.tipoAutorizacao = ? ORDER BY auto.ordem";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setInt(1, codigoPessoa);
            ps.setInt(2, tipoAutorizacaoCobrancaEnum.getId());
            try (ResultSet tabelaResultado = ps.executeQuery()) {
                return montarDadosConsulta(tabelaResultado, nivelMontarDados, con);
            }
        }
    }

    public void validarClienteTitularCartao(AutorizacaoCobrancaVO obj) throws Exception {
        AutorizacaoCobrancaCliente autoDAO;
        try {
            autoDAO = new AutorizacaoCobrancaCliente(this.con);
            autoDAO.validarClienteTitularCartao(obj);
        } finally {
            autoDAO = null;
        }
    }

    public void alterarTipoACobrar(AutorizacaoCobrancaColaboradorVO auto) throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append("UPDATE autorizacaocobrancacolaborador\n");
        sb.append(" SET tipoACobrar = ?\n");
        sb.append(", listaobjetosacobrar = ?\n");
        sb.append(" WHERE codigo = ?");

        try (PreparedStatement ps = getCon().prepareStatement(sb.toString())) {
            int i = 1;
            ps.setInt(i++, auto.getTipoACobrar().getId());
            if (TipoObjetosCobrarEnum.TIPOS_PRODUTOS.equals(auto.getTipoACobrar())) {
                ps.setString(i++, auto.getListaObjetosACobrar());
            } else {
                ps.setString(i++, "");
            }
            ps.setInt(i++, auto.getCodigo());
            ps.executeUpdate();
        }
    }

    public AutorizacaoCobrancaColaboradorVO obterAutorizacaoMaisRecente(TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum,
                                                                        Integer colaborador, Integer pessoa, int nivelMontarDados) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("au.* \n");
        sql.append("from AutorizacaoCobrancaColaborador au \n");
        sql.append("inner join colaborador cl on cl.codigo = au.colaborador \n");
        sql.append("where au.ativa \n");
        if (tipoAutorizacaoCobrancaEnum != null) {
            sql.append("and au.tipoautorizacao = ").append(tipoAutorizacaoCobrancaEnum.getId()).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(pessoa)) {
            sql.append("and cl.pessoa = ").append(pessoa).append(" \n");
        }
        if (!UteisValidacao.emptyNumber(colaborador)) {
            sql.append("and cl.codigo = ").append(colaborador).append(" \n");
        }
        sql.append("order by au.codigo desc  \n");
        sql.append("limit 1 \n");

        try (PreparedStatement pst = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = pst.executeQuery()) {
                if (rs.next()) {
                    return montarDados(rs, nivelMontarDados, con);
                }
                return null;
            }
        }
    }

    public void alterarSomenteCartaoVerificado(AutorizacaoCobrancaColaboradorVO autoVO) throws Exception {
        String sql = "UPDATE AutorizacaoCobrancaColaborador SET cartaoverificado = ? WHERE codigo = ?";
        try (PreparedStatement ps = con.prepareStatement(sql)) {
            ps.setBoolean(1, autoVO.isCartaoVerificado());
            ps.setInt(2, autoVO.getCodigo());
            ps.executeUpdate();
        }
    }

    private void validarMesmoTipo(AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        boolean utilizaRetentativa = false;
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {
            Empresa empDAO;
            try {
                empDAO = new Empresa(this.con);
                EmpresaVO empresaVO = empDAO.consultarPorChavePrimaria(obj.getColaborador().getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                utilizaRetentativa = empresaVO.isHabilitarReenvioAutomaticoRemessa();
            } finally {
                empDAO = null;
            }
        }

        if (!obj.getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE) &&
                existeOutraAutorizacaoParecidaParaOMesmoTipo(obj) && !utilizaRetentativa) {
            if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO) &&
                    !obj.getConvenio().getTipo().getTipoCobranca().equals(TipoCobrancaEnum.ONLINE)) {
                throw new ConsistirException("Já existe uma Autorização do mesmo Tipo com os dados informados. Para utilizar múltiplos cartões é necessário ter um convênio de cobrança ativo do tipo ONLINE.");
            } else {
                throw new ConsistirException("Já existe uma Autorização do mesmo Tipo com os dados informados (Autorização Cobrança)");
            }
        }
    }

    public void validarMesmoCartao(AutorizacaoCobrancaVO obj) throws Exception {
        if (obj.getTipoAutorizacao().equals(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO)) {

            //validar quantidade de cartões que o cliente já tem
            // máximo de 3
            validarQuantidadeCartoesCadastrados(((AutorizacaoCobrancaColaboradorVO) obj).getColaborador().getCodigo(), obj);

            StringBuilder sql = new StringBuilder();
            sql.append("select \n");
            sql.append("auto.codigo \n");
            sql.append("from AutorizacaoCobrancaColaborador auto \n");
            sql.append("where auto.ativa \n");
            sql.append("and auto.colaborador = ").append(((AutorizacaoCobrancaColaboradorVO) obj).getColaborador().getCodigo()).append(" \n");
            sql.append("and auto.tipoautorizacao = ").append(obj.getTipoAutorizacao().getId()).append(" \n");

            String numeroCartao = obj.getNumeroCartao();
            if (UteisValidacao.emptyString(numeroCartao)) {
                numeroCartao = obj.getCartaoMascarado();
            }
            sql.append("and auto.cartaomascaradointerno = '").append(APF.getCartaoMascarado(numeroCartao.replace(" ", ""))).append("' \n");

            String validade = obj.getValidadeCartao();
            if (validade != null && validade.length() == 5) {
                validade = (validade.replace("/", "/20"));
            }

            sql.append("and auto.validadecartao = '").append(validade).append("' \n");

            if (!UteisValidacao.emptyNumber(obj.getCodigo())) {
                sql.append("and auto.codigo <> ").append(obj.getCodigo()).append(" \n");
            }
            if (existe(sql.toString(), con)) {
                throw new ConsistirException("Já existe uma autorização com o mesmo cartão informado (Autorização Cobrança)");
            }
        }
    }

    private void processarOrdemAutorizacoes(ColaboradorVO colaboradorVO,
                                            AutorizacaoCobrancaColaboradorVO autoColocarPrimeiro,
                                            UsuarioVO usuarioVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("au.codigo, \n");
        sql.append("au.ordem \n");
        sql.append("from AutorizacaoCobrancaColaborador au \n");
        sql.append("where au.ativa \n");
        sql.append("and au.colaborador = ").append(colaboradorVO.getCodigo()).append(" \n");
        sql.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        sql.append("order by ordem \n");
        try (PreparedStatement stm = con.prepareStatement(sql.toString())) {
            try (ResultSet rs = stm.executeQuery()) {
                int ord = 0;
                while (rs.next()) {

                    Integer codigo = rs.getInt("codigo");
                    Integer ordemAnterior = rs.getInt("ordem");
                    Integer ordemNova = ordemAnterior + 1;

                    //quando não informa autorização então reprocessatodas
                    if (autoColocarPrimeiro == null) {
                        ordemNova = ++ord;
                    } else if (autoColocarPrimeiro.getCodigo().equals(codigo)) {
                        ordemNova = 1;
                    }

                    if (ordemNova.equals(ordemAnterior)) {
                        continue;
                    }

                    alterarOrdem(ordemAnterior, ordemNova, codigo, colaboradorVO, usuarioVO);
                }
            }
        }
    }

    public void alterarOrdem(Integer ordemAnterior, Integer ordemNova, Integer codigo,
                             ColaboradorVO colaboradorVO, UsuarioVO usuarioVO) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(this.con);

            SuperFacadeJDBC.executarUpdate("update AutorizacaoCobrancaColaborador set ordem = " + ordemNova + " where codigo = " + codigo, this.con);

            LogVO log = new LogVO();
            log.setPessoa(colaboradorVO.getPessoa().getCodigo());
            log.setOperacao("ALTERAÇÃO");
            log.setChavePrimaria(codigo.toString());
            log.setNomeEntidade("AUTORIZACAOCOBRANCACLIENTE");
            log.setNomeCampo("Ordem");
            log.setNomeEntidadeDescricao("Ordem de autorização de cobrança");
            log.setValorCampoAlterado(ordemNova.toString());
            log.setValorCampoAnterior(ordemAnterior.toString());

            if (usuarioVO != null) {
                log.setResponsavelAlteracao(usuarioVO.getNome());
                log.setUserOAMD(usuarioVO.getUserOamd());
            } else {
                log.setResponsavelAlteracao("AUTOMATICO");
            }

            logDAO.incluirSemCommit(log);
        } finally {
            logDAO = null;
        }
    }

    public void validarQuantidadeCartoesCadastrados(Integer colaborador, AutorizacaoCobrancaVO autorizacaoCobrancaVO) throws Exception {
        //validar quantidade de cartões que o cliente já tem
        // máximo de 3
        StringBuilder sqlC = new StringBuilder();
        sqlC.append("select \n");
        sqlC.append("count(au.codigo) as qtd \n");
        sqlC.append("from AutorizacaoCobrancaColaborador au \n");
        sqlC.append("where au.ativa \n");
        sqlC.append("and au.colaborador = ").append(colaborador).append(" \n");
        sqlC.append("and au.tipoautorizacao = ").append(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO.getId()).append(" \n");
        if (autorizacaoCobrancaVO != null && !UteisValidacao.emptyNumber(autorizacaoCobrancaVO.getCodigo())) {
            sqlC.append("and au.codigo <> ").append(autorizacaoCobrancaVO.getCodigo()).append(" \n");
        }
        if (SuperFacadeJDBC.contar(sqlC.toString(), this.con) >= 3) {
            throw new ConsistirException("O máximo de cartões cadastros que o cliente pode ter são 3 cartões.");
        }
    }

    public void alterarCartaoMascaradoInterno(AutorizacaoCobrancaColaboradorVO autoVO) throws  Exception{
        String sql = "UPDATE AutorizacaoCobrancaColaborador SET cartaomascaradointerno = ? WHERE codigo = ?";
        PreparedStatement ps = con.prepareStatement(sql);
        ps.setString(1, APF.getCartaoMascarado(autoVO.getCartaoMascarado()));
        ps.setInt(2, autoVO.getCodigo());
        ps.executeUpdate();
    }
}
