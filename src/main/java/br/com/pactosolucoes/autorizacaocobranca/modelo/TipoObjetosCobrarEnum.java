/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.modelo;

/**
 *
 * <AUTHOR>
 */
public enum TipoObjetosCobrarEnum {

    NENHUM                      (0, "(Nenhum)"),
    TUDO                        (1, "Em Aberto"),
    APENAS_PLANOS               (2, "Planos"),
    TIPOS_PRODUTOS              (3, "Produtos Especificados"),
    CONTRATOS_RENOVAVEIS_AUTO   (4, "Contratos Autorrenováveis");

    TipoObjetosCobrarEnum(int id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    private int id;
    private String descricao;

    public String getDescricao() {
        return descricao;
    }

    public int getId() {
        return id;
    }

    public static TipoObjetosCobrarEnum valueOf(final int id) {
        TipoObjetosCobrarEnum[] lista = TipoObjetosCobrarEnum.values();
        for (TipoObjetosCobrarEnum tipoObjetosCobrarEnum : lista) {
            if (id == tipoObjetosCobrarEnum.id) {
                return tipoObjetosCobrarEnum;
            }

        }
        return NENHUM;
    }
}
