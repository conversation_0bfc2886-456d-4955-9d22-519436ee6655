/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.modelo;

import annotations.arquitetura.ChaveEstrangeira;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.financeiro.BancoVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.UteisValidacao;

public class AutorizacaoCobrancaColaboradorVO extends AutorizacaoCobrancaVO {

    @ChaveEstrangeira
    private ColaboradorVO colaborador = new ColaboradorVO();
    private Integer ordem;

    public AutorizacaoCobrancaColaboradorVO(boolean iniciarLog) {
        if (iniciarLog) {
            registrarObjetoVOAntesDaAlteracao();
        }
    }

    public AutorizacaoCobrancaColaboradorVO() {
    }

    public static void validarDados(AutorizacaoCobrancaColaboradorVO obj) throws Exception {
        validarDados(obj, true);
    }

    public static void validarDados(AutorizacaoCobrancaColaboradorVO obj, boolean validarColaborador) throws Exception {
        AutorizacaoCobrancaVO.validarDados(obj);
        if (UteisValidacao.emptyNumber(obj.getColaborador().getCodigo()) && validarColaborador) {
            throw new ConsistirException("Colaborador deve ser informado (Autorização Cobrança)");
        }
    }

    public ColaboradorVO getColaborador() {
        return colaborador;
    }

    public void setColaborador(ColaboradorVO colaborador) {
        this.colaborador = colaborador;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AutorizacaoCobrancaVO auto = (AutorizacaoCobrancaVO) obj;
        final AutorizacaoCobrancaColaboradorVO other = (AutorizacaoCobrancaColaboradorVO) obj;
        return super.equals(auto) && !(this.colaborador != other.colaborador && (this.colaborador == null || !(this.colaborador.getCodigo().intValue() == other.colaborador.getCodigo().intValue())));
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 47 * hash + (this.colaborador != null ? this.colaborador.hashCode() : 0);
        hash = 47 * hash + (super.getTipoAutorizacao() != null ? super.getTipoAutorizacao().hashCode() : 0);
        hash = 47 * hash + (super.getNumeroCartao() != null ? super.getNumeroCartao().hashCode() : 0);
        hash = 47 * hash + (super.getValidadeCartao() != null ? super.getValidadeCartao().hashCode() : 0);
        hash = 47 * hash + (super.getBanco() != null ? super.getBanco().hashCode() : 0);
        hash = 47 * hash + (super.getAgencia() != null ? super.getAgencia().hashCode() : 0);
        hash = 47 * hash + (super.getAgenciaDV() != null ? super.getAgenciaDV().hashCode() : 0);
        hash = 47 * hash + (super.getContaCorrente() != null ? super.getContaCorrente().hashCode() : 0);
        hash = 47 * hash + (super.getContaCorrenteDV() != null ? super.getContaCorrenteDV().hashCode() : 0);
        hash = 47 * hash + (super.getTipoACobrar() != null ? super.getTipoACobrar().hashCode() : 0);
        hash = 47 * hash + (super.getListaObjetosACobrar() != null ? super.getListaObjetosACobrar().hashCode() : 0);
        hash = 47 * hash + (super.getConvenio() != null ? super.getConvenio().hashCode() : 0);
        hash = 47 * hash + (super.getOperadoraCartao() != null ? super.getOperadoraCartao().hashCode() : 0);
        return hash;
    }

    @Override
    public Integer getOrdem() {
        if (ordem == null) {
            ordem = 0;
        }
        return ordem;
    }

    @Override
    public void setOrdem(Integer ordem) {
        this.ordem = ordem;
    }
}
