/*
 *
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.autorizacaocobranca.interfaces;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.estrutura.paginacao.ConfPaginacao;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.interfaces.basico.SuperInterface;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface AutorizacaoCobrancaClienteInterfaceFacade extends SuperInterface {

    void alterar(AutorizacaoCobrancaClienteVO obj) throws Exception;

    void alterar(AutorizacaoCobrancaClienteVO obj, UsuarioVO usuarioVO) throws Exception;

    void alterar(AutorizacaoCobrancaClienteVO obj, Boolean atribuirParcelaDCC, UsuarioVO usuarioVO) throws Exception;

    void alterarTipoACobrar(AutorizacaoCobrancaClienteVO obj) throws Exception;

    AutorizacaoCobrancaClienteVO consultarPorChavePrimaria(final int codigo) throws Exception;

    AutorizacaoCobrancaClienteVO consultarPorChavePrimaria(final int codigo, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorCliente(final int codigoCliente, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorClienteAutoCobAtivaAndTipoAuto(final int codigoCliente, boolean ativa, TipoAutorizacaoCobrancaEnum tipoAuto, int nivelMontarDados) throws Exception;

    Integer qtdAutorizacaoAtivaPorCliente(final int codigoCliente) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorClienteTipoAutorizacao(final int codigoCliente, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum, int nivelMontarDados) throws Exception;

    void incluir(AutorizacaoCobrancaClienteVO obj) throws Exception;

    void incluir(AutorizacaoCobrancaClienteVO obj, UsuarioVO usuarioVO) throws Exception;

    boolean existeOutraAutorizacaoParecidaParaOMesmoTipo(final AutorizacaoCobrancaClienteVO obj) throws Exception;
    
    List<AutorizacaoCobrancaClienteVO> obterOutrasAutorizacoesParecidasParaOMesmoTipo(final AutorizacaoCobrancaClienteVO obj) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consulta(final String condicao,int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorPessoaEmpresa(final int codigoPessoa, final int codigoEmpresa, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorPessoa(final int codigoCliente, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoAutorizacao(final int codigoPessoa, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarListaCartoesVencidos(EmpresaVO empresa, List<ColaboradorVO> colaboradores, Integer cliente) throws Exception;

    boolean trocarCartao(final int cliente, final CartaoCreditoTO cartao, final String cartaoMascaradoAnterior) throws Exception;

    public void atribuirParcelasDCC(Integer codigoAutorizacao, int pessoaAutorizacao,int clienteAutorizacao,boolean alterarParcelasDCC) throws Exception;
    
    public boolean validarBandeiraConvenio(OperadorasExternasAprovaFacilEnum band, TipoConvenioCobrancaEnum tipo);

    public AutorizacaoCobrancaClienteVO consultar(Integer codigoCliente, TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum)throws Exception;

    void clonarAutorizacoesNovaEmpresa(Integer cliente, Integer empresaNova, Integer empresaAntiga, Connection con) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoConvenio(Integer pessoa, TipoConvenioCobrancaEnum tipoConvenioCobranca, int nivelMontarDados) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarPorPessoaTipoConvenio(Integer pessoa, TipoConvenioCobrancaEnum tipoConvenioCobranca, Boolean somenteAtivas, int nivelMontarDados) throws Exception;

    public JSONObject obterUltimoCartaoAtivoAluno(Integer cliente) throws Exception;

    public JSONObject obterUltimoCartaoAtivoAluno(Integer cliente, TipoAutorizacaoCobrancaEnum tipo) throws Exception;

    void alterarSituacaoAutorizacaoCobranca(boolean ativa, AutorizacaoCobrancaClienteVO auto, String descricao) throws Exception;

    void alterarSituacaoAutorizacaoCobranca(boolean ativa, AutorizacaoCobrancaClienteVO auto, String descricao, UsuarioVO usuarioVO) throws  Exception;

    void marcarRenovadoAutomatico(AutorizacaoCobrancaClienteVO autorizacaoAntiga, AutorizacaoCobrancaClienteVO autorizacaoNova) throws  Exception;

    Integer contarPendenciaCartaoProblema(Integer codigoEmpresa, List<Integer> consultores) throws Exception;

    ResultSet contarPendenciaCartaoProblemaRS(Integer codigoEmpresa, List<Integer> consultores) throws Exception;

    /**
     *
     * @param codigoEmpresa     0 para consultar de todas as empresas
     * @param consultores
     * @param paginacao
     * @param count
     * @return
     * @throws Exception
     */
    ResultSet consultarPendenciaClienteCartaoComProblema(Integer codigoEmpresa, List<Integer> consultores, ConfPaginacao paginacao, boolean count) throws Exception;

    void atualizarAutorizacaoCobrancaClienteCartaoCreditoUsandoConfigReenvioCobrancaAutomatica(Integer codEmpresa, Integer cliente) throws Exception;

    void atualizarAssinaturaDigitalBiometria(Integer codigo, String assinatura) throws SQLException;

    void possuiPermissao(String tipo) throws Exception;

    public List<Integer> consultarCodigosAutorizacaoCobrancaCartao() throws  Exception;

    public void alterarNumeroCartao(Integer codigo, String numeroCartao) throws  Exception;

    public void incluirLogAlteracaoAutorizacaoCobrancaFeitoViaSite(AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO, String origem, boolean inclusao, UsuarioVO usuarioVO) throws  Exception;

    void atualizarAutorizacoesProdutosEspecificados(EmpresaVO empresaVO) throws SQLException;

    boolean necessarioTrocarConvenioCobranca(Integer cliente, Integer empresaDestino);

    void alterarConvenioCobrancaTrocaEmpresa(Integer cliente, ConvenioCobrancaVO convenioNovoVO, UsuarioVO usuarioVO , Integer empresaAnterior) throws Exception;

    boolean clienteTemAutorizacaoCobrancaAtiva(Integer cliente) throws Exception;

    List<String> obterTokensAragornParaRemessa(List<Integer> listaPessoas) throws Exception;

    String cartaoMascaradoPessoa(Integer pessoa) throws Exception;

    List<AutorizacaoCobrancaClienteVO> consultarProcessoAlterarTipoACobrar(Integer empresa, Integer convenioCobranca,
                                                                           TipoObjetosCobrarEnum tipoObjetosCobrarEnumDiferente,
                                                                           int nivelMontarDados) throws Exception;

    StringBuilder processoAlterarTipoACobrar(List<AutorizacaoCobrancaClienteVO> lista,
                                      TipoObjetosCobrarEnum tipoObjetosCobrarEnum,
                                      UsuarioVO usuarioVO) throws Exception;

    AutorizacaoCobrancaClienteVO obterUltimaAutorizacao(Integer cliente, Integer pessoa,
                                                        TipoAutorizacaoCobrancaEnum tipoAutorizacao, int nivelMontarDados) throws Exception;

    boolean gerarAutorizacaoCobrancaBoleto(Integer empresa, Integer pessoa, Integer cliente) throws Exception;

    void clonarAutorizacaoCobrancaParaOutroCliente(Integer codigoAutorizacaoConsultar, Integer clienteReceber);

    void alterarSomenteCartaoVerificado(AutorizacaoCobrancaClienteVO autoVO) throws Exception;

    void processarCartaoVerificado(RemessaItemVO remessaItemVO);

    void processarCartaoVerificado(TransacaoVO transacaoVO);

    void validarClienteTitularCartao(AutorizacaoCobrancaVO obj) throws Exception;

    void validarMesmoCartao(AutorizacaoCobrancaVO obj) throws Exception;

    void validarQuantidadeCartoesCadastrados(Integer cliente, AutorizacaoCobrancaVO autorizacaoCobrancaVO) throws Exception;

    void alterarOrdem(Integer ordemAnterior, Integer ordemNova, Integer codigo,
                      ClienteVO clienteVO, UsuarioVO usuarioVO) throws Exception;

    boolean existeAutorizacaoCobranca(Integer cliente, Integer pessoa,
                                      TipoAutorizacaoCobrancaEnum tipoAutorizacaoCobrancaEnum) throws Exception;
}
