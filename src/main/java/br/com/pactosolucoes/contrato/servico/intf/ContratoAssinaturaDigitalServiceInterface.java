/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.contrato.servico.intf;

import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.contrato.ContratoAssinaturaDigitalVO;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.ServletContext;

/**
 * <AUTHOR>
 */
public interface ContratoAssinaturaDigitalServiceInterface {

    Boolean validarToken(String token) throws Exception;

    JSONObject infoAcesso(String chave, Integer codigoUsuario, Integer empresaLogada) throws Exception;

    void incluirAssinatura(String chave, String token, Integer contrato, String ip, Integer usuarioResponsavel,
                           Integer contratoTextoPadrao, String documentos, String assinatura, String endereco, String atestado,
                           String anexo1, String anexo2, boolean updateAssinatura, boolean updateDocs, boolean updateEnd,
                           boolean updateAte, boolean updateAnexo1, boolean updateAnexo2, String descMoedaEmpresa, Boolean assinaturaCancelamento, String urlRequest, String assinatura2) throws Exception;

    JSONObject consultarContratos(boolean assinados, String filtro, Integer empresa, boolean todos, Boolean contratosCancelados) throws Exception;

    JSONObject selecionarContrato(final String key, final Integer contrato, String descMoedaEmpresa) throws Exception;

    void incluirAssinaturaSimples(String chave, Integer contrato, String assinatura) throws Exception;

    JSONObject visualizarContrato(Integer contrato, String url, Boolean assiCancelamento) throws Exception;

    ServletContext getServletContext();

    void setServletContext(ServletContext servletContext);

    String alterarFotoAluno(String chave,
                            Integer contrato, Integer codigoPessoa, String foto, Integer usuarioLogado, UsuarioVO usuarioSessao) throws Exception;

    JSONObject consultarParaAtestado(String filtro, Integer empresa) throws Exception;

    void salvarImagens(String chave, Integer contrato, UsuarioVO usuarioSessao, String imagemDocumentos, boolean documentosUpdate,
                       String imagemEndereco, boolean enderecoUpdate, String imagemAtestado, boolean atestadoUpdate,
                       String imagemAnexo1, boolean anexo1Update, String imagemAnexo2, boolean anexo2Update) throws Exception;

    void salvarAnexoCancelamento(Integer contrato, UsuarioVO usuarioSessao, String imagemAnexoCancelamento, boolean anexoCancelamentoUpdate) throws Exception;

    JSONObject consultarAlunosCartaoVacina(boolean cadastrados, String filtro, Integer empresa, boolean todos, Integer tipoAnexoCartaoVacina) throws Exception;

    void incluirCartaoVacina(String chave, String token, Integer pessoa, Integer usuarioResponsavel,
                             Integer tipoAnexo,
                             String anexo1, boolean updateAnexo1) throws Exception;

    JSONObject visualizarCartaoVacina(Integer pessoa, String url) throws Exception;

    JSONArray verificarClientesParQPositivo(JSONArray clientesParQPositivo, String nomeOuMatricula, Integer empresaZw) throws Exception;

    public void registrarLog(ContratoAssinaturaDigitalVO obj, UsuarioVO usuarioResponsavel, Integer codigoPessoa) throws Exception;

    public void registrarLogApp(ContratoAssinaturaDigitalVO obj, UsuarioVO usuarioResponsavel, Integer codigoPessoa) throws Exception;
    public JSONObject consultarContratosTermoResponsabilidade(boolean assinados, String filtro, Integer empresa, boolean todos, boolean isTermoResponsabilidadeExAluno) throws Exception;
    public JSONObject selecionarClienteTermoResponsabilidade(final String key, final Integer matricula) throws Exception;
    public void salvarAssinaturaCliente( final Integer matricula, final String assinatura) throws Exception;
    public void removerAssinaturaCliente(final Integer contrato, final Integer usuarioResponsavel, Boolean assinaturaCancelamento, Boolean assinatura2) throws Exception;
    public JSONObject visualizarAssinaturaTermoResponsabilidade(String key, final Integer matricula, final String url) throws Exception;
    public void removerAssinaturaEletronicaCliente(final Integer contrato, final Integer usuarioResponsavel, Boolean assinaturaCancelamento) throws Exception;

    public JSONObject verificaUtilizaTermoResponsabilidade() throws Exception;
}
