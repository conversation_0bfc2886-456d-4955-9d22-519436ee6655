package br.com.pactosolucoes.enumeradores;

public enum TipoFilaEnum {
    ALUNO_TREINO(0),
    ALUNO_PESQUISA_TREINO(1);

    private Integer id;

    TipoFilaEnum(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public static TipoFilaEnum getInstance(Integer id){
        for(TipoFilaEnum t : TipoFilaEnum.values()){
            if(t.getId().equals(id)){
                return t;
            }
        }
        return null;
    }
}
