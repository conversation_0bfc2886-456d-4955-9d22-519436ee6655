package br.com.pactosolucoes.enumeradores;

public enum TipoManutencaoTurmaEnum {

    NENHUM(0, ""),
    PROFESSOR(1, "Manutenção de Professor"),
    REMOVER_ALUNOS(2, "Remover aluno de turma"),
    TRANSFERIR_ALUNOS(3, "Transferir alunos de turma"),
    ;

    TipoManutencaoTurmaEnum(int codigo, String descricao){
        this.codigo = codigo;
        this.descricao = descricao;
    }

    private int codigo;
    private String descricao;

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
