package br.com.pactosolucoes.enumeradores;

/**
 * <AUTHOR>
 *
 */
public enum TipoRateioEnum {

	PLANO_CONTAS(1, "Plano de Contas"), CENTRO_CUSTOS(2,"Centro de Custos");
	
	private int codigo;
	private String descricao;
	
	private TipoRateioEnum(int codigo, String descricao){
		this.codigo = codigo;
		this.descricao = descricao;
	}
	
	
	public TipoRateioEnum getTipoRateioEnum(int tipo){
		TipoRateioEnum tRateio = null;
		for(TipoRateioEnum tipoRateio : TipoRateioEnum.values()){
			if(tipoRateio.getCodigo() == tipo){
				tRateio = tipoRateio;
				break;
			}
		}
		return tRateio;
	}

	/**
	 * @param codigo the codigo to set
	 */
	public void setCodigo(int codigo) {
		this.codigo = codigo;
	}

	/**
	 * @return the codigo
	 */
	public int getCodigo() {
		return codigo;
	}

	/**
	 * @param descricao the descricao to set
	 */
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	/**
	 * @return the descricao
	 */
	public String getDescricao() {
		return descricao;
	}

}
