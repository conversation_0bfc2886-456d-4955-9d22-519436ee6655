package br.com.pactosolucoes.enumeradores;

import negocio.comuns.plano.MarcadorVO;

import java.util.ArrayList;
import java.util.List;

public enum TagsEmailBoasVindasEnum {

    TAG_DATA_CADASTRO(1, "Data de cadastro", "TAG_DATA_CADASTRO"),
    TAG_VENCIMENTO_PLANO(2, "Data de vencimento do plano", "TAG_VENCIMENTO_PLANO"),
    TAG_DESCRICAO_FINANCEIRA(3, "Descrição financeira", "TAG_DESCRICAO_FINANCEIRA"),
    TAG_CARTAO_MASCARADO(4, "Cartão Mascarado", "TAG_CARTAO_MASCARADO"),
    TAG_USUARIO_VENDAS_ONLINE(7, "Usu<PERSON><PERSON> (vendas online)", "USUARIO_VENDAS_ONLINE"),
    TAG_SENHA_VENDAS_ONLINE(8, "Senha (vendas online)", "SENHA_VENDAS_ONLINE"),
    ;

    private Integer codigo;
    private String descricao;
    private String tag;

    private TagsEmailBoasVindasEnum(Integer codigo, String descricao, String tag) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.tag = tag;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public static List<MarcadorVO> getListaTags() {
        List<MarcadorVO> objs = new ArrayList<MarcadorVO>();
        MarcadorVO marcador = new MarcadorVO();
        for (TagsEmailBoasVindasEnum tag : TagsEmailBoasVindasEnum.values()) {
            marcador.setTag(tag.getTag());
            marcador.setNome(tag.getDescricao());
            objs.add(marcador);
            marcador = new MarcadorVO();
        }
        return objs;
    }
}
