package br.com.pactosolucoes.enumeradores;

/**
 * Created by ulisses on 04/09/2015.
 */
public enum SituacaoAtualMetaEnum {

    META_SELECIONADA(1, "Meta escolhida aleatoriamente para ser atendida", "colunaEsquerdaCRMSel, colunaCentroCRMSel, colunaDireitaCRMSel", "corBranca", "corBranca fa-icon-arrow-right", "color: white", "text-align: left; color: white; font-weight: bold"),
    META_NAO_ATENDIDA(2, "Meta ainda não foi atendida", "colunaEsquerdaCRM, colunaCentroCRM, colunaDireitaCRM", "corPretaNaoRealizado", "", "color: #333333", "color: #333333; text-align: left; font-weight: bold"),
    META_NAO_ATENDIDA_CONTATOREALIZADO(3, "Foi realizado um contato com o cliente no dia porém a meta não foi atendida", "colunaEsquerdaCRM, colunaCentroCRM, colunaDireitaCRM", "corPretaRealizado", "corCinza fa-icon-ok-sign", "color: #333333", "color: #333333; text-align: left; font-weight: regular"),
    META_ATENDIDA(4, "Meta já foi atendida", "colunaEsquerdaCRM, colunaCentroCRM, colunaDireitaCRM", "corPretaRealizado", "corVerde fa-icon-ok-sign", "color: #333333", "color: #333333; text-align: left; font-weight: regular");

    private Integer codigo;
    private String descricao;
    private String styleClassPanel;
    private String styleClassLabel;
    private String styleClassIcon;
    private String stylePanel;
    private String styleLabel;

    SituacaoAtualMetaEnum(Integer codigo, String descricao, String styleClassPanel, String styleClassLabel, String styleClassIcon, String stylePanel, String styleLabel) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.styleClassPanel = styleClassPanel;
        this.styleClassLabel = styleClassLabel;
        this.styleClassIcon = styleClassIcon;
        this.stylePanel = stylePanel;
        this.styleLabel = styleLabel;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getStyleClassPanel() {
        return styleClassPanel;
    }

    public void setStyleClassPanel(String styleClassPanel) {
        this.styleClassPanel = styleClassPanel;
    }

    public String getStyleClassLabel() {
        return styleClassLabel;
    }

    public void setStyleClassLabel(String styleClassLabel) {
        this.styleClassLabel = styleClassLabel;
    }

    public String getStyleClassIcon() {
        return styleClassIcon;
    }

    public void setStyleClassIcon(String styleClassIcon) {
        this.styleClassIcon = styleClassIcon;
    }

    public String getStylePanel() {
        return stylePanel;
    }

    public void setStylePanel(String stylePanel) {
        this.stylePanel = stylePanel;
    }

    public String getStyleLabel() {
        return styleLabel;
    }

    public void setStyleLabel(String styleLabel) {
        this.styleLabel = styleLabel;
    }

}
