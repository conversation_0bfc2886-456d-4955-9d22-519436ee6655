/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

import java.util.Arrays;
import java.util.List;
import javax.faces.model.SelectItem;

/**
 *
 * <AUTHOR>
 */
public enum ConfiguracaoBIEnum {

    INCLUIR_PRODUTOS_RECEITA(ConfiguracaoTipoEnum.BOOLEAN,0,BIEnum.TICKET_MEDIO, "Incluir produtos na receita", "true"),
    INCLUIR_PRODUTOS_COMPETENCIA(ConfiguracaoTipoEnum.BOOLEAN,1,BIEnum.TICKET_MEDIO, "Incluir produtos na competência", "false"),
    ALUNOS_BOLSA(ConfiguracaoTipoEnum.BOOLEAN,2,BIEnum.TICKET_MEDIO, "Incluir bolsas no cálculo", "false"),
    ATIVOS(ConfiguracaoTipoEnum.COMBO,3,BIEnum.TICKET_MEDIO, "Ativos", "1",
            new SelectItem(1, "<PERSON>é<PERSON> do mês"),new SelectItem(2, "Quantidade")),
    ASSINATURA_FOTO_ALUNO(ConfiguracaoTipoEnum.BOOLEAN,4,null, "", "true"),
    ASSINATURA_COMPROVANTE_ENDERECO(ConfiguracaoTipoEnum.BOOLEAN,5,null, "", "true"),
    ASSINATURA_DOCUMENTOS(ConfiguracaoTipoEnum.BOOLEAN,6,null, "", "true"),
    ASSINATURA_APTIDAO_FISICA(ConfiguracaoTipoEnum.BOOLEAN,7,null, "", "true"),
    VENDA_RAPIDA(ConfiguracaoTipoEnum.BOOLEAN,8,null, "", "false"),
    FONTE_RECEITA_DESPESA(ConfiguracaoTipoEnum.COMBO,9,BIEnum.TICKET_MEDIO, "Fonte receita\\despesa", "1",
            new SelectItem(1, "Demonstrativo Financeiro" ),new SelectItem(2, "D.R.E Financeiro")),
    CONTAR_DEPENDENTES_COMO_PAGANTES(ConfiguracaoTipoEnum.BOOLEAN, 10, BIEnum.TICKET_MEDIO, "Considerar dependentes como pagantes", "false"),
    FILTRAR_PARCELAS_VENCIDAS_NAO_PAGAS(ConfiguracaoTipoEnum.BOOLEAN,11,BIEnum.TICKET_MEDIO, "Desconsiderar alunos inadimplentes", "false"),

    EXIBIR_AGREGADORES(ConfiguracaoTipoEnum.BOOLEAN, 12, BIEnum.ROTATIVIDADE_CONTRATO, "Exibir clientes de agregadores no BI Movimentação de contrato", "false"),
    QTD_CHECKIN_CONSIDERAR_AGREGADOR(ConfiguracaoTipoEnum.COMBO, 13, BIEnum.ROTATIVIDADE_CONTRATO, "Quantidade de checkins nos últimos dias para ser considerado clientes do contrato com agregadores", "1",
            new SelectItem(1, "01"),
            new SelectItem(2, "02"),
            new SelectItem(3, "03"),
            new SelectItem(4, "04"),
            new SelectItem(5, "05"),
            new SelectItem(6, "06"),
            new SelectItem(7, "07"),
            new SelectItem(8, "08"),
            new SelectItem(9, "09"),
            new SelectItem(10, "10"),
            new SelectItem(11, "11"),
            new SelectItem(12, "12")),
    QTD_DIAS_CONSIDERAR_AGREGADOR(ConfiguracaoTipoEnum.COMBO, 14, BIEnum.ROTATIVIDADE_CONTRATO, "Quantidade de dias a ser apresentado no BI clientes do contrato com agregadores", "30",
            new SelectItem(30, "30"),
            new SelectItem(60, "60"),
            new SelectItem(90, "90")),
    ;
    private final ConfiguracaoTipoEnum tipo;
    private final BIEnum bi;
    private final String nome;
    private final Integer codigo;
    private final String valorPadrao;
    private final List<SelectItem> itens;
    
    ConfiguracaoBIEnum(ConfiguracaoTipoEnum tipo, Integer codigo, BIEnum bi, String nome, String valorPadrao, SelectItem ... itens) {
        this.tipo = tipo;
        this.bi = bi;
        this.nome = nome;
        this.codigo = codigo;
        this.valorPadrao = valorPadrao;
        this.itens = Arrays.asList(itens);
    }
    
    public BIEnum getBi() {
        return bi;
    }

    public String getNome() {
        return nome;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public String getValorPadrao() {
        return valorPadrao;
    }

    public List<SelectItem> getItens() {
        return itens;
    }

    public ConfiguracaoTipoEnum getTipo() {
        return tipo;
    }

    public static ConfiguracaoBIEnum getFromCodigo(Integer cod) {
        for (ConfiguracaoBIEnum cfg : ConfiguracaoBIEnum.values()) {
            if (cfg.getCodigo().equals(cod)) {
                return cfg;
            }
        }
        return null;
    }
    
}
