/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum SinalizadorEnum {
    BDATUALIZANDO(0,"Banco de Dados Atualizando", "Não permite logar no sistema durante uma atualização do banco dados", "Banco de Dados atualizando! Aguarde alguns instantes e tente logar novamente"),
    BDEMBARALHADO(1,"Banco de Dados Embaralhado", "Identifica se o banco de dados já foi embaralhado", "Banco de dados já embaralhado");
    
    private Integer codigo;
    private String nome;
    private String descricao;
    private String mensagem;

    private SinalizadorEnum(Integer codigo, String nome, String descricao, String mensagem) {
        this.codigo = codigo;
        this.nome = nome;
        this.descricao = descricao;
        this.mensagem = mensagem;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public static SinalizadorEnum getFromCodigo(Integer cod){
        for(SinalizadorEnum snd :SinalizadorEnum.values()){
            if(cod != null && snd.getCodigo().equals(cod)){
                return snd;
            }
        }
        return null;
        
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
    
}
