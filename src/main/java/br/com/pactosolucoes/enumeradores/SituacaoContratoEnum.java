/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pactosolucoes.enumeradores;

/**
 *
 * <AUTHOR>
 */
public enum SituacaoContratoEnum {
    MATRICULA("MA"),
    REMATRICULA("RE"),
    RENOVACAO("RN"),
    TRANSFERENCIA("TF");
    
    private String codigo;
    
    private SituacaoContratoEnum(String codigo){
        this.codigo = codigo;
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }
    
    public static SituacaoContratoEnum obterPorCodigo(String codigo){
        for(SituacaoContratoEnum s : values()){
            if(s.getCodigo().equals(codigo)){
                return s;
            }
        }
        return null;
    }
    
}
