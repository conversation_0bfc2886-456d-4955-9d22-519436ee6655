package springboot.dto.campanhacupomdesconto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CampanhaDescontoParcelaDTO {

    private Integer codigo;
    private String descricao;

    public CampanhaDescontoParcelaDTO() {
    }

    public CampanhaDescontoParcelaDTO(Integer codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
