package springboot.dto.campanhacupomdesconto;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.oamd.HistoricoUtilizacaoCupomDescontoVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CampanhaDescontoPlanoDTO {

    private Integer codigo;
    private String descricao;
    private String descricaoApresentar;

    public CampanhaDescontoPlanoDTO() {
    }

    public CampanhaDescontoPlanoDTO(Integer codigo, String descricao, String descricaoApresentar) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.descricaoApresentar = descricaoApresentar;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoApresentar() {
        return descricaoApresentar;
    }

    public void setDescricaoApresentar(String descricaoApresentar) {
        this.descricaoApresentar = descricaoApresentar;
    }
}
