package springboot.dto.comissao;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ComissaoConfiguracaoDTO {

    private boolean pagarComissaoProdutos = false;
    private boolean pagarComissaoSeAtingirMetaFinanceira = false;

    public boolean isPagarComissaoProdutos() {
        return pagarComissaoProdutos;
    }

    public void setPagarComissaoProdutos(boolean pagarComissaoProdutos) {
        this.pagarComissaoProdutos = pagarComissaoProdutos;
    }

    public boolean isPagarComissaoSeAtingirMetaFinanceira() {
        return pagarComissaoSeAtingirMetaFinanceira;
    }

    public void setPagarComissaoSeAtingirMetaFinanceira(boolean pagarComissaoSeAtingirMetaFinanceira) {
        this.pagarComissaoSeAtingirMetaFinanceira = pagarComissaoSeAtingirMetaFinanceira;
    }
}
