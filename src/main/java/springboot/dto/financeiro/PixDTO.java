package springboot.dto.financeiro;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.PixVO;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PixDTO {

    private Integer codigo;
    private Double valor;
    private Integer empresa;
    private Long data;
    private Integer situacao;
    private String situacaoDescricao;
    private String urlQrCode;
    private String qrCodeText;
    private String urlCompartilhar;
    private Integer reciboPagamento;

    public PixDTO() {

    }

    public PixDTO(PixVO obj, String chave) {
        this.codigo = obj.getCodigo();
        this.valor = obj.getValor();
        this.empresa = obj.getEmpresa();
        this.situacao = obj.getStatusEnum().getCodigo();
        this.situacaoDescricao = obj.getStatusEnum().getDescricao();
        this.urlQrCode = obj.obterUrlSomenteImagemQRcode(chave);
        this.qrCodeText = obj.getTextoImagemQrcode();
        this.reciboPagamento = obj.getReciboPagamento();
        this.urlCompartilhar = obj.obterUrlCompartilharQRcode(chave);
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Long getData() {
        return data;
    }

    public void setData(Long data) {
        this.data = data;
    }

    public Integer getSituacao() {
        return situacao;
    }

    public void setSituacao(Integer situacao) {
        this.situacao = situacao;
    }

    public String getUrlQrCode() {
        return urlQrCode;
    }

    public void setUrlQrCode(String urlQrCode) {
        this.urlQrCode = urlQrCode;
    }

    public String getQrCodeText() {
        return qrCodeText;
    }

    public void setQrCodeText(String qrCodeText) {
        this.qrCodeText = qrCodeText;
    }

    public Integer getReciboPagamento() {
        return reciboPagamento;
    }

    public void setReciboPagamento(Integer reciboPagamento) {
        this.reciboPagamento = reciboPagamento;
    }

    public String getSituacaoDescricao() {
        return situacaoDescricao;
    }

    public void setSituacaoDescricao(String situacaoDescricao) {
        this.situacaoDescricao = situacaoDescricao;
    }

    public String getUrlCompartilhar() {
        return urlCompartilhar;
    }

    public void setUrlCompartilhar(String urlCompartilhar) {
        this.urlCompartilhar = urlCompartilhar;
    }
}
