package springboot.dto.financeiro;

import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.TaxaCartaoVO;
import negocio.comuns.financeiro.FormaPagamentoEmpresaVO;
import negocio.comuns.financeiro.FormaPagamentoPerfilAcessoVO;
import negocio.comuns.financeiro.PinPadVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import springboot.dto.comuns.EmpresaDTO;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FormaPagamentoDetalheDTO {

    private Integer codigo;
    private String descricao;
    private String tipoFormaPagamento;
    private String tipoFormaPagamentoDescricao;
    private Double taxaCartao;
    private Double taxaPix;
    private Boolean defaultRecorrencia;
    private Boolean defaultDCO;
    private Boolean ativo;
    private Boolean somenteFinanceiro;
    private Boolean compensacaoDiasUteis;
    private Boolean apresentarNSU;
    private Integer diasCompensacaoCartaoCredito;
    private String cor;
    private Boolean exigeCodAutorizacao;
    private Boolean cartaoDebitoOnline;
    private String tipoDebitoOnline;
    private String merchantId;
    private String merchantKey;
    private Boolean gerarPontos;
    private String tipoParceiro;
    private String tipoConvenioCobranca;
    private Boolean receberSomenteViaPinPad;
    private Integer nrMaxParcelasPinpad;
    private String posIDGeoitd;
    private String systemIdGeoitd;
    private Boolean stoneConnect;
    private Boolean getCard;
    private Boolean taxaPixValorAbsoluto;
    private Boolean contaDeConsumo;

    // Objetos relacionados
    private ConvenioCobrancaDTO convenioCobranca;
    private List<TaxaCartaoDTO> taxasCartao = new ArrayList<TaxaCartaoDTO>();
    private List<FormaPagamentoEmpresaDTO> formasEmpresas = new ArrayList<FormaPagamentoEmpresaDTO>();
    private List<FormaPagamentoPerfilAcessoDTO> formasPerfilAcesso = new ArrayList<FormaPagamentoPerfilAcessoDTO>();
    private List<PinpadDTO> listaPinPad = new ArrayList<PinpadDTO>();
    private PinpadDTO pinpad;

    public FormaPagamentoDetalheDTO() {
    }

    public FormaPagamentoDetalheDTO(FormaPagamentoVO vo) {
        if (vo != null) {
            this.codigo = vo.getCodigo();
            this.descricao = vo.getDescricao();
            this.tipoFormaPagamento = vo.getTipoFormaPagamento();
            
            // Buscar descrição do tipo
            TipoFormaPagto tipo = TipoFormaPagto.getTipoFormaPagtoSigla(vo.getTipoFormaPagamento());
            this.tipoFormaPagamentoDescricao = tipo != null ? tipo.getDescricao() : "";
            
            this.taxaCartao = vo.getTaxaCartao();
            this.taxaPix = vo.getTaxaPix();
            this.defaultRecorrencia = vo.getDefaultRecorrencia();
            this.defaultDCO = vo.getDefaultDCO();
            this.ativo = vo.isAtivo();
            this.somenteFinanceiro = vo.isSomenteFinanceiro();
            this.compensacaoDiasUteis = vo.isCompensacaoDiasUteis();
            this.apresentarNSU = vo.isApresentarNSU();
            this.diasCompensacaoCartaoCredito = vo.getDiasCompensacaoCartaoCredito();
            this.cor = vo.getCor();
            this.exigeCodAutorizacao = vo.isExigeCodAutorizacao();
            this.cartaoDebitoOnline = vo.isCartaoDebitoOnline();
            this.tipoDebitoOnline = vo.getTipoDebitoOnline() != null ? vo.getTipoDebitoOnline().name() : null;
            this.merchantId = vo.getMerchantid();
            this.merchantKey = vo.getMerchantkey();
            this.gerarPontos = vo.isGerarPontos();
            this.tipoParceiro = vo.getTipoParceiro() != null ? vo.getTipoParceiro().name() : null;
            this.tipoConvenioCobranca = vo.getTipoConvenioCobranca() != null ? vo.getTipoConvenioCobranca().name() : null;
            this.receberSomenteViaPinPad = vo.isReceberSomenteViaPinPad();
            this.nrMaxParcelasPinpad = vo.getNrMaxParcelasPinpad();
            this.posIDGeoitd = vo.getPosIDGeoitd();
            this.systemIdGeoitd = vo.getSystemIdGeoitd();
            this.stoneConnect = vo.isStoneConnect();
            this.getCard = vo.isGetCard();
            this.taxaPixValorAbsoluto = vo.isTaxaPixValorAbsoluto();
            this.contaDeConsumo = vo.isContaDeConsumo();

            // Converter objetos relacionados
            if (vo.getConvenioCobrancaVO() != null && vo.getConvenioCobrancaVO().getCodigo() != 0) {
                this.convenioCobranca = new ConvenioCobrancaDTO(vo.getConvenioCobrancaVO());
            }

            if (vo.getTaxasCartao() != null) {
                for (TaxaCartaoVO taxa : vo.getTaxasCartao()) {
                    this.taxasCartao.add(new TaxaCartaoDTO(taxa));
                }
            }

            if (vo.getFormasEmpresas() != null) {
                for (FormaPagamentoEmpresaVO forma : vo.getFormasEmpresas()) {
                    this.formasEmpresas.add(new FormaPagamentoEmpresaDTO(forma));
                }
            }

            if (vo.getFormasPerfilAcesso() != null) {
                for (FormaPagamentoPerfilAcessoVO forma : vo.getFormasPerfilAcesso()) {
                    this.formasPerfilAcesso.add(new FormaPagamentoPerfilAcessoDTO(forma));
                }
            }

            if (vo.getListaPinPad() != null) {
                for (PinPadVO pinpad : vo.getListaPinPad()) {
                    this.listaPinPad.add(new PinpadDTO(pinpad));
                }
            }

            if (vo.getPinpad() != null) {
                this.pinpad = new PinpadDTO(vo.getPinpad());
            }
        }
    }

    // Getters e Setters
    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getTipoFormaPagamento() {
        return tipoFormaPagamento;
    }

    public void setTipoFormaPagamento(String tipoFormaPagamento) {
        this.tipoFormaPagamento = tipoFormaPagamento;
    }

    public String getTipoFormaPagamentoDescricao() {
        return tipoFormaPagamentoDescricao;
    }

    public void setTipoFormaPagamentoDescricao(String tipoFormaPagamentoDescricao) {
        this.tipoFormaPagamentoDescricao = tipoFormaPagamentoDescricao;
    }

    public Double getTaxaCartao() {
        return taxaCartao;
    }

    public void setTaxaCartao(Double taxaCartao) {
        this.taxaCartao = taxaCartao;
    }

    public Double getTaxaPix() {
        return taxaPix;
    }

    public void setTaxaPix(Double taxaPix) {
        this.taxaPix = taxaPix;
    }

    public Boolean getDefaultRecorrencia() {
        return defaultRecorrencia;
    }

    public void setDefaultRecorrencia(Boolean defaultRecorrencia) {
        this.defaultRecorrencia = defaultRecorrencia;
    }

    public Boolean getDefaultDCO() {
        return defaultDCO;
    }

    public void setDefaultDCO(Boolean defaultDCO) {
        this.defaultDCO = defaultDCO;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public Boolean getSomenteFinanceiro() {
        return somenteFinanceiro;
    }

    public void setSomenteFinanceiro(Boolean somenteFinanceiro) {
        this.somenteFinanceiro = somenteFinanceiro;
    }

    public Boolean getCompensacaoDiasUteis() {
        return compensacaoDiasUteis;
    }

    public void setCompensacaoDiasUteis(Boolean compensacaoDiasUteis) {
        this.compensacaoDiasUteis = compensacaoDiasUteis;
    }

    public Boolean getApresentarNSU() {
        return apresentarNSU;
    }

    public void setApresentarNSU(Boolean apresentarNSU) {
        this.apresentarNSU = apresentarNSU;
    }

    public Integer getDiasCompensacaoCartaoCredito() {
        return diasCompensacaoCartaoCredito;
    }

    public void setDiasCompensacaoCartaoCredito(Integer diasCompensacaoCartaoCredito) {
        this.diasCompensacaoCartaoCredito = diasCompensacaoCartaoCredito;
    }

    public String getCor() {
        return cor;
    }

    public void setCor(String cor) {
        this.cor = cor;
    }

    public Boolean getExigeCodAutorizacao() {
        return exigeCodAutorizacao;
    }

    public void setExigeCodAutorizacao(Boolean exigeCodAutorizacao) {
        this.exigeCodAutorizacao = exigeCodAutorizacao;
    }

    public Boolean getCartaoDebitoOnline() {
        return cartaoDebitoOnline;
    }

    public void setCartaoDebitoOnline(Boolean cartaoDebitoOnline) {
        this.cartaoDebitoOnline = cartaoDebitoOnline;
    }

    public String getTipoDebitoOnline() {
        return tipoDebitoOnline;
    }

    public void setTipoDebitoOnline(String tipoDebitoOnline) {
        this.tipoDebitoOnline = tipoDebitoOnline;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantKey() {
        return merchantKey;
    }

    public void setMerchantKey(String merchantKey) {
        this.merchantKey = merchantKey;
    }

    public Boolean getGerarPontos() {
        return gerarPontos;
    }

    public void setGerarPontos(Boolean gerarPontos) {
        this.gerarPontos = gerarPontos;
    }

    public String getTipoParceiro() {
        return tipoParceiro;
    }

    public void setTipoParceiro(String tipoParceiro) {
        this.tipoParceiro = tipoParceiro;
    }

    public String getTipoConvenioCobranca() {
        return tipoConvenioCobranca;
    }

    public void setTipoConvenioCobranca(String tipoConvenioCobranca) {
        this.tipoConvenioCobranca = tipoConvenioCobranca;
    }

    public Boolean getReceberSomenteViaPinPad() {
        return receberSomenteViaPinPad;
    }

    public void setReceberSomenteViaPinPad(Boolean receberSomenteViaPinPad) {
        this.receberSomenteViaPinPad = receberSomenteViaPinPad;
    }

    public Integer getNrMaxParcelasPinpad() {
        return nrMaxParcelasPinpad;
    }

    public void setNrMaxParcelasPinpad(Integer nrMaxParcelasPinpad) {
        this.nrMaxParcelasPinpad = nrMaxParcelasPinpad;
    }

    public String getPosIDGeoitd() {
        return posIDGeoitd;
    }

    public void setPosIDGeoitd(String posIDGeoitd) {
        this.posIDGeoitd = posIDGeoitd;
    }

    public String getSystemIdGeoitd() {
        return systemIdGeoitd;
    }

    public void setSystemIdGeoitd(String systemIdGeoitd) {
        this.systemIdGeoitd = systemIdGeoitd;
    }

    public Boolean getStoneConnect() {
        return stoneConnect;
    }

    public void setStoneConnect(Boolean stoneConnect) {
        this.stoneConnect = stoneConnect;
    }

    public Boolean getGetCard() {
        return getCard;
    }

    public void setGetCard(Boolean getCard) {
        this.getCard = getCard;
    }

    public Boolean getTaxaPixValorAbsoluto() {
        return taxaPixValorAbsoluto;
    }

    public void setTaxaPixValorAbsoluto(Boolean taxaPixValorAbsoluto) {
        this.taxaPixValorAbsoluto = taxaPixValorAbsoluto;
    }

    public Boolean getContaDeConsumo() {
        return contaDeConsumo;
    }

    public void setContaDeConsumo(Boolean contaDeConsumo) {
        this.contaDeConsumo = contaDeConsumo;
    }

    public ConvenioCobrancaDTO getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(ConvenioCobrancaDTO convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public List<TaxaCartaoDTO> getTaxasCartao() {
        return taxasCartao;
    }

    public void setTaxasCartao(List<TaxaCartaoDTO> taxasCartao) {
        this.taxasCartao = taxasCartao;
    }

    public List<FormaPagamentoEmpresaDTO> getFormasEmpresas() {
        return formasEmpresas;
    }

    public void setFormasEmpresas(List<FormaPagamentoEmpresaDTO> formasEmpresas) {
        this.formasEmpresas = formasEmpresas;
    }

    public List<FormaPagamentoPerfilAcessoDTO> getFormasPerfilAcesso() {
        return formasPerfilAcesso;
    }

    public void setFormasPerfilAcesso(List<FormaPagamentoPerfilAcessoDTO> formasPerfilAcesso) {
        this.formasPerfilAcesso = formasPerfilAcesso;
    }

    public List<PinpadDTO> getListaPinPad() {
        return listaPinPad;
    }

    public void setListaPinPad(List<PinpadDTO> listaPinPad) {
        this.listaPinPad = listaPinPad;
    }

    public PinpadDTO getPinpad() {
        return pinpad;
    }

    public void setPinpad(PinpadDTO pinpad) {
        this.pinpad = pinpad;
    }
}
