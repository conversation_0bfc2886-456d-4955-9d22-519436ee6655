package springboot.dto.financeiro;

import com.fasterxml.jackson.annotation.JsonInclude;
import springboot.dto.comuns.MovParcelaDTO;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ValidacaoParcelasDTO {

    private boolean sucesso = false;
    private List<MovParcelaDTO> parcelasSPC;
    private List<MovParcelaDTO> parcelasPendenteTransacaoRemessa;
    private List<MovParcelaDTO> parcelasPendenteBoleto;
    private List<BoletoDTO> boletosPendentes;

    public ValidacaoParcelasDTO() {

    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public List<MovParcelaDTO> getParcelasSPC() {
        return parcelasSPC;
    }

    public void setParcelasSPC(List<MovParcelaDTO> parcelasSPC) {
        this.parcelasSPC = parcelasSPC;
    }

    public List<MovParcelaDTO> getParcelasPendenteTransacaoRemessa() {
        return parcelasPendenteTransacaoRemessa;
    }

    public void setParcelasPendenteTransacaoRemessa(List<MovParcelaDTO> parcelasPendenteTransacaoRemessa) {
        this.parcelasPendenteTransacaoRemessa = parcelasPendenteTransacaoRemessa;
    }

    public List<MovParcelaDTO> getParcelasPendenteBoleto() {
        return parcelasPendenteBoleto;
    }

    public void setParcelasPendenteBoleto(List<MovParcelaDTO> parcelasPendenteBoleto) {
        this.parcelasPendenteBoleto = parcelasPendenteBoleto;
    }

    public List<BoletoDTO> getBoletosPendentes() {
        return boletosPendentes;
    }

    public void setBoletosPendentes(List<BoletoDTO> boletosPendentes) {
        this.boletosPendentes = boletosPendentes;
    }
}
