package springboot.dto.financeiro;

import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CartaoPagamentoDTO {

    private Integer formaPagamento;
    private Integer convenioCobranca;
    private Integer operadoraCartao;
    private Integer nrParcelas;
    private Integer tipoParcelamento;
    private String tokenAragorn;
    private String numero;
    private Integer mesValidade;
    private Integer anoValidade;
    private String cvv;
    private String titular;
    private String documento;
    private String bandeira;

    public CartaoPagamentoDTO() {

    }

    public CartaoPagamentoDTO(AutorizacaoCobrancaClienteVO obj) {
        this.tokenAragorn = obj.getTokenAragorn();
        this.numero = obj.getNumeroCartao().contains("***") ? obj.getNumeroCartao() : "";
        this.titular = obj.getNomeTitularCartao();
        this.documento = obj.getCpfTitular();
        this.mesValidade = obj.getMesValidade();
        this.anoValidade = obj.getAnoValidade();
        this.bandeira = (obj.getOperadoraCartao() != null ? obj.getOperadoraCartao().name() : "");
    }

    public CartaoPagamentoDTO(AutorizacaoCobrancaColaboradorVO obj) {
        this.tokenAragorn = obj.getTokenAragorn();
        this.numero = obj.getNumeroCartao().contains("***") ? obj.getNumeroCartao() : "";
        this.titular = obj.getNomeTitularCartao();
        this.documento = obj.getCpfTitular();
        this.mesValidade = obj.getMesValidade();
        this.anoValidade = obj.getAnoValidade();
        this.bandeira = (obj.getOperadoraCartao() != null ? obj.getOperadoraCartao().name() : "");
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public String getTitular() {
        return titular;
    }

    public void setTitular(String titular) {
        this.titular = titular;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getTokenAragorn() {
        return tokenAragorn;
    }

    public void setTokenAragorn(String tokenAragorn) {
        this.tokenAragorn = tokenAragorn;
    }

    public Integer getTipoParcelamento() {
        return tipoParcelamento;
    }

    public void setTipoParcelamento(Integer tipoParcelamento) {
        this.tipoParcelamento = tipoParcelamento;
    }

    public Integer getFormaPagamento() {
        return formaPagamento;
    }

    public void setFormaPagamento(Integer formaPagamento) {
        this.formaPagamento = formaPagamento;
    }

    public Integer getConvenioCobranca() {
        return convenioCobranca;
    }

    public void setConvenioCobranca(Integer convenioCobranca) {
        this.convenioCobranca = convenioCobranca;
    }

    public Integer getNrParcelas() {
        return nrParcelas;
    }

    public void setNrParcelas(Integer nrParcelas) {
        this.nrParcelas = nrParcelas;
    }

    public Integer getOperadoraCartao() {
        return operadoraCartao;
    }

    public void setOperadoraCartao(Integer operadoraCartao) {
        this.operadoraCartao = operadoraCartao;
    }

    public Integer getMesValidade() {
        return mesValidade;
    }

    public void setMesValidade(Integer mesValidade) {
        this.mesValidade = mesValidade;
    }

    public Integer getAnoValidade() {
        return anoValidade;
    }

    public void setAnoValidade(Integer anoValidade) {
        this.anoValidade = anoValidade;
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }
}
