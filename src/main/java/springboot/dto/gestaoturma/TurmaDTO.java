package springboot.dto.gestaoturma;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TurmaDTO {
    
    private Integer codigo;
    private String label;
    private String turma;
    private String professor;
    private String dia;
    private String horario;
    
    public TurmaDTO() {
    }
    
    public TurmaDTO(Integer codigo, String label) {
        this.codigo = codigo;
        this.label = label;
    }
    
    public Integer getCodigo() {
        return codigo;
    }
    
    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public String getTurma() {
        return turma;
    }
    
    public void setTurma(String turma) {
        this.turma = turma;
    }
    
    public String getProfessor() {
        return professor;
    }
    
    public void setProfessor(String professor) {
        this.professor = professor;
    }
    
    public String getDia() {
        return dia;
    }
    
    public void setDia(String dia) {
        this.dia = dia;
    }
    
    public String getHorario() {
        return horario;
    }
    
    public void setHorario(String horario) {
        this.horario = horario;
    }
}
