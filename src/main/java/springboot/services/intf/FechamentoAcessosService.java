package springboot.services.intf;

import controle.arquitetura.exceptions.ServiceException;
import org.json.JSONObject;
import springboot.dto.fechamentoacessos.FechamentoAcessosFiltroDTO;
import springboot.dto.fechamentoacessos.FechamentoAcessosResultadoDTO;

import javax.servlet.http.HttpServletRequest;

/**
 * Interface do servi?o de Fechamento de Acessos
 * Baseado no FechamentoAcessosControleRel.java
 * 
 * <AUTHOR>
 */
public interface FechamentoAcessosService {

    /**
     * Consulta os dados do relat?rio de fechamento de acessos
     * Equivalente ao m?todo pesquisar() do JSF
     * 
     * @param filtro Filtros de consulta
     * @param empresaId ID da empresa
     * @return Resultado da consulta com todos os valores totais
     * @throws ServiceException
     */
    FechamentoAcessosResultadoDTO consultarDadosRelatorio(FechamentoAcessosFiltroDTO filtro,
                                                         Integer empresaId) throws ServiceException;

    String consultarEmailsFechamentoAcesso() throws ServiceException;

    void gravarEmailsFechamentoAcesso(String emails) throws ServiceException;

    String imprimirRelatorioPDF(HttpServletRequest request, Integer empresa, JSONObject filtros) throws ServiceException;

    void autorizarEnvioEmail(Integer empresaId, JSONObject filtros, HttpServletRequest request) throws ServiceException;
}
