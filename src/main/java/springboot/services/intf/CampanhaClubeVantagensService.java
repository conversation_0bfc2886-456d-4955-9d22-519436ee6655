package springboot.services.intf;

import negocio.comuns.arquitetura.PaginadorDTO;
import org.json.JSONObject;
import springboot.dto.campanhaclubevantagems.CampanhaClubeVantagensDTO;
import springboot.dto.comuns.ItemGenericoDTO;

import java.util.List;

public interface CampanhaClubeVantagensService {

    /**
     * Consulta campanhas do clube de vantagens com filtros e paginação
     * @param filters Filtros de consulta
     * @param empresa ID da empresa
     * @param paginadorDTO Dados de paginação
     * @return Lista de campanhas
     * @throws Exception
     */
    List<CampanhaClubeVantagensDTO> consultar(JSONObject filters, Integer empresa, PaginadorDTO paginadorDTO) throws Exception;

    /**
     * Consulta campanha por código
     * @param codigo Código da campanha
     * @return Dados da campanha
     * @throws Exception
     */
    CampanhaClubeVantagensDTO consultarPorCodigo(Integer codigo) throws Exception;

    /**
     * Salva uma campanha (inclusão ou alteração)
     * @param dto Dados da campanha
     * @throws Exception
     */
    void salvar(CampanhaClubeVantagensDTO dto) throws Exception;

    /**
     * Exclui uma campanha
     * @param codigo Código da campanha
     * @throws Exception
     */
    void excluir(Integer codigo) throws Exception;

    /**
     * Obtém lista de tipos de item de campanha
     * @return Lista de tipos de item
     * @throws Exception
     */
    List<ItemGenericoDTO> obterListaTiposItemCampanha();
}
