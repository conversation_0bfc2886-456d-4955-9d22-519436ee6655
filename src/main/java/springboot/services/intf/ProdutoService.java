package springboot.services.intf;

import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.PaginadorDTO;
import springboot.dto.comuns.CategoriaProdutoDTO;
import springboot.dto.comuns.ItemGenericoDTO;
import springboot.dto.comuns.ProdutoDTO;
import springboot.dto.plano.ProdutoDetalheDTO;
import springboot.dto.plano.ProdutoFiltroDTO;
import springboot.dto.produto.ProdutoVisualizacaoRegrasDTO;

import java.util.List;

public interface ProdutoService {

    /**
     * Consulta produtos com pagina??o
     * @param filtro Filtros de consulta
     * @param paginadorDTO Configura??es de pagina??o
     * @return Lista paginada de produtos
     * @throws ServiceException em caso de erro
     */
    List<ProdutoDTO> consultarProdutos(ProdutoFiltroDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException;

    /**
     * Busca um produto por c?digo
     * @param codigo C?digo do produto
     * @return Produto encontrado
     * @throws ServiceException em caso de erro
     */
    ProdutoDetalheDTO obterPorCodigo(Integer codigo) throws ServiceException;

    /**
     * Cria um novo produto
     * @param produtoDTO Dados do produto
     * @return Produto criado
     * @throws ServiceException em caso de erro
     */
    ProdutoDetalheDTO criarProduto(ProdutoDetalheDTO produtoDTO) throws ServiceException;

    /**
     * Atualiza um produto existente
     * @param codigo C?digo do produto
     * @param produtoDTO Novos dados do produto
     * @return Produto atualizado
     * @throws ServiceException em caso de erro
     */
    ProdutoDetalheDTO atualizarProduto(Integer codigo, ProdutoDetalheDTO produtoDTO) throws ServiceException;

    /**
     * Remove um produto
     * @param codigo C?digo do produto
     * @throws ServiceException em caso de erro
     */
    void removerProduto(Integer codigo) throws ServiceException;

    /**
     * Lista categorias de produto dispon?veis
     * @return Lista de categorias
     * @throws ServiceException em caso de erro
     */
    List<CategoriaProdutoDTO> listarCategoriasProduto() throws ServiceException;

    /**
     * Lista tipos de produto dispon?veis
     * @return Lista de tipos com c?digo e descri??o
     * @throws ServiceException em caso de erro
     */
    List<ItemGenericoDTO> listarTiposProduto() throws ServiceException;

    /**
     * Lista unidades de medida dispon?veis
     * @return Lista de unidades de medida com c?digo e descri??o
     * @throws ServiceException em caso de erro
     */
    List<ItemGenericoDTO> listarUnidadesMedida() throws ServiceException;

    /**
     * Lista tipos de vig?ncia dispon?veis
     * @return Lista de tipos de vig?ncia com c?digo e descri??o
     * @throws ServiceException em caso de erro
     */
    List<ItemGenericoDTO> listarTiposVigencia() throws ServiceException;

    /**
     * Valida se um produto pode ser exclu?do
     * @param codigo C?digo do produto
     * @return true se pode ser exclu?do, false caso contr?rio
     * @throws ServiceException em caso de erro
     */
    Boolean validarExclusaoProduto(Integer codigo) throws ServiceException;

    /**
     * Busca produtos por c?digo de barras
     * @param codigoBarras C?digo de barras
     * @return Lista de produtos encontrados
     * @throws ServiceException em caso de erro
     */
    List<ProdutoDTO> buscarPorCodigoBarras(String codigoBarras) throws ServiceException;

    /**
     * Busca produtos por descri??o
     * @param descricao Descri??o do produto
     * @return Lista de produtos encontrados
     * @throws ServiceException em caso de erro
     */
    List<ProdutoDTO> buscarPorDescricao(String descricao) throws ServiceException;

    List<ProdutoDTO> consultarMinimal(ProdutoFiltroDTO filtroDTO, PaginadorDTO paginadorDTO) throws ServiceException;

    /**
     * Obter regras de visualiza??o para o formul?rio de produto
     * @param codigoProduto C?digo do produto (opcional, para produto existente)
     * @param tipoProduto Tipo do produto (opcional, para novo produto)
     * @param empresaId ID da empresa
     * @return DTO com as regras de visualiza??o
     * @throws ServiceException em caso de erro
     */
    ProdutoVisualizacaoRegrasDTO obterRegrasVisualizacao(Integer codigoProduto, String tipoProduto, Integer empresaId) throws ServiceException;

    List<springboot.dto.plano.PlanoDTO> listarPlanosPorEmpresa(Integer empresaId) throws ServiceException;
}
