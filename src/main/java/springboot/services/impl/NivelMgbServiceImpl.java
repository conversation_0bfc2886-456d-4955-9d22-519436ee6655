package springboot.services.impl;

import com.pacto.config.security.interfaces.RequestService;
import controle.arquitetura.exceptions.ServiceException;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import servicos.integracao.mgb.impl.MgbServiceImpl;
import springboot.bd.intf.ConexaoZWService;
import springboot.dto.mgb.NivelMgbDTO;
import springboot.services.intf.NivelMgbService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

@Service
public class NivelMgbServiceImpl implements NivelMgbService {

    private final ConexaoZWService conexaoZWService;
    private final RequestService requestService;

    public NivelMgbServiceImpl(ConexaoZWService conexaoZWService, RequestService requestService) {
        this.conexaoZWService = conexaoZWService;
        this.requestService = requestService;
    }

    @Override
    public List<NivelMgbDTO> consultarNiveis(Integer empresa) throws ServiceException {
        String key = this.requestService.getUsuarioAtual().getChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            MgbServiceImpl mgbService = new MgbServiceImpl(con);
            
            if (!mgbService.integradoMgb(empresa)) {
                throw new ServiceException("A integração com o MGB não está ativa para esta empresa");
            }
            
            JSONObject jsonObject = mgbService.consultarNiveis(empresa);
            List<NivelMgbDTO> niveis = new ArrayList<>();
            
            if (jsonObject.has("data")) {
                JSONArray lista = new JSONArray(jsonObject.get("data").toString());
                
                for (int i = 0; i < lista.length(); i++) {
                    JSONObject obj = lista.getJSONObject(i);
                    String publicId = obj.optString("publicId");
                    String nome = obj.optString("namePTBR");
                    
                    if (publicId != null && !publicId.isEmpty() && nome != null && !nome.isEmpty()) {
                        niveis.add(new NivelMgbDTO(publicId, nome));
                    }
                }
            }
            
            return niveis;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("Erro ao consultar níveis MGB: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean verificarIntegracao(Integer empresa) throws ServiceException {
        String key = this.requestService.getUsuarioAtual().getChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            MgbServiceImpl mgbService = new MgbServiceImpl(con);
            return mgbService.integradoMgb(empresa);
        } catch (Exception e) {
            throw new ServiceException("Erro ao verificar integração MGB: " + e.getMessage(), e);
        }
    }
}
