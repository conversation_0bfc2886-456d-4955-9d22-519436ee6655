package springboot.services.impl;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.TipoConsultaCupomDescontoEnum;
import com.pacto.config.security.interfaces.RequestService;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.oamd.CampanhaCupomDesconto;
import negocio.facade.jdbc.oamd.CampanhaCupomDescontoJSON;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.PlanoDuracao;
import negocio.oamd.*;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import servicos.impl.oamd.OAMDService;
import springboot.bd.intf.ConexaoZWService;
import springboot.dto.campanhacupomdesconto.*;
import springboot.services.intf.CampanhaCupomDescontoService;

import javax.faces.model.SelectItem;
import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CampanhaCupomDescontoServiceImpl implements CampanhaCupomDescontoService {

    private RequestService requestService;
    private ConexaoZWService conexaoZWService;

    public CampanhaCupomDescontoServiceImpl(RequestService requestService,
                                            ConexaoZWService conexaoZWService) throws Exception {
        this.requestService = requestService;
        this.conexaoZWService = conexaoZWService;
    }

    private String obterChave() {
        return this.requestService.getUsuarioAtual().getChave();
    }

    public List<CampanhaCupomDescontoDTO> consultar(JSONObject filters, Integer empresa, PaginadorDTO paginadorDTO) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Empresa empresaDAO = new Empresa(con);
            CampanhaCupomDesconto campanhaCupomDescontoDAO = new CampanhaCupomDesconto();

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = filters != null ? filters.optInt("empresa") : 0;
            }
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresa;
            }
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            Integer idFavorecido = empresaVO.getCodEmpresaFinanceiro();
            RedeEmpresaVO redeEmpresaVO = OAMDService.consultarRedeEmpresa(key);

            String chaveRede = null;
            if (redeEmpresaVO != null) {
                chaveRede = redeEmpresaVO.getChaverede();
            }

            List<CampanhaCupomDescontoJSON> listaVO = campanhaCupomDescontoDAO.consultarCampanhaCupomDescontoJSONOAMD(chaveRede, idFavorecido, false);
            List<CampanhaCupomDescontoDTO> listaDTO = new ArrayList<>();

            for (CampanhaCupomDescontoJSON obj : listaVO) {
                CampanhaCupomDescontoDTO dto = new CampanhaCupomDescontoDTO();
                    dto.setId(Integer.parseInt(obj.getId()));
                    dto.setDescricaoCampanha(obj.getDescricaoCampanha());
                    try {
                        dto.setVigenciaInicial(obj.getVigenciaInicial() != null ? Uteis.getDate(obj.getVigenciaInicial(), "dd/MM/yyyy HH:mm:ss").getTime() : null);
                        dto.setVigenciaFinal(obj.getVigenciaFinal() != null ? Uteis.getDate(obj.getVigenciaFinal(), "dd/MM/yyyy HH:mm:ss").getTime() : null);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                listaDTO.add(dto);
            }

            int indiceInicial = paginadorDTO.getPage().intValue() * paginadorDTO.getSize().intValue();
            int maxResults = (indiceInicial + paginadorDTO.getSize().intValue());
            paginadorDTO.setQuantidadeTotalElementos((long) listaDTO.size());

            if (maxResults > listaDTO.size()) {
                maxResults = listaDTO.size();
            }

            List<CampanhaCupomDescontoDTO> listaFinal;
            if (indiceInicial < listaDTO.size()) {
                listaFinal = listaDTO.subList(indiceInicial, maxResults);
            } else {
                listaFinal = new ArrayList<>();
            }
            return listaFinal;
        }
    }

    public List<CampanhaDescontoParcelaDTO> obterListaParcelas() throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            List<CampanhaDescontoParcelaDTO> lista = new ArrayList<>();
            Integer meses = 12;
            try {
                PlanoDuracao planoDuracaoDAO = new PlanoDuracao(con);
                meses = planoDuracaoDAO.obterPlanoDuracaoMesesMaximo();
            } catch (Exception ignored){}

            if (UteisValidacao.emptyNumber(meses) || meses < 12) {
                meses = 12; //quantidade mínima
            }

            for (int i = 1; i <= meses; i++) {
                String desc = "PARCELA " + i;
                lista.add(new CampanhaDescontoParcelaDTO(i, desc));
            }
            return lista;
        }
    }

    public List<CampanhaDescontoPlanoDTO> obterListaPlanos() throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Plano planoDAO = new Plano(con);
            List<CampanhaDescontoPlanoDTO> lista = new ArrayList<>();
            List<PlanoVO> planos = planoDAO.consultarTodos(Calendario.hoje(), Uteis.NIVELMONTARDADOS_TELACONSULTA);
            for (PlanoVO planoVO : planos) {
                lista.add(new CampanhaDescontoPlanoDTO(planoVO.getCodigo(), planoVO.getDescricao(), planoVO.getDescricao() + " | " + planoVO.getEmpresa().getNome()));
            }
            return lista;
        }
    }

    public void salvar(CampanhaCupomDescontoDTO dto) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {

            Log logDAO = new Log(con);
            Usuario usuarioDAO = new Usuario(con);
            Empresa empresaDAO = new Empresa(con);
            OAMDService oamdService = new OAMDService();
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(this.requestService.getUsuarioAtual().getCodZw(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(this.requestService.getUsuarioAtual().getIdEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            CampanhaCupomDescontoVO campanhaCupomDescontoVO = dto.toCampanhaCupomDescontoVO();

            if (campanhaCupomDescontoVO.getAplicarParaRede()) {
//                    campanhaCupomDescontoVO.setRedeEmpresaVO(getRedeEmpresaVO());
                campanhaCupomDescontoVO.setEmpresaFinanceiroVO(null);
            } else {
                EmpresaFinanceiroVO empresaFinanceiroVO = new EmpresaFinanceiroVO(empresaVO.getCodEmpresaFinanceiro());
                campanhaCupomDescontoVO.setRedeEmpresaVO(null);
                campanhaCupomDescontoVO.setEmpresaFinanceiroVO(empresaFinanceiroVO);
            }

            montarCampoRestricaoPlano();
            if (campanhaCupomDescontoVO.getId() == null || campanhaCupomDescontoVO.getId() == 0) {
                campanhaCupomDescontoVO = oamdService.incluirCampanhaCupomDesconto(campanhaCupomDescontoVO, usuarioVO, key, logDAO);

                try {
                    SuperControle.notificarRecursoEmpresa(con, key, RecursoSistema.CADASTRO_NOVA_CAMPANHA_CUPOM_DESCONTO, empresaVO.getCodigo().toString(), usuarioVO.getCodigo().toString());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

            } else {
                campanhaCupomDescontoVO = oamdService.alterarCampanhaCupomDesconto(campanhaCupomDescontoVO, usuarioVO, key, logDAO);
            }
            campanhaCupomDescontoVO.setAplicarParaRede(campanhaCupomDescontoVO.getRedeEmpresaVO() != null && campanhaCupomDescontoVO.getRedeEmpresaVO().getId() != 0);
            campanhaCupomDescontoVO.registrarObjetoVOAntesDaAlteracao();
        }
    }

    private void montarCampoRestricaoPlano() {
//        Collections.sort(this.listaRestricaoPlano);
//        if (this.listaRestricaoPlano.size() > 0) {
//            StringBuilder desc = new StringBuilder();
//            for (String plano : this.listaRestricaoPlano) {
//                if (desc.length() <= 0) {
//                    desc.append(plano);
//                } else {
//                    desc.append(";").append(plano);
//                }
//            }
//            if (this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha() != null) {
//                if (!this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha().equals(desc.toString())) {
//                    this.campanhaCupomDescontoVO.setPlanosQueParticiparaoDaCampanha(desc.toString());
//                }
//            } else {
//                this.campanhaCupomDescontoVO.setPlanosQueParticiparaoDaCampanha(desc.toString());
//            }
//
//        } else {
//            if ((this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha() != null) && (!this.campanhaCupomDescontoVO.getPlanosQueParticiparaoDaCampanha().trim().equals(""))) {
//                this.campanhaCupomDescontoVO.setPlanosQueParticiparaoDaCampanha(null);
//            }
//        }
    }

    public void excluir(Integer codigo) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            OAMDService oamdService = new OAMDService();
            CampanhaCupomDescontoVO campanhaCupomDescontoVO = oamdService.consultarCampanhaCupomDescontoPorId(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            oamdService.excluirCampanhaCupomDesconto(campanhaCupomDescontoVO);
        }
    }

    public CampanhaCupomDescontoDTO consultarPorCodigo(Integer codigo) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            OAMDService oamdService = new OAMDService();
            CampanhaCupomDescontoVO campanhaCupomDescontoVO = oamdService.consultarCampanhaCupomDescontoPorId(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            return new CampanhaCupomDescontoDTO(campanhaCupomDescontoVO);
        }
    }

    public void gravarLoteCupomDesconto(NovoLoteCupomDescontoDTO dto) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Log logDAO = new Log(con);
            Usuario usuarioDAO = new Usuario(con);
            Empresa empresaDAO = new Empresa(con);
            OAMDService oamdService = new OAMDService();
            CampanhaCupomDescontoVO campanhaCupomDescontoVO = oamdService.consultarCampanhaCupomDescontoPorId(dto.getCampanhaCupomDesconto(), Uteis.NIVELMONTARDADOS_TODOS);
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(this.requestService.getUsuarioAtual().getCodZw(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(dto.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            if (!dto.isGerarCupomAleatorio()) {
                if (UteisValidacao.emptyString(dto.getNomeCupom())) {
                    throw new ConsistirException("Informe um nome para o cupom.");
                }
                CupomDescontoVO cupomDescontoVO = oamdService.consultarPorNumeroCupom(dto.getNomeCupom(), empresaVO.getCodEmpresaFinanceiro());
                if (cupomDescontoVO != null) {
                    throw new ConsistirException("Um cupom com este mesmo nome já foi cadastrado em outra Campanha.");
                }
            }
            if (UteisValidacao.emptyNumber(dto.getQuantidadeCupons())) {
                throw new ConsistirException("Informe uma quantidade de cupons maior que zero.");
            }
            oamdService.gerarNovoLoteCupomDesconto(usuarioVO, campanhaCupomDescontoVO, dto.getQuantidadeCupons(),
                    dto.getObservacoes(), dto.getNomeCupom(), key, logDAO);
        }
    }

    public List<HistoricoCupomDTO> consultarHistoricoUtilizacao(ConsultarHistoricoCupomDTO dto) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            OAMDService oamdService = new OAMDService();
            CampanhaCupomDescontoVO campanhaCupomDescontoVO = oamdService.consultarCampanhaCupomDescontoPorId(dto.getCampanhaCupomDesconto(), Uteis.NIVELMONTARDADOS_TODOS);
            List<HistoricoUtilizacaoCupomDescontoVO> historico = oamdService.consultarCupons(campanhaCupomDescontoVO, dto.getChave(), dto.getEmpresa(), TipoConsultaCupomDescontoEnum.TODOS, dto.getCupom());
            List<HistoricoCupomDTO> lista = new ArrayList<>();
            for (HistoricoUtilizacaoCupomDescontoVO obj : historico) {
                lista.add(new HistoricoCupomDTO(obj));
            }
            return lista;
        }
    }
}
