package springboot.services.impl;

import com.pacto.config.security.interfaces.RequestService;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.facade.jdbc.plano.Modalidade;
import org.springframework.stereotype.Service;
import springboot.bd.intf.ConexaoZWService;
import springboot.dto.plano.ModalidadeDTO;
import springboot.dto.plano.ModalidadeFiltroDTO;
import springboot.services.intf.ModalidadeService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

@Service
public class ModalidadeServiceImpl implements ModalidadeService {

    private ConexaoZWService conexaoZWService;
    private RequestService requestService;

    public ModalidadeServiceImpl(ConexaoZWService conexaoZWService, RequestService requestService) {
        this.conexaoZWService = conexaoZWService;
        this.requestService = requestService;
    }

    private String obterChave() {
        return this.requestService.getUsuarioAtual().getChave();
    }

    @Override
    public List<ModalidadeDTO> consultarMinimal(ModalidadeFiltroDTO filtroDTO, PaginadorDTO paginadorDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Modalidade modalidadeDAO = new Modalidade(con);
            List<ModalidadeVO> modalidades = modalidadeDAO.consultarMinimal(filtroDTO, paginadorDTO);

            List<ModalidadeDTO> resultado = new ArrayList<>();
            modalidades.forEach(vo -> resultado.add(new ModalidadeDTO(vo.getCodigo(), vo.getNome())));

            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
