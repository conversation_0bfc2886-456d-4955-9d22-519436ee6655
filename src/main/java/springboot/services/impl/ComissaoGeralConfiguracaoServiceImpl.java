package springboot.services.impl;

import com.pacto.config.security.interfaces.RequestService;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ComissaoGeralConfiguracaoVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.financeiro.ComissaoMetaFinananceiraVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.ComissaoGeralConfiguracao;
import negocio.facade.jdbc.contrato.ContratoDuracao;
import org.springframework.stereotype.Service;
import springboot.bd.intf.ConexaoZWService;
import springboot.dto.contrato.ComissaoGeralConfiguracaoDTO;
import springboot.dto.contrato.ComissaoGeralConfiguracaoDetalheDTO;
import springboot.dto.contrato.ComissaoGeralConfiguracaoFiltroDTO;
import springboot.dto.contrato.ContratoDuracaoDTO;
import springboot.dto.financeiro.ComissaoMetaFinanceiraDTO;
import springboot.services.intf.ComissaoGeralConfiguracaoService;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

@Service
public class ComissaoGeralConfiguracaoServiceImpl implements ComissaoGeralConfiguracaoService {

    private ConexaoZWService conexaoZWService;
    private RequestService requestService;

    public ComissaoGeralConfiguracaoServiceImpl(ConexaoZWService conexaoZWService, RequestService requestService) {
        this.conexaoZWService = conexaoZWService;
        this.requestService = requestService;
    }

    private String obterChave() {
        return this.requestService.getUsuarioAtual().getChave();
    }

    @Override
    public List<ComissaoGeralConfiguracaoDTO> consultarComissoesGerais(ComissaoGeralConfiguracaoFiltroDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            filtro.setEmpresaId(requestService.getEmpresaId());
            // Para simplificar, vamos consultar por empresa se informada, senão consulta todas
            List<ComissaoGeralConfiguracaoVO> comissoes;
            if (filtro != null && filtro.getEmpresaId() != null) {
                comissoes = comissaoDAO.consultarPorEmpresa(filtro.getEmpresaId(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            } else {
                // Implementar consulta geral se necessário
                comissoes = new ArrayList<ComissaoGeralConfiguracaoVO>();
            }
            paginadorDTO.setQuantidadeTotalElementos((long) comissoes.size());
            List<ComissaoGeralConfiguracaoDTO> resultado = new ArrayList<ComissaoGeralConfiguracaoDTO>();
            EmpresaVO empresaVO = new EmpresaVO(); // Para compatibilidade com o construtor existente
            
            for (ComissaoGeralConfiguracaoVO vo : comissoes) {
                resultado.add(new ComissaoGeralConfiguracaoDTO(vo, empresaVO));
            }

            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ComissaoGeralConfiguracaoDetalheDTO obterPorCodigo(Integer codigo) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            ComissaoGeralConfiguracaoVO vo = comissaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            
            if (vo == null) {
                throw new ServiceException("Configuração de comissão geral não encontrada");
            }
            
            return new ComissaoGeralConfiguracaoDetalheDTO(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ComissaoGeralConfiguracaoDetalheDTO criarComissaoGeral(ComissaoGeralConfiguracaoDetalheDTO comissaoDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            
            ComissaoGeralConfiguracaoVO vo = converterDTOParaVO(comissaoDTO);
            comissaoDAO.incluir(vo);
            
            return new ComissaoGeralConfiguracaoDetalheDTO(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ComissaoGeralConfiguracaoDetalheDTO atualizarComissaoGeral(Integer codigo, ComissaoGeralConfiguracaoDetalheDTO comissaoDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            
            ComissaoGeralConfiguracaoVO voExistente = comissaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            if (voExistente == null) {
                throw new ServiceException("Configuração de comissão geral não encontrada");
            }
            
            ComissaoGeralConfiguracaoVO vo = converterDTOParaVO(comissaoDTO);
            vo.setCodigo(codigo);
            comissaoDAO.alterar(vo);
            
            return new ComissaoGeralConfiguracaoDetalheDTO(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void removerComissaoGeral(Integer codigo) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            
            ComissaoGeralConfiguracaoVO vo = comissaoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (vo == null) {
                throw new ServiceException("Configuração de comissão geral não encontrada");
            }
            
            comissaoDAO.excluir(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ContratoDuracaoDTO> listarDuracoesContrato() throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ContratoDuracao duracaoDAO = new ContratoDuracao(con);
            List<ContratoDuracaoVO> duracoes = duracaoDAO.consultarNumeroMeses();
            
            List<ContratoDuracaoDTO> resultado = new ArrayList<ContratoDuracaoDTO>();
            for (ContratoDuracaoVO vo : duracoes) {
                resultado.add(new ContratoDuracaoDTO(vo));
            }
            
            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void removerTodasComissoesPorEmpresa(Integer empresaId) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            List<ComissaoGeralConfiguracaoVO> comissoes = comissaoDAO.consultarPorEmpresa(empresaId, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            for (ComissaoGeralConfiguracaoVO vo : comissoes) {
                comissaoDAO.excluir(vo);
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ComissaoGeralConfiguracaoDTO> consultarPorEmpresa(Integer empresaId) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ComissaoGeralConfiguracao comissaoDAO = new ComissaoGeralConfiguracao(con);
            List<ComissaoGeralConfiguracaoVO> comissoes = comissaoDAO.consultarPorEmpresa(empresaId, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            List<ComissaoGeralConfiguracaoDTO> resultado = new ArrayList<ComissaoGeralConfiguracaoDTO>();
            EmpresaVO empresaVO = new EmpresaVO(); // Para compatibilidade com o construtor existente
            
            for (ComissaoGeralConfiguracaoVO vo : comissoes) {
                resultado.add(new ComissaoGeralConfiguracaoDTO(vo, empresaVO));
            }
            
            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    // Método auxiliar para conversão
    private ComissaoGeralConfiguracaoVO converterDTOParaVO(ComissaoGeralConfiguracaoDetalheDTO dto) throws Exception {
        ComissaoGeralConfiguracaoVO vo = new ComissaoGeralConfiguracaoVO();
        vo.setCodigo(dto.getCodigo());
        vo.setDuracao(dto.getDuracao());
        vo.setSituacao(dto.getSituacao());
        vo.setValorFixoEspontaneo(dto.getValorFixoEspontaneo());
        vo.setPorcentagemEspontaneo(dto.getPorcentagemEspontaneo());
        vo.setValorFixoAgendado(dto.getValorFixoAgendado());
        vo.setPorcentagemAgendado(dto.getPorcentagemAgendado());
        vo.setVigenciaInicio(dto.getVigenciaInicio());
        vo.setVigenciaFinal(dto.getVigenciaFinal());
        
        if (dto.getEmpresa() != null) {
            EmpresaVO empresaVO = new EmpresaVO();
            empresaVO.setCodigo(dto.getEmpresa().getCodigo());
            vo.setEmpresa(empresaVO);
        }
        
        if (dto.getListaComissaoMeta() != null && !dto.getListaComissaoMeta().isEmpty()) {
            List<ComissaoMetaFinananceiraVO> listaMeta = new ArrayList<ComissaoMetaFinananceiraVO>();
            for (ComissaoMetaFinanceiraDTO metaDTO : dto.getListaComissaoMeta()) {
                ComissaoMetaFinananceiraVO metaVO = new ComissaoMetaFinananceiraVO();
                metaVO.setCodigo(metaDTO.getCodigo());
                metaVO.setCodigoMeta(metaDTO.getCodigoMeta());
                metaVO.setValorEspontaneo(metaDTO.getValorEspontaneo());
                metaVO.setValorAgendado(metaDTO.getValorAgendado());
                metaVO.setPorcentagemEspontaneo(metaDTO.getPorcentagemEspontaneo());
                metaVO.setPorcentagemAgendado(metaDTO.getPorcentagemAgendado());
                listaMeta.add(metaVO);
            }
            vo.setListaComissaoMeta(listaMeta);
        }
        
        return vo;
    }
}
