package springboot.services.impl;

import com.pacto.config.security.interfaces.RequestService;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ConfiguracaoSistemaVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.plano.CategoriaProdutoVO;
import negocio.comuns.plano.ConfiguracaoProdutoEmpresaVO;
import negocio.comuns.plano.ProdutoVO;
import negocio.comuns.plano.enumerador.TipoProduto;
import negocio.comuns.plano.enumerador.UnidadeMedidaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.contrato.MovProduto;
import negocio.facade.jdbc.plano.CategoriaProduto;
import negocio.facade.jdbc.plano.Plano;
import negocio.facade.jdbc.plano.Produto;
import org.springframework.stereotype.Service;
import springboot.bd.intf.ConexaoZWService;
import springboot.dto.comuns.CategoriaProdutoDTO;
import springboot.dto.comuns.ItemGenericoDTO;
import springboot.dto.comuns.ProdutoDTO;
import springboot.dto.plano.ConfiguracaoProdutoEmpresaDTO;
import springboot.dto.plano.PlanoDTO;
import springboot.dto.plano.ProdutoDetalheDTO;
import springboot.dto.plano.ProdutoFiltroDTO;
import springboot.dto.produto.ProdutoVisualizacaoRegrasDTO;
import springboot.services.intf.ProdutoService;
import negocio.comuns.plano.PlanoVO;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class ProdutoServiceImpl implements ProdutoService {

    private ConexaoZWService conexaoZWService;
    private RequestService requestService;

    public ProdutoServiceImpl(ConexaoZWService conexaoZWService, RequestService requestService) {
        this.conexaoZWService = conexaoZWService;
        this.requestService = requestService;
    }

    private String obterChave() {
        return this.requestService.getUsuarioAtual().getChave();
    }

    @Override
    public List<ProdutoDTO> consultarProdutos(ProdutoFiltroDTO filtro, PaginadorDTO paginadorDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            return produtoDAO.consultarProdutosPaginado(filtro, paginadorDTO);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ProdutoDetalheDTO obterPorCodigo(Integer codigo) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            ProdutoVO vo = produtoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            
            if (vo == null) {
                throw new ServiceException("Produto nao encontrado");
            }
            
            return new ProdutoDetalheDTO(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ProdutoDetalheDTO criarProduto(ProdutoDetalheDTO produtoDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            
            ProdutoVO vo = converterDTOParaVO(produtoDTO);
            vo.setNovoObj(true);
            produtoDAO.incluir(vo);
            
            return new ProdutoDetalheDTO(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ProdutoDetalheDTO atualizarProduto(Integer codigo, ProdutoDetalheDTO produtoDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            
            ProdutoVO voExistente = produtoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_TODOS);
            if (voExistente == null) {
                throw new ServiceException("Produto nao encontrado");
            }
            
            ProdutoVO vo = converterDTOParaVO(produtoDTO);
            vo.setCodigo(codigo);
            vo.setNovoObj(false);
            produtoDAO.alterar(vo);
            
            return new ProdutoDetalheDTO(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void removerProduto(Integer codigo) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            
            ProdutoVO vo = produtoDAO.consultarPorChavePrimaria(codigo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (vo == null) {
                throw new ServiceException("Produto n?o encontrado");
            }
            
            produtoDAO.excluir(vo);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<CategoriaProdutoDTO> listarCategoriasProduto() throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            CategoriaProduto categoriaDAO = new CategoriaProduto(con);
            List<CategoriaProdutoVO> categorias = categoriaDAO.consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            List<CategoriaProdutoDTO> resultado = new ArrayList<>();
            for (CategoriaProdutoVO vo : categorias) {
                resultado.add(new CategoriaProdutoDTO(vo));
            }
            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ItemGenericoDTO> listarTiposProduto() throws ServiceException {
        List<ItemGenericoDTO> tipos = new ArrayList<>();
        for (TipoProduto tipo : TipoProduto.values()) {
            tipos.add(new ItemGenericoDTO(tipo.getCodigo(), tipo.getDescricao()));
        }
        return tipos;
    }

    @Override
    public List<ItemGenericoDTO> listarUnidadesMedida() throws ServiceException {
        List<ItemGenericoDTO> unidades = new ArrayList<>();
        for (UnidadeMedidaEnum unidade : UnidadeMedidaEnum.values()) {
            unidades.add(new ItemGenericoDTO(unidade.getCodigo(), unidade.getDescricao()));
        }
        return unidades;
    }

    @Override
    public List<ItemGenericoDTO> listarTiposVigencia() throws ServiceException {
        List<ItemGenericoDTO> tipos = new ArrayList<>();
        tipos.add(new ItemGenericoDTO("ID", "Indefinida"));
        tipos.add(new ItemGenericoDTO("FX", "Fixa"));
        tipos.add(new ItemGenericoDTO("IL", "Ilimitada"));
        return tipos;
    }

    @Override
    public Boolean validarExclusaoProduto(Integer codigo) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            // Implementar valida??o se produto pode ser exclu?do
            // Verificar se produto foi vendido, tem movimenta??es, etc.
            return true; // Simplificado por enquanto
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProdutoDTO> buscarPorCodigoBarras(String codigoBarras) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
//            List<ProdutoVO> produtos = produtoDAO.consultarPorCodigoBarras(codigoBarras, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            List<ProdutoDTO> resultado = new ArrayList<>();
//            for (ProdutoVO vo : produtos) {
//                resultado.add(new ProdutoDTO(vo));
//            }
            
            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProdutoDTO> buscarPorDescricao(String descricao) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            List<ProdutoVO> produtos = produtoDAO.consultarPorDescricao(descricao, true, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            
            List<ProdutoDTO> resultado = new ArrayList<>();
            for (ProdutoVO vo : produtos) {
                resultado.add(new ProdutoDTO(vo));
            }
            
            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public List<ProdutoDTO> consultarMinimal(ProdutoFiltroDTO filtroDTO, PaginadorDTO paginadorDTO) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Produto produtoDAO = new Produto(con);
            List<ProdutoVO> produtos = produtoDAO.consultarMinimal(filtroDTO, paginadorDTO);

            List<ProdutoDTO> resultado = new ArrayList<>();
            for (ProdutoVO vo : produtos) {
                resultado.add(new ProdutoDTO(vo));
            }

            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public ProdutoVisualizacaoRegrasDTO obterRegrasVisualizacao(Integer codigoProduto, String tipoProduto, Integer empresaId) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            ProdutoVisualizacaoRegrasDTO regras = new ProdutoVisualizacaoRegrasDTO();

            ConfiguracaoSistema configuracaoSistemaDAO = new ConfiguracaoSistema(con);
            ConfiguracaoSistemaVO configuracaoSistema = configuracaoSistemaDAO.consultarConfigs(Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Usuario usuarioDAO = new Usuario(con);
            Integer codUsuario = this.requestService.getUsuarioAtual().getCodZw();
            UsuarioVO usuario = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresa = empresaDAO.consultarPorChavePrimaria(empresaId, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            ProdutoVO produto = null;
            if (!UteisValidacao.emptyNumber(codigoProduto)) {
                Produto produtoDAO = new Produto(con);
                produto = produtoDAO.consultarPorChavePrimaria(codigoProduto, Uteis.NIVELMONTARDADOS_TODOS);
                tipoProduto = produto.getTipoProduto();
            }

            aplicarRegrasVisualizacao(regras, produto, tipoProduto, empresa, usuario, configuracaoSistema, con);

            return regras;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    private void aplicarRegrasVisualizacao(ProdutoVisualizacaoRegrasDTO regras, ProdutoVO produto, String tipoProduto,
                                         EmpresaVO empresa, UsuarioVO usuario, ConfiguracaoSistemaVO configuracaoSistema,
                                         Connection con) throws Exception {

        String chave = this.requestService.getUsuarioAtual().getChave();
        regras.setApresentarMargemLucroPrecoCusto(
            chave != null && (chave.equalsIgnoreCase("teste") || chave.equalsIgnoreCase("458906d2b465dd6cd2c07507e2e4650e"))
        );
        regras.setApresentarConfiguracoesSesi(configuracaoSistema.isUtilizarServicoSesiSC());
        regras.setCreditoPersonal(
            tipoProduto != null && tipoProduto.equals(TipoProduto.CREDITO_PERSONAL.getCodigo())
        );
        regras.setUsaAulaCheia(
            tipoProduto != null && (tipoProduto.equals(TipoProduto.DIARIA.getCodigo()) ||
                                   tipoProduto.equals(TipoProduto.FREEPASS.getCodigo()))
        );
        regras.setDiaria(
            tipoProduto != null && tipoProduto.equalsIgnoreCase(TipoProduto.DIARIA.getCodigo())
        );
        regras.setProdutoVendaAvulsa(TipoProduto.tipoProdutoVendaAvulsa(tipoProduto));
        regras.setExibirConfiguracoesNFSe(
            usuario.getUsuarioPactoSolucoes() || empresa.getUsarNFSe()
        );
        regras.setExibirConfiguracoesNFCe(
            usuario.getUsuarioPactoSolucoes() || empresa.isUsarNFCe()
        );
        regras.setExibirAbaVendasOnline(true);
        verificarPermissaoCfgEmpresa(regras, empresa, usuario, con);
        verificarIntegranteRedeEmpresa(regras);
        verificarApresentarEmpresaComissao(regras, con);
        if (produto != null) {
            aplicarRegrasComProduto(regras, produto, empresa, usuario, con);
        } else {
            aplicarRegrasSemProduto(regras, tipoProduto, empresa, usuario);
        }
    }

    private void aplicarRegrasComProduto(ProdutoVisualizacaoRegrasDTO regras, ProdutoVO produto,
                                       EmpresaVO empresa, UsuarioVO usuario, Connection con) throws Exception {

        // naoPermiteEditar - verifica se produto j? foi vendido
        MovProduto movProdutoDAO = new MovProduto(con);
        regras.setNaoPermiteEditar(movProdutoDAO.consultarPorCodigoProduto(produto.getCodigo()));

        // editarSomenteDescricao - baseado no tipo do produto
        regras.setEditarSomenteDescricao(
            produto.getTipoProduto().equals(TipoProduto.MANUTENCAO_MODALIDADE.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.CHEQUE_DEVOLVIDO.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.ACERTO_CONTA_CORRENTE_ALUNO.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.ALTERAR_HORARIO.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.CREDITO_PERSONAL.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.DEPOSITO_CONTA_CORRENTE_ALUNO.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.DEVOLUCAO.getCodigo()) ||
            produto.getTipoProduto().equals(TipoProduto.DEVOLUCAO_CREDITO.getCodigo())
        );

        // Regras baseadas nas propriedades do produto
        regras.setDesenharValorProduto(produto.getDesenharValorProduto());
        regras.setDesenharTipoVigencia(produto.getDesenharTipoVigencia());
        regras.setDesenharDataVigencia(produto.getDesenharDataVigencia());
        regras.setDesenharPeriodoVigencia(produto.getDesenharPeriodoVigencia());
        regras.setPodeSerRenovadoAutomatica(produto.getPodeSerRenovadoAutomatica());
        regras.setDesenhartela(produto.getDesenhartela());
        regras.setArmario(produto.getArmario());
        regras.setProdutoEstoque(produto.isProdutoEstoque());
        regras.setProdutoApresentarIcms(produto.isProdutoApresentarIcms());
        regras.setEnviarPercentualImposto(produto.isEnviarPercentualImposto());
        regras.setApresentarVendasOnline(produto.isApresentarVendasOnline());
        regras.setApresentarPactoApp(produto.isApresentarPactoApp());
        regras.setApresentarPactoFlow(produto.isApresentarPactoFlow());

        // apresentarAbaComissao - produto n?o novo e tipos espec?ficos
        regras.setApresentarAbaComissao(
            !produto.isNovoObj() &&
            !TipoProduto.CHEQUE_DEVOLVIDO.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.DESCONTO.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.CONVENIO_DESCONTO.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.FREEPASS.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.DESCONTO_RENOVACAO_ANTECIPADA.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.DEVOLUCAO.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.DEVOLUCAO_CREDITO.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.DEVOLUCAO_DE_RECEBIVEIS.getCodigo().equals(produto.getTipoProduto()) &&
            !TipoProduto.RENOVACAO.getCodigo().equals(produto.getTipoProduto())
        );

        // exibirConfiguracoesParaEmpresas
        regras.setExibirConfiguracoesParaEmpresas(
            !UteisValidacao.emptyList(produto.getConfiguracoesEmpresa()) ||
            ((regras.getProdutoVendaAvulsa() || produto.getArmario() ||
              produto.getTipoProduto().equalsIgnoreCase(TipoProduto.DIARIA.getCodigo())) &&
             regras.getPodeAdicionarCfgEmpresa())
        );
    }

    private void aplicarRegrasSemProduto(ProdutoVisualizacaoRegrasDTO regras, String tipoProduto,
                                       EmpresaVO empresa, UsuarioVO usuario) {
        // Para produto novo
        regras.setNaoPermiteEditar(false);
        regras.setEditarSomenteDescricao(false);
        regras.setApresentarAbaComissao(false);
        regras.setExibirConfiguracoesParaEmpresas(false);

        // Valores padr?o para produto novo
        regras.setDesenharValorProduto(true);
        regras.setDesenharTipoVigencia(true);
        regras.setDesenharDataVigencia(false);
        regras.setDesenharPeriodoVigencia(false);
        regras.setPodeSerRenovadoAutomatica(false);
        regras.setDesenhartela(true);
        regras.setArmario(false);
        regras.setProdutoEstoque(false);
        regras.setProdutoApresentarIcms(false);
        regras.setEnviarPercentualImposto(false);
        regras.setApresentarVendasOnline(false);
        regras.setApresentarPactoApp(false);
        regras.setApresentarPactoFlow(false);
    }

    private void verificarPermissaoCfgEmpresa(ProdutoVisualizacaoRegrasDTO regras, EmpresaVO empresa,
                                            UsuarioVO usuario, Connection con) throws Exception {
        try {
            Empresa empresaDAO = new Empresa(con);
            if (empresaDAO.countEmpresas(true) <= 1) {
                regras.setPodeAdicionarCfgEmpresa(false);
                return;
            }

            // Simplificado - assumir que pode adicionar se for administrador
            regras.setPodeAdicionarCfgEmpresa(usuario.getAdministrador() != null && usuario.getAdministrador());
        } catch (Exception e) {
            regras.setPodeAdicionarCfgEmpresa(false);
        }
    }

    private void verificarIntegranteRedeEmpresa(ProdutoVisualizacaoRegrasDTO regras) {
        // Simplificado - assumir false por enquanto
        regras.setIntegranteRedeEmpresa(false);
        regras.setExibirReplicarRedeEmpresa(false);
    }

    private void verificarApresentarEmpresaComissao(ProdutoVisualizacaoRegrasDTO regras, Connection con) throws Exception {
        try {
            Empresa empresaDAO = new Empresa(con);
            List<EmpresaVO> empresas = empresaDAO.consultarTodas(true, Uteis.NIVELMONTARDADOS_MINIMOS);
            regras.setApresentarEmpresaComissao(empresas.size() > 1);
        } catch (Exception e) {
            regras.setApresentarEmpresaComissao(true);
        }
    }


    private ProdutoVO converterDTOParaVO(ProdutoDetalheDTO dto) {
        ProdutoVO vo = new ProdutoVO();
        
        vo.setCodigo(dto.getCodigo());
        vo.setDescricao(dto.getDescricao());
        vo.setValorBaseCalculo(dto.getValorBaseCalculo() != null ? dto.getValorBaseCalculo() : 0.0);
        vo.setTipoVigencia(dto.getTipoVigencia());
        vo.setValorFinal(dto.getValorFinal() != null ? dto.getValorFinal() : 0.0);
        vo.setDataInicioVigencia(dto.getDataInicioVigencia());
        vo.setDataFinalVigencia(dto.getDataFinalVigencia());
        vo.setNrDiasVigencia(dto.getNrDiasVigencia() != null ? dto.getNrDiasVigencia() : 0);
        vo.setTipoProduto(dto.getTipoProduto());
        vo.setDesativado(dto.getDesativado());
        vo.setCapacidade(dto.getCapacidade() != null ? dto.getCapacidade() : 0);
        vo.setBloqueiaPelaVigencia(dto.getBloqueiaPelaVigencia());
        vo.setNcm(dto.getNcm());
        vo.setNcmNFCe(dto.getNcmNFCe());
        vo.setCfop(dto.getCfop());
        vo.setCest(dto.getCest());
        vo.setPrefixo(dto.getPrefixo());
        vo.setCodigoBarras(dto.getCodigoBarras());
        vo.setPrevalecerVigenciaContrato(dto.getPrevalecerVigenciaContrato() != null ? dto.getPrevalecerVigenciaContrato() : false);
        vo.setAparecerAulaCheia(dto.getAparecerAulaCheia() != null ? dto.getAparecerAulaCheia() : false);
        vo.setObservacao(dto.getObservacao());
        vo.setUnidadeMedida(dto.getUnidadeMedida());
        vo.setPrecoCusto(dto.getPrecoCusto() != null ? dto.getPrecoCusto() : 0.0);
        vo.setMargemLucro(dto.getMargemLucro() != null ? dto.getMargemLucro() : 0.0);
        vo.setPontos(dto.getPontos() != null ? dto.getPontos() : 0);
        vo.setRenovavelAutomaticamente(dto.getRenovavelAutomaticamente() != null ? dto.getRenovavelAutomaticamente() : false);
        vo.setImagens(dto.getImagens());
        vo.setMaxDivisao(dto.getMaxDivisao() != null ? dto.getMaxDivisao() : 1);
        vo.setModalidadeVendasOnline(dto.getModalidadeVendasOnline());
        vo.setApresentarVendasOnline(dto.getApresentarVendasOnline() != null ? dto.getApresentarVendasOnline() : false);
        vo.setApresentarPactoFlow(dto.getApresentarPactoFlow() != null ? dto.getApresentarPactoFlow() : false);
        vo.setApresentarPactoApp(dto.getApresentarPactoApp() != null ? dto.getApresentarPactoApp() : false);
        vo.setQtdConvites(dto.getQtdConvites() != null ? dto.getQtdConvites() : 0);
        vo.setContratoTextoPadrao(dto.getContratoTextoPadrao());
        vo.setDescricaoServicoMunicipio(dto.getDescricaoServicoMunicipio());
        
        // Campos de impostos
        vo.setSituacaoTributariaICMS(dto.getSituacaoTributariaICMS());
        vo.setIsentoICMS(dto.getIsentoICMS() != null ? dto.getIsentoICMS() : false);
        vo.setEnviaAliquotaNFeICMS(dto.getEnviaAliquotaNFeICMS() != null ? dto.getEnviaAliquotaNFeICMS() : false);
        vo.setAliquotaICMS(dto.getAliquotaICMS() != null ? dto.getAliquotaICMS() : 0.0);
        vo.setSituacaoTributariaISSQN(dto.getSituacaoTributariaISSQN());
        vo.setAliquotaISSQN(dto.getAliquotaISSQN() != null ? dto.getAliquotaISSQN() : 0.0);
        vo.setCodigoBeneficioFiscal(dto.getCodigoBeneficioFiscal());
        vo.setSituacaoTributariaPIS(dto.getSituacaoTributariaPIS());
        vo.setIsentoPIS(dto.getIsentoPIS() != null ? dto.getIsentoPIS() : false);
        vo.setEnviaAliquotaNFePIS(dto.getEnviaAliquotaNFePIS() != null ? dto.getEnviaAliquotaNFePIS() : false);
        vo.setAliquotaPIS(dto.getAliquotaPIS() != null ? dto.getAliquotaPIS() : 0.0);
        vo.setSituacaoTributariaCOFINS(dto.getSituacaoTributariaCOFINS());
        vo.setIsentoCOFINS(dto.getIsentoCOFINS() != null ? dto.getIsentoCOFINS() : false);
        vo.setEnviaAliquotaNFeCOFINS(dto.getEnviaAliquotaNFeCOFINS() != null ? dto.getEnviaAliquotaNFeCOFINS() : false);
        vo.setAliquotaCOFINS(dto.getAliquotaCOFINS() != null ? dto.getAliquotaCOFINS() : 0.0);
        vo.setCodigoListaServico(dto.getCodigoListaServico());
        vo.setCodigoTributacaoMunicipio(dto.getCodigoTributacaoMunicipio());
        vo.setEnviarPercentualImposto(dto.getEnviarPercentualImposto() != null ? dto.getEnviarPercentualImposto() : false);
        vo.setPercentualFederal(dto.getPercentualFederal() != null ? dto.getPercentualFederal() : 0.0);
        vo.setPercentualEstadual(dto.getPercentualEstadual() != null ? dto.getPercentualEstadual() : 0.0);
        vo.setPercentualMunicipal(dto.getPercentualMunicipal() != null ? dto.getPercentualMunicipal() : 0.0);

        if (dto.getCategoriaProduto() != null) {
            CategoriaProdutoVO categoriaVO = new CategoriaProdutoVO();
            categoriaVO.setCodigo(dto.getCategoriaProduto().getCodigo());
            categoriaVO.setDescricao(dto.getCategoriaProduto().getDescricao());
            vo.setCategoriaProduto(categoriaVO);
        }

        if (dto.getValoresEmpresa() != null && !dto.getValoresEmpresa().isEmpty()) {
            List<ConfiguracaoProdutoEmpresaVO> configuracoesVO = new ArrayList<>();
            for (ConfiguracaoProdutoEmpresaDTO configDTO : dto.getValoresEmpresa()) {
                if (configDTO != null) {
                    configuracoesVO.add(configDTO.toConfiguracaoProdutoEmpresaVO());
                }
            }
            vo.setConfiguracoesEmpresa(configuracoesVO);
        }

        return vo;
    }

    @Override
    public List<PlanoDTO> listarPlanosPorEmpresa(Integer empresaId) throws ServiceException {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Plano planoDAO = new Plano(con);
            List<PlanoVO> planos = planoDAO.consultarTodosOsPlanosVendas(empresaId);

            List<PlanoDTO> resultado = new ArrayList<>();
            for (PlanoVO vo : planos) {
                resultado.add(new PlanoDTO(vo.getCodigo(), vo.getDescricao()));
            }

            return resultado;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }
}
