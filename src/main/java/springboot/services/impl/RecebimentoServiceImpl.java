package springboot.services.impl;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaColaborador;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaColaboradorVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.comuns.util.Formatador;
import com.pacto.config.security.interfaces.RequestService;
import controle.arquitetura.SuperControle;
import controle.arquitetura.exceptions.ServiceException;
import controle.financeiro.PinpadTO;
import negocio.comuns.arquitetura.LogVO;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.*;
import negocio.comuns.utilitarias.*;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.UsuarioPerfilAcesso;
import negocio.facade.jdbc.basico.*;
import negocio.facade.jdbc.crm.Feriado;
import negocio.facade.jdbc.financeiro.*;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import servicos.boleto.TokenBoletoVO;
import servicos.discovery.ClientDiscoveryDataDTO;
import servicos.discovery.DiscoveryMsService;
import servicos.impl.boleto.BoletoService;
import servicos.impl.gatewaypagamento.PagamentoService;
import servicos.impl.stone.connect.PedidoRetornoDTO;
import servicos.impl.stone.connect.StoneConnectService;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.pix.PixEmailService;
import servicos.pix.PixPagamentoService;
import servicos.pix.PixStatusEnum;
import springboot.bd.intf.ConexaoZWService;
import springboot.dto.comuns.MovParcelaDTO;
import springboot.dto.comuns.PessoaDTO;
import springboot.dto.financeiro.*;
import springboot.services.intf.RecebimentoService;

import java.sql.Connection;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static controle.arquitetura.SuperControle.getConfiguracaoSMTPNoReply;

@Service
public class RecebimentoServiceImpl implements RecebimentoService {

    private RequestService requestService;
    private ConexaoZWService conexaoZWService;

    public RecebimentoServiceImpl(RequestService requestService,
                                  ConexaoZWService conexaoZWService) throws Exception {
        this.requestService = requestService;
        this.conexaoZWService = conexaoZWService;
    }

    private String obterChave() {
        return this.requestService.getUsuarioAtual().getChave();
    }

    private OrigemCobrancaEnum obterOrigemCobranca(RecebimentoDTO recebimentoDTO) {
        OrigemCobrancaEnum origemCobrancaEnum = OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO_V2;
        if (!UteisValidacao.emptyNumber(recebimentoDTO.getOrigemCobranca())) {
            origemCobrancaEnum = OrigemCobrancaEnum.obterPorCodigo(recebimentoDTO.getOrigemCobranca());
        }
        return origemCobrancaEnum.equals(OrigemCobrancaEnum.NENHUM) ? OrigemCobrancaEnum.ZW_MANUAL_CAIXA_ABERTO_V2 : origemCobrancaEnum;
    }

    public List<FormaPagamentoDTO> formasPagamento(Integer empresaId) throws Exception {
        List<FormaPagamentoDTO> listaRetorno = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            UsuarioPerfilAcesso usuarioPerfilAcessoDAO = new UsuarioPerfilAcesso(con);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            Usuario usuarioDAO = new Usuario(con);
            Empresa empresaDAO = new Empresa(con);

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresaId;
            }
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                throw new ServiceException("validacao_empresa", "Empresa não informada");
            }

            Integer codUsuario = this.requestService.getUsuarioAtual().getCodZw();

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(codUsuario, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            UsuarioPerfilAcessoVO usuarioPerfilAcessoVO = usuarioPerfilAcessoDAO.consultarPorUsuarioEmpresa(usuarioVO.getCodigo(), codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (usuarioPerfilAcessoVO == null) {
                throw new ServiceException("validacao_usuario", "Perfil de acesso não encontrado");
            }

            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            List<FormaPagamentoVO> listaForma = formaPagamentoDAO.consultarPorDescricaoTipoFormaPagamento("", false, true, true, true,
                    codEmpresa, Uteis.NIVELMONTARDADOS_TODOS);

            List<FormaPagamentoVO> lista = new ArrayList(listaForma);
            List<FormaPagamentoVO> listaValidandoPerfilAcesso = new ArrayList<FormaPagamentoVO>();
            for (FormaPagamentoVO formaPagamentoVO : lista) {
                if (usuarioVO.getAdministrador() || formaPagamentoVO.validarPerfilAcesso(usuarioPerfilAcessoVO.getPerfilAcesso())) {
                    listaValidandoPerfilAcesso.add(formaPagamentoVO);
                }
            }
            listaForma = (listaValidandoPerfilAcesso);


            Set<Integer> listaEmp = new HashSet<>();
            listaEmp.add(codEmpresa);
            boolean existePagamentoOnline = convenioCobrancaDAO.existeConvenioOnline(listaEmp, SituacaoConvenioCobranca.ATIVO);
            if (existePagamentoOnline) {
                List<FormaPagamentoVO> online = new ArrayList<>();
                for (FormaPagamentoVO formaPagamentoVO : listaForma) {
                    if (formaPagamentoVO.getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                        FormaPagamentoVO formaPagamentoOnline = (FormaPagamentoVO) formaPagamentoVO.getClone(true);
                        formaPagamentoOnline.setApresentarPagamentoOnline(true);
                        online.add(formaPagamentoOnline);
                    }
                }
                if (!UteisValidacao.emptyList(online)) {
                    listaForma.addAll(online);
                }
            }
            Ordenacao.ordenarLista(listaForma, "tipoFormaPagamento");
            Ordenacao.ordenarLista(listaForma, "descricao");

//            //colocar a forma de pagamento "Conta Corrente" por ultimo, para tela nao ter uma linha escondida no meio de todas as outras formas de pagamento
//            List<FormaPagamentoVO> listaa = new ArrayList<>(listaForma);
//            for (FormaPagamentoVO formaPagamentoVO : listaa) {
//                if (formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
//                    listaForma.remove(formaPagamentoVO);
//                    listaForma.add(formaPagamentoVO);
//                    break;
//                }
//            }

            listaForma.forEach(adq -> {
                listaRetorno.add(new FormaPagamentoDTO(adq, empresaVO));
            });
        }
        return listaRetorno;
    }

    public Double obterValorContaCorrente(Integer pessoa) throws Exception {
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            return obterValorContaCorrente(pessoa, con);
        }
    }

    private Double obterValorContaCorrente(Integer pessoa, Connection con) throws Exception {
        if (UteisValidacao.emptyNumber(pessoa)) {
            return 0.0;
        }
        MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(con);
        MovimentoContaCorrenteClienteVO movimentoContaCorrenteClienteVO = movimentoContaCorrenteClienteDAO.consultarPorCodigoPessoa(pessoa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (movimentoContaCorrenteClienteVO != null) {
            return movimentoContaCorrenteClienteVO.getSaldoAtual();
        } else {
            return 0.0;
        }
    }

    private List<ConvenioCobrancaVO> obterConveniosPix(FormaPagamentoVO formaPagamentoVO, Integer empresa, ConvenioCobranca convenioCobrancaDAO) throws Exception {
        List<ConvenioCobrancaVO> resultadoConsulta = convenioCobrancaDAO.consultarPorTipoPixComEmpresa(new EmpresaVO(empresa));
        List<ConvenioCobrancaVO> listaFinal = new ArrayList<>();
        for (ConvenioCobrancaVO obj : resultadoConsulta) {
            if (formaPagamentoVO.getConvenioCobrancaVO().getCodigo().equals(obj.getCodigo())) {
                listaFinal.add(obj);
            }
        }
        if (UteisValidacao.emptyNumber(listaFinal.size())) {
            for (ConvenioCobrancaVO obj : resultadoConsulta) {
                listaFinal.add(obj);
            }
        }
        Ordenacao.ordenarLista(listaFinal, "descricao");
        return listaFinal;
    }

    public List<OperadoraCartaoDTO> operadoraCartao(Integer formaPagamento) throws Exception {
        List<OperadoraCartaoDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);

            List<OperadoraCartaoVO> objs = new ArrayList<>();
            if (UteisValidacao.emptyNumber(formaPagamento)) {
                objs = operadoraCartaoDAO.consultarAtivos(Uteis.NIVELMONTARDADOS_TODOS);
            } else {
                FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(formaPagamento, Uteis.NIVELMONTARDADOS_MINIMOS);
                if (formaPagamentoVO == null) {
                    throw new ServiceException("validacao_formaPagamento", "Forma pagamento não encontrada com o código " + formaPagamento);
                }
                if (formaPagamentoVO.getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                    objs = operadoraCartaoDAO.consultarPorTipo(true, false, Uteis.NIVELMONTARDADOS_TODOS);
                } else if (formaPagamentoVO.getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAODEBITO.getSigla())) {
                    objs = operadoraCartaoDAO.consultarPorTipo(false, false, Uteis.NIVELMONTARDADOS_TODOS);
                }
            }
            objs.forEach(adq -> lista.add(new OperadoraCartaoDTO(adq)));
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public List<ConvenioCobrancaDTO> convenioCobrancaGeral(Integer empresaId, Integer formaPagamento) throws Exception {
        List<ConvenioCobrancaDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresaId;
            }

            List<ConvenioCobrancaVO> objs = convenioCobrancaDAO.consultarPorTiposESituacao(null, codEmpresa, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS, 0, false);
            objs.forEach(adq -> lista.add(new ConvenioCobrancaDTO(adq)));
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public List<ConvenioCobrancaDTO> convenioCobrancaPix(Integer formaPagamento, Integer empresaId) throws Exception {
        List<ConvenioCobrancaDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresaId;
            }

            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(formaPagamento, Uteis.NIVELMONTARDADOS_TODOS);
            List<ConvenioCobrancaVO> objs = obterConveniosPix(formaPagamentoVO, codEmpresa, convenioCobrancaDAO);
            objs.forEach(conVO -> lista.add(new ConvenioCobrancaDTO(conVO)));
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public List<ConvenioCobrancaDTO> convenioCobrancaCartaoOnline(Integer empresaId) throws Exception {
        List<ConvenioCobrancaDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresaId;
            }

            List<ConvenioCobrancaVO> objs = convenioCobrancaDAO.consultarPorTipoCobranca(TipoCobrancaEnum.ONLINE, codEmpresa, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            objs.forEach(adq -> lista.add(new ConvenioCobrancaDTO(adq)));
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public List<ConvenioCobrancaDTO> convenioCobrancaBoleto(TipoCobrancaEnum tipoCobrancaEnum, Integer empresaId) throws Exception {
        List<ConvenioCobrancaDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresaId;
            }

            List<ConvenioCobrancaVO> objsGeral = new ArrayList<>();
            if (tipoCobrancaEnum == null || tipoCobrancaEnum.equals(TipoCobrancaEnum.BOLETO)) {
                List<ConvenioCobrancaVO> objsOff = convenioCobrancaDAO.consultarPorTipoCobranca(TipoCobrancaEnum.BOLETO, codEmpresa, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                objsGeral.addAll(objsOff);
            }
            if (tipoCobrancaEnum == null || tipoCobrancaEnum.equals(TipoCobrancaEnum.BOLETO_ONLINE)) {
                List<ConvenioCobrancaVO> objsOn = convenioCobrancaDAO.consultarPorTipoCobranca(TipoCobrancaEnum.BOLETO_ONLINE, codEmpresa, SituacaoConvenioCobranca.ATIVO, false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                objsGeral.addAll(objsOn);
            }
            objsGeral.forEach(adq -> lista.add(new ConvenioCobrancaDTO(adq)));
        }
        Ordenacao.ordenarLista(lista, "descricao");
        return lista;
    }

    public List<AdquirenteDTO> adquirentes() throws Exception {
        List<AdquirenteDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            Adquirente adquirenteDAO = new Adquirente(con);
            List<AdquirenteVO> adquirenteVOS = adquirenteDAO.consultarTodos(true);
            adquirenteVOS.forEach(adq -> lista.add(new AdquirenteDTO(adq)));
        }
        Ordenacao.ordenarLista(lista, "nome");
        return lista;
    }

    public List<BancoDTO> bancos() throws Exception {
        List<BancoDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            Banco bancoDAO = new Banco(con);
            List<BancoVO> bancoVOS = bancoDAO.consultarTodos(Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            bancoVOS.forEach(adq -> lista.add(new BancoDTO(adq)));
        }
        Ordenacao.ordenarLista(lista, "nome");
        return lista;
    }

    public List<CartaoPagamentoDTO> obterAutorizacoes(Integer pessoa) throws Exception {
        List<CartaoPagamentoDTO> lista = new ArrayList<>();
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            AutorizacaoCobrancaCliente autoCliDAO = new AutorizacaoCobrancaCliente(con);
            AutorizacaoCobrancaColaborador autoColDAO = new AutorizacaoCobrancaColaborador(con);

            List<AutorizacaoCobrancaClienteVO> listaCli = autoCliDAO.consultarPorPessoaTipoAutorizacao(pessoa, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            listaCli.forEach(cli -> {
                lista.add(new CartaoPagamentoDTO(cli));
            });
            List<AutorizacaoCobrancaColaboradorVO> listaCol = autoColDAO.consultarPorPessoaTipoAutorizacao(pessoa, TipoAutorizacaoCobrancaEnum.CARTAOCREDITO, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            listaCol.forEach(cli -> {
                lista.add(new CartaoPagamentoDTO(cli));
            });
        }
        return lista;
    }

    public ReciboPagamentoDTO pagar(RecebimentoDTO recebimentoDTO) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            try {
                con.setAutoCommit(false);

                MovParcela movParcelaDAO = new MovParcela(con);
                FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
                MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(con);
                Cliente clienteDAO = new Cliente(con);
                Colaborador colaboradorDAO = new Colaborador(con);
                Usuario usuarioDAO = new Usuario(con);
                Pessoa pessoaDAO = new Pessoa(con);
                Empresa empresaDAO = new Empresa(con);

                validarDadosRecebimento(recebimentoDTO, null);

                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(recebimentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(recebimentoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
                PessoaVO pessoaVO = obterPessoaVO(recebimentoDTO, false, con);

                List<MovPagamentoVO> pagamentos = obterListaMovPagamento(recebimentoDTO, pessoaVO, empresaVO, usuarioVO, con);
                Date dataPagamento = recebimentoDTO.getDataPagamento() != null ? new Date(recebimentoDTO.getDataPagamento()) : Calendario.hoje();

                ClienteVO clienteVO = null;
                if (!UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    clienteVO = clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }
                ColaboradorVO colaboradorVO = null;
                if (clienteVO == null || UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    colaboradorVO = colaboradorDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), empresaVO.getCodigo(),
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                }

                //verificar cobrar multa e juros e criar a parcela de multa e juros
                if (recebimentoDTO.isCobrarMultaJuros()) {
                    List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);
                    Double multa = movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, dataPagamento,
                            false, 1.0, null);
                    if (!UteisValidacao.emptyNumber(multa)) {
                        List<MovParcelaVO> multas = movParcelaDAO.criarParcelaMultaJuros(listaParcelas,
                                empresaVO, usuarioVO,
                                dataPagamento, 1.0, null, false);
                        multas.forEach(parc -> {
                            recebimentoDTO.getParcelas().add(new MovParcelaDTO(parc));
                        });
                    }
                }

                AtomicReference<MovimentoContaCorrenteClienteVO> movimentoContaCorrenteClienteVOAtomicReference = new AtomicReference<>();
                AtomicReference<TipoOperacaoContaCorrenteEnum> tipoOperacaoContaCorrenteEnumAtomicReference = new AtomicReference<>();

                //verificar se vai usar conta corrente
                PessoaVO finalPessoaVO = pessoaVO;
                pagamentos.forEach(movPagamentoVO -> {
                    if (!UteisValidacao.emptyNumber(movPagamentoVO.getValor()) &&
                            movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
                        try {
                            if (UteisValidacao.emptyNumber(finalPessoaVO.getCodigo())) {
                                if (!UteisValidacao.emptyString(recebimentoDTO.getTipoComprador()) &&
                                        recebimentoDTO.getTipoComprador().equalsIgnoreCase("CN")) {
                                    throw new ServiceException("validacao_consumidor", "Recebimento de uma parcela de consumidor não é possível usar conta corrente.");
                                } else {
                                    throw new ServiceException("validacao_pessoa", "O campo PESSOA deve ser informado.");
                                }
                            }
                            tipoOperacaoContaCorrenteEnumAtomicReference.set(TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);
                            movimentoContaCorrenteClienteVOAtomicReference.set(montarContaCorrentePagarDebito(finalPessoaVO, movPagamentoVO.getValor(), con));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });

                //se foi informado valor negativo é pq vai receber o débito q tinha na conta corrente
                if (!UteisValidacao.emptyNumber(recebimentoDTO.getValorDebitoContaCorrente()) &&
                        recebimentoDTO.getValorDebitoContaCorrente() < 0) {

                    double valorDebito = (recebimentoDTO.getValorDebitoContaCorrente() * -1);
                    tipoOperacaoContaCorrenteEnumAtomicReference.set(TipoOperacaoContaCorrenteEnum.TOCC_Receber);

                    VendaAvulsaVO vendaAvulsaVO = movimentoContaCorrenteClienteDAO.gerarProdutoPagamentoDebito(valorDebito, clienteVO, colaboradorVO,
                            tipoOperacaoContaCorrenteEnumAtomicReference.get(), usuarioVO);

                    MovParcelaVO movParcela = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    movParcela.setSituacao("EA");

                    //adicionar a parcela a lista das parcelas q devem ser pagas
                    recebimentoDTO.getParcelas().add(new MovParcelaDTO(movParcela));

                    movimentoContaCorrenteClienteVOAtomicReference.set(montarContaCorrentePagarDebito(pessoaVO, 0.0, con));
                }

                //usar crédito q tem na conta corrente
                if (!UteisValidacao.emptyNumber(recebimentoDTO.getValorCreditoContaCorrente()) &&
                        recebimentoDTO.getValorCreditoContaCorrente() > 0) {

                    FormaPagamentoVO formaPagamentoContaCorrenteVO = formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (formaPagamentoContaCorrenteVO == null) {
                        throw new ServiceException("validacao_formaPagamento", "Forma pagamento CONTA CORRENTE não encontrada");
                    }
                    PagamentoDTO pagamentoDTO = new PagamentoDTO();
                    pagamentoDTO.setFormaPagamento(formaPagamentoContaCorrenteVO.getCodigo());
                    pagamentoDTO.setValor(recebimentoDTO.getValorCreditoContaCorrente());
                    recebimentoDTO.getPagamentos().add(pagamentoDTO);
                    pagamentos = obterListaMovPagamento(recebimentoDTO, pessoaVO, empresaVO, usuarioVO, con);
                    movimentoContaCorrenteClienteVOAtomicReference.set(montarContaCorrentePagarDebito(pessoaVO, recebimentoDTO.getValorCreditoContaCorrente(), con));
                    tipoOperacaoContaCorrenteEnumAtomicReference.set(TipoOperacaoContaCorrenteEnum.TOCC_Devolver);
                }

                //verificar se vai usar conta corrente para gerar crédito ou deixar na conta corrente
                if (!UteisValidacao.emptyNumber(recebimentoDTO.getValorSobressalenteResidual()) &&
                        recebimentoDTO.getValorSobressalenteResidual() > 0) {

//                    FormaPagamentoVO formaPagamentoContaCorrenteVO = formaPagamentoDAO.consultarPorTipoFormaPagamento(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla(), Uteis.NIVELMONTARDADOS_MINIMOS);
//                    if (formaPagamentoContaCorrenteVO == null) {
//                        throw new ServiceException("validacao_formaPagamento", "Forma pagamento CONTA CORRENTE não encontrada");
//                    }
//                    PagamentoDTO pagamentoDTO = new PagamentoDTO();
//                    pagamentoDTO.setFormaPagamento(formaPagamentoContaCorrenteVO.getCodigo());
//                    pagamentoDTO.setValor(valorDepositarContaCorrente);

                    double valorDepositarNaConta = recebimentoDTO.getValorSobressalenteResidual();

                    VendaAvulsaVO vendaAvulsaVO = movimentoContaCorrenteClienteDAO.gerarProdutoPagamentoCredito(valorDepositarNaConta, clienteVO, colaboradorVO, null,
                            usuarioVO, MovimentoContaCorrenteClienteVO.DESCRICAO_DEPOSITO_CONTA_ALUNO,
                            Calendario.hoje(), Calendario.hoje(), empresaVO);

                    MovParcelaVO movParcela = movParcelaDAO.consultarPorCodigoVendaAvulsa(vendaAvulsaVO.getCodigo(), "EA", false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    movParcela.setSituacao("EA");
                    recebimentoDTO.getParcelas().add(new MovParcelaDTO(movParcela));

                }

                //consultar as parcelas somente depois
                //pois é adicionado acima as parcelas de conta corrente e multa e juros caso necessário
                List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);
                validarDataPagamento(dataPagamento, listaParcelas);

                // verificar se houve alteração na data do pagamento
                if (dataPagamento != null &&
                        !Calendario.igual(dataPagamento, Calendario.hoje())) {
                    // atualiza os pagamentos para corresponder à data escolhida
                    pagamentos.forEach(movPagamentoVO -> {

                        movPagamentoVO.setDataAlteracaoManual(Calendario.hoje());

                        //alterar a data na threadlocal
                        Calendario.setDateThread(Calendario.getDataComHoraZerada(dataPagamento));

                        movPagamentoVO.setDataLancamento(dataPagamento);
                        if (!movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.BOLETOBANCARIO.getSigla())) {
                            movPagamentoVO.setDataPagamento(dataPagamento);
                            movPagamentoVO.setDataQuitacao(dataPagamento);
                        }
                    });
                }


//                pagamentos.forEach(movPagamentoVO -> {
//                    if (movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CREDITOCONTACORRENTE.getSigla())) {
//                        try {
//                            AtomicReference<PagamentoDTO> pagamentoDTOAto = new AtomicReference<>();
//                            recebimentoDTO.getPagamentos().forEach(pag -> {
//                                if (pag.getFormaPagamento().equals(movPagamentoVO.getFormaPagamento().getCodigo())) {
//                                    pagamentoDTOAto.set(pag);
//                                }
//                            });
//                            PagamentoDTO pagamentoDTO = pagamentoDTOAto.get();
//
//                            // usar o crédito q tem na conta corrente para pagar
//                            boolean usarCreditoContaCorrente = (!UteisValidacao.emptyNumber(recebimentoDTO.getValorCreditoContaCorrente()) && recebimentoDTO.getValorCreditoContaCorrente() > 0);
//                            //pagar usando a conta corrente
//                            boolean pagarUsandoContaCorrente = (!UteisValidacao.emptyNumber(pagamentoDTO.getValor()) && pagamentoDTO.getValor() > 0);
//
//
//                            // usar o crédito q tem na conta corrente para pagar
//                            if (usarCreditoContaCorrente) {
//                                movimentoContaCorrenteClienteVOAto.set(montarContaCorrente(movPagamentoVO.getPessoa(), recebimentoDTO.getValorCreditoContaCorrente(), con));
//                                tipoOperacaoContaCorrenteEnum.set(TipoOperacaoContaCorrenteEnum.TOCC_Devolver);
//                            }
//
//                            // pagar usando conta corrente
//                            if (pagarUsandoContaCorrente) {
//                                movimentoContaCorrenteClienteVOAto.set(montarContaCorrente(movPagamentoVO.getPessoa(), pagamentoDTO.getValor(), con));
//                                tipoOperacaoContaCorrenteEnum.set(TipoOperacaoContaCorrenteEnum.TOCC_Nenhum);
//                            }
//
//                            validarPagamentoChequeDevolvido(tipoOperacaoContaCorrenteEnum.get(), parcelas, con);
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                            throw new RuntimeException(e);
//                        }
//                    }
//                });

                if (tipoOperacaoContaCorrenteEnumAtomicReference.get() != null) {
                    validarPagamentoChequeDevolvido(tipoOperacaoContaCorrenteEnumAtomicReference.get(), listaParcelas, con);
                }

                MovPagamento movPagamentoDAO = new MovPagamento(con);
                ReciboPagamentoVO reciboVO = movPagamentoDAO.incluirListaPagamento(
                        pagamentos, listaParcelas,
                        movimentoContaCorrenteClienteVOAtomicReference.get() != null ? movimentoContaCorrenteClienteVOAtomicReference.get() : null,
                        obterContratoParcelas(listaParcelas),
                        false, null, false, null,
                        tipoOperacaoContaCorrenteEnumAtomicReference.get() != null ? tipoOperacaoContaCorrenteEnumAtomicReference.get() : null);
                ReciboPagamentoDTO reciboPagamentoDTO = new ReciboPagamentoDTO(reciboVO);

                OrigemCobrancaEnum origemCobrancaEnum = obterOrigemCobranca(recebimentoDTO);
                reciboVO.setOrigem(origemCobrancaEnum.getDescricao());
                incluirLogRecibo(reciboVO, usuarioVO, origemCobrancaEnum, con);

                try {
                    SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CAIXA_ABERTO_V2_RECEBER_PARCELA, usuarioVO, empresaVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                con.commit();
                return reciboPagamentoDTO;
            } catch (Exception ex) {
                con.rollback();
//                ex.printStackTrace();
                if (ex.getMessage() != null &&
                        ex.getMessage().contains("Produtos que geram crédito na conta corrente do aluno não podem")) {
                    throw new ServiceException("pagar", "Não é possível realizar o pagamento utilizando a conta corrente, pois a parcela selecionada gera crédito na conta do aluno.");
                }
                throw ex;
            } finally {
                con.setAutoCommit(true);
                Calendario.setDateThread(null);
            }
        }
    }

    private void validarPagamentoChequeDevolvido(TipoOperacaoContaCorrenteEnum tipoOperacaoContaCorrenteEnum,
                                                 List<MovParcelaVO> listaParcelas, Connection con) throws Exception {
        // o sistema deve proibir o uso da conta corrente do cliente para pagar produtos de devolução de cheques
        if (!tipoOperacaoContaCorrenteEnum.equals(tipoOperacaoContaCorrenteEnum.TOCC_Devolver)) {
            MovParcela movParcelaDAO = new MovParcela(con);
            for (MovParcelaVO parcela : listaParcelas) {
                try {
                    if (movParcelaDAO.parcelaPagaChequeDevolvido(parcela.getCodigo())) {
                        throw new ConsistirException("Não é permitido quitar parcela(s) de cheque(s) devolvido(s) deixando débito na conta corrente do aluno. A indicação é utilizar a renegociação de parcelas, dividindo em um número de parcelas maior e em valores que serão quitados a cada pagamento");
                    }
                } catch (ConsistirException ce) {
                    throw ce;
                } catch (Exception ignored) {
                }
            }
        }
    }

    private ContratoVO obterContratoParcelas(List<MovParcelaVO> parcelas) {
        //verificar se todas as parcelas são do mesmo contrato;
        Integer contrato = parcelas.get(0).getContrato().getCodigo();
        for (MovParcelaVO movParcelaVO : parcelas) {
            if (!contrato.equals(movParcelaVO.getContrato().getCodigo())) {
                return null;
            }
        }
        ContratoVO contratoVO = new ContratoVO();
        contratoVO.setCodigo(contrato);
        return contratoVO;
    }

    private List<MovParcelaVO> obterListaMovParcela(RecebimentoDTO recebimentoDTO, Connection con) throws Exception {
        MovParcela movParcelaDAO = new MovParcela(con);

        StringBuilder parcelasCod = new StringBuilder();
        for (MovParcelaDTO movParcelaDTO : recebimentoDTO.getParcelas()) {
            if (!UteisValidacao.emptyNumber(movParcelaDTO.getCodigo())) {
                parcelasCod.append(",").append(movParcelaDTO.getCodigo());
            }
        }

        if (UteisValidacao.emptyNumber(parcelasCod.length())) {
            throw new ServiceException("validacao_parcelas", "Nenhuma parcela informada.");
        }

        List<MovParcelaVO> listaMovParcelas = movParcelaDAO.consultar("select * from movparcela where codigo in (" + parcelasCod.toString().replaceFirst(",", "") + ")", Uteis.NIVELMONTARDADOS_TODOS);

        if (UteisValidacao.emptyList(listaMovParcelas)) {
            throw new ServiceException("validacao_parcelas", "Nenhuma parcela encontrada.");
        }
        if (recebimentoDTO.getParcelas().size() != listaMovParcelas.size()) {
            throw new ServiceException("validacao_parcelas", "A quantidade de parcelas encontrada é diferente da quantidade informada.");
        }

        Integer empresa = listaMovParcelas.get(0).getEmpresa().getCodigo();
        for (MovParcelaVO movParcelaVO : listaMovParcelas) {
            try {
                if (!empresa.equals(movParcelaVO.getEmpresa().getCodigo())) {
                    throw new ServiceException("validacao_parcelas", "As parcelas devem ser da mesma empresa.");
                }
                if (!movParcelaVO.getSituacao().equalsIgnoreCase("EA")) {
                    throw new ServiceException("validacao_parcelas", "A parcela \"" + movParcelaVO.getDescricao() + "\" não está em aberto.");
                }
                movParcelaDAO.validarMovParcelaComTransacaoConcluidaOuPendente(movParcelaVO.getCodigo());
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new ServiceException("validacao_parcelas", ex.getMessage());
            }
        }

        return listaMovParcelas;
    }

    private void validarParcelasEmpresa(EmpresaVO empresaVO, List<MovParcelaVO> listaParcelas) throws ServiceException {
        Integer empresaParcelas = listaParcelas.get(0).getEmpresa().getCodigo();
        for (MovParcelaVO movParcelaVO : listaParcelas) {
            try {
                if (!empresaParcelas.equals(movParcelaVO.getEmpresa().getCodigo())) {
                    throw new ServiceException("validacao_parcelas", "As parcelas devem ser da mesma empresa.");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new ServiceException("validacao_parcelas", ex.getMessage());
            }
        }

        if (!empresaParcelas.equals(empresaVO.getCodigo())) {
            throw new ServiceException("validacao_parcelas", "As parcelas devem ser da mesma empresa que está sendo realizado a operação.");
        }
    }

    private List<MovPagamentoVO> obterListaMovPagamento(RecebimentoDTO recebimentoDTO, PessoaVO pessoaVO,
                                                        EmpresaVO empresaVO, UsuarioVO usuarioVO, Connection con) throws Exception {

        Set<String> autorizacaoMesmoRecebimento = new HashSet<>();
        Set<String> nsuMesmoRecebimento = new HashSet<>();

        List<MovPagamentoVO> lista = new ArrayList<>();
        for (PagamentoDTO dto : recebimentoDTO.getPagamentos()) {

            if (UteisValidacao.emptyNumber(recebimentoDTO.getValor()) &&
                    recebimentoDTO.getPagamentos().size() == 1 &&
                    UteisValidacao.emptyNumber(dto.getValor())) {
                continue;
            }

            MovPagamentoVO obj = new MovPagamentoVO();
            obj.setMovPagamentoEscolhida(true);
            obj.setValor(dto.getValor());
            obj.setValorTotal(dto.getValor());
            obj.setUsuarioVO(usuarioVO);
            obj.setResponsavelPagamento(usuarioVO);
            obj.setPessoa(pessoaVO);
            obj.setEmpresa(empresaVO);
            obj.setNomePagador(pessoaVO.getNome());
            obj.setObservacao(recebimentoDTO.getObservacao());

            if (UteisValidacao.emptyNumber(dto.getFormaPagamento())) {
                throw new ServiceException("validacao_formaPagamento", "Nenhuma forma pagamento informada");
            }

            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            obj.setFormaPagamento(formaPagamentoDAO.consultarPorChavePrimaria(dto.getFormaPagamento(), Uteis.NIVELMONTARDADOS_TODOS));

            if (obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla()) ||
                    obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAODEBITO.getSigla())) {

                obj.setNsu(dto.getNsu());
                obj.setAutorizacaoCartao(dto.getAutorizacao());

                if (obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                    obj.setNrParcelaCartaoCredito(dto.getNrParcelas());
                }

            } else if (obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CHEQUE.getSigla())) {

                obj.setChequeVOs(obterChequesVO(dto, obj.getFormaPagamento(), con));

            } else if (obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.BOLETOBANCARIO.getSigla())) {

                if (UteisValidacao.emptyNumber(dto.getDataQuitacao())) {
                    throw new ServiceException("validacao_dataQuitacao", "O campo DATA DE QUITAÇÃO da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
                }
                obj.setDataQuitacao(new Date(dto.getDataQuitacao()));

                if (UteisValidacao.emptyNumber(dto.getConvenioCobranca())) {
                    throw new ServiceException("validacao_convenioCobranca", "O campo CONVÊNIO DE COBRANÇA da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
                }
                ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
                obj.setConvenio(convenioCobrancaDAO.consultarPorChavePrimaria(dto.getConvenioCobranca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));

                if (obj.getConvenio().getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK) &&
                        UteisValidacao.emptyNumber(dto.getDataCredito())) {
                    throw new ServiceException("validacao_dataCredito", "O campo DATA CRÉDITO da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
                }
                obj.setDataPagamento(new Date(dto.getDataCredito()));

            }

            if (!UteisValidacao.emptyNumber(dto.getAdquirente())) {
                Adquirente adquirenteDAO = new Adquirente(con);
                obj.setAdquirenteVO(adquirenteDAO.consultarPorCodigo(dto.getAdquirente()));
            }

            if (!UteisValidacao.emptyNumber(dto.getOperadoraCartao())) {
                OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);
                obj.setOperadoraCartaoVO(operadoraCartaoDAO.consultarPorChavePrimaria(dto.getOperadoraCartao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            if (!UteisValidacao.emptyNumber(dto.getConvenioCobranca())) {
                ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
                obj.setConvenio(convenioCobrancaDAO.consultarPorChavePrimaria(dto.getConvenioCobranca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            //Adicionado essa validação porquê tem cliente que lança dois Cartões Crédito ou Cartão Débito com a mesma Autorização ou mesmo NSU e depois abri suporte reclamando da Conciliação.
            if (!UteisValidacao.emptyString(obj.getAutorizacaoCartao()) && !autorizacaoMesmoRecebimento.contains(obj.getAutorizacaoCartao().toUpperCase())) {
                autorizacaoMesmoRecebimento.add(obj.getAutorizacaoCartao().toUpperCase());
            } else if (!UteisValidacao.emptyString(obj.getAutorizacaoCartao())) {
                throw new ServiceException("validacao_autorizacao_duplicado", "Os códigos de autorização precisam ser diferentes para um mesmo pagamento.");
            }
            if (!UteisValidacao.emptyString(obj.getNsu()) && !nsuMesmoRecebimento.contains(obj.getNsu().toUpperCase())) {
                nsuMesmoRecebimento.add(obj.getNsu().toUpperCase());
            } else if (!UteisValidacao.emptyString(obj.getNsu())) {
                throw new ServiceException("validacao_nsu_duplicado", "Os NSU precisam ser diferentes para um mesmo pagamento.");
            }

            validarDados(obj);
            lista.add(obj);
        }
        return lista;
    }

    private void validarDadosRecebimento(RecebimentoDTO obj, TipoCobrancaEnum tipoCobrancaEnum) throws ServiceException {
        if (UteisValidacao.emptyNumber(obj.getEmpresa())) {
            throw new ServiceException("validacao_empresa", "O campo EMPRESA deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getUsuario())) {
            throw new ServiceException("validacao_usuario", "O campo USUÁRIO deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getPessoa())) {
            if (UteisValidacao.emptyString(obj.getTipoComprador()) ||
                    !obj.getTipoComprador().equalsIgnoreCase("CN")) {
                throw new ServiceException("validacao_pessoa", "O campo PESSOA deve ser informado.");
            }
        }
        if (!UteisValidacao.emptyString(obj.getTipoComprador()) &&
                obj.getTipoComprador().equalsIgnoreCase("CN") &&
                UteisValidacao.emptyString(obj.getNomeComprador())) {
            throw new ServiceException("validacao_nomeComprador", "O campo NOME DO COMPRADOR deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getValor()) &&
                UteisValidacao.emptyNumber(obj.getValorCreditoContaCorrente())) {
            throw new ServiceException("validacao_valor", "O campo VALOR deve ser informado.");
        }
        if (!UteisValidacao.emptyNumber(obj.getValorDebitoContaCorrente()) &&
                !UteisValidacao.emptyNumber(obj.getValorCreditoContaCorrente())) {
            throw new ServiceException("validacao_conta_corrente", "Dados incorretos, foi informado débito e crédito da conta corrente");
        }
        if (tipoCobrancaEnum != null) {
            if (tipoCobrancaEnum.equals(TipoCobrancaEnum.ONLINE)) {
                validarDadosCartao(obj);
            } else if (tipoCobrancaEnum.equals(TipoCobrancaEnum.PIX)) {
                validarDadosPix(obj);
            } else if (tipoCobrancaEnum.equals(TipoCobrancaEnum.BOLETO) ||
                    tipoCobrancaEnum.equals(TipoCobrancaEnum.BOLETO_ONLINE)) {
                validarDadosBoleto(obj);
            } else if (tipoCobrancaEnum.equals(TipoCobrancaEnum.PINPAD)) {
                validarDadosPinpad(obj);
            }
        }
    }

    private void validarDadosCartao(RecebimentoDTO obj) throws ServiceException {
        if (obj.getCartao() == null) {
            throw new ServiceException("validacao_cartao", "O campo CARTAO deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getCartao().getFormaPagamento())) {
            throw new ServiceException("validacao_formaPagamento", "O campo FORMA DE PAGAMENTO deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getCartao().getConvenioCobranca())) {
            throw new ServiceException("validacao_convenioCobranca", "O campo CONVÊNIO DE COBRANÇA deve ser informado.");
        }
//        if (UteisValidacao.emptyNumber(obj.getCartao().getOperadoraCartao())) {
//            throw new ServiceException("operadoraCartao", "O campo OPERADORA de CARTÃO deve ser informado.");
//        }
        if (UteisValidacao.emptyNumber(obj.getCartao().getNrParcelas())) {
            throw new ServiceException("validacao_nrParcelas", "O campo NÚMERO DE PARCELAS deve ser informado.");
        }

        if (UteisValidacao.emptyString(obj.getCartao().getTokenAragorn()) &&
                UteisValidacao.emptyString(obj.getCartao().getNumero())) {
            throw new ServiceException("validacao_tokenAragornNrCartao", "O campo TOKEN ou NUMERO DO CARTAO deve ser informado.");
        }

        //validar dados do cartao
        if (!UteisValidacao.emptyString(obj.getCartao().getNumero())) {
            if (UteisValidacao.emptyString(obj.getCartao().getTitular())) {
                throw new ServiceException("validacao_titular", "O campo TITULAR deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCartao().getDocumento())) {
                throw new ServiceException("validacao_documento", "O campo DOCUMENTO deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(obj.getCartao().getMesValidade())) {
                throw new ServiceException("validacao_mesValidade", "O campo MES VALIDADE deve ser informado.");
            }
            if (UteisValidacao.emptyNumber(obj.getCartao().getAnoValidade())) {
                throw new ServiceException("validacao_anoValidade", "O campo ANO VALIDADE deve ser informado.");
            }
            if (UteisValidacao.emptyString(obj.getCartao().getCvv())) {
                throw new ServiceException("validacao_cvv", "O campo CVV deve ser informado.");
            }
        }
    }

    private void validarDadosPix(RecebimentoDTO obj) throws ServiceException {
        if (obj.getPix() == null) {
            throw new ServiceException("validacao_pix", "O campo PIX deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getPix().getFormaPagamento())) {
            throw new ServiceException("validacao_formaPagamento", "O campo FORMA DE PAGAMENTO deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getPix().getConvenioCobranca())) {
            throw new ServiceException("validacao_convenioCobranca", "O campo CONVÊNIO DE COBRANÇA deve ser informado.");
        }
    }

    private void validarDadosBoleto(RecebimentoDTO obj) throws ServiceException {
        if (obj.getBoleto() == null) {
            throw new ServiceException("validacao_boleto", "O campo BOLETO deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getBoleto().getConvenioCobranca())) {
            throw new ServiceException("validacao_convenioCobranca", "O campo CONVÊNIO DE COBRANÇA deve ser informado.");
        }

        if (obj.getBoleto().getBoletoUnico() == null) {
            throw new ServiceException("validacao_boletoUnico", "O campo BOLETO UNICO deve ser informado.");
        }
        //se informou boleto unico tem q informar a data de vencimento
        if (obj.getBoleto().getBoletoUnico() &&
                UteisValidacao.emptyNumber(obj.getBoleto().getDataVencimento())) {
            throw new ServiceException("validacao_dataVencimento", "O campo DATA DE VENCIMENTO deve ser informado.");
        }

        if (!UteisValidacao.emptyNumber(obj.getBoleto().getDataVencimento()) &&
                Calendario.menor(new Date(obj.getBoleto().getDataVencimento()), Calendario.hoje())) {
            throw new ServiceException("validacao_dataVencimento", "A DATA DE VENCIMENTO do boleto não pode ser menor que hoje.");
        }
    }

    private void validarDadosPinpad(RecebimentoDTO obj) throws ServiceException {
        if (obj.getBoleto() == null) {
            throw new ServiceException("validacao_pinpad", "O campo PINPAD deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getPinpad().getPinpad())) {
            throw new ServiceException("validacao_pinpad", "O campo CÓDIGO DO PINPAD deve ser informado.");
        }
        if (UteisValidacao.emptyNumber(obj.getPinpad().getFormaPagamento())) {
            throw new ServiceException("validacao_formaPagamento", "O campo FORMA DE PAGAMENTO deve ser informado.");
        }
        if (!UteisValidacao.emptyNumber(obj.getValorSobressalenteResidual())) {
            throw new ServiceException("validacao_valorSobressalenteResidual", "Não foi quitado o valor total do pagamento. "
                    + "Ainda falta ser pago: " + Formatador.formatarValorMonetario(obj.getValorSobressalenteResidual()));
        }
//        if (UteisValidacao.emptyNumber(obj.getPinpad().getConvenioCobranca())) {
//            throw new ServiceException("validacao_convenioCobranca", "O campo CONVÊNIO DE COBRANÇA deve ser informado.");
//        }
    }

    private void validarDados(MovPagamentoVO obj) throws ServiceException {
        if (obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla()) ||
                obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAODEBITO.getSigla())) {

            if (UteisValidacao.emptyNumber(obj.getOperadoraCartaoVO().getCodigo())) {
                throw new ServiceException("validacao_operadoraCartao", "O campo OPERADORA DE CARTÃO da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
            }

            //somente para cartão de crédito
            if (obj.getFormaPagamento().getTipoFormaPagamento().equalsIgnoreCase(TipoFormaPagto.CARTAOCREDITO.getSigla())) {
                if (UteisValidacao.emptyNumber(obj.getNrParcelaCartaoCredito())) {
                    throw new ServiceException("validacao_nrParcelas", "O campo NÚMERO DE PARCELAS da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
                }
            }

            if ((obj.getFormaPagamento().isExigeCodAutorizacao() ||
                    obj.getEmpresa().isObrigatorioPreencherCamposCartao()) &&
                    UteisValidacao.emptyString(obj.getAutorizacaoCartao().trim())) {
                throw new ServiceException("validacao_autorizacao", "O campo AUTORIZAÇÃO da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
            }
            if (obj.getFormaPagamento().isApresentarNSU() &&
                    obj.getEmpresa().isObrigatorioPreencherCamposCartao() &&
                    UteisValidacao.emptyString(obj.getNsu().trim())) {
                throw new ServiceException("validacao_nsu", "O campo NSU da forma de pagamento \"" + obj.getFormaPagamento().getDescricao() + "\" deve ser informado.");
            }
        }

        try {
            MovPagamentoVO.validarDadosEspecial(obj, 0.0, null);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("validacao_movpagamento_geral", ex.getMessage());
        }
    }

    private void validarConvenio(ConvenioCobrancaVO obj, TipoCobrancaEnum tipo) throws ServiceException {
        if (!obj.getTipo().getTipoCobranca().equals(tipo)) {
            throw new ServiceException("validacao_convenioCobranca", "O convênio \"" + obj.getDescricao() + "\" é inválido para \"" + tipo.getDescricao() + "\".");
        }
    }

    private void validarFormaPagamento(FormaPagamentoVO obj, TipoFormaPagto tipo) throws ServiceException {
        if (!obj.getTipoFormaPagamento().equalsIgnoreCase(tipo.getSigla())) {
            throw new ServiceException("validacao_formaPagamento", "A forma de pagamento \"" + obj.getDescricao() + "\" é inválida para \"" + tipo.getDescricao() + "\".");
        }
    }

    private List<ChequeVO> obterChequesVO(PagamentoDTO pagamentoDTO, FormaPagamentoVO formaPagamentoVO, Connection con) throws Exception {
        if (pagamentoDTO.getCheques() == null) {
            throw new ServiceException("validacao_cheques", "Cheques não informados");
        }
        List<ChequeVO> lista = new ArrayList<>();
        Map<Integer, BancoVO> mapaBanco = new HashMap<>();
        Double valorTotalCheques = 0.0;
        for (ChequeDTO chequeDTO : pagamentoDTO.getCheques()) {
            ChequeVO chequeVO = new ChequeVO();
            chequeVO.setValor(chequeDTO.getValor());
            chequeVO.setValorTotal(chequeDTO.getValor());
            chequeVO.setAgencia(chequeDTO.getAgencia());
            chequeVO.setConta(chequeDTO.getConta());
            chequeVO.setNumero(chequeDTO.getNumero());
            chequeVO.setNomeNoCheque(chequeDTO.getNome());

            valorTotalCheques += chequeVO.getValorTotal();

            String documento = chequeDTO.getDocumento();
            if (documento != null) {
                String documentoMasc = Uteis.formatarCpfCnpj(documento, false);
                chequeVO.setCpfOuCnpj(documentoMasc);
                if (documentoMasc.length() <= 14) {
                    chequeVO.setCpf(documentoMasc);
                } else {
                    chequeVO.setCnpj(documentoMasc);
                }
            }

            if (UteisValidacao.emptyNumber(chequeDTO.getDataCompensacao())) {
                throw new ServiceException("validacao_cheques_dataCompensacao", "O campo DATA COMPENSAÇÃO DO CHEQUE da forma de pagamento \"" + formaPagamentoVO.getDescricao() + "\" deve ser informado.");
            }
            chequeVO.setDataCompensacao(Calendario.getDataComHoraZerada(new Date(chequeDTO.getDataCompensacao())));

            if (!mapaBanco.containsKey(chequeDTO.getBanco())) {
                if (UteisValidacao.emptyNumber(chequeDTO.getBanco())) {
                    throw new ServiceException("validacao_cheques_banco", "O campo BANCO DO CHEQUE da forma de pagamento \"" + formaPagamentoVO.getDescricao() + "\" deve ser informado.");
                }
                Banco bancoDAO = new Banco(con);
                BancoVO bancoVO = bancoDAO.consultarPorChavePrimaria(chequeDTO.getBanco(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                mapaBanco.put(chequeDTO.getBanco(), bancoVO);
            }
            chequeVO.setBanco(mapaBanco.get(chequeDTO.getBanco()));

            try {
                ChequeVO.validarDados(chequeVO);
            } catch (Exception ex) {
                throw new ServiceException("validacao_cheques", ex.getMessage().replace(" (Movimento do Pagamento)", ""));
            }
            lista.add(chequeVO);

        }

        if (Uteis.arredondarForcando2CasasDecimais(valorTotalCheques) !=
                Uteis.arredondarForcando2CasasDecimais(pagamentoDTO.getValor())) {
            throw new ServiceException("validacao_cheques", "O valor total (" + Uteis.arredondarForcando2CasasDecimais(valorTotalCheques) +
                    ") dos cheques não é igual ao pagamento (" + Uteis.arredondarForcando2CasasDecimais(pagamentoDTO.getValor()) + ").");
        }
        return lista;
    }

    public PixDTO gerarPix(RecebimentoDTO recebimentoDTO) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Usuario usuarioDAO = new Usuario(con);
            Pessoa pessoaDAO = new Pessoa(con);
            Empresa empresaDAO = new Empresa(con);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            Pix pixDAO = new Pix(con);
            MovParcela movParcelaDAO = new MovParcela(con);

            validarDadosRecebimento(recebimentoDTO, TipoCobrancaEnum.PIX);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(recebimentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PessoaVO pessoaVO = obterPessoaVO(recebimentoDTO, false, con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(recebimentoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(recebimentoDTO.getPix().getFormaPagamento(), Uteis.NIVELMONTARDADOS_TODOS);
            validarFormaPagamento(formaPagamentoVO, TipoFormaPagto.PIX);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(recebimentoDTO.getPix().getConvenioCobranca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            validarConvenio(convenioCobrancaVO, TipoCobrancaEnum.PIX);
            List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);
            Date dataPagamento = recebimentoDTO.getDataPagamento() != null ? new Date(recebimentoDTO.getDataPagamento()) : Calendario.hoje();
            validarDataPagamento(dataPagamento, listaParcelas);

            if (recebimentoDTO.isCobrarMultaJuros()) {
                movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, dataPagamento,
                        false, 1.0, null);
            }

            //validar crédito pacto
            pixDAO.validarCreditosPacto(empresaVO);

            //é chamado somente para validar o cpf
            try {
                pixDAO.obterCPF(pessoaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new ServiceException("validacao_pessoa", ex.getMessage());
            }

            validarPagamentoValorTotal(recebimentoDTO, listaParcelas);

            PixVO pixVO = pixDAO.gerarObterPix(obterChave(),
                    pessoaVO,
                    convenioCobrancaVO,
                    empresaVO,
                    listaParcelas,
                    usuarioVO,
                    formaPagamentoVO.getCodigo(),
                    obterOrigemCobranca(recebimentoDTO),
                    recebimentoDTO.isCobrarMultaJuros());

            try {
                SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CAIXA_ABERTO_V2_RECEBER_PARCELA_PIX, usuarioVO, empresaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            return new PixDTO(pixVO, obterChave());
        }
    }

    private void validarPagamentoValorTotal(RecebimentoDTO recebimentoDTO, List<MovParcelaVO> listaParcelas) throws Exception {
        AtomicReference<Double> valorTotal = new AtomicReference<>(0.0);
        listaParcelas.forEach(pac -> {
            valorTotal.updateAndGet(v -> v + (recebimentoDTO.isCobrarMultaJuros() ? pac.getValorParcela() + pac.getValorMulta() + pac.getValorJuros() : pac.getValorParcela()));
        });
        if (Uteis.arredondarForcando2CasasDecimais(valorTotal.get()) !=
                Uteis.arredondarForcando2CasasDecimais(recebimentoDTO.getValor())) {
            throw new ServiceException("validacao_valor_total_pagamento", "O valor pago não é igual a soma das parcelas.");
        }
    }

    public PixDTO consultarPix(Integer codigoPix) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Pix pixDAO = new Pix(con);

            PixVO pixVO = pixDAO.consultarPorCodigo(codigoPix);

            if (!pixVO.getStatusEnum().equals(PixStatusEnum.CONCLUIDA)) {
                boolean consultarNoBancoDeDados = pixVO.getConveniocobranca().isConsultarPixNoBancoDeDados(isRedePratique());
                //consultar a situação no banco de dados. O Webhook que muda o Status do pix para PAGO.
                if (pixVO.getConveniocobranca() != null && consultarNoBancoDeDados) {
                    boolean pixFoiPago = pixDAO.isPixPago(pixVO.getCodigo());
                    if (pixFoiPago) {
                        pixVO.setStatus(PixStatusEnum.CONCLUIDA.getDescricao());
                        try {
                            pixVO.setReciboPagamento(pixDAO.consultarCodigoReciboPorCodigoPix(pixVO.getCodigo()));
                        } catch (Exception ex) {
                            ex.printStackTrace();
                            Uteis.logarDebug("Não foi possível obter o recibo do pix: " + ex.getMessage());
                        }
                    }
                } else {
                    //Aqui ainda está consultando lá na API do banco por enquanto
                    PixPagamentoService pixPagamentoService = new PixPagamentoService(con);
                    PixVO pixVOConsulta = pixPagamentoService.processarPixControlandoTransacao(pixVO.getCodigo());
                    pixVO.setStatus(pixVOConsulta.getStatus());
                }
            }
            return new PixDTO(pixVO, key);
        }
    }

    public void enviarEmailPix(EmailPixDTO dto) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Pix pixDAO = new Pix(con);
            Usuario usuarioDAO = new Usuario(con);
            Empresa empresaDAO = new Empresa(con);

            if (Uteis.isValidEmailAddressRegex(dto.getEmail())) {
                PixVO pixVO = pixDAO.consultarPorCodigo(dto.getPix());
                UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(this.requestService.getUsuarioAtual().getCodZw(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(pixVO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                ClientDiscoveryDataDTO clientDiscoveryDataDTO = DiscoveryMsService.urlsChave(key);
                PixEmailService.enviarEmail(dto.getEmail(),
                        pixVO,
                        clientDiscoveryDataDTO.getServiceUrls().getZwUrl(),
                        empresaVO,
                        key,
                        getConfiguracaoSMTPNoReply(),
                        usuarioVO);
            } else {
                throw new ServiceException("email", "Informe um email válido");
            }
        }
    }

    private boolean isRedePratique() {
        //Como todas as unidades da pratique usam a mesma conta bancária, então não é possível configurar o webhook pois o webhook é uma URL por chave pix.
        //Momentaneamente quando for rede pratique continuará sem webhook até que a integração do Pix Inter fique pronta.
        try {
            ClientDiscoveryDataDTO discoveryDataDTO = DiscoveryMsService.urlsChave(obterChave());
            if (discoveryDataDTO != null &&
                    discoveryDataDTO.getRedeEmpresas() != null &&
                    !discoveryDataDTO.getRedeEmpresas().isEmpty() &&
                    discoveryDataDTO.getRedeEmpresas().get(0).getChaveRede().equals("341b908afd7637c1d5b09f248d3498f1")) {
                return true;
            }
        } catch (Exception ex) {
            return false;
        }
        return false;
    }

    public List<PinpadDTO> aparelhosPinpad(Integer formaPagamento, Integer empresa) throws Exception {
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            PinPad pinPadDAO = new PinPad(con);
            List<PinPadVO> listaPinpad = pinPadDAO.consultarPorFormaEmpresa(formaPagamento, empresa, null);
            List<PinpadDTO> listaRet = new ArrayList<>();
            listaPinpad.forEach(p -> {
                listaRet.add(new PinpadDTO(p));
            });
            return listaRet;
        }
    }

    public PinpadPedidoDTO gerarPinpadPedido(RecebimentoDTO recebimentoDTO) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Usuario usuarioDAO = new Usuario(con);
            Pessoa pessoaDAO = new Pessoa(con);
            Empresa empresaDAO = new Empresa(con);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            PinPad pinPadDAO = new PinPad(con);
            PinPadPedido pinPadPedidoDAO = new PinPadPedido(con);

            validarDadosRecebimento(recebimentoDTO, TipoCobrancaEnum.PINPAD);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(recebimentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(recebimentoDTO.getPessoa(), Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(recebimentoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(recebimentoDTO.getPinpad().getFormaPagamento(), Uteis.NIVELMONTARDADOS_TODOS);
            PinPadVO pinPadVO = pinPadDAO.consultarPorChavePrimaria(recebimentoDTO.getPinpad().getPinpad());
            List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);


//                validarDadosPagamento(pinpad.getPinpadEnum());
//                validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "MovPagamento", ConstantesAcesso.INCLUIR);
//                validarPermissaoEntidade(getEmpresaLogado(), getUsuarioLogado(), "VendaAvulsa", ConstantesAcesso.INCLUIR);

//                //Venda Avulsa Consumidor
//                if (UteisValidacao.emptyString(obj.getPessoa().getNome()) && !UteisValidacao.emptyString(obj.getTipoPagador())
//                        && obj.getTipoPagador().equals("CN")) {
//                    tratarPessoaConsumidor(obj);
//                }

//                if (obj.getValorTotal() < getTotalDivida()) {
//                    throw new Exception("O valor total do pagamento deve ser quitado apenas pelo pinpad, não sendo permitido mais de uma forma de pagamento");
//                }

            if (formaPagamentoVO.getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla()) &&
                    UteisValidacao.emptyNumber(recebimentoDTO.getPinpad().getNrParcelas())) {
                throw new ServiceException("validacao_nrParcelas", "O campo NR PARCELAS deve ser informado.");
            }

//                if (getAbrirRichModalConfirmacaoPagamento()) {
//                    ///criar pedido stonne connect


            PinpadTO pinpadTO = new PinpadTO();
            pinpadTO.setValorPinpad(recebimentoDTO.getValor());
            pinpadTO.setFormaPagamento(formaPagamentoVO.getCodigo());
            pinpadTO.setPinpadEnum(pinPadVO.getOpcoesPinpadEnum());
            pinpadTO.setNrParcelas(recebimentoDTO.getPinpad().getNrParcelas());
//            pinpadTO.setTipo(obj.getTipoParcelamento());

            PinPadPedidoVO pinPadPedidoVO;
            if (pinPadVO.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {

                StoneConnectService.validarPedidoAguardandoEFechar(pinPadVO, con);

                pinPadPedidoVO = (StoneConnectService.gerarPedido(obterChave(), formaPagamentoVO.getTipoFormaPagamentoEnum(),
                        pessoaVO, empresaVO, pinPadVO, pinpadTO, formaPagamentoVO,
                        obterOrigemCobranca(recebimentoDTO), usuarioVO, listaParcelas, con));

                if (pinPadPedidoVO.getStatus() == null ||
                        !pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {
                    throw new Exception(pinPadPedidoVO.getMsg());
                }

            } else if (pinPadVO.getPinpadEnum().equals(OpcoesPinpadEnum.GETCARD)) {

                pinPadPedidoVO = pinPadPedidoDAO.criarPedido(pessoaVO, recebimentoDTO.getValor(),
                        pinPadVO.getPinpadEnum(), empresaVO, pinPadVO.getConvenioCobranca(),
                        pinPadVO.getPdvPinpad(), formaPagamentoVO, obterOrigemCobranca(recebimentoDTO),
                        usuarioVO, listaParcelas, pinpadTO.getNrParcelas());
//                        this.setPinPadPedidoVO(pedidoVO);
//                        pedidoVO.setParamsEnvio(this.getBodyPinPadGetCard());
//                        pinPadPedidoDAO.alterar(pedidoVO);
//                        this.setOnCompletePinpad("cobrarGetcard();");

            } else {
                throw new Exception("Não implementado.");
            }

//                } else {
//                    //erro
//                    limparPinpadSelecionado();
//                    this.setOnCompletePinpad(getMensagemNotificar());
//                }

            try {
                SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CAIXA_ABERTO_V2_RECEBER_PARCELA_PINPAD, usuarioVO, empresaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            return new PinpadPedidoDTO(pinPadPedidoVO);
        }
    }

    public PinpadPedidoDTO consultarPinpadPedido(Integer codigoPinpadPedido, String body) throws Exception {
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            PinPadPedido pinpadPedidoDAO = new PinPadPedido(con);
            OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);
            Adquirente adquirenteDAO = new Adquirente(con);
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);
            Pessoa pessoaDAO = new Pessoa(con);
            Usuario usuarioDAO = new Usuario(con);
            MovPagamento movPagamentoDAO = new MovPagamento(con);
            PinPadPedidoMovParcela pinPadPedidoMovParcelaDAO = new PinPadPedidoMovParcela(con);
            MovParcela movParcelaDAO = new MovParcela(con);

            PinPadPedidoVO pinPadPedidoVO = pinpadPedidoDAO.consultarPorChavePrimaria(codigoPinpadPedido);

            if (pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {

                PedidoRetornoDTO pedidoRetornoDTO = null;

                if (pinPadPedidoVO.getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {

                    pedidoRetornoDTO = pinpadPedidoDAO.consultarPedido(pinPadPedidoVO);

                } else if (pinPadPedidoVO.getPinpad().equals(OpcoesPinpadEnum.GETCARD)) {
                    try {
                        pedidoRetornoDTO = new PedidoRetornoDTO();
                        pedidoRetornoDTO.setDados(body);
                        pinPadPedidoVO.setParamsResp(body);

                        JSONObject jsonResp = new JSONObject(pinPadPedidoVO.getParamsResp());
                        JSONObject response = jsonResp.getJSONObject("response");
                        pedidoRetornoDTO.setStatus(response.optBoolean("Sucesso") ? StatusPinpadEnum.PAGO : StatusPinpadEnum.AGUARDANDO);

                        if (response.optBoolean("Sucesso")) {
                            String cupomReduzido = response.getString("CupomReduzido").replaceAll("\n", " ");

                            try {
                                String autorizacao = "";
                                if (cupomReduzido.contains("AUT=")) {
                                    autorizacao = cupomReduzido.split("AUT=")[1].split(" ")[0];
                                } else if (cupomReduzido.contains("AUT:")) {
                                    autorizacao = cupomReduzido.split("AUT:")[1].split(" ")[0];
                                }
                                pedidoRetornoDTO.setAutorizacao(autorizacao.trim());
                            } catch (Exception ignored) {
                            }

                            try {
                                String nsu = "";
                                if (cupomReduzido.contains("DOC=")) {
                                    nsu = cupomReduzido.split("DOC=")[1].split(" ")[0];
                                } else if (cupomReduzido.contains("NSU:")) {
                                    nsu = cupomReduzido.split("NSU:")[1].split(" ")[0];
                                } else if (cupomReduzido.contains("NSU=")) {
                                    nsu = cupomReduzido.split("NSU=")[1].split(" ")[0];
                                }

                                pedidoRetornoDTO.setNsu(nsu.trim());
                            } catch (Exception ignored) {
                            }

                            try {
                                String bandeira = cupomReduzido.split(" ")[0];
                                pedidoRetornoDTO.setBandeira(bandeira.trim());
                            } catch (Exception ignored) {
                            }

                            try {
                                String cupomCliente = response.getString("CupomCliente");
                                String adquirente = "";
                                if (cupomCliente.toUpperCase().contains("CIELO")) {
                                    adquirente = "CIELO";
                                } else if (cupomCliente.toUpperCase().contains("GETNET") ||
                                        cupomCliente.toUpperCase().contains("GET NET")) {
                                    adquirente = "GETNET";
                                } else if (cupomCliente.toUpperCase().contains("STONE")) {
                                    adquirente = "STONE";
                                }
                                pedidoRetornoDTO.setAdquirente(adquirente);
                            } catch (Exception ignored) {
                            }
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }

                if (pedidoRetornoDTO != null &&
                        pedidoRetornoDTO.getStatus() != null &&
                        !pedidoRetornoDTO.getStatus().equals(StatusPinpadEnum.AGUARDANDO)) {

                    pinPadPedidoVO.setStatus(pedidoRetornoDTO.getStatus());

                    if (pinPadPedidoVO.getStatus().equals(StatusPinpadEnum.PAGO)) {
//                        setConfirmarPinpad(false);
//                        pinpad.setCodigoAutorizacao(pedidoRetornoDTO.getAutorizacao());
//                        pinpad.setCodigoNSU(pedidoRetornoDTO.getNsu());
//                        pinpad.setBandeira(pedidoRetornoDTO.getBandeira());
//                        pinpad.setRespostaRequisicao(pedidoRetornoDTO.getDados());
//                        pinpad.setConvenioCobranca(this.getPinPadPedidoVO().getConvenioCobrancaVO().getCodigo());
//                        pinpad.setPinpadPedido(this.getPinPadPedidoVO().getCodigo());


                        //                        gerarPagamentoPinpad();


                        List<PinPadPedidoMovParcelaVO> listaPinpadMovParcela = pinPadPedidoMovParcelaDAO.consultarPorPinPadPedido(pinPadPedidoVO.getCodigo());
                        RecebimentoDTO recebimentoDTO = new RecebimentoDTO();
                        recebimentoDTO.setParcelas(new ArrayList<>());
                        for (PinPadPedidoMovParcelaVO pinPadPedidoMovParcelaVO : listaPinpadMovParcela) {
                            MovParcelaDTO movParcelaDTO = new MovParcelaDTO();
                            movParcelaDTO.setCodigo(pinPadPedidoMovParcelaVO.getMovparcela());
                            recebimentoDTO.getParcelas().add(movParcelaDTO);
                        }

                        List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);
                        if (UteisValidacao.emptyList(listaParcelas)) {
                            throw new ServiceException("validacao_parcelas", "Nenhuma parcela encontrada");
                        }

                        FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(pinPadPedidoVO.getFormaPagamentoVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(pinPadPedidoVO.getPessoaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                        UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(pinPadPedidoVO.getUsuarioVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

                        MovPagamentoVO movPagamentoVO = new MovPagamentoVO();
                        movPagamentoVO.setMovPagamentoEscolhida(true);
                        movPagamentoVO.setValor(pinPadPedidoVO.getValor());
                        movPagamentoVO.setValorTotal(pinPadPedidoVO.getValor());
                        movPagamentoVO.setUsuarioVO(pinPadPedidoVO.getUsuarioVO());
                        movPagamentoVO.setResponsavelPagamento(pinPadPedidoVO.getUsuarioVO());
                        movPagamentoVO.setEmpresa(pinPadPedidoVO.getEmpresaVO());
                        movPagamentoVO.setFormaPagamento(formaPagamentoVO);
                        movPagamentoVO.setPessoa(pessoaVO);
                        movPagamentoVO.setNomePagador(pessoaVO.getNome());
                        movPagamentoVO.setAutorizacaoCartao(pedidoRetornoDTO.getAutorizacao());
                        movPagamentoVO.setNsu(pedidoRetornoDTO.getNsu());
                        movPagamentoVO.setConvenio(pinPadPedidoVO.getConvenioCobrancaVO());
//                        movPagamentoVO.setObservacao(recebimentoDTO.getObservacao());
//                        movPagamentoVO.setRespostaRequisicaoPinpad(pinpad.getDadosPinPadGravar());

                        boolean credito = movPagamentoVO.getFormaPagamento().getTipoFormaPagamento().equals(TipoFormaPagto.CARTAOCREDITO.getSigla());
                        if (credito) {
                            if (pinPadPedidoVO.getDadosPedidoDTO() != null &&
                                    !UteisValidacao.emptyNumber(pinPadPedidoVO.getDadosPedidoDTO().getNrParcelas())) {
                                movPagamentoVO.setNrParcelaCartaoCredito(pinPadPedidoVO.getDadosPedidoDTO().getNrParcelas());
                            }
//                            movPagamentoVO.setNrParcelaCartaoCredito(pinpad.getNrParcelas());
                        }

                        try {
                            OperadoraCartaoVO operadoraCartaoVO = new OperadoraCartaoVO();
//                            if (pinpad.getOperadorasExternasAprovaFacilEnum() != null) {
//                                operadoraCartaoVO = operadoraCartaoDAO.consultarOuCriaSeNaoExistirPinpad(pinpad.getOperadorasExternasAprovaFacilEnum(),
//                                        pinPadPedidoVO.getPinpad(), credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//                            }

                            if (UteisValidacao.emptyNumber(operadoraCartaoVO.getCodigo())) {
                                if (UteisValidacao.emptyNumber(movPagamentoVO.getOperadoraCartaoVO().getCodigo())) {
                                    List<OperadoraCartaoVO> listaOperadora = operadoraCartaoDAO.consultarTodas(true, credito, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                                    if (!UteisValidacao.emptyList(listaOperadora)) {
                                        movPagamentoVO.setOperadoraCartaoVO(listaOperadora.get(0));
                                    }
                                }
                            } else {
                                movPagamentoVO.setOperadoraCartaoVO(operadoraCartaoVO);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        try {
                            if (!UteisValidacao.emptyString(pedidoRetornoDTO.getAdquirente())) {
                                movPagamentoVO.setAdquirenteVO(adquirenteDAO.consultarOuCriaSeNaoExistir(pedidoRetornoDTO.getAdquirente()));
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }

                        List<MovPagamentoVO> pagamentos = new ArrayList<>();
                        pagamentos.add(movPagamentoVO);

                        ReciboPagamentoVO reciboVO = movPagamentoDAO.incluirListaPagamento(
                                pagamentos, listaParcelas,
                                null,
                                obterContratoParcelas(listaParcelas),
                                false, null, false, null, null);

                        OrigemCobrancaEnum origemCobrancaEnum = obterOrigemCobranca(recebimentoDTO);
                        reciboVO.setOrigem(origemCobrancaEnum.getDescricao());
                        incluirLogRecibo(reciboVO, usuarioVO, origemCobrancaEnum, con);

                        pinPadPedidoVO.setReciboPagamentoVO(reciboVO);
                        if (!UteisValidacao.emptyList(reciboVO.getPagamentosDesteRecibo())) {
                            pinPadPedidoVO.setMovPagamentoVO(reciboVO.getPagamentosDesteRecibo().get(0));
                        }
                    }

                    try {
                        if (pinPadPedidoVO.getPinpad().equals(OpcoesPinpadEnum.GETCARD) ||
                                pinPadPedidoVO.getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
                            pinpadPedidoDAO.alterar(pinPadPedidoVO);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }

            return new PinpadPedidoDTO(pinPadPedidoVO);
        }
    }

    private void incluirLogRecibo(ReciboPagamentoVO reciboPagamentoVO, UsuarioVO usuarioVO,
                                  OrigemCobrancaEnum origemCobrancaEnum, Connection con) {
        Log logDAO;
        try {
            logDAO = new Log(con);
            LogVO logVO = new LogVO();
            reciboPagamentoVO.montarLogReciboPagamento(logVO);
            logVO.setOperacao("INCLUSÃO DE RECIBO");
            logVO.setOrigem(reciboPagamentoVO.getOrigem());
            logVO.setOrigem(origemCobrancaEnum != null ? origemCobrancaEnum.getDescricao() : "");
            logVO.setPessoa(reciboPagamentoVO.getPessoaPagador().getCodigo());
            logDAO.incluirSemCommit(logVO);
        } catch (Exception e) {
            e.printStackTrace();
            registrarLogErroObjetoVO("RECIBOPAGAMENTO", reciboPagamentoVO.getPessoaPagador().getCodigo(),
                    "ERRO AO GERAR LOG DE INCLUSÃO RECIBO",
                    usuarioVO, origemCobrancaEnum, con);
        } finally {
            logDAO = null;
        }
    }

    private void registrarLogErroObjetoVO(String nomeEntidade, int codPessoa, String msg, UsuarioVO usuarioVO,
                                          OrigemCobrancaEnum origemCobrancaEnum, Connection con) {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO log = new LogVO();
            log.setNomeCampo("Erro");
            log.setChavePrimaria("");
            log.setNomeEntidade(nomeEntidade);
            log.setOrigem(origemCobrancaEnum != null ? origemCobrancaEnum.getDescricao() : "");
            log.setPessoa(codPessoa);
            log.setValorCampoAnterior(msg);
            log.setValorCampoAlterado(msg);
            log.setOperacao("ERRO AO CRIAR LOG");
            log.setResponsavelAlteracao(usuarioVO.getNome());
            log.setUserOAMD(usuarioVO.getUserOamd());
            logDAO.incluirSemCommit(log);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            logDAO = null;
        }
    }

    public TransacaoDTO gerarTransacao(RecebimentoDTO recebimentoDTO) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Usuario usuarioDAO = new Usuario(con);
            Empresa empresaDAO = new Empresa(con);
            OperadoraCartao operadoraCartaoDAO = new OperadoraCartao(con);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
            FormaPagamento formaPagamentoDAO = new FormaPagamento(con);

            validarDadosRecebimento(recebimentoDTO, TipoCobrancaEnum.ONLINE);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(recebimentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PessoaVO pessoaVO = obterPessoaVO(recebimentoDTO, true, con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(recebimentoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            FormaPagamentoVO formaPagamentoVO = formaPagamentoDAO.consultarPorChavePrimaria(recebimentoDTO.getCartao().getFormaPagamento(), Uteis.NIVELMONTARDADOS_TODOS);
            validarFormaPagamento(formaPagamentoVO, TipoFormaPagto.CARTAOCREDITO);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(recebimentoDTO.getCartao().getConvenioCobranca(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            validarConvenio(convenioCobrancaVO, TipoCobrancaEnum.ONLINE);

            List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);

            OperadoraCartaoVO operadoraCartaoVO = null;
            //caso não seja informado o sistema irá descobrir
            if (!UteisValidacao.emptyNumber(recebimentoDTO.getCartao().getOperadoraCartao())) {
                operadoraCartaoVO = operadoraCartaoDAO.consultarPorChavePrimaria(recebimentoDTO.getCartao().getOperadoraCartao(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            CartaoCreditoTO cartaoCreditoTO = new CartaoCreditoTO(recebimentoDTO.getCartao());
            cartaoCreditoTO.setIdPessoaCartao(pessoaVO.getCodigo());
            cartaoCreditoTO.setPessoaVO(pessoaVO);
            cartaoCreditoTO.setConvenioCobrancaVO(convenioCobrancaVO);
            cartaoCreditoTO.setTipoTransacaoEnum(convenioCobrancaVO.getTipo().getTipoTransacao());
            cartaoCreditoTO.setFormaPagamentoVO(formaPagamentoVO);
            cartaoCreditoTO.setOperadoraCartaoVO(operadoraCartaoVO);
            cartaoCreditoTO.setEmpresa(empresaVO.getCodigo());
            cartaoCreditoTO.setUsuarioVO(usuarioVO);
            cartaoCreditoTO.setUsuarioResponsavel(usuarioVO);
            cartaoCreditoTO.setIpClientePacto(recebimentoDTO.getIpCliente());
            cartaoCreditoTO.setListaParcelas(listaParcelas);
            cartaoCreditoTO.setParcelas(recebimentoDTO.getCartao().getNrParcelas());
            cartaoCreditoTO.setOrigemCobranca(obterOrigemCobranca(recebimentoDTO));

            //tipo de parcelamento
            TipoParcelamentoEnum tipoParcelamentoEnum = null;
            if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.DCC_STONE_ONLINE)) {
                if (recebimentoDTO.getCartao().getTipoParcelamento() == null) {
                    throw new ServiceException("validacao_tipoParcelamento", "O campo TIPO PARCELAMENTO deve ser informado.");
                }
                tipoParcelamentoEnum = TipoParcelamentoEnum.obterPorId(recebimentoDTO.getCartao().getTipoParcelamento());
            }
            if (tipoParcelamentoEnum == null) {
                tipoParcelamentoEnum = TipoParcelamentoEnum.A_VISTA;
            }
            cartaoCreditoTO.setTipoParcelamentoEnum(tipoParcelamentoEnum);
            cartaoCreditoTO.setTipoParcelamentoStone(tipoParcelamentoEnum.getStone());

            PagamentoService pagamentoService = new PagamentoService(con, convenioCobrancaVO);
            TransacaoVO transacaoVO = pagamentoService.processarCobranca(cartaoCreditoTO);

            try {
                SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CAIXA_ABERTO_V2_RECEBER_PARCELA_TRANSACAO, usuarioVO, empresaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            return new TransacaoDTO(transacaoVO);
        }
    }

    public InfoGerarBoletoDTO boletoInformacoes(Integer empresaId) throws Exception {
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {

            Integer codEmpresa = this.requestService.getUsuarioAtual().getIdEmpresa();
            if (UteisValidacao.emptyNumber(codEmpresa)) {
                codEmpresa = empresaId;
            }

            if (UteisValidacao.emptyNumber(codEmpresa)) {
                return new InfoGerarBoletoDTO();
            }

            Empresa empresaDAO = new Empresa(con);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            InfoGerarBoletoDTO info = new InfoGerarBoletoDTO();
            info.setQtdDiasVencimentoBoleto(empresaVO.getQtdDiasVencimentoBoleto());
            return info;
        }
    }

    public List<BoletoGerarDTO> simularBoletos(RecebimentoDTO recebimentoDTO) throws Exception {
        try (Connection con = conexaoZWService.conexaoZw(obterChave())) {
            return simularBoletos(recebimentoDTO, con);
        }
    }

    private List<BoletoGerarDTO> simularBoletos(RecebimentoDTO recebimentoDTO, Connection con) throws Exception {
        Empresa empresaDAO = new Empresa(con);
        ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);
        MovParcela movParcelaDAO = new MovParcela(con);

        validarDadosRecebimento(recebimentoDTO, null);

        EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(recebimentoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
        ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(recebimentoDTO.getBoleto().getConvenioCobranca(), Uteis.NIVELMONTARDADOS_TODOS);
        List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);
        Ordenacao.ordenarLista(listaParcelas, "dataVencimento");

        validarDadosRecebimento(recebimentoDTO, convenioCobrancaVO.getTipo().getTipoCobranca());
        validarParcelasEmpresa(empresaVO, listaParcelas);

        List<BoletoGerarDTO> lista = new ArrayList<>();

        if (recebimentoDTO.getBoleto().getBoletoUnico()) {

            Date dataVencimento = new Date(recebimentoDTO.getBoleto().getDataVencimento());

            BoletoGerarDTO dto = new BoletoGerarDTO();
            dto.setDataVencimento(dataVencimento.getTime());
            dto.setParcelas(new ArrayList<>());
            dto.setValor(0.0);
            dto.setValorMultaJuros(0.0);

            if (recebimentoDTO.isCobrarMultaJuros()) {
                Double valorMultaJuros = movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, dataVencimento,
                        false, 1.0, null);
                dto.setValorMultaJuros(valorMultaJuros);
            }

            for (MovParcelaVO movParcelaVO : listaParcelas) {
                dto.getParcelas().add(new MovParcelaDTO(movParcelaVO));
                dto.setValor(dto.getValor() + movParcelaVO.getValorParcela());
            }

            lista.add(dto);

        } else {

            Map<Date, List<MovParcelaVO>> mapParcelas = new HashMap<>();
            for (MovParcelaVO movParcelaVO : listaParcelas) {
                Date dataVencimentoSemHora = null;
                if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
                    dataVencimentoSemHora = Uteis.somarDias(Calendario.hoje(), empresaVO.getQtdDiasVencimentoBoleto());
                } else {
                    dataVencimentoSemHora = movParcelaVO.getDataVencimento();
                }
                dataVencimentoSemHora = Calendario.getDataComHoraZerada(dataVencimentoSemHora);

                List<MovParcelaVO> parcelasBoleto = mapParcelas.get(dataVencimentoSemHora);
                if (parcelasBoleto == null) {
                    parcelasBoleto = new ArrayList<>();
                }
                parcelasBoleto.add(movParcelaVO);
                mapParcelas.put(Calendario.getDataComHoraZerada(dataVencimentoSemHora), parcelasBoleto);
            }

            for (Date dataVencimento : mapParcelas.keySet()) {

                BoletoGerarDTO dto = new BoletoGerarDTO();
                dto.setDataVencimento(dataVencimento.getTime());
                dto.setParcelas(new ArrayList<>());
                dto.setValor(0.0);
                dto.setValorMultaJuros(0.0);

                List<MovParcelaVO> parcelasBoleto = mapParcelas.get(dataVencimento);

                if (recebimentoDTO.isCobrarMultaJuros()) {
                    Double valorMultaJuros = movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, parcelasBoleto, new Date(recebimentoDTO.getBoleto().getDataVencimento()),
                            false, 1.0, null);
                    dto.setValorMultaJuros(valorMultaJuros);
                }

                for (MovParcelaVO movParcelaVO : parcelasBoleto) {
                    dto.getParcelas().add(new MovParcelaDTO(movParcelaVO));
                    dto.setValor(dto.getValor() + movParcelaVO.getValorParcela());
                }


                lista.add(dto);
            }
        }
        Ordenacao.ordenarLista(lista, "dataVencimento");
        AtomicInteger i = new AtomicInteger();
        lista.forEach(bol -> {
            bol.setDescricao("BOLETO " + i.incrementAndGet());
        });
        return lista;
    }

    private List<BoletoBancarioTO> gerarBoletoBancarioTO(UsuarioVO usuarioVO, PessoaVO pessoaVO,
                                                         EmpresaVO empresaVO, List<MovParcelaVO> listaParcelas,
                                                         List<BoletoGerarDTO> listaBoletosGerar) {

        List<BoletoBancarioTO> lista = new ArrayList<>();

        for (BoletoGerarDTO boletoGerarDTO : listaBoletosGerar) {
            BoletoBancarioTO boletoBancarioTO = new BoletoBancarioTO();
            boletoBancarioTO.setValorBoleto(boletoGerarDTO.getValor() + boletoGerarDTO.getValorMultaJuros());
            boletoBancarioTO.setValorMultaJuros(boletoGerarDTO.getValorMultaJuros());
            boletoBancarioTO.setEmpresa(empresaVO);
            boletoBancarioTO.setSacado(pessoaVO);
            boletoBancarioTO.setDataVencimento(new Date(boletoGerarDTO.getDataVencimento()));
            boletoBancarioTO.setParcelas(new ArrayList<>());
            for (MovParcelaDTO movParcelaDTO : boletoGerarDTO.getParcelas()) {
                for (MovParcelaVO movParcelaVO : listaParcelas) {
                    if (movParcelaDTO.getCodigo().equals(movParcelaVO.getCodigo())) {
                        boletoBancarioTO.getParcelas().add(movParcelaVO);
                        break;
                    }
                }
            }
            lista.add(boletoBancarioTO);
        }
        return lista;
    }

    public BoletoGeradoDTO gerarBoleto(RecebimentoDTO recebimentoDTO) throws Exception {
        String key = obterChave();
        try (Connection con = conexaoZWService.conexaoZw(key)) {
            Usuario usuarioDAO = new Usuario(con);
            Pessoa pessoaDAO = new Pessoa(con);
            Empresa empresaDAO = new Empresa(con);
            MovParcela movParcelaDAO = new MovParcela(con);
            ConvenioCobranca convenioCobrancaDAO = new ConvenioCobranca(con);

            //o tipo aqui não importa a validação é igual para online e boleto de remessa
            validarDadosRecebimento(recebimentoDTO, TipoCobrancaEnum.BOLETO);

            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(recebimentoDTO.getUsuario(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            PessoaVO pessoaVO = pessoaDAO.consultarPorChavePrimaria(recebimentoDTO.getPessoa(), Uteis.NIVELMONTARDADOS_TODOS);
            EmpresaVO empresaVO = empresaDAO.consultarPorChavePrimaria(recebimentoDTO.getEmpresa(), Uteis.NIVELMONTARDADOS_TODOS);
            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(recebimentoDTO.getBoleto().getConvenioCobranca(), Uteis.NIVELMONTARDADOS_TODOS);
            List<MovParcelaVO> listaParcelas = obterListaMovParcela(recebimentoDTO, con);

            //calcular multa e juros
            if (recebimentoDTO.getBoleto().getBoletoUnico() != null &&
                    recebimentoDTO.getBoleto().getBoletoUnico() &&
                    recebimentoDTO.isCobrarMultaJuros()) {
                Date dataVencimento = new Date(recebimentoDTO.getBoleto().getDataVencimento());
                movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, listaParcelas, dataVencimento, false, 1.0, null);
            }

            List<BoletoGerarDTO> listaBoletosGerar = simularBoletos(recebimentoDTO, con);
            List<BoletoBancarioTO> boletosTO = gerarBoletoBancarioTO(usuarioVO, pessoaVO, empresaVO, listaParcelas, listaBoletosGerar);

            BoletoGeradoDTO boletoGeradoDTO = new BoletoGeradoDTO();
            boletoGeradoDTO.setBoletos(new ArrayList<>());
            boletoGeradoDTO.setPessoa(new PessoaDTO(pessoaVO));

            if (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO_ONLINE)) {

                validarDadosRecebimento(recebimentoDTO, convenioCobrancaVO.getTipo().getTipoCobranca());

                List<BoletoVO> listaBoletos = gerarBoletoOnline(recebimentoDTO, recebimentoDTO.getBoleto(),
                        pessoaVO, convenioCobrancaVO, empresaVO, usuarioVO,
                        listaParcelas, boletosTO, obterOrigemCobranca(recebimentoDTO), con);

                listaBoletos.forEach(boletoVO -> {
                    boletoGeradoDTO.getBoletos().add(new BoletoDTO(boletoVO));
                });

                try {
                    if (listaBoletos.size() == 1) {
                        boletoGeradoDTO.setUrlBoleto(listaBoletos.get(0).getLinkBoleto());
                    } else if (listaBoletos.size() > 1) {
                        if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_PJBANK)) {
                            LinkedHashSet<String> pedidos = BoletoVO.obterListaImpressaoBoleto(listaBoletos);
                            BoletosManager boletosManager = new BoletosManager(convenioCobrancaVO.getCredencialPJBank(), convenioCobrancaVO.getChavePJBank(), convenioCobrancaVO);
                            boletoGeradoDTO.setUrlBoleto(boletosManager.getByIds(pedidos));
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                Ordenacao.ordenarLista(boletoGeradoDTO.getBoletos(), "dataVencimento");
                gravarBoletoGerado(boletoGeradoDTO, con);

                try {
                    SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CAIXA_ABERTO_V2_RECEBER_PARCELA_BOLETO_ONLINE, usuarioVO, empresaVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                return boletoGeradoDTO;

            } else if (convenioCobrancaVO.getTipo().getTipoCobranca().equals(TipoCobrancaEnum.BOLETO)) {

                validarDadosRecebimento(recebimentoDTO, convenioCobrancaVO.getTipo().getTipoCobranca());

                List<RemessaItemVO> listaBoletos = gerarBoletoRemessa(recebimentoDTO, recebimentoDTO.getBoleto(), pessoaVO,
                        convenioCobrancaVO, empresaVO, usuarioVO, listaParcelas, boletosTO,
                        obterOrigemCobranca(recebimentoDTO), con);

                String chave = obterChave();
                listaBoletos.forEach(boletoVO -> {
                    boletoGeradoDTO.getBoletos().add(new BoletoDTO(boletoVO, chave));
                });
                if (listaBoletos.size() > 0) {
                    boletoGeradoDTO.setUrlBoleto(!boletoGeradoDTO.getBoletos().isEmpty() ? boletoGeradoDTO.getBoletos().get(0).getUrlBoleto() : null);
                }

                Ordenacao.ordenarLista(boletoGeradoDTO.getBoletos(), "dataVencimento");
                gravarBoletoGerado(boletoGeradoDTO, con);

                try {
                    SuperControle.notificarRecursoEmpresa(key, RecursoSistema.CAIXA_ABERTO_V2_RECEBER_PARCELA_BOLETO_REMESSA, usuarioVO, empresaVO);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                return boletoGeradoDTO;

            } else {
                throw new ServiceException("validacao_tipo_cobranca_boleto", "Tipo cobrança não identificado");
            }
        }
    }

    private void gravarBoletoGerado(BoletoGeradoDTO boletoGeradoDTO, Connection con) throws Exception {
        TokenBoleto tokenBoletoDAO = new TokenBoleto(con);
        TokenBoletoVO tokenBoletoVO = tokenBoletoDAO.gerarToken(new JSONObject(boletoGeradoDTO).toString());
        boletoGeradoDTO.setToken(tokenBoletoVO.getToken());

        String chave = obterChave();
        String urlImpressaoBoleto = (Uteis.getUrlAPI() + "/prest/boleto/boleto-gerado/" + chave + "/" + tokenBoletoVO.getToken());
        boletoGeradoDTO.setUrl(urlImpressaoBoleto);
    }

    private List<RemessaItemVO> gerarBoletoRemessa(RecebimentoDTO recebimentoDTO,
                                                   BoletoPagamentoDTO boletoPagamentoDTO,
                                                   PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO,
                                                   EmpresaVO empresaVO, UsuarioVO usuarioVO, List<MovParcelaVO> parcelasBoleto,
                                                   List<BoletoBancarioTO> boletosTO,
                                                   OrigemCobrancaEnum origemCobrancaEnum, Connection con) throws Exception {

        Empresa empresaDAO = new Empresa(con);
        MovParcela movParcelaDAO = new MovParcela(con);

        empresaVO = empresaDAO.consultarPorChavePrimaria(empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);

        BoletoService bService = new BoletoService(con);
        RemessaVO remessa;
        Boolean agruparPorVencimento = true;
        Boolean calcularMultaJuros = recebimentoDTO.isCobrarMultaJuros();
        Date dataVencimento = new Date(boletoPagamentoDTO.getDataVencimento());

        List<RemessaItemVO> listaItem = new ArrayList<>();
        for (BoletoBancarioTO boleto : boletosTO) {
            remessa = bService.obterRemessaBoleto(convenioCobrancaVO, empresaVO, usuarioVO);

            Double valorMultaJuros;
            if (!agruparPorVencimento) {
                boleto.setDataVencimento(dataVencimento);
                if (calcularMultaJuros) {
                    valorMultaJuros = movParcelaDAO.montarMultaJurosParcelaVencida(empresaVO, boleto.getParcelas(), dataVencimento, false, 1.0, null);
                    boleto.setValorMultaJuros(valorMultaJuros);
                }
            } else {
                alterarVencimentoParcelasVencendoFimDeSemanaEFeriado(boleto, empresaVO, con);
            }

            boleto.calcularValorBoleto();

            RemessaItemVO item = bService.adicionarBoletoRemessa(boleto, remessa);
            listaItem.add(item);
        }
        return listaItem;
    }

    private void alterarVencimentoParcelasVencendoFimDeSemanaEFeriado(BoletoBancarioTO boleto, EmpresaVO empresaVO, Connection con) throws Exception {
        List<MovParcelaVO> listaParcelasAntesAlteracao = new ArrayList<>();
        boolean parcelaFoiAlterada = false;
        MovParcela movParcelaDAO = new MovParcela(con);

        for (MovParcelaVO movParcelaVO : boleto.getParcelas()) {
            listaParcelasAntesAlteracao.add((MovParcelaVO) movParcelaVO.getClone(true));
            Date dataValidar = movParcelaVO.getDataVencimento();

            if (!isParcelaVencida(movParcelaVO)) {
                while (isFeriado(dataValidar, empresaVO, con) || !Calendario.isDiaUtil(dataValidar)) {
                    dataValidar = Calendario.somarDias(dataValidar, 1);
                    parcelaFoiAlterada = true;
                }
                boleto.setDataVencimento(dataValidar);
                movParcelaVO.setDataVencimento(dataValidar);
            }
        }

        if (parcelaFoiAlterada) {
            movParcelaDAO.alterarVencimentoListaParcelas(boleto.getParcelas(), listaParcelasAntesAlteracao, false, "GeracaoBoletoPjbank", "CaixaAbertoOuLinkPagamento", true, true);
        }
    }

    private boolean isFeriado(Date data, EmpresaVO empresaVO, Connection con) throws Exception {
        Feriado feriadoDAO = new Feriado(con);
        List<Date> dataLimiteFeriado = feriadoDAO.consultarDiasFeriados(data, data, empresaVO);
        if (dataLimiteFeriado.size() > 0) {
            return true;
        } else {
            return false;
        }
    }

    private boolean isParcelaVencida(MovParcelaVO movParcelaVO) throws Exception {
        if (Calendario.menor(movParcelaVO.getDataVencimento(), Calendario.hoje())) {
            return true;
        }
        return false;
    }

    private List<BoletoVO> gerarBoletoOnline(RecebimentoDTO recebimentoDTO,
                                             BoletoPagamentoDTO boletoPagamentoDTO,
                                             PessoaVO pessoaVO, ConvenioCobrancaVO convenioCobrancaVO,
                                             EmpresaVO empresaVO, UsuarioVO usuarioVO, List<MovParcelaVO> parcelasBoleto,
                                             List<BoletoBancarioTO> boletosTO,
                                             OrigemCobrancaEnum origemCobrancaEnum, Connection con) throws Exception {
        Boleto boletoDAO = new Boleto(con);

        Ordenacao.ordenarLista(parcelasBoleto, "dataVencimento");

        Boolean boletoUnicoParcelasMesmoVencimento = boletoPagamentoDTO.getBoletoUnico();
        Boolean calcularMultaJuros = recebimentoDTO.isCobrarMultaJuros();

        List<BoletoVO> listaBoletosOnlineGerados = new ArrayList<>();
        if (!boletoUnicoParcelasMesmoVencimento) {
            //emitir boleto único agrupando parcelas com o mesmo vencimento
            if (boletoUnicoParcelasMesmoVencimento) {
                for (BoletoBancarioTO boleto : boletosTO) {
                    if (!UteisValidacao.emptyList(boleto.getParcelas())) {
                        PessoaVO pessoaSacado;
                        if (boleto.getSacado().getNome().equals(boleto.getParcelas().get(0).getPessoa().getNome())) {
                            pessoaSacado = boleto.getSacado();
                        } else {
                            pessoaSacado = boleto.getParcelas().get(0).getPessoa();
                        }
                        //gerar um boleto único com todas as parcelas
                        BoletoVO boletoVO = boletoDAO.gerarBoleto(pessoaSacado, convenioCobrancaVO, boleto.getParcelas(), boleto.getDataVencimento(),
                                usuarioVO, origemCobrancaEnum, calcularMultaJuros, true);
                        listaBoletosOnlineGerados.add(boletoVO);
                        if (boletoVO != null && boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                            throw new ServiceException("erro_geracao_boleto", boletoVO.getErroMensagem());
                        }
                    }
                }
            } else {
                //emitir de acordo com o vencimento de cada parcela
                listaBoletosOnlineGerados.addAll(boletoDAO.gerarBoletoPorParcela(pessoaVO, convenioCobrancaVO, parcelasBoleto,
                        usuarioVO, origemCobrancaEnum, calcularMultaJuros, true));

                //Ajusta as datas para serem exibidas no front, caso elas sejam alteradas pelo processo de geração do boleto Pjbank
                for (BoletoBancarioTO boletoBancarioTO : boletosTO) {
                    for (MovParcelaVO movParcelaVO : parcelasBoleto) {
                        if (boletoBancarioTO.getParcelas().get(0).getCodigo() == movParcelaVO.getCodigo() && Calendario.maior(movParcelaVO.getDataVencimento(), boletoBancarioTO.getDataVencimento())) {
                            boletoBancarioTO.setDataVencimento(movParcelaVO.getDataVencimento());
                        }
                    }
                }

                if (listaBoletosOnlineGerados.size() == 1 &&
                        listaBoletosOnlineGerados.get(0).getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                    throw new ServiceException("erro_geracao_boleto", listaBoletosOnlineGerados.get(0).getErroMensagem());
                }

                //Validar se todos os boletos foram gerados com sucesso
                if (convenioCobrancaVO.getTipo().equals(TipoConvenioCobrancaEnum.BOLETO_BANCO_BRASIL_ONLINE)) {
                    String msgErro = "";
                    for (BoletoVO boletoVO : listaBoletosOnlineGerados) {
                        if (boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                            msgErro += boletoVO.getErroMensagem() + "\n";
                        }
                    }
                    if (!UteisValidacao.emptyString(msgErro)) {
                        throw new ServiceException("erro_geracao_boleto", msgErro);
                    }
                }
            }
        } else {
            Date dataVencimento = new Date(boletoPagamentoDTO.getDataVencimento());
            if (Calendario.menor(dataVencimento, Calendario.hoje())) {
                throw new ServiceException("validacao_dataVencimento", "A data de vencimento do boleto não pode ser menor que Hoje!");
            }
            //gerar um boleto com todas as parcelas
            BoletoVO boletoVO = boletoDAO.gerarBoleto(pessoaVO, convenioCobrancaVO, parcelasBoleto, dataVencimento,
                    usuarioVO, origemCobrancaEnum, calcularMultaJuros, true);
            listaBoletosOnlineGerados.add(boletoVO);
            if (boletoVO != null && boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO)) {
                throw new ServiceException("erro_geracao_boleto", boletoVO.getErroMensagem());
            }
        }
        return listaBoletosOnlineGerados;
    }

    private MovimentoContaCorrenteClienteVO montarContaCorrentePagarDebito(PessoaVO pessoaVO, Double valor, Connection con) throws Exception {
        MovimentoContaCorrenteCliente movimentoContaCorrenteClienteDAO = new MovimentoContaCorrenteCliente(con);
        MovimentoContaCorrenteClienteVO atual = movimentoContaCorrenteClienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (atual == null) {
            atual = new MovimentoContaCorrenteClienteVO();
        }

        MovimentoContaCorrenteClienteVO novo = new MovimentoContaCorrenteClienteVO();
        novo.setCodigo(0);
        novo.setNovoObj(true);
        novo.setDescricao("Pagar Parcelas");
        novo.setPessoa(pessoaVO);
        novo.setDataRegistro(Calendario.hoje());

        novo.setTipoMovimentacao("DE");
//        if (tipoMovimentacao.equals("DE")) {
//            return "Débito";
//        } else if (tipoMovimentacao.equals("CR")) {
//            return "Crédito";
//        }

        /*Como Atributo Valor nao e gravado no banco ele fica com o valor do Saldo anterior caso a pesso desista de  utilizar a forma de pagamento CC*/
        novo.setSaldoAnterior(UteisValidacao.emptyNumber(atual.getSaldoAtual()) ? 0.0 : atual.getSaldoAtual());
        novo.setValor(valor);
        //getMovContaCorrenteCliente().setValor(getMovContaCorrenteCliente().getSaldoAtual());
        //descomentar
        //getMovContaCorrenteCliente().setValor(movPagamento.getValor());

        Double saldo = (atual.getSaldoAtual() - valor);
        novo.setSaldoAtual(saldo);
        return novo;
    }

    private void validarDataPagamento(Date dataPagamento, List<MovParcelaVO> parcelas) throws ServiceException {
        if (Calendario.maior(dataPagamento, Calendario.hoje())) {
            throw new ServiceException("validacao_dataPagamento", "Data de pagamento não pode ser maior que a data atual");
        }
        Iterator i = parcelas.iterator();
        while (i.hasNext()) {
            MovParcelaVO mp = (MovParcelaVO) i.next();
            // se a parcela possui data de alteração manual
            if (mp.getDataRegistro().compareTo(dataPagamento) > 0) {
                throw new ServiceException("validacao_dataPagamento", "Data de pagamento não pode ser anterior a " + mp.getDataRegistro_Apresentar() + ", " +
                        "porque pelo menos uma parcela foi lançada nessa data e você não pode pagar com data anterior ao lançamento.");
            }
        }
    }

    private PessoaVO obterPessoaVO(RecebimentoDTO recebimentoDTO, boolean consultarIncluirPessoaNaoExistir, Connection con) throws Exception {
        Pessoa pessoaDAO = new Pessoa(con);
        if (!UteisValidacao.emptyNumber(recebimentoDTO.getPessoa())) {
            return pessoaDAO.consultarPorChavePrimaria(recebimentoDTO.getPessoa(), Uteis.NIVELMONTARDADOS_TODOS);
        } else if (!UteisValidacao.emptyString(recebimentoDTO.getTipoComprador()) &&
                recebimentoDTO.getTipoComprador().equalsIgnoreCase("CN")) {
            //venda consumidor
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(recebimentoDTO.getNomeComprador());

            if (consultarIncluirPessoaNaoExistir) {
                pessoaVO = pessoaDAO.consultarPessoaConsumidor(pessoaVO.getNome(), Uteis.NIVELMONTARDADOS_TODOS);
                if (UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                    pessoaVO.setNome(recebimentoDTO.getNomeComprador());
                    pessoaVO.setCategoriaPessoa(TipoPessoa.FISICA);
                    pessoaVO.setTipoPessoa("Consumidor");
                    pessoaVO.setDataCadastro(Calendario.hoje());
                    pessoaDAO.incluirPessoaSimplificado(pessoaVO);
                    gerarLogPessoaConsumidor(pessoaVO, recebimentoDTO, con);
                }
            }

            return pessoaVO;
        } else {
            throw new ServiceException("validacao_pessoa", "O campo PESSOA deve ser informado.");
        }
    }

    private void gerarLogPessoaConsumidor(PessoaVO pessoaVO, RecebimentoDTO recebimentoDTO, Connection con) throws Exception {
        Log logDAO;
        try {
            logDAO = new Log(con);

            LogVO obj = new LogVO();
            obj.setChavePrimaria(pessoaVO.getCodigo().toString());
            obj.setNomeEntidade("PESSOA");
            obj.setNomeEntidadeDescricao("PESSOA");
            obj.setOperacao("CADASTRO CONSUMIDOR RECEBIMENTO V2");
            obj.setPessoa(pessoaVO.getCodigo());
            obj.setDataAlteracao(Calendario.hoje());
            obj.setResponsavelAlteracao("AUTOMATICO");
            obj.setNomeCampo("TODOS");
            obj.setValorCampoAnterior("");
            obj.setValorCampoAlterado("");

            OrigemCobrancaEnum origemCobrancaEnum = obterOrigemCobranca(recebimentoDTO);
            obj.setOrigem(origemCobrancaEnum != null ? origemCobrancaEnum.getDescricao() : "");
            logDAO.incluirSemCommit(obj);
        } finally {
            logDAO = null;
        }
    }
}
