package springboot.controllers;

import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.ResponseEntityFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springboot.dto.contrato.ComissaoGeralConfiguracaoDTO;
import springboot.dto.contrato.ComissaoGeralConfiguracaoDetalheDTO;
import springboot.dto.contrato.ComissaoGeralConfiguracaoFiltroDTO;
import springboot.dto.contrato.ContratoDuracaoDTO;
import springboot.services.intf.ComissaoGeralConfiguracaoService;

import java.util.List;

@RestController
@RequestMapping("/taxa-comissao")
public class ComissaoGeralConfiguracaoController {

    private final ComissaoGeralConfiguracaoService comissaoGeralConfiguracaoService;

    public ComissaoGeralConfiguracaoController(ComissaoGeralConfiguracaoService comissaoGeralConfiguracaoService) {
        this.comissaoGeralConfiguracaoService = comissaoGeralConfiguracaoService;
    }

    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> consultarComissoesGerais(
            @RequestParam(required = false) Integer codigo,
            @RequestParam(required = false) Integer duracao,
            @RequestParam(required = false) String situacao,
            @RequestParam(required = false) Double valorFixoEspontaneoMin,
            @RequestParam(required = false) Double valorFixoEspontaneoMax,
            @RequestParam(required = false) Double porcentagemEspontaneoMin,
            @RequestParam(required = false) Double porcentagemEspontaneoMax,
            @RequestParam(required = false) Double valorFixoAgendadoMin,
            @RequestParam(required = false) Double valorFixoAgendadoMax,
            @RequestParam(required = false) Double porcentagemAgendadoMin,
            @RequestParam(required = false) Double porcentagemAgendadoMax,
            @RequestParam(required = false) Integer empresaId,
            PaginadorDTO paginadorDTO) {

        try {
            ComissaoGeralConfiguracaoFiltroDTO filtro = new ComissaoGeralConfiguracaoFiltroDTO();
            filtro.setCodigo(codigo);
            filtro.setDuracao(duracao);
            filtro.setSituacao(situacao);
            filtro.setValorFixoEspontaneoMin(valorFixoEspontaneoMin);
            filtro.setValorFixoEspontaneoMax(valorFixoEspontaneoMax);
            filtro.setPorcentagemEspontaneoMin(porcentagemEspontaneoMin);
            filtro.setPorcentagemEspontaneoMax(porcentagemEspontaneoMax);
            filtro.setValorFixoAgendadoMin(valorFixoAgendadoMin);
            filtro.setValorFixoAgendadoMax(valorFixoAgendadoMax);
            filtro.setPorcentagemAgendadoMin(porcentagemAgendadoMin);
            filtro.setPorcentagemAgendadoMax(porcentagemAgendadoMax);
            filtro.setEmpresaId(empresaId);

            return ResponseEntityFactory.ok(
                comissaoGeralConfiguracaoService.consultarComissoesGerais(filtro, paginadorDTO),
                paginadorDTO
            );
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno("erro_consultar_comissoes_gerais", e.getMessage());
        }
    }

    @GetMapping("/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> obterPorCodigo(@PathVariable Integer codigo) {
        try {
            ComissaoGeralConfiguracaoDetalheDTO comissao = comissaoGeralConfiguracaoService.obterPorCodigo(codigo);
            return ResponseEntityFactory.ok(comissao);
        } catch (ServiceException e) {
            if (e.getMessage().contains("nao encontrada") || e.getMessage().contains("não encontrada")) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping
    public ResponseEntity<ComissaoGeralConfiguracaoDetalheDTO> criarComissaoGeral(@RequestBody ComissaoGeralConfiguracaoDetalheDTO comissaoDTO) {
        try {
            ComissaoGeralConfiguracaoDetalheDTO novaComissao = comissaoGeralConfiguracaoService.criarComissaoGeral(comissaoDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(novaComissao);
        } catch (ServiceException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PutMapping("/{codigo}")
    public ResponseEntity<ComissaoGeralConfiguracaoDetalheDTO> atualizarComissaoGeral(
            @PathVariable Integer codigo,
            @RequestBody ComissaoGeralConfiguracaoDetalheDTO comissaoDTO) {
        try {
            ComissaoGeralConfiguracaoDetalheDTO comissaoAtualizada = comissaoGeralConfiguracaoService.atualizarComissaoGeral(codigo, comissaoDTO);
            return ResponseEntity.ok(comissaoAtualizada);
        } catch (ServiceException e) {
            if (e.getMessage().contains("não encontrada") || e.getMessage().contains("nao encontrada")) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @DeleteMapping("/{codigo}")
    public ResponseEntity<Void> removerComissaoGeral(@PathVariable Integer codigo) {
        try {
            comissaoGeralConfiguracaoService.removerComissaoGeral(codigo);
            return ResponseEntity.noContent().build();
        } catch (ServiceException e) {
            if (e.getMessage().contains("não encontrada") || e.getMessage().contains("nao encontrada")) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/duracoes")
    public ResponseEntity<List<ContratoDuracaoDTO>> listarDuracoesContrato() {
        try {
            List<ContratoDuracaoDTO> duracoes = comissaoGeralConfiguracaoService.listarDuracoesContrato();
            return ResponseEntity.ok(duracoes);
        } catch (ServiceException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/empresa/{empresaId}")
    public ResponseEntity<Void> removerTodasComissoesPorEmpresa(@PathVariable Integer empresaId) {
        try {
            comissaoGeralConfiguracaoService.removerTodasComissoesPorEmpresa(empresaId);
            return ResponseEntity.noContent().build();
        } catch (ServiceException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/empresa/{empresaId}")
    public ResponseEntity<List<ComissaoGeralConfiguracaoDTO>> consultarPorEmpresa(@PathVariable Integer empresaId) {
        try {
            List<ComissaoGeralConfiguracaoDTO> comissoes = comissaoGeralConfiguracaoService.consultarPorEmpresa(empresaId);
            return ResponseEntity.ok(comissoes);
        } catch (ServiceException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
