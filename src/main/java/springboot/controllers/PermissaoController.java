package springboot.controllers;

import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.ResponseEntityFactory;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springboot.services.intf.PermissaoService;

@RestController
@RequestMapping("/permissao")
public class PermissaoController {

    private PermissaoService service;

    public PermissaoController(PermissaoService service) {
        this.service = service;
    }

    @PostMapping("/validar-permissao-usuario")
    public ResponseEntity<EnvelopeRespostaDTO> validarPermissaoUsuario(@RequestBody String body) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            String bodyDecodificado = Uteis.desencriptarZWUI(new JSONObject(body).getString("data"));
            return ResponseEntityFactory.ok(service.validarPermissaoUsuario(bodyDecodificado));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro-validar-permissao-usuario", e.getMessage());
        }
    }

    @PostMapping("/validar-permissao-username")
    public ResponseEntity<EnvelopeRespostaDTO> validarPermissaoUsername(@RequestBody String body) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            String bodyDecodificado = Uteis.desencriptarZWUI(new JSONObject(body).getString("data"));
            return ResponseEntityFactory.ok(service.validarPermissaoUsername(bodyDecodificado));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("erro_validar-permissao-username", e.getMessage());
        }
    }
}