package springboot.controllers;

import br.com.pactosolucoes.comuns.json.JSONMapper;
import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springboot.dto.pedidospinpad.PedidosPinpadFiltroDTO;
import springboot.services.intf.PedidosPinpadService;

@RestController
@RequestMapping("/pedidos-pinpad")
public class PedidosPinpadController {

    private final PedidosPinpadService pedidosPinpadService;

    public PedidosPinpadController(PedidosPinpadService pedidosPinpadService) {
        this.pedidosPinpadService = pedidosPinpadService;
    }

    @GetMapping("/consultar")
    public ResponseEntity<EnvelopeRespostaDTO> consultar(@RequestHeader(name = "empresaId", required = false) Integer empresaId,
                                                         @RequestParam(value = "filters", required = false) String filtros,
                                                         PaginadorDTO paginadorDTO) {

        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);

            PedidosPinpadFiltroDTO filtroDTO = JSONMapper.getObject(filtros != null ? new JSONObject(filtros) : new JSONObject(), PedidosPinpadFiltroDTO.class);
            return ResponseEntityFactory.ok(pedidosPinpadService.consultar(filtroDTO, empresaId, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("erro-consultar-pedidos-pinpad", e.getMessage());
        }
    }

    @GetMapping("/find/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> consultarPorCodigo(@PathVariable Integer codigo) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            return ResponseEntityFactory.ok(pedidosPinpadService.consultarPorCodigo(codigo));
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("erro-consultar-pedido-codigo", e.getMessage());
        }
    }

    @PostMapping("/atualizar")
    public ResponseEntity<EnvelopeRespostaDTO> atualizar(@RequestBody String body) {
        HttpHeaders headers = new HttpHeaders();
        try {
            headers.setContentType(MediaType.APPLICATION_JSON);
            return ResponseEntityFactory.ok(pedidosPinpadService.atualizar(body));
        } catch (ServiceException e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno("erro-reprocessar-pedido", e.getMessage());
        }
    }
}
