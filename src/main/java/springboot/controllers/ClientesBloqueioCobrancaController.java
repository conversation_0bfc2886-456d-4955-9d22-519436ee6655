package springboot.controllers;

import controle.arquitetura.exceptions.ServiceException;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.arquitetura.ResponseEntityFactory;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springboot.dto.clientesbloqueiocobranca.ClienteBloqueioCobrancaDTO;
import springboot.dto.clientesbloqueiocobranca.ClienteBloqueioCobrancaFiltroDTO;
import springboot.services.intf.ClientesBloqueioCobrancaService;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/clientes-bloqueio-cobranca")
public class ClientesBloqueioCobrancaController {

    private final ClientesBloqueioCobrancaService clientesBloqueioCobrancaService;

    public ClientesBloqueioCobrancaController(ClientesBloqueioCobrancaService clientesBloqueioCobrancaService) {
        this.clientesBloqueioCobrancaService = clientesBloqueioCobrancaService;
    }

    @GetMapping
    public ResponseEntity<EnvelopeRespostaDTO> consultarClientesBloqueioCobranca(
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            HttpServletRequest request) {

        try {
            ClienteBloqueioCobrancaFiltroDTO filtro = new ClienteBloqueioCobrancaFiltroDTO();
            filtro.setEmpresaId(filtros.optInt("empresa"));
            filtro.setQuicksearchValue(filtros.optString("quicksearchValue"));
            if (filtros.optString("dataInicio") != null
                    && !filtros.optString("dataInicio").isEmpty()) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy");
                    filtro.setDataInicio(sdf.parse(filtros.optString("dataInicio")));
                } catch (Exception e) {
                    return ResponseEntityFactory.erroInterno("DATA_INICIO_INVALIDA", "Data de início inválida. Use o formato dd/MM/yyyy");
                }
            }

            if (filtros.optString("dataFinal") != null && !filtros.optString("dataFinal").isEmpty()) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("dd/MM/yyyy");
                    filtro.setDataFinal(sdf.parse(filtros.optString("dataFinal")));
                } catch (Exception e) {
                    return ResponseEntityFactory.erroInterno("DATA_FINAL_INVALIDA", "Data final inválida. Use o formato dd/MM/yyyy");
                }
            }

            PaginadorDTO paginadorDTO = new PaginadorDTO(request);

            return ResponseEntityFactory.ok(
                    clientesBloqueioCobrancaService.consultarClientesBloqueioCobranca(filtro, paginadorDTO),
                    paginadorDTO
            );

        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("ERRO_INTERNO", "Erro interno do servidor: " + e.getMessage());
        }
    }

    @GetMapping("/empresas")
    public ResponseEntity<EnvelopeRespostaDTO> obterEmpresas() {
        try {
            return ResponseEntityFactory.ok(clientesBloqueioCobrancaService.obterEmpresas());
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("ERRO_INTERNO", "Erro interno do servidor: " + e.getMessage());
        }
    }

    @GetMapping("/{clienteId}")
    public ResponseEntity<EnvelopeRespostaDTO> obterClientePorId(@PathVariable Integer clienteId) {
        try {
            return ResponseEntityFactory.ok(clientesBloqueioCobrancaService.obterClientePorId(clienteId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno("ERRO_INTERNO", "Erro interno do servidor: " + e.getMessage());
        }
    }
}
