/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package importador.outros;

import br.com.pactosolucoes.atualizadb.processo.CorrigirImportacao;
import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.utilitarias.validator.CreditCardValidator;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.jdom.Element;
import test.simulacao.LeitorXML;

/**
 *
 * <AUTHOR>
 */
public class CartaoCreditoXML {
    
    Map<String, AutorizacaoCobrancaClienteVO> autorizacoes = new HashMap<String, AutorizacaoCobrancaClienteVO>();
    static Connection con;
    static Integer codigoConvenio = 1;
    
    
    public static void importar() {
        try {
            con = DriverManager.getConnection("***********************************************", "postgres", "pactodb");
            AutorizacaoCobrancaCliente autorizacaoDao = new AutorizacaoCobrancaCliente(con);
            Conexao.guardarConexaoForJ2SE(con);
            Map<Integer, Integer> mapaClientes = mapaClientes();
            Map<String, AutorizacaoCobrancaClienteVO> autorizacoes = new HashMap<String, AutorizacaoCobrancaClienteVO>();
            LeitorXML leitorXML = new LeitorXML();
            int i = 0;
            CreditCardValidator cartaoCredito = new CreditCardValidator();
            List<Element> lista = leitorXML.lerXML("D:\\formasdepagamentos.xml");
            for (Element e : lista) {
                i++;
                String tipo = CorrigirImportacao.retirarNulo(e, "FormaPgExt");
                if(tipo.equals("Cartao Credito")){
                    try {
                        AutorizacaoCobrancaClienteVO aut = autorizacoes.get(CorrigirImportacao.retirarNulo(e, "Cd_Aluno"));
                        if (aut == null) {
                            String[] split = CorrigirImportacao.retirarNulo(e, "Nome_NoChequeCartao").split(";");
                            String seguranca = split[0].replaceFirst("SEG", "");
                            String validade = split[1].replaceFirst("DTV", "");
                            String nr = CorrigirImportacao.retirarNulo(e, "Nr_Doc");
                            aut = new AutorizacaoCobrancaClienteVO();
                            aut.setCliente(new ClienteVO());
                            aut.getCliente().setCodigo(mapaClientes.get(Integer.valueOf(CorrigirImportacao.retirarNulo(e, "Cd_Aluno"))));
                            aut.setNumeroCartao(nr);
                            aut.setConvenio(new ConvenioCobrancaVO());
                            aut.getConvenio().setCodigo(codigoConvenio);
                            aut.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.CARTAOCREDITO);
                            aut.setValidadeCartao(validade.substring(0,2)+"/20"+validade.substring(2,4));
                            aut.setTipoACobrar(TipoObjetosCobrarEnum.APENAS_PLANOS);
                            aut.setOperadoraCartao(OperadorasExternasAprovaFacilEnum.valueOf(cartaoCredito.operadora(nr)));
                            autorizacoes.put(CorrigirImportacao.retirarNulo(e, "Cd_Aluno"), aut);
                            System.out.println((i+"/"+lista.size()+" - ")+"OK");
                        }
                    } catch (Exception ex) {
                        System.out.println((i+"/"+lista.size()+" - ")+ CorrigirImportacao.retirarNulo(e, "Cd_Aluno") + " - erro = " + ex.getMessage());
                    }
                }
            }
            i = 0;
            for(AutorizacaoCobrancaClienteVO a : autorizacoes.values()){
                i++;
                try{
                    autorizacaoDao.incluir(a);
                    System.out.println((i+"/"+autorizacoes.values().size()+" - ")+"OK");
                }catch(Exception e){
                    System.out.println((i+"/"+autorizacoes.values().size()+" - ")+ " - erro = " + e.getMessage());
                }
                
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    
    public static Map<Integer, Integer> mapaClientes() throws Exception{
        Map<Integer, Integer> mapa = new HashMap<Integer, Integer>();
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT matriculaexterna, codigo from cliente", con);
        while(rs.next()){
            mapa.put(rs.getInt("matriculaexterna"), rs.getInt("codigo"));
        }
        return mapa;
    }
    
    public static void main(String[] a){
        importar();
    }
            
}
