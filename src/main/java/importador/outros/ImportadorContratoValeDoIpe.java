package importador.outros;

import controle.contrato.ContratoControle;
import importador.LeitorExcel2010;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.contrato.ContratoModalidadeHorarioTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeTurmaVO;
import negocio.comuns.contrato.ContratoModalidadeVO;
import negocio.comuns.plano.AmbienteVO;
import negocio.comuns.plano.CondicaoPagamentoVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.plano.HorarioVO;
import negocio.comuns.plano.ModalidadeVO;
import negocio.comuns.plano.PlanoCondicaoPagamentoVO;
import negocio.comuns.plano.PlanoDuracaoVO;
import negocio.comuns.plano.PlanoHorarioVO;
import negocio.comuns.plano.PlanoModalidadeVezesSemanaVO;
import negocio.comuns.plano.PlanoProdutoSugeridoVO;
import negocio.comuns.plano.PlanoVO;
import negocio.comuns.plano.TurmaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.FacadeManager;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by ulisses on 19/12/2017.
 */
public class ImportadorContratoValeDoIpe {

    public static Integer COLUNA_MATRICULA = 0;
    public static Integer COLUNA_NOME_ALUNO = 1;
    public static Integer DATA_INICIO_CONTRATO = 4;
    public static Integer DATA_FIM_CONTRATO = 5;

    public static void main(String[] args) {
        try {
            String pathExcel = "/opt/Venc_tit_ipe_slim.xlsx";
            Connection connection = new Conexao("**************************************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE("valeDoIpe", connection);
            importarContratos(pathExcel, connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void importarContratos(String pathExcel_plano, Connection connection) throws Exception {
        List<String> nomesErro = new ArrayList<String>();
        try {
            System.out.println("INICIO IMPORTACAO");
            connection.setAutoCommit(false);
            List<AmbienteVO> listaAmbiente = FacadeManager.getFacade().getAmbiente().consultarPorDescricao("PISCINA", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((listaAmbiente == null) || (listaAmbiente.size() <= 0)) {
                throw new Exception("Não foi encontrado nenhum ambiente com o nome de PISCINA. Cadastre o ambiente e depois repita a importação.");
            }
            EmpresaVO empresaVO = FacadeManager.getFacade().getEmpresa().consultarPorCodigo(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(empresaVO, "getCodigo()")) {
                throw new Exception("Não foi encontrado nenhuma empresa cadastrada. Cadastre uma empresa e depois repita a importação.");
            }
            ColaboradorVO professor = FacadeManager.getFacade().getColaborador().consultarPorNomeColaborador("PACTO", empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UtilReflection.objetoMaiorQueZero(professor, "getCodigo()")) {
                throw new Exception("Não foi encontrado nenhum professor cadastrado com o nome PACTO. Cadastre um novo professor com o nome PACTO e depois repita a importação.");
            }
            HorarioVO horarioTurma = null;
            HorarioVO horarioLivre = null;
            List<HorarioVO> listaHorario = FacadeManager.getFacade().getHorario().consultarPorDescricao("", false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if ((listaHorario == null) || (listaHorario.size() <= 0)) {
                throw new Exception("Não foi encontrado nenhum horário cadastrado. Cadastre dois horário(HORARIO DA TURMA e LIVRE) depois repita a importação.");
            }
            for (HorarioVO horarioVO : listaHorario) {
                if (horarioVO.getDescricao().contains("TURMA")) {
                    horarioTurma = horarioVO;
                } else if (horarioVO.getDescricao().contains("LIVRE")) {
                    horarioLivre = horarioTurma;
                }
            }
            if (!UtilReflection.objetoMaiorQueZero(horarioTurma, "getCodigo()")) {
                throw new Exception("Não foi encontrado nenhum horário cadastrado com o nome HORARIO DA TURMA. Cadastre um novo horário com o nome HORARIO DA TURMA e depois repita a importação.");
            }
            if (!UtilReflection.objetoMaiorQueZero(horarioLivre, "getCodigo()")) {
                throw new Exception("Não foi encontrado nenhum horário cadastrado com o nome LIVRE. Cadastre um novo horário com o nome LIVRE e depois repita a importação.");
            }

            List<XSSFRow> linhasPlano = LeitorExcel2010.lerLinhas(pathExcel_plano);
            List<DadosImportarTO> listaDadosImportar = new ArrayList<DadosImportarTO>();
            int i = 0;

            for (XSSFRow linha : linhasPlano) {
                String nomeAluno = LeitorExcel2010.obterString(linha, COLUNA_NOME_ALUNO);
                try {
                    lerDadosPlano(linha, listaDadosImportar, pathExcel_plano, i);
                } catch (Exception ex) {
                    nomesErro.add(nomeAluno + " - " + ex.getMessage());
                }
                i++;
            }

            String nomeModalidade = "FITNESS";
            ModalidadeVO modalidadeVO = FacadeManager.getFacade().getModalidade().consultarPorNomeModalidade(nomeModalidade, false, Uteis.NIVELMONTARDADOS_TODOS);
            for (DadosImportarTO dadosImportarTO : listaDadosImportar) {
                dadosImportarTO.setModalidadeVO(modalidadeVO);
            }

            PlanoVO planoVO = FacadeManager.getFacade().getPlano().consultarPorDescricao("PLANO IMPORTACAO", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            StringBuilder nomes = new StringBuilder();
            // Validar se todos os clientes estão cadastrados.
            for (DadosImportarTO dadosImportarTO : listaDadosImportar) {
                dadosImportarTO.setClienteVO(FacadeManager.getFacade().getCliente().consultarPorCodigoMatricula(dadosImportarTO.getMatriculaExterna(), 1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
                if (!UtilReflection.objetoMaiorQueZero(dadosImportarTO, "getClienteVO().getCodigo()")) {
                    if (nomes.length() <= 0) {
                        nomes.append(dadosImportarTO.getNomeAluno());
                    } else {
                        nomes.append(",\n ").append(dadosImportarTO.getNomeAluno());
                    }
                }
            }
            if (nomes.length() > 0) {
                throw new Exception("Os clientes abaixo não foram encontrados através da matriculaExterna. \n" + nomes.toString() + "\n Cadastre os clientes manualmente ou Retire-os da planilha e repita a operação.");
            }


            incluirContratos(listaDadosImportar, planoVO);
            connection.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");

            for (String erro : nomesErro) {
                System.out.println(erro);
            }
        } catch (Exception e) {
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }

    private static boolean jaImportouContrato(DadosImportarTO dadosImportarTO, PlanoVO planoVO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select cm.* \n");
        sql.append("from contratoModalidade cm \n");
        sql.append("inner join contrato c on c.codigo = cm.contrato \n");
        sql.append("where c.pessoa = ").append(dadosImportarTO.getClienteVO().getPessoa().getCodigo());
        sql.append(" and c.plano = ").append(planoVO.getCodigo());
        Statement st = FacadeManager.getFacade().getContrato().getCon().createStatement();
        ResultSet rs = st.executeQuery(sql.toString());
        return rs.next();
    }


    private static void montarDadosMatriculaHorarioTurma(ContratoControle contratoControle, DadosImportarTO dadosImportarTO) throws Exception {
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
            contratoModalidadeVO.getModalidade().setModalidadeEscolhida(false);
        }
        ContratoModalidadeTurmaVO cmt = new ContratoModalidadeTurmaVO();
        for (HorarioTurmaVO horarioTurmaVO : dadosImportarTO.getListaHorarioTurma()) {
            TurmaVO turmaVO = dadosImportarTO.getTurmaVO();
            turmaVO.setTurmaEscolhida(true);
            ContratoModalidadeHorarioTurmaVO cmht = new ContratoModalidadeHorarioTurmaVO();
            cmht.setHorarioTurma(horarioTurmaVO);
            cmht.getHorarioTurma().setHorarioTurmaEscolhida(true);
            cmt.getContratoModalidadeHorarioTurmaVOs().add(cmht);
            cmt.setTurma(turmaVO);
            for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
                if ((contratoModalidadeVO.getModalidade().getCodigo().equals(dadosImportarTO.getModalidadeVO().getCodigo()))) {
                    //(contratoModalidadeVO.getModalidade().getNrVezes().intValue() == dadosImportarTO.getNumeroVzSemana().intValue())){
                    cmt.setContratoModalidade(contratoModalidadeVO.getCodigo());
                    if (contratoModalidadeVO.getContratoModalidadeTurmaVOs().size() == 0) {
                        PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
                        planoVezesSemanaVO.setNrVezes(dadosImportarTO.getNumeroVzSemana());
                        planoVezesSemanaVO.setVezeSemanaEscolhida(true);
                        contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemanaVO);
                        contratoModalidadeVO.setNrVezesSemana(dadosImportarTO.getNumeroVzSemana());

                        contratoModalidadeVO.getContratoModalidadeTurmaVOs().add(cmt);
                    }
                    contratoModalidadeVO.getModalidade().setUtilizarTurma(true);
                    contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                    break;
                }
            }
        }

    }

    private static void montarDadosMatriculaHorarioLivre(ContratoControle contratoControle, DadosImportarTO dadosImportarTO) throws Exception {
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
            contratoModalidadeVO.getModalidade().setModalidadeEscolhida(false);
        }
        for (ContratoModalidadeVO contratoModalidadeVO : contratoControle.getContratoVO().getContratoModalidadeVOs()) {
            if ((contratoModalidadeVO.getModalidade().getCodigo().equals(dadosImportarTO.getModalidadeVO().getCodigo()))) {
                contratoModalidadeVO.setContratoModalidadeTurmaVOs(new ArrayList());
                PlanoModalidadeVezesSemanaVO planoVezesSemanaVO = new PlanoModalidadeVezesSemanaVO();
                planoVezesSemanaVO.setNrVezes(7);
                planoVezesSemanaVO.setVezeSemanaEscolhida(true);
                contratoModalidadeVO.setPlanoVezesSemanaVO(planoVezesSemanaVO);
                contratoModalidadeVO.setNrVezesSemana(7);
                contratoModalidadeVO.getModalidade().setUtilizarTurma(false);
                contratoModalidadeVO.getModalidade().setModalidadeEscolhida(true);
                break;
            }
        }
    }


    private static void incluirContratos(List<DadosImportarTO> listaDadosImportar, PlanoVO planoVO) throws Exception {
        int total = 0;
        for (DadosImportarTO dadosImportarTO : listaDadosImportar) {
            total++;
            dadosImportarTO.setClienteVO(FacadeManager.getFacade().getCliente().consultarPorCodigoMatricula(dadosImportarTO.getMatriculaExterna(), 1, Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            if (!UtilReflection.objetoMaiorQueZero(dadosImportarTO, "getClienteVO().getCodigo()")) {
                throw new Exception("Não foi encontrado nenhum cliente com a matriculaExterna " + dadosImportarTO.getMatriculaExterna());
            }
            if ((dadosImportarTO.getQtdeMesesDuracaoContrato() == null) || (dadosImportarTO.getQtdeMesesDuracaoContrato() <= 0)) {
                throw new Exception("Não foi encontrado a duração do plano para o cliente " + dadosImportarTO.getNomeAluno());
            }
            if (jaImportouContrato(dadosImportarTO, planoVO)) {
                continue;
            }
            ContratoControle contratoControle = FacadeManager.getFacade().getContrato().obterContratoControle(planoVO.getCodigo(), dadosImportarTO.getClienteVO().getCodigo());
            contratoControle.getContratoVO().setImportacao(true);

            // ZERAR OS VALORES DOS PRODUTOS DOS PLANOS
            for (Object obj : contratoControle.getContratoVO().getPlano().getPlanoProdutoSugeridoVOs()) {
                PlanoProdutoSugeridoVO planoProdutoSugeridoVO = (PlanoProdutoSugeridoVO) obj;
                planoProdutoSugeridoVO.setValorProduto(0.0);
                planoProdutoSugeridoVO.getProduto().setValorFinal(0.0);
            }

            // HORARIO LIVRE
            montarDadosMatriculaHorarioLivre(contratoControle, dadosImportarTO);

            CondicaoPagamentoVO condicaoPagamentoVO = FacadeManager.getFacade().getCondicaoPagamento().consultarPorDescricao("A VISTA", Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            // INFORMAR A DURAÇÃO DO CONTRATO
            PlanoDuracaoVO planoDuracaoVO = FacadeManager.getFacade().getPlanoDuracao().consultarPorNumeroMesesPlano(dadosImportarTO.getQtdeMesesDuracaoContrato(), planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            if (planoDuracaoVO == null) {
                planoDuracaoVO = new PlanoDuracaoVO();
                planoDuracaoVO.setPlano(planoVO.getCodigo());
                planoDuracaoVO.setNumeroMeses(dadosImportarTO.getQtdeMesesDuracaoContrato());
                planoDuracaoVO.setNrMaximoParcelasCondPagamento(dadosImportarTO.getQtdeMesesDuracaoContrato());
                planoDuracaoVO.setTipoValor("VE");
                planoDuracaoVO.setValorDesejado(0.0);
                FacadeManager.getFacade().getPlanoDuracao().incluir(planoDuracaoVO);

                PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = new PlanoCondicaoPagamentoVO();
                planoCondicaoPagamentoVO.setPlanoDuracao(planoDuracaoVO.getCodigo());
                planoCondicaoPagamentoVO.setCondicaoPagamento(condicaoPagamentoVO);
                planoCondicaoPagamentoVO.setQtdParcela(dadosImportarTO.getQtdeMesesDuracaoContrato());
                FacadeManager.getFacade().getPlanoCondicaoPagamento().incluir(planoCondicaoPagamentoVO);
            }
            contratoControle.getContratoVO().setPlanoDuracao(planoDuracaoVO);

            // INFORMAR O HORÁRIO
            List<PlanoHorarioVO> listaPlanoHorario = FacadeManager.getFacade().getPlanoHorario().consultarPlanoHorarios(planoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            PlanoHorarioVO planoHorarioSel = null;
            for (PlanoHorarioVO planoHorarioVO : listaPlanoHorario) {
                if (planoHorarioVO.getHorario().getDescricao().contains("LIVRE")) {
                    planoHorarioSel = planoHorarioVO;
                    break;
                }
            }
            contratoControle.getContratoVO().setPlanoHorario(planoHorarioSel);

            // INFORMAR A CONDIÇÃO DE PAGAMENTO
            PlanoCondicaoPagamentoVO planoCondicaoPagamentoVO = FacadeManager.getFacade().getPlanoCondicaoPagamento().consultarPorPlanoDuracaoCondicao(planoDuracaoVO.getCodigo(), condicaoPagamentoVO.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
            planoCondicaoPagamentoVO.getCondicaoPagamento().setCondicaoPagamentoEscolhida(true);
            contratoControle.selecionarCondicaoPagamento(planoCondicaoPagamentoVO);
            contratoControle.getContratoVO().setPlanoCondicaoPagamento(planoCondicaoPagamentoVO);
            contratoControle.getContratoVO().setVigenciaDe(dadosImportarTO.getDataInicioContrato());
            contratoControle.getContratoVO().setVigenciaAte(dadosImportarTO.getDataFimContrato());
            contratoControle.getContratoVO().setDataLancamento(dadosImportarTO.getDataInicioContrato());
            contratoControle.getContratoVO().setVigenciaAteAjustada(dadosImportarTO.getDataFimContrato());
            contratoControle.getContratoVO().setDataMatricula(dadosImportarTO.getDataInicioContrato());
            contratoControle.setDataInicioContrato(dadosImportarTO.getDataInicioContrato());

            String fecharNegociacao = contratoControle.fecharNegociacao();
            if (fecharNegociacao.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            UsuarioVO usuarioVO = FacadeManager.getFacade().getUsuario().consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_MINIMOS);
            String gravarContrato = contratoControle.gravar(usuarioVO);
            if (gravarContrato.contains("erro")) {
                throw new Exception(contratoControle.getMensagemDetalhada());
            }
            System.out.println("CONTRATO INCLUIDO COM SUCESSO PARA O CLIENTE: " + dadosImportarTO.getNomeAluno());
        }
        System.out.println("\n\n TOTAL GERAL DE CONTRATOS INCLUIDOS: " + total);
    }


    private static void lerDadosPlano(XSSFRow linha, List<DadosImportarTO> listaDadosImportar, String pathExcel_plano, int indiceLinha) throws Exception {
        Integer matriculaExterna = Integer.parseInt(LeitorExcel2010.obterString(linha, COLUNA_MATRICULA));
        if (matriculaExterna <= 0) {
            throw new Exception("Erro no arquivo: " + pathExcel_plano + " na linha: " + indiceLinha + ". Não foi informado a matrícula do aluno");
        }
        DadosImportarTO dadosImportarTO = new ImportadorContratoValeDoIpe().new DadosImportarTO();
        dadosImportarTO.setMatriculaExterna(matriculaExterna);
        dadosImportarTO.setNomeAluno(LeitorExcel2010.obterString(linha, COLUNA_NOME_ALUNO));
        Date dataInicio = LeitorExcel2010.obterDataNoFormatoData(linha, DATA_INICIO_CONTRATO);
        if (dataInicio == null) {
            return;
        }
        dadosImportarTO.setDataInicioContrato(dataInicio);
        Date dataFinal = LeitorExcel2010.obterDataNoFormatoData(linha, DATA_FIM_CONTRATO);
        if (dataFinal == null) {
            return;
        }
        dadosImportarTO.setDataFimContrato(dataFinal);
        dadosImportarTO.setQtdeMesesDuracaoContrato((int) Uteis.nrDiasEntreDatas(dadosImportarTO.getDataInicioContrato(), dadosImportarTO.getDataFimContrato()) / 30);

        listaDadosImportar.add(dadosImportarTO);
    }

    public static Connection obterConexao(String hostBD, String porta, String nomeBD) throws Exception {
        String userBD = "postgres";
        String passwordBD = "pactodb";
        String url = "jdbc:postgresql://" + hostBD + ":" + porta + "/" + nomeBD;
        String driver = "org.postgresql.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(url, userBD, passwordBD);
    }

    public class DadosImportarTO {
        private Integer matriculaExterna;
        private String nomeAluno;
        private Date dataInicioContrato;
        private Date dataFimContrato;
        private String nomeModalidade;
        private ClienteVO clienteVO;
        private double valorContrato = 0.01;
        private Integer qtdeMesesDuracaoContrato;
        private String nomeTurma;
        private String horaInicioTurma;
        private String horaFimTurma;
        private String nrVezesSemanaTurma;
        private String nomeProfessor;
        private ModalidadeVO modalidadeVO;
        private TurmaVO turmaVO;
        private List<HorarioTurmaVO> listaHorarioTurma;


        public Integer getMatriculaExterna() {
            return matriculaExterna;
        }

        public void setMatriculaExterna(Integer matriculaExterna) {
            this.matriculaExterna = matriculaExterna;
        }

        public String getNomeAluno() {
            return nomeAluno;
        }

        public void setNomeAluno(String nomeAluno) {
            this.nomeAluno = nomeAluno;
        }

        public Date getDataInicioContrato() {
            return dataInicioContrato;
        }

        public void setDataInicioContrato(Date dataInicioContrato) {
            this.dataInicioContrato = dataInicioContrato;
        }

        public Date getDataFimContrato() {
            return dataFimContrato;
        }

        public void setDataFimContrato(Date dataFimContrato) {
            this.dataFimContrato = dataFimContrato;
        }

        public String getNomeModalidade() {
            return nomeModalidade;
        }

        public void setNomeModalidade(String nomeModalidade) {
            this.nomeModalidade = nomeModalidade;
        }

        public ClienteVO getClienteVO() {
            return clienteVO;
        }

        public void setClienteVO(ClienteVO clienteVO) {
            this.clienteVO = clienteVO;
        }

        public double getValorContrato() {
            return valorContrato;
        }

        public void setValorContrato(double valorContrato) {
            this.valorContrato = valorContrato;
        }

        public Integer getQtdeMesesDuracaoContrato() {
            return qtdeMesesDuracaoContrato;
        }

        public void setQtdeMesesDuracaoContrato(Integer qtdeMesesDuracaoContrato) {
            this.qtdeMesesDuracaoContrato = qtdeMesesDuracaoContrato;
        }

        public String getNomeTurma() {
            return nomeTurma;
        }

        public void setNomeTurma(String nomeTurma) {
            this.nomeTurma = nomeTurma;
        }

        public ModalidadeVO getModalidadeVO() {
            return modalidadeVO;
        }

        public void setModalidadeVO(ModalidadeVO modalidadeVO) {
            this.modalidadeVO = modalidadeVO;
        }

        public TurmaVO getTurmaVO() {
            return turmaVO;
        }

        public void setTurmaVO(TurmaVO turmaVO) {
            this.turmaVO = turmaVO;
        }

        public String getHoraInicioTurma() {
            return horaInicioTurma;
        }

        public void setHoraInicioTurma(String horaInicioTurma) {
            this.horaInicioTurma = horaInicioTurma;
        }

        public String getHoraFimTurma() {
            return horaFimTurma;
        }

        public void setHoraFimTurma(String horaFimTurma) {
            this.horaFimTurma = horaFimTurma;
        }

        public String getNrVezesSemanaTurma() {
            return nrVezesSemanaTurma;
        }

        public void setNrVezesSemanaTurma(String nrVezesSemanaTurma) {
            this.nrVezesSemanaTurma = nrVezesSemanaTurma;
        }

        public String getNomeProfessor() {
            return nomeProfessor;
        }

        public void setNomeProfessor(String nomeProfessor) {
            this.nomeProfessor = nomeProfessor;
        }

        public List<HorarioTurmaVO> getListaHorarioTurma() {
            return listaHorarioTurma;
        }

        public void setListaHorarioTurma(List<HorarioTurmaVO> listaHorarioTurma) {
            this.listaHorarioTurma = listaHorarioTurma;
        }

        public Integer getNumeroVzSemana() {
            return Integer.parseInt(this.nrVezesSemanaTurma.substring(0, 1));
        }


        public String toString() {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
            StringBuilder msg = new StringBuilder();
            msg.append(this.matriculaExterna).append(" - ");
            msg.append(this.nomeAluno).append(" - ");
            msg.append(sdf.format(this.dataInicioContrato)).append(" - ");
            msg.append(sdf.format(this.dataFimContrato)).append(" - ");
            msg.append("Modalidade: " + this.getNomeModalidade()).append(" - ");
            msg.append("Turma: " + this.getNomeTurma()).append(" - ");
            msg.append(this.getHoraInicioTurma()).append(" - ");
            msg.append(this.getHoraFimTurma()).append(" - ");
            msg.append(this.getNrVezesSemanaTurma()).append(" - ");
            msg.append(this.getNomeProfessor());
            return msg.toString();
        }

        @Override
        public boolean equals(Object obj) {
            if ((!(obj instanceof DadosImportarTO))) {
                return false;
            }
            if (this.matriculaExterna == null) {
                return false;
            }
            return ((DadosImportarTO) obj).getMatriculaExterna().equals(this.matriculaExterna);
        }
    }

}
