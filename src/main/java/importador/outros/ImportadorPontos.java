package importador.outros;

import importador.LeitorExcel2010;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.HistoricoPontos;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class ImportadorPontos {

    public static Integer COLUNA_MATRICULA = 0;
    public static Integer COLUNA_NOME_ALUNO = 1;
    public static Integer COLUNA_DATA_ENTRADA = 2;
    public static Integer COLUNA_PONTOS = 3;

    public static void main(String[] args) {
        try {
            String pathExcel = "C:\\pacto\\backups\\Lista de pontos a serem adicionados - Campo Grande.xlsx";
            Connection con = new Conexao("**********************************************************************", "postgres", "pactodb").getConexao();
            String mascaraData = "dd/MM/yyyy HH:mm";
            Conexao.guardarConexaoForJ2SE(con);

            acrescentarPontosPorAcesso(pathExcel, mascaraData, con);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void acrescentarPontosPorAcesso(String pathPlanilha, String mascaraData, Connection connection) throws Exception {
        List<String> casosAvaliar = new ArrayList<>();
        try {
            System.out.println("INICIO IMPORTACAO");
            connection.setAutoCommit(false);

            Cliente clienteDAO = new Cliente(connection);
            HistoricoPontos historicoPontosDAO = new HistoricoPontos(connection);

            List<XSSFRow> linhasPlano = LeitorExcel2010.lerLinhas(pathPlanilha);
            int i = 0;
            for (XSSFRow linha : linhasPlano) {
                System.out.print("\n" + (++i) + "/" + linhasPlano.size());
                if (i == 0) {
                    continue;
                }

                String nomeAluno = LeitorExcel2010.obterString(linha, COLUNA_NOME_ALUNO);
                Date dataEntrada = LeitorExcel2010.obterDataNoFormatoData(linha, COLUNA_DATA_ENTRADA);
                if (dataEntrada == null) {
                    String data = LeitorExcel2010.obterString(linha, COLUNA_DATA_ENTRADA);
                    dataEntrada = UteisValidacao.emptyString(data) ? null : Calendario.getDate(mascaraData, data);
                }

                String m = LeitorExcel2010.obterString(linha, COLUNA_MATRICULA);
                int matricula = UteisValidacao.emptyString(m) || !m.matches("\\d+") ? 0 : Integer.parseInt(m);

                String p = LeitorExcel2010.obterString(linha, COLUNA_PONTOS);
                Integer pontos = UteisValidacao.emptyString(p) || !p.matches("\\d+") ? 0 : Integer.parseInt(p);

                String descricaoAtual = String.format("mat: %d - %s - pontos add: %d", matricula, nomeAluno, pontos);
                System.out.printf(" - Processando %s", descricaoAtual);

                try {

                    if (UteisValidacao.emptyNumber(matricula)) {
                        casosAvaliar.add(descricaoAtual + "Pontos não adicionados! Número Matricula não informado ou invalido! ");
                        continue;
                    }
                    if (UteisValidacao.emptyNumber(pontos)) {
                        casosAvaliar.add(descricaoAtual + "Pontos não adicionados! Quantidade de Pontos não informada ou invalida!");
                        continue;
                    }
                    if (dataEntrada == null) {
                        casosAvaliar.add(descricaoAtual + " - Pontos não adicionados! Data não informada!");
                        continue;
                    }

                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(matricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (clienteVO == null || clienteVO.getCodigo() == 0) {
                        casosAvaliar.add(descricaoAtual + " - Nenhum aluno encontrado para essa matricula");
                        continue;
                    }

                    String descricaoPontos = "AJUSTE DE SALDO IMPORTA\u00C7\u00C3O - Dt Entrada: " + Calendario.getData(dataEntrada, "dd/MM/yyyy HH:mm:mm");

                    String sql = "select codigo from historicopontos \n" +
                            "where cliente = " + clienteVO.getCodigo() + "\n" +
                            "and descricao ilike '%Dt Entrada:%" + Calendario.getData(dataEntrada, "dd/MM/yyyy HH:mm:mm") + "%' \n";
                    ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, connection);
                    if (rs.next()) {
                        System.out.printf(" - RESULTADO: Os pontos já foram adicionados anteriorente");
                        continue;
                    }

                    Integer totalPontos = historicoPontosDAO.obterPontosTotalPorCliente(clienteVO.getCodigo());
                    totalPontos += pontos;

                    HistoricoPontosVO historicoPontosVO = new HistoricoPontosVO();
                    historicoPontosVO.setCliente(clienteVO);
                    historicoPontosVO.setDescricao(descricaoPontos);
                    historicoPontosVO.setPontos(totalPontos);
                    historicoPontosVO.setPontostotal(pontos);
                    historicoPontosVO.setDataaula(dataEntrada);
                    historicoPontosVO.setDataConfirmacao(dataEntrada);
                    historicoPontosVO.setEntrada(true);
                    historicoPontosVO.setTipoPonto(TipoItemCampanhaEnum.AJUSTE_PONTO);

                    historicoPontosDAO.incluir(historicoPontosVO);
                    System.out.print(" - RESULTADO: Pontos Adicionados!");

                } catch (Exception ex) {
                    casosAvaliar.add(descricaoAtual + " Erro: " + ex.getMessage());
                }
            }

            connection.commit();
            System.out.printf("\nIMPORTACAO REALIZADA COM SUCESSO");
            System.out.printf("\n==================================================");
            System.out.printf("\nCASOS AVALIAR:");
            for (String erro : casosAvaliar) {
                System.out.printf("\n" + erro);
            }
        } catch (Exception e) {
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }

}
