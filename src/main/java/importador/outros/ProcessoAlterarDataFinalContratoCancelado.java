package importador.outros;

import br.com.pactosolucoes.enumeradores.SituacaoContratoEnum;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import relatorio.negocio.comuns.sad.SituacaoClienteSinteticoEnum;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ProcessoAlterarDataFinalContratoCancelado {

    private static boolean verificaContratoSituacaoAtivo(Connection con, int codigoContrato) {
        String sql = "SELECT situacao FROM contrato WHERE codigo = ?";
        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setInt(1, codigoContrato);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("situacao").equals("AT");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        return false;
    }

    private static boolean verificaContratoPossuiOperacaoCancelamento(Connection con, int codigoContrato) {
        String sql = "SELECT tipooperacao FROM contratooperacao WHERE contrato = ?";
        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setInt(1, codigoContrato);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("tipooperacao").equals("CA");
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        return false;
    }

    public static void atualizarSintetico(Connection con, String codigoContrato) throws Exception {

        Integer codigoPessoa = null;

        Integer codigoContratoInt = Integer.parseInt(codigoContrato);

        String sql = "SELECT pessoa FROM contrato WHERE codigo = ?";

        try (PreparedStatement stmt = con.prepareStatement(sql)) {
            stmt.setInt(1, codigoContratoInt);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    codigoPessoa = rs.getInt("pessoa");

                    ZillyonWebFacade zillyonWebFacadeDAO = new ZillyonWebFacade(con);

                    ClienteVO cliente = zillyonWebFacadeDAO.getCliente().consultarPorCodigoPessoa(codigoPessoa, Uteis.NIVELMONTARDADOS_SITUACAOCLIENTESINTETICODW);

                    zillyonWebFacadeDAO.atualizarSintetico(cliente, Calendario.hoje(), SituacaoClienteSinteticoEnum.GRUPO_TODOS, false);

                } else {
                    throw new Exception("Não foi possível atualizar o sintetico do cliente id =" + codigoPessoa + ". Cliente nao encontrado.");
                }
            }
        }
    }

    public static String processar(Connection con, String codigoContrato, Date dataFinalContratoCancelado) throws SQLException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String dataFimStr = sdf.format(dataFinalContratoCancelado);
        Timestamp dataFimSql = new Timestamp(dataFinalContratoCancelado.getTime());

        int codigoContratoInt = Integer.parseInt(codigoContrato);

        try {
            con.setAutoCommit(false);

            if(!verificaContratoSituacaoAtivo(con, codigoContratoInt) || !verificaContratoPossuiOperacaoCancelamento(con, codigoContratoInt)) {
                throw new SQLException("Esse processo só pode ser executado em contratos ativos e com operação de cancelamento!");
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE contrato SET vigenciaateajustada = ?, situacao = 'CA' WHERE codigo = ?")) {
                stmt.setTimestamp(1, dataFimSql);
                stmt.setInt(2, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE contrato SET dataprevistarenovar = vigenciaateajustada, dataprevistarematricula = vigenciaateajustada WHERE codigo = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE contratooperacao cp SET datainicioefetivacaooperacao = con.vigenciaateajustada, datafimefetivacaooperacao = con.vigenciaateajustada " +
                            "FROM contrato con WHERE con.codigo = cp.contrato AND cp.tipooperacao = 'CA' AND cp.contrato = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE contratooperacao cp SET datafimefetivacaooperacao = cp_ca.datainicioefetivacaooperacao - INTERVAL '1 day' " +
                            "FROM contratooperacao cp_ca WHERE cp_ca.contrato = cp.contrato AND cp_ca.tipooperacao = 'CA' AND cp.codigo = " +
                            "(SELECT max(cp_sub.codigo) FROM contratooperacao cp_sub WHERE cp_sub.contrato = cp.contrato AND cp_sub.codigo < cp_ca.codigo) AND cp.contrato = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE periodoacessocliente pa SET datainicioacesso = con.vigenciaateajustada, datafinalacesso = con.vigenciaateajustada " +
                            "FROM contrato con WHERE con.codigo = pa.contrato AND pa.tipoacesso = 'CN' AND pa.contrato = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE periodoacessocliente pa SET datafinalacesso = pa_ca.datainicioacesso - INTERVAL '1 day' " +
                            "FROM periodoacessocliente pa_ca WHERE pa_ca.contrato = pa.contrato AND pa_ca.tipoacesso = 'CN' AND pa.codigo = " +
                            "(SELECT max(pa_sub.codigo) FROM periodoacessocliente pa_sub WHERE pa_sub.contrato = pa.contrato AND pa_sub.codigo < pa_ca.codigo) AND pa.contrato = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE historicocontrato hc SET datainiciosituacao = con.vigenciaateajustada, datafinalsituacao = con.vigenciaateajustada " +
                            "FROM contrato con WHERE con.codigo = hc.contrato AND hc.tipohistorico = 'CA' AND hc.contrato = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            try (PreparedStatement stmt = con.prepareStatement(
                    "UPDATE historicocontrato hc SET datafinalsituacao = hc_ca.datainiciosituacao - INTERVAL '1 day' " +
                            "FROM historicocontrato hc_ca WHERE hc_ca.contrato = hc.contrato AND hc_ca.tipohistorico = 'CA' AND hc.codigo = " +
                            "(SELECT max(hc_sub.codigo) FROM historicocontrato hc_sub WHERE hc_sub.contrato = hc.contrato AND hc_sub.codigo < hc_ca.codigo) AND hc.contrato = ?")) {
                stmt.setInt(1, codigoContratoInt);
                stmt.executeUpdate();
            }

            con.commit();
            return "Data final do contrato " + codigoContrato + " ajustada para " + sdf.format(dataFinalContratoCancelado) + ".";

        } catch (SQLException e) {
            con.rollback();
            throw e;
        } finally {
            con.setAutoCommit(true);
        }
    }
}
