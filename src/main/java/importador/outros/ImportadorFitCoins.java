package importador.outros;

import importador.LeitorExcel2010;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.HistoricoPontosVO;
import negocio.comuns.basico.enumerador.TipoItemCampanhaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.HistoricoPontos;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.poi.xssf.usermodel.XSSFRow;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ulisses on 19/12/2017.
 */
public class ImportadorFitCoins {

    public static Integer COLUNA_MATRICULA = 0;
    public static Integer COLUNA_NOME_ALUNO = 1;
    public static Integer COLUNA_TOTAL_PONTOS = 2;

    public static void main(String[] args) {
        try {
            String pathExcel = "/opt/FIT_COINS RUN FITNESS.xlsx";
            Connection connection = new Conexao("***********************************************************", "postgres", "pactodb").getConexao();
            Conexao.guardarConexaoForJ2SE("runFitness", connection);
            importarFitcoins(pathExcel, connection);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void importarFitcoins(String pathExcel_plano, Connection connection) throws Exception {
        List<String> nomesErro = new ArrayList<String>();
        try {
            System.out.println("INICIO IMPORTACAO");
            connection.setAutoCommit(false);

            Cliente clienteDAO = new Cliente(connection);
            HistoricoPontos historicoPontosDAO = new HistoricoPontos(connection);

            List<XSSFRow> linhasPlano = LeitorExcel2010.lerLinhas(pathExcel_plano);
            int i = 0;
            for (XSSFRow linha : linhasPlano) {
                System.out.println(++i + "/" + linhasPlano.size());

                String nomeAluno = LeitorExcel2010.obterString(linha, COLUNA_NOME_ALUNO);
                int matricula = LeitorExcel2010.obterNumero(linha, COLUNA_MATRICULA).intValue();
                try {
                    Integer fitcoins = LeitorExcel2010.obterNumero(linha, COLUNA_TOTAL_PONTOS).intValue();

                    ClienteVO clienteVO = clienteDAO.consultarPorCodigoMatriculaExterna(matricula, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    if (clienteVO == null || clienteVO.getCodigo() == 0) {
                        throw new ConsistirException("Não encontrado");
                    }
                    Integer totalPontos = historicoPontosDAO.obterPontosTotalPorCliente(clienteVO.getCodigo());

                    int diff = fitcoins - totalPontos;
                    if (diff == 0) {
                        continue;
                    }

                    HistoricoPontosVO historicoPontosVO = new HistoricoPontosVO();
                    historicoPontosVO.setCliente(clienteVO);
                    historicoPontosVO.setDescricao("AJUSTE DE SALDO");
                    historicoPontosVO.setPontos(diff);
                    historicoPontosVO.setPontostotal(fitcoins);
                    historicoPontosVO.setDataaula(Calendario.hoje());
                    historicoPontosVO.setDataConfirmacao(Calendario.hoje());
                    historicoPontosVO.setEntrada(diff >= 0);
                    historicoPontosVO.setTipoPonto(TipoItemCampanhaEnum.AJUSTE_PONTO);

                    historicoPontosDAO.incluir(historicoPontosVO);


                } catch (Exception ex) {
                    nomesErro.add(matricula + " - " + nomeAluno + " - " + ex.getMessage());
                }
            }


            // Validar se todos os clientes estão cadastrados.
            connection.commit();
            System.out.println("IMPORTACAO REALIZADA COM SUCESSO");

            for (String erro : nomesErro) {
                System.out.println(erro);
            }
        } catch (Exception e) {
            connection.rollback();
            System.out.println("ERRO NA IMPORTACAO.");
            e.printStackTrace();
        } finally {
            connection.setAutoCommit(true);
            connection.close();
        }
    }

}
