package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;
import negocio.comuns.utilitarias.Uteis;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/12/2019.
 */
public class ColaboradorImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private String nome, cpf, rg, rgOrgao, sexo, estadoCivil, profissao, grauInstrucao, nacionalidade;
    private String naturalidade, cidade, uf, nomePai, nomeMae, observacao, categoria, cref;
    private boolean ativo = false;
    private Date dataNascimento, dataCadastro;
    private List<TelefoneImportacaoJSON> telefones;
    private List<EmailImportacaoJSON> emails;
    private List<EnderecoImportacaoJSON> enderecos;
    private List<TipoColaboradorImportacaoJSON> listaTipoColaborador;

    private Integer empresa;

    private Integer codigo;
    private Integer codigoUsuario;
    private boolean sucesso = false;
    private String msgRetorno;
    private String msgEnvioEmail;

    public String getNome() {
        if (nome == null) {
            nome = "";
        }
        return nome.toUpperCase();
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpf_Formatado() {
        return Uteis.formatarCpfCnpj(getCpf(), false);
    }

    public String getCpf_SomenteNumeros() {
        return Uteis.formatarCpfCnpj(getCpf(), true);
    }

    public String getCpf() {
        if (cpf == null) {
            cpf = "";
        }
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getRgOrgao() {
        if (rgOrgao == null) {
            rgOrgao = "";
        }
        return rgOrgao;
    }

    public void setRgOrgao(String rgOrgao) {
        this.rgOrgao = rgOrgao;
    }

    public String getSexo() {
        if (sexo == null) {
            sexo = "";
        }
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public String getEstadoCivil() {
        if (estadoCivil == null) {
            estadoCivil = "";
        }
        return estadoCivil;
    }

    public void setEstadoCivil(String estadoCivil) {
        this.estadoCivil = estadoCivil;
    }

    public String getProfissao() {
        if (profissao == null) {
            profissao = "";
        }
        return profissao;
    }

    public void setProfissao(String profissao) {
        this.profissao = profissao;
    }

    public String getGrauInstrucao() {
        if (grauInstrucao == null) {
            grauInstrucao = "";
        }
        return grauInstrucao;
    }

    public void setGrauInstrucao(String grauInstrucao) {
        this.grauInstrucao = grauInstrucao;
    }

    public String getNacionalidade() {
        if (nacionalidade == null) {
            nacionalidade = "";
        }
        return nacionalidade;
    }

    public void setNacionalidade(String nacionalidade) {
        this.nacionalidade = nacionalidade;
    }

    public String getNaturalidade() {
        if (naturalidade == null) {
            naturalidade = "";
        }
        return naturalidade;
    }

    public void setNaturalidade(String naturalidade) {
        this.naturalidade = naturalidade;
    }

    public String getCidade() {
        if (cidade == null) {
            cidade = "";
        }
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getUf() {
        if (uf == null) {
            uf = "";
        }
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getRg() {
        if (rg == null) {
            rg = "";
        }
        return rg;
    }

    public void setRg(String rg) {
        this.rg = rg;
    }

    public String getCategoria() {
        if (categoria == null) {
            categoria = "";
        }
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Integer getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public List<TelefoneImportacaoJSON> getTelefones() {
        if (telefones == null) {
            telefones = new ArrayList<>();
        }
        return telefones;
    }

    public void setTelefones(List<TelefoneImportacaoJSON> telefones) {
        this.telefones = telefones;
    }

    public List<EmailImportacaoJSON> getEmails() {
        if (emails == null) {
            emails = new ArrayList<>();
        }
        return emails;
    }

    public void setEmails(List<EmailImportacaoJSON> emails) {
        this.emails = emails;
    }

    public List<EnderecoImportacaoJSON> getEnderecos() {
        if (enderecos == null) {
            enderecos = new ArrayList<>();
        }
        return enderecos;
    }

    public void setEnderecos(List<EnderecoImportacaoJSON> enderecos) {
        this.enderecos = enderecos;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getNomePai() {
        if (nomePai == null) {
            nomePai = "";
        }
        return nomePai;
    }

    public void setNomePai(String nomePai) {
        this.nomePai = nomePai;
    }

    public String getNomeMae() {
        if (nomeMae == null) {
            nomeMae = "";
        }
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getEmpresa() {
        if (empresa == null) {
            empresa = 0;
        }
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getCref() {
        return cref;
    }

    public void setCref(String cref) {
        this.cref = cref;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public List<TipoColaboradorImportacaoJSON> getListaTipoColaborador() {
        if (listaTipoColaborador == null) {
            listaTipoColaborador = new ArrayList<>();
        }
        return listaTipoColaborador;
    }

    public void setListaTipoColaborador(List<TipoColaboradorImportacaoJSON> listaTipoColaborador) {
        this.listaTipoColaborador = listaTipoColaborador;
    }

    public Integer getCodigoUsuario() {
        return codigoUsuario;
    }

    public void setCodigoUsuario(Integer codigoUsuario) {
        this.codigoUsuario = codigoUsuario;
    }

    public String getMsgEnvioEmail() {
        return msgEnvioEmail;
    }

    public void setMsgEnvioEmail(String msgEnvioEmail) {
        this.msgEnvioEmail = msgEnvioEmail;
    }


}
