package importador.json;

import br.com.pactosolucoes.comuns.json.SuperJSON;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 27/12/2019.
 */
public class ProdutoImportacaoJSON extends SuperJSON {

    private Integer idExterno;
    private String descricao;
    private Double valor;
    private String tipo;
    private String categoria;
    private String codigoBarras;
    private boolean ativo = true;
    private Integer vigenciaNrDias;
    private Date vigenciaInicio;
    private Date vigenciaFinal;
    private boolean bloquearAposVigencia = false;
    private String observacao;

    private Integer codigo;
    private boolean sucesso = false;
    private String msgRetorno;

    public Integer getIdExterno() {
        if (idExterno == null) {
            idExterno = 0;
        }
        return idExterno;
    }

    public void setIdExterno(Integer idExterno) {
        this.idExterno = idExterno;
    }

    public String getDescricao() {
        if (descricao == null) {
            descricao = "";
        }
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Double getValor() {
        if (valor == null) {
            valor = 0.0;
        }
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public String getTipo() {
        if (tipo == null) {
            tipo = "";
        }
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getCategoria() {
        if (categoria == null) {
            categoria = "";
        }
        return categoria;
    }

    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }

    public Integer getVigenciaNrDias() {
        return vigenciaNrDias;
    }

    public void setVigenciaNrDias(Integer vigenciaNrDias) {
        this.vigenciaNrDias = vigenciaNrDias;
    }

    public Date getVigenciaInicio() {
        return vigenciaInicio;
    }

    public void setVigenciaInicio(Date vigenciaInicio) {
        this.vigenciaInicio = vigenciaInicio;
    }

    public Date getVigenciaFinal() {
        return vigenciaFinal;
    }

    public void setVigenciaFinal(Date vigenciaFinal) {
        this.vigenciaFinal = vigenciaFinal;
    }

    public String getObservacao() {
        if (observacao == null) {
            observacao = "";
        }
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public boolean isBloquearAposVigencia() {
        return bloquearAposVigencia;
    }

    public void setBloquearAposVigencia(boolean bloquearAposVigencia) {
        this.bloquearAposVigencia = bloquearAposVigencia;
    }

    public boolean isAtivo() {
        return ativo;
    }

    public void setAtivo(boolean ativo) {
        this.ativo = ativo;
    }

    public boolean isSucesso() {
        return sucesso;
    }

    public void setSucesso(boolean sucesso) {
        this.sucesso = sucesso;
    }

    public String getMsgRetorno() {
        if (msgRetorno == null) {
            msgRetorno = "";
        }
        return msgRetorno;
    }

    public void setMsgRetorno(String msgRetorno) {
        this.msgRetorno = msgRetorno;
    }

    public Integer getCodigo() {
        if (codigo == null) {
            codigo = 0;
        }
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public String getCodigoBarras() {
        if (codigoBarras == null) {
            codigoBarras = "";
        }
        return codigoBarras;
    }

    public void setCodigoBarras(String codigoBarras) {
        this.codigoBarras = codigoBarras;
    }

}
