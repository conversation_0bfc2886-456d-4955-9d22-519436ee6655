package importador.financeiro;


import br.com.pactosolucoes.enumeradores.TipoOperacaoLancamento;
import importador.ImportacaoCache;
import importador.ImportacaoConfigTO;
import importador.json.ContaImportacaoJSON;
import negocio.comuns.basico.ConfigExcelImportacaoTO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.FornecedorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.TipoES;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Log;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.basico.Fornecedor;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.MovConta;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class ImportarConta {

    private Connection con;
    private ImportacaoConfigTO configTO;

    public ImportarConta(ImportacaoConfigTO configTO, Connection con) {
        this.con = con;
        this.configTO = configTO;
    }

    public MovContaVO importarConta(ContaImportacaoJSON json, ConfigExcelImportacaoTO configExcelImportacaoTO, ImportacaoCache cache, EmpresaVO empresaVO, CaixaVO caixaVO) throws Exception {
        try {
            con.setAutoCommit(false);

            Uteis.logar(true, null, "INICIA IMPORTAÇÃO | CONTA FINANCEIRO... " + json.getIdExterno() + " | " + json.getDescricao().toUpperCase());

            if (UteisValidacao.emptyNumber(json.getIdExterno())) {
                throw new Exception("Necessário que a conta tenha um IdExterno");
            }

            ResultSet rsExiste = SuperFacadeJDBC.criarConsulta("SELECT codigo FROM movconta WHERE idExterno = '" + json.getIdExterno()
                    + "' and empresa = " + json.getEmpresa(), con);
            if (rsExiste.next()) {
                throw new Exception("Já existe uma CONTA importado com o idExterno " + json.getIdExterno() + " na empresa " + json.getEmpresa());
            }

            MovContaVO movContaVO = new MovContaVO();
            movContaVO.setEmpresaVO(empresaVO);
            movContaVO.setDataLancamento(json.getDataLancamento());
            movContaVO.setDataCompetencia(json.getDataCompetencia() == null ? json.getDataLancamento() : json.getDataCompetencia());
            movContaVO.setDataVencimento(json.getDataVencimento() == null ? json.getDataLancamento() : json.getDataVencimento());
            movContaVO.setDataQuitacao(json.getDataQuitacao());

            PessoaVO pessoaVO = obterPessoaVO(empresaVO, json);
            if (pessoaVO == null || UteisValidacao.emptyNumber(pessoaVO.getCodigo())) {
                throw new Exception("Pessoa não encontrada.");
            }
            movContaVO.setPessoaVO(pessoaVO);

            Double valor = json.getValor();
            movContaVO.setValor(valor < 0.0 ? valor * -1 : valor);

            movContaVO.setContaVO(new ContaVO());
//        movContaVO.getContaVO().setCodigo(json.getConta());

            TipoES tipoES = TipoES.getPorSigla(json.getTipoConta());
            if (tipoES == null) {
                throw new Exception("Tipo conta não encontrado.");
            }

            if (tipoES.equals(TipoES.SAIDA)) {
                movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.PAGAMENTO);
            } else {
                movContaVO.setTipoOperacaoLancamento(TipoOperacaoLancamento.RECEBIMENTO);
            }

            movContaVO.setDescricao(json.getDescricao());

            MovContaRateioVO movContaRateioVO = new MovContaRateioVO();
            movContaRateioVO.setDescricao(movContaVO.getDescricao());
            movContaRateioVO.setValor(movContaVO.getValor());
            movContaRateioVO.setTipoES(tipoES);

            if (movContaVO.getDataQuitacao() != null) {
                movContaVO.setValorPago(movContaVO.getValor());
                movContaRateioVO.setFormaPagamentoVO(cache.obterFormaPagamento(TipoFormaPagto.AVISTA));
            }

            //PlanoContaTO
            PlanoContaTO planoContaTO = cache.obterPlanoContaTODescricao(json.getPlanoConta());
            if (planoContaTO != null && !UteisValidacao.emptyNumber(planoContaTO.getCodigo())) {
                movContaRateioVO.setPlanoContaVO(planoContaTO);
            }

            //CentroCustoTO
            CentroCustoTO centroCustoTO = cache.obterCentroCustoTODescricao(json.getCentroCusto());
            if (centroCustoTO != null && !UteisValidacao.emptyNumber(centroCustoTO.getCodigo())) {
                movContaRateioVO.setCentroCustoVO(centroCustoTO);
            }

            movContaVO.setObservacoes("CONTA IMPORTADA EXCEL \n\n" + json.getObservacao());
            movContaVO.setMovContaRateios(new ArrayList<>());
            movContaVO.getMovContaRateios().add(movContaRateioVO);
            movContaVO.setUsuarioVO(caixaVO.getUsuarioVo());

            MovConta movContaDAO = new MovConta(con);
            movContaDAO.incluirSemCommit(movContaVO, caixaVO.getCodigo(), false, null);
            movContaDAO = null;

            if (UteisValidacao.emptyNumber(movContaVO.getCodigo())) {
                throw new Exception("Erro ao criar conta.");
            }

            SuperFacadeJDBC.executarUpdate("update movconta set idExterno = '" + json.getIdExterno() + "' where codigo = " + movContaVO.getCodigo(), con);

            Log logDao = new Log(con);
            logDao.incluirLogItemImportacao("MOVCONTA", movContaVO.getCodigo(), cache.getUsuarioVOImportacao(), 0);
            logDao = null;

            if (!UteisValidacao.emptyNumber(movContaVO.getCodigo())) {
                json.setSucesso(true);
                json.setCodigo(movContaVO.getCodigo());
                json.setMsgRetorno("Conta importada com sucesso.");
            } else {
                throw new Exception("Conta não importada.");
            }

            con.commit();
            return movContaVO;
        } catch (Exception ex) {
            json.setSucesso(false);
            json.setCodigo(null);
            json.setMsgRetorno(ex.getMessage());
            ex.printStackTrace();
            con.rollback();
            throw ex;
        } finally {
            con.setAutoCommit(true);
        }
    }

    private PessoaVO obterPessoaVO(EmpresaVO empresaVO, ContaImportacaoJSON json) throws Exception {
        Integer codigoPessoa = 0;
        if (!UteisValidacao.emptyString(json.getCpf_CNPJ_SomenteNumeros())) {
            String sql = "select pessoa from fornecedor where cnpj = '" + json.getCpf_CNPJ_Formatado() + "'";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            if (rs.next()) {
                codigoPessoa = rs.getInt("pessoa");
            }
        }

        if (UteisValidacao.emptyNumber(codigoPessoa) && isNotBlank(json.getCpf_CNPJ_Formatado())) {
            String sql = "select codigo from pessoa where cfp = '" + json.getCpf_CNPJ_Formatado() + "'";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            if (rs.next()) {
                codigoPessoa = rs.getInt("codigo");
            }
        }

        if (UteisValidacao.emptyNumber(codigoPessoa) && isNotBlank(json.getPessoa())) {
            String sql = "select codigo from pessoa where upper(nome) = '"+json.getPessoa().toUpperCase()+"'";
            ResultSet rs = SuperFacadeJDBC.criarConsulta(sql, con);
            if (rs.next()) {
                codigoPessoa = rs.getInt("codigo");
            }
        }

        if (UteisValidacao.emptyNumber(codigoPessoa)) {
            PessoaVO pessoaVO = new PessoaVO();
            pessoaVO.setNome(json.getPessoa());
            Pessoa pessoaDAO = new Pessoa(con);
            pessoaDAO.incluirSemComit(pessoaVO);
            pessoaDAO = null;

            FornecedorVO fornecedorVO = new FornecedorVO();
            fornecedorVO.setCnpj(json.getCpf_CNPJ_SomenteNumeros());
            fornecedorVO.setContato(json.getPessoa());
            fornecedorVO.setPessoa(pessoaVO);
            fornecedorVO.setEmpresaVO(empresaVO);
            Fornecedor fornecedorDAO = new Fornecedor(con);
            fornecedorDAO.incluir(fornecedorVO);
            fornecedorDAO = null;
            return pessoaVO;
        }

        PessoaVO pessoaVO = new PessoaVO();
        Pessoa pessoaDAO = new Pessoa(con);
        pessoaVO = pessoaDAO.consultarPorChavePrimaria(codigoPessoa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        pessoaDAO = null;
        return pessoaVO;
    }

    public Connection getCon() {
        return con;
    }

    public void setCon(Connection con) {
        this.con = con;
    }

    public ImportacaoConfigTO getConfigTO() {
        return configTO;
    }

    public void setConfigTO(ImportacaoConfigTO configTO) {
        this.configTO = configTO;
    }
}
