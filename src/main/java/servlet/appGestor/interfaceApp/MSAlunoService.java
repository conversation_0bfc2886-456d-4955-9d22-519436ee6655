package servlet.appGestor.Interface;

import negocio.comuns.arquitetura.PaginadorDTO;
import servlet.appGestor.appGestorDados.AlunoSimplesJSON;
import servlet.appGestor.appGestorDados.VendaDescontoJSON;

import java.sql.Connection;
import java.util.Date;
import java.util.List;

public interface MSAlunoService extends AutoCloseable {

    Connection getCon();

    void setCon(Connection con);

    List<AlunoSimplesJSON> obterContratosCanceladosSql(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> obterClientesComBonusSql(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> obterClientesComFreePassSql(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> consultarInativosPeriodoAcesso(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> consultarOperacoesContratoRetroativas(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, Boolean distinct, String filtro) throws Exception;

    List<AlunoSimplesJSON> contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade,
                                                                            java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim, String operacao,
                                                                            String nomeEmpresa, boolean buscarComAdministrador, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> contarContratoAlteracaoManual(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> contarPagamentoAlteracaoManual(Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> contarPorNomeEntidadePorDataAlteracaoPorOperacao(String nomeEntidade, Date inicio, Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> obterClientesComGymPassSql(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> obterClientesBolsa(Date fim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    String nomeEmpresa(Integer codigoEmpresa) throws Exception;

    List<AlunoSimplesJSON> consultarPorNomeEntidadePorDataAlteracaoPorOperacao(java.sql.Date dataAlteracaoInicial, java.sql.Date dataAlteracaoFim,
                                                                               Integer codEmpresa, boolean buscarComAdministrador, boolean buscarComRecorrencia, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> consultaPorNomeEntidadePorDataAlteracaoPorOperacao(Date dataAlteracaoInicial, Date dataAlteracaoFim,
                                                                              Integer codEmpresa, boolean buscarComAdministrador, PaginadorDTO paginadorDTO, String filtro) throws Exception;


    List<AlunoSimplesJSON> obterClientesComAutorizacaoSemRenovacaoAutomatica(Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, Boolean comAutorizacaoNaoRenovavel, Boolean comAutorizacaoContratoRenovavel, String filtro) throws Exception;

    List<VendaDescontoJSON> obterValorDescontos(Date dataInicio, Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, String filtro) throws Exception;

    List<AlunoSimplesJSON> obterAlunosExcluidos(Date dataInicio, Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, String nomeEntidade, String valorCampoAlterado, String filtro) throws Exception;

    List<AlunoSimplesJSON> obterRenegociacaoParcelas(Date dataInicio, Date dataFim, Integer empresa, PaginadorDTO paginadorDTO, String filtro, String nomeEmpresa) throws Exception;
}
