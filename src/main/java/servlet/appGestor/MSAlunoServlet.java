package servlet.appGestor;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import negocio.comuns.arquitetura.PaginadorDTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import servlet.appGestor.Interface.MSAlunoService;
import servlet.appGestor.appGestorDados.AlunoListToJSON;
import servlet.appGestor.appGestorDados.AlunoSimplesJSON;
import servlet.appGestor.appGestorDados.OperacoesAlunoGestor;
import servlet.appGestor.appGestorDados.VendaDescontoJSON;
import servlet.appGestor.appGestorDados.VendaDescontoListToJSON;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.List;

public class MSAlunoServlet extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();

        try {
            Date inicio = Uteis.obterPrimeiroDiaMes(Calendario.hoje());
            Date dataBase = Calendario.hoje();
            java.sql.Date dataInicio = Uteis.getDataJDBC(inicio);
            java.sql.Date dataFim = Uteis.getDataJDBC(dataBase);
            String filtro = request.getParameter("filtro");
            Boolean dataPadrao = true;
            if (request.getParameter("dataInicio") != null && request.getParameter("dataFim") != null) {
                dataInicio = Uteis.getSQLData(Calendario.converterDataPrimeiraHoraDia(request.getParameter("dataInicio")));
                dataFim = Uteis.getSQLData(Calendario.converterDataUltimaHoraDia(request.getParameter("dataFim")));
                dataPadrao = false;
            }
            Integer empresa = obterParametroInt(request, "empresaId");

            try {
                String key = obterParametroString(request, "key");
                String operacao = obterParametroString(request, "operacao");
                PaginadorDTO paginadorDTO = new PaginadorDTO(request);
                OperacoesAlunoGestor operacaoEnum = OperacoesAlunoGestor.obterOperacao(operacao);
                if (operacaoEnum == null) {
                    throw new Exception("Não foi possível identificar qual operação deve ser realizada.");
                }
                MSAlunoService msAlunoService = DaoAuxiliar.retornarAcessoControle(key).getAppGestorAlunoService();
                AlunoListToJSON alunoListToJSON = new AlunoListToJSON();
                List<AlunoSimplesJSON> alunoSimplesJSON;
                switch (operacaoEnum) {

                    case ESTORNO_CONTRATOS_USUARIO:
                        alunoSimplesJSON = msAlunoService.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(dataInicio, dataFim, empresa, false, false, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case ESTORNOS_CONTRATOS_RECORRENCIA:
                        alunoSimplesJSON = msAlunoService.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(dataInicio, dataFim, empresa, false, true, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case ESTORNOS_CONTRATOS_ADM:
                        alunoSimplesJSON = msAlunoService.consultarPorNomeEntidadePorDataAlteracaoPorOperacao(dataInicio, dataFim, empresa, true, false, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case CONTRATOS_CANCELADOS:
                        alunoSimplesJSON = msAlunoService.obterContratosCanceladosSql(dataInicio, dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case CLIENTES_BONUS:
                        alunoSimplesJSON = msAlunoService.obterClientesComBonusSql(dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case FREEPASS:
                        alunoSimplesJSON = msAlunoService.obterClientesComFreePassSql(dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case INATIVOS_PERIODO_ACESSO:
                        alunoSimplesJSON = msAlunoService.consultarInativosPeriodoAcesso(dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case OPERACOES_RETROATIVAS:
                        alunoSimplesJSON = msAlunoService.consultarOperacoesContratoRetroativas(dataInicio, dataFim, empresa, paginadorDTO, dataPadrao, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case PARCELAS_CANCELADAS:
                        alunoSimplesJSON = msAlunoService.consultaPorNomeEntidadePorDataAlteracaoPorOperacao(dataInicio, dataFim, empresa, false, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case CONTRATOS_DATABASE_ALTERADA:
                        alunoSimplesJSON = msAlunoService.contarContratoAlteracaoManual(dataInicio, dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case PAGAMENTOS_DATABASE_ALTERADA:
                        alunoSimplesJSON = msAlunoService.contarPagamentoAlteracaoManual(dataInicio, dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case EDICOES_PAGAMENTO:
                        alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao("MOVPAGAMENTO", Uteis.getDataJDBC(dataInicio), Uteis.getDataJDBC(dataFim), empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case GYMPASS:
                        alunoSimplesJSON = msAlunoService.obterClientesComGymPassSql(dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case ALUNOS_BOLSA:
                        alunoSimplesJSON = msAlunoService.obterClientesBolsa(dataFim, empresa, paginadorDTO, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case EXCLUSAO_VISITANTES:
                        if (dataPadrao) {
                            alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                                    "CLIENTE - VISITANTE", Uteis.getDataJDBC(inicio),
                                    Uteis.getDataJDBC(Calendario.hoje()),
                                    "EXCLUS%O", msAlunoService.nomeEmpresa(empresa), true, paginadorDTO, filtro);
                        } else {
                            alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                                    "CLIENTE - VISITANTE", Uteis.getDataJDBC(dataInicio),
                                    Uteis.getDataJDBC(dataFim),
                                    "EXCLUS%O", msAlunoService.nomeEmpresa(empresa), true, paginadorDTO, filtro);
                        }
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case CONSULTORES_CONTRATOS_ALTERADOS:
                        if (dataPadrao) {
                            alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                                    "CONTRATO", Uteis.getDataJDBC(inicio),
                                    Uteis.getDataJDBC(Calendario.hoje()),
                                    "ALTERA%O DO CONTRATO%", msAlunoService.nomeEmpresa(empresa), true, paginadorDTO,
                                    filtro);
                        } else {
                            alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                                    "CONTRATO", Uteis.getDataJDBC(dataInicio),
                                    Uteis.getDataJDBC(dataFim),
                                    "ALTERA%O DO CONTRATO%", msAlunoService.nomeEmpresa(empresa), true, paginadorDTO, filtro);
                        }
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;

                    case ESTORNOS_RECIBO:
                        if (dataPadrao) {
                            alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                                    "RECIBOPAGAMENTO", Uteis.getDataJDBC(inicio),
                                    Uteis.getDataJDBC(Calendario.hoje()),
                                    "ESTORNO - RECIBO PAGAMENTO", msAlunoService.nomeEmpresa(empresa), true, paginadorDTO,
                                    filtro);
                        } else {
                            alunoSimplesJSON = msAlunoService.contarPorNomeEntidadePorDataAlteracaoPorOperacao(
                                    "RECIBOPAGAMENTO", Uteis.getDataJDBC(dataInicio),
                                    Uteis.getDataJDBC(dataFim),
                                    "ESTORNO - RECIBO PAGAMENTO", msAlunoService.nomeEmpresa(empresa), true, paginadorDTO, filtro);
                        }
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;
                    case CLIENTES_COM_AUTORIZACAO_SEM_RENOVACAO_AUTOMATICA:
                        alunoSimplesJSON = msAlunoService.obterClientesComAutorizacaoSemRenovacaoAutomatica(dataFim, empresa, paginadorDTO, true, false, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;
                    case CLIENTES_COM_AUTORIZACAO_COM_RENOVACAO_AUTOMATICA:
                        alunoSimplesJSON = msAlunoService.obterClientesComAutorizacaoSemRenovacaoAutomatica(dataFim, empresa, paginadorDTO, false, true, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;
                    case VALOR_DESCONTOS:
                        List<VendaDescontoJSON> vendaDescontoJSON = msAlunoService.obterValorDescontos(dataInicio, dataFim, empresa, paginadorDTO, filtro);
                        VendaDescontoListToJSON vendaDescontoListToJSON = new VendaDescontoListToJSON();
                        vendaDescontoListToJSON.setAlunoList(vendaDescontoJSON);
                        jsonRetorno.put("resultado", vendaDescontoListToJSON.toJSON());
                        break;
                    case ALUNOS_EXCLUIDOS_TREINOWEB:
                        alunoSimplesJSON = msAlunoService.obterAlunosExcluidos(dataInicio, dataFim, empresa, paginadorDTO, "CLIENTESINTETICO", "", filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;
                    case ALUNOS_EXCLUIDOS_BASE_DADOS:
                        String valorCampoAlterado = "Empresa = " + msAlunoService.nomeEmpresa(empresa);
                        alunoSimplesJSON = msAlunoService.obterAlunosExcluidos(dataInicio, dataFim, empresa, paginadorDTO, "CLIENTE", valorCampoAlterado, filtro);
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;
                    case RENEGOCIACAO_PARCELAS:
                        alunoSimplesJSON = msAlunoService.obterRenegociacaoParcelas(dataInicio, dataFim, empresa, paginadorDTO, filtro, msAlunoService.nomeEmpresa(empresa));
                        alunoListToJSON.setAlunoList(alunoSimplesJSON);
                        jsonRetorno.put("resultado", alunoListToJSON.toJSON());
                        break;
//                    case TRANSFERIDOS_COM_CONTRATOS_CANCELADOS:
                    //Ainda não vai ser criado, pois no BI no ZW não existe nome de aluno para esse tipo de relatório
                }
                if (paginadorDTO.getSize() != null && paginadorDTO.getPage() != null) {
                    jsonRetorno.put("page", paginadorDTO.getPage());
                    jsonRetorno.put("size", paginadorDTO.getSize());
                    jsonRetorno.put("totalElements", paginadorDTO.getQuantidadeTotalElementos());
                }
            } catch (Exception e) {

                jsonRetorno.put(STATUS_ERRO, e.getMessage());
            }
            out.println(jsonRetorno);
        } catch (
                Exception e) {
            out.println(e.getMessage());
        }
    }
}

