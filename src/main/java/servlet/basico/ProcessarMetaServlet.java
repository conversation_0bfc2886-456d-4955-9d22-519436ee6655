package servlet.basico;

import br.com.pactosolucoes.atualizadb.processo.ProcessarMetasDoDia;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.crm.AberturaMetaControle;
import negocio.comuns.arquitetura.LogProcessoSistemaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ThreadEnviarEmail;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.utilitarias.Conexao;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ProcessarMetaServlet extends SuperServlet {

    private Connection con;

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        LogProcessoSistemaVO log = null;

        try {
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET,POST");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        if (UteisValidacao.emptyString(chave)) {
            throw new Exception("Chave não informada.");
        }

        con = new DAO().obterConexaoEspecifica(chave);
        Conexao.guardarConexaoForJ2SE(chave, con);
        abrirFecharMetas(con);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        } finally {
            if (con != null) {
                try {
                    con.close();
                    this.con =  null;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }


    public static void abrirFecharMetas(Connection con) throws Exception {
        Conexao.guardarConexaoForJ2SE(con);
        try {
            new AberturaMetaControle().fecharMetas(Uteis.somarDias(Calendario.hoje(), -1));
            new AberturaMetaControle().abrirMetas(Calendario.hoje());
        } catch (Exception ex) {
            Logger.getLogger(ProcessarMetasDoDia.class.getName()).log(Level.SEVERE, null, ex);
        } finally{
            ThreadEnviarEmail.finalmente();
        }
    }
}
