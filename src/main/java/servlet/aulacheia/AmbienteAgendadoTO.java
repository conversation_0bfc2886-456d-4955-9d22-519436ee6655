package servlet.aulacheia;

import negocio.comuns.utilitarias.Calendario;
import org.json.JSONObject;

import java.util.Date;

public class AmbienteAgendadoTO {
    private Integer ambiente;
    private Integer horarioTurma;
    private Date inicio;
    private Date fim;

    public AmbienteAgendadoTO(JSONObject json) {
        this.ambiente = json.getInt("ambiente");
        this.horarioTurma = json.getInt("horarioturma");
        Date dia = new Date(json.getLong("dia"));
        this.inicio = Calendario.getDataComHora(dia, json.getString("horainicial"));
        this.fim = Calendario.getDataComHora(dia, json.getString("horafinal"));
    }

    public Integer getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(Integer ambiente) {
        this.ambiente = ambiente;
    }

    public Integer getHorarioTurma() {
        return horarioTurma;
    }

    public void setHorarioTurma(Integer horarioTurma) {
        this.horarioTurma = horarioTurma;
    }

    public Date getInicio() {
        return inicio;
    }

    public void setInicio(Date inicio) {
        this.inicio = inicio;
    }

    public Date getFim() {
        return fim;
    }

    public void setFim(Date fim) {
        this.fim = fim;
    }
}
