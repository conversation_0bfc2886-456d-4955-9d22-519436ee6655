package servlet.confirmarPresencaAluno;

import br.com.pactosolucoes.enumeradores.OrigemSistemaEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;


public class ConfirmarPresencaAlunoServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String path = request.getServletPath();
        String[] pathParts = path.split("/");
        String recurso = pathParts[3];

        String ctx = request.getParameter("chave");
        Integer empresa = request.getParameter("empresa") == null ? null : Integer.valueOf(request.getParameter("empresa"));

        try {
            Connection c = new DAO().obterConexaoEspecifica(ctx);
            TurmasServiceInterface turmasService = new TurmasServiceImpl(c);
            JSONObject dados = new JSONObject(request.getParameter("dados"));
            Integer horarioTurmaId = dados.has("horarioId") ? dados.optInt("horarioId") : null;
            Integer usuario = dados.has("usuario") ? dados.optInt("usuario") : null;
            Date dia = dados.has("dia") ? Uteis.getDate(dados.optString("dia")) : null;
            OrigemSistemaEnum origem = dados.has("origem") ? OrigemSistemaEnum.getOrigemSistema(dados.getInt("origem")) : null;
            Boolean reposicao = dados.has("reposicao") ? dados.optBoolean("reposicao") : null;
            Boolean desmarcar = dados.has("desmarcar") ? dados.optBoolean("desmarcar") : null;
            Boolean autorizadoGestaoRede = dados.has("autorizadoGestaoRede") ? dados.optBoolean("autorizadoGestaoRede") : false;
            String codAcessoAutorizado = dados.has("codAcessoAutorizado") ? dados.getString("codAcessoAutorizado") : null;
            Integer matriculaAutorizado = dados.has("matriculaAutorizado") ? dados.getInt("matriculaAutorizado") : null;
            Integer codCliente = null;
            Integer codHorarioTurma = null;
            Integer codColaborador = null;
            String retorno = "";
            switch (recurso) {
                case "autorizado":
                    if (!desmarcar && Calendario.maior(dia, Calendario.hoje())) {
                        retorno = "ERRO: Não é permitido marcar presença para datas futuras";
                    } else {
                        Integer autorizado = dados.getInt("autorizado");
                        turmasService.confirmacaoAulaCheiaAutorizado(ctx, autorizado, horarioTurmaId, usuario, dia, desmarcar, origem, autorizadoGestaoRede, codAcessoAutorizado, matriculaAutorizado);
                        retorno = "sucesso";
                    }
                    break;
                case "remover-aula":
                    Integer autorizadoRemover = dados.getInt("autorizado");
                    turmasService.excluirAutorizadoAulaCheia(autorizadoRemover, horarioTurmaId, dia, usuario, autorizadoGestaoRede, codAcessoAutorizado, matriculaAutorizado);
                    retorno = "sucesso";
                    break;
                case "confirmar-todos":
                    if (Calendario.maior(dia, Calendario.hoje())) {
                        retorno = "ERRO: Não é permitido marcar presença para datas futuras";
                    } else {
                        if (dados.getBoolean("isAulaCheia")) {
                            retorno = turmasService.confirmarTodasPresencasAulaCheia(ctx, horarioTurmaId, dia, empresa, usuario, origem, autorizadoGestaoRede, codAcessoAutorizado, matriculaAutorizado);
                        } else {
                            retorno = turmasService.confirmarTodasPresencasTurma(ctx, horarioTurmaId, dia, empresa, usuario, origem);
                        }
                    }
                    break;
                case "presenca":
                    try {
                        JSONObject json = new JSONObject(request.getParameter("dados"));
                        codCliente = json.getInt("codCliente");
                        codHorarioTurma = json.getInt("codHorarioTurma");
                        Date diaAula = Uteis.getDate(json.getString("diaAula"));
                        codColaborador = json.getInt("codColaborador");
                        if (dados.getBoolean("isAulaCheia")) {
                            Boolean confirmar = json.getBoolean("confirmar");
                            turmasService.presencaAulaCheiaAluno(ctx, codCliente, codHorarioTurma, codColaborador, diaAula, origem, confirmar);
                            response.getWriter().append(new JSONObject().put("content", "OK").toString());
                        } else {
                            turmasService.marcarPresenca(ctx, diaAula, codHorarioTurma, codCliente, reposicao, desmarcar, codColaborador, origem);
                            response.getWriter().append(new JSONObject().put("content", "sucesso").toString());
                        }
                    } catch (Exception e) {
                        Logger.getLogger(getClass().getName()).log(Level.SEVERE, null, e);
                        response.getWriter().append(new JSONObject().put("content", "ERRO: " + e.getMessage()).toString());
                    }

                    break;
            }
            response.getWriter().append(new JSONObject().put("content", retorno).toString());
        } catch (Exception e ) {
            System.out.println("Erro na api rest " + recurso + ". Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }
    }



}
