package servlet.estado;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.basico.Empresa;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

public class EstadoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("key");
        int codEmpresa = Integer.valueOf(request.getParameter("empresa"));
        String operacao = request.getParameter("op");

        JSONObject retorno = new JSONObject();

        try (Connection con = new DAO().obterConexaoEspecifica(chave)) {
            Empresa empresaDAO = new Empresa(con);
            switch (operacao) {
                case "sigla":
                    String estadoSigla = empresaDAO.consultarPorChavePrimaria(codEmpresa, Uteis.NIVELMONTARDADOS_TODOS).getEstado().getSigla();
                    retorno.put("sigla", estadoSigla);
                    JSONObject jsonFinal = new JSONObject();
                    jsonFinal.put("return", retorno);
                    response.getWriter().write(jsonFinal.toString());
                    break;
            }

        } catch (Exception e) {
            System.out.println("Erro na api rest operacao " + operacao + "\" -> Message: " + e.getMessage());
            processarErro(e, request, response, null);
        }
    }
}
