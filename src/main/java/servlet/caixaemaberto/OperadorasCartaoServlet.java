package servlet.caixaemaberto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.OperadoraCartaoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.financeiro.OperadoraCartao;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.List;

public class OperadorasCartaoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try{
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"ativo"});

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Boolean ativo = Boolean.parseBoolean(request.getParameter("ativo"));
            ativo = ativo == null ? true : ativo;


            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            OperadoraCartao operadoraCartao = new OperadoraCartao(connection);

            List<OperadoraCartaoVO> operadoraCartaoVOS = operadoraCartao.consultarTodas(ativo, Uteis.NIVELMONTARDADOS_DADOSBASICOS);

            response.getWriter().append(converterOperadorasCartao(operadoraCartaoVOS).toString());

            operadoraCartao = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private JSONArray converterOperadorasCartao(List<OperadoraCartaoVO> operadoraCartaoVOS) {
        JSONArray formasPagemntoJSON = new JSONArray();

        for(OperadoraCartaoVO operadoraCartaoVO: operadoraCartaoVOS){
            formasPagemntoJSON.put(operadoraCartaoVO.toJSON());
        }

        return formasPagemntoJSON;
    }
}
