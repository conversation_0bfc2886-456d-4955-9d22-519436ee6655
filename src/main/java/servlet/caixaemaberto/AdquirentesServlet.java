package servlet.caixaemaberto;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import controle.arquitetura.exceptions.ParametroInvalidoException;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import negocio.comuns.financeiro.AdquirenteVO;
import negocio.comuns.financeiro.FormaPagamentoVO;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Calendario;
import negocio.facade.jdbc.financeiro.Adquirente;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class AdquirentesServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json");

        try{
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"ativo"});

            String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
            Boolean ativo = Boolean.parseBoolean(request.getParameter("ativo"));
            ativo = ativo == null ? true : ativo;


            Connection connection = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(chave, connection);

            Adquirente adquirente = new Adquirente(connection);

            List<AdquirenteVO> adquirenteVOS = adquirente.consultarTodos(ativo);

            response.getWriter().append(converterAdquirentes(adquirenteVOS).toString());

            adquirente = null;
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private JSONArray converterAdquirentes(List<AdquirenteVO> adquirenteVOS) {
        JSONArray formasPagemntoJSON = new JSONArray();

        for(AdquirenteVO adquirenteVO: adquirenteVOS){
            formasPagemntoJSON.put(adquirenteVO.toJSON());
        }

        return formasPagemntoJSON;
    }
}
