/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package servlet.arquitetura;

import org.json.JSONException;
import org.json.JSONObject;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

/**
 *
 * <AUTHOR>
 */
public class MockServlet extends HttpServlet {

    /**
     * Processes requests for both HTTP
     * <code>GET</code> and
     * <code>POST</code> methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    
    private JSONObject onError(JSONObject result, final String msgErro) throws JSONException {
        JSONObject status = new JSONObject();
        status.put("code", "999");
        status.put("description", msgErro);
        result.put("status", status);
        return result;
    }
    
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        try {
            if (request.getParameter("mockcontent") != null) {
                JSONObject m = new JSONObject(Uteis.desencriptar(request.getParameter("mockcontent"), "m0oOCk"));
                if (m.getString("tipoMock").equals("gympass")) {
                    response.setContentType("application/json;charset=UTF-8");
                    /*
                     *  String params = "pass_number=" + getTokenGymPass();
                     params += "&gym_number=" + getEmpresaLogado().getCodigoGymPass();
                     params += "&pass_type_number=" + getTipoGymPass();
                     params += "&auth_token=" + getEmpresaLogado().getTokenApiGymPass();

                     */
                    final String pass_number = m.getString("pass_number");
                    final String gym_number = m.getString("gym_number");
                    final String pass_type_number = m.getString("pass_type_number");
                    final String auth_token = m.getString("auth_token");
                    //
                    final String params = String.format("pass_number=%s&gym_number=%s&pass_type_number=%s&auth_token=%s",
                            pass_number, gym_number, pass_type_number, auth_token);

                    Map<String, String> headers = new HashMap<String, String>();
                    final String urlConsultar = String.format("%s/transacoes/validar_numero.json?%s", 
                            PropsService.getPropertyValue(PropsService.urlAPIGymPass), params);
                    Uteis.logar(null, "GYMPASS (INNER MOCK) Requisição | " + urlConsultar);
                    try {
                        final String retorno = ExecuteRequestHttpService.executeHttpRequestGETEncode(urlConsultar, headers, "UTF-8");
                        Uteis.logar(null, retorno);
                        jsonRetorno = new JSONObject(retorno);
                        Uteis.logar(null, "JSON GYMPASS: " + jsonRetorno);
                        out.println(jsonRetorno.toString());
                    } catch (Exception e) {
                        if (e.getMessage().toLowerCase().contains("timed out") || e.getMessage().toLowerCase().contains("timedout")) {
                            onError(jsonRetorno, "GymPass indisponível no momento. Por favor tente mais tarde.");
                        } else {
                            onError(jsonRetorno, e.getMessage());
                        }
                        out.println(jsonRetorno.toString());
                        Uteis.logar(null, "JSON GYMPASS (ERRO): " + jsonRetorno);
                    }

                } else if (m.getString("tipoMock").equals("requestMock")) {

                    response.setContentType("application/json;charset=UTF-8");

                    Map<String, String> headers = new HashMap<String, String>();

                    JSONObject jsonObject = new JSONObject(m.getString("json"));
                    String urlRequest = jsonObject.getString("urlRequest");
                    String corpo = jsonObject.getString("corpo");
                    String metodo = jsonObject.getString("metodo");
                    String encode = jsonObject.getString("encode");

                    boolean retornoJSON = true;
                    try {
                        retornoJSON = jsonObject.getBoolean("retornoJSON");
                    } catch (Exception e) {
                        retornoJSON = true;
                    }

                    JSONObject headersJSON = new JSONObject(jsonObject.getString("headers"));
                    for (int e = 0; e < headersJSON.length(); e++) {
                        String obj = (String) headersJSON.names().get(e);
                        headers.put(obj, headersJSON.getString(obj));
                    }

                    if (UteisValidacao.emptyString(corpo)) {
                        corpo = null;
                    }

                    try {
                        final String retorno = ExecuteRequestHttpService.executeHttpRequest(urlRequest, corpo, headers, metodo, encode);
                        Uteis.logar(null, "RETORNO REQUEST MOCK: " + retorno);
                        if (retornoJSON) {
                            jsonRetorno = new JSONObject(retorno);
                            Uteis.logar(null, "JSON REQUEST MOCK: " + jsonRetorno);
                            out.println(jsonRetorno.toString());
                        } else {
                            Uteis.logar(null, "REQUEST MOCK SEM RETORNO JSON: " + retorno);
                            out.println(retorno);
                        }
                    } catch (Exception e) {
                        out.println(e.getMessage());
                        Uteis.logar(null, "JSON REQUEST MOCK (ERRO): " + e.getMessage());
                    }
                } else if (m.getString("tipoMock").equals("requestMockStone")) {

                    response.setContentType("application/json;charset=UTF-8");

                    Map<String, String> headers = new HashMap<String, String>();

                    JSONObject jsonObject = m.getJSONObject("json");
                    String urlRequest = jsonObject.getString("urlRequest");
                    String corpo = jsonObject.getString("corpo");
                    String metodo = jsonObject.getString("metodo");
                    String encode = jsonObject.getString("encode");

                    boolean retornoJSON = true;
                    try {
                        retornoJSON = jsonObject.getBoolean("retornoJSON");
                    } catch (Exception e) {
                        retornoJSON = true;
                    }

                    JSONObject headersJSON = jsonObject.getJSONObject("headers");
                    for (int e = 0; e < headersJSON.length(); e++) {
                        String obj = (String) headersJSON.names().get(e);
                        headers.put(obj, headersJSON.getString(obj));
                    }

                    if (UteisValidacao.emptyString(corpo)) {
                        corpo = null;
                    }

                    try {
                        ExecuteRequestHttpService executeRequestHttpService = new ExecuteRequestHttpService();
                        executeRequestHttpService.connectTimeout = 10000; //10 segundos
                        executeRequestHttpService.readTimeout = 60000; //60 segundos
                        final String retorno = executeRequestHttpService.executeHttpRequest(urlRequest, corpo, headers, metodo, encode);
                        Uteis.logar(null, "RETORNO REQUEST MOCK: " + retorno);
                        if (retornoJSON) {
                            jsonRetorno = new JSONObject(retorno);
                            Uteis.logar(null, "JSON REQUEST MOCK: " + jsonRetorno);
                            out.println(jsonRetorno.toString());
                        } else {
                            Uteis.logar(null, "REQUEST MOCK SEM RETORNO JSON: " + retorno);
                            out.println(retorno);
                        }
                    } catch (Exception e) {
                        out.println(e.getMessage());
                        Uteis.logar(null, "JSON REQUEST MOCK (ERRO): " + e.getMessage());
                    }
                } else {
                    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                }
            } else if (request.getParameter("status").equals("true")) {
                JSONObject o = new JSONObject();
                o.put("status", new Date().toString());
                out.println(o.toString());
            }
        } catch (Exception e) {
            try {
                onError(jsonRetorno, e.getMessage());
            } catch (JSONException ex) {
                Logger.getLogger(MockServlet.class.getName()).log(Level.SEVERE, null, ex);
            }
            out.println(jsonRetorno.toString());
        } finally {
            out.close();
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP
     * <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP
     * <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>
}
