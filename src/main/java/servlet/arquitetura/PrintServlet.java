package servlet.arquitetura;

import br.com.pactosolucoes.comuns.util.JSFUtilities;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

public class PrintServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        
        processRequestAPI(request, response);        
        
        JSONObject resultado = new JSONObject();
        JSONArray resultadoImagens = new JSONArray();
        JSONArray resultadoHtmls = new JSONArray();
        resultado.put("imagens", resultadoImagens);
        resultado.put("htmls", resultadoHtmls);

        try {

            String requestBody = IOUtils.toString(request.getInputStream());
            JSONObject payload = new JSONObject(requestBody);
            JSONArray imagens = new JSONArray(payload.getString("imagens"));
            JSONArray htmls = new JSONArray(payload.getString("htmls"));

            for (int i = 0; i < imagens.length(); i++) {
                JSONObject imagem = imagens.getJSONObject(i);

                byte[] arquivo = converterImagemBase64ParaArquivo(imagem.getString("imagem"));
                String urlImagem = PropsService.getPropertyValue(PropsService.urlFotosNuvem) + "/";
                urlImagem += MidiaService.getInstance()
                            .uploadObjectFromByteArray(JSFUtilities.getChave(request),
                                    MidiaEntidadeEnum.PRINT_ACOES_USUARIO,
                                    imagem.getString("nome"),
                                    arquivo,
                                    false
                            );
                resultadoImagens.put(urlImagem);
            }

            for (int i = 0; i < htmls.length(); i++) {
                JSONObject htmlObject = htmls.getJSONObject(i);
                String htmlString = htmlObject.getString("html");

                String urlArquivo = PropsService.getPropertyValue(PropsService.urlFotosNuvem) + "/";
                urlArquivo += MidiaService.getInstance()
                        .uploadObjectFromByteArray(getChave(),
                                MidiaEntidadeEnum.PRINT_ACOES_USUARIO,
                                htmlObject.getString("nome"),
                                htmlString.getBytes(),
                                false
                        );
                resultadoHtmls.put(urlArquivo);
            }

            resultado.put("sucesso", true);
            response.getWriter().append(resultado.toString());
        } catch (Exception e) {
            processarErro(e, request, response, getFormatoPayload());
        }
    }

    private byte[] converterImagemBase64ParaArquivo(String base64){
        String[] parts = base64.split(",");
        return Base64.decodeBase64(parts[1]);
    }

    private JSONObject getFormatoPayload(){
        JSONObject payload = new JSONObject();

        JSONArray imagens = new JSONArray();
        JSONObject imagem = new JSONObject();

        imagem.put("imagem", "<codigo da imagem em base64>");
        imagem.put("nome", "<nome que será salva a imagem>");
        imagens.put(imagem);

        payload.put("imagens", imagens);

        return payload;
    }
}
