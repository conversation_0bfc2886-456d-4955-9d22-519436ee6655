package servlet.arquitetura;

import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 22/06/2020
 */
public class AtualizaDBServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String key = request.getParameter("chave");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            try (Connection con = new DAO().obterConexaoEspecifica(key)) {

                SuperControle superControle = new SuperControle();
                Conexao.guardarConexaoForJ2SE(con);
                AtualizadorBD atualizador = new AtualizadorBD(con);
                Integer versaoAnteriorVoltar = atualizador.obterVersaoAtual();

                Integer versaoVoltar = null;
                try {
                    //voltar a versão do banco
                    if (request.getParameter("versaovoltar") != null) {
                        versaoVoltar = UteisValidacao.converterInteiro(request.getParameter("versaovoltar"));
                        if (!UteisValidacao.emptyNumber(versaoVoltar)) {
                            SuperFacadeJDBC.executarUpdate("update bdversaoatual set versao = " + versaoVoltar, con);
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                Integer versaoAnterior = atualizador.obterVersaoAtual();

                String msg = "";
                try {
                    atualizador.atualizar(superControle.getVersaoBD(), 1);
                    msg = "Atualização executada com sucesso";
                } catch (Exception ex) {
                    if (ex.getMessage().toLowerCase().contains("versão corrente do banco de dados do sistema já é a especificada")) {
                        msg = ex.getMessage();
                    } else {
                        throw ex;
                    }
                }
                Integer versaoAtual = atualizador.obterVersaoAtual();

                JSONObject retorno = new JSONObject();
                retorno.put("nomeDB", con.getCatalog());
                retorno.put("msg", msg);
                retorno.put("versaoSuperControle", superControle.getVersaoBD());
                retorno.put("versaoAnterior", versaoAnterior);
                retorno.put("versaoAtual", versaoAtual);
                if (!UteisValidacao.emptyNumber(versaoVoltar)) {
                    retorno.put("versaoVoltar", versaoVoltar);
                    retorno.put("versaoAnteriorVoltar", versaoAnteriorVoltar);
                }
                response.getWriter().append(this.toJSON(true, retorno).toString());
            }
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        }
    }

    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }
}
