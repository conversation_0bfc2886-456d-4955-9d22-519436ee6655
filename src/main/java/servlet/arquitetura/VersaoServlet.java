package servlet.arquitetura;

import br.com.pactosolucoes.atualizadb.negocio.AtualizadorBD;
import br.com.pactosolucoes.enumeradores.SinalizadorEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.arquitetura.SuperControle;
import negocio.facade.jdbc.basico.SinalizadorSistema;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

public class VersaoServlet extends SuperServlet {
    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");

        String method = request.getMethod();
        try {
            switch (method) {
                case "GET":
                    consultarDbVersao(request, response, chave);
                    break;
                case "POST":
                    Connection c = new DAO().obterConexaoEspecifica(chave);
                    Conexao.guardarConexaoForJ2SE(c);
                    SuperControle superControle = new SuperControle();
                    AtualizadorBD atualizador = new AtualizadorBD(c, true);
                    SinalizadorSistema sinalizadorSistema = new SinalizadorSistema(c);
                    List<String> atualizacoes = atualizador.atualizar(superControle.getVersaoBD(), 1);
                    sinalizadorSistema.deletarPorBI(SinalizadorEnum.BDATUALIZANDO);
                    JSONObject result = new JSONObject();
                    result.put("versoes", atualizacoes);
                    response.getWriter().append(result.toString());
                    break;
                default:
                    processarErro(
                            new UnsupportedOperationException("Methodo não suportado para esse recurso"),
                            request,
                            response,
                            null);
            }
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private void consultarDbVersao(HttpServletRequest request, HttpServletResponse response, String chave) throws Exception {
        JSONObject result = new JSONObject();
        SuperControle superControle = new SuperControle();

        result.put("versaoAtual", getAtualizadorBD(chave).obterVersaoAtual());
        result.put("ultimaVersao", superControle.getVersaoBD());

        response.getWriter().append(result.toString());
    }

    private AtualizadorBD getAtualizadorBD(String chave) throws Exception {
        return new AtualizadorBD(new DAO().obterConexaoEspecifica(chave));
    }
}
