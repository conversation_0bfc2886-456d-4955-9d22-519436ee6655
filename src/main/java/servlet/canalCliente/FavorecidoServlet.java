package servlet.canalCliente;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import negocio.comuns.arquitetura.BI.FiltroBiVO;
import negocio.comuns.arquitetura.BI.RankingMetaDiariaVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.crm.MetaCRMTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.BI.FiltroBi;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.crm.FecharMeta;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;
import servlet.crm.FiltroMetaDiariaJSON;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class FavorecidoServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json;charset=UTF-8");

        try {
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa"});
        } catch (Exception e) {
            processarErro(e, request, response, null);
            return;
        }

        String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
        try (Connection connection = new DAO().obterConexaoEspecifica(chave)){
            Integer empresa = Uteis.converterInteiro(request.getParameter("empresa"), 1);
            String valorConsulta = request.getParameter("valorConsulta");
            Integer limite = Uteis.converterInteiro(request.getParameter("limite"), 0);

            Pessoa pessoa = new Pessoa(connection);
            List<PessoaVO> pessoaVOS = pessoa.consultarPorNomePessoaComLimiteFinanceiro(empresa, valorConsulta, false,limite, false, Uteis.NIVELMONTARDADOS_TIPOPESSOA);
            JSONArray objs = new JSONArray();
            for(PessoaVO pessoaVO : pessoaVOS){
                JSONObject obj = new JSONObject();
                obj.put("codigo",pessoaVO.getCodigo());
                obj.put("cnpj",pessoaVO.getCnpj());
                obj.put("cpf",pessoaVO.getCfp());
                obj.put("nome",pessoaVO.getNome());
                obj.put("tipo",pessoaVO.getTipoPessoa());
                objs.put(obj);
            }

            response.getWriter().append(objs.toString());
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }
}
