package servlet.cobranca;

import br.com.pactosolucoes.agendatotal.json.AgendaTotalJSON;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.vendas.*;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: William Douglas Lacerda Guimarães
 * Date: 06/12/2021
 */
public class HotSiteServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        Connection con = null;
        try {
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.addHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setContentType("application/json");

            String method = request.getMethod();

            if (!method.equalsIgnoreCase("POST")) {
                throw new Exception("Método não suportado.");
            }

            String key = request.getParameter("key");
            if (UteisValidacao.emptyString(key)) {
                throw new Exception("Chave não informada.");
            }

            OperacaoVendasOnlineDTO operacaoDTO = null;

            try {
                InputStream inputStream = request.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));

                StringBuffer body = new StringBuffer();
                String line = null;

                while ((line = reader.readLine()) != null) {
                    body.append(line);
                }

                operacaoDTO = new OperacaoVendasOnlineDTO(new JSONObject(body.toString()));
            } catch (Exception ex) {
                ex.printStackTrace();
                throw new Exception("Erro body: " + ex.getMessage());
            }

            con = new DAO().obterConexaoEspecifica(key);

            Object objRetorno = null;
            switch (operacaoDTO.getOperacao()) {
                case "menu":
                    objRetorno = consultarMenu(operacaoDTO, con);
                    break;
                case "bannerInicial":
                    objRetorno = consultarBannerInicial(operacaoDTO, con);
                    break;
                case "fotoFachada":
                    objRetorno = consultarFotoFachada(operacaoDTO, con);
                    break;
                case "carroselModalidade":
                    objRetorno = consultarCarroselModalidade(operacaoDTO, con);
                    break;
                case "configCarroselModalidade":
                    objRetorno = consultarConfigCarroselModalidade(operacaoDTO, con);
                    break;
                case "detalheModalidade":
                    objRetorno = consultarDetalheModalidade(operacaoDTO, con);
                    break;
                case "plano":
                    objRetorno = consultarPlanos(operacaoDTO, con);
                    break;
                case "contato":
                    objRetorno = consultarContatos(operacaoDTO, con);
                    break;
                case "multiEmpresaConfig":
                    objRetorno = consultarMultiempresa(operacaoDTO, con);
                    break;
                case "aulasProduto":
                    objRetorno = aulasProduto(operacaoDTO, con);
                    break;
                case "existeaulasProduto":
                    objRetorno = existeAulasProduto(operacaoDTO, con);
                    break;
                case "lead":
                    objRetorno = leads(operacaoDTO, con);
                    break;
                case "empresaZw":
                    objRetorno = obterUnidadesMesmoBancoZw(con);
                    break;
                case "empresahotsite":
                    objRetorno = consutarEmpresaHotsite(con);
                    break;
                case "parametrosGerais":
                    objRetorno = consultarParametrosGerais(operacaoDTO, con);
                    break;
                default:
                    throw new Exception("Nenhuma operação executada");
            }

            if (objRetorno == null) {
                throw new Exception("Nenhuma operação executada");
            }

            response.getWriter().append(this.toJSON(true, objRetorno).toString());
        } catch (Exception ex) {
            response.setStatus(400);
            response.getWriter().append(this.toJSON(false, ex.getMessage()).toString());
        } finally {
            if (con != null) {
                try {
                    con.close();
                } catch (Exception ex) {
                }
            }
        }
    }

    private Object consutarEmpresaHotsite(Connection con) {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<EmpresaHotsiteVO>  list = config.consultarEmpresasHotsite();
            list.stream().forEach(m -> listaDTO.put(new JSONObject(m)));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarMultiempresa(OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        VendasConfig config = null;
        JSONObject retorno = null;
        try {
            config = new VendasConfig(con);
            MultiEmpresaConfigsVendasOnlineVO menu = config.consultarMultiEmpresaCongifsVendasOnline();
            retorno = new JSONObject(menu);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            retorno = new JSONObject().put("ERRO", ex.getMessage());
        } finally {
            config = null;
        }
        return retorno;
    }

    private Object consultarMenu(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONObject retorno = null;
        try {
            config = new VendasConfig(con);
            MenuVendasOnlineVO menu = config.consultarMenuVendasOnlineEmpresa(operacaoDTO.getCodigo());
            retorno = new JSONObject(menu);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            retorno = new JSONObject().put("ERRO", ex.getMessage());
        } finally {
            config = null;
        }
        return retorno;
    }
    private Object consultarFotoFachada(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<FotoFachadaVendasOnlineVO> listaFach = config.consultarFotoFachadaVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            listaFach.stream().forEach(m -> listaDTO.put(new JSONObject(m)));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarBannerInicial(OperacaoVendasOnlineDTO operacaoDTO, Connection con) {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<PaginaInicialVendasOnlineVO> listaBannerInicial = config.consultarPaginaInicialVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            listaBannerInicial.stream().forEach(m -> listaDTO.put(new JSONObject(m)));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarCarroselModalidade(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<ModalidadeCarrouselVendasOnlineVO> modalidades = config.consultarModalidadesCarrouselVendasOnlinePorEmpresa(operacaoDTO.getCodigo(), false);
            modalidades.stream().forEach(m -> listaDTO.put(new JSONObject(m)));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarConfigCarroselModalidade(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            ConfigModalidadeCarrosselVendasOnlineVO configModalidadeCarrossel = config.consultarConfigModalidadesCarrosselVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            listaDTO.put(new JSONObject(configModalidadeCarrossel));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarDetalheModalidade(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<ModalidadeCarrouselVendasOnlineVO> modalidades = config.consultarModalidadesCarrouselVendasOnlinePorEmpresa(operacaoDTO.getCodigo(), true);
            modalidades.stream().forEach(m -> listaDTO.put(new JSONObject(m)));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarPlanos(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<PlanosSiteVendasOnlineVO> planos =  config.consultarPlanosSiteVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            planos.stream().forEach(m -> listaDTO.put(new JSONObject(m)));
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private Object consultarContatos(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONObject retorno = null;
        try {
            config = new VendasConfig(con);
            ContatoRodapeVendasOnlineVO contato = config.consultarContatoRodapeVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            retorno = new JSONObject(contato);
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            retorno = new JSONObject().put("ERRO", ex.getMessage());
        } finally {
            config = null;
        }
        return retorno;
    }

    private Object aulasProduto(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        List<AgendaTotalJSON> list = new ArrayList<>();
        List<AgendaTotalJSON> retorno = new ArrayList<>();

        try {
            config = new VendasConfig(con);

            Date dataInicial = Calendario.getDataComHoraZerada(Uteis.getDate(operacaoDTO.getDataCorteTurma()));
            Date dataFinal = Calendario.getDataComHora(Uteis.getDate(operacaoDTO.getDataCorteTurma()), "23:59:59");
            List<AulasVendasOnline>  objs = config.consultarAulasVendasOnlineAtivaPorEmpresa(operacaoDTO.getCodigo());
            list = config.produtosAgendaAulaEmpresa(operacaoDTO.getCodigo(), dataInicial, dataFinal, con);

            for(int i=0; i<=objs.size()-1; i++) {
                for(int x=0; x<=list.size()-1; x++)
                    if (list.get(x).getCodigoProduto().equals(objs.get(i).getCodigoProduto()) && list.get(x).getCodigoTurma().equals(objs.get(i).getCodigoTurma())) {
                        retorno.add(list.get(x));
                    }
            }

            //Valida se já existe alguma outra aula já marcada no MESMO local e no MESMO horário
            //Caso existir, ela ficará "opaca" e não poderá ser selecionada no front
            existeOutraAulaMesmoHorarioEAmbiente(retorno);

        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            throw ex;
        } finally {
            config = null;
        }
        return retorno;
    }

    private void existeOutraAulaMesmoHorarioEAmbiente(List<AgendaTotalJSON> retorno) {
        for (AgendaTotalJSON aula : retorno) {
            for (AgendaTotalJSON aulaLista : retorno) {
                if (aula != aulaLista && aula.getCodigoLocal().equals(aulaLista.getCodigoLocal()) && aula.getInicio().equals(aulaLista.getInicio())) {
                    if (aulaLista.getNrVagas().equals(aulaLista.getNrVagasPreenchidas())) {
                        aula.setExisteOutraAulaMarcadaMesmoHorarioEAmbiente(true);
                        break;
                    }
                }
            }
        }
    }

    private Object existeAulasProduto(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;

        try {
            config = new VendasConfig(con);

            List<AulasVendasOnline>  objs = config.consultarAulasVendasOnlineAtivaPorEmpresa(operacaoDTO.getCodigo());

            if(objs.size()>0){
                return true;
            }
            return false;

        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            throw ex;
        } finally {
            config = null;
        }
    }

    private Object leads(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONObject retorno = null;
        try {
            config = new VendasConfig(con);

            CaptacaoLeadsVendasOnlineVO contato = config.consultarCapLeadsVendasOnlineApi(operacaoDTO.getCodigo());
            retorno = new JSONObject(contato);

        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            throw ex;
        } finally {
            config = null;
        }
        return retorno;
    }

    private Object obterUnidadesMesmoBancoZw(Connection con) throws Exception {
        Empresa config = null;
        JSONObject json = new JSONObject();
        try {
            config = new Empresa(con);
            List<EmpresaVO> empresasa = config.consultarEmpresas();
            json =  toJSONEMP(true , empresasa.get(0).getCodigo(), empresasa.get(0).getRazaoSocial());
        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            throw ex;
        } finally {
            config = null;
        }
        return json;
    }

    private Object consultarParametrosGerais(OperacaoVendasOnlineDTO operacaoDTO, Connection con) throws Exception {
        VendasConfig config = null;
        JSONArray listaDTO = new JSONArray();
        try {
            config = new VendasConfig(con);
            List<FotoFachadaVendasOnlineVO> listaFach = config.consultarFotoFachadaVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            listaFach.stream().forEach(m -> {
                JSONObject obj = new JSONObject(m);
                obj.put("tipo", "fotoFachada");
                listaDTO.put(obj);
            });

            List<PlanosSiteVendasOnlineVO> planos =  config.consultarPlanosSiteVendasOnlinePorEmpresa(operacaoDTO.getCodigo());
            planos.stream().forEach(m -> {
                JSONObject obj = new JSONObject(m);
                obj.put("tipo", "plano");
                listaDTO.put(obj);
            });

            List<ModalidadeCarrouselVendasOnlineVO> modalidades = config.consultarModalidadesCarrouselVendasOnlinePorEmpresa(operacaoDTO.getCodigo(), false);
            modalidades.stream().forEach(m -> {
                JSONObject obj = new JSONObject(m);
                obj.put("tipo", "carroselModalidade");
                listaDTO.put(obj);
            });

            List<ModalidadeCarrouselVendasOnlineVO> modalidadesDetalhes = config.consultarModalidadesCarrouselVendasOnlinePorEmpresa(operacaoDTO.getCodigo(), true);
            modalidades.stream().forEach(m -> {
                JSONObject obj = new JSONObject(m);
                obj.put("tipo", "detalheModalidade");
                listaDTO.put(obj);
            });

        } catch (Exception ex) {
            Uteis.logar(ex, this.getClass());
            listaDTO.put(new JSONObject().put("ERRO", ex.getMessage()));
        } finally {
            config = null;
        }
        return listaDTO;
    }

    private JSONObject toJSONEMP(boolean sucesso, Object dados, String empresa) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        json.put("empresa", empresa);
        return json;
    }
    private JSONObject toJSON(boolean sucesso, Object dados) {
        JSONObject json = new JSONObject();
        json.put("sucesso", sucesso);
        json.put("dados", dados);
        return json;
    }
}
