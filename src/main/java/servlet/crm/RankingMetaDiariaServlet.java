package servlet.crm;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.common.net.HttpHeaders;
import com.sun.xml.fastinfoset.stax.events.Util;
import controle.arquitetura.exceptions.ParametroObrigatorioException;
import negocio.comuns.arquitetura.BI.FiltroBiVO;
import negocio.comuns.arquitetura.BI.RankingMetaDiariaVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.crm.MetaCRMTO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.arquitetura.BI.FiltroBi;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Colaborador;
import negocio.facade.jdbc.crm.FecharMeta;
import org.json.JSONArray;
import org.json.JSONObject;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class RankingMetaDiariaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Authorization, content-type, xsrf-token");
        response.addHeader("Access-Control-Expose-Headers", "xsrf-token");
        response.setContentType("application/json;charset=UTF-8");

        try {
            validarAuthorizationComChave(request);
            validarParametrosObrigatorios(request, new String[]{"empresa", "dataInicio", "dataFim"});
        } catch (Exception e) {
            processarErro(e, request, response, null);
            return;
        }

        String chave = request.getHeader(HttpHeaders.AUTHORIZATION);
        try (Connection connection = new DAO().obterConexaoEspecifica(chave)){
            Integer empresa = Uteis.converterInteiro(request.getParameter("empresa"), 1);
            Date dataInicio = Calendario.converterDataPrimeiraHoraDia(request.getParameter("dataInicio"));
            Date dataFim = Calendario.converterDataUltimaHoraDia(request.getParameter("dataFim"));
            boolean reloadFull = Boolean.parseBoolean(request.getParameter("reloadFull"));
            String codColaboradores = request.getParameter("colaboradores");
            boolean somenteColaboradoresAtivos = Util.isEmptyString(request.getParameter("somenteColaboradoresAtivos")) ? true : Boolean.parseBoolean(request.getParameter("somenteColaboradoresAtivos"));
            List<Integer> codColaboradoresList = new ArrayList<>();
            List<UsuarioVO> usuarioVOS = new ArrayList<>();
            Usuario usuario = new Usuario(connection);

            if (!Util.isEmptyString(codColaboradores)) {
                String[] codColaboradoresArray = codColaboradores.split(",");
                for (String codColaborador : codColaboradoresArray) {
                    if (!codColaborador.isEmpty()) {
                        UsuarioVO usuarioVO = usuario.consultarPorCodigoColaborador(Integer.parseInt(codColaborador), Uteis.NIVELMONTARDADOS_MINIMOS);
                        if (usuarioVO.getCodigo() == 0) {
                            throw new ParametroObrigatorioException("Não foi possível encontrar o usuário do colaborador " + codColaborador + ".");
                        }
                        usuarioVOS.add(usuarioVO);
                        codColaboradoresList.add(Integer.parseInt(codColaborador));
                    }
                }
            } else {
                usuarioVOS.addAll(usuario.consultarPorSituacaoColaborador("AT", false, Uteis.NIVELMONTARDADOS_MINIMOS));
                if (!somenteColaboradoresAtivos) {
                    usuarioVOS.addAll(usuario.consultarPorSituacaoColaborador("NA", false, Uteis.NIVELMONTARDADOS_MINIMOS));
                }
                List<Integer> codigos = usuarioVOS.stream()
                        .mapToInt(UsuarioVO::getCodigo)
                        .boxed()
                        .collect(Collectors.toList());

                codColaboradoresList.addAll(codigos);
            }

            validarParametroDatas(dataInicio, dataFim);

            FiltroBi filtroBi = new FiltroBi(connection);
            FiltroMetaDiariaJSON filtroJSON = new FiltroMetaDiariaJSON(empresa, dataInicio.getTime(), dataFim.getTime(), codColaboradoresList);
            FiltroBiVO filtroBiVO = filtroBi.obterPorToken(filtroJSON.getToken(), "RANKING_META_DIARIA");
            FecharMeta servico = new FecharMeta(connection);
            List<RankingMetaDiariaVO> rankingMetaDiariaVOS = new ArrayList<>();

            if (filtroBiVO == null || reloadFull) {
                Colaborador colaborador = new Colaborador(connection);
                if (filtroBiVO != null) {
                    filtroBi.excluir(filtroBiVO.getTokenFiltro());
                }
                for (UsuarioVO usuarioVO : usuarioVOS) {
                    List<MetaCRMTO> metas = getMetas(empresa, dataInicio, dataFim, servico, usuarioVO);
                    JSONObject jsonObject = converterListaMetaJson(metas);
                    RankingMetaDiariaVO rankingMetaDiariaVO = new RankingMetaDiariaVO();
                    ColaboradorVO colaboradorVO = colaborador.consultarPorCodigoUsuario(usuarioVO.getCodigo(), empresa, false, Uteis.NIVELMONTARDADOS_MINIMOS);
                    rankingMetaDiariaVO.setCodigoColaborador(colaboradorVO.getCodigo());
                    rankingMetaDiariaVO.setNomeColaborador(colaboradorVO.getPessoa().getNome());
                    rankingMetaDiariaVO.setPercentualRealizado(jsonObject.getDouble("percentualRealizado"));
                    rankingMetaDiariaVO.setTotalPrevisto(jsonObject.getInt("totalPrevisto"));
                    rankingMetaDiariaVO.setTotalRealizado(jsonObject.getInt("totalRealizado"));

                    rankingMetaDiariaVOS.add(rankingMetaDiariaVO);
                }

                rankingMetaDiariaVOS.sort((rmd1, rmd2) ->
                        Double.compare(rmd2.getPercentualRealizado(), rmd1.getPercentualRealizado())
                );


                int posicao = 1;
                for (RankingMetaDiariaVO rankingMetaDiariaVO : rankingMetaDiariaVOS) {
                    rankingMetaDiariaVO.setPosicao(posicao);
                    posicao++;
                }

                filtroBiVO = filtroBi.incluir("RANKING_META_DIARIA", filtroJSON, converterListaRankingJson(rankingMetaDiariaVOS));
            }

            response.getWriter().append(filtroBiVO.toJSON().toString());
        } catch (Exception e) {
            processarErro(e, request, response, null);
        }
    }

    private List<MetaCRMTO> getMetas(Integer empresa, Date dataInicio, Date dataFim, FecharMeta servico, UsuarioVO usuarioVO) throws Exception {
        List<UsuarioVO> usuarioVOS = new ArrayList<>();
        usuarioVOS.add(usuarioVO);
        return servico.consultarMeta(dataInicio, dataFim, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA, usuarioVOS, empresa, true);
    }

    private JSONObject converterListaRankingJson(List<RankingMetaDiariaVO> rankingMetaDiariaVOS) {
        JSONObject jsonObject = new JSONObject();

        for (RankingMetaDiariaVO rankingMetaDiariaVO : rankingMetaDiariaVOS) {
            jsonObject.put(rankingMetaDiariaVO.getNomeColaborador(), rankingMetaDiariaVO.toString());
        }

        return jsonObject;
    }

    private void validarParametroDatas(Date dataInicio, Date dataFim) throws ParametroObrigatorioException {
        Integer periodoConsulta = Calendario.diferencaEmDias(dataInicio, dataFim);
        if (periodoConsulta > 31) {
            throw new ParametroObrigatorioException("O período máximo permitido de consulta é de 31 dias. Sua consulta contém " + periodoConsulta + " dias.");
        }
    }

    private JSONObject converterListaMetaJson(List<MetaCRMTO> metas) {
        JSONObject metaJSON = new JSONObject();
        JSONArray listaMetaJSON = new JSONArray();

        Double totalPrevisto = 0.0;
        Double totalRealizado = 0.0;

        for (MetaCRMTO meta : metas) {
            listaMetaJSON.put(meta.toJson(meta));
            totalPrevisto += meta.getTotalMeta();
            totalRealizado += meta.getTotalMetaRealizada();
        }

        metaJSON.put("metas", listaMetaJSON);
        metaJSON.put("totalPrevisto", totalPrevisto);
        metaJSON.put("totalRealizado", totalRealizado);
        metaJSON.put("percentualRealizado", Uteis.arrendondar(totalRealizado > 0.0 ? (totalRealizado / totalPrevisto) * 100 : 0.0));

        return metaJSON;
    }
}
