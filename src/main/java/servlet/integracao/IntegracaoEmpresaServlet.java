package servlet.integracao;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.EnvelopeRespostaDTO;
import negocio.comuns.basico.EmpresaConfigEstacionamentoVO;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.adm.IntegracaoEstacionamento;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
public class IntegracaoEmpresaServlet extends SuperServlet {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET,POST");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setContentType("application/json");

            String operacao = request.getParameter("op");
            if (UteisValidacao.emptyString(operacao)) {
                throw new Exception("operacao 'op' não informada na url.");
            }

            String chave = request.getParameter("key");
            if (UteisValidacao.emptyString(chave)) {
                throw new Exception("Chave não informada na url.");
            }

            if (operacao.equalsIgnoreCase("estacionamento")) {
                JSONObject json = new JSONObject(obterBody(request));

                EmpresaConfigEstacionamentoVO empresaConfigEstacionamentoVO = new EmpresaConfigEstacionamentoVO();
                empresaConfigEstacionamentoVO.setHost(json.optString("ftpHost"));
                empresaConfigEstacionamentoVO.setUser(json.optString("ftpUser"));
                empresaConfigEstacionamentoVO.setPass(json.optString("ftpPass"));
                empresaConfigEstacionamentoVO.setPort(json.optInt("ftpPort"));
                empresaConfigEstacionamentoVO.setNomeArquivo(json.optString("nomeArquivo"));
                empresaConfigEstacionamentoVO.setEnviaTelefoneEmail(json.optBoolean("enviaTelefoneEmail"));
                empresaConfigEstacionamentoVO.setEnviaHorario(json.optBoolean("enviaHorario"));
                empresaConfigEstacionamentoVO.setEnviaValor(json.optBoolean("enviaValor"));
                empresaConfigEstacionamentoVO.setProdutosAdicionar(json.optString("produtosAdicionar"));

                Integer empresa = json.optInt("empresa");

                Connection con = new DAO().obterConexaoEspecifica(chave);
                IntegracaoEstacionamento integracaoEstacionamento = new IntegracaoEstacionamento();
                new IntegracaoEstacionamento().gerarArquivoTesteEnviarFTP(chave, false, empresaConfigEstacionamentoVO, con, empresa);
            } else {
                throw new Exception("Nenhuma operação executada");
            }
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().append(new JSONObject(EnvelopeRespostaDTO.of("sucesso")).toString());
        } catch (Exception ex) {
            ex.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            JSONObject json = new JSONObject();
            json.put("erro", ex.getMessage());
            response.getWriter().append(json.toString());
        }
    }

    private String obterBody(ServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

    private String obterBody(HttpServletRequest request) throws Exception {
        StringBuffer body = new StringBuffer();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line = null;

            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro body: " + ex.getMessage());
        }
    }

}
