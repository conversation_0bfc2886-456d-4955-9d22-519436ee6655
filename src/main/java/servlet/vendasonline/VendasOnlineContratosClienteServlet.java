package servlet.vendasonline;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.controle.json.SuperServletControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoDuracaoVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.contrato.ContratoVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.facade.jdbc.contrato.Contrato;
import negocio.facade.jdbc.contrato.ContratoRecorrencia;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.json.JSONArray;
import org.json.JSONObject;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

public class VendasOnlineContratosClienteServlet extends SuperServletControle {

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        super.processRequest(request, response);
        PrintWriter out = response.getWriter();
        JSONObject jsonRetorno = new JSONObject();
        try {
            String key = obterParametroString(request, "key");
            Integer codigoCliente = obterParametroInt(request, "cliente");
            JSONArray retorno = new JSONArray();
            retorno = consultarContratosRematricular(key,codigoCliente);
            out.println(retorno.toString());
        } catch (Exception e) {
            jsonRetorno.put(STATUS_ERRO, e.getMessage());
            out.println(jsonRetorno);
            out.println("ERRO:" + e.getMessage());
        }
    }

    private JSONArray consultarContratosRematricular(String key, Integer codigoCliente) throws Exception {
        Conexao.guardarConexaoForJ2SE(key, DaoAuxiliar.retornarAcessoControle(key).getContratoDao().getCon());
        ClienteVO clienteVO = DaoAuxiliar.retornarAcessoControle(key).getClienteDao().consultarPorCodigo(codigoCliente, false, Uteis.NIVELMONTARDADOS_METAAGENDAMENTODETALHADA);
        List<ContratoVO> list = DaoAuxiliar.retornarAcessoControle(key).getContratoDao().consultarContratosRematricular(clienteVO.getPessoa().getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
        JSONArray contratos = new JSONArray();
        for (ContratoVO obj : list) {
            ContratoVO contrato = obj;
            consultarDadosContrato(key, contrato, clienteVO);
            contrato.verificarQualBotaoReferenteASituacaoContratoSeraApresentado(clienteVO, list);
            contrato.setRematricula(true);
            JSONObject contratoObj = new JSONObject(contrato.toWS(false));
            contratos.put(contratoObj);
        }

        return contratos;
    }

    private void consultarDadosContrato(String key, ContratoVO contrato, ClienteVO clienteVO) throws Exception {
        contrato.setPlano(DaoAuxiliar.retornarAcessoControle(key).getPlanoDao().consultarPorChavePrimaria(contrato.getPlano().getCodigo(), Uteis.NIVELMONTARDADOS_CONSULTA_WS));
        contrato.setContratoDuracao(new ContratoDuracaoVO());
        contrato.getContratoDuracao().setNumeroMeses(DaoAuxiliar.retornarAcessoControle(key).getContratoDuracaoDao().consultarDuracaoMeses(contrato.getCodigo()));
        contrato.setContratoModalidadeVOs(DaoAuxiliar.retornarAcessoControle(key).getContratoModalidadeDao().consultarContratoModalidades(contrato.getCodigo(), false, Uteis.NIVELMONTARDADOS_ROBO));
        contrato.setContratoCondicaoPagamento(DaoAuxiliar.retornarAcessoControle(key).getContratoCondicaoPagamentoDao().consultarContratoCondicaoPagamentos(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
        contrato.setContratoHorario(DaoAuxiliar.retornarAcessoControle(key).getContratoHorarioDao().consultarContratoHorarios(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));

        String situacaoSub = "";
        if (contrato.getSituacao().equals("AT")) {
            situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteAtivo(contrato, clienteVO);

        }
        if (contrato.getSituacao().equals("TR")) {
            situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteTrancado(contrato, clienteVO);

        }
        if (contrato.getSituacao().equals("IN")) {
            situacaoSub = DaoAuxiliar.retornarAcessoControle(key).getZwFacade().obterSituacaoClienteInativo(contrato, clienteVO);
        }
        if (contrato.getSituacao().equals("CA")) {
            situacaoSub = "CA";
        }
        contrato.setSituacaoSubordinada(situacaoSub);
        ContratoRecorrencia contratoRecorrenciaDAO = new ContratoRecorrencia(DaoAuxiliar.retornarAcessoControle(key).getCon());
        ContratoRecorrenciaVO contratoRecorrenciaVO = contratoRecorrenciaDAO.consultarPorContrato(contrato.getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        contratoRecorrenciaDAO = null;
        if (contratoRecorrenciaVO == null) {
            contratoRecorrenciaVO = new ContratoRecorrenciaVO();
        }
        contrato.setContratoRecorrenciaVO(contratoRecorrenciaVO);
    }
}
