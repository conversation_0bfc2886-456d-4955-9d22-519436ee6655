package servlet.financeiro;

import br.com.pactosolucoes.agendatotal.json.TurmaAulaCheiaJSON;
import br.com.pactosolucoes.enumeradores.ConfiguracaoBIEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.turmas.servico.impl.TurmasServiceImpl;
import br.com.pactosolucoes.turmas.servico.intf.TurmasServiceInterface;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONArray;
import org.json.JSONObject;
import relatorio.negocio.comuns.basico.ListaPaginadaTO;
import relatorio.negocio.comuns.basico.TicketMedioVO;
import relatorio.negocio.jdbc.financeiro.TicketMedio;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TicketMedioServlet extends SuperServlet {

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }


    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {

        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Methods", "GET");
        response.setContentType("application/json");

        String chave = request.getParameter("chave");
        String database = request.getParameter("database");
        Boolean atualizar = request.getParameter("atualizar") == null ? false : Boolean.valueOf(request.getParameter("atualizar"));
        Integer empresa = request.getParameter("empresa") == null ? null : Integer.valueOf(request.getParameter("empresa"));
        Integer mes = request.getParameter("mes") == null ? null : Integer.valueOf(request.getParameter("mes"));
        Integer ano = request.getParameter("ano") == null ? null : Integer.valueOf(request.getParameter("ano"));

        try {

            JSONObject content = getService(chave).gerarTicketMedio(atualizar, empresa, database, mes, ano);
            response.getWriter().append(content.toString());
        }catch (Exception e ){
            System.out.println("Erro na api rest ticket medio. Message: "+e.getMessage());
            processarErro(e, request, response, null);
        }

    }

    private TicketMedio getService(String chave) throws Exception {
        return new TicketMedio(new DAO().obterConexaoEspecifica(chave));
    }
}
