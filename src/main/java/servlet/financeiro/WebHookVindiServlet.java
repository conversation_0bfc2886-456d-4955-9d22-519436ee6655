package servlet.financeiro;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.impl.gatewaypagamento.VerificadorTransacaoService;
import servlet.arquitetura.SuperServlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by johnys on 03/03/2017.
 */
public class WebHookVindiServlet extends SuperServlet{

    @Override
    protected void processRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try{
            Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Chamada ao Webhook VINDI");
            String chave = request.getParameter("chave");
            String params = "";
            try {
                Set keySet = request.getParameterMap().keySet();
                for(Object k : keySet){
                    params += k.toString() + ":"+request.getParameter(k.toString())+";";
                }
                Logger.getLogger(this.getClass().getName()).log(Level.INFO, "Webhook chamado com os seguintes parametros:{0}", params);
            } catch (Exception e) {
                //IGNORE
            }
            
            if(!UteisValidacao.emptyString(chave)){
                Connection con = new DAO().obterConexaoEspecifica(chave);
                String body = Uteis.convertStreamToStringBuffer(request.getInputStream()).toString();
                JSONObject resp = new JSONObject(body);
                Integer idBill = resp.getJSONObject("event").getJSONObject("data").getJSONObject("charge").getJSONObject("bill").getInt("id");
                new VerificadorTransacaoService(con).processarWebhook(TipoTransacaoEnum.VINDI, idBill.toString(), null);
            }
        }catch (Exception e){
            Uteis.logar(e, this.getClass());
        }
    }
}
