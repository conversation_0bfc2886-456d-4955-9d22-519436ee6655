<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ReciboRel" pageWidth="200" pageHeight="500" whenNoDataType="AllSectionsNoDetail" columnWidth="200" leftMargin="0" rightMargin="0" topMargin="5" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="3.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR2" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="empresaVO.cnpj" class="java.lang.String"/>
	<parameter name="empresaVO.endereco" class="java.lang.String"/>
	<parameter name="empresaVO.site" class="java.lang.String"/>
	<parameter name="empresaVO.fone" class="java.lang.String"/>
	<parameter name="mostrarCnpj" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[true]]></defaultValueExpression>
	</parameter>
	<field name="dataConfirmacao" class="java.util.Date"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="cliente.nome_Apresentar" class="java.lang.String"/>
	<field name="cliente.matricula" class="java.lang.String"/>
	<field name="brinde.nome" class="java.lang.String"/>
	<field name="cliente.pessoa.cfp" class="java.lang.String"/>
	<field name="observacao" class="java.lang.String"/>
	<field name="pontos" class="java.lang.Integer"/>
	<group name="sequencial" keepTogether="true">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="97">
			<rectangle radius="10">
				<reportElement key="retDadosRecibo1" x="101" y="1" width="94" height="34" backcolor="#F0F0F0"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Transparent" x="52" y="75" width="142" height="10"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Nome"]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" positionType="Float" stretchType="RelativeToBandHeight" x="0" y="0" width="95" height="39" isPrintWhenDetailOverflows="true" forecolor="#FFFFFF"/>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<line>
				<reportElement x="1" y="85" width="198" height="1"/>
			</line>
			<rectangle radius="10">
				<reportElement key="retDadosEmpresa1" x="0" y="39" width="195" height="36" backcolor="#FFFFFF"/>
				<graphicElement>
					<pen lineWidth="0.25"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="5" y="39" width="189" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="56" width="192" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.endereco}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="66" width="125" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.site}.toLowerCase()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="5" y="47" width="72" height="9">
					<printWhenExpression><![CDATA[$P{mostrarCnpj}.equals(true)]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.cnpj}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="66" width="66" height="9"/>
				<textElement>
					<font fontName="Microsoft Sans Serif" size="7" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{empresaVO.fone}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="102" y="10" width="91" height="18" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="9"/>
				</textElement>
				<text><![CDATA[Resgate de brinde]]></text>
			</staticText>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Transparent" x="5" y="75" width="45" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Matrícula"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Transparent" x="5" y="87" width="45" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement key="staticText-86" positionType="Float" mode="Transparent" x="52" y="87" width="142" height="10"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.nome_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
		<band height="139">
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="135" width="199" height="1" isRemoveLineWhenBlank="true"/>
				<graphicElement>
					<pen lineWidth="1.5" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="5" y="109" width="55" height="8" isRemoveLineWhenBlank="true"/>
				<textElement markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Impressão em:"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataImpressao1" positionType="FixRelativeToBottom" mode="Transparent" x="60" y="109" width="70" height="8" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="5" y="125" width="55" height="8" isRemoveLineWhenBlank="true"/>
				<textElement markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Resgate em:"]]></textFieldExpression>
			</textField>
			<elementGroup>
				<line>
					<reportElement positionType="FixRelativeToBottom" x="5" y="72" width="190" height="1" isRemoveLineWhenBlank="true"/>
				</line>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="5" y="74" width="190" height="12" isRemoveLineWhenBlank="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Cliente: " + $F{cliente.nome_Apresentar}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="FixRelativeToBottom" x="5" y="86" width="190" height="12" isRemoveLineWhenBlank="true"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="8" isBold="true"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["CPF: " + $F{cliente.pessoa.cfp}]]></textFieldExpression>
				</textField>
			</elementGroup>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="dataImpressao1" positionType="FixRelativeToBottom" mode="Transparent" x="60" y="125" width="70" height="8" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataConfirmacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="5" y="17" width="188" height="13" isPrintWhenDetailOverflows="true"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{brinde.nome}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="5" y="117" width="55" height="8" isRemoveLineWhenBlank="true"/>
				<textElement markup="none">
					<font size="6" isBold="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Impressão por:"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="dataImpressao1" positionType="FixRelativeToBottom" mode="Transparent" x="60" y="117" width="70" height="8" isRemoveLineWhenBlank="true"/>
				<textElement verticalAlignment="Top">
					<font fontName="SansSerif" size="6" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{usuario}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="1" y="15" width="198" height="1"/>
			</line>
			<textField>
				<reportElement key="staticText-85" positionType="Float" mode="Transparent" x="5" y="4" width="90" height="10"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Brinde resgatado"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="staticText-85" positionType="Float" mode="Transparent" x="5" y="34" width="188" height="10">
					<printWhenExpression><![CDATA[$F{observacao}.length() > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Observação: " + $F{observacao}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement key="staticText-85" positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="101" y="4" width="92" height="10" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="none">
					<font fontName="SansSerif" size="7" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pontos gastos: " + $F{pontos}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
