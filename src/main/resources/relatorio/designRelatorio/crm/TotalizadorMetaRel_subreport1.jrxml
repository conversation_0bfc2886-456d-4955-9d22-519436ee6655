<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TotalizadorMetaRel_subreport1" columnCount="6" printOrder="Horizontal" pageWidth="520" pageHeight="842" whenNoDataType="NoDataSection" columnWidth="74" columnSpacing="15" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="2.1435888100000025"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="Table Dataset 1"/>
	<subDataset name="dataset1"/>
	<field name="identificadorMeta_Apresentar" class="java.lang.String"/>
	<field name="meta_Apresentar" class="java.lang.String"/>
	<field name="metaAtingida_Apresentar" class="java.lang.String"/>
	<field name="porcentagem" class="java.lang.Double"/>
	<field name="repescagem_Apresentar" class="java.lang.String"/>
	<field name="perc" class="java.lang.String"/>
	<field name="metaBoxResultado_Apresentar" class="java.lang.String"/>
	<field name="porcentagemBoxResultado_Apresentar" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="73" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="24" width="74" height="48" forecolor="#C0C0C0"/>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="0" width="74" height="24" forecolor="#C0C0C0" backcolor="#C0C0C0"/>
			</rectangle>
			<textField>
				<reportElement positionType="Float" x="3" y="0" width="67" height="19" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{identificadorMeta_Apresentar}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00">
				<reportElement positionType="Float" x="12" y="24" width="47" height="27" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Arial" size="16"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{porcentagemBoxResultado_Apresentar}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="59" y="31" width="8" height="20"/>
				<textElement verticalAlignment="Bottom">
					<font size="9"/>
				</textElement>
				<text><![CDATA[%]]></text>
			</staticText>
			<textField pattern="###0.00">
				<reportElement positionType="Float" x="8" y="54" width="61" height="14" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{metaBoxResultado_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
