<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ParcelaEmAbertoRel" pageWidth="595" pageHeight="842" columnWidth="567" leftMargin="14" rightMargin="14" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<property name="ireport.zoom" value="2.1435888100000033"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["E:\\desenvolvimentosv\\Projeto Pacto Solucoes\\ZillyonWeb\\src\\java\\relatorio\\designRelatorio\\financeiro\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdCA" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequeAV" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdChequePR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="qtdOutro" class="java.lang.String" isForPrompting="false"/>
	<parameter name="valorAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorCA" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequeAV" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorChequePR" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="valorOutro" class="java.lang.Double" isForPrompting="false"/>
	<parameter name="parametro1" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro2" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro3" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro4" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro5" class="java.lang.String" isForPrompting="false"/>
	<parameter name="parametro6" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalPessoas" class="java.lang.Double"/>
	<field name="nomePessoaRel" class="java.lang.String"/>
	<field name="agenda.tipoAgendamento_Apresentar" class="java.lang.String"/>
	<field name="agenda.dataLancamento_Apresentar" class="java.lang.String"/>
	<field name="agenda.dataAgendamentoComparecimento_Apresentar" class="java.lang.String"/>
	<field name="agenda.dataComparecimento_Apresentar" class="java.lang.String"/>
	<field name="agenda.responsavelComparecimento.nomeAbreviado" class="java.lang.String"/>
	<field name="agenda.colaboradorResponsavel.primeiroNomeConcatenado" class="java.lang.String"/>
	<field name="cliente.matricula" class="java.lang.String"/>
	<field name="historicoContatoVO.resultado" class="java.lang.String"/>
	<field name="cliente.clienteSituacaoVO_Apresentar.situacaoAtualCliente_Apresentar" class="java.lang.String"/>
	<group name="nomeCliente">
		<groupHeader>
			<band height="15" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-19" x="-1" y="0" width="111" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="110" y="0" width="31" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Tipo Ag.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="141" y="0" width="64" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Lançamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="205" y="0" width="56" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Agendamento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="401" y="0" width="68" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Colaborador Resp.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="469" y="0" width="37" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Matrícula]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="261" y="0" width="69" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Comparecimento]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="330" y="0" width="71" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Resp. Comparec.]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-19" x="506" y="0" width="61" height="14"/>
					<textElement>
						<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Resultado]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="62" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="1" width="82" height="36" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="false">
				<reportElement key="dataRel-1" mode="Opaque" x="443" y="1" width="124" height="19" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isItalic="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" x="83" y="1" width="360" height="19"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{tituloRelatorio}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-25" x="443" y="20" width="73" height="17"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Pág: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-26" x="516" y="20" width="51" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="83" y="20" width="360" height="17"/>
				<box>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-19" x="1" y="40" width="55" height="13"/>
				<textElement>
					<font size="7" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Total Pessoas:]]></text>
			</staticText>
			<textField pattern="###0">
				<reportElement x="56" y="40" width="27" height="13"/>
				<textElement>
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$P{totalPessoas}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField>
				<reportElement x="110" y="0" width="31" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.tipoAgendamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="141" y="0" width="64" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.dataLancamento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="261" y="0" width="69" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.dataComparecimento_Apresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="401" y="0" width="68" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.colaboradorResponsavel.primeiroNomeConcatenado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="469" y="0" width="37" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cliente.matricula}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="506" y="0" width="61" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{historicoContatoVO.resultado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="110" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomePessoaRel}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="0" width="71" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.responsavelComparecimento.nomeAbreviado}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="205" y="0" width="56" height="13"/>
				<textElement>
					<font size="6"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agenda.dataAgendamentoComparecimento_Apresentar}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="19" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="0" y="0" width="565" height="13"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
