<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MapaTurmasDia" pageWidth="113" pageHeight="283" orientation="Landscape" columnWidth="113" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="3.000000000000013"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="tituloRelatorio" class="java.lang.String" isForPrompting="false"/>
	<parameter name="versaoSoftware" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["D:\\PactoJ\\Desenvolvimento\\Sistemas\\ZillyonWeb\\tronco-novo\\src\\java\\relatorio\\designRelatorio\\outros\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEmpresa" class="java.lang.String"/>
	<parameter name="enderecoEmpresa" class="java.lang.String"/>
	<parameter name="cidadeEmpresa" class="java.lang.String"/>
	<parameter name="dataIni" class="java.lang.String" isForPrompting="false"/>
	<parameter name="dataFim" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String"/>
	<parameter name="totalClientes" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalContratos" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalValor" class="java.lang.String" isForPrompting="false"/>
	<parameter name="totalCompetencia" class="java.lang.String" isForPrompting="false"/>
	<parameter name="listaTotais" class="java.lang.Object" isForPrompting="false"/>
	<parameter name="colMatricula" class="java.lang.Boolean"/>
	<parameter name="colNome" class="java.lang.Boolean"/>
	<parameter name="colSituacao" class="java.lang.Boolean"/>
	<parameter name="colVinculo" class="java.lang.Boolean"/>
	<parameter name="colPlano" class="java.lang.Boolean"/>
	<parameter name="colContrato" class="java.lang.Boolean"/>
	<parameter name="colModalidade" class="java.lang.Boolean"/>
	<parameter name="colValorModalidade" class="java.lang.Boolean"/>
	<parameter name="colDuracao" class="java.lang.Boolean"/>
	<parameter name="colDataLancamento" class="java.lang.Boolean"/>
	<parameter name="colInicio" class="java.lang.Boolean"/>
	<parameter name="colVence" class="java.lang.Boolean"/>
	<parameter name="colValor" class="java.lang.Boolean"/>
	<parameter name="colHorario" class="java.lang.Boolean"/>
	<parameter name="colFaturamento" class="java.lang.Boolean"/>
	<field name="nomeProfessor" class="java.lang.String"/>
	<field name="ambiente" class="java.lang.String"/>
	<field name="nomeTurma" class="java.lang.String"/>
	<field name="nrMaxApresentar" class="java.lang.String"/>
	<field name="vagasApresentar" class="java.lang.String"/>
	<field name="alunosJr" class="java.lang.Object"/>
	<field name="nivel" class="java.lang.String"/>
	<field name="modalidade" class="java.lang.String"/>
	<detail>
		<band height="80" splitType="Stretch">
			<textField>
				<reportElement x="0" y="1" width="113" height="12"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Prof. "+$F{nomeProfessor}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="25" width="113" height="12"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeTurma}+" - L"+$F{nrMaxApresentar}+"/V"+$F{vagasApresentar}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="49" width="113" height="13"/>
				<textElement>
					<font size="9" isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nivel}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement isPrintRepeatedValues="false" x="0" y="64" width="113" height="14"/>
				<subreportParameter name="enderecoEmpresa"/>
				<subreportParameter name="totalCompetencia"/>
				<subreportParameter name="colValorModalidade"/>
				<subreportParameter name="colInicio"/>
				<subreportParameter name="usuario"/>
				<subreportParameter name="colVence"/>
				<subreportParameter name="totalValor"/>
				<subreportParameter name="colNome"/>
				<subreportParameter name="totalContratos"/>
				<subreportParameter name="SUBREPORT_DIR1"/>
				<subreportParameter name="colDuracao"/>
				<subreportParameter name="colHorario"/>
				<subreportParameter name="colSituacao"/>
				<subreportParameter name="colMatricula"/>
				<subreportParameter name="dataIni"/>
				<subreportParameter name="colFaturamento"/>
				<subreportParameter name="colContrato"/>
				<subreportParameter name="logoPadraoRelatorio"/>
				<subreportParameter name="totalClientes"/>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="dataFim"/>
				<subreportParameter name="colModalidade"/>
				<subreportParameter name="tituloRelatorio"/>
				<subreportParameter name="colValor"/>
				<subreportParameter name="colPlano"/>
				<subreportParameter name="nomeEmpresa"/>
				<subreportParameter name="colVinculo"/>
				<subreportParameter name="colDataLancamento"/>
				<subreportParameter name="cidadeEmpresa"/>
				<subreportParameter name="listaTotais"/>
				<subreportParameter name="filtros"/>
				<subreportParameter name="versaoSoftware"/>
				<dataSourceExpression><![CDATA[$F{alunosJr}]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA[$P{SUBREPORT_DIR} + "AlunosMapaTurmasDia.jasper"]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="0" y="37" width="113" height="12"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{ambiente}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="13" width="113" height="12"/>
				<textElement>
					<font size="8" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Mod. " + $F{modalidade}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
