<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RelatorioHistoricoMetasFinanceira_subreport1" columnCount="5" printOrder="Horizontal" pageWidth="302" pageHeight="802" columnWidth="58" columnSpacing="3" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<field name="valor" class="java.lang.Double"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="20" splitType="Stretch">
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="58" height="19"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
