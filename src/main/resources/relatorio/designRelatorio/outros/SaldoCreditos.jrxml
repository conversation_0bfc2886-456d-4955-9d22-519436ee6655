<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SaldoCreditos" pageWidth="595" pageHeight="842" columnWidth="591" leftMargin="2" rightMargin="2" topMargin="2" bottomMargin="2" whenResourceMissingType="Empty">
	<property name="ireport.zoom" value="1.3636363636363638"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="ISO-8859-1"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<subDataset name="Teste"/>
	<parameter name="logoPadraoRelatorio" class="java.io.InputStream" isForPrompting="false"/>
	<parameter name="nomeEmpresa" class="java.lang.String" isForPrompting="false"/>
	<parameter name="usuario" class="java.lang.String" isForPrompting="false"/>
	<parameter name="filtros" class="java.lang.String" isForPrompting="false"/>
	<queryString>
		<![CDATA[SELECT cliente.matricula as matricula, pessoa.nome as nome, pessoa.datanasc FROM cliente
INNER JOIN pessoa ON pessoa.codigo = cliente.pessoa
WHERE ((DATE_PART('MONTH',pessoa.datanasc) > 6) OR (DATE_PART('MONTH',pessoa.datanasc) = 6 AND DATE_PART('DAY',pessoa.datanasc) >= 1)) AND
      ((DATE_PART('MONTH',pessoa.datanasc) < 6) OR (DATE_PART('MONTH',pessoa.datanasc) = 6 AND DATE_PART('DAY',pessoa.datanasc) <= 30)) AND
cliente.empresa = 1]]>
	</queryString>
	<field name="matricula" class="java.lang.Integer"/>
	<field name="nomeCliente" class="java.lang.String"/>
	<field name="dataVigenciaAte_Apresentar" class="java.lang.String"/>
	<field name="saldoCreditoTreino" class="java.lang.Integer"/>
	<field name="primeiroTelefone" class="java.lang.String"/>
	<variable name="total" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{matricula}]]></variableExpression>
	</variable>
	<variable name="total.pagina" class="java.lang.Integer" resetType="Page" calculation="Count">
		<variableExpression><![CDATA[$F{matricula}]]></variableExpression>
	</variable>
	<group name="cliente">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="26" splitType="Stretch">
				<staticText>
					<reportElement key="staticText-3" x="79" y="0" width="206" height="24"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Nome]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-3" x="394" y="0" width="112" height="24"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Vencimento Contrato]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-3" x="285" y="0" width="109" height="24"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[ Saldo de Créditos]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-3" x="10" y="0" width="69" height="24"/>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Matrícula]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText-3" x="506" y="1" width="81" height="24"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<text><![CDATA[Telefone]]></text>
				</staticText>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="145" splitType="Stretch">
			<image isUsingCache="true" onErrorType="Blank" evaluationTime="Page">
				<reportElement key="image-1" x="1" y="6" width="82" height="52" isPrintWhenDetailOverflows="true"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<imageExpression class="java.io.InputStream"><![CDATA[$P{logoPadraoRelatorio}]]></imageExpression>
			</image>
			<staticText>
				<reportElement key="staticText-17" mode="Opaque" x="319" y="2" width="268" height="23"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[Zillyon - Sistema Administrativo para Academias Desenvolvido por PACTO Soluções Tecnológicas Ltda.]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-18" mode="Opaque" x="476" y="30" width="111" height="12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" lineSpacing="Single">
					<font fontName="Microsoft Sans Serif" size="9" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<text><![CDATA[(0xx62) 3251-5820]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-19" x="109" y="59" width="366" height="27"/>
				<textElement textAlignment="Center">
					<font size="18" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Relatório de saldo de créditos ]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-212" x="86" y="4" width="245" height="14"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{nomeEmpresa}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="false">
				<reportElement key="textField-215" x="557" y="47" width="31" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER} + ""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-216" x="475" y="47" width="75" height="17"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="10" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Página: " + $V{PAGE_NUMBER} + " de "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-218" x="6" y="94" width="581" height="38"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="10" isBold="true" isItalic="true" pdfFontName="Helvetica-BoldOblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{filtros}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<rectangle>
				<reportElement key="rectangle-1" mode="Opaque" x="10" y="0" width="577" height="20" backcolor="#B4CDCD">
					<printWhenExpression><![CDATA[new Boolean(($V{COLUMN_COUNT}.intValue()%2)==0)]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="12" y="0" width="70" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="79" y="0" width="206" height="20"/>
				<textElement verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeCliente}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField" x="394" y="0" width="112" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataVigenciaAte_Apresentar}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField" x="285" y="0" width="109" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{saldoCreditoTreino}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField" x="506" y="0" width="81" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="10" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{primeiroTelefone}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="23" splitType="Stretch">
			<staticText>
				<reportElement key="staticText-16" x="428" y="5" width="69" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Total por Página]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="499" y="5" width="41" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{total.pagina}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-207" x="44" y="3" width="496" height="17"/>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true" pdfFontName="Helvetica-Oblique"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+" Usuário:" + $P{usuario}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH.mm.ss" isBlankWhenNull="true">
				<reportElement key="dataRel-1" mode="Opaque" x="281" y="5" width="113" height="13" backcolor="#FFFFFF"/>
				<box bottomPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="false" pdfFontName="Helvetica-Oblique" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="18" splitType="Stretch">
			<rectangle>
				<reportElement x="44" y="3" width="496" height="13"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-211" x="500" y="3" width="41" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$V{total}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-16" x="475" y="3" width="22" height="13"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="8" isBold="false" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Total:]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
