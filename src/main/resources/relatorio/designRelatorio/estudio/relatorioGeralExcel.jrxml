<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorioGeralExcel" language="groovy" pageWidth="5300" pageHeight="50000" columnWidth="5300" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.zoom" value="2.3538212825407543"/>
	<property name="ireport.x" value="1479"/>
	<property name="ireport.y" value="0"/>
	<field name="matricula" class="java.lang.String"/>
	<field name="nome" class="java.lang.String"/>
	<field name="telefone" class="java.lang.String"/>
	<field name="sexo" class="java.lang.String"/>
	<field name="email" class="java.lang.String"/>
	<field name="situacaoCliente" class="java.lang.String"/>
	<field name="dataNascimento" class="java.util.Date"/>
	<field name="logradouro" class="java.lang.String"/>
	<field name="bairro" class="java.lang.String"/>
	<field name="cep" class="java.lang.String"/>
	<field name="cidade" class="java.lang.String"/>
	<field name="dataMatricula" class="java.util.Date"/>
	<field name="numero" class="java.lang.String"/>
	<field name="complemento" class="java.lang.String"/>
	<field name="inicioPlano" class="java.util.Date"/>
	<field name="vencimentoPlano" class="java.util.Date"/>
	<field name="dataUltimoAcesso" class="java.util.Date"/>
	<field name="plano" class="java.lang.String"/>
	<field name="dataCadastro" class="java.util.Date"/>
	<field name="consultor" class="java.lang.String"/>
	<field name="cpf" class="java.lang.String"/>
	<field name="nomeRespContrato" class="java.lang.String"/>
	<field name="nomeConsultor" class="java.lang.String"/>
	<field name="nomeProfessores" class="java.lang.String"/>
	<field name="rg" class="java.lang.String"/>
	<field name="professorTreino" class="java.lang.String"/>
	<columnHeader>
		<band height="13">
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="50" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Matrícula]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="50" y="0" width="197" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nome]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="954" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Sexo]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="658" y="0" width="156" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Email]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="884" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Situação]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="814" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Nascimento]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1024" y="0" width="140" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Logradouro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1214" y="0" width="136" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Bairro]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1473" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[CEP]]></text>
			</staticText>
			<staticText>
				<reportElement x="1350" y="0" width="123" height="13"/>
				<textElement>
					<font isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cidade]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1649" y="0" width="70" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Data Mat.]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1164" y="0" width="50" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Número]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1543" y="0" width="106" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Complemento]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1719" y="0" width="75" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Início Plano]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1794" y="0" width="75" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Venc. Plano]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1869" y="0" width="75" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Último Acesso]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="1944" y="0" width="185" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Plano]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="2129" y="0" width="75" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Cadastro]]></text>
			</staticText>
			<staticText>
				<reportElement x="2204" y="0" width="1181" height="13"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Consultor/Professor]]></text>
			</staticText>
			<staticText>
				<reportElement x="247" y="0" width="105" height="13"/>
				<textElement/>
				<text><![CDATA[CPF]]></text>
			</staticText>
			<staticText>
				<reportElement x="5050" y="0" width="354" height="13"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Responsável Contrato]]></text>
			</staticText>
			<staticText>
				<reportElement x="3385" y="0" width="329" height="13"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Consultor]]></text>
			</staticText>
			<staticText>
				<reportElement x="3714" y="0" width="859" height="13"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professores]]></text>
			</staticText>
			<staticText>
				<reportElement x="352" y="0" width="105" height="13"/>
				<textElement/>
				<text><![CDATA[RG]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="457" y="0" width="201" height="13"/>
				<textElement verticalAlignment="Middle">
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Telefone]]></text>
			</staticText>
			<staticText>
				<reportElement x="4573" y="0" width="477" height="13"/>
				<textElement>
					<font size="9" isBold="true" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<text><![CDATA[Professor(TreinoWeb)]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="50" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{matricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="0" width="197" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="658" y="0" width="156" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{email}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1164" y="0" width="50" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numero}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="814" y="0" width="70" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataNascimento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1473" y="0" width="70" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cep}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="1024" y="0" width="140" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{logradouro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="884" y="0" width="70" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{situacaoCliente}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1350" y="0" width="123" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cidade}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="954" y="0" width="70" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{sexo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1214" y="0" width="136" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{bairro}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1649" y="0" width="70" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataMatricula}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1794" y="0" width="75" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{vencimentoPlano}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2129" y="0" width="75" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataCadastro}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="2204" y="0" width="1181" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{consultor}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1944" y="0" width="185" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{plano}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1719" y="0" width="75" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{inicioPlano}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1543" y="0" width="106" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{complemento}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="1869" y="0" width="75" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[$F{dataUltimoAcesso}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="247" y="0" width="105" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{cpf}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="5050" y="0" width="354" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeRespContrato}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="3385" y="0" width="329" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeConsultor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="3714" y="0" width="859" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeProfessores}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="352" y="0" width="105" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{rg}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="457" y="0" width="201" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{telefone}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="4573" y="0" width="469" height="13"/>
				<textElement>
					<font size="9"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{professorTreino}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
