¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             ô              ô          sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp    w    xp  wî    pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCHsq ~ sq ~     w    xp  wî    ppq ~ sq ~ sq ~     w    xp  wî    ppq ~ ur ([Lnet.sf.jasperreports.engine.JRDataset;L6Í¬D  xp   sr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî  pppt dataset1ur *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ *L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ *L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt 
java.util.Mappsq ~ 4ppt 
JASPER_REPORTpsq ~ 7pppt (net.sf.jasperreports.engine.JasperReportpsq ~ 4ppt REPORT_CONNECTIONpsq ~ 7pppt java.sql.Connectionpsq ~ 4ppt REPORT_MAX_COUNTpsq ~ 7pppt java.lang.Integerpsq ~ 4ppt REPORT_DATA_SOURCEpsq ~ 7pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~ 4ppt REPORT_SCRIPTLETpsq ~ 7pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~ 4ppt 
REPORT_LOCALEpsq ~ 7pppt java.util.Localepsq ~ 4ppt REPORT_RESOURCE_BUNDLEpsq ~ 7pppt java.util.ResourceBundlepsq ~ 4ppt REPORT_TIME_ZONEpsq ~ 7pppt java.util.TimeZonepsq ~ 4ppt REPORT_FORMAT_FACTORYpsq ~ 7pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~ 4ppt REPORT_CLASS_LOADERpsq ~ 7pppt java.lang.ClassLoaderpsq ~ 4ppt REPORT_URL_HANDLER_FACTORYpsq ~ 7pppt  java.net.URLStreamHandlerFactorypsq ~ 4ppt REPORT_FILE_RESOLVERpsq ~ 7pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~ 4ppt REPORT_TEMPLATESpsq ~ 7pppt java.util.Collectionpsq ~ 7ppppppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupt %Lnet/sf/jasperreports/engine/JRGroup;L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ tL resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp    ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt new java.lang.Integer(1)q ~ Fpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~ Fpsq ~ r  wî   q ~ yppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpt 
COLUMN_NUMBERp~q ~ t PAGEq ~ Fpsq ~ r  wî   ~q ~ xt COUNTsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt REPORT_COUNTpq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt 
PAGE_COUNTpq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpt COLUMN_COUNTp~q ~ t COLUMNq ~ Fp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLpppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sq ~ sq ~    
w   
sr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColort Ljava/awt/Color;L bottomBorderq ~ L bottomBorderColorq ~ ÀL 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ ÁL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ ÃL 
isPdfEmbeddedq ~ ÃL isStrikeThroughq ~ ÃL isStyledTextq ~ ÃL isUnderlineq ~ ÃL 
leftBorderq ~ L leftBorderColorq ~ ÀL leftPaddingq ~ ÁL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ ÁL pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ ÀL rightPaddingq ~ ÁL rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ ÀL 
topPaddingq ~ ÁL verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolorq ~ ÀL defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ ÀL keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ tL 
propertiesMapq ~ *[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî           $     pq ~ q ~ ¼pt staticText-115p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ t OPAQUEpp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FLOATsq ~ ~   uq ~    sq ~ t !sq ~ t titulosq ~ t  && !sq ~ t totalt java.lang.Booleanpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCH  wîpppppt Microsoft Sans Serifsr java.lang.Integerâ ¤÷8 I valuexr java.lang.Number¬à  xp   p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ t LEFTsr java.lang.BooleanÍ rÕúî Z valuexp q ~ ìpq ~ ìpq ~ ìpppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ ÁL 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ ÁL leftPenq ~ îL paddingq ~ ÁL penq ~ îL rightPaddingq ~ ÁL rightPenq ~ îL 
topPaddingq ~ ÁL topPenq ~ îxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ Äxr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ ÀL 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ úxp    ÿfffpppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.FloatÚíÉ¢Û<ðì F valuexq ~ æ    q ~ ðq ~ ðq ~ Îpsr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ò  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~ ðq ~ ðpsq ~ ò  wîppppq ~ ðq ~ ðpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ò  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~ ðq ~ ðpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ò  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~ ðq ~ ðpppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ t MIDDLEt  Valor:sr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ tL evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ ÃL 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~ ¿  wî           #  E   pq ~ q ~ ¼pt 
textField-220pq ~ Ñppq ~ Ôsq ~ ~   	uq ~    sq ~ t !sq ~ t totalsq ~ t  && !sq ~ t tituloq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çp~q ~ èt RIGHTq ~ ìq ~ ìpppppppsq ~ ípsq ~ ñ  wîppppq ~%q ~%q ~psq ~  wîppppq ~%q ~%psq ~ ò  wîppppq ~%q ~%psq ~  wîppppq ~%q ~%psq ~
  wîppppq ~%q ~%pppppt 	Helveticappppppppppq ~  wî        pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsq ~ ~   
uq ~    sq ~ t qtdt java.lang.Integerppppppq ~ ìppt  sq ~ ¾  wî             '   pq ~ q ~ ¼pt staticText-114pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t !sq ~ t totalsq ~ t  && !sq ~ t tituloq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çpq ~ éq ~ ìq ~ ìpq ~ ìpq ~ ìpppsq ~ ípsq ~ ñ  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~Bq ~Bq ~5psq ~  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~Bq ~Bpsq ~ ò  wîppppq ~Bq ~Bpsq ~  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~Bq ~Bpsq ~
  wîsq ~ ø    ÿfffppppq ~ ýsq ~ ÿ    q ~Bq ~Bpppppt 	Helveticappppppppppq ~t  Qtd:sq ~  wî           J  ¤   pq ~ q ~ ¼pt 
textField-221pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t !sq ~ t titulosq ~ t  && !sq ~ t totalq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çpq ~#q ~ ìq ~ ìpppppppsq ~ ípsq ~ ñ  wîppppq ~_q ~_q ~Rpsq ~  wîppppq ~_q ~_psq ~ ò  wîppppq ~_q ~_psq ~  wîppppq ~_q ~_psq ~
  wîppppq ~_q ~_pppppt 	Helveticappppppppppq ~  wî        ppq ~-sq ~ ~   
uq ~    sq ~ t valort java.lang.Doubleppppppq ~ ìppt #,##0.00sq ~  wî           Ð      pq ~ q ~ ¼pt 
textField-220pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t !sq ~ t tituloq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çppq ~ ìq ~ ìpppppppsq ~ ípsq ~ ñ  wîppppq ~uq ~uq ~lpsq ~  wîppppq ~uq ~upsq ~ ò  wîppppq ~uq ~upsq ~  wîppppq ~uq ~upsq ~
  wîppppq ~uq ~upppppt 	Helveticappppppppppq ~  wî        ppq ~-sq ~ ~   uq ~    sq ~ t 	descricaot java.lang.Stringppppppq ~ ìppq ~4sr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xq ~ È  wî           n     pq ~ q ~ ¼pppppp~q ~ Ót FIX_RELATIVE_TO_TOPsq ~ ~   uq ~    sq ~ t totalq ~ àppppq ~ â  wîppsq ~ ó  wîppppq ~p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~  wî          #   ÿÿÿÿpq ~ q ~ ¼pt 
textField-220pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t tituloq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çppsq ~ ëq ~ ìpppq ~pppsq ~ ípsq ~ ñ  wîppppq ~q ~q ~psq ~  wîppppq ~q ~psq ~ ò  wîppppq ~q ~psq ~  wîppppq ~q ~psq ~
  wîppppq ~q ~pppppt 	Helveticappppppppppq ~  wî        ppq ~-sq ~ ~   uq ~    sq ~ t 	descricaot java.lang.Stringppppppq ~ ìppq ~4sq ~  wî           '     pq ~ q ~ ¼pt staticText-116pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t totalq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çpq ~ éq ~q ~ ìpq ~ ìpq ~ ìpppsq ~ ípsq ~ ñ  wîppppq ~¬q ~¬q ~¥psq ~  wîppppq ~¬q ~¬psq ~ ò  wîppppq ~¬q ~¬psq ~  wîppppq ~¬q ~¬psq ~
  wîppppq ~¬q ~¬ppt noneppt Helvetica-Boldppppppppppq ~  wî        ppq ~-sq ~ ~   uq ~    sq ~ t moedat java.lang.Stringppppppppppsq ~  wî           J  ¤   pq ~ q ~ ¼pt 
textField-221pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t totalq ~ àppppq ~ â  wîpppppt Microsoft Sans Serifq ~ çpq ~#q ~q ~ ìpppppppsq ~ ípsq ~ ñ  wîppppq ~Àq ~Àq ~¹psq ~  wîppppq ~Àq ~Àpsq ~ ò  wîppppq ~Àq ~Àpsq ~  wîppppq ~Àq ~Àpsq ~
  wîppppq ~Àq ~Àpppppt Helvetica-Boldppppppppppq ~  wî        ppq ~-sq ~ ~   uq ~    sq ~ t valort java.lang.Doubleppppppq ~ ìppt #,##0.00sq ~  wî           Æ   º   pq ~ q ~ ¼pt 
textField-221pq ~ Ñppq ~ Ôsq ~ ~   uq ~    sq ~ t totalq ~ àpppp~q ~ át RELATIVE_TO_BAND_HEIGHT  wîpppppt Microsoft Sans Serifq ~ çp~q ~ èt CENTERq ~q ~ ìpppppppsq ~ ípsq ~ ñ  wîppppq ~Øq ~Øq ~Ípsq ~  wîppppq ~Øq ~Øpsq ~ ò  wîppppq ~Øq ~Øpsq ~  wîppppq ~Øq ~Øpsq ~
  wîppppq ~Øq ~Øpppppt Helvetica-Boldppppppppppq ~  wî       ppq ~-sq ~ ~   uq ~    sq ~ t 
labelTotalt java.lang.Stringppppppq ~ ìppq ~4xp  wî   pppppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsq ~ &  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ *L valueClassNameq ~ L valueClassRealNameq ~ xppt 	descricaosq ~ 7pppt java.lang.Stringpsq ~ípt qtdsq ~ 7pppt java.lang.Integerpsq ~ípt valorsq ~ 7pppt java.lang.Doublepsq ~ípt totalsq ~ 7pppt java.lang.Booleanpsq ~ípt titulosq ~ 7pppt java.lang.Booleanpsq ~ípt 
labelTotalsq ~ 7pppt java.lang.Stringpppt TotalizadoresCaixaPorOperadoruq ~ 2   sq ~ 4ppq ~ 6psq ~ 7pppq ~ :psq ~ 4ppq ~ <psq ~ 7pppq ~ >psq ~ 4ppq ~ @psq ~ 7pppq ~ Bpsq ~ 4ppq ~ Dpsq ~ 7pppq ~ Fpsq ~ 4ppq ~ Hpsq ~ 7pppq ~ Jpsq ~ 4ppq ~ Lpsq ~ 7pppq ~ Npsq ~ 4ppq ~ Ppsq ~ 7pppq ~ Rpsq ~ 4ppq ~ Tpsq ~ 7pppq ~ Vpsq ~ 4ppq ~ Xpsq ~ 7pppq ~ Zpsq ~ 4ppq ~ \psq ~ 7pppq ~ ^psq ~ 4ppq ~ `psq ~ 7pppq ~ bpsq ~ 4ppq ~ dpsq ~ 7pppq ~ fpsq ~ 4ppq ~ hpsq ~ 7pppq ~ jpsq ~ 4ppq ~ lpsq ~ 7pppq ~ npsq ~ 4ppt REPORT_VIRTUALIZERpsq ~ 7pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~ 4ppt IS_IGNORE_PAGINATIONpsq ~ 7pppq ~ àpsq ~ 4 ppt qtdDVpsq ~ 7pppt java.lang.Stringpsq ~ 4 ppt valorDVpsq ~ 7pppt java.lang.Doublepsq ~ 4 ppt moedapsq ~ 7pppt java.lang.Stringpsq ~ 7psq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~;t 2.5937424601000028q ~:t UTF-8q ~<t 128q ~=t 0q ~9t 0xpppppuq ~ p   sq ~ r  wî   q ~ yppq ~ |ppsq ~ ~    uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ r  wî   q ~ yppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fpq ~ pq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ pq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ §pq ~ q ~ Fpsq ~ r  wî   q ~ sq ~ ~   uq ~    sq ~ t new java.lang.Integer(1)q ~ Fppq ~ |ppsq ~ ~   uq ~    sq ~ t new java.lang.Integer(0)q ~ Fpq ~ ±pq ~ ²q ~ Fpq ~ µq ~p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t 	LANDSCAPEpsq ~ sq ~     w    xp  wî    ppq ~ ~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICALpsq ~ sq ~     w    xp  wî    ppq ~ psq ~ sq ~     w    xp  wî    ppq ~ ~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ t ALL_SECTIONS_NO_DETAILsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~ 8L datasetCompileDataq ~ 8L mainDatasetCompileDataq ~ xpsq ~>?@     w       xsq ~>?@     w      q ~ 1ur [B¬óøTà  xp  Êþº¾   .  ;TotalizadoresCaixaPorOperador_dataset1_1610477336987_925413  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES  parameter_REPORT_RESOURCE_BUNDLE variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code  
    	    	  !  	  # 	 	  % 
 	  '  	  )  	  + 
 	  -  	  /  	  1  	  3  	  5  	  7  	  9  	  ;  	  =  	  ?  	  A  	  C LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V H I
  J 
initFields L I
  M initVars O I
  P 
REPORT_LOCALE R 
java/util/Map T get &(Ljava/lang/Object;)Ljava/lang/Object; V W U X 0net/sf/jasperreports/engine/fill/JRFillParameter Z 
JASPER_REPORT \ REPORT_TIME_ZONE ^ REPORT_FILE_RESOLVER ` REPORT_SCRIPTLET b REPORT_PARAMETERS_MAP d REPORT_CONNECTION f REPORT_CLASS_LOADER h REPORT_DATA_SOURCE j REPORT_URL_HANDLER_FACTORY l REPORT_FORMAT_FACTORY n REPORT_MAX_COUNT p REPORT_TEMPLATES r REPORT_RESOURCE_BUNDLE t PAGE_NUMBER v /net/sf/jasperreports/engine/fill/JRFillVariable x 
COLUMN_NUMBER z REPORT_COUNT | 
PAGE_COUNT ~ COLUMN_COUNT  evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable  java/lang/Integer  (I)V  
   evaluateOld evaluateEstimated 
SourceFile !                      	     
               
                                                                   Ì     d*· *µ  *µ "*µ $*µ &*µ (*µ **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D±    E   V       	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c   F G     4     *+· K*,· N*-· Q±    E       :  ; 
 <  =  H I    M     ý*+S¹ Y À [À [µ  *+]¹ Y À [À [µ "*+_¹ Y À [À [µ $*+a¹ Y À [À [µ &*+c¹ Y À [À [µ (*+e¹ Y À [À [µ **+g¹ Y À [À [µ ,*+i¹ Y À [À [µ .*+k¹ Y À [À [µ 0*+m¹ Y À [À [µ 2*+o¹ Y À [À [µ 4*+q¹ Y À [À [µ 6*+s¹ Y À [À [µ 8*+u¹ Y À [À [µ :±    E   >    E  F $ G 6 H H I Z J l K ~ L  M ¢ N ´ O Æ P Ø Q ê R ü S  L I           ±    E       [  O I          [*+w¹ Y À yÀ yµ <*+{¹ Y À yÀ yµ >*+}¹ Y À yÀ yµ @*+¹ Y À yÀ yµ B*+¹ Y À yÀ yµ D±    E       c  d $ e 6 f H g Z h              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    p  r 0 v 9 w < { E | H  Q  T  ]  `  i  l  u  x       ¡              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ª  ¬ 0 ° 9 ± < µ E ¶ H º Q » T ¿ ] À ` Ä i Å l É u Ê x Î  Ï  Ó  Û              ë     Mª             -   9   E   Q   ]   i   u   » Y· M§ T» Y· M§ H» Y· M§ <» Y· M§ 0» Y· M§ $» Y· M§ » Y· M§ » Y· M,°    E   J    ä  æ 0 ê 9 ë < ï E ð H ô Q õ T ù ] ú ` þ i ÿ l u x 	 
       xuq ~~  Êþº¾   . ß 2TotalizadoresCaixaPorOperador_1610477336987_925413  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_REPORT_LOCALE 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_qtdDV parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_valorDV parameter_REPORT_TEMPLATES parameter_moeda  parameter_REPORT_RESOURCE_BUNDLE field_total .Lnet/sf/jasperreports/engine/fill/JRFillField; field_titulo field_labelTotal field_valor 	field_qtd field_descricao variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code & '
  )  	  +  	  -  	  / 	 	  1 
 	  3  	  5  	  7 
 	  9  	  ;  	  =  	  ?  	  A  	  C  	  E  	  G  	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [   !	  ] " !	  _ # !	  a $ !	  c % !	  e LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V j k
  l 
initFields n k
  o initVars q k
  r 
REPORT_LOCALE t 
java/util/Map v get &(Ljava/lang/Object;)Ljava/lang/Object; x y w z 0net/sf/jasperreports/engine/fill/JRFillParameter | qtdDV ~ 
JASPER_REPORT  REPORT_VIRTUALIZER  REPORT_TIME_ZONE  REPORT_FILE_RESOLVER  REPORT_SCRIPTLET  REPORT_PARAMETERS_MAP  REPORT_CONNECTION  REPORT_CLASS_LOADER  REPORT_DATA_SOURCE  REPORT_URL_HANDLER_FACTORY  IS_IGNORE_PAGINATION  REPORT_FORMAT_FACTORY  REPORT_MAX_COUNT  valorDV  REPORT_TEMPLATES  moeda  REPORT_RESOURCE_BUNDLE   total ¢ ,net/sf/jasperreports/engine/fill/JRFillField ¤ titulo ¦ 
labelTotal ¨ valor ª qtd ¬ 	descricao ® PAGE_NUMBER ° /net/sf/jasperreports/engine/fill/JRFillVariable ² 
COLUMN_NUMBER ´ REPORT_COUNT ¶ 
PAGE_COUNT ¸ COLUMN_COUNT º evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable ¿ java/lang/Integer Á (I)V & Ã
 Â Ä getValue ()Ljava/lang/Object; Æ Ç
 ¥ È java/lang/Boolean Ê booleanValue ()Z Ì Í
 Ë Î valueOf (Z)Ljava/lang/Boolean; Ð Ñ
 Ë Ò java/lang/Double Ô java/lang/String Ö
 } È evaluateOld getOldValue Ú Ç
 ¥ Û evaluateEstimated 
SourceFile !                      	     
               
                                                                                            !    " !    # !    $ !    % !     & '  (  /     *· **µ ,*µ .*µ 0*µ 2*µ 4*µ 6*µ 8*µ :*µ <*µ >*µ @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f±    g           	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9    h i  (   4     *+· m*,· p*-· s±    g       E  F 
 G  H  j k  (  »    W*+u¹ { À }À }µ ,*+¹ { À }À }µ .*+¹ { À }À }µ 0*+¹ { À }À }µ 2*+¹ { À }À }µ 4*+¹ { À }À }µ 6*+¹ { À }À }µ 8*+¹ { À }À }µ :*+¹ { À }À }µ <*+¹ { À }À }µ >*+¹ { À }À }µ @*+¹ { À }À }µ B*+¹ { À }À }µ D*+¹ { À }À }µ F*+¹ { À }À }µ H*+¹ { À }À }µ J*+¹ { À }À }µ L*+¹ { À }À }µ N*+¡¹ { À }À }µ P±    g   R    P  Q $ R 6 S H T Z U l V ~ W  X ¢ Y ´ Z Æ [ Ø \ ê ] ü ^ _  `2 aD bV c  n k  (        m*+£¹ { À ¥À ¥µ R*+§¹ { À ¥À ¥µ T*+©¹ { À ¥À ¥µ V*+«¹ { À ¥À ¥µ X*+­¹ { À ¥À ¥µ Z*+¯¹ { À ¥À ¥µ \±    g       k  l $ m 6 n H o Z p l q  q k  (        [*+±¹ { À ³À ³µ ^*+µ¹ { À ³À ³µ `*+·¹ { À ³À ³µ b*+¹¹ { À ³À ³µ d*+»¹ { À ³À ³µ f±    g       y  z $ { 6 | H } Z ~  ¼ ½  ¾     À (  +    GMª  B          q   }         ¡   ­   ¹   Å   Ñ   ý  )  7  c      ¹  Ç  Õ  ã  ñ  ÿ  
    )  7» ÂY· ÅM§È» ÂY· ÅM§¼» ÂY· ÅM§°» ÂY· ÅM§¤» ÂY· ÅM§» ÂY· ÅM§» ÂY· ÅM§» ÂY· ÅM§t*´ T¶ ÉÀ Ë¶ Ï *´ R¶ ÉÀ Ë¶ Ï § ¸ ÓM§H*´ R¶ ÉÀ Ë¶ Ï *´ T¶ ÉÀ Ë¶ Ï § ¸ ÓM§*´ Z¶ ÉÀ ÂM§*´ R¶ ÉÀ Ë¶ Ï *´ T¶ ÉÀ Ë¶ Ï § ¸ ÓM§ â*´ T¶ ÉÀ Ë¶ Ï *´ R¶ ÉÀ Ë¶ Ï § ¸ ÓM§ ¶*´ X¶ ÉÀ ÕM§ ¨*´ T¶ ÉÀ Ë¶ Ï § ¸ ÓM§ *´ \¶ ÉÀ ×M§ ~*´ R¶ ÉÀ ËM§ p*´ T¶ ÉÀ ËM§ b*´ \¶ ÉÀ ×M§ T*´ R¶ ÉÀ ËM§ F*´ N¶ ØÀ ×M§ 8*´ R¶ ÉÀ ËM§ **´ X¶ ÉÀ ÕM§ *´ R¶ ÉÀ ËM§ *´ V¶ ÉÀ ×M,°    g   Ò 4      t  }            ¡  ¤   ­ ¡ ° ¥ ¹ ¦ ¼ ª Å « È ¯ Ñ ° Ô ´ ý µ  ¹) º, ¾7 ¿: Ãc Äf È É Í Î  Ò¹ Ó¼ ×Ç ØÊ ÜÕ ÝØ áã âæ æñ çô ëÿ ì ð
 ñ õ ö ú) û, ÿ7 :E  Ù ½  ¾     À (  +    GMª  B          q   }         ¡   ­   ¹   Å   Ñ   ý  )  7  c      ¹  Ç  Õ  ã  ñ  ÿ  
    )  7» ÂY· ÅM§È» ÂY· ÅM§¼» ÂY· ÅM§°» ÂY· ÅM§¤» ÂY· ÅM§» ÂY· ÅM§» ÂY· ÅM§» ÂY· ÅM§t*´ T¶ ÜÀ Ë¶ Ï *´ R¶ ÜÀ Ë¶ Ï § ¸ ÓM§H*´ R¶ ÜÀ Ë¶ Ï *´ T¶ ÜÀ Ë¶ Ï § ¸ ÓM§*´ Z¶ ÜÀ ÂM§*´ R¶ ÜÀ Ë¶ Ï *´ T¶ ÜÀ Ë¶ Ï § ¸ ÓM§ â*´ T¶ ÜÀ Ë¶ Ï *´ R¶ ÜÀ Ë¶ Ï § ¸ ÓM§ ¶*´ X¶ ÜÀ ÕM§ ¨*´ T¶ ÜÀ Ë¶ Ï § ¸ ÓM§ *´ \¶ ÜÀ ×M§ ~*´ R¶ ÜÀ ËM§ p*´ T¶ ÜÀ ËM§ b*´ \¶ ÜÀ ×M§ T*´ R¶ ÜÀ ËM§ F*´ N¶ ØÀ ×M§ 8*´ R¶ ÜÀ ËM§ **´ X¶ ÜÀ ÕM§ *´ R¶ ÜÀ ËM§ *´ V¶ ÜÀ ×M,°    g   Ò 4    t }   ! % & * ¡+ ¤/ ­0 °4 ¹5 ¼9 Å: È> Ñ? ÔC ýD H)I,M7N:RcSfWX\] a¹b¼fÇgÊkÕlØpãqæuñvôzÿ{
),7:E  Ý ½  ¾     À (  +    GMª  B          q   }         ¡   ­   ¹   Å   Ñ   ý  )  7  c      ¹  Ç  Õ  ã  ñ  ÿ  
    )  7» ÂY· ÅM§È» ÂY· ÅM§¼» ÂY· ÅM§°» ÂY· ÅM§¤» ÂY· ÅM§» ÂY· ÅM§» ÂY· ÅM§» ÂY· ÅM§t*´ T¶ ÉÀ Ë¶ Ï *´ R¶ ÉÀ Ë¶ Ï § ¸ ÓM§H*´ R¶ ÉÀ Ë¶ Ï *´ T¶ ÉÀ Ë¶ Ï § ¸ ÓM§*´ Z¶ ÉÀ ÂM§*´ R¶ ÉÀ Ë¶ Ï *´ T¶ ÉÀ Ë¶ Ï § ¸ ÓM§ â*´ T¶ ÉÀ Ë¶ Ï *´ R¶ ÉÀ Ë¶ Ï § ¸ ÓM§ ¶*´ X¶ ÉÀ ÕM§ ¨*´ T¶ ÉÀ Ë¶ Ï § ¸ ÓM§ *´ \¶ ÉÀ ×M§ ~*´ R¶ ÉÀ ËM§ p*´ T¶ ÉÀ ËM§ b*´ \¶ ÉÀ ×M§ T*´ R¶ ÉÀ ËM§ F*´ N¶ ØÀ ×M§ 8*´ R¶ ÉÀ ËM§ **´ X¶ ÉÀ ÕM§ *´ R¶ ÉÀ ËM§ *´ V¶ ÉÀ ×M,°    g   Ò 4  ¤ ¦ tª }« ¯ ° ´ µ ¹ ¡º ¤¾ ­¿ °Ã ¹Ä ¼È ÅÉ ÈÍ ÑÎ ÔÒ ýÓ ×)Ø,Ü7Ý:ácâfæçëì ð¹ñ¼õÇöÊúÕûØÿã æñô	ÿ

),7:"E*  Þ    t _1610477336987_925413t 2net.sf.jasperreports.engine.design.JRJavacCompiler