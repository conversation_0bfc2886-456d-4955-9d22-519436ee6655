¬í sr (net.sf.jasperreports.engine.JasperReport      'Ø L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      'Ø +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultFontt *Lnet/sf/jasperreports/engine/JRReportFont;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;[ fontst +[Lnet/sf/jasperreports/engine/JRReportFont;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  wî             N           J  S          pppppppsr .net.sf.jasperreports.engine.base.JRBaseSection      'Ø [ bandst %[Lnet/sf/jasperreports/engine/JRBand;xpur %[Lnet.sf.jasperreports.engine.JRBand;Ý~ìÊ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      'Ø L childrent Ljava/util/List;L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListxÒÇa I sizexp   lw   lsr +net.sf.jasperreports.engine.base.JRBaseLine      'Ø I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      'Ø I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;L penq ~ xr .net.sf.jasperreports.engine.base.JRBaseElement      'Ø I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 'L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;[ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;xp  wî                 spq ~ q ~ pt line-20pppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xr java.lang.Enum          xpt FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ 2t 
NO_STRETCH  wîppsr *net.sf.jasperreports.engine.base.JRBasePen      'Ø I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 'L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  wîpp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ 2t DASHEDsr java.lang.FloatÚíÉ¢Û<ðì F valuexr java.lang.Number¬à  xp?  q ~ /p  wî ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ 2t TOP_DOWNsq ~ !  wî          
      pq ~ q ~ pt line-1ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîpp~q ~ =t SOLIDsq ~ @?  q ~ Fp  wî q ~ Dsq ~ !  wî              x   vpq ~ q ~ pt line-2ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Lp  wî q ~ Dsq ~ !  wî                 vpq ~ q ~ pt line-3ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Pp  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-4ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Tp  wî q ~ Dsq ~ !  wî                 ©pq ~ q ~ pt line-5ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ Xp  wî q ~ Dsq ~ !  wî                 ºpq ~ q ~ pt line-6ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ \p  wî q ~ Dsq ~ !  wî                 Ëpq ~ q ~ pt line-7ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ `p  wî q ~ Dsq ~ !  wî             y   pq ~ q ~ pt line-8ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ dp  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-9ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ hp  wî q ~ Dsq ~ !  wî             z   Üpq ~ q ~ pt line-10ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ lp  wî q ~ Dsq ~ !  wî             z   ípq ~ q ~ pt line-11ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ pp  wî q ~ Dsq ~ !  wî             z   þpq ~ q ~ pt line-12ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ tp  wî q ~ Dsq ~ !  wî             y  pq ~ q ~ pt line-13ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ xp  wî q ~ Dsq ~ !  wî   "           L   ©pq ~ q ~ pt line-14ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ |p  wî q ~ Dsq ~ !  wî   "              ©pq ~ q ~ pt line-15ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              |   ºpq ~ q ~ pt line-16ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî   "           í   ©pq ~ q ~ pt line-17ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî              Ñ   ©pq ~ q ~ pt line-18ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsq ~ !  wî                Kpq ~ q ~ pt line-19ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~ p  wî q ~ Dsr 1net.sf.jasperreports.engine.base.JRBaseStaticText      'Ø L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      'Ø %I PSEUDO_SERIAL_VERSION_UIDL borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingt Ljava/lang/Integer;L fontNameq ~ L fontSizeq ~ L horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ L 
isPdfEmbeddedq ~ L isStrikeThroughq ~ L isStyledTextq ~ L isUnderlineq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L paddingq ~ L pdfEncodingq ~ L pdfFontNameq ~ L 
reportFontq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;xq ~ &  wî           B      pq ~ q ~ pt staticText-1ppppq ~ 3ppppq ~ 6  wîpppppt Arialsr java.lang.Integerâ ¤÷8 I valuexq ~ A   ppsr java.lang.BooleanÍ rÕúî Z valuexp ppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      'Ø L 
bottomPaddingq ~ L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ L leftPenq ~ ¥L paddingq ~ L penq ~ ¥L rightPaddingq ~ L rightPenq ~ ¥L 
topPaddingq ~ L topPenq ~ ¥xppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      'Ø  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      'Ø L lineBoxq ~ xq ~ 8  wîsr java.awt.Color¥3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ ­xp    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §q ~ psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psq ~ ©  wîppppq ~ §q ~ §psr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §psr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      'Ø  xq ~ ©  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ §q ~ §pppppt 	Helveticapppppppppp~r 2net.sf.jasperreports.engine.type.VerticalAlignEnum          xq ~ 2t MIDDLEt Local de pagamentosq ~   wî           B      pq ~ q ~ pt staticText-2ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åq ~ Âpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ©  wîppppq ~ Åq ~ Åpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Åq ~ Åpppppt 	Helveticappppppppppq ~ ¿t 
BeneficiÃ¡riosq ~   wî           B      ©pq ~ q ~ pt staticText-3ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øq ~ Õpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ©  wîppppq ~ Øq ~ Øpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ Øq ~ Øpppppt 	Helveticappppppppppq ~ ¿t Data do Documentosq ~   wî           K      ºpq ~ q ~ pt staticText-4ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëq ~ èpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ©  wîppppq ~ ëq ~ ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ ëq ~ ëpppppt 	Helveticappppppppppq ~ ¿t NÂº da Conta / Respons.sq ~   wî           K      Ípq ~ q ~ pt staticText-5ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þq ~ ûpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ©  wîppppq ~ þq ~ þpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ þq ~ þpppppt 	Helveticappppppppppq ~ ¿t InstruÃ§Ãµes :sq ~   wî           B   O   ©pq ~ q ~ pt staticText-6ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~   wî           *      ©pq ~ q ~ pt staticText-7ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~!psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt 	Helveticappppppppppq ~ ¿t 
EspÃ©cie Doc.sq ~   wî              Ô   ©pq ~ q ~ pt staticText-8ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7q ~4psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ©  wîppppq ~7q ~7psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~7q ~7pppppt 	Helveticappppppppppq ~ ¿t Aceitesq ~   wî              ð   ©pq ~ q ~ pt staticText-9ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jq ~Gpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ©  wîppppq ~Jq ~Jpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Jq ~Jpppppt 	Helveticappppppppppq ~ ¿t Data do Processamentosq ~   wî           #   O   ºpq ~ q ~ pt 
staticText-10ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]q ~Zpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ©  wîppppq ~]q ~]psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~]q ~]pppppt 	Helveticappppppppppq ~ ¿t Carteirasq ~   wî              ~   ºpq ~ q ~ pt 
staticText-11ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~pq ~mpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ©  wîppppq ~pq ~ppsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~pq ~ppppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           B      ºpq ~ q ~ pt 
staticText-12ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~   wî           B   ð   ºpq ~ q ~ pt 
staticText-13ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t Valorsq ~   wî           d  |   pq ~ q ~ pt 
staticText-14ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©q ~¦psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ©  wîppppq ~©q ~©psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~©q ~©pppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d  |   pq ~ q ~ pt 
staticText-15ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼q ~¹psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¼q ~¼pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~   wî           d  |  pq ~ q ~ pt 
staticText-16ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïq ~Ìpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ©  wîppppq ~Ïq ~Ïpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Ïq ~Ïpppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           d  |   þpq ~ q ~ pt 
staticText-17ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âq ~ßpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ©  wîppppq ~âq ~âpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~âq ~âpppppt 	Helveticappppppppppq ~ ¿t (=) Outros acrÃ©scimossq ~   wî           d  |   ípq ~ q ~ pt 
staticText-18ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õq ~òpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ©  wîppppq ~õq ~õpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~õq ~õpppppt 	Helveticappppppppppq ~ ¿t (+) Juros / Multasq ~   wî           d  |   Üpq ~ q ~ pt 
staticText-19ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (-) Outros deduÃ§Ãµessq ~   wî           d  |   Ëpq ~ q ~ pt 
staticText-20ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           d  |   ºpq ~ q ~ pt 
staticText-21ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.q ~+psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ©  wîppppq ~.q ~.psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~.q ~.pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           d  |   ©pq ~ q ~ pt 
staticText-22ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Aq ~>psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ©  wîppppq ~Aq ~Apsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Aq ~Apppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           !     "pq ~ q ~ pt 
staticText-23ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tq ~Qpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ ¿t Pagador:sq ~   wî           A     @pq ~ q ~ pt 
staticText-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gq ~dpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ©  wîppppq ~gq ~gpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~gq ~gpppppt 	Helveticappppppppppq ~ ¿t Sacador / Avalista :sq ~   wî           9  e  @pq ~ q ~ pt 
staticText-25ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zq ~wpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ©  wîppppq ~zq ~zpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~zq ~zpppppt 	Helveticappppppppppq ~ ¿t CÃ³digo de baixasq ~   wî           D  L  Mpq ~ q ~ pt 
staticText-26ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t AutenticaÃ§Ã£o mecÃ¢nicasq ~   wî           j    Mpq ~ q ~ pt 
staticText-27ppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     	ppsq ~ ¢ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ©  wîppppq ~¢q ~¢psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¢q ~¢pppppt Helvetica-Boldppppppppppq ~ ¿t Ficha de CompensaÃ§Ã£osq ~   wî              ê   Àpq ~ q ~ pt 
staticText-28p~r )net.sf.jasperreports.engine.type.ModeEnum          xq ~ 2t OPAQUEppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡p~r 4net.sf.jasperreports.engine.type.HorizontalAlignEnum          xq ~ 2t CENTERq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»q ~²psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ©  wîppppq ~»q ~»psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~»q ~»pppppt Helvetica-Boldppppppppppq ~ ¿t Xsr ,net.sf.jasperreports.engine.base.JRBaseImage      'Ø *I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L borderq ~ L borderColorq ~ 'L bottomBorderq ~ L bottomBorderColorq ~ 'L 
bottomPaddingq ~ L evaluationGroupq ~ +L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isUsingCacheq ~ L 
leftBorderq ~ L leftBorderColorq ~ 'L leftPaddingq ~ L lineBoxq ~ L 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L paddingq ~ L rightBorderq ~ L rightBorderColorq ~ 'L rightPaddingq ~ L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L 	topBorderq ~ L topBorderColorq ~ 'L 
topPaddingq ~ L verticalAlignmentq ~ L verticalAlignmentValueq ~ xq ~ #  wî   #       ?     Msq ~ «    ÿÿÿÿpppq ~ q ~ sq ~ «    ÿ   pppt 	barcode-1pq ~µppq ~ 3ppppq ~ 6  wîp~r )net.sf.jasperreports.engine.type.FillEnum          xq ~ 2t SOLIDsq ~ 8  wîppq ~ Isq ~ @    q ~Ðp  wî         ppppppp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ 2t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      'Ø I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mYÏÞiK£U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      'Ø B typeL textq ~ xpt <it.businesslogic.ireport.barcode.BcImage.getBarcodeImage(13,sq ~át banco.codigoBarrassq ~át ,false,false,null,0,0)t java.awt.Imagepp~q ~¸t LEFTpppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëq ~Ðpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ©  wîppppq ~ëq ~ëpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ëq ~ëpp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ 2t ERRORppppp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ 2t 
FILL_FRAMEpppp~q ~ ¾t TOPsq ~ !  wî          	      pq ~ q ~ pt line-24ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî                 pq ~ q ~ pt line-25ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî              y   pq ~ q ~ pt line-26ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~	p  wî q ~ Dsq ~   wî           d      cpq ~ q ~ pt 
staticText-29ppppq ~ 3ppppq ~ 6  wîppppppq ~ pq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~
psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt Helvetica-Boldppppppppppq ~ÿt Recibo do Pagadorsr 0net.sf.jasperreports.engine.base.JRBaseTextField      'Ø I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ +L evaluationTimeValueq ~ÌL 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ÍL hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L isBlankWhenNullq ~ L 
linkTargetq ~ L linkTypeq ~ L patternq ~ xq ~   wî             {   pq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîpppppppp~q ~¸t RIGHTq ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$q ~ psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ©  wîppppq ~$q ~$psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~$q ~$pppppt Helvetica-Boldppppppppppq ~ÿ  wî       ppq ~Úsq ~Ü   	uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~ !  wî             @   pq ~ q ~ pt line-30ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~8p  wî q ~ Dsq ~ !  wî              W   pq ~ q ~ pt line-31ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~<p  wî q ~ Dsq ~ !  wî          	      'pq ~ q ~ pt line-32ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~@p  wî q ~ Dsq ~ !  wî             ¨   pq ~ q ~ pt line-33ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Dp  wî q ~ Dsq ~   wî           .      pq ~ q ~ pt 
staticText-31ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Kq ~Kq ~Hpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Kq ~Kpsq ~ ©  wîppppq ~Kq ~Kpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Kq ~Kpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Kq ~Kpppppt 	Helveticappppppppppq ~ ¿t 
Vencimentosq ~   wî           d   Y   pq ~ q ~ pt 
staticText-32ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^q ~[psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^psq ~ ©  wîppppq ~^q ~^psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~^q ~^pppppt 	Helveticappppppppppq ~ ¿t  AgÃªncia / CÃ³digo BeneficiÃ¡riosq ~   wî             C   pq ~ q ~ pt 
staticText-33ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qq ~npsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpsq ~ ©  wîppppq ~qq ~qpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~qq ~qpppppt 	Helveticappppppppppq ~ ¿t EspÃ©ciesq ~   wî           /  «   pq ~ q ~ pt 
staticText-34ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t 
Quantidadesq ~ !  wî          	      8pq ~ q ~ pt line-39ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî              W   'pq ~ q ~ pt line-40ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~ !  wî             @   'pq ~ q ~ pt line-41ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~   wî           R      'pq ~ q ~ pt 
staticText-39ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£q ~ psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£psq ~ ©  wîppppq ~£q ~£psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~£q ~£pppppt 	Helveticappppppppppq ~ ¿t (=) Valor do Documentosq ~   wî           P   Z   'pq ~ q ~ pt 
staticText-40ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶q ~³psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶psq ~ ©  wîppppq ~¶q ~¶psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~¶q ~¶pppppt 	Helveticappppppppppq ~ ¿t (-) Desconto / Abatimentosq ~   wî           R  C   'pq ~ q ~ pt 
staticText-41ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Éq ~Æpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Épsq ~ ©  wîppppq ~Éq ~Épsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Épsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Éq ~Épppppt 	Helveticappppppppppq ~ ¿t (+) Juros / Multasq ~ !  wî              W   8pq ~ q ~ pt line-42ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Ùp  wî q ~ Dsq ~ !  wî          	      Ipq ~ q ~ pt line-43ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Ýp  wî q ~ Dsq ~ !  wî             @   8pq ~ q ~ pt line-44ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~áp  wî q ~ Dsq ~   wî           R      8pq ~ q ~ pt 
staticText-42ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~èq ~èq ~åpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~èq ~èpsq ~ ©  wîppppq ~èq ~èpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~èq ~èpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~èq ~èpppppt 	Helveticappppppppppq ~ ¿t (=) Valor cobradosq ~   wî           P   Z   8pq ~ q ~ pt 
staticText-43ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ûq ~ûq ~øpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ûq ~ûpsq ~ ©  wîppppq ~ûq ~ûpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ûq ~ûpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~ûq ~ûpppppt 	Helveticappppppppppq ~ ¿t 
Nosso NÃºmerosq ~   wî           B  C   8pq ~ q ~ pt 
staticText-44ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~pppppt 	Helveticappppppppppq ~ ¿t NÂº do Documentosq ~ !  wî          
      Upq ~ q ~ pt line-47ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~p  wî q ~ Dsq ~   wî           X      Ipq ~ q ~ pt 
staticText-46ppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&q ~"psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&psq ~ ©  wîppppq ~&q ~&psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~&q ~&pppppt 	Helveticappppppppppq ~ ¿t  BeneficiÃ¡rio/CPF/CNPJ/EndereÃ§osq ~   wî           D   `   dpq ~ q ~ pt 
staticText-49ppppq ~ 3ppppq ~ 6  wîpppppt 	SansSerifq ~%pq ~¹q ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9q ~6psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9psq ~ ©  wîppppq ~9q ~9psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~9q ~9pppppt 	Helveticappppppppppq ~ÿt AutenticaÃ§Ã£o mecÃ¢nicasq ~ !  wî                pq ~ q ~ pt line-50ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ >sq ~ @?  q ~Ip  wî q ~ Dsq ~ !  wî          
      cpq ~ q ~ pt line-51ppppq ~ 3ppppq ~ 6  wîppsq ~ 8  wîppq ~ Isq ~ @?  q ~Mp  wî q ~ Dsq ~   wî           !      Vpq ~ q ~ pt 
staticText-51ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~%ppq ~ £ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tq ~Qpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ©  wîppppq ~Tq ~Tpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Tq ~Tpppppt 	Helveticappppppppppq ~ ¿t Pagadorsq ~  wî             {   vpq ~ q ~ pt textField-35ppppq ~ 3ppppq ~ 6  wîppppppppq ~"q ~¡ppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fq ~dpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fpsq ~ ©  wîppppq ~fq ~fpsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fpsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~fq ~fpppppt Helvetica-Boldppppppppppq ~ÿ  wî       ppq ~Úsq ~Ü   
uq ~ß   sq ~át banco.numeroFormattedt java.lang.Stringppppppq ~¡pppsq ~  wî          p      vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîppppq ~{q ~{q ~zpsq ~ °  wîppppq ~{q ~{psq ~ ©  wîppppq ~{q ~{psq ~ µ  wîppppq ~{q ~{psq ~ ¹  wîppppq ~{q ~{pppppt Helvetica-Boldppppppppppp  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.linhaDigitavelt java.lang.Stringppppppppppsq ~  wî   	             Lpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~pppppppppppppppp~q ~ ¾t BOTTOM  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.cedentesq ~át  + " / " + sq ~át cnpjEmpresasq ~át  + " / " + sq ~át enderecoEmpresat java.lang.Stringppppppppppsq ~  wî   	       t      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialsq ~     pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~¡q ~¡q ~psq ~ °  wîppppq ~¡q ~¡psq ~ ©  wîppppq ~¡q ~¡psq ~ µ  wîppppq ~¡q ~¡psq ~ ¹  wîppppq ~¡q ~¡ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   
uq ~ß   sq ~át boleto.localPagamentot java.lang.Stringppppppq ~¡pppsq ~  wî   	        B      ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~®q ~®q ~¬psq ~ °  wîppppq ~®q ~®psq ~ ©  wîppppq ~®q ~®psq ~ µ  wîppppq ~®q ~®psq ~ ¹  wîppppq ~®q ~®ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataDocumentot java.lang.Stringppppppq ~¡ppt  sq ~  wî   	        3      ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~¼q ~¼q ~ºpsq ~ °  wîppppq ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîppppq ~¼q ~¼psq ~ ¹  wîppppq ~¼q ~¼ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.especieDocumentot java.lang.Stringppppppppppsq ~  wî   	           Ó   ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~Éq ~Éq ~Çpsq ~ °  wîppppq ~Éq ~Épsq ~ ©  wîppppq ~Éq ~Épsq ~ µ  wîppppq ~Éq ~Épsq ~ ¹  wîppppq ~Éq ~Éppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át 
boleto.aceitet java.lang.Stringppppppppppsq ~  wî   	           ð   ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Öq ~Öq ~Ôpsq ~ °  wîppppq ~Öq ~Öpsq ~ ©  wîppppq ~Öq ~Öpsq ~ µ  wîppppq ~Öq ~Öpsq ~ ¹  wîppppq ~Öq ~Öppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataProcessamentot java.lang.Stringppppppppppsq ~  wî   	        (   O   Âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ãq ~ãq ~ápsq ~ °  wîppppq ~ãq ~ãpsq ~ ©  wîppppq ~ãq ~ãpsq ~ µ  wîppppq ~ãq ~ãpsq ~ ¹  wîppppq ~ãq ~ãppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.carteirat java.lang.Stringppppppppppsq ~  wî   	        P  ²   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ðq ~ðq ~îpsq ~ °  wîppppq ~ðq ~ðpsq ~ ©  wîppppq ~ðq ~ðpsq ~ µ  wîppppq ~ðq ~ðpsq ~ ¹  wîppppq ~ðq ~ðppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~  wî   	        P  ²   Âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîppppq ~ýq ~ýq ~ûpsq ~ °  wîppppq ~ýq ~ýpsq ~ ©  wîppppq ~ýq ~ýpsq ~ µ  wîppppq ~ýq ~ýpsq ~ ¹  wîppppq ~ýq ~ýppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~  wî   	        .      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~épppppppppsq ~ ¤psq ~ ¨  wîppppq ~
q ~
q ~psq ~ °  wîppppq ~
q ~
psq ~ ©  wîppppq ~
q ~
psq ~ µ  wîppppq ~
q ~
psq ~ ¹  wîppppq ~
q ~
ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.dataVencimentot java.lang.Stringppppppppppsq ~  wî   	        J      /pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.valorBoletot java.lang.Stringppppppppppsq ~  wî   	             °pq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~%q ~%q ~"psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~%q ~%psq ~ ©  wîppppq ~%q ~%psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~%q ~%psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~%q ~%ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.numConveniosq ~át +""+sq ~át boleto.nossoNumerot java.lang.Stringppppppq ~¡ppq ~¹sq ~  wî   	        P  ²    pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîppppq ~>q ~>q ~<psq ~ °  wîppppq ~>q ~>psq ~ ©  wîppppq ~>q ~>psq ~ µ  wîppppq ~>q ~>psq ~ ¹  wîppppq ~>q ~>ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~  wî                 [pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~Kq ~Kq ~Ipsq ~ °  wîppppq ~Kq ~Kpsq ~ ©  wîppppq ~Kq ~Kpsq ~ µ  wîppppq ~Kq ~Kpsq ~ ¹  wîppppq ~Kq ~Kppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.nomeSacadot java.lang.Stringppppppppppsq ~  wî   F       p      Õpq ~ q ~ pt textField-24ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~épppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Yq ~Yq ~Vpsq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Yq ~Ypsq ~ ©  wîppppq ~Yq ~Ypsq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Yq ~Ypsq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~Yq ~Yppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.instrucao1t java.lang.Stringppppppq ~¡pppsq ~  wî           w      vpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~nq ~nq ~lpsq ~ °  wîppppq ~nq ~npsq ~ ©  wîppppq ~nq ~npsq ~ µ  wîppppq ~nq ~npsq ~ ¹  wîppppq ~nq ~nppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.bancot java.lang.Stringppppppppppsq ~  wî           w      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialppppppppppppsq ~ ¤psq ~ ¨  wîppppq ~{q ~{q ~ypsq ~ °  wîppppq ~{q ~{psq ~ ©  wîppppq ~{q ~{psq ~ µ  wîppppq ~{q ~{psq ~ ¹  wîppppq ~{q ~{ppppppppppppppppq ~ÿ  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át banco.bancot java.lang.Stringppppppppppsq ~  wî   	              Âpq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át "R$"t java.lang.Stringppppppppppsq ~  wî   	        C   N   ±pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~¹pppppppppsq ~ ¤psq ~ ¨  wîppppq ~q ~q ~psq ~ °  wîppppq ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîppppq ~q ~psq ~ ¹  wîppppq ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át boleto.noDocumentot java.lang.Stringppppppppppsq ~  wî   	        Z   Y   pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîppppq ~¢q ~¢q ~ psq ~ °  wîppppq ~¢q ~¢psq ~ ©  wîppppq ~¢q ~¢psq ~ µ  wîppppq ~¢q ~¢psq ~ ¹  wîppppq ~¢q ~¢ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   uq ~ß   sq ~át  banco.agenciaCodCedenteFormattedt java.lang.Stringppppppppppsq ~  wî          p      pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîppppppppq ~¹q ~¡ppppppppsq ~ ¤psq ~ ¨  wîppppq ~®q ~®q ~­psq ~ °  wîppppq ~®q ~®psq ~ ©  wîppppq ~®q ~®psq ~ µ  wîppppq ~®q ~®psq ~ ¹  wîppppq ~®q ~®pppppt Helvetica-Boldppppppppppp  wî        ppq ~Úsq ~Ü    uq ~ß   sq ~át banco.linhaDigitavelt java.lang.Stringppppppppppsq ~  wî   	       Ý   "  "pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~¼q ~¼q ~ºpsq ~ °  wîppppq ~¼q ~¼psq ~ ©  wîppppq ~¼q ~¼psq ~ µ  wîppppq ~¼q ~¼psq ~ ¹  wîppppq ~¼q ~¼ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   !uq ~ß   sq ~át ((sq ~át boleto.responsavelsq ~át  != null &&  !sq ~át boleto.responsavelsq ~át .isEmpty()) ? sq ~át boleto.responsavelsq ~át  :  sq ~át boleto.nomeSacadosq ~át 
) + " / " + (sq ~át boleto.cpfSacadosq ~át A.replace(".","").replace("-","").length()<12?"CPF: ":"CNPJ: ") + sq ~át boleto.cpfSacadot java.lang.Stringppppppppppsq ~  wî   	       Ý   "  +pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~ßq ~ßq ~Ýpsq ~ °  wîppppq ~ßq ~ßpsq ~ ©  wîppppq ~ßq ~ßpsq ~ µ  wîppppq ~ßq ~ßpsq ~ ¹  wîppppq ~ßq ~ßppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   "uq ~ß   sq ~át boleto.enderecoSacadot java.lang.Stringppppppppppsq ~  wî   	       Ý   "  4pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~ìq ~ìq ~êpsq ~ °  wîppppq ~ìq ~ìpsq ~ ©  wîppppq ~ìq ~ìpsq ~ µ  wîppppq ~ìq ~ìpsq ~ ¹  wîppppq ~ìq ~ìppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   #uq ~ß   
sq ~át (sq ~át boleto.cidadeSacadosq ~át 
==null?"":sq ~át boleto.cidadeSacadosq ~át .trim() + "  " ) + ( sq ~át boleto.ufSacadosq ~át ==null? "" : ( sq ~át boleto.ufSacadosq ~át )+" "+(sq ~át boleto.cepSacadosq ~át .trim() != null ? "CEP: "+sq ~át boleto.cepSacadosq ~át :""))t java.lang.Stringppppppq ~¡pppsq ~  wî   	              @pq ~ q ~ pt textField-49ppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ pq ~"pppppppppsq ~ ¤psq ~ ¨  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~q ~psq ~ °  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ©  wîppppq ~q ~psq ~ µ  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~psq ~ ¹  wîsq ~ «    ÿ   ppppq ~ Isq ~ @    q ~q ~ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   $uq ~ß   sq ~át boleto.numConveniosq ~át +""+sq ~át boleto.nossoNumerot java.lang.Stringppppppq ~¡ppq ~¹sq ~  wî   	              pq ~ q ~ ppppppq ~ 3ppppq ~ 6  wîpppppt Arialq ~ ¡pppppppppppsq ~ ¤psq ~ ¨  wîppppq ~+q ~+q ~)psq ~ °  wîppppq ~+q ~+psq ~ ©  wîppppq ~+q ~+psq ~ µ  wîppppq ~+q ~+psq ~ ¹  wîppppq ~+q ~+ppppppppppppppppq ~  wî        ppq ~Úsq ~Ü   %uq ~ß   sq ~át boleto.cedentesq ~át  + " / " + sq ~át cnpjEmpresasq ~át  + " / " + sq ~át enderecoEmpresat java.lang.Stringppppppppppxp  wî  pp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ 2t STRETCHppsr java.util.HashSetºD¸·4  xpw   ?@     t "net.sf.jasperreports.engine.data.*t net.sf.jasperreports.engine.*t java.util.*xt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      'Ø I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ ,L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  wî ur &[Lnet.sf.jasperreports.engine.JRField;<ßÇN*òp  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      'Ø L descriptionq ~ L nameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xppt bancosr +net.sf.jasperreports.engine.JRPropertiesMap      'Ø L baseq ~ ,L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppppt java.lang.Objectpsq ~Spt banco.numeroFormattedsq ~Vpppt java.lang.Stringpsq ~Spt banco.linhaDigitavelsq ~Vpppt java.lang.Stringpsq ~Spt banco.codigoBarrassq ~Vpppt java.lang.Stringpsq ~Spt boleto.cedentesq ~Vpppt java.lang.Stringpsq ~Spt boleto.localPagamentosq ~Vpppt java.lang.Stringpsq ~Spt boleto.dataDocumentosq ~Vpppt java.lang.Stringpsq ~Spt boleto.especieDocumentosq ~Vpppt java.lang.Stringpsq ~Spt 
boleto.aceitesq ~Vpppt java.lang.Stringpsq ~Spt boleto.dataProcessamentosq ~Vpppt java.lang.Stringpsq ~Spt boleto.carteirasq ~Vpppt java.lang.Stringpsq ~Spt boleto.dataVencimentosq ~Vpppt java.lang.Stringpsq ~Spt boleto.valorBoletosq ~Vpppt java.lang.Stringpsq ~Spt boleto.nomeSacadosq ~Vpppt java.lang.Stringpsq ~Spt boleto.cpfSacadosq ~Vpppt java.lang.Stringpsq ~Spt boleto.nossoNumerosq ~Vpppt java.lang.Stringpsq ~Spt boleto.enderecoSacadosq ~Vpppt java.lang.Stringpsq ~Spt boleto.cepSacadosq ~Vpppt java.lang.Stringpsq ~Spt boleto.cidadeSacadosq ~Vpppt java.lang.Stringpsq ~Spt boleto.ufSacadosq ~Vpppt java.lang.Stringpsq ~Spt  banco.agenciaCodCedenteFormattedsq ~Vpppt java.lang.Stringpsq ~Spt boleto.instrucao1sq ~Vpppt java.lang.Stringpsq ~Spt banco.bancosq ~Vpppt java.lang.Stringpsq ~Spt banco.nossoNumeroFormattedsq ~Vpppt java.lang.Stringpsq ~Spt boleto.dvNossoNumerosq ~Vpppt java.lang.Stringpsq ~Spt boleto.noDocumentosq ~Vpppt java.lang.Stringpsq ~St boleto.responsavelt boleto.responsavelsq ~Vpppt java.lang.Stringpsq ~Spt boleto.bairroSacadosq ~Vpppt java.lang.Stringpsq ~Spt boleto.numConveniosq ~Vpppt java.lang.Stringpppt boletour *[Lnet.sf.jasperreports.engine.JRParameter;" *Ã`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      'Ø 	Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ ,L valueClassNameq ~ L valueClassRealNameq ~ xpppt REPORT_PARAMETERS_MAPpsq ~Vpppt 
java.util.Mappsq ~Îppt 
JASPER_REPORTpsq ~Vpppt (net.sf.jasperreports.engine.JasperReportpsq ~Îppt REPORT_CONNECTIONpsq ~Vpppt java.sql.Connectionpsq ~Îppt REPORT_MAX_COUNTpsq ~Vpppt java.lang.Integerpsq ~Îppt REPORT_DATA_SOURCEpsq ~Vpppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Îppt REPORT_SCRIPTLETpsq ~Vpppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Îppt 
REPORT_LOCALEpsq ~Vpppt java.util.Localepsq ~Îppt REPORT_RESOURCE_BUNDLEpsq ~Vpppt java.util.ResourceBundlepsq ~Îppt REPORT_TIME_ZONEpsq ~Vpppt java.util.TimeZonepsq ~Îppt REPORT_FORMAT_FACTORYpsq ~Vpppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Îppt REPORT_CLASS_LOADERpsq ~Vpppt java.lang.ClassLoaderpsq ~Îppt REPORT_URL_HANDLER_FACTORYpsq ~Vpppt  java.net.URLStreamHandlerFactorypsq ~Îppt REPORT_FILE_RESOLVERpsq ~Vpppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Îppt REPORT_TEMPLATESpsq ~Vpppt java.util.Collectionpsq ~Îppt REPORT_VIRTUALIZERpsq ~Vpppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Îppt IS_IGNORE_PAGINATIONpsq ~Vpppt java.lang.Booleanpsq ~Î ppt cnpjEmpresapsq ~Vpppt java.lang.Stringpsq ~Î ppt enderecoEmpresapsq ~Vpppt java.lang.Stringpsq ~Vpsq ~    w   t ireport.scriptlethandlingt ireport.encodingt ireport.zoomt 	ireport.xt 	ireport.yxsr java.util.HashMapÚÁÃ`Ñ F 
loadFactorI 	thresholdxp?@     w      q ~t 1.6934217901613318q ~t UTF-8q ~t 0q ~t 0q ~t 0xsr ,net.sf.jasperreports.engine.base.JRBaseQuery      'Ø [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppur )[Lnet.sf.jasperreports.engine.JRVariable;bæ|,·D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      'Ø I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ +L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ +L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  wî   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ 2t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ 2t NONEppsq ~Ü    uq ~ß   sq ~át new java.lang.Integer(1)q ~Þpt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ 2t REPORTq ~Þpsq ~+  wî   q ~1ppq ~4ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~Þpt 
COLUMN_NUMBERp~q ~;t PAGEq ~Þpsq ~+  wî   ~q ~0t COUNTsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~Þppq ~4ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~Þpt REPORT_COUNTpq ~<q ~Þpsq ~+  wî   q ~Gsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~Þppq ~4ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~Þpt 
PAGE_COUNTpq ~Dq ~Þpsq ~+  wî   q ~Gsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(1)q ~Þppq ~4ppsq ~Ü   uq ~ß   sq ~át new java.lang.Integer(0)q ~Þpt COLUMN_COUNTp~q ~;t COLUMNq ~Þp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ 2t NULLq ~Ëp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ 2t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ 2t VERTICALpppp~r 3net.sf.jasperreports.engine.type.WhenNoDataTypeEnum          xq ~ 2t NO_PAGESsr 6net.sf.jasperreports.engine.design.JRReportCompileData      'Ø L crosstabCompileDataq ~WL datasetCompileDataq ~WL mainDatasetCompileDataq ~ xpsq ~?@     w       xsq ~?@     w       xur [B¬óøTà  xp  -Êþº¾   .{ boleto_1569354279803_352825  ,net/sf/jasperreports/engine/fill/JREvaluator  parameter_enderecoEmpresa 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_LOCALE parameter_JASPER_REPORT parameter_REPORT_VIRTUALIZER parameter_REPORT_TIME_ZONE parameter_REPORT_FILE_RESOLVER parameter_REPORT_SCRIPTLET parameter_REPORT_PARAMETERS_MAP parameter_REPORT_CONNECTION parameter_REPORT_CLASS_LOADER parameter_REPORT_DATA_SOURCE $parameter_REPORT_URL_HANDLER_FACTORY parameter_IS_IGNORE_PAGINATION parameter_REPORT_FORMAT_FACTORY parameter_REPORT_MAX_COUNT parameter_REPORT_TEMPLATES parameter_cnpjEmpresa  parameter_REPORT_RESOURCE_BUNDLE field_boleto46cedente .Lnet/sf/jasperreports/engine/fill/JRFillField; !field_banco46nossoNumeroFormatted field_boleto46enderecoSacado field_boleto46dvNossoNumero field_banco46codigoBarras 'field_banco46agenciaCodCedenteFormatted field_boleto46nomeSacado field_boleto46aceite field_banco46banco field_boleto46valorBoleto field_boleto46especieDocumento field_banco46numeroFormatted field_banco field_boleto46dataVencimento field_boleto46numConvenio field_boleto46dataProcessamento field_boleto46ufSacado field_boleto46bairroSacado field_boleto46cepSacado field_boleto46dataDocumento field_banco46linhaDigitavel field_boleto46nossoNumero field_boleto46responsavel field_boleto46noDocumento field_boleto46localPagamento field_boleto46cpfSacado field_boleto46cidadeSacado field_boleto46carteira field_boleto46instrucao1 variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code < =
  ?  	  A  	  C  	  E 	 	  G 
 	  I  	  K  	  M 
 	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a  	  c  	  e  	  g  	  i  	  k  	  m  	  o  	  q   	  s ! 	  u " 	  w # 	  y $ 	  { % 	  } & 	   ' 	   ( 	   ) 	   * 	   + 	   , 	   - 	   . 	   / 	   0 	   1 	   2 	   3 	   4 	   5 	   6 7	   8 7	  ¡ 9 7	  £ : 7	  ¥ ; 7	  § LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V 
initParams (Ljava/util/Map;)V ¬ ­
  ® 
initFields ° ­
  ± initVars ³ ­
  ´ enderecoEmpresa ¶ 
java/util/Map ¸ get &(Ljava/lang/Object;)Ljava/lang/Object; º » ¹ ¼ 0net/sf/jasperreports/engine/fill/JRFillParameter ¾ 
REPORT_LOCALE À 
JASPER_REPORT Â REPORT_VIRTUALIZER Ä REPORT_TIME_ZONE Æ REPORT_FILE_RESOLVER È REPORT_SCRIPTLET Ê REPORT_PARAMETERS_MAP Ì REPORT_CONNECTION Î REPORT_CLASS_LOADER Ð REPORT_DATA_SOURCE Ò REPORT_URL_HANDLER_FACTORY Ô IS_IGNORE_PAGINATION Ö REPORT_FORMAT_FACTORY Ø REPORT_MAX_COUNT Ú REPORT_TEMPLATES Ü cnpjEmpresa Þ REPORT_RESOURCE_BUNDLE à boleto.cedente â ,net/sf/jasperreports/engine/fill/JRFillField ä banco.nossoNumeroFormatted æ boleto.enderecoSacado è boleto.dvNossoNumero ê banco.codigoBarras ì  banco.agenciaCodCedenteFormatted î boleto.nomeSacado ð 
boleto.aceite ò banco.banco ô boleto.valorBoleto ö boleto.especieDocumento ø banco.numeroFormatted ú banco ü boleto.dataVencimento þ boleto.numConvenio  boleto.dataProcessamento boleto.ufSacado boleto.bairroSacado boleto.cepSacado boleto.dataDocumento
 banco.linhaDigitavel boleto.nossoNumero boleto.responsavel boleto.noDocumento boleto.localPagamento boleto.cpfSacado boleto.cidadeSacado boleto.carteira boleto.instrucao1 PAGE_NUMBER /net/sf/jasperreports/engine/fill/JRFillVariable  
COLUMN_NUMBER" REPORT_COUNT$ 
PAGE_COUNT& COLUMN_COUNT( evaluate (I)Ljava/lang/Object; 
Exceptions java/lang/Throwable- java/lang/Integer/ (I)V <1
02 getValue ()Ljava/lang/Object;45
 å6 java/lang/String8 (it/businesslogic/ireport/barcode/BcImage: getBarcodeImage I(ILjava/lang/Object;ZZLjava/lang/String;II)Ljava/awt/image/BufferedImage;<=
;> java/lang/StringBuffer@ valueOf &(Ljava/lang/Object;)Ljava/lang/String;BC
9D (Ljava/lang/String;)V <F
AG  / I append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;KL
AM
 ¿6 toString ()Ljava/lang/String;PQ
AR R$T isEmpty ()ZVW
9X .Z  \ replace D(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;^_
9` -b length ()Ide
9f CPF: h CNPJ: j trimlQ
9m   o  q CEP: s evaluateOld getOldValuev5
 åw evaluateEstimated 
SourceFile !     4                 	     
               
                                                                                                !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6 7    8 7    9 7    : 7    ; 7     < =  >  õ    	*· @*µ B*µ D*µ F*µ H*µ J*µ L*µ N*µ P*µ R*µ T*µ V*µ X*µ Z*µ \*µ ^*µ `*µ b*µ d*µ f*µ h*µ j*µ l*µ n*µ p*µ r*µ t*µ v*µ x*µ z*µ |*µ ~*µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ *µ  *µ ¢*µ ¤*µ ¦*µ ¨±    ©   Ú 6      	          ! " " ' # , $ 1 % 6 & ; ' @ ( E ) J * O + T , Y - ^ . c / h 0 m 1 r 2 w 3 | 4  5  6  7  8  9  :  ; ¤ < © = ® > ³ ? ¸ @ ½ A Â B Ç C Ì D Ñ E Ö F Û G à H å I ê J ï K ô L ù M þ N O   ª «  >   4     *+· ¯*,· ²*-· µ±    ©       [  \ 
 ]  ^  ¬ ­  >  ¥    E*+·¹ ½ À ¿À ¿µ B*+Á¹ ½ À ¿À ¿µ D*+Ã¹ ½ À ¿À ¿µ F*+Å¹ ½ À ¿À ¿µ H*+Ç¹ ½ À ¿À ¿µ J*+É¹ ½ À ¿À ¿µ L*+Ë¹ ½ À ¿À ¿µ N*+Í¹ ½ À ¿À ¿µ P*+Ï¹ ½ À ¿À ¿µ R*+Ñ¹ ½ À ¿À ¿µ T*+Ó¹ ½ À ¿À ¿µ V*+Õ¹ ½ À ¿À ¿µ X*+×¹ ½ À ¿À ¿µ Z*+Ù¹ ½ À ¿À ¿µ \*+Û¹ ½ À ¿À ¿µ ^*+Ý¹ ½ À ¿À ¿µ `*+ß¹ ½ À ¿À ¿µ b*+á¹ ½ À ¿À ¿µ d±    ©   N    f  g $ h 6 i H j Z k l l ~ m  n ¢ o ´ p Æ q Ø r ê s ü t u  v2 wD x  ° ­  >  ¦    *+ã¹ ½ À åÀ åµ f*+ç¹ ½ À åÀ åµ h*+é¹ ½ À åÀ åµ j*+ë¹ ½ À åÀ åµ l*+í¹ ½ À åÀ åµ n*+ï¹ ½ À åÀ åµ p*+ñ¹ ½ À åÀ åµ r*+ó¹ ½ À åÀ åµ t*+õ¹ ½ À åÀ åµ v*+÷¹ ½ À åÀ åµ x*+ù¹ ½ À åÀ åµ z*+û¹ ½ À åÀ åµ |*+ý¹ ½ À åÀ åµ ~*+ÿ¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+	¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+
¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ *+¹ ½ À åÀ åµ ±    ©   z       $  6  H  Z  l  ~    ¢  ´  Æ  Ø  ê  ü  " 5 H [ n   § º Í à ó     ³ ­  >        `*+¹ ½ À!À!µ  *+#¹ ½ À!À!µ ¢*+%¹ ½ À!À!µ ¤*+'¹ ½ À!À!µ ¦*+)¹ ½ À!À!µ ¨±    ©       ¥  ¦ & § 9 ¨ L © _ ª *+ ,    . >  ª    ^Mª  Y       %   ¥   ±   ½   É   Õ   á   í   ù      +  9  G      ¤  ²  À  Î  Ü  ê  ø      <  J  X  f  t        ¥  ³  ;  I  ó  »0Y·3M§«»0Y·3M§»0Y·3M§»0Y·3M§»0Y·3M§{»0Y·3M§o»0Y·3M§c»0Y·3M§W
*´ n¶7À9¸?M§?*´ |¶7À9M§1*´ |¶7À9M§#*´ ¶7À9M§»AY*´ f¶7À9¸E·HJ¶N*´ b¶OÀ9¶NJ¶N*´ B¶OÀ9¶N¶SM§Ô*´ ¶7À9M§Æ*´ ¶7À9M§¸*´ z¶7À9M§ª*´ t¶7À9M§*´ ¶7À9M§*´ ¶7À9M§*´ ¶7À9M§r*´ x¶7À9M§d*´ ¶7À9M§V*´ x¶7À9M§H»AY*´ ¶7À9¸E·H*´ ¶7À9¶N¶SM§ *´ p¶7À9M§*´ r¶7À9M§*´ ¶7À9M§ö*´ v¶7À9M§è*´ v¶7À9M§ÚUM§Ó*´ ¶7À9M§Å*´ p¶7À9M§·*´ ¶7À9M§©»AY*´ ¶7À9Æ  *´ ¶7À9¶Y *´ ¶7À9§ 
*´ r¶7À9¸E·HJ¶N*´ ¶7À9[]¶ac]¶a¶g¢ 	i§ k¶N*´ ¶7À9¶N¶SM§!*´ j¶7À9M§»AY*´ ¶7À9Ç 	]§ #»AY*´ ¶7À9¶n¸E·Hp¶N¶S¸E·H*´ ¶7À9Ç 	]§ S»AY*´ ¶7À9¸E·Hr¶N*´ ¶7À9¶nÆ  »AYt·H*´ ¶7À9¶N¶S§ ]¶N¶S¶N¶SM§ i»AY*´ ¶7À9¸E·H*´ ¶7À9¶N¶SM§ A»AY*´ f¶7À9¸E·HJ¶N*´ b¶OÀ9¶NJ¶N*´ B¶OÀ9¶N¶SM,°    ©  : N   ²  ´ ¨ ¸ ± ¹ ´ ½ ½ ¾ À Â É Ã Ì Ç Õ È Ø Ì á Í ä Ñ í Ò ð Ö ù × ü Û Ü à á  å+ æ. ê9 ë< ïG ðJ ô õ ù ú þ¤ ÿ§²µÀ	Ã
ÎÑÜßêíøû!"	&'+<,?0J1M5X6[:f;i?t@wDEIJNOS¥T¨X³Y¶];^>bIcLgóhölmq\y u+ ,    . >  ª    ^Mª  Y       %   ¥   ±   ½   É   Õ   á   í   ù      +  9  G      ¤  ²  À  Î  Ü  ê  ø      <  J  X  f  t        ¥  ³  ;  I  ó  »0Y·3M§«»0Y·3M§»0Y·3M§»0Y·3M§»0Y·3M§{»0Y·3M§o»0Y·3M§c»0Y·3M§W
*´ n¶xÀ9¸?M§?*´ |¶xÀ9M§1*´ |¶xÀ9M§#*´ ¶xÀ9M§»AY*´ f¶xÀ9¸E·HJ¶N*´ b¶OÀ9¶NJ¶N*´ B¶OÀ9¶N¶SM§Ô*´ ¶xÀ9M§Æ*´ ¶xÀ9M§¸*´ z¶xÀ9M§ª*´ t¶xÀ9M§*´ ¶xÀ9M§*´ ¶xÀ9M§*´ ¶xÀ9M§r*´ x¶xÀ9M§d*´ ¶xÀ9M§V*´ x¶xÀ9M§H»AY*´ ¶xÀ9¸E·H*´ ¶xÀ9¶N¶SM§ *´ p¶xÀ9M§*´ r¶xÀ9M§*´ ¶xÀ9M§ö*´ v¶xÀ9M§è*´ v¶xÀ9M§ÚUM§Ó*´ ¶xÀ9M§Å*´ p¶xÀ9M§·*´ ¶xÀ9M§©»AY*´ ¶xÀ9Æ  *´ ¶xÀ9¶Y *´ ¶xÀ9§ 
*´ r¶xÀ9¸E·HJ¶N*´ ¶xÀ9[]¶ac]¶a¶g¢ 	i§ k¶N*´ ¶xÀ9¶N¶SM§!*´ j¶xÀ9M§»AY*´ ¶xÀ9Ç 	]§ #»AY*´ ¶xÀ9¶n¸E·Hp¶N¶S¸E·H*´ ¶xÀ9Ç 	]§ S»AY*´ ¶xÀ9¸E·Hr¶N*´ ¶xÀ9¶nÆ  »AYt·H*´ ¶xÀ9¶N¶S§ ]¶N¶S¶N¶SM§ i»AY*´ ¶xÀ9¸E·H*´ ¶xÀ9¶N¶SM§ A»AY*´ f¶xÀ9¸E·HJ¶N*´ b¶OÀ9¶NJ¶N*´ B¶OÀ9¶N¶SM,°    ©  : N    ¨ ± ´ ½ À É Ì Õ Ø á ä¡ í¢ ð¦ ù§ ü«¬°± µ+¶.º9»<¿GÀJÄÅÉÊÎ¤Ï§Ó²ÔµØÀÙÃÝÎÞÑâÜãßçêèíìøíûñò	ö÷û<ü? JMX[
fitw#¥$¨(³)¶-;.>2I3L7ó8ö<=A\I y+ ,    . >  ª    ^Mª  Y       %   ¥   ±   ½   É   Õ   á   í   ù      +  9  G      ¤  ²  À  Î  Ü  ê  ø      <  J  X  f  t        ¥  ³  ;  I  ó  »0Y·3M§«»0Y·3M§»0Y·3M§»0Y·3M§»0Y·3M§{»0Y·3M§o»0Y·3M§c»0Y·3M§W
*´ n¶7À9¸?M§?*´ |¶7À9M§1*´ |¶7À9M§#*´ ¶7À9M§»AY*´ f¶7À9¸E·HJ¶N*´ b¶OÀ9¶NJ¶N*´ B¶OÀ9¶N¶SM§Ô*´ ¶7À9M§Æ*´ ¶7À9M§¸*´ z¶7À9M§ª*´ t¶7À9M§*´ ¶7À9M§*´ ¶7À9M§*´ ¶7À9M§r*´ x¶7À9M§d*´ ¶7À9M§V*´ x¶7À9M§H»AY*´ ¶7À9¸E·H*´ ¶7À9¶N¶SM§ *´ p¶7À9M§*´ r¶7À9M§*´ ¶7À9M§ö*´ v¶7À9M§è*´ v¶7À9M§ÚUM§Ó*´ ¶7À9M§Å*´ p¶7À9M§·*´ ¶7À9M§©»AY*´ ¶7À9Æ  *´ ¶7À9¶Y *´ ¶7À9§ 
*´ r¶7À9¸E·HJ¶N*´ ¶7À9[]¶ac]¶a¶g¢ 	i§ k¶N*´ ¶7À9¶N¶SM§!*´ j¶7À9M§»AY*´ ¶7À9Ç 	]§ #»AY*´ ¶7À9¶n¸E·Hp¶N¶S¸E·H*´ ¶7À9Ç 	]§ S»AY*´ ¶7À9¸E·Hr¶N*´ ¶7À9¶nÆ  »AYt·H*´ ¶7À9¶N¶S§ ]¶N¶S¶N¶SM§ i»AY*´ ¶7À9¸E·H*´ ¶7À9¶N¶SM§ A»AY*´ f¶7À9¸E·HJ¶N*´ b¶OÀ9¶NJ¶N*´ B¶OÀ9¶N¶SM,°    ©  : N  R T ¨X ±Y ´] ½^ Àb Éc Ìg Õh Øl ám äq ír ðv ùw ü{| +.9<GJ¤§£²¤µ¨À©Ã­Î®Ñ²Ü³ß·ê¸í¼ø½ûÁÂ	ÆÇË<Ì?ÐJÑMÕXÖ[ÚfÛißtàwäåéêîïó¥ô¨ø³ù¶ý;þ>ILóö
\ z    t _1569354279803_352825t 2net.sf.jasperreports.engine.design.JRJavacCompiler