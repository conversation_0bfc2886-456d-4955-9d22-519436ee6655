<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovPagamento_cheques" pageWidth="528" pageHeight="802" whenNoDataType="AllSectionsNoDetail" columnWidth="528" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="3.9930000000000065"/>
	<property name="ireport.x" value="1002"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="moeda" class="java.lang.String">
		<defaultValueExpression><![CDATA["XX"]]></defaultValueExpression>
	</parameter>
	<field name="valor" class="java.lang.Double"/>
	<field name="numero" class="java.lang.String"/>
	<field name="agencia" class="java.lang.String"/>
	<field name="banco.nome" class="java.lang.String"/>
	<field name="conta" class="java.lang.String"/>
	<field name="dataOriginalApresentar" class="java.lang.String"/>
	<field name="cpf" class="java.lang.String"/>
	<field name="cnpj" class="java.lang.String"/>
	<field name="situacao" class="java.lang.String"/>
	<field name="nomeNoCheque" class="java.lang.String"/>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement key="textField-4" mode="Opaque" x="190" y="0" width="25" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{agencia}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-5" mode="Opaque" x="225" y="0" width="50" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{conta}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" mode="Opaque" x="347" y="0" width="50" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{dataOriginalApresentar}.substring( 0, 10 )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-4" x="90" y="0" width="10" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[ B:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-7" mode="Opaque" x="397" y="0" width="70" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[( $F{cnpj}.equals("") ?  $F{cpf}:$F{cnpj} )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="0" width="10" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6"/>
				</textElement>
				<text><![CDATA[NT:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="10" y="0" width="80" height="12" isPrintWhenDetailOverflows="true"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{nomeNoCheque}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-3" mode="Opaque" x="100" y="0" width="80" height="12" isPrintWhenDetailOverflows="true"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{banco.nome}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-2" x="275" y="0" width="10" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[N:]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-1" mode="Opaque" x="493" y="0" width="35" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valor}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement key="staticText-6" x="215" y="0" width="10" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[C:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-7" x="335" y="0" width="12" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Dtc:]]></text>
			</staticText>
			<staticText>
				<reportElement key="staticText-5" x="180" y="0" width="10" height="12"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" pdfFontName="Helvetica"/>
				</textElement>
				<text><![CDATA[Ag:]]></text>
			</staticText>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-2" mode="Opaque" x="285" y="0" width="50" height="12"/>
				<box>
					<pen lineWidth="0.0" lineColor="#999999"/>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
					<leftPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<bottomPen lineWidth="0.0" lineStyle="Double" lineColor="#666666"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#666666"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" pdfFontName="Helvetica"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{numero}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement mode="Opaque" x="467" y="0" width="16" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single" markup="none">
					<font fontName="Microsoft Sans Serif" size="6" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
