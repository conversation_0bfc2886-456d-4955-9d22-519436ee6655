<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="MovProduto" pageWidth="195" pageHeight="535" orientation="Landscape" columnWidth="195" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="moeda" class="java.lang.String" isForPrompting="false">
	<defaultValueExpression><![CDATA["R$"]]></defaultValueExpression>
	</parameter>
	<field name="descricao" class="java.lang.String"/>
	<field name="valorParcela" class="java.lang.Double"/>
	<field name="codigo" class="java.lang.Integer"/>
	<pageHeader>
		<band height="12">
			<textField>
				<reportElement x="0" y="0" width="116" height="10"/>
				<textElement markup="none">
					<font fontName="Microsoft Sans Serif" size="7" pdfFontName="Helvetica-Bold"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA["Parcelas Renegociadas do Recibo"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="false">
				<reportElement key="textField-1" x="38" y="0" width="92" height="10" isPrintWhenDetailOverflows="true"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[" "+$F{descricao}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement key="textField-6" x="0" y="0" width="38" height="10">
					<printWhenExpression><![CDATA[$F{codigo} > 0]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Integer"><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="0" width="24" height="10"/>
				<textElement textAlignment="Right" markup="none">
					<font size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.String"><![CDATA[$P{moeda}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="false">
				<reportElement key="textField-5" x="155" y="0" width="40" height="10"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Microsoft Sans Serif" size="7"/>
				</textElement>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{valorParcela}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
