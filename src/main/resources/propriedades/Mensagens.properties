armario_excluidomsg_consultar_por==============================================================================
# Mensagens da Aplicacao
# ==============================================================================
msg_entre_prmlogin=Entre com Usu\u00E1rio e Senha
msg_entre_login=Login Realizado Com Sucesso
msg_entre_prmlogout=Logout Realizado com Sucesso
msg_dados_gravados=Dados Gravados com Sucesso
msg_empresa_persistida_sem_movidesk=Empresa gravada com sucesso, mas os dados do Movidesk n\u00E3o foram persistidos. Acione algum desenvolvedor.
msg_dados_adicionados=Clique em Gravar para confirmar a opera\u00E7\u00E3o
msg_dados_adicionadosQuestionario=Clique em Confirmar ou Responder Depois para gravar
msg_dados_selecionados=Dado Selecionado com Sucesso
msg_dados_excluidos=Dados Exclu\u00EDdos com Sucesso
msg_dados_prontos_exclusao= Dados Prontos para Exclus\u00E3o
msg_dados_excluidosPlano=N\u00E3o foi poss\u00EDvel remover o produto sugerido devido ao v\u00EDnculo com os contratos de clientes
msg_dados_consultados=Dados Consultados com Sucesso
msg_dados_consultados_vazio=Nenhum registro encontrado.
msg_dados_editar=Dados Prontos para Edi\u00E7\u00E3o
msg_dados_obrigatorios=Dados Obrigat\u00F3rios
msg_dados_invalidos=Dados Inv\u00E1lidos
msg_consultar_por=Consultar por:
msg_entre_dados=Informe os Dados
msg_entre_prmconsulta=Informe os Par\u00E2metros para a Consulta
msg_entre_prmrelatorio=Informe os Par\u00E2metros para a Gera\u00E7\u00E3o do Relat\u00F3rio
msg_entre_dicarelatorio=DICA: se selecionar um cliente ou colaborador voc\u00EA pode consultar at\u00E9 12 meses.
msg_relatorio_ok=Relat\u00F3rio Gerado com Sucesso
msg_erro=N\u00E3o Foi Poss\u00EDvel Realizar esta Opera\u00E7\u00E3o
msg_erro_valorduplicado=Opera\u00E7\u00E3o n\u00E3o Realizada. J\u00E1 existe um registro cadastrado com estes dados.
msg_erro_TipoAmbienteRelacionado=Imposs\u00EDvel excluir tipo Ambiente. Esta relacionado com outro Ambiente.
msg_erro_FornecedorRelacionado=Imposs\u00EDvel excluir Fornecedor. Esta relacionado com outro Servi\u00E7o ou Lan\u00E7amento Financeiro.
msg_erro_dadosreferenciados=Opera\u00E7\u00E3o n\u00E3o Realizada. Registro referenciado por outra entidade.
msg_erro_conectarBD=N\u00E3o foi Poss\u00EDvel Conectar a Base de Dados
msg_erro_dadosnaoencontrados=Dados n\u00E3o Encontrados
msg_erro_relatorio=N\u00E3o foi Poss\u00EDvel Gerar Relat\u00F3rio de Dados
msg_erro_emailInvalido=E-mail Inv\u00E1lido
msg_erro_emailNotificacaoVendasOnlineInvalido=E-mail de Notifica\u00E7\u00E3o de Vendas Online Inv\u00E1lido
msg_erro_excedeuLimiteEmail=Lista de E-mail alcan\u00E7ou o tamanho m\u00E1ximo
msg_erro_cnpjInvalido= CNPJ Inv\u00E1lido
msg_erro_telefoneInvalido= Telefone Inv\u00E1lido
msg_consultar_dados=Consultar Dados
msg_editar_dados=Editar Dados
msg_novo_dados=Incluir Novos Dados
msg_adicionados_dados=Dados adicionados com Sucesso !
msg_transferencia_conta_corrente=Transfer\u00EAncia realizada com sucesso!
msg_parcelaDebito_conta_corrente=Parcela Gerada com Sucesso! Acesse o Caixa em Aberto para quitar a parcela.  
msg_gravar_dados=Gravar Dados
msg_confirmar=Confirmar
msg_enviar_email=Envio agendado com sucesso !
msg_email_enviado=E-mail enviado com sucesso!
msg_app_enviado=Contato enviado com sucesso!
msg_SMS_enviado=SMS enviado com sucesso!
msg_enviar_emailErro=Envio n\u00E3o foi agendado.
msg_excluir_dados=Excluir Dados
msg_existePessoa= Pessoa J\u00E1 Cadastrada! Deseja Buscar os Dados?
msg_gerarParcela= \u00C9 necess\u00E1rio gerar as parcelas. Deseja gerar agora?
msg_selecionar_dados=Selecionar Dado.
msg_clonar_dados=C\u00F3piar Dados
msg_Questionario_Erro=Nenhum Question\u00E1rio foi definido!
msg_calculo=C\u00E1lculo Realizado com Sucesso.
msg_erro_senha_cli=As Senhas Informadas n\u00E3o Conferem
msg_entre_senha_cli=Digite a Nova Senha
msg_senhaAlterada_cli=Senha Alterada com Sucesso
msg_composicao_Default=Com essa op\u00E7\u00E3o marcada, o pacote torna-se padr\u00E3o nos novos Planos cadastros.
msg_composicao_Adicional=Com essa op\u00E7\u00E3o marcada, o pacote s\u00F3 poder\u00E1 ser usado se houver um pacote normal j\u00E1 selecionado.
msg_composicao_limite=Com essa op\u00E7\u00E3o marcada, na Negocia\u00E7\u00E3o n\u00E3o ser\u00E1 poss\u00EDvel escolher outra combina\u00E7\u00E3o de modalidades diferente do pacote.
msg_composicao_mensagem=Voc\u00EA pode escolher qualquer uma das modalidades em verde.
msg_ConfirmaExclusao=Excluir Dados?
msg_RelatorioCliente=Relat\u00F3rio de Clientes
msg_AlunosAtivos=Somente Alunos(as) Ativas Hoje
msg_AlunosInativos=Somente Alunos(as) Inativos(as) e Visitantes
msg_TodosCadastros=Todo Cadastro de Alunos(as)
msg_EstornoSucesso=Estorno do Contrato realizado com Sucesso!
msg_EstornoContrato_ExisteOutroContrato=Aten\u00E7\u00E3o o(s) RECIBO(s) deste contrato pagam parcelas de outros contratos. Caso voc\u00EA confirme o estorno desse contrato ter\u00E1 que receber novamente as parcelas dos outros contratos.
msg_Estorno_ExisteTransacoesJaProcessadas=Estas parcelas possuem transa\u00E7\u00F5es de Cart\u00E3o de Cr\u00E9dito j\u00E1 processadas e que n\u00E3o podem ser canceladas. N\u00E3o ser\u00E1 poss\u00EDvel realizar o estorno.
msg_Estorno_ExisteTransacoesQuePodemSerCanceladas=Estas Parcelas possuem transa\u00E7\u00F5es de Cart\u00E3o de Cr\u00E9dito j\u00E1 processadas que podem ser canceladas antes de prosseguir o Estorno. Se estiver certo disso, cancele as transa\u00E7\u00F5es antes de continuar.
msg_OrganizadorCarteira_transferido=Aluno(s) Transferido(s) com sucesso.
msg_OrganizadorCarteira_vinculado=Aluno(s) Vinculado(s) com sucesso.
msg_limpar_objecao=Limpar Obje\u00E7\u00E3o
msg_dados_gravados_simplesRegistro=Simples Registro Gravado com Sucesso 
msg_dados_gravados_movimentacaoCCC=Foi gerado um produto no valor do cr\u00E9dito informado. Acesse o Caixa em Aberto e pague esse produto para que o cr\u00E9dito seja inserido na conta do aluno.
msg_dados_gravados_movimentacaoDCC=D\u00E9bito adicionado com sucesso
msg_dados_gravados_envioSMS= SMS enviado com sucesso.
msg_dados_gravados_agenda=Agendamento gravado com Sucesso 
msg_dados_gravados_objecao=Obje\u00E7\u00E3o gravada com Sucesso 
msg_erro_naoGravados=Os dados ainda n\u00E3o foram gravados
msg_dados_mensagens_atualizadas=Mensagens dos clientes atualizadas com sucesso.
msg_erro_mensagens = Mensagens n\u00E3o foram atualizadas
msg_dados_ja_gravados = J\u00E1 existe essa cidade cadastrada.
msg_cliente_selecionados=Cliente Selecionado.
msg_data_periodo_atual=Usar o Mes Atual Como Per\u00EDodo de Consulta
msg_data_periodo_anterior=Usar o Mes Anterior Como Per\u00EDodo de Consulta
msg_tag_nao_selecionada=Selecione uma tag.
msg_upload_arquivo=Upload de arquivo realizado com sucesso.
msg_erro_obterModeloMensagem=N\u00E3o foi poss\u00EDvel obter modelo mensagem.
msg_erro_emailResponsavelRecorrenciaVazio=Preencha o campo e-mail do respons\u00E1vel.
msg_erro_emailNotificacaoNotasVazio=Preencha o campo e-mail para a notifica\u00E7\u00E3o.
msg_erro_emailResponsavelRecorrenciaExistente=E-mail j\u00E1 consta na lista.
msg_erro_configuracoesRecorrenciaInexistentes=O pagamento por recorr\u00EAncia (Pagamento online) n\u00E3o est\u00E1 configurado no sistema.
msg_erro_parcelasContratoRecorrencia=Todas as parcelas selecionadas devem ser de contratos em regime de recorr\u00EAncia.
msg_erro_nenhumaParcelaRelacionada=Nenhuma parcela relacionada.
msg_entre_gravar_ok = Clique em OK para gravar
msg_valor_ajustado_sucesso = Valor ajustado com sucesso
msg_dados_usuario = Informe os dados do usu\u00E1rio
msg_rateio_plano_contas= Salve o cadastro para habilitar o rateio do Plano de Contas.
msg_rateio_automatico=Plano de Contas possui rateio de Centro de Custos, foi gerado o rateio automaticamente. Para salvar, clique em Gravar.
msg_meta_retroativa=N\u00E3o calculada para abertura retroativa.
msg_meta_nao_calculada=Meta n\u00E3o associada a este tipo de colaborador.
msg_tip_meta_nao_calculada=Para associar uma fase a um tipo de colaborador, v\u00E1 nas configura\u00E7\u00F5es do CRM, aba 'Respons\u00E1veis pelas Fases'. Voc\u00EA deve ter permiss\u00E3o para realizar esta opera\u00E7\u00E3o. 
msg_listaClientes_vazia=Nenhum cliente listado.
msg_existeMetaEmpresa=J\u00E1 existe uma meta para esta empresa no m\u00EAs informado. 
msg_devolucao= Ser\u00E1 realizada uma sa\u00EDda no caixa de hoje no valor de
msg_quitacaoCancelamento= ATEN\u00C7\u00C3O: Ser\u00E1 cobrado do aluno o valor referente ao res\u00EDduo devedor do cancelamento. Acesse o link "Caixa" a partir do cadastro do cliente para receber esta quantia
msg_usuarioNaoPossuiPermissaoMetasFinanceiras= Usu\u00E1rio n\u00E3o possui permiss\u00E3o para visualizar meta financeira da Empresa.
msg_cancelamentoProporcional = Ser\u00E1 cobrado as parcelas vencidas e um valor proporcional aos dias utilizados referente a parcela do m\u00EAs do cancelamento. Os valores podem ser visualizados no "Caixa em aberto".
msg_validacaoDataMenorQueDataCancelamento = A data n\u00E3o pode ser menor que a data base do cancelamento.
msg_validacaoDataMaiorQueDataFimContrato = A data n\u00E3o pode ser maior que a data final do contrato.
msg_restaurarVinculos=V\u00EDnculos de consultor restaurados com sucesso!
msg_restaurarVinculos_professores=V\u00EDnculos de professores restaurados com sucesso!
msg_descontoMaior=Desconto n\u00E3o pode ser maior do que o valor do produto!
msg_escolhaProduto=Escolha primeiro o produto
msg_tip_bloquearTermos=Caso voc\u00EA n\u00E3o queira bloquear estes termos, o sistema apenas avisar\u00E1 ao usu\u00E1rio a import\u00E2ncia de n\u00E3o us\u00E1-los no Mailing.
msg_tip_tituloMail=Dica Anti-Spam: Nunca use o t\u00EDtulo todo em caixa alta. 
msg_tip_tituloMailPontos=O sistema ir\u00E1 retirar os pontos de exclama\u00E7\u00E3o (!) e interroga\u00E7\u00E3o (?) automaticamente.
msg_tip_tituloMailTermos=Evite termos fiscalizados pelos programas anti-spam como:
msg_mail_termobloqueado=Os seguintes termos n\u00E3o podem estar na mensagem ou no t\u00EDtulo:
msg_tip_mensagem=Dica Anti-Spam: Equilibre seu template. Use imagem, mas tamb\u00E9m use textos. O ideal \u00E9 50% para cada.
msg_sucesso_substituido=Refer\u00EAncias substitu\u00EDdas com sucesso!
msg_sucesso_recibo_sem_prod_cc=Recibos procesados com sucesso!
msg_selecione_novo_status= Selecione o novo status para esse cheque!
msg_sucesso_novo_status=Status alterado com sucesso!
msg_somenteTurmas=Desmarque esta op\u00E7\u00E3o se quiser visualizar tamb\u00E9m os alunos que n\u00E3o est\u00E3o inclu\u00EDdos em alguma turma (apenas no anal\u00EDtico).
msg_conciliacaoSucesso=Concilia\u00E7\u00E3o de conta efetuada com sucesso! 
msg_naoPossuiCaixaAberto=O usu\u00E1rio n\u00E3o possui Caixa aberto.\\r\\n\u00C9 necess\u00E1rio abrir o Caixa antes de realizar a opera\u00E7\u00E3o.
msg_naoPossuiCaixaAbertoParaConta=O caixa aberto n\u00E3o contempla a conta selecionada.
msg_naoPossuiCaixaAbertoParaConta_Recebiveis=A conta que o(s) receb\u00EDvel(is) est\u00E1(\u00E3o) agora, não foi selecionada para trabalho no momento da abertura do caixa.
msg_naoexcluirChequePagaMovConta=N\u00E3o \u00E9 poss\u00EDvel remover cheques deste lote pois ele foi usado para pagar uma conta.
msg_Justificativa_tamanho=Justificativa n\u00E3o pode ter mais do que 200 caracteres.
msg_selecione_lancamento=Selecione pelo menos um lan\u00E7amento.
msg_lancamento_excluir_caixaFechado=Esta opera\u00E7\u00E3o foi lan\u00E7ada num caixa que j\u00E1 est\u00E1 fechado e n\u00E3o pode ser exclu\u00EDda.
msg_cheque_naoDepositado=N\u00E3o \u00E9 permitido juntar na mesma opera\u00E7\u00E3o cheques que n\u00E3o foram depositados no financeiro com cheques que j\u00E1 constam nas contas.
msg_lote_maior_movconta=Este lote possui um valor maior do que a conta que est\u00E1 sendo paga e n\u00E3o pode ser selecionado.
msg_lote_menor_o_que_fazer=Valor do lote \u00E9 menor do que o total a pagar em cheque, voc\u00EA dever\u00E1 escolher como o Financeiro vai tratar o valor restante.
msg_formaPagamentoJaEscolhida=A mesma forma de pagamento n\u00E3o pode ser escolhida mais de uma vez, as altera\u00E7\u00F5es neste registro ser\u00E3o desfeitas.
msg_quitacaoPagamentoMaior=Esta op\u00E7\u00E3o te permite fazer a quita\u00E7\u00E3o com um valor maior do que o informado originalmente. O valor do lan\u00E7amento ser\u00E1 atualizado.
msg_quitacaoPagamentoMenor=Esta op\u00E7\u00E3o te permite fazer a quita\u00E7\u00E3o com um valor menor do que o informado originalmente. O valor do lan\u00E7amento ser\u00E1 atualizado.
msg_quitacaoPagamentoParcial=Esta op\u00E7\u00E3o te permite fazer a quita\u00E7\u00E3o com um valor menor do que o informado originalmente. O restante n\u00E3o quitado ser\u00E1 convertido em um novo lan\u00E7amento, id\u00EAntico a este, exceto pelo valor.
msg_loteNaoEstaNaConta=Esta quita\u00E7\u00E3o possui um lote que n\u00E3o est\u00E1 na conta selecionada. Escolha outro lote ou transfira o desejado para esta conta.
msg_chequeNaoInformado=Informe o cheque da forma de pagamento do tipo CHEQUE.
msg_loteNaoInformado=Informe o lote da forma de pagamento do tipo LOTE.
msg_todasFormasQuitacao=Todas as formas de pagamento devem ser informadas.
msg_lotenaopodeexcluirpagamovconta=Este lote n\u00E3o pode ser exclu\u00EDdo pois ele paga um lan\u00E7amento.
msg_envioemailnaoexecutado=Todos os e-mails selecionados s\u00E3o inv\u00E1lidos, corrija e tente novamente.
msg_nenhumitemselecionado= Nenhum cart\u00E3o selecionado.
msg_datavendaavulsa=A data n\u00E3o pode estar vazia!
msg_lotesDerivados=Este lote n\u00E3o poder\u00E1 ser exclu\u00EDdo porque faz parte de uma s\u00E9rie de movimenta\u00E7\u00F5es financeiras. Exclua primeiro os lotes a seguir na seguinte ordem:
msg_movConta_Lotes_Derivados=Este lan\u00E7amento n\u00E3o poder\u00E1 ser exclu\u00EDdo porque faz parte de uma s\u00E9rie de movimenta\u00E7\u00F5es financeiras. Exclua primeiro os lan\u00E7amentos a seguir: 
msg_movConta_derivado=Este lan\u00E7amento faz parte de uma transfer\u00EAncia. Ao exclu\u00ED-lo voc\u00EA excluir\u00E1 automaticamente o(s) lan\u00E7amento(s):
msg_excluir_lancamento_cartao=Aten\u00E7\u00E3o: Ap\u00F3s excluir um lan\u00E7amento de cart\u00E3o de cr\u00E9dito, verifique se existe pagamento de taxa administrativa da operadora e exclua-o tamb\u00E9m.
msg_limparRateioSugerido=Os rateios sugeridos foram removidos pois n\u00E3o condizem mais com o valor informado.
msg_corrigirValoresRateio=Os valores dos rateios foram ajustados.
 
msg_recAvulso_empresa=Empresa deve ser informada.
msg_recAvulso_responsavel=Respons\u00E1vel deve ser informado.
msg_recAvulso_cliente=Cliente deve ser informado.
msg_recAvulso_faturamento=Data de Faturamento deve ser informada.
msg_recAvulso_compensacao=Data de Compensa\u00E7\u00E3o deve ser informada.
msg_recAvulso_valor=Valor deve ser informado.
msg_recAvulso_planocontas=Plano de Contas n\u00E3o foi corretamente selecionado ou n\u00E3o existe.
msg_recAvulso_centrocustos=Centro de Custos n\u00E3o foi corretamente selecionado ou n\u00E3o existe.
msg_recAvulso_operadora=Operadora de Cart\u00E3o deve ser informada.
msg_recAvulso_nrparcela=Nr. Parcela deve ser maior que zero.
msg_recAvulso_banco=Banco deve ser informado.
msg_recAvulso_agencia=Ag\u00EAncia deve ser informada.
msg_recAvulso_conta=Conta do cheque deve ser informada.
msg_recAvulso_nrcheque=Nr. do Cheque deve ser informado.
msg_dados_gravados_sucesso= Dados gravados com sucesso!


# ==============================================================================
# Component Rich Calendar <rich:calendar>
# ==============================================================================
RICH_CALENDAR_TODAY_LABEL=Hoje
RICH_CALENDAR_CLEAN_LABEL=Limpar
RICH_CALENDAR_APPLY_LABEL=Aplicar
RICH_CALENDAR_CLOSE_LABEL=Fechar
RICH_CALENDAR_CANCEL_LABEL=Cancelar

# ==============================================================================
# Component Errors
# ==============================================================================
javax.faces.component.UIInput.CONVERSION=Ocorreu um erro de convers\u00E3o.
javax.faces.component.UIInput.REQUIRED=Campo de valor obrigat\u00F3rio
javax.faces.component.UISelectOne.INVALID=Campo com valor inv\u00E1lido
javax.faces.component.UISelectMany.INVALID=Campo com valor inv\u00E1lido

# ==============================================================================
# Validator Errors
# ==============================================================================

javax.faces.validator.NOT_IN_RANGE=Erro de Valida\u00E7\u00E3o: Intervalo de valores v\u00E1lidos {0} at\u00E9 {1}
javax.faces.validator.DoubleRangeValidator.MAXIMUM=Valor maior do que o permitido "{0}"
javax.faces.validator.DoubleRangeValidator.MINIMUM=Valor menor do que o permitido ''{0}''
javax.faces.validator.DoubleRangeValidator.TYPE=Valor n\u00E3o \u00E9 do tipo permitido
javax.faces.validator.LengthValidator.MAXIMUM=Valor maior do que o permitido ''{0}''
javax.faces.validator.LengthValidator.MINIMUM=Valor menor do que o permitido ''{0}''
javax.faces.validator.LongRangeValidator.MAXIMUM=Valor maior do que o permitido ''{0}''
javax.faces.validator.LongRangeValidator.MINIMUM=Valor menor do que o permitido ''{0}''
javax.faces.validator.LongRangeValidator.TYPE=Valor n\u00E3o \u00E9 do tipo permitido


jsp:

<h:messages styleClass="mensagemValidador" />





mensagens.properties:

javax.faces.converter.DateTimeConverter.DATE=Data inv\u00E1lida: ''{0}''
javax.faces.converter.DateTimeConverter.DATE_detail=Data inv\u00E1lida: ''{0}''. Exemplo: {1}.
javax.faces.converter.DateTimeConverter.TIME=Data inv\u00E1lida: ''{0}''.
javax.faces.converter.DateTimeConverter.TIME_detail=Data inv\u00E1lida: ''{0}''. Exemplo: {1}.
javax.faces.converter.DateTimeConverter.DATETIME=Data e Hor\u00E1rio inv\u00E1lidos: ''{0}''.
javax.faces.converter.DateTimeConverter.DATETIME_detail=Data e Hor\u00E1rio inv\u00E1lidos: ''{0}''. Exemplo: {1}
javax.faces.converter.DateTimeConverter.PATTERN_TYPE={1}: A 'pattern' or 'type' attribute must be specified to convert the value ''{0}''.

########################################################################################################################################

# ==============================================================================
# Mensagens M\u00F3dulo CENTRAL EVENTOS
# ==============================================================================

operacoes.arquivo.upload.tamanhoLimiteExcedido=O arquivo solicitado excede o tamanho limite
operacoes.arquivo.upload.erroTransferencia=Erro na transfer\u00EAncia do arquivo
operacoes.arquivo.upload.carregando=Carregando
operacoes.arquivo.upload.dimensaoMenorQue=A imagem menor que dimens\u00E3o limite
operacoes.arquivo.upload.dimensaoForaProprocao=A imagem est\u00E1 foram da propor\u00E7\u00E3o

operacoes.adicao.sucesso=Dados Adicionados com Sucesso

operacoes.adicao.erro.quantidadeMinima=O per\u00EDodo informado \u00E9 menor do que a quantidade m\u00EDnima de horas do ambiente. 
operacoes.adicao.erro.reservasExcedentes=O ambiente n\u00E3o possui mais reservas dispon\u00EDveis para o dia informado. 
operacoes.adicao.erro.periodoInvalido=O per\u00EDodo informado est\u00E1 fora do per\u00EDodo do ambiente selecionado.
operacoes.adicao.erro.periodoIndisponivel=O per\u00EDodo informado n\u00E3o est\u00E1 dispon\u00EDvel para esse ambiente.
operacoes.adicao.erro.patrimonioJaRelacionado=O patrim\u00F4nio com o c\u00F3digo informado j\u00E1 foi adicionado
operacoes.adicao.erro.ambienteJaRelacionado=O Ambiente selecionado j\u00E1 foi adicionado
operacoes.adicao.erro.servicoJaRelacionado=O Servi\u00E7o escolhido j\u00E1 est\u00E1 cadastrado
operacoes.adicao.erro.servicoFaixaQuantidade=Esta associa\u00E7\u00E3o do Servi\u00E7o selecionado deve possuir Faixa de Quantidade estreitamente vizinha das Faixas de Quantidade das outras associa\u00E7\u00F5es do mesmo Servi\u00E7o 
operacoes.adicao.erro.servicoObrigatorio=Algum Servi\u00E7o deve ser informado
operacoes.adicao.erro.bemConsumoJaRelacionado=O Bem de Consumo escolhido j\u00E1 est\u00E1 cadastrado
operacoes.adicao.erro.bemConsumoFaixaQuantidade=Esta associa\u00E7\u00E3o do Bem de Consumo selecionado deve possuir Faixa de Quantidade estreitamente vizinha das Faixas de Quantidade das outras associa\u00E7\u00F5es do mesmo Bem de Consumo
operacoes.adicao.erro.bemConsumoObrigatorio=Algum Bem de Consumo deve ser informado
operacoes.adicao.erro.utensilioJaRelacionado=O Utens\u00EDlio escolhido j\u00E1 est\u00E1 cadastrado
operacoes.adicao.erro.utensilioFaixaQuantidade=Esta associa\u00E7\u00E3o do Utens\u00EDlio selecionado deve possuir Faixa de Quantidade estreitamente vizinha das Faixas de Quantidade das outras associa\u00E7\u00F5es do mesmo Utens\u00EDlio
operacoes.adicao.erro.utensilioObrigatorio=Algum Utens\u00EDlio deve ser informado
operacoes.adicao.erro.brinquedoJaRelacionado=O Brinquedo escolhido j\u00E1 est\u00E1 cadastrado
operacoes.adicao.erro.brinquedoFaixaQuantidade=Esta associa\u00E7\u00E3o do Brinquedo selecionado deve possuir Faixa de Quantidade estreitamente vizinha das Faixas de Quantidade das outras associa\u00E7\u00F5es do mesmo Brinquedo
operacoes.adicao.erro.brinquedoObrigatorio=Algum Brinquedo deve ser informado
operacoes.adicao.erro.detalheClienteoObrigatorio=Algum Cliente deve ser informado
operacoes.adicao.erro.sazonalidadeJaRelacionada=A Sazonalidade informada j\u00E1 foi adicionada ao Ambiente
operacoes.adicao.erro.valorSazonalidade=A redu\u00E7\u00E3o n\u00E3o pode ser maior do que o valor do Ambiente.
operacoes.adicao.erro.sazonalidadeConcorrente=A Sazonalidade informada possui um conflito de per\u00EDodo com outra j\u00E1 cadastrada.
operacoes.adicao.erro.clienteInexistente=Preencha dados complementares para fechar a negocia\u00E7\u00E3o.
operacoes.alterarValores.encerrado=N\u00E3o \u00E9 permitido alterar a negocia\u00E7\u00E3o de um evento encerrado.
operacoes.alterarValores.valorMenor=N\u00E3o \u00E9 permitido alterar os valores para um total menor do que o total j\u00E1 pago pelo cliente.
operacoes.consulta.sucesso=Dados Consultados com Sucesso
operacoes.conversa.proximoContato=Deseja registrar essa conversa sem uma data para um pr\u00F3ximo contato?
operacoes.adicao.visita=Visita agendada com sucesso.
operacoes.adicao.contato=Pr\u00F3ximo contato agendado com sucesso.
operacoes.adicao.preReserva=Pr\u00E9-reserva remarcada com sucesso.
operacoes.consulta.nenhumResultado=N\u00E3o foram encontrados resultados para a sua pesquisa
operacoes.desconto.valorNaoPermitido=Valor do desconto excede o valor m\u00E1ximo permitido.
operacoes.desconto.sem.tipo.desconto.definido=Favor selecionar um tipo de desconto.
operacoes.edicao.dadosProntos=Dados Prontos para Edi\u00E7\u00E3o
operacoes.edicao.sucesso=Dados Alterados com Sucesso
operacoes.edicao.erro.qtdMaximaMenorQueAnterior=A quantidade m\u00E1xima deve ser maior ou igual a quantidade m\u00E1xima at\u00E9 ent\u00E3o registrada
operacoes.negociacaoFechada=A negocia\u00E7\u00E3o deste evento j\u00E1 foi fechada.
operacoes.data.evento.menor=Data do evento menor que a data atual.
operacoes.nrConvidados=O n\u00FAmero de convidados para este ambiente n\u00E3o pode ser maior do que   
operacoes.salvar.negociacao.erro.eventoSemReserva=O evento foi salvo sem reserva, retorne ao or\u00E7amento detalhado e confirme a reserva do mesmo para que seja permitido fechar a negocia\u00E7\u00E3o.
operacoes.salvar.negociacao.erro.quitacao=A negocia\u00E7\u00E3o possui quita\u00E7\u00E3o de cheque cau\u00E7\u00E3o / cr\u00E9dito pendentes.

operacoes.selecionado.ambiente=Ambiente j\u00E1 selecionado.

operacoes.erro=N\u00E3o Foi Poss\u00EDvel Realizar esta Opera\u00E7\u00E3o
operacoes.definir.mensagem.prospect.erro=Erro ao definir a(s) mensagem(s) para a lista de prospect.

operacoes.exclusao.confirmar=Confirma exclus\u00E3o dos dados?
operacoes.exclusao.erro.negociacoesVinculadasPerfil=H\u00E1 negocia\u00E7\u00F5es vinculadas a este Perfil de Evento, e, portanto, ele n\u00E3o p\u00F4de ser exclu\u00EDdo.
operacoes.exclusao.sucesso=Dados Exclu\u00EDdos com Sucesso
operacoes.favorito.sucesso=Aluno marcado como favorito
operacoes.lembrete.sucesso=Observação registrada com sucesso
operacoes.exclusao.favorito.sucesso=Aluno retirado dos favoritos
operacoes.exclusao.erro=H\u00E1 negocia\u00E7\u00F5es vinculadas a este Patrim\u00F4nio, e, portanto, ele n\u00E3o pode ser exclu\u00EDdo.
operacoes.cancelamento.confirmar=Confirma cancelamento do evento?
operacoes.cancelamento.confirmarParcelas=O evento j\u00E1 tem parcelas pagas, deseja REALMENTE fazer o estorno e apagar tudo do banco de dados?
operacoes.salvar.sucesso=Dados salvos com Sucesso
operacoes.salvar.erro=Todos os campos obrigatorios devem estar preenchidos
operacoes.remarcarPreReserva=Deseja remarcar a pr\u00E9-reserva?
operacoes.detalhamento=Deseja visualizar o detalhamento do evento?
operacoes.parcelaPaga=Parcela(s) paga(s) com sucesso.
dados.informar=Informe os Dados

dados.horario.evento.terminoDiaPosterior=T\u00E9rmino do evento no dia posterior. V\u00E1lido at\u00E9 as 06:00.


parametros.dataNegociacaoMaiorDataPerfil=Data do evento \u00E9 maior do que a data final do perfil.

parametros.dataFinalMenorDataInicial=Data Final informada \u00E9 menor que a Data Inicial.
parametros.horaFinalMenorhoraInicial=Hora Final informada \u00E9 menor ou igual que a Hora Inicial.
parametros.horafinalMaiorAmbiente=Hora Final maior que a hora disponivel para visita
parametros.dataInicialMenorDataAtual=Data Inicial anterior a Data Atual.
parametros.telefoneInvalido=Telefone incompleto ou inv\u00E1lido.
parametros.emailInvalido=E-mail incompleto ou inv\u00E1lido.
parametros.numeroPatrimoniosEstoque=O n\u00FAmero de patrim\u00F4nios deve ser igual ao de produtos em estoque.
parametros.dataVisitaMenorDataAtual=Data da Visita anterior a Data Atual.
parametros.descricaoVazio=Digite a descri\u00E7\u00E3o da conversa.
parametros.informar=Informe os Par\u00E2metros para a Consulta
campoObrigatorio.nrConvidados= O n\u00FAmero de convidados deve ser maior do que 0.
=======
parametros.dataFinalMenorDataInicial=Data Final informada \u00E9 menor que a Data Inicial.
parametros.dataInicialMenorDataAtual=Data de Interesse anterior a Data Atual.
parametros.informar=Informe os Par\u00E2metros para a Consulta
campoObrigatorio.dataHorario=A data deve ser selecionada.
campoObrigatorio.nrConvidados= O n\u00FAmero de convidados deve ser maior do que 0.

campoObrigatorio_comentario=O campo coment\u00E1rio deve ser preenchido.
campoObrigatorio.intervalo=O intervalo deve ser informado corretamente.
campoObrigatorio.semana=Alguma \\'Semana\\' deve ser selecionada.
campoObrigatorio.data=O campo \\'Data\\' deve ser informado.
campoObrigatorio.telefone=Pelo menos um telefone deve ser informado.
campoObrigatorio.nomeEvento=O campo \\'Nome do Evento\\' deve ser informado.
campoObrigatorio.email=O campo \\'E-mail\\' deve ser informado.
campoObrigatorio.nomeCliente=O campo \\'Nome do Cliente\\' deve ser informado.
campoObrigatorio.evento=O campo \\'Evento\\' deve ser informado.
campoObrigatorio.perfilEvento.nome=O campo \\'Nome do perfil\\' deve ser informado.
campoObrigatorio.empresa= O campo \\'Empresa\\' deve ser informado.
campoObrigatorio.perfilEvento.ambiente=Ao menos um \\'Ambiente\\' deve ser relacionado ao Perfil de Evento.
campoObrigatorio.dataInicio=O campo \\'Data de Interesse\\' deve ser informado.
campoObrigatorio.dataValidade=O campo \\'Data de Validade\\' deve ser informado.
campoObrigatorio.dataFim=O campo \\'Data fim\\' deve ser informado.
campoObrigatorio.dataTermino=O campo \\'Data t\u00E9rmino\\' deve ser informado.
campoObrigatorio.diaSemana=O campo \\'Dia da semana\\' deve ser informado.
campoObrigatorio.textoPadrao=O campo \\'Texto padr\u00E3o\\' deve ser informado.
campoObrigatorio.formasContato=O campo \\'Formas de Contato\\' deve ser informado.
campoObrigatorio.servico=O campo \\'Servi\u00E7o\\' deve ser informado.
campoObrigatorio.valor=O campo \\'Valor\\' deve ser informado.
campoObrigatorio.quantidade=O campo \\'Quantidade\\' deve ser informado.
campoObrigatorio.ambiente=O campo \\'Ambiente\\' deve ser informado.
campoObrigatorio.ambienteEscolhido=O campo \\'Ambiente escolhido\\' deve ser informado.
campoObrigatorio.nrMaximoConvidados=O campo \\'N\u00FAmero m\u00E1ximo de convidados\\' deve ser informado.
campoObrigatorio.tipoOperacao=O campo \\'Tipo de Opera\u00E7\u00E3o\\' deve ser informado.
campoObrigatorio.formaCalculo=O campo \\'Forma de C\u00E1lculo\\' deve ser informado.
campoObrigatorio.bemConsumo=O campo \\'Bem de Consumo\\' deve ser informado.
campoObrigatorio.utensilio=O campo \\'Utens\u00EDlio\\' deve ser informado.
campoObrigatorio.brinquedo=O campo \\'Brinquedo\\' deve ser informado.
campoObrigatorio.perfilEvento=O campo \\'Perfil do Evento\\' deve ser informado.
campoObrigatorio.ambiente=Algum \\'Ambiente\\' deve ser selecionado.
campoObrigatorio.ambiente.ambienteRelacionado=Pelo menos um Ambiente deve ser selecionado.
campoObrigatorio.ambiente.nrConvidados=Todo ambiente deve ter seu n\u00FAmero de convidados informado.
campoObrigatorio.ambiente.horarios=Preencha todos os hor\u00E1rios dos ambientes.
campoObrigatorio.condicaoPagamento=Alguma \\'Condi\u00E7\u00E3o de Pagamento\\' deve ser selecionada.
campoObrigatorio.descricao=O campo \\'Descri\u00E7\u00E3o\\' deve ser informado.
campoObrigatorio.estoque=O campo \\'Estoque\\' deve ser maior do que 0.
campoObrigatorio.minEstoque=O campo \\'M\u00EDnimo Estoque\\' deve ser maior do que 0.
campoObrigatorio.tipo=Algum \\'Tipo\\' deve ser selecionado.
campoObrigatorio.horaInvalida=Hor\u00E1rio inv\u00E1lido.
campoObrigatorio.horaFinalMenorHoraInicial=Hor\u00E1rio Final n\u00E3o pode ser maior do que Hor\u00E1rio Inicial.
campoObrigatorio.minimoMaiorMaximo = Quantidade m\u00EDnima maior que a m\u00E1xima.
campoObrigatorio.perfilEvento.modeloContrato=Ao menos um \\'Modelo de Contrato\\' deve ser relacionado ao Perfil de Evento.
campoObrigatorio.agendaVisita.tipoVisita=Selecione um tipo de visita.
campoObrigatorio.agendaVisita.horaInicial=Selecione um hor\u00E1rio dispon\u00EDvel.
campoObrigatorio.agendaVisita.horaFinal=Para o tipo de visita \\'Em aberto\\' um hor\u00E1rio final deve ser informado.
campoObrigatorio.tipoDescontoOrcamento=Escolha um tipo de desconto.
campoObrigatorio.valorDesconto=Informe o valor do desconto.
campoObrigatorio.definir.ambiente=Selecione um ambiente, antes de adicionar outro ambiente.
campoObrigatorio.definir.ambiente.nrConvidados=Selecione o ambiente antes de informar n\u00FAmero de convidados.
campoObrigatorio.definir.horario.inicial.ambiente=Defina o hor\u00E1rio inicial para o ambiente, antes de adicionar outro ambiente.  
campoObrigatorio.definir.horario.final.ambiente=Defina o hor\u00E1rio final para o ambiente, antes de adicionar outro ambiente.

contato.adiamentoPreReserva=Adiamento da pr\u00E9-reserva.
contato.agendarVisita=Agendar Visita.
contato.autorizarEvento=Evento autorizado.
contato.realizarCheckList=CheckList Realizado.
contato.confirmarEvento=Evento confirmado.
contato.encerrarEvento=Evento encerrado.
contato.conversa=Conversa salva.
contato.emitirCheclist=Checklist emitido.
contato.emitirContrato=Contrato enviado.
contato.emitirOrcamento=Or\u00E7amento enviado.
contato.eventoCancelado=Evento cancelado.
contato.orcamentoSalvo=Or\u00E7amento do evento salvo.
contato.preReserva=Evento pr\u00E9-reservado.

caucao.credito.chequeCaucao=Cheque Cau\u00E7\u00E3o
caucao.credito.credito=Cr\u00E9dito
caucao.credito.quitacao.depositocreditocliente=Depositado em cr\u00E9dito do cliente

log.sucesso=Exibi\u00E7\u00E3o do Log.

fornecedor.validacao.servico = Informe o servi\u00E7o

# Mensagens da tela de cadastro de Locais
localAcesso_nomeComputador=Informe o nome do computador que este Local ser\u00E1 operado.
localAcesso_servidorImpressoes=Informe o nome do computador onde est\u00E1 instalado o servi\u00E7o de biometria.
localAcesso_pedirSenhaLibParaCadaAcesso=Se n\u00E3o estiver marcado, ser\u00E1 necess\u00E1rio informar o login de autoriza\u00E7\u00E3o somente uma vez para as libera\u00E7\u00F5es de acesso de Entrada e Sa\u00EDda; Se estiver marcado, ser\u00E1 necess\u00E1rio informar o login de autoriza\u00E7\u00E3o para cada libera\u00E7\u00E3o de acesso.
localAcesso_portaServidorImp=Informe a porta do servidor de impresso\u00F5es. Porta Default:5100
localAcesso_utilizarModoOffline=Habilita o modo de Valida\u00E7\u00E3o de Acesso Off-line, que n\u00E3o exige o acesso a Internet para a catraca funcionar. Por\u00E9m, \u00E9 necess\u00E1rio que o servi\u00E7o de gera\u00E7\u00E3o dos dados off-line esteja devidamente configurado e executado na madrugada do pr\u00F3prio dia.

agendavisita.realizada=Esta visita j\u00E1 ocorreu!

# --------------------------------------------------------------
# Mensagens de ajuda da tela de relat\u00F3rio de clientes
# --------------------------------------------------------------

ajuda_coluna_agrupamento=Aqui voc\u00EA escolhe as colunas que ser\u00E3o exibidas no relat\u00F3rio e por qual item a exibi\u00E7\u00E3o dos dados ser\u00E1 agrupada.
ajuda_situacoes_contrato=O relat\u00F3rio exibir\u00E1 os contratos agrupados por clientes e sua situa\u00E7\u00F5es no per\u00EDdo de data ou data exata informada. Escolha as situa\u00E7\u00F5es que ser\u00E3o utilizadas como filtros.
ajuda_plano=Escolha os planos e suas depend\u00EAncias usadas como filtro da consulta.
ajuda_colaborador=Escolha os colaboradores usados como filtro da consulta.
ajuda_imprimir_rel_cliente=Abre uma janela pop-up com o relat\u00F3rio gerado em PDF.
ajuda_filtrar_data=Escolha como ir\u00E1 filtrar as datas: data exata, per\u00EDodo de data ou por semana de um m\u00EAs. Ser\u00E3o exibidos clientes e seus contratos desse per\u00EDodo, com a situa\u00E7\u00E3o em que se encontravam.
ajuda_empresa=Se o banco de dados possui mais de uma empresa, o relat\u00F3rio e os filtros ir\u00E3o ser disponibilizados ap\u00F3s a escolha de empresa.
ajuda_robot_control=Este processo utiliza o objeto Rob\u00F4 do ZillyonWeb para executar as suas rotinas. O Rob\u00F4 possui duas fontes de dados distintas: Cliente e Contrato. Portanto: se voc\u00EA informar uma ou mais matr\u00EDculas de Clientes, ele executar\u00E1 as rotinas inerentes para estas matr\u00EDculas; se voc\u00EA informar os c\u00F3digos dos contratos, onde haver processamento sobre Contratos, estes ser\u00E3o utilizados. Consequentemente, se voc\u00EA n\u00E3o informar um contrato, n\u00E3o haver\u00E1 nada a ser feito sobre Contratos; se voc\u00EA n\u00E3o informar um cliente, nada acerca de Clientes ser\u00E1 executado.


msg_dados_prontos_cancelar= Dados Prontos para Cancelar
msg_recibo_nao_pode_estornar=Este recibo n\u00E3o pode ser estornado porque possui cheques ou cart\u00F5es que se encontram em lotes do Financeiro Web.
msg_contrato_nao_pode_estornar=Este contrato n\u00E3o pode ser estornado porque possui cheques ou cart\u00F5es que se encontram em lotes do Financeiro Web. Para maiores informa\u00E7\u00F5es
msg_produto_nao_pode_estornar=Este produto n\u00E3o pode ser estornado porque possui cheques ou cart\u00F5es que se encontram em lotes do Financeiro Web. Para maiores informa\u00E7\u00F5es
msg_cheques_retirados=Cheques retirados com sucesso!
msg_cartoes_retirados=Cart\u00F5es retirados com sucesso!
msg_transferirMostrar=Utilize este recurso para transferir todos os alunos da carteira de um colaborador para outro do mesmo tipo de v\u00EDnculo.
msg_transferirVinculos=V\u00EDnculos transferidos com sucesso! Atualize os dados para visualizar os dados corretos.
msg_transferirMesmo=Selecione consultores diferentes.
msg_transferirValidar=Selecione origem e destino.
msg_dadosEnviados= Dados enviados com sucesso.
msg_naoPossuiCaixaAbertoEscape=O usu\u00E1rio n\u00E3o possui Caixa aberto.\u00C9 necess\u00E1rio abrir o Caixa antes de realizar a opera\u00E7\u00E3o.
msg_lote_alterado_sucesso=O lote foi alterado com sucesso. Se voc\u00EA alterou a data de dep\u00F3sito do lote, o sistema alterou tamb\u00E9m a data de quita\u00E7\u00E3o do lan\u00E7amento associado a ele.
msg_nfse_gerado_automatico=Nota Fiscal Eletr\u00F4nica de Servi\u00E7o enviada automaticamente. Resultado
msg_nfse_gerado_automatico_problema=Problema ao enviar Nota Fiscal Eletr\u00F4nica de Servi\u00E7o
msg_nfce_gerado_automatico=Nota Fiscal de Consumidor Eletr\u00F4nica enviada automaticamente.
msg_nfce_gerado_automatico_problema=Problema ao enviar Nota Fiscal de Consumidor Eletr\u00F4nica
msg_responder_bv_sessao=Para fechar a compra de sess\u00E3o para este aluno, voc\u00EA deve solicitar que ele responda o BV.
msg_finalizar_venda=Salva o BV e retorna para a venda de sess\u00E3o em andamento.
msg_simulacao_efetuada=Simula\u00E7\u00E3o efetuada com sucesso!
msg_reajuste_efetuado=Reajuste Monet\u00E1rio aplicado com sucesso!
msg_notajaemitida=A NFSe deste recibo j\u00E1 foi enviada. Deseja enviar novamente?
msg_finan_observacao_retirada_lote=O campo "Observa\u00E7\u00E3o" deve ser informado.
msg_finan_nenhum_item_selecionado=Nenhum item selecionado.

msg_finan_estorno_grave=Clique em gravar para confirmar o estorno.

msg_local_acesso_empresa_nao_contem=Empresa selecionada n\u00E3o possui nenhum local de acesso que n\u00E3o utilize o modo offline configurado.
msg_coletor_nao_contem=N\u00E3o foram encontrados coletores para este local de acesso.
msg_selecione_empresa_remota= Selecione a empresa remota.
msg_informe_matriculaounome=Informe uma matr\u00EDcula ou nome de aluno para fazer a consulta.
msg_informe_matriculaounomecol=Informe uma matr\u00EDcula ou nome de colaborador para fazer a consulta.

msg_mesma_chave=A chave n\u00E3o pode ser a mesma usada na aplica\u00E7\u00E3o local.

msg_valor_maior_credito=O novo saldo da conta n\u00E3o pode ser maior do que o atual.
msg_valor_menor_debito=O novo saldo da conta n\u00E3o pode ser menor do que o atual.
msg_valor_maior_debito_zero=O novo saldo da conta n\u00E3o pode ser maior do que o zero.
msg_valor_menor_credito_zero=O novo saldo da conta n\u00E3o pode ser menor do que o zero.
msg_planocontas_repovoados=O plano de contas foi repovoado com sucesso!
msg_valor_nao_pode_sermaior_pago=Valor n\u00E3o pode ser maior do que o pago pelo cliente.
msg_produto_excluir_validacaoacesso=Este produto n\u00E3o pode ser exclu\u00EDdo pois esta sendo usado como valida\u00E7\u00E3o de acesso no local de acesso:
msg_autorizacao_adicionada=Autoriza\u00E7\u00E3o adicionada com sucesso!
msg_autorizacao_removida=Autoriza\u00E7\u00E3o removida!

CONTATO=Contato com o cliente
TROCAR_CARTAO=Trocar cart\u00E3o
REALIZAR_CONTATO_CIELO=Realizar contato Cielo
REALIZAR_REENVIO=Realizar reenvio
REALIZAR_REENVIO_CANCELAMENTO=Realizar envio para cancelamento
OUTROS=Outros

msg_preencha_todos_dados_acao=Preencha todos os dados (C\u00F3digo status e A\u00E7\u00E3o associada)
msg_bloqueio_lancado=Bloqueio realizado com sucesso!
msg_bloqueios_lancado=Bloqueios realizados com sucesso!
msg_desbloqueio_lancado=Desbloqueio realizado com sucesso!
msg_desbloqueios_lancado=Desbloqueios realizados com sucesso!

msg_tente_outra_vez=Ocorreu um erro ao selecionar o aluno, tente outra vez.

msg_desconsiderarDataTurmas=Com esta op\u00E7\u00E3o o relat\u00F3rio ir\u00E1 considerar as turmas vigentes do contrato, independente do per\u00EDodo consultado. Ideal para casos de renova\u00E7\u00E3o antecipada, onde o pagamento se d\u00E1 antes do in\u00EDcio do contrato.
msg_produtos_lancados_com_sucesso=produtos lan\u00E7ados com sucesso!
msg_onclick_conferir_negociacao=Voc\u00EA n\u00E3o informou os dados para autoriza\u00E7\u00E3o de cobran\u00E7a. Deseja continuar assim mesmo?
msg_produtos_estornados_com_sucesso_log=Produtos em aberto foram estornados. Confira o log para saber quem foram os estornados.
percentual_nao_pode_ser_maior_100=Percentual n\u00E3o pode ser maior do que 100!
informe_nome_sobrenome=Informe nome e sobrenome!
informe_um_dos_filtros=Informe o termo da consulta!
cliente_de_outra_empresa=Este cliente \u00E9 de outra unidade. Clique em 'Transferir cliente para esta empresa' para traz\u00EA-lo para sua unidade. Caso o cliente possua parcela(s) vencida(s) em aberto ele não será transferido.

title_cpf_titular=O CPF do Titular da conta \u00E9 usado nos casos onde o cliente n\u00E3o \u00E9 o dono da conta informada na autoriza\u00E7\u00E3o de cobran\u00E7a. Se estiver vazio, o sistema usar\u00E1 o CPF do cadastro da pessoa.
lancamento_retirada_nao_excluir=Lan\u00E7amentos de retirada de receb\u00EDveis n\u00E3o podem ser exclu\u00EDdos pois eles mant\u00E9m a integridade do seu financeiro. Este lan\u00E7amento n\u00E3o impede que voc\u00EA fa\u00E7a devolu\u00E7\u00F5es ou estorno de cheques ou cart\u00F5es.
msg_erro_pacote_personal=Para adicionar um pacote de cr\u00E9ditos de personal, informe quantidade e valor, que devem ser maiores do que zero.
produto_credito_personal_somente_colaborador=A venda de porodutos do tipo cr\u00E9dito de personal s\u00F3 \u00E9 permitida para colaboradores.
tempoCheckOutMaiorDuracaoCredito=O tempo para check-out autom\u00E1tico n\u00E3o pode ser menor do que a dura\u00E7\u00E3o do cr\u00E9dito.

usuariomovel_sincronizado=Usu\u00E1rio Movel alterado com sucesso!

nossa_chave_carregada=Nossa chave carregada com sucesso!
nossa_chave_itau_carregada=Chave privada certificado ITAÚ carregada com sucesso!
chave_getnet_carregada=Chave GETNET carregada com sucesso!
chave_itau_carregada=Certificado ITAÚ carregado com sucesso!
chave_bin_carregada=Chave BIN carregada com sucesso!
armarios_gerados_sucesso=Arm\u00E1rios gerados com sucesso!
armario_excluido=Arm\u00E1rio exclu\u00EDdo com sucesso!
armario_inativado=Arm\u00E1rio n\u00E3o pode ser exclu\u00EDdo pois j\u00E1 foi aberto, por isso ele foi inativado. 
armario_alterado=Arm\u00E1rio alterado com sucesso!
produto_nao_armario=Selecione um produto do tipo arm\u00E1rio.
armario_aberto=Arm\u00E1rio aberto.
datafinal_aluguel_alterada_sucesso=Data final do aluguel alterada com sucesso!

extrato_diario_data_incompativel=A data do pagamento encontrado no sistema com esse c\u00F3digo de autoriza\u00E7\u00E3o, n\u00E3o bate com o item do Extrato di\u00E1rio. Voc\u00EA pode alter\u00E1-la clicando no bot\u00E3o abaixo.
extrato_diario_valor_incompativel=O valor do pagamento encontrado no sistema com esse c\u00F3digo de autoriza\u00E7\u00E3o, n\u00E3o bate com o item do Extrato di\u00E1rio. Verifique o lan\u00E7amento no sistema e se necess\u00E1rio, refa\u00E7a-o.
produto_atestado_aptidao_fisica_nao_informado=Informe um produto do tipo Atestado para continuar
justificativa_valor_muito_longo=Valor muito longo para o campo (justificativa), limite de 50 caracteres.
dados_impressao_marcar_ao_menos_uma_opcao=Marque ao menos uma das duas op\u00E7\u00F5es para que o relat\u00F3rio possa ser gerado.
relacao_prod_colab_colaborador_inativo=O colaborador relacionado da edi\u00E7\u00E3o est\u00E1 inativo, selecione um colaborador ativo antes de gravar.
relacao_prod_colab_categoria_sem_produto=N\u00E3o existe produto para essa categoria, selecione outra categoria!
hintnrCreditosTreinoRenovar=Esta configura\u00E7\u00E3o diz o quando o cliente que usa cr\u00E9dito de treino ir\u00E1 entrar na meta de Renova\u00E7\u00E3o. Se o valor for 0(zero), o recurso ser\u00E1 desabilitado.
extrato_mesclar=Ao realizar esta opera\u00E7\u00E3o, voc\u00EA est\u00E1 informando que este lan\u00E7amento do sistema corresponde ao item do extrato di\u00E1rio selecionado. Confirma?

msg_operacao_sucesso=Opera\u00E7\u00E3o Realizada com sucesso!
msg_dados_gravados_processo_pendencias=Dados Gravados com Sucesso. Houveram altera\u00E7\u00F5es nos campos de cliente e visitante, portanto o sistema iniciou um processo interno que atualizar\u00E1 as mensagens de pend\u00EAncias dos clientes.
msg_turma_contrato_associdado=N\u00E3o foi poss\u00EDvel realizar a exclus\u00E3o, pois j\u00E1 houveram negocia\u00E7\u00F5es utilizando esta turma!
msg_turma_reposicao_associdada=N\u00E3o foi poss\u00EDvel realizar a exclus\u00E3o, pois existem reposi\u00E7\u00F5es utilizando esta turma!
msg_por_favor_escolha_empresa=Por favor, escolha uma empresa!
msg_erro_codigo_seguranca=C\u00F3digo de seguran\u00E7a n\u00E3o confere
msg_xml_processado_sucesso=Arquivo XML processado com sucesso, verifique as informa\u00E7\u00F5es e confirme a opera\u00E7\u00E3o.
msg_produtos_processados_sucesso=Produtos processados com sucesso. Verifique as informa\u00E7\u00F5es e confirme a opera\u00E7\u00E3o.
msg_produtos_processados_atualizados_sucesso=Produtos da NFe processados e atualizados com sucesso.

msg_camp_tipo_usuario=O campo TIPO USU\u00C1RIO (Usu\u00E1rio) deve ser informado.
msg_camp_nome_cliente=O campo NOME DO CLIENTE (Usu\u00E1rio) deve ser informado.
msg_camp_nome_cliente_ch=O campo NOME DO CLIENTE (Usu\u00E1rio) n\u00E3o pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).
msg_camp_nome_colaborador=O campo NOME DO COLABORADOR (Usu\u00E1rio) deve ser informado.
msg_camp_nome_colaborador_ch=O campo NOME DO COLABORADOR (Usu\u00E1rio) n\u00E3o pode conter caracteres especiais (!@#$%&*}{][?:><,\\|/\"+).
msg_camp_data_nasc_usuario=O campo DATA NASCIMENTO (Usu\u00E1rio) deve ser informado.
msg_camp_cpf_usuario=O campo CPF (Usu\u00E1rio) deve ser informado.
msg_camp_nome_de_usuario=O campo NOME DE USU\u00C1RIO (Usu\u00E1rio) deve ser informado.
msg_camp_senha_usuario=O campo SENHA (Usu\u00E1rio) deve ser informado.
msg_camp_nome_empresa_usuario=O campo NOME DA EMPRESA  (Usu\u00E1rio)  deve ser informado
msg_camp_senha_empresa_usuario=O usu\u00E1rio do Service foi informado mas n\u00E3o a senha.
msg_camp_email_nao_inf_usuario=O campo EMAIL (Usu\u00E1rio) deve ser informado.
msg_camp_email_usuario=O campo EMAIL (Usu\u00E1rio) \u00E9 inv\u00E1lido!
msg_camp_cliente_usuario=O campo Cliente (Usu\u00E1rio) deve ser informado.
msg_camp_colaborador_usuario=O campo Colaborador (Usu\u00E1rio) deve ser informado.
msg_camp_nome_usuario=O campo Nome (Usu\u00E1rio) deve ser informado.

msg_err_empres_usuario_senha=Acesso Negado! Empresa , Usu\u00E1rio e Senha n\u00E3o Conferem!
msg_err_data_alteracao_senha=A data da \u00FAltima altera\u00E7\u00E3o de senha desse usu\u00E1rio deve ser informada.
msg_err_acesso_horario=Acesso n\u00E3o permitido para esse hor\u00E1rio.
msg_err_embaralhar_banco=Falha ao embaralhar dados do banco
msg_entrar_em_contato=Entrar em contato!
msg_err_usuario_perm_senha=Usu\u00E1rio n\u00E3o possui permiss\u00E3o para alterar sua pr\u00F3pria senha
msg_err_nome_usuario_inf=O campo NOME USU\u00C1RIO (Usu\u00E1rio) deve ser informado
msg_err_user_name_usuario_inf=O campo USER NAME (Usu\u00E1rio) deve ser informado
msg_err_senha_usuario_inf=O campo SENHA ATUAL (Usu\u00E1rio) deve ser informado
msg_err_senha1_usuario_inf=O campo NOVA SENHA (Usu\u00E1rio) deve ser informado
msg_err_senha2_usuario_inf=O campo CONFIRMAR SENHA (Usu\u00E1rio) deve ser informado
msg_err_acessa_via_oamd="Usuario %s s\u00F3 pode acessar via OAMD"
msg_sistema_expirado=Sistema expirado.
msg_sistema_bloqueio=Favor entrar em contato com a Pacto.<br>Ligue (62) 3251-5820.<br>Fale com o Dep. Administrativo.
msg_sistema_bloq_meio_dia=Sistema ser\u00E1 expirado HOJE a partir do meio dia!
msg_sistema_expra_dia=Sistema ser\u00E1 expirado dentro de %d dia(s).
msg_cont_dep_adm="Favor entrar em contato com a Pacto.<br>Ligue (62) 3251-5820.<br>Fale com o Dep. Administrativo.
msg_nenhum_mod_pric_habilitado=Nenhum m\u00F3dulo principal habilitado para esta Empresa.
msg_atu_banco_dados_vers_menor=O Banco de dados do sistema encontra-se na vers\u00E3o %d enquanto a vers\u00E3o especificada da aplica\u00E7\u00E3o \u00E9 a %d.
msg_atu_banco_dados_vers_maior=O Banco de dados do sistema encontra-se em uma vers\u00E3o superior \u00E0 especificada da aplica\u00E7\u00E3o. O Banco est\u00E1 na vers\u00E3o %d enquanto a especificada na aplica\u00E7\u00E3o \u00E9 a %d .
msg_rotina_ja_processada_usuario=Rotina j\u00E1 foi processada por outro usu\u00E1rio.. tente logar novamente
msg_rotina_processada_com_sucess_logar=Rotina processada com sucesso.. tente logar novamente
msg_robo_desatualizado=N\u00E3o foi possivel logar no sistema, pois houve um problema com o servidor.\n\rVoc\u00EA est\u00E1 tentando logar com a data do dia: %s.\n\rE o rob\u00F4 est\u00E1 DESATUALIZADO, a \u00FAltima vez em que o rob\u00F4 processou sua rotina foi no dia  %s. Voc\u00EA pode resolver este problema clicando no bot\u00E3o abaixo e aguardar at\u00E9 que seja finalizado este processamento. Isso levar\u00E1 poucos minutos.
msg_robo_verif_data_excu=N\u00E3o foi possivel logar no sistema, pois houve um problema com a data do servidor.\n\rVoc\u00EA est\u00E1 tentando logar com a data do dia: %s.\n\r E foi encontrado no banco de dados uma data maior que \u00E9: %s chame o administrador do sistema para modificar a data do servidor para %s
msg_banco_atu_sucess=O Banco de Dados foi atualizado com sucesso
msg_err_perm_acss_inclu=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \"INCLUIR %s \"
msg_err_perm_acss_atulz=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \"ALTERAR %s \"
msg_err_perm_acss_exclu=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \"EXCLUIR %s \"
msg_err_perm_acss_consul=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \"CONSULTAR %s\"
msg_err_perm_acss_relatr=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \"EMITIR RELAT\u00D3RIO %s \"
msg_err_perm_acss_funcion=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \" %s \"
msg_err_acss_em_branco=O nome da A\u00E7\u00E3o verificada est\u00E1 em Branco.
msg_err_acss_ususario_em_branco=O USU\u00C1RIO a ser verificado est\u00E1 em Branco
msg_err_acss_prfl_usu_branco=O Perfil Acesso do usu\u00E1rio a ser verificado est\u00E1 em Branco
msg_err_perm_acss_operacao=Este Usu\u00E1rio (%s - %s) n\u00E3o possui permiss\u00E3o para esta opera\u00E7\u00E3o, \"%s \"
msg_err_perm_relatorio=Para gerar o relat\u00F3rio escolha seu nome de operador ou inclua a permiss\u00E3o LIBERAR RELAT\u00D3RIO DE FECHAMENTO DE CAIXA POR OPERADOR - TODOS OS COLABORADORES
msg_err_usuario_senha=Usu\u00E1rio e Senha n\u00E3o Confere.
msg_err_usuario_desativado=Usu\u00E1rio desativado!
msg_err_usuario_inativo=Usu\u00E1rio inativo.
msg_err_empresa_inativa=Empresa inativa.
msg_err_data_inic_maior_fim=A data de in\u00EDcio agendamento n\u00E3o poder ser maior que a data fim.
msg_err_inf_data_fim_agendamento=A informe a data prazo fim para agendamento.
msg_err_inf_data_inic_agendamento=A informe a data prazo inicio para agendamento.
msg_err_vender_produto=N\u00E3o \u00E9 poss\u00EDvel vender o produto %s pois seu estoque atual \u00E9 %d.
msg_err_vender_produto_data_venc=O produto %s n\u00E3o pode ser adicionado na lista de venda, pois o mesmo est\u00E1 com a (DATA FINAL VIG\u00CANCIA) vencido.
msg_err_vender_produto_desc=O produto de descri\u00E7\u00E3o: %s n\u00E3o pode ser vendido.
msg_err_camp_emp_venda_avulsa=O campo Empresa (Venda Avulsa) deve ser informado.
msg_err_camp_tipo_cobrador=O campo TIPO DE COMPRADOR deve ser informado.
msg_err_camp_nome_cobrador=O campo NOME COMPRADOR (Venda Avulsa) deve ser informado.
msg_err_realiz_vend_cons_prod=Para realizar a venda \u00E9 necess\u00E1rio consumir pelo menos um produto.
msg_apliq_desc_manual_limp=Aplique o desconto manual ou limpe os descontos antes de inserir o produto
msg_err_prod_cred_personal=Produto Cr\u00E9dito de personal s\u00F3 deve ser vendido para personais.
msg_err_cred_pers_intern=Para personal interno, escolha o produto CR\u00C9DITO DE PERSONAL INTERNO.
msg_err_cred_pers_extern=Para personal externo, escolha o produto CR\u00C9DITO DE PERSONAL EXTERNO.
msg_err_cliente_nao_encontrado=Cliente n\u00E3o encontrado.
msg_err_conctar_treino_cred_personal=N\u00E3o foi poss\u00EDvel conectar ao TreinoWeb para registrar os cr\u00E9ditos do personal. Tente novamente.
msg_err_conctar_treino_estorno_recibo_cred_person=N\u00E3o foi poss\u00EDvel estornar o recibo pois ele paga Cr\u00E9ditos de Personal e a conex\u00E3o com o TreinoWeb pode estar comprometida.
msg_err_dados_venda_avuls=Dados N\u00E3o Encontrados ( VendaAvulsa ).
msg_empresa_informada=Empresa deve ser informada.
msg_err_camp_data_reg_mov_parc=O campo DATA DE REGISTRO (Movimento da Parcela) deve ser informado.
msg_err_camp_stiuacao_mov_parc=O campo SITUA\u00C7\u00C3O (Movimento da Parcela) deve ser informado.
msg_err_camp_contrato_mov_parc=O campo CONTRATO (Movimento da Parcela) deve ser informado.
msg_err_camp_responsavel_mov_parc=O campo RESPONS\u00C1VEL  (Movimento da Parcela) deve ser informado.
msg_err_camp_conven_cobrc_mov_parc=O campo CONV\u00CANIO DE COBRAN\u00C7A (Movimento da Parcela) deve ser informado.
msg_err_relatorio_dados=N\u00E3o foi poss\u00EDvel emitir o relat\u00F3rio. Dados n\u00E3o encontrados!
msg_err_negc_parcela=N\u00E3o ser\u00E1 poss\u00EDvel renegociar a %s pois ela est\u00E1 em uma remessa gerada ou aguardando retorno.
msg_err_empresa_nao_encontrada=Empresa N\u00E3o Encontrada. Entre Novamente no Sistema.
msg_err_cancelar_parcela=N\u00E3o ser\u00E1 poss\u00EDvel cancelar a %s pois ela est\u00E1 em uma remessa gerada ou aguardando retorno.
msg_err_nao_escolh_parc_canc=N\u00E3o foi escolhida nenhuma PARCELA PARA Cancelar.
msg_parcelas_canc_sucss=Parcelas Canceladas com Sucesso!!
msg_err_escolh_parc=N\u00E3o foi escolhida nenhuma PARCELA PARA PAGAR.
msg_err_realz_pagmt=N\u00E3o \u00E9 possivel realizar o pagamento, pois o campo VALOR TOTAL \u00E9 menor do que R$ 0.00
msg_err_formt_data=A data deve ser informada com o seguinte formato: dd/mm/aaaa
msg_cliente_filtro_pesq_cliente=Nenhum cliente foi encontrado com os seguintes filtros de pesquisa: CLIENTE -> %s
msg_cliente_filtro_pesq_cliente_periodo=Nenhum cliente foi encontrado com os seguintes filtros de pesquisa: PER\u00CDODO DE VENCIMENTO DE PARCELAS DE -> %s 
msg_cliente_filtro_pesq_cliente_ate=Nenhum cliente foi encontrado com os seguintes filtros de pesquisa: AT\u00C9 %s 
msg_err_camp_periodo_de_ate=O campo (PER\u00CDODO DE) n\u00E3o pode ser maior que o campo (AT\u00C9).
msg_err_renegoc_parc=Voc\u00EA est\u00E1 tentando renegociar a parcela \"%s\" do Cliente \"%s\". Por\u00E9m, ela se encontra em uma Remessa de Cobran\u00E7a (\"%s\"), que j\u00E1 foi enviada ao Banco. \u00C9 necess\u00E1rio voc\u00EA esperar o processamento do arquivo de Retorno Banc\u00E1rio. Isso \u00E9 uma medida de seguran\u00E7a que evita uma inconsist\u00EAncia do tipo \"Duplicidade de Pagamentos\".
msg_err_camp_empresa=O campo EMPRESA deve ser informado.
msg_err_camp_cliente_diaria=O campo Cliente (Di\u00E1ria) deve ser informado.
msg_err_camp_produto=O campo PRODUTO deve ser informado.
msg_err_camp_modaliadade=O campo MODALIDADE deve ser informado.
msg_venda_realiz_sucss=Venda realizada com SUCESSO!
mag_err_dados_nao_encontr=Dados N\u00E3o Encontrados ( AulaAvulsaDiaria ).
msg_err_camp_cliente=O CLIENTE deve ser informado.
msg_produto_freepass=O PRODUTO FREEPASS deve ser informado.
msg_data_inic_freepass=A data de in\u00EDcio do FreePass n\u00E3o pode ser retroativa.
msg_nao_poss_lanca_freepass=N\u00E3o \u00E9 poss\u00EDvel lan\u00E7ar FreePass com contrato vigente.
msg_ja_exst_freepass_cliente=J\u00E1 existe atualmente um FreePass para o cliente nesse per\u00EDodo.
msg_freepass_lancado=Free Pass lan\u00E7ado com Sucesso!
msg_freepass_excl_sucess=Free Pass exclu\u00EDdo com Sucesso!
msg_freepass_aluno_periodo=Existe um Free Pass para este aluno para o per\u00EDodo de %s at\u00E9 %s. \u00C9 necess\u00E1rio excluir esse Free Pass para lan\u00E7ar outro.
msg_err_inf_empres_cons=Informe a Empresa a Consultar.
msg_err_inf_person_cons=Informe o Personal a Consultar.
msg_selec_alun_des_ret_list=Selecione os Alunos que Deseja Retirar na Lista. Alunos Com Parcelas Geradas n\u00E3o Ser\u00E3o Considerados Para Exclus\u00E3o.
msg_err_data_lanc_nao_maior_data_venc=A data de lan\u00E7amento n\u00E3o pode ser maior do que a data de vencimento da parcela.
msg_err_aluno_prod_assc=O aluno %s n\u00E3o possui produto associado. Informe um produto antes de finalizar a negocia\u00E7\u00E3o
msg_err_gerar_prod_parcela=N\u00E3o Foi Poss\u00EDvel Gerar Produtos Para Parcela. Verifique se h\u00E1 Algum Aluno Selecionado.
msg_err_numinci_maior_numfim=O n\u00FAmero inicial n\u00E3o pode ser maior que o final.
msg_err_nao_poss_cds_50=N\u00E3o \u00E9 poss\u00EDvel cadastrar mais que 50 arm\u00E1rios por vez.
msg_err_armar_esta_fech=O Armario de destino est\u00E1 fechado!
msg_troca_armar=TROCA ARM\u00C1RIO : Arm\u00E1rio %s para Arm\u00E1rio: %s
msg_err_lanc_fech_arm_dtfutura=N\u00E3o \u00E9 poss\u00EDvel lan\u00E7ar fechar Arm\u00E1rio com data futura.
msg_err_parc_dtvencmenor_dtinic=Data de vencimento da parcela n\u00E3o pode ser menor que a data inicio.
msg_err_camp_empres_local=O campo EMPRESA LOCAL deve ser preenchido.
msg_err_camp_empres_remota=O campo EMPRESA REMOTA deve ser preenchido.
msg_err_camp_nome_pessoa=O campo NOME PESSOA deve ser preenchido.
msg_err_usu_inf_perf_nao_inf=O usu\u00E1rio informado n\u00E3o tem nenhum perfil acesso informado.
msg_err_parc_renegociar=Por favor, selecione as parcelas que deseja renegociar
msg_err_vlr_desj_negativo=O Valor desejado n\u00E3o poder\u00E1 ser negativo.
msg_err_vlr_desj_maior_vlr_div=O Valor desejado n\u00E3o poder\u00E1 ser maior do que o valor da d\u00EDvida.
msg_perct_desc_sup_100=O Percentual de desconto n\u00E3o pode ser superior a 100%
msg_perct_desc_infr_0=O Percentual de desconto n\u00E3o pode ser inferior a 0%
msg_err_resi_lancados=Existem res\u00EDduos para serem lan\u00E7ados
msg_err_add_desc_renegoc=\u00C9 necess\u00E1rio adicionar desconto ou Taxa/Juros na renegocia\u00E7\u00E3o.
msg_err_data_vencimento_anterior=Data de vencimento n\u00E3o pode ser anterior a %s.
msg_err_nenhum_cliente_encontrado=Nenhum Cliente encontrado !
msg_err_nenhuma_modalidade_encontrada=Nenhuma Modalidade encontrada !
msg_err_excluir_tipo_modalidade=N\u00E3o Foi Poss\u00EDvel Realizar esta Opera\u00E7\u00E3o
msg_nenhum_registro_encontrado=Nenhum registro encontrado no per\u00EDodo.
msg_transacao_concluida_descart_gerada_nova=Transa\u00E7\u00E3o conclu\u00EDda com sucesso! Esta transa\u00E7\u00E3o foi descartada e gerada uma nova: %s em\: %s
msg_err_nao_concluir_trans=N\u00E3o foi poss\u00EDvel concluir a transa\u00E7\u00E3o: %s
msg_transacao_confirmada_sucss=Transa\u00E7\u00E3o confirmada com sucesso!
msg_transacao_cancelada_sucss=Transa\u00E7\u00E3o cancelada com sucesso!
msg_err_nao_cancelar_trans=N\u00E3o foi poss\u00EDvel cancelar a transa\u00E7\u00E3o: %s
msg_transacao_descartada_sucss=Transa\u00E7\u00E3o descartada com sucesso!
msg_err_nao_descartar_trans=N\u00E3o foi poss\u00EDvel descartar a transa\u00E7\u00E3o.
msg_err_cliente_nao_inicializado=Cliente N\u00E3o Inicializado.
msg_err_selecione_1tipo_parcela=Selecione pelo menos um tipo de parcela.
msg_err_selecione_convenio_cobranca=Selecione um Conv\u00EAnio de Cobran\u00E7a. Para esta opera\u00E7\u00E3o, n\u00E3o pode ser todos.
msg_err_nao_existe_parcela_em_aberto_remessa=N\u00E3o existe parcela em aberto para gerar remessa no periodo informado.
msg_criar_remessas_vencimento_de=Criar remessa com parcelas cujo vencimento \u00E9 de %s at\u00E9 %s ?
msg_total_itens_valor_de=Total de itens\: <b>%d</b> no valor de <b>R$ %s</b>.
msg_remessas_criadas_sucesso=Remessa(s) \"%s\" criada(s) com sucesso!
msg_info_datas_inicio_fim_consulta=Informe as datas de in\u00EDcio e fim para a consulta.
msg_err_header_arqu_incompativel_conv_selec=Erro: Header de Arquivo incompat\u00EDvel com o Tipo de Conv\u00EAnio Selecionado -> %s
msg_err_nenhuma_parc_retorno=Erro: Nenhuma parcela foi encontrada para este retorno.
msg_err_arq_incomptivel_conta_convenio=Erro: Arquivo incompat\u00EDvel com a Conta Corrente/Ag\u00EAncia do Conv\u00EAnio Selecionado.
msg_err_boleto_nenhum_encontrado_sistema=Erro: Nenhum boleto foi encontrado no sistema para este retorno.
msg_err_remessa_compativel_retorno=Erro: Mais de uma Remessa compat\u00EDvel com este Retorno foi encontrada.
msg_entre_contato_pacto=Entre em contato com a Pacto para resolver isso
msg_arquivo_processado_as=Arquivo %s Processado \u00E0s %s
msg_remessas_retorno_totalmente_processado=Remessas com retorno totalmente processado\: %s <br/>
msg_remessas_com_itens_a_serem_processados=Remessas com itens a serem processados ainda\: %s
msg_processamento_concluido_clique_resultado=Processamento conclu\u00EDdo. Clique no bot\u00E3o resultado para maiores detalhes.
msg_cpfs_invalidos=CPFs Inv\u00E1lidos\: %s
msg_err_gerar_remessa_convenio_inativo=N\u00E3o e possivel gerar uma remessa para um conv\u00EAnio inativo
msg_remessa_cancelamento_sucesso=Remessa de cancelamento gerada com sucesso!
msg_precissa_permissao=Voc\u00EA precisa da permiss\u00E3o \"%s\"
msg_saldo_transacoes=Seu saldo \u00E9 de %d transa\u00E7\u00F5es.
msg_saldo_esta_restam_apenas_transacoes=Seu saldo est\u00E1 no fim! Restam apenas %d transa\u00E7\u00F5es.
msg_limite_emergencial_dcc=O limite emerg\u00EAncial de DCC foi ativado. Voc\u00EA j\u00E1 consumiu %d de %d transa\u00E7\u00F5es.
msg_nao_possui_saldo_dcc=Voc\u00EA n\u00E3o possui saldo DCC.
msg_err_creditos_bloqueados_dia=Seus cr\u00E9ditos ser\u00E3o bloqueados no dia %d.
msg_err_creditos_serao_bloqueados=Seus cr\u00E9ditos est\u00E3o bloqueados.
msg_err_inicializar_tela_pagamento=N\u00E3o foi poss\u00EDvel inicializar tela de pagamento
msg_total_repescagem=Total: %d - Repescagem: %d
msg_nao_selecionado_nota=N\u00E3o foi selecionado nenhuma nota.
msg_err_empresa_parte_rede_empresa_indentificador=Empresa faz parte de uma rede de empresa e n\u00E3o tem um identificador configurado. \u00C9 necess\u00E1rio fazer essa configura\u00E7\u00E3o
msg_arqui_tipo_nao_aceito=Arquivo com tipo n\u00E3o aceito. Arquivos de retorno s\u00F3 podem ser do tipo ".ret", ".txt", ".cmp" ou "crt".
msg_nao_abrir_armario_parcl_aberto=N\u00E3o \u00E9 poss\u00EDvel abrir Arm\u00E1rio com parcelas em aberto.
msg_campos_data_preenchidos=Os campos de data devem ser preenchidos.
msg_err_registros_comissionaveis=N\u00E3o h\u00E1 registros comission\u00E1veis para o per\u00EDodo informado.
msg_empresa_config_pg_comissao_meta_finan=A empresa '%s ' foi configurada para pagar comiss\u00E3o somente se atingir a Meta Financeira. Como a meta financeira leva em considera\u00E7\u00E3o o faturamento recebido, o c\u00E1lculo da comiss\u00E3o ficar\u00E1 correto caso seja escolhido o filtro 'Por faturamento recebido'.
msg_escolha_tipo_dados=Escolha um Tipo dos Dados
msg_escolhar_empresa=Escolha uma Empresa
msg_err_usuario_nao_encontrado=Usu\u00E1rio N\u00E3o Encontrado. Entre Novamente no Sistema.
msg_err_consulta_retorn_resultados=A consulta n\u00E3o retornou resultados.
msg_err_retornar_dados_contacte_suporte=Erro ao retornar dados selecionados. Contate Suporte T\u00E9cnico.
msg_err_encontrar_horario=N\u00E3o foi poss\u00EDvel encontrar o Hor\u00E1rio correto. Contate suporte t\u00E9cnico
msg_verifique_dados_turma_chamada=Verifique se os dados da turma est\u00E3o corretos antes de fazer a chamada.
msg_err_registro_impresso=Nenhum registro a ser impresso, fa\u00E7a a consulta novamente.
msg_err_usuario_nao_encontrado=Usu\u00E1rio N\u00E3o Encontrado. Entre Novamente no Sistema.
msg_err_consulta_retorn_resultados=A consulta n\u00E3o retornou resultados.
msg_err_retornar_dados_contacte_suporte=Erro ao retornar dados selecionados. Contate Suporte T\u00E9cnico.
msg_err_encontrar_horario=N\u00E3o foi poss\u00EDvel encontrar o Hor\u00E1rio correto. Contate suporte t\u00E9cnico
msg_verifique_dados_turma_chamada=Verifique se os dados da turma est\u00E3o corretos antes de fazer a chamada.
msg_err_registro_impresso=Nenhum registro a ser impresso, fa\u00E7a a consulta novamente.
msg_err_preencha_taxas_zeradas=Preencha as taxas que est\u00E3o zeradas
msg_err_preencha_faturamentos_zerados=Preencha os faturamentos que est\u00E3o zerados
msg_nenhuma_parcela_alterada=Nenhuma parcela foi alterada.
msg_1_parcela_alterada=1 parcela foi alterada.
msg_num_parcelas_alteradas=%d parcelas foram alteradas.
msg_err_nao_add_mesma_modalidade=N\u00E3o \u00E9 poss\u00EDvel adicionar a mesma modalidade mais de uma vez
msg_err_nao_add_mesma_turma=N\u00E3o \u00E9 poss\u00EDvel adicionar a mesma turma mais de uma vez
msg_err_nao_add_mesma_aluno=N\u00E3o \u00E9 poss\u00EDvel adicionar o mesmo aluno mais de uma vez
msg_err_colaraborador_editado=Este colaborador n\u00E3o pode ser editado.
msg_err_colaborador_qtd_agendamento=Este colaborador est\u00E1 com %d agendamento(s) futuro(s) e n\u00E3o pode ser desativado. Retire o(s) agendamento(s) futuro(s) desse colaborador antes de realizar essa operacao
msg_err_colaborador_qtd_agendamento_estudio=Este colaborador est\u00E1 com %d agendamento(s) futuro(s) e n\u00E3o pode ser retirado do tipo de colaborar Est\u00FAdio. Retire o(s) agendamento(s) futuro(s) desse colaborador antes de realizar essa operacao
msg_err_professor_turmas_ativas=O Professor est\u00E1 em %d turmas ativas, e n\u00E3o pode ser desativado.
msg_err_colaborador_ja_cadastrado=O Colaborador de nome %s e o CPF %s j\u00E1 est\u00E1 CADASTRADO na EMPRESA %s.
msg_err_colaborador_ja_cadastrado_por_dtnasc=O Colaborador de nome %s e a Data de Nascimento %s j\u00E1 est\u00E1 CADASTRADO na EMPRESA %s.
msg_err_colaborador_vinculos=Este colaborador ainda possui v\u00EDnculo(s) na(s) seguinte(s) empresa(s)<br/>%s,<br/>por favor v\u00E1 no organizador de carteiras de cada unidade listada e reorganize os v\u00EDnculos.
msg_existe_plano_mesma_descricao=J\u00E1 existe um plano cadastrado com esta descri\u00E7\u00E3o
msg_desconto_convenio_autoriazado=Desconto conv\u00EAnio autorizado.
msg_numeros_seperados_virgula=Por favor entre com os n\u00FAmeros separados por v\u00EDrgula. Ex 50,51,52.
msg_cheques_devolvidos_finan=Cheques movimentados para uma conta de devolu\u00E7\u00E3o. Foi lan\u00E7ada uma despesa correspondente.
msg_cheques_devolvidos_finan_configure=Cheques movimentados para uma conta de devolu\u00E7\u00E3o. Foi lan\u00E7ada uma despesa correspondente. Informe um plano de contas padr\u00E3o para cheques devolvidos nas configura\u00E7\u00F5es do financeiro.
msg_err_salve_usuario_antes_carregar=Salve o usu\u00E1rio antes de carregar a imagem da assinatura.
msg_err_porta_comunicacao_catraca=Porta de comunica\u00E7\u00E3o com a catraca (%s)\u00E9 obrigat\u00F3ria
msg_err_ip_comunicacao_catraca=IP de comunica\u00E7\u00E3o com a catraca (%s) \u00E9 obrigat\u00F3ria e deve ser v\u00E1lida
msg_err_campo_descr_grupo_tipo=O campo tipo deve ser informado.
convite_lancado_sucesso=Convite lan\u00E7ado com sucesso!
todos_convites_usados=Todos os convites dispon\u00EDveis para este m\u00EAs j\u00E1 foram utilizados!
msg_recebido_nao_pode_excluir=Este receb\u00EDvel n\u00E3o pode ser exclu\u00EDdo porque se encontra em lote do Financeiro Web.
msg_extrato_cielo_erro=N\u00E3o foi poss\u00EDvel registrar o extrato na CIELO.
msg_extrato_cielo_registrado=Extrato CIELO registrado com sucesso!
msg_favorito_adicionado=Restam {0} itens para acrescentar. Você pode colocar até {1} itens na sua lista de favoritos.
msg_favorito_limite_maximo_atingido=lista de favoritos atingiu a quantidade máxima de {0} itens.
msg_top_favorito_adicionado=Adicionado aos favoritos!
msg_top_favorito_limite_maximo_atingido=Limite atingido!
msg_top_removido_favorito=Removido dos favoritos!
Mensagem_modal_canal_do_cliente=Fizemos uma mudança no sistema! Agora a funcionalidade da Central do cliente foi realocada para o seu Perfil.
msg_info_configuracao_de_cada_modulo=Acesse as configurações de cada módulo usando o menu lateral.
msg_carteirinha_solicitada_com_sucesso=Carteirinha solicitada com sucesso!
msg_produto_mesclado_cadastrado= Produto j\u00E1 cadastrado
msg_atencao_mesclagem=Aten\u00E7\u00E3o! A opera\u00E7\u00E3o de mesclagem n\u00E3o poder\u00E1 ser desfeita.

