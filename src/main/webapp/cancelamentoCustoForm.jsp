<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
    setTimeout(setInterval(function () {
        getDocumentCookie('popupsImportante') == 'close' ? this.close() : '';
    }, 500), 500);
</script>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-buttons.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style type="text/css">
    .tabelaCancelamento > tbody > tr > td:nth-child(2) {
        width: 100%;
    }

    .pure-button {
        font-family: sans-serif;
        font-size: 100%;
        margin: 0;
        vertical-align: baseline;
        *vertical-align: middle
    }

    .to-uper-case {
        text-transform: uppercase;
    }
</style>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        Cancelamento
        <c:set var="urlWiki" scope="session"
               value="${SuperControle.urlBaseConhecimento}como-faco-para-cancelar-um-plano-com-devolucao-de-valores/"/>
        <c:set var="titulo" scope="session" value="Cancelamento"/>
    </title>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
    </h:panelGrid>


    <div styleClass="col-md-12">
        <hr class="dividerFundoClaro"/>
    </div>
    <h:form id="form">
        <h:panelGrid columns="2" id="painelCancelamento" styleClass="tabelaCancelamento" width="100%">
            <h:panelGrid columns="1" width="100%"
                         style="margin-left: auto; margin-right: auto; padding-left: 70px; padding-right: 70px;">
                <%--<h:outputText value="NOME DO CLIENTE: "--%>
                <%--styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"/>--%>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font texto-bold"
                              value="#{ClienteControle.clienteVO.matricula} - #{ClienteControle.clienteVO.pessoa.nome} - #{ClienteControle.clienteVO.empresa_Apresentar}"/>

                <br>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              value="Solicitação de cancelamento"/>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              value="- Data da solicitação de cancelamento: #{CancelamentoContratoControle.dataAtual}"/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              value="- Acesso até o dia #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.dataLimiteAcesso_Apresentar}, continue desfrutando de nossos serviços."/>

                <br>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              value="Próximas cobranças:"/>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                              value="Serão efetuadas as cobranças futuras do pró-rata da taxa de manutenção anual,
                              última mensalidade e eventuais débitos pendentes até a data do efetivo cancelamento,
                              conforme previsto no termo de matrícula."/>

                <br>

                <c:if test="${!CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.naoCobrarMulta}">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                  value="Multa Cancelamento:"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  value="Valor restante do contrato: #{CancelamentoContratoControle.cancelamentoContratoVO.valorCreditoRestanteContratoValorMensalSemTaxaEMulta_Apresentar}."/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  value="Será cobrado uma multa de #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.valorMulta_Apresentar}, referente à #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.porcentagemMulta}% sobre o valor restante do contrato, a ser cobrada no dia #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.dataVencimentoMulta_Apresentar}."/>

                    <h:panelGroup layout="block">
                        <div styleClass="col-md-12">
                            <hr class="dividerFundoClaro"/>
                        </div>
                        <h:selectBooleanCheckbox id="naoCobrarMulta"
                                                 value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.naoCobrarMulta}">
                            <a4j:support event="onclick"
                                         reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                         action="#{CancelamentoContratoControle.obterLiberacaoMultaCancelamentoProporcional}"
                                         oncomplete="#{CancelamentoContratoControle.mensagemNotificar};#{CancelamentoContratoControle.msgAlert}"/>
                        </h:selectBooleanCheckbox>
                        <rich:spacer width="10px"/>
                        <h:outputText value="NÃO COBRAR MULTA"
                                      styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block"
                                  rendered="#{!CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.usuarioAlterouValorMulta}">
                        <div styleClass="col-md-12">
                            <hr class="dividerFundoClaro"/>
                        </div>
                        <h:selectBooleanCheckbox id="alterarValorMulta"
                                                 value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.alterarValorMulta}">
                            <a4j:support event="onclick"
                                         reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                         action="#{CancelamentoContratoControle.obterLiberacaoAlterarValorMultaCancelamentoProporcional}"
                                         oncomplete="#{CancelamentoContratoControle.mensagemNotificar};#{CancelamentoContratoControle.msgAlert}"/>
                        </h:selectBooleanCheckbox>
                        <rich:spacer width="10px"/>
                        <h:outputText value="ALTERAR VALOR DA MULTA"
                                      styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block"
                                  rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.alterarValorMulta && !CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.usuarioAlterouValorMulta}">
                        <h:inputText id="valorMulta" size="7"
                                     onkeypress="return formatar_moeda(this,'.',',',event);" maxlength="10"
                                     value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.valorMulta}"
                                     onfocus="focusinput(this);" styleClass="inputTextClean"
                                     style="margin-left: 8px; margin-right: 8px; height: 36px; width: 150px;">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:inputText>
                        <a4j:commandLink id="aplicarValorAlteradoMultaCancelamentoPropocional"
                                         styleClass="pure-button pure-button-small pure-button-secundary"
                                         action="#{CancelamentoContratoControle.aplicarValorAlteradoMultaCancelamentoPropocional}"
                                         oncomplete="#{CancelamentoContratoControle.mensagemNotificar};#{CancelamentoContratoControle.msgAlert}"
                                         reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade">
                            <i class="fa-icon-ok"></i>&nbsp Aplicar
                        </a4j:commandLink>
                    </h:panelGroup>

                    <br>
                    <br>

                </c:if>

                <c:if test="${CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.cobrarAnuidade == 'S' && !CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.cobrarAnuidadeTotal}">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                  value="Pró-rata de Anuidade:"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.valorAnuidade_Apresentar} - Referente à #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.diasProRataAnuidade} dias de pró-rata referente a anuidade, a ser cobrada no dia #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.dataVencimentoAnuidade_Apresentar}."/>
                    <br>
                </c:if>

                <c:if test="${CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.cobrarAnuidade == 'S' && CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.cobrarAnuidadeTotal}">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                                  value="Anuidade:"/>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza-2 texto-font"
                                  value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.valorAnuidade_Apresentar} - Referente a anuidade, a ser cobrada no dia #{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.dataVencimentoAnuidade_Apresentar}."/>
                    <br>
                </c:if>

                <h:outputText
                        rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.cobrarProximaParcela}"
                        styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                        value="Próxima Parcela em Aberto:"/>

                <rich:dataTable id="listaParcelasPagar" width="100%" border="0" cellspacing="0"
                                columnClasses="col-text-align-left" headerClass="col-text-align-left"
                                styleClass="tabelaSimplesCustom"
                                rendered="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.cobrarProximaParcela}"
                                value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.listaParcelasPagar}"
                                var="parcela">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="CÓDIGO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="DESCRIÇÃO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.descricao}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="DATA VENCIMENTO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.dataVencimento}">
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:outputText>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                          value="VALOR"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                      value="#{parcela.valorParcela}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>

                <c:if test="${not empty CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.listaParcelasVencidas}">
                    <br>
                    <h:outputText
                            styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                            style="color: red"
                            value="Parcelas Vencidas:"/>

                    <rich:dataTable id="listaParcelasVencidas" width="100%" border="0" cellspacing="0"
                                    columnClasses="col-text-align-left" headerClass="col-text-align-left"
                                    styleClass="tabelaSimplesCustom"
                                    value="#{CancelamentoContratoControle.cancelamentoContratoVO.cancelamentoProporcional.listaParcelasVencidas}"
                                    var="parcela">

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="CÓDIGO"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.codigo}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="DESCRIÇÃO"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.descricao}"/>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="DATA VENCIMENTO"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.dataVencimento}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText>
                        </rich:column>
                        <rich:column>
                            <f:facet name="header">
                                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                              value="VALOR"/>
                            </f:facet>
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                          value="#{parcela.valorParcela}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </rich:column>
                    </rich:dataTable>
                    <br>
                </c:if>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold to-uper-case"
                              rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                              value="#{msg_aplic.prt_JustificativaOperacao_consultarJustificativa}"/>
                <h:panelGroup styleClass="font-size-em-max"
                              rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}">
                    <div class="cb-container margenVertical" style="width: 300px;">
                        <h:selectOneMenu id="tipoOperacao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         style="width: 300px;"
                                         value="#{CancelamentoContratoControle.cancelamentoContratoVO.tipoJustificativaOperacao}">
                            <f:selectItems
                                    value="#{CancelamentoContratoControle.listaSelectItemJustificativaOperacao}"/>
                        </h:selectOneMenu>
                    </div>
                    <rich:spacer width="7"/>
                    <a4j:commandLink id="atualizar_tipoOperacao"
                                     action="#{CancelamentoContratoControle.montarListaSelectItemJustificativaCancelamento}"
                                     ajaxSingle="true" reRender="form:tipoOperacao">
                        <i class="fa-icon-refresh texto-size-14-real texto-cor-cinza "></i>
                    </a4j:commandLink>
                    <rich:spacer width="7"/>
                    <a4j:commandLink id="adicionarTipoOperacao" action="#{JustificativaOperacaoControle.reset}"
                                     oncomplete="abrirPopup('justificativaOperacaoCons.jsp', 'JustificativaOperacao', 800, 595);">
                        <i class="fa-icon-plus-sign texto-size-14-real texto-cor-cinza "/>
                    </a4j:commandLink>
                </h:panelGroup>

                <h:panelGrid>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold"
                                  value="OBSERVAÇÃO:"/>
                    <h:inputTextarea value="#{CancelamentoContratoControle.cancelamentoContratoVO.observacao}"
                                     id="descricaoCalculo" styleClass="" rows="7" cols="80"/>
                </h:panelGrid>

                <h:panelGrid style="top:10px;" width="100%">
                    <h:panelGrid width="100%" columns="2">
                        <h:panelGroup>
                            <a4j:commandLink id="confirmar"
                                             rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                             title="Finalizar"
                                             reRender="form,formConfirmacaoCancelamento,panelMensagem,panelBotoes,panelAutorizacaoFuncionalidade"
                                             style="float: right;"
                                             action="#{CancelamentoContratoControle.validarAutorizacaoCancelamentoProporcional}"
                                             oncomplete="#{CancelamentoContratoControle.mensagemNotificar};#{CancelamentoContratoControle.msgAlert}"
                                             styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-ok"></i>&nbsp Finalizar
                            </a4j:commandLink>

                            <h:commandLink id="fechar" title="Fechar Janela" onclick="fecharJanela();executePostMessage({close: true});"
                                           style="float: right;"
                                           rendered="#{!CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                           styleClass="pure-button pure-button-primary">
                                <i class="fa-icon-remove"></i>&nbsp Fechar
                            </h:commandLink>

                            <rich:spacer width="7" style="float: right"/>

                            <a4j:commandLink id="comprovanteOpCan"
                                             style="float: right;"
                                             rendered="#{!CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao}"
                                             title="Imprimir Comprovante da Operação de Cancelamento"
                                             action="#{CancelamentoContratoControle.imprimirComprovanteOperacao}"
                                             oncomplete="abrirPopupPDFImpressao('relatorio/#{CancelamentoContratoControle.nomeArquivoComprovanteOperacao}','', 780, 595);"
                                             styleClass="pure-button">
                                <i class="fa-icon-print"></i>&nbsp Imprimir Comprovante
                            </a4j:commandLink>

                            <rich:spacer width="7" style="float: right"/>

                            <h:commandLink id="voltar" style="float: right;" title="Voltar Passo"
                                           rendered="#{CancelamentoContratoControle.apresentarBotoes && !CancelamentoContratoControle.processandoOperacao && !CancelamentoContratoControle.contratoVO.contratoRecorrenciaVO.cancelamentoProporcional}"
                                           action="#{CancelamentoContratoControle.voltarTelaCancelamento}"
                                           styleClass="pure-button">
                                <i class="fa-icon-arrow-left"></i>
                            </h:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
