<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script type="text/javascript" language="javascript">
     setTimeout(setInterval(function() { getDocumentCookie('popupsImportante') == 'close' ? this.close() : ''; }, 500), 500);
</script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <style>
        body{
            min-width: 0 !important;
        }
    </style>
    <title>
        <h:outputText value="Definir senha de acesso para digitar na catraca."/>
    </title>
    <h:form id="formDefinirSenhaAcesso" style="background-color:white;" >
        <h:panelGrid columns="1" width="100%"  cellpadding="0" cellspacing="0">
            <f:facet name="header">
                <f:verbatim>
                    <f:facet name="header">

                        <jsp:include page="./topoReduzido_material.jsp"/>

                    </f:facet>
                </f:verbatim>
            </f:facet>

            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos14" value="#{msg_aplic.prt_Pessoa_definirSenhaAcesso}">
                            <h:outputLink value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                          title="Clique e saiba mais: Definição de Senha para Catraca" target="_blank">
                               <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                            </h:outputLink>
                        </h:outputText>

                    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
                        <h:outputText  value="#{msg_aplic.prt_Pessoa_nome}:" />
                        <h:outputText  value="#{PessoaControle.pessoaVO.nome}" />
                        <h:outputText value="#{msg_aplic.prt_Pessoa_liberaSenhaAcesso}" />
                        <h:selectBooleanCheckbox  value="#{PessoaControle.pessoaVO.liberaSenhaAcesso}" >
                            <a4j:support event="onclick" action="#{PessoaControle.removerSenhaAcesso}" reRender="ConfirmarSenhaAcesso,senhaAcesso,panelMensagemErro,panelBotao,panelSenhas"/>
                       </h:selectBooleanCheckbox>
                    </h:panelGrid>        
                    <h:panelGrid id="panelSenhas" columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">            
                        <h:outputText rendered="#{PessoaControle.pessoaVO.liberaSenhaAcesso}" value="#{msg_aplic.prt_Pessoa_senhaAcesso}" />
                        <h:inputSecret rendered="#{PessoaControle.pessoaVO.liberaSenhaAcesso}" disabled="#{!PessoaControle.pessoaVO.liberaSenhaAcesso}"   id="senhaAcesso" size="10" maxlength="#{PessoaControle.numeroDigitosSenhaAcesso}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PessoaControle.pessoaVO.senhaAcesso}" />
                        <h:outputText rendered="#{PessoaControle.pessoaVO.liberaSenhaAcesso}" value="#{msg_aplic.prt_Pessoa_confirmarSenhaAcesso}" />
                        <h:inputSecret rendered="#{PessoaControle.pessoaVO.liberaSenhaAcesso}" disabled="#{!PessoaControle.pessoaVO.liberaSenhaAcesso}"  id="ConfirmarSenhaAcesso" size="10" maxlength="#{PessoaControle.numeroDigitosSenhaAcesso}" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{PessoaControle.pessoaVO.confirmarSenhaAcesso}" />

                    </h:panelGrid>

                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{PessoaControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{PessoaControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{PessoaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{PessoaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>

                    <h:panelGrid id="panelBotao" columns="1" width="100%" styleClass="tabBotoes" columnClasses="colunaCentralizada">
                        <a4j:commandButton  rendered="#{PessoaControle.pessoaVO.liberaSenhaAcesso}" id="salvarSenha"
                                           action="#{PessoaControle.alterarSenhaAcesso}"
                                           reRender="formDefinirSenhaAcesso"
                                           value="#{msg_bt.btn_gravar}"
                                           image="./imagens/botaoGravar.png"
                                           alt="#{msg.msg_gravar_dados}"
                                           accesskey="2" styleClass="botoes"/>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>

            

        </h:panelGrid>
    </h:form>
</f:view>
<script>
    document.getElementById("formDefinirSenhaAcesso:senhaAcesso").focus();
</script>
