<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="./includes/include_import_minifiles.jsp" %>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<script type="text/javascript" language="javascript" src="../script/ce_script.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="../bootstrap/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="../script/tooltipster/jquery.tooltipster.min.js"></script>
<title>Régua de Cobrança - Comunicações</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>

<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="Régua de Cobrança - Comunicações"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}integracao-com-stone-connect/"/>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">

            <c:if test="${modulo eq 'zillyonWeb'}">
                <jsp:include page="./topoReduzido_material.jsp"/>
            </c:if>
            <c:if test="${modulo eq 'centralEventos'}">
                <jsp:include page="./pages/ce/includes/topoReduzido.jsp"/>
            </c:if>
        </f:facet>

        <h:form id="form">
            <h:panelGroup layout="block"
                          style="display: flex; justify-content: center; align-items: center; flex-direction: column;">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                             columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText styleClass="tituloCampos" value="Dt. Registro:"/>
                    <h:panelGroup layout="block">
                        <rich:calendar id="dataInicialRegistro"
                                       value="#{PactoPayComunicacaoControle.dataInicialRegistro}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       showWeeksBar="false"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"
                                      style="padding: 0 10px 0 10px"/>

                        <rich:calendar id="dataFinalRegistro"
                                       value="#{PactoPayComunicacaoControle.dataFinalRegistro}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"/>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Dt. Execução:"/>
                    <h:panelGroup layout="block">
                        <rich:calendar id="dataInicialExecucao"
                                       value="#{PactoPayComunicacaoControle.dataInicialExecucao}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"
                                      style="padding: 0 10px 0 10px"/>

                        <rich:calendar id="dataFinalExecucao"
                                       value="#{PactoPayComunicacaoControle.dataFinalExecucao}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"/>
                        <h:message for="dataFinalExecucao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>


                    <h:outputText styleClass="tituloCampos" value="Dt. Clicou:"/>
                    <h:panelGroup layout="block">
                        <rich:calendar id="dataInicialClicou"
                                       value="#{PactoPayComunicacaoControle.dataInicialClicou}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"
                                      style="padding: 0 10px 0 10px"/>

                        <rich:calendar id="dataFinalClicou"
                                       value="#{PactoPayComunicacaoControle.dataFinalClicou}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"/>
                        <h:message for="dataFinalClicou" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Dt. Lido:"/>
                    <h:panelGroup layout="block">
                        <rich:calendar id="dataInicialLido"
                                       value="#{PactoPayComunicacaoControle.dataInicialLido}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>

                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_ate}"
                                      style="padding: 0 10px 0 10px"/>
                        <rich:calendar id="dataFinalLido"
                                       value="#{PactoPayComunicacaoControle.dataFinalLido}"
                                       inputSize="10"
                                       style="height: 30px; width: 130px; font-size: 20px; text-align: center;"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value=""/>
                    <h:panelGroup layout="block" style="display: flex; float: left;">
                        <a4j:commandLink id="btnConsultarComunicacao"
                                         reRender="form"
                                         action="#{PactoPayComunicacaoControle.consultarRel}"
                                         oncomplete="#{PactoPayComunicacaoControle.onComplete}"
                                         styleClass="pure-button pure-button-small pure-button-primary"
                                         value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid id="tabela" rendered="#{not empty PactoPayComunicacaoControle.lista}" columns="1"
                             width="100%" cellpadding="0" cellspacing="0">
                    <h:panelGrid columns="1" width="100%">
                        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes"
                                        value="#{PactoPayComunicacaoControle.lista}" rows="50" var="item"
                                        rowKeyVar="status">
                            <rich:column styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Cod."/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.codigo}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.pessoaVO.nome}" styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Cliente"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.pessoaVO.nome}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.meioEnvio.codigo}" styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Meio"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.meioEnvio.descricao}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.tipoEnvioPactoPay.id}"
                                         styleClass="col-text-align-center"
                                         headerClass="col-text-align-center">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Tipo Comunicação"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.tipoEnvioPactoPay.identificador}">
                                </h:outputText>
                            </rich:column>
                            <rich:column sortBy="#{item.status.identificador}" styleClass="col-text-align-left"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Status"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.status.identificador}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.dataRegistro}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Dt. Registro"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.dataRegistroApresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.dataRegistro}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Dt. Execução"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.dataExecucaoApresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.sucesso}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Enviado"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.sucesso ? 'SIM' : 'NÃO'}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.dataClicou}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Dt. Clicou"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.dataClicouApresentar}"/>
                            </rich:column>
                            <rich:column sortBy="#{item.dataLido}" styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Dt. Lido"/>
                                </f:facet>
                                <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font tooltipster"
                                              escape="false"
                                              title="#{item.titleResultadoTela}"
                                              value="#{item.dataLidoApresentar}"/>
                            </rich:column>
                            <rich:column styleClass="col-text-align-left tooltipster"
                                         headerClass="col-text-align-left">
                                <f:facet name="header">
                                    <h:outputText styleClass="texto-size-12-real texto-cor-cinza texto-font"
                                                  value="Opções"/>
                                </f:facet>
                                <h:panelGroup id="panelAcoes" style="display: inline-flex">
                                    <%--ENVIO--%>
                                    <a4j:commandLink rendered="#{not empty item.comunicacao}"
                                                     style="color: #444; font-size: 15px; margin-left: 10px"
                                                     styleClass="tooltipster"
                                                     title="Visualizar os parâmetros de envio"
                                                     reRender="modalParamsComunicacao"
                                                     oncomplete="#{PactoPayComunicacaoControle.onComplete}"
                                                     actionListener="#{PactoPayComunicacaoControle.exibirParams}">
                                        <f:attribute name="params" value="envio"/>
                                        <f:attribute name="item" value="#{item}"/>
                                        <i class="fa-icon-share-alt"></i>
                                    </a4j:commandLink>

                                    <%--RESPOSTA--%>
                                    <a4j:commandLink rendered="#{not empty item.resultado}"
                                                     style="color: #444; font-size: 15px; margin-left: 10px"
                                                     styleClass="tooltipster"
                                                     title="Visualizar os parâmetros de resposta"
                                                     reRender="modalParamsComunicacao"
                                                     oncomplete="#{PactoPayComunicacaoControle.onComplete}"
                                                     actionListener="#{PactoPayComunicacaoControle.exibirParams}">
                                        <f:attribute name="params" value="resposta"/>
                                        <f:attribute name="item" value="#{item}"/>
                                        <i class="fa-icon-reply"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </rich:column>
                        </rich:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGroup>
        </h:form>
    </h:panelGrid>

    <rich:modalPanel id="modalParamsComunicacao" width="500"
                     autosized="true" styleClass="novaModal"
                     shadowOpacity="true">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Código: #{PactoPayComunicacaoControle.pactoPayComunicacaoVO.codigo}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                        id="hidelinkModalParams"/>
                <rich:componentControl for="modalParamsComunicacao" attachTo="hidelinkModalParams"
                                       operation="hide" event="onclick">
                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <h:panelGroup>
            <c:if test="${empty PactoPayComunicacaoControle.listaParametrosSelecionado}">
                <h:panelGrid columns="2">
                    <h:graphicImage value="images/warning.png"/>
                    <h:outputText styleClass="mensagemDetalhada" value="Não há parâmetros."/>
                </h:panelGrid>
            </c:if>
            <h:panelGroup layout="block" style="height: 400px; overflow-y: scroll; overflow-x: hidden;">
                <rich:dataTable width="100%" value="#{PactoPayComunicacaoControle.listaParametrosSelecionado}"
                                var="obj">
                    <f:facet name="header">
                        <c:if test="${!empty PactoPayComunicacaoControle.listaParametrosSelecionado}">
                            <h:outputText value="Parâmetros utilizados"/>
                        </c:if>
                    </f:facet>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Atributo"/>
                        </f:facet>
                        <h:outputText value="#{obj.atributo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor"/>
                        </f:facet>
                        <h:outputText style="font-weight:bold;" value="#{obj.valor}"/>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGroup>
    </rich:modalPanel>
</f:view>
<script>
    carregarTooltipster();
    document.getElementById("form:dataInicio").focus();
</script>
