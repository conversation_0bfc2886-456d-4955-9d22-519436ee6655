<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_MovProduto_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <c:set var="titulo" scope="session" value="${msg_aplic.prt_MovProduto_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}relatorio-movimento-do-produto/"/>
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{MovProdutoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{MovProdutoControle.movProdutoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_empresa}" />
                    <h:panelGroup>
                        <h:selectOneMenu disabled="#{!MovProdutoControle.movProdutoVO.usuarioVO.administrador}" id="empresa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.empresa.codigo}" >
                            <f:selectItems  value="#{MovProdutoControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_empresa" action="#{MovProdutoControle.montarListaSelectItemEmpresa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:empresa"/>
                        <h:message for="empresa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_pessoa}" />
                    <h:panelGroup>
                        <h:inputText   readonly="true"  id="Pessoa"  styleClass="camposSomenteLeitura"  value="#{MovProdutoControle.movProdutoVO.pessoa.nome}" />
                        <%-- <a4j:commandButton id="consultaDadosPessoa" focus="valorConsultaPessoa" alt="Consulta Pessoa" reRender="formPessoa" oncomplete="Richfaces.showModalPanel('panelPessoa'), setFocus(formPessoa,'formPessoa:valorConsultaPessoa');" image="./imagens/informacao.gif" />--%>
                    </h:panelGroup> 
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_contrato}" />

                    <h:panelGroup>
                        <h:inputText id="contrato"  readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.contrato.codigo}" />                         
                    </h:panelGroup>
                    <%-- <h:panelGroup>
                                <h:selectOneMenu  id="pessoa" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.pessoa.codigo}" >
                                    <f:selectItems  value="#{MovProdutoControle.listaSelectItemPessoa}" />
                                </h:selectOneMenu>
                                <a4j:commandButton id="atualizar_pessoa" action="#{MovProdutoControle.montarListaSelectItemPessoa}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:pessoa"/>
                                <h:message for="pessoa" styleClass="mensagemDetalhada"/>
                            </h:panelGroup>--%>


                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ProdutoSugerido_produto}" />
                    <h:panelGroup>
                        <h:inputText   readonly="true"  id="Produto" size="40"  maxlength="45"  styleClass="camposSomenteLeitura"  value="#{MovProdutoControle.movProdutoVO.produto.descricao}" />
                        <%-- <a4j:commandButton id="consultaDadosProduto"  alt="Consulta Produto" reRender="formProduto" oncomplete="Richfaces.showModalPanel('panelProduto'), setFocus(formProduto,'formProduto:valorConsultaProduto');" image="./imagens/informacao.gif" />--%>
                    </h:panelGroup>   

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" readonly="true" size="45" maxlength="45" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_quantidade}" />
                    <h:inputText  id="quantidade" size="10"  readonly="true" maxlength="10" onblur="blurinput(this);" onkeypress="return mascara(this.form, 'form:quantidade', '999999999999999999', event);" onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.quantidade}" >
                        <a4j:support  event="onchange" reRender="totalFinal" focus="mesReferencia" action="#{MovProdutoControle.calculaTotalFinal}"/>
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_precoUnitario}" />
                    <h:inputText  id="precoUnitario" readonly="true" size="20" maxlength="20"  styleClass="camposSomenteLeitura" value="#{MovProdutoControle.movProdutoVO.precoUnitario}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_valorDesconto}" />
                    <h:inputText  id="valorDesconto" size="20" readonly="true" maxlength="20" onblur="blurinput(this);" onkeypress="return Tecla(event);" onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.valorDesconto}" >
                        <f:converter converterId="FormatadorNumerico" />
                        <a4j:support  event="onchange" focus="mesReferencia" reRender="totalFinal" action="#{MovProdutoControle.calculaTotalFinal}"/>
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_totalFinal}" />
                    <h:inputText  id="totalFinal" readonly="true" size="20" maxlength="20"  styleClass="camposSomenteLeitura" value="#{MovProdutoControle.movProdutoVO.totalFinal}" >
                        <f:converter converterId="FormatadorNumerico" />
                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_situacao}" />
                    <h:inputText  id="situacao" readonly="true" size="20" maxlength="20"  styleClass="camposSomenteLeitura" value="#{MovProdutoControle.movProdutoVO.situacao_Apresentar}" >

                    </h:inputText>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_dataLancamento}" />
                    <h:panelGroup>
                        <rich:calendar id="dataLancamento"
                                       value="#{MovProdutoControle.movProdutoVO.dataLancamento}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataLancamento"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_responsavelLancamento}" />

                    <h:panelGroup>
                        <h:inputText id="responsavelLancamento" readonly="true"  styleClass="camposSomenteLeitura"   size="5" maxlength="10" value="#{MovProdutoControle.movProdutoVO.responsavelLancamento.codigo}"/>
                        <h:outputText  styleClass="tituloCampos" value="#{MovProdutoControle.movProdutoVO.responsavelLancamento.nome}" />                                
                    </h:panelGroup>   
                    <%--<h:panelGroup>
                        <h:selectOneMenu  id="responsavelLancamento" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.responsavelLancamento.codigo}" >
                            <f:selectItems  value="#{MovProdutoControle.listaSelectItemResponsavelLancamento}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_responsavelLancamento" action="#{MovProdutoControle.montarListaSelectItemResponsavelLancamento}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:responsavelLancamento"/>
                        <h:message for="responsavelLancamento" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>--%>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_mesReferencia}" />
                    <h:inputText  id="mesReferencia" size="10" readonly="true"  maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.mesReferencia}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_anoReferencia}" />
                    <h:inputText  id="anoReferencia" size="10" readonly="true"  maxlength="10" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{MovProdutoControle.movProdutoVO.anoReferencia}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_dataInicioVigencia}" />
                    <h:panelGroup>
                        <rich:calendar id="dataInicioVigencia"
                                       value="#{MovProdutoControle.movProdutoVO.dataInicioVigencia}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataInicioVigencia"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_MovProduto_dataFinalVigencia}" />
                    <h:panelGroup>
                        <rich:calendar id="dataFinalVigencia"
                                       value="#{MovProdutoControle.movProdutoVO.dataFinalVigencia}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <h:message for="dataFinalVigencia"  styleClass="mensagemDetalhada"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </h:panelGroup>
                </h:panelGrid>              


                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{MovProdutoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{MovProdutoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>                           
                            <h:commandButton id="consultar" immediate="true" action="#{MovProdutoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:produto").focus();
</script>