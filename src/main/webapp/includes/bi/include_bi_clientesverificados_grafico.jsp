<script type="text/javascript">
    function carregarGraficoClientesVerificados() {
        var total = ${ClientesVerificadosRelControle.totalVerificar};
        var chart;
        var verificado = ${ClientesVerificadosRelControle.qtdVerificado};
        var naoVerificado = ${ClientesVerificadosRelControle.qtdNaoVerificado};
        var chartData = [
            {
                "desc": "",
                "verificado": verificado,
                "nao-verificado": naoVerificado,
                "total": total
            }
        ];

        // SERIAL CHART
        chart = new AmCharts.AmSerialChart();
        chart.dataProvider = chartData;
        chart.categoryField = "desc";
        chart.theme = "light";
        chart.type = "serial";
        chart.rotate = true;
        chart.maxWidth = '100%';

        // AXES
        // category
        var categoryAxis = chart.categoryAxis;
        categoryAxis.gridAlpha = 0;
        categoryAxis.axisAlpha = 0;
        categoryAxis.gridPosition = "start";
        categoryAxis.position = "left";
        categoryAxis.labelFrequency = 20;

        // value
        var valueAxis = new AmCharts.ValueAxis();
        valueAxis.stackType = "regular";
        valueAxis.gridAlpha = 0;
        valueAxis.axisAlpha = 0.5;
        valueAxis.maximum = total;
        valueAxis.maxReal = total;
        valueAxis.autoGridCount = false;
        valueAxis.labelFrequency = 1;
        valueAxis.gridCount = 16;
        valueAxis.labelColorField = "#29ABE2";
        chart.addValueAxis(valueAxis);

        graph = new AmCharts.AmGraph();
        graph.title = "Verificados";
        graph.labelText = "[[value]]";
        graph.valueField = "verificado";
        graph.type = "column";
        graph.lineAlpha = 0;
        graph.fillAlphas = 1;
        graph.lineColor = "#00C350";
        graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
        chart.addGraph(graph);

        var graph = new AmCharts.AmGraph();
        graph.title = "Nao Verificados";
        graph.labelText = "[[value]]";
        graph.colorFields = "#ffffff";
        graph.colorField = "#ffffff";
        graph.textcolor = "#ffffff";
        graph.valueField = "nao-verificado";
        graph.type = "column";
        graph.lineAlpha = 0;
        graph.fillAlphas = 1;
        graph.lineColor = "#FF5555";
        graph.balloonText = "<span style='color:#555555;'>[[category]]</span><br><span style='font-size:14px'>[[title]]:<b>[[value]]</b></span>";
        chart.addGraph(graph);

        chart.write("grafico-clientesverificados");
    }
</script>
<div layout="block" class="grafico-clientesverificados" style="width: 100%; height: 150px;">
    <div layout="block" id="grafico-clientesverificados" style="width: 100%; height: 150px;"></div>
</div>
<script type="text/javascript">
    carregarGraficoClientesVerificados();
</script>



