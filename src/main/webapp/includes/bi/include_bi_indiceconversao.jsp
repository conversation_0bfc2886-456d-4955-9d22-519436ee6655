<%--
    Document   : include_bi_indicieconversao
    Created on : 20/02/2016
    Author     : <PERSON>-%>
<%@include file="../imports.jsp" %>
<h:panelGroup layout="block" id="bi-indicieconversao" styleClass="container-bi"
              rendered="#{LoginControle.permissaoAcessoMenuVO.indiceConversao}">
    <c:if test="${(LoginControle.permissaoAcessoMenuVO.indiceConversao || LoginControle.usuarioLogado.administrador) && (!BIControle.configuracaoBI.conversaoVenda.naLixeira)}">
        <a4j:commandLink action="#{BIControle.carregarConversao}" reRender="containerConversao"
                         styleClass="btn-atualizar-bi"
                         status="nenhumStatus"
                         onclick="abrirCarregandoPoBI(this)" oncomplete="hideCarregandoPorBI(this)"/>

        <h:panelGroup layout="block" id="containerConversao">
            <h:panelGroup layout="block"
                          rendered="#{(BIControle.configuracaoBI.conversaoVenda.apresentarBoxCarregar) && !BIControle.biCarregado}"
                          styleClass="bi-unloaded">
                <h:panelGroup styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Convers�o de Vendas" styleClass="bi-titulo pull-left"/>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="ghost">

                    <div class="loading bloco"></div>
                    <div style="display: inline-block;width: 60%; vertical-align: top">
                        <h3 class="loading linha" style="margin-top: 0"></h3>
                        <h3 class="loading linha"></h3>
                        <h3 class="loading linha"></h3>
                    </div>
                </div>

            </h:panelGroup>
            <h:panelGroup layout="block"
                          rendered="#{!BIControle.configuracaoBI.conversaoVenda.apresentarBoxCarregar || BIControle.biCarregado}">

                <h:panelGroup id="bi-header-conversao" styleClass="bi-header" layout="block">
                    <h:panelGroup layout="block" styleClass="bi-panel">
                        <h:outputText value="Convers�o de vendas" styleClass="bi-titulo pull-left"/>

                        <h:outputLink styleClass="tooltipster linkWiki bi-cor-cinza bi-left-align"
                                      style="float: left;margin-top: 4.4%;margin-left: 2%"
                                      value="#{SuperControle.urlBaseConhecimento}bi-conversao-de-vendas-adm/"
                                      title="Clique e saiba mais: BI - �ndice de Convers�o de Vendas" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-top: -0.4em !important"></i>
                        </h:outputLink>

                        <a4j:commandLink id="consultarICV"
                                         reRender="bi-header-conversao,panelListaColaboradorIndiceConversao,icv-container-numeros,icv-container-resultado,panelGrafico"
                                         oncomplete="carregarGragicoICV();montarTips();"
                                         action="#{IndiceConversaoVendaRelControle.atualizar}">
                            <i title="Consultar Dados Indice de Convers�o de Vendas"
                               class="lineHeight-3em tooltipster fa-icon-refresh bi-btn-refresh bi-link pull-right"></i>
                        </a4j:commandLink>

                        <a4j:commandLink oncomplete="Richfaces.showModalPanel('panelFiltro')" reRender="form:consultarICV, formPanelFiltroICVTipo">
                            <i title="Filtro"
                               class="tooltipster fa-icon-filter bi-btn-refresh bi-link pull-right lineHeight-3em"></i>
                        </a4j:commandLink>

                        <h:panelGroup layout="block" styleClass="col-text-align-right pull-right calendarSemInputBI">
                            <div title="${IndiceConversaoVendaRelControle.dataBase_ApresentarMesDia}"
                                 style="display: inline-flex;margin-top: 1em;vertical-align: top;"
                                 class="tooltipster dateTimeCustom alignToRight">
                                <rich:calendar id="dataInicioICV"
                                               value="#{IndiceConversaoVendaRelControle.dataBaseFiltro}"
                                               inputSize="8"
                                               showInput="false"
                                               inputClass="forcarSemBorda"
                                               buttonIcon="#{IndiceConversaoVendaRelControle.dataAlterada ? '/imagens_flat/icon-calendar-red.png' : '/imagens_flat/icon-calendar-check.png'}"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false">
                                    <a4j:support event="onchanged"
                                                 action="#{IndiceConversaoVendaRelControle.atualizar}"
                                                 oncomplete="carregarGragicoICV();montarTips();"
                                                 reRender="dataInicioICV,bi-header-conversao,panelListaColaboradorIndiceConversao,icv-container-numeros,icv-container-resultado,panelGrafico,consulorBvAlterado"/>
                                </rich:calendar>
                            </div>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="pull-right calendarSemInputBI"
                                      style="margin-right: 10px; text-align: center;" id="quantColSelIndConversao">
                            <a4j:commandLink oncomplete="Richfaces.showModalPanel('filtroConversaoColaborador')"
                                             action="#{BIControle.biAtualizarParam}"
                                             styleClass="text-align: center;"
                                             reRender="formPanelFiltroCol, tituloFiltro">
                                <i title="Filtrar Por Colaborador"
                                   class="tooltipster fa-icon-user bi-btn-refresh bi-link lineHeight-3em">
                                    <span class="badgeItem3Icon" data-bagde="${BIControle.qtdColConvVendas}"></span>
                                </i>
                                <a4j:actionparam name="biAtualizar" value="CONVERSAO_VENDAS"></a4j:actionparam>
                                <a4j:actionparam name="biAtualizarAbrirConsulta"
                                                 value="biAtualizarAbrirConsulta"></a4j:actionparam>
                                <a4j:actionparam name="reRenderBi" value="containerConversao"></a4j:actionparam>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" id="icv-container-numeros" style="margin-top: 4%"
                              styleClass="icv-container-numeros">

                    <h:panelGroup layout="block" style="margin-left: 60%;text-align: center"
                                  styleClass="icv-numeros-col">
                        <h:outputText styleClass="icv-periodo-text" value="DIA"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <h:outputText styleClass="icv-periodo-text" value="M�S"/>
                    </h:panelGroup>

                    <%--Resultado B.V.--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor- fa-icon-circle texto-cor-cinza-3"/>
                        <h:outputText styleClass="bi-table-text bi-font-family" value=" BV"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{IndiceConversaoVendaRelControle.mostrarListaClientesBVHoje}"
                                         oncomplete="abrirPopup('indiceConversaoVenda.jsp', 'Rematricula',980 ,700);"
                                         value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdQuestionarioDia}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         id="bvPreenchidosMes"
                                         action="#{IndiceConversaoVendaRelControle.mostrarListaClientesBVMes}"
                                         oncomplete="abrirPopup('indiceConversaoVenda.jsp', 'Rematricula',980 ,700);"
                                         value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdQuestionarioMes}"/>
                    </h:panelGroup>

                    <%--Matriculas I.C.V--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-azul fa-icon-circle"/>
                        <h:outputText style="margin-left: 1px;" styleClass="bi-table-text bi-font-family"
                                      value=" Matr�culas"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{IndiceConversaoVendaRelControle.mostrarListaClientesMatriculaHoje}"
                                         oncomplete="abrirPopup('indiceConversaoVenda.jsp', 'Matricula',980 ,700);"
                                         value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdMatriculaDia}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{IndiceConversaoVendaRelControle.mostrarListaClientesMatriculaMes}"
                                         oncomplete="abrirPopup('indiceConversaoVenda.jsp', 'Matricula',980 ,700);"
                                         value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdMatriculaMes}"/>
                    </h:panelGroup>

                    <%--Renovados I.C.V--%>
                    <h:panelGroup layout="block" style="text-align: left" styleClass="icv-numeros-col">
                        <h:outputText styleClass="bi-cor-verde fa-icon-circle"/>
                        <h:outputText style="margin-left: 1px;" styleClass="bi-table-text bi-font-family"
                                      value=" Rematr�culas"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{IndiceConversaoVendaRelControle.mostrarListaClientesRematriculaHoje}"
                                         oncomplete="abrirPopup('indiceConversaoVenda.jsp', 'Rematricula',980 ,700);"
                                         value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdRematriculaDia}"/>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="text-align: center" styleClass="icv-numeros-col">
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16"
                                         action="#{IndiceConversaoVendaRelControle.mostrarListaClientesRematriculaMes}"
                                         oncomplete="abrirPopup('indiceConversaoVenda.jsp', 'Rematricula',980 ,700);"
                                         value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.qtdRematriculaMes}"/>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup layout="block" id="icv-container-resultado" style="margin-top: 4%"
                              styleClass="icv-container-resultado">
                    <h:outputText style="display: block;" styleClass="bi-table-text" value="ICV"/>
                    <h:outputText id="bvIndiceConversao" styleClass="gr-totalizador-text bi-font-family"
                                  value="#{IndiceConversaoVendaRelControle.indiceConversaoVendaVO.totalICV}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                    <h:outputText styleClass="gr-totalizador-text bi-font-family" value="%"/>
                </h:panelGroup>
                <h:panelGroup layout="block" id="panelGrafico">
                    <jsp:include flush="true" page="include_bi_icv_grafico.jsp"/>
                    <h:panelGroup layout="block"
                                  rendered="#{IndiceConversaoVendaRelControle.apresentarBotaoDeAlteracaoConsulorBV}"
                                  id="consulorBvAlterado">
                        <i class="fa-icon-warning-sign " style="color: #FF5555;"></i>
                        <h:outputText styleClass="bi-table-text"
                                      value="BI modificado por altera��o de consultor do BV. Para mais informa��es, "/>
                        <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-16" id="listaConsultorBV"
                                         value="clique aqui" style="margin-left: 5px;"
                                         oncomplete="abrirPopup('alteracaoConsultorBV.jsp', 'ConsultorBV',980 ,700);"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
        </h:panelGroup>
    </c:if>
</h:panelGroup>
