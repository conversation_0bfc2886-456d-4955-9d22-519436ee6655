<%--
  Created by IntelliJ IDEA.
  User: rodrigo
  Date: 28/05/2020
  Time: 22:57
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" pageEncoding="ISO-8859-1" language="java" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<h:panelGrid id="panelUsuarioAmigoFit" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
    <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
        <f:facet name="header">
            <h:outputText styleClass="titulo3" escape="false" value="Defina os dados do usuário para Interagir com aplicativo Amigo Fit" />
        </f:facet>
        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_UsernameAmigoFit}" />
        <h:panelGroup>
            <h:outputText id="usernameAmigoFit" style="margin-left: 5px" styleClass="form" value="#{ClienteControle.clienteVO.usernameAmigoFit}"
            title="Este username é o e-mail de correspondência cadastrado no cliente."/>
        </h:panelGroup>

        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_senhaAmigoFit}" />
        <h:panelGroup>
            <h:inputSecret id="senhaUsuarioAmigoFit"  size="50" maxlength="200"  onblur="blurinput(this);" style="margin-left: 5px"
                           autocomplete="off"
                           onfocus="focusinput(this);"
                           styleClass="form" value="#{ClienteControle.clienteVO.senhaUsuarioAmigoFit}">
            </h:inputSecret>
        </h:panelGroup>
        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_bancoAmigoFit}" />
        <h:selectOneMenu style="margin-left: 5px"
                id="bancoAmigoFit"
                value="#{ClienteControle.clienteVO.bancoAmigoFit.codigo}"
                tabindex="5">
            <f:selectItems value="#{ClienteControle.bancos}"/>
            <a4j:support event="onchange" action="#{ClienteControle.preencherBanco}"
                         reRender="gridForm"/>
        </h:selectOneMenu>

        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_agencia}" />
        <h:panelGroup>
            <h:inputText id="agenciaAmigoFit" size="5" maxlength="8"  onblur="blurinput(this);"
                         onfocus="focusinput(this);" style="margin-left: 5px"
                         styleClass="form" value="#{ClienteControle.clienteVO.agenciaAmigoFit}">
            </h:inputText>
            -
            <h:inputText id="digitoagenciaContaAmigoFit" size="2" maxlength="3"  onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{ClienteControle.clienteVO.digitoAgenciaAmigoFit}">
            </h:inputText>
        </h:panelGroup>

        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Cliente_conta}" />
        <h:panelGroup>
            <h:inputText id="contaAmigoFit" size="5" maxlength="8"  onblur="blurinput(this);" style="margin-left: 5px"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{ClienteControle.clienteVO.contaAmigoFit}">
            </h:inputText>
            -
            <h:inputText id="digitoContaAmigoFit" size="2" maxlength="3"  onblur="blurinput(this);"
                         onfocus="focusinput(this);"
                         styleClass="form" value="#{ClienteControle.clienteVO.digitoContaAmigoFit}">
            </h:inputText>
        </h:panelGroup>
        <h:outputText value="Tipo de Conta:"/>
        <h:panelGroup>
            <h:selectBooleanCheckbox value="#{ClienteControle.clienteVO.contaCorrenteAmigoFit}" title="Conta Corrente" style="margin-bottom: 0px;margin-top: 5px; margin-left: 5px"/>
            <h:outputText value="Conta corrente" style="margin-left: 5px;font-size: 13px;"/>
            <rich:spacer width="15px" />
            <h:selectBooleanCheckbox value="#{ClienteControle.clienteVO.contaPoupancaAmigoFit}" title="Conta Poupança/Investimento" style="margin-bottom: 0px;margin-top: 5px"/>
            <h:outputText value="Conta Poupança/Investimento" style="margin-left: 5px;font-size: 13px;margin-bottom: 3px"/>
        </h:panelGroup>

    </h:panelGrid>
</h:panelGrid>
