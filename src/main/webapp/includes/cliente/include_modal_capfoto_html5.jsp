<%-- 
    Document   : include_modal_capfoto_html5
    Created on : 14/08/2017, 16:06:59
    Author     : <PERSON><PERSON><PERSON><PERSON>.
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<!DOCTYPE HTML>
<%@include file="/include_imports.jsp"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<!--jQuery version must be at least 1.9.0-->
<script type="text/javascript" src="./bootstrap/jquery.js"></script>
<script type="text/javascript">
    // Variable used in the function setLastActionTime in the script time_1.3.js
    var ctxForJS = "${pageContext.request.contextPath}";
</script>
<script type="text/javascript" src="./script/capturaFoto/Jcrop/jquery.Jcrop.zwmod.js"></script>
<script type="text/javascript" src="./script/capturaFoto/Webcam.js/webcam.zwmod.js"></script>
<script type="text/javascript" src="./script/capturaFoto/capturaFotoHTML5.2.js"></script>
<script type="text/javascript">
    Jcrop_initialize(jQuery_capFoto);
</script>

<link rel="stylesheet" href="./beta/css/pure-ext.css" type="text/css" />
<link rel="stylesheet" href="./beta/css/pure-min.css" type="text/css" />
<link rel="stylesheet" href="./css/capturaFoto/Jcrop/jquery.Jcrop.min.css" type="text/css" />
<link rel="stylesheet" href="./css/capturaFoto/capturaFotoHTML5.css" type="text/css" />

<rich:modalPanel id="modalCapFotoHTML5" autosized="true" styleClass="novaModal noMargin" 
                 shadowOpacity="true" width="730" height="600" onshow="initializeModalCapFoto();" onhide="hideWebcamTab();">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Adicionar Imagem"></h:outputText>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                id="closeModalCapFoto"/>
            <rich:componentControl for="modalCapFotoHTML5" attachTo="closeModalCapFoto" operation="hide"  event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="imageUploadBox" styleClass="imageUploadBox">
        <h:panelGroup layout="block" id="menu_captura" styleClass="menuCaptura">
            <ul class="menuHorizontalList">
                <li class="menuHorizontalListItem">
                    <a id="link_capturar_webcam" href="#" class="menuHorizontalListItemLink">Webcam</a>
                </li>
                <li class="menuHorizontalListItem">
                    <a id="link_enviar_foto" href="#" class="menuHorizontalListItemLink active">Escolher do computador</a>
                </li>
            </ul>
        </h:panelGroup>
        <h:panelGroup layout="block" id="capturaFotoDiv" styleClass="uploadBox">
            <i id="loading_webcam" class="fa-icon-camera loadingWebcam"></i>
            <span id="loading_webcam_lbl" class="background_message centered">
                Carregando...
            </span>
            <div id="video_container" class="uploadBox videoContainer">                
                <video id="webcamVideo"></video>
            </div>
            <div id="drop_zone" class="uploadBox drop_zone verticallyCentered">
                <div id="drop_zone_center" class="drop_zone center">
                    <span id="background_message" class="background_message">
                        Arraste a imagem aqui
                    </span>
                    <span id="background_message_or" class="background_message or">ou</span>
                    <span id="background_message" class="background_message">
                        Faça upload de uma imagem
                    </span>
                    <label id="upload_img_lbl" class="upload_img_btn pure-button pure-button-primary">
                        Aqui
                        <input id="upload_image_btn" type="file" accept="image/*">
                        <i class="fa-icon-upload-alt" style="margin-left: 5px;"></i>
                    </label>
                </div>
            </div>
        </h:panelGroup>
        <h:panelGroup layout="block" id="imageDiv" styleClass="uploadBox imageDiv">
            <canvas id="canvas_img" alt="Imagem enviada" class="imageUploaded pure-button-"></canvas>
            </h:panelGroup>
        <a href="#" id="back_btn" class="capFotoButton hiddenBtn pure-button pure-button">
            <i class="capFotoButton icon fa-icon-arrow-left"></i>
            Não
        </a>
        <a href="#" id="crop_image_btn" class="capFotoButton hiddenBtn pure-button pure-button-primary">
            <i class="capFotoButton icon fa-icon-crop"></i>
            Cortar
        </a>
        <a href="#" id="snapshot_btn" class="capFotoButton hiddenBtn pure-button pure-button-primary">
            <i class="capFotoButton icon fa-icon-camera"></i>
            Capturar Foto
        </a>
        <a href="#" id="finish_btn" class="capFotoButton hiddenBtn pure-button pure-button-primary">
            <i class="capFotoButton icon fa-icon-ok"></i>
            Sim
        </a>
        <a href="#" id="cancel_btn" class="capFotoButton hiddenBtn pure-button pure-button">
            <i class="capFotoButton icon fa-icon-remove"></i>
            Cancelar
        </a>
    </h:panelGroup>
</rich:modalPanel>
