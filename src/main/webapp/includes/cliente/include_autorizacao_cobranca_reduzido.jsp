<%-- 
    Document   : include_box_autorizacao_cobranca
    Created on : 04/12/2012, 14:43:21
    Author     : waller
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>
  .colsL{
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
    width:120px;
    padding-top: 2px;
    padding-bottom: 2px;
  }
  .colsR{
    font-family:Arial, Helvetica, sans-serif;
    font-size: 11px;
    text-decoration: none;
    line-height: normal;
    font-weight: normal;
    text-transform: none;
    color: #333;
    line-height:150%;
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .rich-panel {
    margin: 0 0 0 0;
  }

</style>

<%@include file="../../include_imports.jsp" %>
<a4j:outputPanel>
  <h:panelGroup id="panelAutorizacaoCobrancaCliente" layout="block" style="margin: 20px">
        <h:panelGroup style="width:100%; text-align: right;margin-bottom: 10px;height: 20px;line-height: 20px;" layout="block">
            <h:outputText  styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold" style="float: left" value="AUTORIZAÇÃO COBRANÇA"/>
          </h:panelGroup>
      <h:panelGroup>
      <h:panelGroup layout="block" style="margin-top:10px;margin-bottom: 10px" id="tabAutorizacao"
                    styleClass="tabelaSimplesCustom grupoRadioButton border-color-c2">
          <a4j:repeat value="#{AutorizacaoCobrancaControle.listaAutorizacoes}" rowKeyVar="status" var="autorizacao">
              <%--Excluir autorização--%>
              <h:panelGroup rendered="#{autorizacao.tipoAutorizacao eq AutorizacaoCobrancaControle.tipoAutorizacaoCondicaoPagamento}" layout="block" style="display: inline-block;height: 40px;line-height: 40px;" styleClass="pull-right">
                  <a4j:commandLink styleClass="tooltipster"
                                   id="excluirAutorizacaoCobranca" title="Excluir"
                                   style="float:right; display:block;margin-top: 3px;"
                                   action="#{AutorizacaoCobrancaControle.confirmarRemoverAutorizacaoCobranca}"
                                   oncomplete="#{AutorizacaoCobrancaControle.msgAlert}"
                                   reRender="mdlMensagemGenerica">
                      <i class="fa-icon-trash texto-size-20"></i>
                  </a4j:commandLink>
              </h:panelGroup>
               <h:panelGroup layout="block"  styleClass=" autorizacaoCobrancaItemcodigo#{autorizacao.codigo}" >
                 <h:panelGroup layout="block"  styleClass="autorizacaoCobrancaItem autorizacaoCobrancaItemcodigo#{autorizacao.codigo}" rendered="#{autorizacao.tipoAutorizacao eq AutorizacaoCobrancaControle.tipoAutorizacaoCondicaoPagamento}" >

                   <div class="tooltipster" title="Clique para editar">
                  <h:panelGroup layout="block" styleClass="maginAutorizacao">
                      <h:panelGroup layout="block" style="width: 100%"   rendered="#{autorizacao.tipoContaCorrente and (autorizacao.tipoAutorizacao eq AutorizacaoCobrancaControle.tipoAutorizacaoCondicaoPagamento)}">
                        <h:panelGroup layout="block" styleClass="pull-left" style="display: inline-block;height: 40px;line-height: 40px;width: 72%;text-align: left">
                            <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza" value="AG "/>
                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{autorizacao.agencia}-#{autorizacao.agenciaDV} "/>

                            <h:outputText style="margin-left: 5px;" styleClass="texto-font texto-size-12 texto-cor-cinza" value="CC "/>
                            <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{autorizacao.contaCorrente}-#{autorizacao.contaCorrenteDV}"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="display: inline-block;height: 40px;line-height: 40px;" styleClass="pull-right">
                          <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza" value="#{autorizacao.banco.nome}"/>
                        </h:panelGroup>
                          <h:panelGroup layout="block" styleClass="autorizacaoCobrancaBody">
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza pull-left" value="#{autorizacao.tipoAutorizacao.descricao}"/>
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza pull-right" value="A cobrar: #{autorizacao.tipoACobrar.descricao}"/>
                              <h:panelGroup layout="block" style="width:100%;display: inline-table;text-align: left;width: 100%">
                                  <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza" value="Convênio: #{autorizacao.convenio.descricao}"/>
                              </h:panelGroup>
                          </h:panelGroup>
                      </h:panelGroup>

                      <h:panelGroup layout="block" style="width: 100%" rendered="#{autorizacao.tipoBoleto and (autorizacao.tipoAutorizacao eq AutorizacaoCobrancaControle.tipoAutorizacaoCondicaoPagamento)}">
                              <h:panelGroup layout="block" styleClass="pull-left" style="display: inline-block;height: 40px;line-height: 40px;">
                                  <i class="texto-font texto-size-25 texto-cor-cinza fa-icon-barcode"></i>
                              </h:panelGroup>
                          <h:panelGroup layout="block" styleClass="autorizacaoCobrancaBody">
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza pull-left" value="#{autorizacao.tipoAutorizacao.descricao}"/>
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza pull-right" value="A cobrar: #{autorizacao.tipoACobrar.descricao}"/>
                              <h:panelGroup layout="block" style="width:100%;display: inline-table;text-align: left;width: 100%">
                                  <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza" value="Convênio: #{autorizacao.convenio.descricao}"/>
                              </h:panelGroup>
                          </h:panelGroup>
                      </h:panelGroup>

                      <h:panelGroup layout="block" style="width: 100%" rendered="#{autorizacao.tipoCartao  and (autorizacao.tipoAutorizacao eq AutorizacaoCobrancaControle.tipoAutorizacaoCondicaoPagamento)}">
                          <h:panelGroup rendered="#{!autorizacao.autorizacaoUtilizandoIdVindiPessoa}" layout="block" styleClass="pull-left" style="display: inline-block;height: 40px;line-height: 40px;">
                              <h:outputText rendered="#{autorizacao.cartaoMascarado_Apresentar != ''}" styleClass="texto-font texto-size-16 texto-cor-cinza pull-left" value="#{autorizacao.cartaoMascarado_Apresentar}"/>
                              <h:outputText rendered="#{autorizacao.cartaoMascarado_Apresentar == ''}" styleClass="texto-font texto-size-16 texto-cor-vermelho pull-left" value="Informe número do cartão"/>
                              <h:graphicImage style="vertical-align:middle;margin:5px 5px 0px 5px; width: 50px; height: 30px;border:none;"
                                              url="imagens/bandeiras/#{autorizacao.operadoraCartao.imagem}.png"/>
                          </h:panelGroup>
                          <h:panelGroup rendered="#{autorizacao.tipoCartao && autorizacao.autorizacaoUtilizandoIdVindiPessoa}" layout="block" styleClass="pull-left" style="display: flex; height: 40px; line-height: 40px;">
                              <h:graphicImage
                                      style="vertical-align:-2px; margin-right:5px; width: 40px; height: 15px;border:none;align-self: center;"
                                      url="/imagens/externo/logo-vindi.png"/>
                              <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza pull-left tooltipster" value="Autorização utilizando IdVindi"
                                            title="A transação será enviada utilizando somente o IdVindi do cliente.<br/>
                              A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente tiver cadastrado na Vindi."/>
                          </h:panelGroup>
                          <h:panelGroup rendered="#{!autorizacao.autorizacaoUtilizandoIdVindiPessoa}" layout="block" style="display: inline-block;height: 40px;line-height: 40px;" styleClass="pull-right">
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza" value="Validade "/>
                              <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{autorizacao.validadeCartao}"/>
                          </h:panelGroup>
                          <h:panelGroup layout="block" styleClass="autorizacaoCobrancaBody">
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza pull-left" value="#{autorizacao.tipoAutorizacao.descricao}"/>
                              <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza pull-right" value="A cobrar: #{autorizacao.tipoACobrar.descricao}"/>
                              <h:panelGroup layout="block" style="width:100%;display: inline-table;text-align: left;width: 100%">
                                  <h:outputText styleClass="texto-font texto-size-12 texto-cor-cinza" value="Convênio: #{autorizacao.convenio.descricao}"/>
                              </h:panelGroup>
                          </h:panelGroup>
                      </h:panelGroup>


                  </h:panelGroup>
                   </div>
                </h:panelGroup>
              </h:panelGroup>
              <a4j:commandLink styleClass="autorizacaoCobrancaItemcodigolink#{autorizacao.codigo}" style="display: none;" action="#{AutorizacaoCobrancaControle.selecionaAutorizacao}"  oncomplete="Richfaces.showModalPanel('panelAutorizacaoCobranca')" reRender="formCadastroAutorizacaoCobranca" />

                <rich:jQuery query="click(function(){jQuery('.autorizacaoCobrancaItemcodigolink#{autorizacao.codigo}').click();});" selector=".autorizacaoCobrancaItemcodigo#{autorizacao.codigo}"/>
          </a4j:repeat>
      </h:panelGroup>
      </h:panelGroup>
    <h:panelGroup layout="block" style="width:100%;height: 50px">
          <h:panelGroup layout="block" style="float: right;display: inline-block;">
              <a4j:commandLink id= "novaAutorizacao" action="#{AutorizacaoCobrancaControle.novo}"
                               tabindex="10" styleClass="texto-font texto-size-14 linkPadrao texto-cor-azul"
                               value="Adicionar autorização de cobrança"
                               oncomplete="Richfaces.showModalPanel('panelAutorizacaoCobranca')"
                               reRender="formCadastroAutorizacaoCobranca, dados">
                  <i class="fa-icon-plus-sign"></i>
              </a4j:commandLink>
          </h:panelGroup>
    </h:panelGroup>
  </h:panelGroup>
</a4j:outputPanel>

<jsp:include page="/include_modal/modalVerificacaoCartao.jsp" flush="true" />
