<%--
    Created on : 11/04/2016
    Author     : joao alcides
--%>
<%@page pageEncoding="ISO-8859-1"%>
<%@include file="../imports.jsp" %>

<style>

</style>
<script>
    jQuery.noConflict();
    function desembacar(classe) {
        jQuery('.bola.item').addClass('embacado');
        jQuery('.caixaPeriodo').addClass('embacado');
        jQuery(classe).removeClass('embacado');
    }
</script>
<a4j:keepAlive beanName="LinhaTempoContratoControle"/>
<div class="tituloPainelAluno googleAnalytics">
    <h:panelGroup style="display: inline-flex;"
                  layout="block"
                  rendered="#{TelaClienteControle.contratoSelecionado != null}">
        <h:outputText value="Contrato #{TelaClienteControle.contratoSelecionado.codigo}" styleClass="texto-size-16 negrito cinzaEscuro pl20 tudo"/>

        <a4j:commandLink reRender="idpainelcontrato, idlistacontratos"
                         styleClass="linkAzul texto-size-14 step6"
                         style="padding-right: 20px;padding-left: 20px; position: absolute; right: 0;"
                         action="#{TelaClienteControle.voltarContratos}" oncomplete="#{DicasControle.exibirDicaTutorial ? 'nextStep();' : ''}">
            <i id="voltarListaContratosIcon" class="fa-icon-arrow-left"></i>
            <h:outputText id="voltarListaContratos" value="Voltar" />


        </a4j:commandLink>
    </h:panelGroup>
    <h:panelGroup id="botoesTL" style="display: inline-flex; padding: 10px;" styleClass="step4 tudo"
                  layout="block"
                  rendered="#{TelaClienteControle.contratoSelecionado == null}">
        <a4j:commandLink status="false"
                         id="btnClienteOperacoes"
                         styleClass="botaoModoTimeLine ativo operacao blocoContratos botaoBlocoPrincipal tudo"
                         onclick="desembacar('.operacao');trocarBloco('.blocoContratos', '.blocosPrincipais', '.botaoBlocoPrincipal', false);">
                <h:outputText value="Operações"/>
        </a4j:commandLink>
        <a4j:commandLink onclick="desembacar('.central');trocarBloco('.blocoCentral', '.blocosPrincipais', '.botaoBlocoPrincipal', false);"
                         id="btnClienteCentral"
                         action="#{TelaClienteControle.abrirCentral}"
                         styleClass="botaoModoTimeLine central blocoCentral botaoBlocoPrincipal tudo step7"
                         reRender="idblocoCentral"
                         rendered="#{LoginControle.apresentarLinkCE}">
                <h:outputText value="Central de Eventos"/>
        </a4j:commandLink>
        <a4j:commandLink onclick="desembacar('.financeiro');trocarBloco('.blocoFinanceiro', '.blocosPrincipais', '.botaoBlocoPrincipal', false);"
                         id="btnClienteFinanceiro"
                         action="#{TelaClienteControle.abrirFinanceiro}"
                         styleClass="botaoModoTimeLine financeiro blocoFinanceiro botaoBlocoPrincipal tudo step7"
                         reRender="idblocoFinanceiro">
                <h:outputText value="Financeiro"/>
        </a4j:commandLink>
        <a4j:commandLink id="btnClienteRelacionamento"
                         styleClass="botaoModoTimeLine relacionamento blocoRelacionamento botaoBlocoPrincipal tudo"
                         action="#{TelaClienteControle.abrirRelacionamento}"
                         reRender="idblocoRelacionamento"
                         onclick="desembacar('.relacionamento');trocarBloco('.blocoRelacionamento', '.blocosPrincipais', '.botaoBlocoPrincipal', false);"
                         >
                <h:outputText value="Relacionamento"/>
        </a4j:commandLink>

        <a4j:commandLink id="btnClienteNtaFiscal"
                         rendered="#{LoginControle.mostrarBotaoNotaFiscalPerfilCliente || LoginControle.configuracaoSistema.utilizarServicoSesiSC}"
                         styleClass="botaoModoTimeLine notasFiscais blocoNotasFiscais botaoBlocoPrincipal tudo"
                         action="#{TelaClienteControle.abrirNotasFiscais}"
                         reRender="idblocoNotasFiscais"
                         onclick="desembacar('.notasFiscais');trocarBloco('.blocoNotasFiscais', '.blocosPrincipais', '.botaoBlocoPrincipal', false);">
            <h:outputText value="Notas Fiscais"/>
        </a4j:commandLink>

        <a4j:commandLink styleClass="botaoModoTimeLine studio blocoStudio botaoBlocoPrincipal tudo"
                         id="btnClienteEstudio"
                         action="#{TelaClienteControle.abrirEstudio}"
                         reRender="idblocoStudio, modalAgendaExcecao, outLista"
                         rendered="#{LoginControle.apresentarLinkEstudio}"
                         actionListener="#{clienteEstudioControle.consultarPaginadoListener}"
                         onclick="trocarBloco('.blocoStudio', '.blocosPrincipais', '.botaoBlocoPrincipal', true);">
                         <f:attribute name="codigoCliente" value="#{TelaClienteControle.cliente.codigo}" />
                <h:outputText value="Studio"/>
        </a4j:commandLink>


    </h:panelGroup>


</div>

<h:panelGroup layout="block" styleClass="containerLinhaTempoContrato tudo  #{TelaClienteControle.contratoSelecionado == null ? '' : 'naoEmbacar'}"
              rendered="#{fn:length(LinhaTempoContratoControle.linhas) gt 0}"
              >
    <h:panelGroup layout="block" styleClass="containerLinhaTempoContrato andamento"
                  style="width: #{LinhaTempoContratoControle.percentualHoje}%;">
        <h:panelGroup layout="block"  styleClass="hoje"
                      rendered="#{LinhaTempoContratoControle.mostrarHoje}">
            HOJE
            <h:outputText value="#{LinhaTempoContratoControle.hojeApresentar}"
                          styleClass="textoData" style="position: absolute;top: 20px;left: 0;"/>
        </h:panelGroup>
    </h:panelGroup>

    <a4j:repeat value="#{LinhaTempoContratoControle.linhas}" var="linha">
        <a4j:repeat value="#{linha.meses}" var="mes" rowKeyVar="idx" >
            <h:panelGroup rendered="#{idx > 0}" layout="block" styleClass="linhaMes" style="left: #{mes.percentual}%;">
                <h:outputText styleClass="textoData mes" value="#{mes.mes}<br/>#{mes.ano}" escape="false"/>
            </h:panelGroup>
        </a4j:repeat>


        <h:panelGroup styleClass="caixaLinhaTempo carenciaRenovacao" style="width: #{100-linha.percentualFimContrato}% !important; right: 0 ;" layout="block">
            <h:panelGroup styleClass="linhaCentro verde">
            </h:panelGroup>
            <h:panelGroup styleClass="bola fim verde grande" style="background-color: #777777;left: calc(100% - 20px);">
                <i class="fa-icon-gift"></i>
                <h:outputText value="#{linha.diasCarencia} #{linha.diasCarencia == 1 ? 'dia' : 'dias'} para renovação"
                              styleClass="textoData miniTexto" style="right: 0; text-align: right;"/>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup styleClass="caixaLinhaTempo" style="width: calc(100% - 15px) !important;" layout="block">
            <!-- LINHA CENTRO -->
            <h:panelGroup styleClass="linhaCentro cinzaClaro" style="width: #{linha.percentualInicioContrato}% !important; position: absolute;"
                          rendered="#{linha.percentualInicioContrato != linha.percentualLancamentoContrato}">
            </h:panelGroup>
            <h:panelGroup styleClass="linhaCentro" style="background-color: #777777;">
            </h:panelGroup>
            <!-- LINHA CENTRO  -->
            <!-- INICIO BOLA DO INICIO DO CONTRATO  -->
            <h:panelGroup styleClass="bola contorno borderAzul" style="left: calc(#{linha.percentualInicioContrato}% - 3px);"/>

            <h:panelGroup styleClass="bola especial " style="z-index:6;background-color: #777777; left: #{linha.percentualInicioContrato}%;">
                <div class="tooltiptext fixo normal">
                    <div class="icone inicio">
                        <i class="fa-icon-hourglass-1"></i>
                        <div class="icone linha inicio"></div>
                    </div>
                    <div class="caixaToolTip">
                        <h:outputText styleClass="textoData" value="#{linha.inicioApresentar}"/>
                        <h:outputText styleClass="textoDescricao" value="Início do contrato"/>

                    </div>

                </div>
                <h:outputText value="Contrato #{linha.codigo} de #{linha.duracao} #{linha.duracao == 1 ? 'mês' : 'meses'}"
                              styleClass="textoData miniTexto"
                              rendered="#{linha.percentualInicioContrato == linha.percentualLancamentoContrato}"/>
            </h:panelGroup>
            <!-- FIM  BOLA DO INCIO DO CONTRATO  -->
            <!-- INICIO BOLA LANCAMENTO  -->
            <h:panelGroup styleClass="bola cinzaClaro lancamento" style="left: #{linha.percentualLancamentoContrato}%;"
                          rendered="#{linha.percentualInicioContrato != linha.percentualLancamentoContrato}">
                <h:outputText value="Contrato #{linha.codigo} de #{linha.duracao} #{linha.duracao == 1 ? 'mês' : 'meses'}"
                              styleClass="textoData miniTexto"
                              rendered="#{linha.percentualInicioContrato != linha.percentualLancamentoContrato}"/>

                <div class="tooltiptext fixo normal">
                    <h:panelGroup styleClass="icone linha inicio cinzaClaro" layout="block"></h:panelGroup>
                        <div class="caixaToolTip">
                        <h:outputText styleClass="textoData" value="#{linha.lancamentoApresentar}"/>
                        <h:outputText styleClass="textoDescricao" value="Lançamento do contrato"/>

                    </div>

                </div>
            </h:panelGroup>
            <!-- FIM BOLA LANCAMENTO  -->
            <!-- INICIO BOLA DO FIM DO CONTRATO  -->
            <h:panelGroup styleClass="bola contorno borderVermelho" style="left: calc(100% - 18px);"/>

            <h:panelGroup styleClass="bola fim especial" style="background-color: #777777;left: calc(100% - 15px);">
                <div class="tooltiptext fixo invertido">
                    <div class="icone fim">
                        <i class="fa-icon-hourglass-end"></i>
                        <div class="icone linha fim"></div>
                    </div>
                    <div class="caixaToolTip">
                        <h:outputText styleClass="textoData" value="#{linha.fimApresentar}"/>
                        <h:outputText styleClass="textoDescricao" value="Fim do contrato"/>
                    </div>
                </div>
            </h:panelGroup>
            <!-- FIM  BOLA DO FIM DO CONTRATO  -->

            <!-- ITENS   -->
            <a4j:repeat value="#{linha.itens}" var="item" >
                <h:panelGroup rendered="#{!item.periodo}"
                              styleClass="embacado bola grande #{item.css} item"
                              style="background-color: #777777;left: calc(#{item.percentual}% - 15px + #{item.complementoPosicao}px);">
                    <h:outputText styleClass="#{item.icone}" />
                    <div class="tooltiptext fixo normal">
                        <h:panelGroup styleClass="icone linha inicio #{item.css}" layout="block"></h:panelGroup>
                            <div class="caixaToolTip">
                            <h:outputText styleClass="textoData" value="#{item.inicioApresentar}"/>
                            <h:outputText styleClass="textoDescricao" value="#{(item.mostrarNome ? msg_aplic[item.tipoApresentar] : '')} #{item.complemento}"
                                          escape="false"/>

                        </div>

                    </div>
                </h:panelGroup>
                <h:panelGroup rendered="#{item.periodo}" styleClass="#{item.css} caixaPeriodo">
                    <h:panelGroup styleClass="linhaCentro #{item.css}"
                                  style="left: calc(#{item.percentual}% - 15px + #{item.complementoPosicao}px); width: #{item.tamanhoLinha}% !important; position: absolute;">
                    </h:panelGroup>

                    <h:panelGroup styleClass="embacado bola grande #{item.css} item periodo"
                                  style="left: calc(#{item.percentual}% - 15px + #{item.complementoPosicao}px);">
                        <h:outputText styleClass="#{item.icone}" />
                    </h:panelGroup>

                    <h:panelGroup styleClass="embacado bola grande #{item.css} item periodo"
                                  style="left: calc(#{item.percentualFim}% - 15px + #{item.complementoPosicao}px);">
                        <h:outputText styleClass="#{item.icone}" />
                    </h:panelGroup>

                    <h:panelGroup style="left: calc(#{item.percentual}% + #{item.complementoPosicao}px + #{item.tamanhoLinha/2}%);"
                                  styleClass="tooltiptext fixo normal"
                                  layout="block">
                        <h:panelGroup styleClass="icone linha inicio #{item.css}" layout="block"></h:panelGroup>
                            <div class="caixaToolTip">
                            <h:outputText styleClass="textoData" value="#{item.inicioApresentar} a #{item.fimApresentar}"/>
                            <h:outputText styleClass="textoDescricao" value="#{(item.mostrarNome ? msg_aplic[item.tipoApresentar] : '')} #{item.complemento}"
                                          escape="false"/>

                        </div>

                    </h:panelGroup>
                </h:panelGroup>
            </a4j:repeat>


        </h:panelGroup>


    </a4j:repeat>

    <script>
        desembacar('.operacao');
    </script>

</h:panelGroup>
