
<%@page contentType="text/html"%>
<%@page pageEncoding="UTF-8"%>
<%@include file="../imports.jsp" %>

<h:panelGroup rendered="#{!empty TelaClienteControle.listaContratos}">
    <table class="googleAnalytics tblHeaderLeft ${TelaClienteControle.contratoSelecionado == null ? '' : 'contratoSelecionado'} semZebra">
        <thead><tr>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="CONTRATO"/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="TIPO"/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="RESPONSÁVEL"/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="INÍCIO"/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value=""/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="FIM"/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value="SITUAÇÃO"/></th>
                <th><h:outputText styleClass="texto-size-14 cinza negrito" value=""/></th>
            </tr></thead><tbody>
            <a4j:repeat id="listaContrato"
                        value="#{TelaClienteControle.listaContratos}"
                        rowKeyVar="index"
                        var="contratoTbl">
                <tr>
                    <td><a4j:commandLink id="linkCodigoMatricula"
                            styleClass="linkAzul linkTudo texto-size-16"
                                     reRender="idpainelcontrato,idlistacontratos"
                                     oncomplete="#{TelaClienteControle.mensagemNotificar}"
                                     action="#{TelaClienteControle.selecionarContrato}" ><h:outputText id="linkMatriculaSelecionarContrato" styleClass="texto-size-16"
                                                                                         value="#{contratoTbl.codigo}"/></a4j:commandLink></td>
                    <td><a4j:commandLink id="linkTipoOperacao"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar}"
                            reRender="idpainelcontrato,idlistacontratos" styleClass="texto-size-14 linkTudo"
                                     action="#{TelaClienteControle.selecionarContrato}" ><h:outputText styleClass="texto-size-14 cinza" id="linkTipoOperacaoSelecionarContrato"
                                                                                         value="#{contratoTbl.situacaoContrato}"/></a4j:commandLink></td>
                    <td><a4j:commandLink id="linkResponsavelOperacao"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar}"
                            reRender="idpainelcontrato,idlistacontratos" styleClass="texto-size-14"
                                     action="#{TelaClienteControle.selecionarContrato}" ><h:outputText styleClass="texto-size-14 cinza" id="linkResponsavelSelecionarContrato"
                                                                                         value="#{contratoTbl.responsavelContrato.nomeAbreviado}"/></a4j:commandLink></td>
                    <td style="width: 8%;"><a4j:commandLink id="linkDataInicio"
                                                            oncomplete="#{TelaClienteControle.mensagemNotificar}"
                            styleClass="linkAzul texto-size-16"  reRender="idpainelcontrato,idlistacontratos"
                                     action="#{TelaClienteControle.selecionarContrato}" ><h:outputText style="float: left;" styleClass="texto-size-16 cinza" id="linkDataInicioSelecionarContrato"
                                                                                         value="#{contratoTbl.vigenciaDe}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText></a4j:commandLink></td>
                        <td style="width: 15%;"><div>
                            <h:panelGroup layout="block" style="height: 10px; width: 10px; position: absolute; background-color: #{TelaClienteControle.hoje > contratoTbl.vigenciaDe ? 'black' : '#29ABE2'}; border-radius: 50px; margin-top: -4px;">
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="height: 2px; width: #{contratoTbl.percentualAndamento < 0.0  ? 0.0: contratoTbl.percentualAndamento}%; float: left; background-color: black;">
                            </h:panelGroup>
                            <h:panelGroup style="float: right;height: 2px; width: #{contratoTbl.percentualAndamento < 0.0 ? 100.0 : 100 - contratoTbl.percentualAndamento}%; float: left; background-color: #29ABE2;">
                            </h:panelGroup>
                            <h:panelGroup layout="block" style="float: right; height: 10px; width: 10px; background-color: #{TelaClienteControle.hoje > contratoTbl.vigenciaAteAjustada ? 'black' : '#29ABE2'}; border-radius: 50px; margin-top: -6px;">
                            </h:panelGroup>
                        </div></td>
                    </td>

                    <td><a4j:commandLink id="linkDataFim" styleClass="linkAzul texto-size-16"
                            reRender="idpainelcontrato,idlistacontratos"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar}"
                                     action="#{TelaClienteControle.selecionarContrato}"><h:outputText styleClass="texto-size-16 cinza" id="linkDataFimSelecionarContrato"
                                                                                         value="#{contratoTbl.vigenciaAteAjustada}">
                                <f:convertDateTime pattern="dd/MM/yyyy"/>
                            </h:outputText></a4j:commandLink></td>
                    <td><a4j:commandLink id="linkSituacaoContrato"
                            reRender="idpainelcontrato,idlistacontratos"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar}"
                                     action="#{TelaClienteControle.selecionarContrato}"><h:graphicImage id="contAtivo" value="./imagens/botaoAtivo.png"
                                                                                           rendered="#{contratoTbl.contratoAtivo}" width="25"
                                                                                           height="24"/>
                            <h:graphicImage id="contCancelado" value="./imagens/botaoCancelamento.png"
                                            rendered="#{contratoTbl.contratoCancelado}" width="25"
                                            height="24"/>
                            <h:graphicImage id="contTrancado" value="./imagens/botaoTrancamento.png"
                                            rendered="#{contratoTbl.contratoTrancado}" width="25"
                                            height="24"/>
                            <h:graphicImage id="contInativo" value="./imagens/botaoInativo.png"
                                            rendered="#{contratoTbl.contratoInativo}" width="25"
                                            height="24"/>
                            <h:graphicImage id="contRenovado" value="./imagens/botaoRenovado.png"
                                            rendered="#{contratoTbl.apresentarBotaoRenovarContrato}"
                                            width="25" height="24"/></a4j:commandLink></td>
                             <td style="text-align: right;">
                                <c:if test="${not param.readOnly}">
                                    <a4j:commandLink id="renovarContrato" styleClass="linkAzul texto-size-18 icon"
                                                     rendered="#{!TelaClienteControle.novaNegociacaoPadrao and contratoTbl.renovarContrato and (contratoTbl.pessoaOriginal.codigo eq 0 or contratoTbl.pessoaOriginal.codigo eq TelaClienteControle.cliente.pessoa.codigo)}"
                                                     title="Renovar Contrato"
                                                     oncomplete="#{ClienteControle.mensagemNotificar}"
                                                     action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}">
                                        Renovar
                                    </a4j:commandLink>

                                    <a4j:commandLink id="rematricularContrato" styleClass="linkAzul texto-size-18 icon"
                                                     rendered="#{!TelaClienteControle.novaNegociacaoPadrao and contratoTbl.rematricularContrato and (contratoTbl.pessoaOriginal.codigo eq 0 or contratoTbl.pessoaOriginal.codigo eq TelaClienteControle.cliente.pessoa.codigo)}"
                                                     oncomplete="#{ClienteControle.mensagemNotificar}"
                                                     title="Rematricular Contrato"
                                                     action="#{ClienteControle.gerarOutroContratoApartirDoContratoMatricula}">
                                        Rematricular
                                    </a4j:commandLink>

                                    <a4j:commandLink id="renovarContratonew" styleClass="linkAzul texto-size-18 icon"
                                                     rendered="#{TelaClienteControle.novaNegociacaoPadrao and contratoTbl.renovarContrato and (contratoTbl.pessoaOriginal.codigo eq 0 or contratoTbl.pessoaOriginal.codigo eq TelaClienteControle.cliente.pessoa.codigo)}"
                                                     title="Renovar Contrato"
                                                     oncomplete="#{TelaClienteControle.msgAlert}#{TelaClienteControle.mensagemNotificar}"
                                                     action="#{TelaClienteControle.abrirNovNegociacao}">
                                        Renovar
                                    </a4j:commandLink>

                                    <a4j:commandLink id="rematricularContratonew" styleClass="linkAzul texto-size-18 icon"
                                                     rendered="#{TelaClienteControle.novaNegociacaoPadrao and contratoTbl.rematricularContrato and (contratoTbl.pessoaOriginal.codigo eq 0 or contratoTbl.pessoaOriginal.codigo eq TelaClienteControle.cliente.pessoa.codigo)}"
                                                     oncomplete="#{TelaClienteControle.msgAlert}#{TelaClienteControle.mensagemNotificar}"
                                                     title="Rematricular Contrato"
                                                     action="#{TelaClienteControle.abrirNovNegociacao}">
                                        Rematricular
                                    </a4j:commandLink>

                                </c:if>

                        <a4j:commandLink reRender="form:panelMensagemSuperior,form:panelMensagemInferior" style="margin-left: 10px;"
                                         id="imprimir"
                                         oncomplete="#{TelaClienteControle.mensagemNotificar}#{TelaClienteControle.msgAlert}" 
                                         styleClass="linkAzul texto-size-18 icon"
                                         action="#{TelaClienteControle.imprimirContrato}"
                                         
                                         title="Imprimir Contrato">
                            <i id="imprimirContrato" class="fa-icon-print texto-size-18 linkAzul"></i>
                        </a4j:commandLink>

                        <a4j:commandLink id="enviar" styleClass="linkAzul texto-size-18 icon" style="margin-left: 10px;"
                                         action="#{ClienteControle.prepararEnvioContratoPorEmail}"
                                         oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.msgAlert}"
                                         reRender="panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarContratoEmail"
                                         title="E-mail">
                            <i id="enviarContrato" class="fa-icon-paper-plane texto-size-18 linkAzul"></i>
                        </a4j:commandLink>

                        <a4j:commandLink style="margin-left: 10px;"
                                         id="visualizarDocs"
                                         oncomplete="setAttributesModalDocFoto('#{ClienteControle.key}','#{contratoTbl.codigo}','#{ClienteControle.contextPath}');
                                         #{TelaClienteControle.mensagemNotificar}#{TelaClienteControle.msgAlert}"
                                         styleClass="linkAzul texto-size-18 icon"
                                         reRender="visualizacaoDocs"
                                         action="#{TelaClienteControle.visualizarDocumentos}"
                                         title="Visualizar documentos associados à esse contrato">
                             <i id="visualizarDocumentosCliente" class="fa-icon-archive  texto-size-18 linkAzul"></i>
                        </a4j:commandLink>

                        <c:if test="${not param.readOnly}">
                               <a4j:commandLink styleClass="linkAzul texto-size-18 icon" style="margin-left: 10px;"
                                                reRender="modalAlterarVencimento"
                                                id="btnAbrirModalAlterarVencimentoCont"
                                                title="Alterar os vencimentos de parcelas deste contrato"
                                                oncomplete="#{rich:component('modalAlterarVencimento')}.show();"
                                                actionListener="#{AlterarVencimentoParcelasControle.preparar}">
                                    <f:attribute name="ehRecorrencia" value="#{contratoTbl.regimeRecorrencia}"/>
                                    <f:attribute name="contrato" value="#{contratoTbl}"/>
                                    <f:attribute name="cliente" value="#{TelaClienteControle.cliente}"/>
                                    <i id="btnAbrirModalAlterarVencimentoContIcon" class="fa-icon-calendar texto-size-18 linkAzul"></i>
                               </a4j:commandLink>
                        </c:if>
                    </td>
                </tr>
            </a4j:repeat>
        </tbody>
    </table>
</h:panelGroup>


