<%--
  Created by IntelliJ IDEA.
  User: anderson
  Date: 07/06/19
  Time: 16:16
  To change this template use File | Settings | File Templates.
--%>
<%@ taglib prefix="rick" uri="http://java.sun.com/jsf/html" %>
<%@include file="../../include_imports.jsp" %>
<a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
<div class="container-imagem">
    <div style="margin: 1.5vw; background-color: white;padding: 20px; position: relative; width: calc(100% - 6vw); display: inline-block; font-size: 14px"
         class="container-box zw_ui especial-2">

        <div class="tituloPainelPesquisa notop" style="font-size: 16px;">
            <h:outputText value="Criar - Or�amento"/>
            <h:outputLink styleClass="linkWiki cinza" value="#{SuperControle.urlBaseConhecimento}o-que-e-orcamento/"
                          title="Clique e saiba mais: Or�amento" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </div>

        <div style="text-align: left">


            <h:panelGroup id="dadosCliente">
                <div class="col-md-12" style="margin-top: 20px">


                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Consultor:</span>
                        <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                            <h:selectOneMenu id="consultor" style="width: 500px"
                                             value="#{OrcamentoControle.consultorSelecionado}">
                                <f:selectItems value="#{OrcamentoControle.listaSelectConsultor}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Prospecto:</span>
                        <h:panelGroup rendered="#{OrcamentoControle.clienteSelecionado == 0}">
                            <h:inputText id="nomeCliente" size="50" maxlength="50" onblur="blurinput(this);"
                                         onfocus="focusinput(this);" styleClass="inputTextClean"/>
                            <rich:suggestionbox
                                    height="200" width="444"
                                    for="nomeCliente"
                                    fetchValue="#{result.pessoa.nome}"
                                    suggestionAction="#{OrcamentoControle.executarAutocompleteConsultaCliente}"
                                    minChars="1" rowClasses="20"
                                    status="statusHora"
                                    nothingLabel="Nenhum Cliente encontrado !"
                                    var="result" id="suggestionProduto"
                                    reRender="panelBotoesControle, mensagem">
                                <a4j:support event="onselect"
                                             action="#{OrcamentoControle.selecionarClienteSuggestionBox}"
                                             reRender="panelBotoesControle, mensagem, pontosProduto"/>
                                <h:column>
                                    <h:outputText styleClass="texto-font texto-size-14-real"
                                                  value="#{result.pessoa.nome}"/>
                                </h:column>
                            </rich:suggestionbox>
                        </h:panelGroup>
                        <h:panelGroup rendered="#{OrcamentoControle.clienteSelecionado != 0}">
                            <h:inputText styleClass="inputTextClean margenVertical" disabled="true"
                                         style="margin-left: 2px; width: 500px" id="nomeClienteString"
                                         value="#{OrcamentoControle.nomeCliente}"/>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                            <h:selectOneMenu id="paraquem"
                                             value="#{OrcamentoControle.orcamentoVO.paraQuem}">
                                <f:selectItems value="#{OrcamentoControle.prospects}"/>
                                <a4j:support event="onchange" reRender="nomeProspecto, idade"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                        <h:inputText styleClass="inputTextClean margenVertical"
                                     disabled="#{OrcamentoControle.orcamentoVO.paraQuem == 0}"
                                     style="margin-left: 2px; width: 500px" id="nomeProspecto"
                                     value="#{OrcamentoControle.orcamentoVO.nomeProspecto}"/>

                        <h:outputText value="Idade:" style="margin-left: 20px"
                                      styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"/>
                        <h:inputText styleClass="inputTextClean margenVertical"
                                     disabled="#{OrcamentoControle.orcamentoVO.paraQuem == 0}"
                                     style="margin-left: 2px; width: 50px" id="idade"
                                     value="#{OrcamentoControle.orcamentoVO.idade}"/>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Orcamento:</span>
                        <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                            <h:selectOneMenu id="modeloorcamento" style="width: 500px"
                                             value="#{OrcamentoControle.modeloOrcamentoSelecionado}">
                                <f:selectItems value="#{OrcamentoControle.selectModeloOrcamentos}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Periodo:</span>
                        <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                            <h:selectOneMenu id="periodo" style="width: 200px"
                                             value="#{OrcamentoControle.periodoSelecionado}">
                                <f:selectItems value="#{OrcamentoControle.selectPeriodos}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Situacao:</span>
                        <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                            <h:selectOneMenu id="situacao" style="width: 200px"
                                             value="#{OrcamentoControle.situacaoSelecionada}">
                                <f:selectItems value="#{OrcamentoControle.selectSituacoes}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Tipo de turma no or�amento:</span>
                        <h:panelGroup layout="block" styleClass="cb-container margenVertical">
                            <h:selectOneMenu id="tipoturma" style="width: 200px"
                                             value="#{OrcamentoControle.tipoTurmaSelecionada}">
                                <f:selectItems value="#{OrcamentoControle.selectTipoTurmas}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                    <div class="col-md-12 title-space mtop20">
                        <span class="col-md-12 texto-size-14 texto-cor-cinza texto-font texto-bold">Anotacao:</span>
                        <h:panelGroup>
                            <h:inputTextarea cols="70" rows="50" id="anotacao" onblur="blurinput(this);"
                                             onfocus="focusinput(this);" styleClass="inputTextClean"
                                             style="height:5.4em;" value="#{OrcamentoControle.orcamentoVO.anotacao}"/>
                        </h:panelGroup>
                    </div>


                    <div class="col-md-12 mtop20">
                        <hr class="dividerFundoEscuro">
                    </div>

                    <div class="col-md-12 mtop20">
                        <a4j:commandButton id="salvarImprimir"
                                           action="#{OrcamentoControle.gravarImprimindo}"
                                           reRender="dadosCliente"
                                           value="Concluir e Imprimir" alt="Concluir" styleClass="botoes nvoBt"
                                           style="margin: 0; margin-top: 20px;"
                                           oncomplete="#{OrcamentoControle.realizarImpressao ? OrcamentoControle.abrirPDF : OrcamentoControle.msgAlert}; limparCampos()"/>
                        <a4j:commandButton id="enviar" styleClass="botoes nvoBt btSec"
                                           style="margin: 0; margin-left: 5px; margin-top: 20px;"
                                           action="#{OrcamentoControle.prepararEnvioNovoOrcamentoPorEmail}"
                                           oncomplete="#{OrcamentoControle.mensagemNotificar}#{OrcamentoControle.msgAlert}; limparCampos()"
                                           reRender="dadosCliente, panelMensagemSuperior, panelMensagemInferior, panelConfirmacao, modalEnviarOrcamentoEmail"
                                           value="Concluir Env. E-mail"/>
                        <a4j:commandButton id="salvar"
                                           action="#{OrcamentoControle.gravar}"
                                           reRender="dadosCliente"
                                           value="Concluir" alt="Concluir" styleClass="botoes nvoBt"
                                           style="margin: 0; margin-left: 5px; margin-top: 20px;"
                                           oncomplete="#{OrcamentoControle.msgAlert}; limparCampos()"/>
                    </div>

                </div>
            </h:panelGroup>


        </div>
        <script>
            function limparCampos() {
                document.getElementById('form:nomeCliente').value = "";
                document.getElementById('form:anotacao').value = "";
                document.getElementById('form:modeloorcamento').value = 0;
            }
        </script>
    </div>
</div>