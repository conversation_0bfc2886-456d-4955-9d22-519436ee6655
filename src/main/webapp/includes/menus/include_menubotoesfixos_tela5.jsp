<%-- 
    Document   : include_menubotoesfixos
    Created on : 20/07/2011, 18:13:41
    Author     : waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<link rel="stylesheet" href="includes/menus/menubotoes.css" type="text/css" />
<%@include file="../imports.jsp" %>


<%-- LEMBRE-SE DE QUE OS CONTROLES DEVEM FUNCIONAR EM INTERNET EXPLORER E OUTROS NAVEGADORES--%>
<c:choose>
    <c:when test="${SuperControle.internetExplorer}">

        <a4j:commandButton image="images/btn_confirmar2.gif"
                           id="btnConferirNegociacao" action="#{ContratoControle.escolherFecharNegociacao}"
                           oncomplete="#{ContratoControle.abrirRichModalAvisoDB}"
                           onclick="#{ContratoControle.onclickConferir}"/>

        <a4j:commandButton image="images/btn_cancelar.gif"
                           id="cancela" action="#{ContratoControle.cancelarNegociacao}"
                           oncomplete="#{ContratoControle.abrirRichModalAvisoDB}"/>
    </c:when>
    <c:otherwise>
        <div id="ToolbarZillyonWeb" style="display: block; margin-bottom: 0px; ">
            <div id="globalZillyonWeb">
                <div id="logoZillyonWeb" style="display: block; "></div>

                <div class="menubotoesfixo">
                    <ul id="css3menu1" class="topmenu">
                        <li class="topfirst">
                            <a4j:commandLink style="height:36px;line-height:36px;"
                                             id="btnConferirNegociacao" action="#{ContratoControle.escolherFecharNegociacao}"
                                             onclick="#{ContratoControle.onclickConferir}"
                                             oncomplete="#{ContratoControle.abrirRichModalAvisoDB}">
                                <img src="includes/menus/favour.png"/>
                                Conferir Negociação
                            </a4j:commandLink>
                        </li>
                        <li class="toplast">
                            <a4j:commandLink style="height:36px;line-height:36px;"
                                             id="cancela" action="#{ContratoControle.cancelarNegociacao}"
                                             oncomplete="#{ContratoControle.abrirRichModalAvisoDB}">
                                <img class="imgalpha" src="images/button_cancel.png" title="Cancelar" border="0"/>
                                Cancelar
                            </a4j:commandLink>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </c:otherwise>

</c:choose>