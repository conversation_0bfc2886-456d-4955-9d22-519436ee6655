<%-- 
    Document   : include_filtros_relatorio_bv
    Created on : 19/06/2012, 08:16:28
    Author     : carla
--%>
<style>
    .rich-panel {
        border-width: 0;
    }

</style>
<script type="text/javascript">
    function limparFiltrosColaborador(id) {
        var colaborador = document.getElementById(id);
        colaborador.value = '';
    }

    function adicionarOperacao(valorFiltro, tipoFiltro, marcado) {
        var recipienteFiltro = document.getElementById(tipoFiltro);
        var filtro = valorFiltro + ';';
        // verificar se ainda n�o foi selecionada
        if (marcado.checked == 1) {
            recipienteFiltro.value = recipienteFiltro.value + filtro;
        } else {
            recipienteFiltro.value = recipienteFiltro.value.replace(filtro, '');
        }
    }
</script>

<rich:tabPanel switchType="client" id="tabPanelFiltros" width="100%">

    <rich:tab id="abaData" label="Filtros">

        <h:panelGroup layout="block" style="display: inline-flex">
            <c:if test="${RelatorioBVsControle.usuarioLogado.administrador}">
                <h:outputText styleClass="tituloCampos" value="Empresa: "/>&nbsp;
                <h:selectOneMenu id="empresa" value="#{RelatorioBVsControle.filtrosConsulta.codigoEmpresa}">
                    <f:selectItem itemValue="" itemLabel="--Selecione--"/>
                    <f:selectItems value="#{RelatorioBVsControle.listaEmpresa}"/>
                    <a4j:support event="onchange" action="#{RelatorioBVsControle.limparCampos}"/>
                </h:selectOneMenu>
            </c:if>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="col-md-12 title-space" style="padding-bottom: 8px;">
            <div class="col-md-12 title-space">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">Pesquisar por</span>
            </div>
            <div class="col-md-3 margenVertical">
                <a4j:commandLink id="porPeriodo" styleClass="texto-cor-azul botao-checkbox texto-size-16"
                                 action="#{RelatorioBVsControle.alterarTipoConsulta}"
                                 reRender="form">
                    <h:outputText
                            styleClass="icon #{RelatorioBVsControle.filtrosConsulta.tipoConsulta == 1 ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'}"/>
                    <h:outputText styleClass="label" value="Per�odo"/>
                </a4j:commandLink>

                <a4j:commandLink id="porMes" styleClass="texto-cor-azul botao-checkbox texto-size-16"
                                 style="margin-left: 15px;"
                                 action="#{RelatorioBVsControle.alterarTipoConsulta}"
                                 reRender="form">
                    <h:outputText
                            styleClass="icon #{RelatorioBVsControle.filtrosConsulta.tipoConsulta == 2 ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'}"/>
                    <h:outputText styleClass="label" value="M�s/Ano"/>
                </a4j:commandLink>
            </div>
        </h:panelGroup>

        <c:if test="${RelatorioBVsControle.filtrosConsulta.tipoConsulta==1}">

            <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold" value="Per�odo"/>

            <h:panelGroup styleClass="flex" layout="block">
                <div class="margenVertical">
                    <h:panelGroup styleClass="dateTimeCustom">
                        <rich:calendar id="dataInicio"
                                       value="#{RelatorioBVsControle.filtrosConsulta.dataInicial}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"
                                       buttonIcon="/imagens_flat/calendar-button.svg">
                        </rich:calendar>
                        <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <rich:spacer width="5px"/>
                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                  style="margin: 7px 9px 0 0px;margin-left: 15px;"
                                  value="#{msg_aplic.prt_ate_Maisculo}"/>
                    <rich:spacer width="10px"/>
                    <h:panelGroup styleClass="dateTimeCustom">
                        <rich:calendar id="dataTermino"
                                       value="#{RelatorioBVsControle.filtrosConsulta.dataFinal}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"
                                       buttonIcon="/imagens_flat/calendar-button.svg">
                        </rich:calendar>
                        <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload"
                                     query="mask('99/99/9999')"/>
                    </h:panelGroup>
                    <rich:spacer width="12px"/>
                </div>
            </h:panelGroup>
        </c:if>

        <c:if test="${RelatorioBVsControle.filtrosConsulta.tipoConsulta==2}">
            <div class="col-md-12 title-space">
                <div class="col-md-12 title-space">
                    <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">M�s</span>
                </div>
                <div class="col-md-1 cb-container margenVertical">
                    <h:selectOneMenu id="meses" styleClass=""
                                     value="#{RelatorioBVsControle.filtrosConsulta.codigoMes}">
                        <f:selectItems value="#{RelatorioBVsControle.meses}"/>
                    </h:selectOneMenu>
                </div>
                <h:panelGroup layout="block" styleClass="col-md-3 title-space margenVertical" style="padding-left: 5px">
                    <h:inputText styleClass="inputTextClean"
                                 maxlength="4" size="5"
                                 onkeypress="return mascara(this.form, this.id, '9999', event);"
                                 value="#{RelatorioBVsControle.filtrosConsulta.ano}"
                                 title="Ano">
                        <f:convertDateTime dateStyle="short" locale="#{SuperControle.localeDefault}"
                                           pattern="yyyy"/>
                    </h:inputText>
                </h:panelGroup>
            </div>
        </c:if>

        <div class="col-md-12 title-space">
            <div class="col-md-12 title-space">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">Question�rio</span>
            </div>
            <div class="cb-container margenVertical">
                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="questionario"
                                 value="#{RelatorioBVsControle.filtrosConsulta.codigoQuestionario}">
                    <f:selectItems value="#{RelatorioBVsControle.listaQuestionarios}"/>
                    <a4j:support event="onchange" action="#{RelatorioBVsControle.montarListaPerguntas}" reRender="form"/>
                </h:selectOneMenu>
            </div>
        </div>


        <div class="col-md-12 title-space">
            <div class="col-md-12 title-space">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold"> </span>
            </div>
            <div class="col-md-3 margenVertical">
                <a4j:commandLink id="somentePerguntasNoQuestionario"
                                 styleClass="texto-cor-azul botao-checkbox texto-size-16"
                                 reRender="form" action="#{RelatorioBVsControle.montarListaPerguntas}">
                    <h:outputText
                            styleClass="icon #{RelatorioBVsControle.filtrosConsulta.somentePerguntasNoQuestionario ? 'fa-icon-check' : 'fa-icon-check-empty'}"/>
                    <h:outputText styleClass="label" value="Somente perguntas no question�rio"/>
                    <f:setPropertyActionListener
                            value="#{!RelatorioBVsControle.filtrosConsulta.somentePerguntasNoQuestionario}"
                            target="#{RelatorioBVsControle.filtrosConsulta.somentePerguntasNoQuestionario}"/>
                </a4j:commandLink>
            </div>
        </div>


        <div class="col-md-12 title-space">
            <div class="col-md-12 title-space">
                <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">Pergunta</span>
            </div>
            <div class="cb-container margenVertical">
                <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="pergunta"
                                 value="#{RelatorioBVsControle.filtrosConsulta.indexPergunta}">
                    <f:selectItems value="#{RelatorioBVsControle.listaPerguntas}"/>
                    <a4j:support event="onchange" action="#{RelatorioBVsControle.preencherListaPerguntas}" reRender="form"/>
                </h:selectOneMenu>
            </div>
        </div>

        <c:if test="${!RelatorioBVsControle.relatorioPesquisa && not empty RelatorioBVsControle.listaEventos}">
            <div class="col-md-12 title-space ">
                <div class="col-md-12 title-space">
                    <span class="texto-size-16 texto-cor-cinza texto-font texto-bold">Evento</span>
                </div>
                <div class="cb-container margenVertical">
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" id="evento"
                                     value="#{RelatorioBVsControle.filtrosConsulta.eventoVO.codigo}">
                        <f:selectItems  value="#{RelatorioBVsControle.listaEventos}"/>
                    </h:selectOneMenu>
                </div>
            </div>
        </c:if>


        <c:if test="${RelatorioBVsControle.mostrarCampoRespostaTextual}">
            <h:outputText styleClass="tituloCampos" value="Resposta Subjetiva: "/>
            <h:inputText onblur="blurinput(this);" onfocus="focusinput(this);" id="respostaSubjetiva"
                         value="#{RelatorioBVsControle.filtrosConsulta.respostaSubjetiva}">
            </h:inputText>
        </c:if>

    </rich:tab>

    <rich:tab id="abaConsultores" styleClass="titulo3" label="Consultores">
        <h:panelGroup layout="block" style="width: 100%; display: inline-flex" id="panelConsultores">
            <h:panelGroup id="consultoresAtivos" style="width: 50%">
                <fieldset>
                    <legend>
                        <h:selectBooleanCheckbox value="#{RelatorioBVsControle.filtrosConsulta.consultoresAtivos}">
                            <a4j:support event="onchange" reRender="consultoresAtivos"
                                         action="#{RelatorioBVsControle.marcarTodosAtivos}"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"
                                      style="padding-left: 5px"
                                      value="Ativos"/>
                    </legend>
                    <table width="100%" class="text">
                        <a4j:repeat var="colaborador"
                                    value="#{RelatorioBVsControle.consultoresAtivos}">
                            <tr>
                                <td valign="top">
                                    <h:selectBooleanCheckbox value="#{colaborador.selecionado}">
                                        <a4j:support event="onchange" reRender="consultoresAtivos" status="false"
                                                     action="#{RelatorioBVsControle.verificarCheckTodos}"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText style="padding-left: 5px" value="#{colaborador.pessoa_Apresentar}"/>
                                </td>
                            </tr>
                        </a4j:repeat>
                    </table>
                </fieldset>
            </h:panelGroup>

            <h:panelGroup id="consultoresInativos" style="width: 50%">
                <fieldset>
                    <legend>
                        <h:selectBooleanCheckbox value="#{RelatorioBVsControle.filtrosConsulta.consultoresInativos}">
                            <a4j:support event="onchange" reRender="consultoresInativos"
                                         action="#{RelatorioBVsControle.marcarTodosInativos}"/>
                        </h:selectBooleanCheckbox>
                        <h:outputText styleClass="texto-size-16 texto-cor-cinza texto-font texto-bold"
                                      style="padding-left: 5px"
                                      value="Inativos"/>
                    </legend>
                    <table width="100%" class="text">
                        <a4j:repeat var="colaborador"
                                    value="#{RelatorioBVsControle.consultoresInativos}">
                            <tr>
                                <td valign="top">
                                    <h:selectBooleanCheckbox value="#{colaborador.selecionado}">
                                        <a4j:support event="onchange" reRender="consultoresInativos" status="false"
                                                     action="#{RelatorioBVsControle.verificarCheckTodos}"/>
                                    </h:selectBooleanCheckbox>
                                    <h:outputText style="padding-left: 5px" value="#{colaborador.pessoa_Apresentar} "/>
                                </td>
                            </tr>
                        </a4j:repeat>
                    </table>
                </fieldset>
            </h:panelGroup>
        </h:panelGroup>
    </rich:tab>
</rich:tabPanel>
