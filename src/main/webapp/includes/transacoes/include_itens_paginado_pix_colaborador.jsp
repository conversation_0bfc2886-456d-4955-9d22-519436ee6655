<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<c:set var="pageEstornoOuGestao" value="${pageContext.request.requestURI}" scope="request"/>
<a4j:outputPanel id="containerListaPix">
    <h:outputText value="Transações de Pix"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20"/>
    <h:outputText value="Nenhuma transação de pix"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{ColaboradorControle.listaPix.count <= 0}"/>

    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblTransacoesPix"
                    styleClass="tabelaDados font-size-Em"
                    onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                    onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                    value="#{ColaboradorControle.listaPixVo}"
                    rendered="#{ColaboradorControle.listaPix.count > 0}"
                    var="pix">

        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column sortBy="#{pix.txid}" style="text-transform: none;">
            <f:facet name="header">
                <h:outputText value="TxId"
                              title="Identificador da Transação Pix"
                              styleClass="tooltipster"/>
            </f:facet>
            <h:outputText  styleClass="tooltipster"
                           value="#{pix.txid}"
                           title="Cód. do pix: #{pix.codigo} | #{pix.convenioCobranca_Title}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{pix.valor}">
            <f:facet name="header">
                <h:outputText value="Parcelas"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          value="#{pix.parcelasTelaCliente}"
                          title="#{pix.parcelasTelaClienteTitle}"
                          escape="false"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{pix.valor}">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:outputText value="#{pix.valorApresentar}"/>
        </rich:column>

        <rich:column sortBy="#{pix.status}">
            <f:facet name="header">
                <h:outputText value="Situação"/>
            </f:facet>
            <h:outputText value="#{pix.statusDescricao}"
                          styleClass="tooltipster"
                          title="#{pix.statusExplicacao}" />
        </rich:column>

        <rich:column sortBy="#{pix.data}">
            <f:facet name="header">
                <h:outputText value="Data"
                              styleClass="tooltipster"
                              title="Data da criação do Pix"/>
            </f:facet>
            <h:outputText value="#{pix.dataFormatada}"/>
        </rich:column>

        <rich:column sortBy="#{pix.reciboPagamento}">
            <f:facet name="header">
                <h:outputText value="Recibo"
                              styleClass="tooltipster"/>
            </f:facet>
            <h:outputText value="#{pix.reciboPagamento}"/>
        </rich:column>

        <rich:column style="display: flex">
            <f:facet name="header">
                <h:outputText value="Opções"
                              styleClass="tooltipster"/>
            </f:facet>

            <%--ENVIO--%>
            <a4j:commandLink rendered="#{not empty pix.paramsEnvio}"
                             style="margin-right: 10px; color: #444; font-size: 17px"
                             styleClass="tooltipster"
                             title="Visualizar os parâmetros de envio"
                             reRender="panelDadosParametrosPix"
                             actionListener="#{PixRelatorioController.exibirParams}">
                <f:attribute name="params" value="envio"/>
                <f:attribute name="pix" value="#{pix}"/>
                <i class="fa-icon-share-alt"></i>
            </a4j:commandLink>


            <%--RESPOSTA--%>
            <a4j:commandLink rendered="#{not empty pix.paramsResposta}"
                             reRender="panelDadosParametrosPix"
                             style="margin-right: 10px; color: #444; font-size: 17px"
                             styleClass="tooltipster"
                             title="Visualizar os parâmetros de resposta"
                             actionListener="#{PixRelatorioController.exibirParams}">
                <f:attribute name="params" value="resposta"/>
                <f:attribute name="pix" value="#{pix}"/>
                <i class="fa-icon-reply"></i>
            </a4j:commandLink>
        </rich:column>

    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{ColaboradorControle.listaPix.count > 0 }" width="100%"
                 columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{ColaboradorControle.listaPix.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaPix"
                                             actionListener="#{ColaboradorControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaPix"
                                             actionListener="#{ColaboradorControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{ColaboradorControle.listaPix.paginaAtualApresentar}"
                                          rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                             reRender="containerListaPix"
                                             actionListener="#{ColaboradorControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaPix"
                                             actionListener="#{ColaboradorControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{ColaboradorControle.listaPix.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange"
                                                 actionListener="#{ColaboradorControle.atualizarNumeroItensPagina}"
                                                 reRender="containerListaPix">
                                        <f:attribute name="tipo" value="LISTA_PIX"/>
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</a4j:outputPanel>
