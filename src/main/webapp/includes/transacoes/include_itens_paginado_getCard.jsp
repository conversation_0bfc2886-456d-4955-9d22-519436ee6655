<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<c:set var="pageEstornoOuGestao" value="${pageContext.request.requestURI}" scope="request"/>
<a4j:outputPanel id="containerListaGetCard">
    <h:outputText value="Cancelamento de Cobranças GetCard"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 negrito cinzaEscuro pl20"/>
    <h:outputText value="Nenhum cancelamento de cobrança getcard"
                  style="margin-top: 20px; display: block;"
                  styleClass="texto-size-16 cinza pl20"
                  rendered="#{TelaClienteControle.listaCancelamentoGetCard.count <= 0}"/>

    <rich:dataTable rowClasses="linhaPar,linhaImpar" rowKeyVar="status" id="tblTransacoesPix2"
                    styleClass="tabelaDados font-size-Em"
                    onRowMouseOver="this.addClassName('background-color-tabelaDados-over')"
                    onRowMouseOut="this.removeClassName('background-color-tabelaDados-over')"
                    value="#{TelaClienteControle.listaCancelamentoGetCardVo}"
                    rendered="#{TelaClienteControle.listaCancelamentoGetCard.count > 0}"
                    var="getcard">

        <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

        <rich:column sortBy="#{getcard.idTransacao}" style="text-transform: none;">
            <f:facet name="header">
                <h:outputText value="ID Transação"
                              title="Identificador da Transação"
                              styleClass="tooltipster"/>
            </f:facet>
            <h:outputText  styleClass="tooltipster"
                           title="Convênio: #{getcard.convenio}"
                           value="#{getcard.idTransacao}"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{getcard.valor}">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          value="#{getcard.valor}"
                          escape="false">
                <f:convertNumber type="number" groupingUsed="true" minFractionDigits="2" pattern="#{TelaClienteControle.empresaLogado.moeda} #0.00"/>
            </h:outputText>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{getcard.parcelas}">
            <f:facet name="header">
                <h:outputText value="Parcelas"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          value="#{getcard.parcelas}"
                          escape="false"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{getcard.usuario}">
            <f:facet name="header">
                <h:outputText value="Usuário Responsável"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          value="#{getcard.usuario}"
                          escape="false"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda" sortBy="#{getcard.dataRegistro}">
            <f:facet name="header">
                <h:outputText value="Data"/>
            </f:facet>
            <h:outputText styleClass="tooltipster"
                          value="#{getcard.dataRegistro}"
                          escape="false"/>
        </rich:column>

        <rich:column styleClass="colunaEsquerda">
            <f:facet name="header">
                <h:outputText value="Opções"
                              styleClass="tooltipster"/>
            </f:facet>

            <h:panelGroup style="display: inline-flex">
                <%--ENVIO--%>
                <a4j:commandLink
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="tooltipster"
                                 title="Visualizar os parâmetros de envio"
                                 reRender="panelDadosParametrosGetCard"
                                 actionListener="#{GetCardRelatorioController.exibirParams}">
                    <f:attribute name="params" value="envio"/>
                    <f:attribute name="cancelGetCard" value="#{getcard}"/>
                    <i class="fa-icon-share-alt"></i>
                </a4j:commandLink>

                <%--RESPOSTA--%>
                <a4j:commandLink
                                 reRender="panelDadosParametrosGetCard"
                                 style="margin-right: 10px; color: #444; font-size: 17px"
                                 styleClass="tooltipster"
                                 title="Visualizar os parâmetros de resposta"
                                 actionListener="#{GetCardRelatorioController.exibirParams}">
                    <f:attribute name="params" value="resposta"/>
                    <f:attribute name="cancelGetCard" value="#{getcard}"/>
                    <i class="fa-icon-reply"></i>
                </a4j:commandLink>
                <a4j:commandLink
                        actionListener="#{GetCardRelatorioController.tratarRecibo}"
                        style="margin-right: 10px; color: #444; font-size: 17px"
                        styleClass="tooltipster"
                        title="Imprimir comprovante cancelamento"
                        oncomplete="abrirPopup('includes/transacoes/compCancelGetCard.jsp','', 500, 500);">
                    <f:attribute name="params" value="reciboCancelamento"/>
                    <f:attribute name="cancelGetCard" value="#{getcard}"/>
                    <i class="fa-icon-file-alt"></i>
                </a4j:commandLink>
                <a4j:commandLink
                        actionListener="#{GetCardRelatorioController.tratarRecibo}"
                        style="margin-right: 10px; color: #444; font-size: 17px"
                        styleClass="tooltipster"
                        title="Enviar comprovante cancelamento por e-mail"
                        oncomplete="#{GetCardRelatorioController.mensagemNotificar}"
                        reRender="panelDadosParametrosGetCard;mensagens">
                    <f:attribute name="params" value="enviarEmail"/>
                    <f:attribute name="cancelGetCard" value="#{getcard}"/>
                    <i class="fa-icon-paper-plane"></i>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>

    </rich:dataTable>

    <h:panelGrid columns="1" rendered="#{TelaClienteControle.listaCancelamentoGetCard.count > 0 }" width="100%"
                 columnClasses="colunaCentralizada">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td align="center" valign="middle">
                    <h:panelGroup layout="block"
                                  styleClass="paginador-container">
                        <h:panelGroup styleClass="pull-left"
                                      layout="block">
                            <h:outputText
                                    styleClass="texto-size-14 cinza"
                                    value="Total #{TelaClienteControle.listaCancelamentoGetCard.count} itens"></h:outputText>
                        </h:panelGroup>
                        <h:panelGroup layout="block"
                                      style="align-items: center">
                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaGetCard"
                                             actionListener="#{TelaClienteControle.primeiraPagina}">
                                <i class="fa-icon-double-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaGetCard"
                                             actionListener="#{TelaClienteControle.paginaAnterior}">
                                <i class="fa-icon-angle-left"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>

                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16-real"
                                          value="#{msg_aplic.prt_msg_pagina} #{TelaClienteControle.listaCancelamentoGetCard.paginaAtualApresentar}"
                                          rendered="true"/>
                            <a4j:commandLink styleClass="linkPadrao texto-font texto-cor-azul texto-size-20-real"
                                             reRender="containerListaGetCard"
                                             actionListener="#{TelaClienteControle.proximaPagina}">
                                <i class="fa-icon-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>

                            <a4j:commandLink styleClass="linkPadrao texto-cor-azul texto-size-20-real"
                                             reRender="containerListaGetCard"
                                             actionListener="#{TelaClienteControle.ultimaPagina}">
                                <i class="fa-icon-double-angle-right"></i>
                                <f:attribute name="tipo" value="LISTA_PIX"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="paginador-itens-pagina">
                            <h:panelGroup styleClass="pull-right" layout="block">
                                <h:outputText
                                        styleClass="texto-size-14 cinza "
                                        value="Itens por página "></h:outputText>
                            </h:panelGroup>
                            <h:panelGroup styleClass="cb-container pl20" layout="block">
                                <h:selectOneMenu value="#{TelaClienteControle.listaCancelamentoGetCard.limit}">
                                    <f:selectItem itemValue="#{6}"></f:selectItem>
                                    <f:selectItem itemValue="#{10}"></f:selectItem>
                                    <f:selectItem itemValue="#{20}"></f:selectItem>
                                    <f:selectItem itemValue="#{50}"></f:selectItem>
                                    <f:selectItem itemValue="#{100}"></f:selectItem>
                                    <a4j:support event="onchange"
                                                 actionListener="#{TelaClienteControle.atualizarNumeroItensPagina}"
                                                 reRender="containerListaGetCard">
                                        <f:attribute name="tipo" value="LISTA_GETCARD"/>
                                    </a4j:support>
                                </h:selectOneMenu>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
                </td>
            </tr>
        </table>
    </h:panelGrid>
</a4j:outputPanel>
<script>
    function abrirPopupHtml(html) {
        var popup = window.open("", "Comprovante", "width=600,height=800");
        popup.document.write(html);
        popup.document.close();
    }
</script>