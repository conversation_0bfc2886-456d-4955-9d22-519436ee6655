<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 14/04/2016
  Time: 09:31
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@include file="imports.jsp" %>
<h:panelGroup layout="block" id="pgModalidade" styleClass="itemLinha" style="margin-top: 20px;margin-bottom: 20px;">
    <h:panelGroup layout="block"
                  rendered="#{ContratoControle.mostrarPanelModalidade  && not empty ContratoControle.contratoVO.contratoModalidadeVOs && (ContratoControle.planoDuracaoCreditoTreinoSelecionado.codigo != 0 || !ContratoControle.contratoVO.plano.vendaCreditoTreino)}"
                  style="margin-bottom: 10px">
        <h:outputText value="MODALIDADE" styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                      style="margin-top: 10px;margin-bottom: 10px"/>
        <h:outputLink styleClass="linkWiki tooltipster"
                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                      title="Clique e saiba mais: Negociação - Modalidade"
                      target="_blank">
            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
        </h:outputLink>
        <a4j:repeat id="planoModalidadeVO" value="#{ContratoControle.contratoVO.contratoModalidadeVOs}" var="cm">

            <h:panelGroup id="tabelaModalidadeItem"
                          layout="block" style="margin-top: 10px;"
                          styleClass="containerModalidadeItem #{cm.modalidade.modalidadeEscolhida ? ( (cm.nrVezesSemana > 0 || !cm.modalidade.utilizarTurma) && (!cm.modalidade.editarModalidade) ?  '' : (cm.modalidade.pendente ? 'bordaVermelha' : 'bordaAzul')) : 'bg-desabilitado'}">
                <h:panelGroup layout="block" styleClass="modalidadeHeader">
                    <h:graphicImage value="../imagens_flat/icon-excessao.svg" styleClass="tooltipster" title="Modalidade com exceção" style="vertical-align: text-bottom;margin:0px 1% 0px 2.5%" rendered="#{not empty cm.excecao}" height="23em"/>
                    <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza textoImcompleto tooltipster"
                                  title="#{cm.modalidade.nome}" style="max-width: 50%" rendered="#{cm.modalidade.composicaoLivre == 0}" value="#{cm.modalidade.nome}"/>
                    <h:outputText styleClass="texto-size-16 texto-font texto-cor-verde textoImcompleto tooltipster"
                                  title="#{cm.modalidade.nome}" style="max-width: 50%"  rendered="#{cm.modalidade.composicaoLivre > 0}" value="#{cm.modalidade.nome}"/>
                    <h:panelGroup layout="block" style="float: right; #{ContratoControle.contratoVO.plano.creditoSessao ? 'width: 60%' : 'width: 40%'}">

                        <a4j:commandLink id="btnApresentarDetModalidade"
                                         styleClass="linkPadrao" reRender="pgModalidade, panelParcelaProdutoMatricula"
                                         action="#{ContratoControle.editarModalidade}"
                                         rendered="#{cm.modalidade.modalidadeEscolhida  && !cm.modalidade.editarModalidade && (cm.modalidade.modalidadeEscolhida && cm.nrVezesSemana > 0) && !((cm.modalidade.composicao) || (ContratoControle.planoCreditoTreinoHorarioLivre)) and cm.modalidade.utilizarTurma}">
                            <h:outputText style="margin-left: 2px; margin-right: 15px"
                                          styleClass="texto-size-18 texto-cor-cinza fa-icon-chevron-down btnRemoverModalidade lineHeight-3em"/>
                        </a4j:commandLink>
                        <a4j:commandLink id="btnEsconderDetModalidade"
                                         styleClass="linkPadrao" reRender="pgModalidade, panelParcelaProdutoMatricula"
                                         action="#{ContratoControle.editarModalidade}"
                                         rendered="#{cm.modalidade.modalidadeEscolhida  && cm.modalidade.editarModalidade && (cm.modalidade.modalidadeEscolhida && cm.nrVezesSemana > 0) && !((cm.modalidade.composicao) || (ContratoControle.planoCreditoTreinoHorarioLivre)) and cm.modalidade.utilizarTurma}">
                            <h:outputText style="margin-left: 2px; margin-right: 15px"
                                          styleClass="texto-size-18 texto-cor-cinza fa-icon-chevron-up btnRemoverModalidade lineHeight-3em"/>
                        </a4j:commandLink>
                        <a4j:commandLink id="btnApresentarAlertaSemTurma"
                                         styleClass="linkPadrao" reRender="pgModalidade, panelParcelaProdutoMatricula"
                                         action="#{ContratoControle.abrirDetalheModalidadeSemTurma}"
                                         rendered="#{!cm.modalidade.modalidadeTurmaParaSelecionar and cm.modalidade.utilizarTurma}">
                            <h:outputText style="margin-left: 2px; margin-right: 15px" title="Sem turmas e/ou idade não permitida"
                                          styleClass="texto-size-20 fa-icon-exclamation-triangle-sign texto-cor-amarelo lineHeight-3em tooltipster" />
                        </a4j:commandLink>

                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza pull-left tooltipster" id="qtdeTotalCredMod"
                                      rendered="#{ContratoControle.contratoVO.plano.creditoSessao && cm.contratoModalidadeCredito.qtdCreditoCompra > 0}"
                                      title="Quantidade total crédito da modalidade" style="max-width: 50%"
                                      value="#{cm.contratoModalidadeCredito.qtdCreditoCompra} crédito(s)"/>

                        <h:outputText styleClass="texto-size-16 texto-font texto-cor-cinza pull-left tooltipster" id="valorTotalMod"
                                      rendered="#{ContratoControle.contratoVO.plano.creditoSessao && cm.contratoModalidadeCredito.qtdCreditoCompra > 0}"
                                      title="Valor total da modalidade" style="max-width: 40%"
                                      value="#{cm.contratoModalidadeCredito.valorTotal_Apresentar}"/>

                        <h:outputText
                                styleClass="texto-size-20 texto-cor-verde fa-icon-ok-sign pull-right lineHeight-3em"
                                rendered="#{cm.modalidade.modalidadeEscolhida && ((cm.modalidade.composicao) || (ContratoControle.planoCreditoTreinoHorarioLivre)) && !cm.modalidade.utilizarTurma}"/>


                        <a4j:commandLink id="btnDesmarcarModalidade" styleClass="linkPadrao"
                                         reRender="pgModalidade,pgDuracaoCreditoTreinoSessao,panelParcelaProdutoMatricula,planoDuracaoVO, camposDescontoRedesenhado,#{ContratoControle.updateComponente.valoresContrato}"
                                         action="#{ContratoControle.desmarcarModalidade}"
                                         rendered="#{cm.modalidade.modalidadeEscolhida}">
                            <h:outputText style="margin-right: 5px"
                                          styleClass="texto-size-20 texto-cor-vermelho fa-icon-minus-sign btnRemoverModalidade lineHeight-3em"/>
                        </a4j:commandLink>

                        <h:outputText
                                styleClass="texto-size-20 texto-cor-cinza-3 fa-icon-minus-sign btnRemoverModalidade pull-right lineHeight-3em"
                                rendered="#{((cm.modalidade.composicao) || (ContratoControle.planoCreditoTreinoHorarioLivre)) && ( cm.modalidade.editarModalidade || (cm.modalidade.utilizarTurma && empty cm.contratoModalidadeTurmaVOs))}"/>

                        <a4j:commandLink id="btnAdvertencia"
                                         styleClass="linkPadrao" 
                                         rendered="#{cm.advertenciaMatriculasFuturas}" title="Turma com matrícula(s) futura(s) que pode(m) exceder o limite do horário"> 
                                 <h:outputText styleClass="fa-icon-exclamation-triangle-sign texto-cor-amarelo texto-size-20 lineHeight-3em"/>
                        </a4j:commandLink>        


                        <a4j:commandLink id="selectModalidade" styleClass="linkPadrao"
                                         reRender="pgModalidade,panelParcelaProdutoMatricula,#{ContratoControle.updateComponente.valoresContrato},panelEdicaoModalidade,formConsultarTurma,camposDescontoRedesenhado,planoDuracaoVO,btnDesmarcarModalidade"
                                         action="#{ContratoControle.marcarModalidade}"
                                         oncomplete="#{ContratoControle.mensagemNotificar} #{ContratoControle.abrirModalAdicionarHorarioVZSemana}"
                                         rendered="#{(not cm.modalidade.modalidadeEscolhida and cm.modalidade.utilizarTurma and cm.modalidade.modalidadeTurmaParaSelecionar) ||
                                         (not cm.modalidade.modalidadeEscolhida and not cm.modalidade.utilizarTurma )}">
                            <h:outputText style="margin-left: 3px; margin-right: 15px"
                                    styleClass="texto-size-20 texto-cor-verde fa-icon-plus-sign btnSelecionarModalidade lineHeight-3em"/>
                        </a4j:commandLink>

                        <a4j:commandLink id="editarTurmasSele" styleClass="linkAzul icon"
                                         rendered="#{(cm.modalidade.modalidadeEscolhida) && (!ContratoControle.contratoVO.plano.vendaCreditoTreino and cm.modalidade.utilizarTurma)}"
                                         title="Editar Turma"
                                         action="#{ContratoControle.editarModalidade}"
                                         reRender="formConsultarTurma:containerConsultaTurma"
                                         oncomplete="Richfaces.showModalPanel('panelConsultarTurma');">
                            <h:outputText id="editarTurmaSeleIcon" styleClass="texto-size-20 texto-cor-cinza fa-icon-edit lineHeight-3em" style="margin-left: 3px; margin-right: 3px; margin-top: 2px;"/>
                        </a4j:commandLink>

                        <a4j:commandLink action="#{ContratoControle.consultarModalidadeProdutoSugerido}"
                                         styleClass="linkPadrao"
                                         rendered="#{not empty cm.modalidade.produtoSugeridoVOs}"
                                         reRender="formProdutoSugerido,panelEdicaoModalidade"
                                         oncomplete="Richfaces.showModalPanel('panelProdutoSugerido')">
                            <h:outputText style="margin-left: 3px; margin-right: 3px"
                                    styleClass="texto-size-20 texto-cor-azul fa-icon-cart-arrow-down btnProdutosSugeridos esconder lineHeight-3em"/>
                        </a4j:commandLink>


                        <h:outputText value="#{cm.nrVezesSemana}X" rendered="#{(!cm.modalidade.modalidadeEscolhida)}" style="visibility: #{cm.nrVezesSemana > 0 ? 'visible' : 'hidden'}" title="#{cm.nrVezesSemana} Vez#{cm.nrVezesSemana  != 1 ? 'es' : ''} por semana"  styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold pull-left tooltipster"/>
                        <h:outputText value="#{cm.nrVezesSemana}X" styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold pull-left tooltipster" rendered="#{(ContratoControle.planoCreditoTreinoHorarioLivre) && (cm.modalidade.modalidadeEscolhida)}"/>
                        <h:outputText value="#{cm.nrVezesSemana}X"
                                      rendered="#{(cm.nrVezesSemana == 0 and cm.modalidade.modalidadeEscolhida) && ((ContratoControle.planoCreditoTreinoHorarioTurma))}"
                                      style="visibility: #{cm.nrVezesSemana > 0 ? 'visible' : 'hidden'}" title="#{cm.nrVezesSemana} Vez#{cm.nrVezesSemana  != 1 ? 'es' : ''} por semana"  styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold pull-left tooltipster"/>

                        <h:outputText value="#{cm.nrVezesSemana}X"
                                      rendered="#{(ContratoControle.planoCreditoTreinoHorarioTurma) && (cm.nrVezesSemana > 0 and cm.modalidade.modalidadeEscolhida)}"
                                      style="visibility: #{cm.nrVezesSemana > 0 ? 'visible' : 'hidden'}"
                                      title="#{cm.nrVezesSemana} Vez#{cm.nrVezesSemana  != 1 ? 'es' : ''} por semana"
                                      styleClass="texto-size-16 texto-font texto-cor-cinza texto-bold pull-left tooltipster"/>

                        <a4j:commandLink value="#{cm.nrVezesSemana}X"
                                         rendered="#{(cm.nrVezesSemana == 0 and cm.modalidade.modalidadeEscolhida) && (!ContratoControle.contratoVO.plano.vendaCreditoTreino)}"
                                         reRender="panelGridPlanoModalidadeVezesSemana"
                                         styleClass="linkPadrao texto-size-16 texto-font texto-cor-vermelho texto-bold pull-left tooltipster"
                                         style="margin-right: 2%"
                                         action="#{ContratoControle.consultarVezesSemana}" oncomplete="Richfaces.showModalPanel('panelPlanoModalidadeVezesSemana')"  />

                        <a4j:commandLink value="#{cm.nrVezesSemana}X" reRender="panelGridPlanoModalidadeVezesSemana"
                                         style="margin-right: 2%"
                                         styleClass="linkPadrao texto-size-16 texto-font texto-cor-cinza texto-bold pull-left tooltipster"
                                         rendered="#{(cm.nrVezesSemana > 0 and cm.modalidade.modalidadeEscolhida) && (!ContratoControle.contratoVO.plano.vendaCreditoTreino)}"
                                         action="#{ContratoControle.consultarVezesSemana}" oncomplete="Richfaces.showModalPanel('panelPlanoModalidadeVezesSemana')"  />

                        <h:outputText rendered="#{!ContratoControle.contratoVO.plano.regimeRecorrencia}"
                                      styleClass="texto-size-16 texto-font texto-cor-cinza"
                                      style="float: left;margin-left:5px;margin-right:0px" value="#{MovPagamentoControle.empresaLogado.moeda}">
                        </h:outputText>

                        <h:outputText rendered="#{!ContratoControle.contratoVO.plano.regimeRecorrencia}"
                                      styleClass="texto-size-16 texto-font texto-cor-cinza"
                                      style="float: left;margin-left:5px;margin-right:5px"
                                      value="#{cm.modalidade.modalidadeEscolhida ? cm.modalidade.valorMensalFinal : cm.modalidade.valorOriginal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>

                        <a4j:commandLink id="btnSelecionarDescontoTurma" rendered="#{fn:length(cm.listaContratoModalidadesHorarioTurmaVOs) > 0}"
                                         styleClass="linkPadrao" reRender="form"
                                         action="#{ContratoControle.calcularModalidade}" >
                            <h:outputText title="#{cm.calculaDescontoOcupacao ? 'Remover desconto por ocupação' : 'Incluir desconto por ocupação' }"
                                    styleClass="texto-size-18 fa-icon-percent lineHeight-3em #{cm.calculaDescontoOcupacao ?'texto-cor-azul':'texto-cor-cinza'} tooltipster"
                                    style="margin-right: 5px;margin-left: 0px"/>
                        </a4j:commandLink>


                    </h:panelGroup>

                </h:panelGroup>

<%--                <h:panelGroup layout="block" style="width: 100%;line-height: 3;text-align: center;display: table;"--%>
<%--                              rendered="#{cm.modalidade.modalidadeEscolhida}">--%>
<%--                    <a4j:commandLink id="edtModalidade" styleClass="botaoPrimario texto-cor-branco texto-size-16 tooltipster"--%>
<%--                                     title="Adicionar turmas na modalidade"--%>
<%--                                     reRender="panelEdicaoModalidade,panelConsultarTurma"--%>
<%--                                     action="#{ContratoControle.editarModalidade}"--%>
<%--                                     rendered="#{empty cm.listaContratoModalidadesHorarioTurmaVOs && cm.modalidade.utilizarTurma}"--%>
<%--                                     oncomplete="#{ContratoControle.abrirModalAdicionarHorarioVZSemana}"--%>
<%--                                     value="#{cm.modalidade.utilizarTurma ? 'Adicionar turmas' : 'Adicionar Vezes X Semana'}"/>--%>
<%--                </h:panelGroup>--%>
                <h:panelGroup id="listaModalidadeSelecionada" layout="block" style="width: 100%;display: inline-block;"
                              rendered="#{cm.modalidade.editarModalidade}" styleClass="panelEditarModalidade">
                    <h:panelGroup layout="block"
                                  style="width: calc(100% - 30px);line-height:2.5;float: left;margin-left: 15px;"
                                  rendered="#{not empty cm.contratoModalidadeTurmaVOs}">
                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold" value="TURMAS "/>
                        <h:outputText style="margin-left: 15px;"
                                      styleClass="texto-size-14 texto-font texto-cor-cinza texto-bold"
                                      value="#{cm.nrVezesSemana}X / SEMANA"/>

                        <a4j:repeat id="modalidadeTurma" value="#{cm.contratoModalidadeTurmaVOs}" var="turma">
                            <h:panelGroup layout="block" style="margin:0 10 0 10px;width: calc(100% - 20px)">
                                <a4j:repeat value="#{turma.contratoModalidadeHorarioTurmaVOs}" var="horario">

                                    <h:panelGroup layout="block" style="width: 7%;height:3m;display: inline-block">
                                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16"
                                                      value="#{horario.horarioTurma.diaSemanaAbreviado_Apresentar} "/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  style="width: 22%;height:3m;margin-left:1%;display: inline-block;text-align: center">
                                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16"
                                                      value="#{horario.horarioTurma.horaInicial} às #{horario.horarioTurma.horaFinal} "/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  style="width: 36%;height:3m;margin-left:1%;display: inline-block">
                                        <h:outputText
                                                styleClass="texto-font texto-cor-cinza texto-size-16 texto-formato-nome"
                                                value="#{horario.horarioTurma.professor.pessoa.nomeAbreviadoMinusculo}"/>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="textoImcompleto"
                                                  style="width: 25%;vertical-align:middle;height:3m;margin-left:1%;display: inline-block;text-align: left;">
                                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16 tooltipster"
                                                      title="#{horario.horarioTurma.ambiente.descricao}"
                                                      value="#{horario.horarioTurma.ambiente.descricao}"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block"
                                                  style="width: 2%;height:3m;margin-left: 1%;text-align: left;display: inline-block">
                                        <a4j:commandLink  rendered="#{horario.horarioTurma.msgMatriculasFuturas != ''}"
                                                         styleClass="linkPadrao tooltipster"
                                                         title="#{horario.horarioTurma.msgMatriculasFuturas}">
                                                  <h:outputText
                                                    styleClass="fa-icon-exclamation-triangle-sign texto-cor-amarelo texto-size-16"/>
                                            </a4j:commandLink>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block"
                                                  style="width: 2%;height:3m;margin-left: 1%;text-align: left;display: inline-block">
                                        <a4j:commandLink action="#{ContratoControle.removerHorarioDaTurma}"
                                                         styleClass="linkPadrao tooltipster"
                                                         oncomplete="#{ContratoControle.mensagemNotificar}"
                                                         title="remover horário" reRender="pgModalidade,pgDuracaoCreditoTreinoSessao, #{ContratoControle.updateComponente.valoresContrato}">
                                            <h:outputText
                                                    styleClass="fa-icon-minus-sign texto-cor-vermelho texto-size-16"/>
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                </a4j:repeat>
                            </h:panelGroup>
                        </a4j:repeat>

                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup id="listaModalidadeSemTurma" layout="block" style="width: 100%;display: inline-block; border-top: 1px solid #A3A3A3;"
                              rendered="#{cm.modalidade.editarModalidadeSemTurma and !cm.modalidade.modalidadeTurmaParaSelecionar}" styleClass="">
                    <h:panelGroup layout="block"
                                  style="width: calc(100% - 30px);line-height:1.5;float: left;margin-left: 15px; background-color: #E5E5E5;  margin-top: 15px;margin-bottom: 15px"
                                  rendered="#{!cm.modalidade.modalidadeTurmaParaSelecionar and cm.modalidade.utilizarTurma}">

                        <h:outputText styleClass="texto-size-14 texto-font texto-cor-cinza" style=" border-top: 1px solid #DDE7E7;"
                                      value="Ainda não há turmas cadastradas/vigentes ou o aluno não enquadra na faixa etária desta modalidade."/>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </a4j:repeat>
        <script>
            carregarTooltipster();
        </script>
    </h:panelGroup>
</h:panelGroup>
