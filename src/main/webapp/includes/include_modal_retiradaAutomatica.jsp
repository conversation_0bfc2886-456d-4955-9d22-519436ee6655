<%@include file="imports.jsp" %>

<rich:modalPanel id="modalRetiradaAutomatica" width="700" autosized="true"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Retirada autom�tica de receb�veis"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkRetirar"/>
            <rich:componentControl for="modalRetiradaAutomatica" attachTo="hidelinkRetirar" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>


    <h:form id="formModalRetirada">
        <%-- ---------------- CHEQUE ----------  --%>
        <h:outputText style="font-weight: normal;"
                value="Voc� possui a permiss�o para devolver cheques/cart�es que est�o movimentados em contas do financeiro. Ser�o lan�adas retiradas nestas contas. Veja abaixo quais ser�o as contas movimentadas: "
                styleClass="tituloCampos" />

        <div style="width: 680px; max-height: 380px; overflow-x: auto; margin: 10px 0px;">
            <h:dataTable value="#{RetiradaAutomaticaControle.contas}" var="conta" width="100%" rowClasses="linhaImpar, linhaPar">
            <h:column>
                <h:panelGrid columns="1" width="100%">
                    <h:panelGroup>
                        <h:outputText value="#{conta.descricao} - Valor a ser retirado: #{conta.valorRetirarFormatado}" styleClass="tituloCamposNegrito"/>
                    </h:panelGroup>
                    <h:dataTable id="MovPagamentoCheque" headerClass="consulta"
                                 rowClasses="tablelistras textsmall" width="100%"
                                 columnClasses="centralizado, centralizado,centralizado, centralizado, centralizado,centralizado,colunaDireita"
                                 value="#{conta.chequesRetirar}" var="cheque"
                                 rendered="#{conta.mostrarCheques}">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Lote"  />
                            </f:facet>
                            <h:outputText value="#{cheque.loteVO.codigo}" styleClass="form"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Banco"  />
                            </f:facet>
                            <h:outputText id="bancoPreenchido" value="#{cheque.banco.nome}" styleClass="form"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Ag." />
                            </f:facet>
                            <h:outputText id="agencia" value="#{cheque.agencia}" styleClass="form" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Conta"/>
                            </f:facet>
                            <h:outputText id="conta" value="#{cheque.conta}" styleClass="form" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="N� Cheque"/>
                            </f:facet>
                            <h:outputText id="nDoc" value="#{cheque.numero}" styleClass="form"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Comp."/>
                            </f:facet>
                            <h:outputText value="#{cheque.dataCompensacao}">
                                <f:convertDateTime pattern="dd/MM/yyyy" />
                            </h:outputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Valor"/>
                            </f:facet>
                            <h:outputText id="valor" value="#{cheque.valor}" styleClass="form" >
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:column>
                    </h:dataTable>
                    <%-- ---------------- CARTAO DE CREDITO ----------  --%>
                    <h:dataTable id="MovPagamentoCartao" headerClass="consulta" rowClasses="tablelistras textsmall" width="100%"
                                 columnClasses="centralizado, centralizado,centralizado, centralizado, colunaDireita"
                                 value="#{conta.cartaoRetirar}"
                                 var="cartao" rendered="#{conta.mostrarCartoes}">
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Lote"  />
                            </f:facet>
                            <h:outputText value="#{cartao.lote.codigo}" styleClass="form"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                            </f:facet>
                            <h:outputText value="#{cartao.movpagamento.nomePagador}"/>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                            </f:facet>
                            <h:outputText value="#{cartao.operadora.descricao}" />
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="Comp." />
                            </f:facet>
                            <h:outputText value="#{cartao.dataCompensacao}">
                                <f:convertDateTime pattern="dd/MM/yyyy" />
                            </h:outputText>
                        </h:column>
                        <h:column>
                            <f:facet name="header">
                                <h:outputText value="#{msg_aplic.prt_Cheque_valor}" />
                            </f:facet>
                            <h:outputText rendered="#{cartao.devolverParcial}" value="Parcial: "/>
                            <h:outputText rendered="#{cartao.devolverParcial}" value="#{cartao.valorParcialDevolver}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                            <h:outputText rendered="#{!cartao.devolverParcial}" value="#{cartao.valor}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:column>
                    </h:dataTable>
                    
                </h:panelGrid>
                </h:column>
        </h:dataTable>
       </div>
        <center>
            <a4j:commandLink id="confirmarRetirada"
                               styleClass="pure-button pure-button-primary"
                               action="#{RetiradaAutomaticaControle.confirmar}"
                               oncomplete="#{RetiradaAutomaticaControle.msgAlert}"
                               reRender="formModalLanc, panelAutorizacaoFuncionalidade">
                <h:outputText style="font-size: 14px"
                              value="Confirmar"/>
            </a4j:commandLink>
        </center>

    </h:form>
</rich:modalPanel>