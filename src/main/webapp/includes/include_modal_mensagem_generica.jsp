<%@include file="imports.jsp" %>

<rich:modalPanel id="mdlMensagemGenerica" styleClass="novaModal" width="450" autosized="true" shadowOpacity="true"
                 showWhenRendered="#{MensagemGenericaControle.apresentarModalMensagemGenerica}"
                 onhide="document.getElementById('formMdlMensagemGenerica:botaoFecharHide').click()">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{MensagemGenericaControle.titulo}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkMensagemGenerica" rendered="#{MensagemGenericaControle.mostrarBotaoFechar}"/>
            <rich:componentControl for="mdlMensagemGenerica" attachTo="hidelinkMensagemGenerica" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form prependId="true" id="formMdlMensagemGenerica">
        <a4j:commandButton style="display : none;" status="false" id="botaoFecharHide"
                           reRender="#{MensagemGenericaControle.reRenderComponents}"
                           action="#{MensagemGenericaControle.fechar}"
                           oncomplete="#{MensagemGenericaControle.onCompleteBotaoFechar}"/>
        <h:panelGroup>
            <h:panelGroup styleClass="margin-box">
                <h:panelGroup id="mensagemApresentar" layout="block" styleClass="texto-size-16-real texto-font texto-cor-cinza">
                    ${MensagemGenericaControle.mensagemApresentar}
                </h:panelGroup>
            </h:panelGroup>
            <h:outputText id="msgDetalhada"
                          value="#{MensagemGenericaControle.mensagemDetalhada}"/>
            <h:panelGroup layout="block" styleClass="container-botoes">
                <h:panelGroup rendered="#{MensagemGenericaControle.labelBotaoFecharTela == null}" styleClass="margin-box">
                    <a4j:commandLink action="#{MensagemGenericaControle.invokeBotaoSim}" value="Sim"
                                       id="sim"
                                       styleClass="botaoPrimario texto-size-16-real"
                                       oncomplete="#{MensagemGenericaControle.mensagemNotificar}; #{MensagemGenericaControle.onCompleteBotaoSim};Richfaces.hideModalPanel('mdlMensagemGenerica');"
                                       reRender="mdlMensagemGenerica, #{MensagemGenericaControle.reRenderComponents}"/>
                    <rich:spacer width="30px;"/>
                    <a4j:commandLink oncomplete="#{MensagemGenericaControle.onCompleteBotaoNao};Richfaces.hideModalPanel('mdlMensagemGenerica');"
                                       id="nao"
                                       styleClass="botaoSecundario texto-size-16-real"
                                       action="#{MensagemGenericaControle.invokeBotaoNao}"
                                       value="N�o"
                                       reRender="#{MensagemGenericaControle.reRenderComponents}"/>
                </h:panelGroup>
                <a4j:commandLink value="#{MensagemGenericaControle.labelBotaoFecharTela}"
                                 id="btnFecharModalGenerico"
                                   rendered="#{MensagemGenericaControle.labelBotaoFecharTela != null}"
                                 styleClass="botaoPrimario texto-size-16-real"
                                   oncomplete="#{MensagemGenericaControle.onCompleteBotaoFechar};Richfaces.hideModalPanel('mdlMensagemGenerica');"
                                   reRender="#{MensagemGenericaControle.reRenderComponents}"/>
            </h:panelGroup>
            <br/>
            <br/>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
