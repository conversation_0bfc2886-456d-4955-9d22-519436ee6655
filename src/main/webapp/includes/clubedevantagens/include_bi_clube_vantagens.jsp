<%--
  Created by IntelliJ IDEA.
  User: luiz
  Date: 11/12/2015
  To change this template use File | Settings | File Templates.
--%>
<%@include file="../imports.jsp" %>
<%@page pageEncoding="ISO-8859-1" %>

<h:panelGrid columns="2" >
    <%--AGENDAMENTO DE LIGAÇÃO PENDENTE--%>
    <h:panelGroup layout="block" id="agendamentoLigacaoPendente" styleClass="paginaFontResponsiva">
        <h:panelGroup layout="block" >
            <h:outputText styleClass="tituloBICRM" value="Totalizador de pontos"
                          escape="false"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{ClubeVantagensControle.linkWiki}"
                          title="Clique e saiba mais: Totalizador de pontos"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block">
            <rich:dataTable id="tblTotalPontos" value="#{ClubeVantagensControle.listaTotalizadorCategoriaPontos}" var="pontos">
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Categoria" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{pontos.tipoItemCampanha.descricao}" />
                </rich:column>
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Pontos" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{pontos.pontos}" />
                </rich:column>
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="%" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{(pontos.pontos*100)/ClubeVantagensControle.totalPontos}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>

                </rich:column>
                <f:facet name="footer">
                    <h:outputText value="Total #{ClubeVantagensControle.totalPontos}"/>
                </f:facet>
            </rich:dataTable>
        </h:panelGroup>
    </h:panelGroup>

    <%--INDICAÇÕES SEM CONTATO--%>
    <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="indIndicacoes">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">
            <h:outputText styleClass="tituloBICRM" value="Resgate de Brindes"
                          escape="false"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{ClubeVantagensControle.linkWiki}"
                          title="Clique e saiba mais: Resgate de Brindes"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block">
            <rich:dataTable id="tblTotalBrinde" value="#{ClubeVantagensControle.listaTotalizadorBrindes}"  var="brinde">
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Brinde" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{brinde.descricao}" />
                </rich:column>
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Quantidade" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{brinde.pontos}" />
                </rich:column>
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="%" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{(brinde.pontos*100)/ClubeVantagensControle.totalBrindes}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>

                </rich:column>
                <f:facet name="footer">
                    <h:outputText value="Total #{ClubeVantagensControle.totalBrindes}"/>
                </f:facet>
            </rich:dataTable>
        </h:panelGroup>

    </h:panelGroup>
    <%--CONTATO RECEPTIVO--%>
    <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="indReceptivo">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">
            <h:outputText styleClass="tituloBICRM" value="Campanhas Ativas"
                          escape="false"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{ClubeVantagensControle.linkWiki}"
                          title="Clique e saiba mais: Campanhas Ativas"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block">
            <rich:dataTable id="tblTotalCampanha" value="#{ClubeVantagensControle.listaTotalizadorCampanhas}"  var="campanha">
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Brinde" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{campanha.descricao}" />
                </rich:column>
                <rich:column >
                    <f:facet name="header">
                        <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza texto-bold" value="Pontos acumulados" />
                    </f:facet>
                    <h:outputText  styleClass="texto-size-14 texto-cor-cinza texto-font" value="#{campanha.multiplicador}"/>
                </rich:column>
            </rich:dataTable>
        </h:panelGroup>

    </h:panelGroup>

    <%-- CLIENTES COM OBJEÇÃO DEFINITIVA --%>
    <h:panelGroup layout="block" styleClass="container-bi-crm-indicadores" id="indObjecoes">

        <h:panelGroup layout="block" styleClass="bi-crm-box-header-indicadores">


            <h:outputText styleClass="tituloBICRM" value="Alunos que não resgataram brindes"
                          escape="false"/>
            <h:outputLink styleClass="linkWiki"
                          value="#{ClubeVantagensControle.linkWiki}"
                          title="Clique e saiba mais: Alunos que não resgataram brindes"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup >
            <a4j:commandLink
                    id="abrirListaCli"
                    styleClass="indicadoresBICRM"
                    value="#{fn:length(ClubeVantagensControle.listaTotalizadorAlunoSemResgate)}"/>
        </h:panelGroup>
    </h:panelGroup>

</h:panelGrid>

