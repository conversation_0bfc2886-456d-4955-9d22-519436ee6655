<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>

<h:panelGrid width="100%" columns="1" id="panelCadastroMerchantFacilitePay">

    <%--CADASTRAR NOVO--%>
    <h:panelGrid id="panelCadastrarMerchantFacilitePay"
                 rendered="#{!EmpresaControle.possuiMerchantFacilitePay && !EmpresaControle.editandoMerchantFacilitePay}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Realizar Integração com a Fypay"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos tooltipster"
                      value="Ambiente:"
                      title="Informe o ambiente para o cadastro do Merchant na integração com a Fypay"/>
        <h:outputText
                rendered="#{!(SuperControle.ambienteDesenvolvimentoTeste && LoginControle.usuarioLogado.usuarioPactoSolucoes)}"
                value="#{EmpresaControle.ambienteFacilitePay.descricao}"/>
        <h:selectOneMenu id="listaSelectItemAmbienteFacilite" styleClass="form"
                         rendered="#{SuperControle.ambienteDesenvolvimentoTeste && LoginControle.usuarioLogado.usuarioPactoSolucoes}"
                         value="#{EmpresaControle.ambienteFacilitePay}">
            <f:selectItems value="#{EmpresaControle.listaSelectItemAmbiente}"/>
        </h:selectOneMenu>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o nome da empresa para o cadastro do Merchant na integração com a Fypay"
                      value="Nome da empresa:"/>
        <h:inputText value="#{EmpresaControle.nomeEmpresaFacilitePay}" size="40"/>

        <h:outputText styleClass="tituloCampos"
                      value="CNPJ da empresa:"/>
        <h:inputText size="20" maxlength="18" id="cnpjFacilitePay"
                     onkeypress="return mascara(this.form, 'form:cnpjFacilitePay', '99.999.999/9999-99', event);"
                     onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                     value="#{EmpresaControle.cnpjFacilitePay}"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o telefone da empresa para o cadastro do Merchant na integração com a Fypay"
                      value="Telefone:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.dddTelefoneFacilitePay}" size="2" maxlength="2"/>
            <h:inputText value="#{EmpresaControle.telefoneFacilitePay }" size="10" maxlength="9"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o usuário para o cadastro do Merchant na integração com a Fypay. Informar um email. A senha será enviada para este email. <br> Este usuário é o que poderá acessar depois o portal da Fypay caso necessário"
                      value="Usuário do portal (email):"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.emailUserFacilitePay}" size="40"
                         styleClass="tooltipster"
                         title="Informe o usuário para o cadastro do Merchant na integração com a Fypay. Informar um email. A senha será enviada para este email. <br> Este usuário é o que poderá acessar depois o portal da Fypay caso necessário">
                <a4j:support event="onchange"
                             reRender="form"/>
            </h:inputText>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe o nome da pessoa do usuário para o cadastro do Usuário na integração com a Fypay"
                      value="Nome do usuário (Portal Fypay):"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.nomeUserFacilitePay}"
                         title="Informe o nome da pessoa do usuário para o cadastro do Usuário na integração com a Fypay"
                         styleClass="tooltipster" size="40"/>
        </h:panelGroup>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Informe uma conta bancária ao qual os recebimentos irão cair lá na Fypay quando compensados."
                      value="Conta para recebimento:"/>
        <h:selectOneMenu id="contaEmpresaFacilitePay" styleClass="form tooltipster" onblur="blurinput(this);"
                         title="Informe uma conta bancária ao qual os recebimentos irão cair lá na Fypay quando compensados."
                         value="#{EmpresaControle.contaCorrenteFacilitePay}"
                         onfocus="focusinput(this);">
            <f:selectItems value="#{EmpresaControle.listaSelectItemContaEmpresa}"/>
        </h:selectOneMenu>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Este email aqui é criado com o padrão '...@fypay.com.br', mas não se preocupe, o login no portal da Fypay será realizado com o email que você preencher lá no campo Usuário do Portal (Email)."
                      value="Email da empresa:"/>
        <h:outputText value="#{EmpresaControle.emailFacilitePay}"
                      styleClass="tooltipster"
                      title="Este email aqui é criado com o padrão '...@fypay.com.br', mas não se preocupe, o login no portal da Fypay será realizado com o email que você preencher lá no campo Usuário do Portal (Email)."/>

        <h:outputText value=""/>
        <h:panelGroup layout="block" style="padding: 10px 0 10px">
            <a4j:commandLink value="Cadastrar"
                             style="margin: 0"
                             id="integrarFacilitePay"
                             styleClass="botoes nvoBt btSec tooltipster"
                             title="Cadastrar Merchant na Fypay e iniciar Integração"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.cadastrarMerchantFacilitePay}"
                             reRender="panelCadastroMerchantFacilitePay"/>
        </h:panelGroup>
    </h:panelGrid>


    <%--EXIBIR CAMPOS QUANDO POSSUI INTEGRAÇÃO--%>
    <h:panelGrid id="panelFacilitePayCadastrado"
                 rendered="#{EmpresaControle.possuiMerchantFacilitePay && !EmpresaControle.editandoMerchantFacilitePay && !EmpresaControle.editandoContaBancariaFacilitePay}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Dados da Integração Fypay (Merchant)"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Ambiente informado anteriormente no ato da integração com a Fypay"
                      value="Ambiente:"/>
        <h:outputText value="#{EmpresaControle.ambienteFacilitePay.descricao}"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="CNPJ da empresa informado anteriormente no ato da integração com a Fypay"
                      value="CNPJ:"/>
        <h:outputText value="#{EmpresaControle.merchantFacilitePayDto.cnpj}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Email da empresa informado anteriormente no ato da integração com a Fypay.<br>Este email é fictício, no momento você não precisa fazer nada com este email."
                      value="Email da empresa:"/>
        <h:outputText value="#{EmpresaControle.merchantFacilitePayDto.email}"
                      title="Email da empresa informado anteriormente no ato da integração com a Fypay.<br>Este email é fictício, no momento você não precisa fazer nada com este email."
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Nome da empresa informado anteriormente no ato da integração com a Fypay"
                      value="Nome do Merchant:"/>
        <h:outputText value="#{EmpresaControle.merchantFacilitePayDto.companyName}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Telefone informado anteriormente no ato da integração com a Fypay"
                      value="Telefone:"/>
        <h:outputText value="#{EmpresaControle.merchantFacilitePayDto.phoneNumber}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Este é o código Merchant_Id gerado no ato da integração com a Fypay"
                      value="Merchant_id:"/>
        <h:outputText id="MerchantIdFacilitePay"
                      value="#{EmpresaControle.merchantIdFacilitePay}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText value="Conta para Recebimento:"
                      title="Esta é a conta que está sendo realizada os recebimentos lá no portal da Fypay. É a conta informada no ato da integração. Caso queira alterar, clique no botão 'Alterar'"
                      styleClass="tituloCampos tooltipster"/>

        <h:panelGrid columns="2">
            <h:outputText value="#{EmpresaControle.contaBancariaFacilitePay}"
                          title="Esta é a conta que está sendo realizada os recebimentos lá no portal da Fypay. É a conta informada no ato da integração. Caso queira alterar, clique no botão 'Alterar'"
                          styleClass="tituloCampos tooltipster"/>
            <%--BOTÃO ALTERAR CONTA BANCÁRIA--%>
            <a4j:commandLink value="Alterar"
                             id="editarContaBancariaFaci"
                             styleClass="tooltipster"
                             title="Alterar Conta Bancária de recebimentos do Merchant na Fypay"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.editandoContaFacilitePay}"
                             reRender="panelFacilitePayAlterar, panelFacilitePayCadastrado, panelFacilitePayBotoes, form"/>
        </h:panelGrid>
        <h:outputText styleClass="tituloCampos tooltipster"
                      title="Este é o usuário (email do usuário) informado anteriormente no ato da integração com a Fypay. Utilizado para acessar ao portal da Fypay caso desejado.
                            <br>Note que para fazer o login você também precisará informar o 'portaluser_' antes do seu email."
                      value="Usuário do portal Fypay:"/>
        <h:outputText value="#{EmpresaControle.emailUserFacilitePay}"
                      title="Este é o usuário (email do usuário) informado anteriormente no ato da integração com a Fypay. Utilizado para acessar ao portal da Fypay caso desejado.
                            <br>Note que para fazer o login você também precisará informar o 'portaluser_' antes do seu email."
                      styleClass="tooltipster"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos tooltipster"
                      title="A senha do usuário dá acesso ao portal da Fypay caso desejado. A senha é enviada para o email do usuário informado no campo anterior a este. caso não tenha recebido, entre em contato com a Pacto"
                      value="Senha:"/>
        <h:outputText value="#{EmpresaControle.textoSenhaPagoLivreOuFacilite}"
                      styleClass="tooltipster"
                      title="A senha do usuário dá acesso ao portal da Fypay caso desejado. A senha é enviada para o email do usuário informado no campo anterior a este. caso não tenha recebido, entre em contato com a Pacto"
                      style="font-size: 14px; padding: 5px;"/>
    </h:panelGrid>

    <%--BOTÕES GERAIS--%>
    <h:panelGroup id="panelFacilitePayBotoes"
                  rendered="#{EmpresaControle.possuiMerchantFacilitePay && !EmpresaControle.editandoMerchantFacilitePay && !EmpresaControle.editandoContaBancariaFacilitePay}">

        <div style="margin-left: 35%; margin-top: 10px;">
            <a4j:commandLink value="Editar"
                             id="editarFacilitePay"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.editandoMerchantFacilite}"
                             reRender="panelFacilitePayAlterar, panelFacilitePayCadastrado, panelFacilitePayBotoes, form"/>
        </div>
    </h:panelGroup>

    <%--EDITAR--%>
    <h:panelGrid id="panelFacilitePayAlterar"
                 rendered="#{EmpresaControle.editandoMerchantFacilitePay}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Dados do Merchant Fypay para atualização"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos" value="Merchant_id:"/>
        <h:outputText id="MerchantIdFacilitePayAlterar"
                      value="#{EmpresaControle.merchantIdFacilitePay}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText styleClass="tituloCampos" value="Email da empresa:"/>
        <h:inputText value="#{EmpresaControle.emailFacilitePay}" size="40"/>

        <h:outputText styleClass="tituloCampos" value="Nome da empresa:"/>
        <h:inputText value="#{EmpresaControle.nomeEmpresaFacilitePay}" size="40"/>

        <h:outputText styleClass="tituloCampos" value="Telefone:"/>
        <h:panelGroup layout="block">
            <h:inputText value="#{EmpresaControle.dddTelefoneFacilitePay}" size="2" maxlength="2"/>
            <h:inputText value="#{EmpresaControle.telefoneFacilitePay }" size="10" maxlength="9"/>
        </h:panelGroup>
    </h:panelGrid>


    <%--ALTERAR SOMENTE CONTA BANCÁRIA--%>
    <h:panelGrid id="panelFacilitePayAlterarConta"
                 rendered="#{EmpresaControle.editandoContaBancariaFacilitePay}"
                 columns="2" rowClasses="linhaImpar, linhaPar"
                 columnClasses="classEsquerda, classDireita" width="100%"
                 headerClass="subordinado">

        <f:facet name="header">
            <h:outputText value="Alterar Conta Bancária de recebimento Fypay"/>
        </f:facet>

        <h:outputText styleClass="tituloCampos" value="Merchant_id:"/>
        <h:outputText id="ContaBancariaFacilitePayAlterar"
                      value="#{EmpresaControle.merchantIdFacilitePay}"
                      style="font-size: 14px; padding: 5px;"/>

        <h:outputText value="Conta para Recebimento:"
                      title="Selecione a nova conta para recebimento e clique no botão 'Gravar'"
                      styleClass="tituloCampos tooltipster"/>
        <h:selectOneMenu id="contaEmpresaFacilitePayEditar" styleClass="tituloCampos"
                         value="#{EmpresaControle.contaCorrenteFacilitePay}">
            <f:selectItems value="#{EmpresaControle.listaSelectItemContaEmpresa}"/>
        </h:selectOneMenu>
    </h:panelGrid>


    <%--BOTÕES EDITAR MERCHANT--%>
    <h:panelGroup id="panelFacilitePayBotoesAlterar"
                  rendered="#{EmpresaControle.editandoMerchantFacilitePay}">

        <div style="margin-left: 35%; margin-top: 10px;">
            <a4j:commandLink value="Gravar"
                             id="alterarMerchantFacilite"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.alterarMerchantFacilitePay}"
                             reRender="panelCadastroMerchantFacilitePay"/>

            <a4j:commandLink value="Voltar"
                             id="voltarFacilite"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.carregarDadosMerchantFacilitePay}"
                             reRender="panelFacilitePayAlterar, panelFacilitePayCadastrado, panelFacilitePayBotoes, form"/>
        </div>
    </h:panelGroup>

    <%--BOTÕES EDITAR CONTA BANCÁRIA--%>
    <h:panelGroup id="panelFacilitePayBotoesAlterarContaBancaria"
                  rendered="#{EmpresaControle.editandoContaBancariaFacilitePay}">
        <div style="margin-left: 35%; margin-top: 10px;">
            <a4j:commandLink value="Gravar"
                             id="alterarContaMerchantFacilitePay"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.alterarContaBancariaMerchantFacilitePay}"
                             reRender="panelCadastroMerchantFacilitePay"/>

            <a4j:commandLink value="Voltar"
                             id="voltarPrincipalFacilitePay"
                             styleClass="botoes nvoBt btSec"
                             oncomplete="#{EmpresaControle.mensagemNotificar}"
                             action="#{EmpresaControle.carregarDadosMerchantFacilitePay}"
                             reRender="panelFacilitePayBotoesAlterarContaBancaria, panelFacilitePayCadastrado, panelFacilitePayBotoes, form"/>
        </div>
    </h:panelGroup>
</h:panelGrid>
