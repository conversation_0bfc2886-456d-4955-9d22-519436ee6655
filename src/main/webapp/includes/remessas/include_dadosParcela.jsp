<%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>

<rich:column sortBy="#{parc.pessoa.nome}">
    <f:facet name="header">
        <h:outputText title="Nome da Pessoa" value="Pessoa"/>
    </f:facet>

    <a4j:commandLink style="#{parc.corRemessa}" value="#{parc.pessoa.nome}"
                     oncomplete="abrirPopup('clienteNav.jsp?page=viewCliente', 'ClienteGestaoRemessa', 1024, 700);"
                     actionListener="#{ClienteControle.atualizarCliente}" action="#{ClienteControle.acaoAjax}">
        <f:attribute name="pessoa" value="#{parc.pessoa}"/>
    </a4j:commandLink>
</rich:column>

<rich:column sortBy="#{parc.empresa.codigo}">
    <f:facet name="header">
        <h:outputText title="Cod. Empresa" value="E."/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.empresa.codigo}"/>
</rich:column>

<rich:column sortBy="#{parc.contrato.codigo}">
    <f:facet name="header">
        <h:outputText title="Contrato" value="Ct."/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.contrato.codigo}"/>
</rich:column>

<rich:column sortBy="#{parc.codigo}">
    <f:facet name="header">
        <h:outputText title="C�digo Parcela" value="C."/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.codigo}"/>
</rich:column>

<rich:column sortBy="#{parc.nrTentativas}">
    <f:facet name="header">
        <h:outputText title="N�mero de Tentativas" value="N.T"/>
    </f:facet>
    <h:outputText title="#{parc.nrTentativas} tentativa(s)"
                  style="font-size: 9px;#{parc.corRemessa}" value="#{parc.nrTentativas}"/>
</rich:column>
<rich:column width="30" sortBy="#{parc.itemRemessa.codigoStatus}">
    <f:facet name="header">
        <h:outputText value="St." title="�ltimo Status"/>
    </f:facet>
    <h:outputText rendered="#{parc.itemRemessa.codigo > 0}" style="#{parc.corRemessa}"
                  value="#{parc.itemRemessa.codigoStatus}" title="\"#{parc.itemRemessa.descricaoStatus}\""/>
</rich:column>
<rich:column sortBy="#{parc.descricao}">
    <f:facet name="header">
        <h:outputText title="Descri��o parcela" value="Descri��o"/>
    </f:facet>
    <h:outputText title="#{parc.nrTentativas > 0 ? 'Parcela de Repescagem' : 'Parcela Em Aberto'}"
                  style="font-size: 9px;#{parc.corRemessa}" value="#{parc.descricao}"/>
</rich:column>


<rich:column sortBy="#{parc.dataVencimento}">
    <f:facet name="header">
        <h:outputText title="Data Cobran�a Parcela" value="Data Cobran�a"/>
    </f:facet>
    <h:outputText style="font-size: 9px;#{parc.corRemessa}" value="#{parc.dataCobranca}">
        <f:convertDateTime timeStyle="medium" pattern="dd/MM/yyyy"/>
    </h:outputText>
</rich:column>

<rich:column sortBy="#{parc.dataEnvioApresentar}">
    <f:facet name="header">
        <h:outputText title="Data Envio Remessa" value="Envio"/>
    </f:facet>
    <h:outputText style="font-size: 9px;#{parc.corRemessa}" value="#{parc.dataEnvioApresentar}">
    </h:outputText>
</rich:column>

<rich:column sortBy="#{parc.valorParcela}">
    <f:facet name="header">
        <h:outputText title="Valor da Parcela" value="Valor Parcela"/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.valorParcelaNumerico}"/>
</rich:column>

<rich:column sortBy="#{parc.valorMultaJuros}">
    <f:facet name="header">
        <h:outputText title="Valor Multa/Juros" value="Multa/Juros"/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.valorMultaJuros_Apresentar}"/>
</rich:column>

<rich:column sortBy="#{parc.valorCobranca}">
    <f:facet name="header">
        <h:outputText title="Valor Cobran�a" value="Valor Cobran�a"/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.valorCobranca_Apresentar}"/>
</rich:column>

<rich:column rendered="#{GestaoRemessasControle.buscarTodasEmpresas}" sortBy="#{parc.empresa_Apresentar}">
    <f:facet name="header">
        <h:outputText title="Empresa" value="Empresa"/>
    </f:facet>
    <h:outputText style="#{parc.corRemessa}" value="#{parc.empresa_Apresentar}"/>
</rich:column>

<rich:column>
    <f:facet name="header">
        <h:outputText value="A��es"/>
    </f:facet>
    <a4j:commandButton id="btnRealizarContatoCielo"
                       image="/imagens/cielo.png"
                       style="padding: 2px;"
                       reRender="panelDadosRemessaContatoCielo"
                       title="Realizar contato Cielo"
                       action="#{GestaoRemessasControle.abrirContatoCielo}" ajaxSingle="true"
                       oncomplete="Richfaces.showModalPanel('panelDadosRemessaContatoCielo');"
                       rendered="#{parc.acao != null && parc.acao.contatoCielo}"/>

    <a4j:commandButton id="btnRealizarContato"
                       image="/imagensCRM/icontelefonista.png"
                       style="padding: 2px;"
                       title="Realizar contato"
                       action="#{GestaoRemessasControle.abrirContatoCliente}" ajaxSingle="true"
                       oncomplete="#{GestaoRemessasControle.msgAlert}"
                       rendered="#{parc.acao != null && parc.acao.contatoCliente}"/>

    <a4j:commandButton actionListener="#{AutorizacaoCobrancaControle.abrirAutorizacoes}"
                       style="padding: 2px;" title="Trocar cart�o"
                       action="#{GestaoRemessasControle.abrirModalAutorizacoes}"
                       reRender="panelDadosAutorizacoes"
                       styleClass="rich-fileupload-button rich-fileupload-font"
                       onmouseover="this.className='rich-fileupload-button-light'"
                       onmouseout="this.className='rich-fileupload-button rich-fileupload-font'"
                       image="../images/creditcards.png">
        <f:attribute name="pessoa" value="#{parc.pessoa}"/>
    </a4j:commandButton>

    <a4j:commandButton id="btnRealizarReagendarParcela"
                       image="/imagens/estudio/m_agenda.png"
                       style="padding: 2px;"
                       title="Reagendar envio"
                       action="#{AlterarVencimentoParcelasControle.alterarParcelaEspecifica}" ajaxSingle="true"
                       oncomplete="#{AlterarVencimentoParcelasControle.msgAlert}"
                       reRender="modalAlterarVencimento"
                       rendered="#{parc.acao != null && parc.acao.reenvio}"/>

    <a4j:commandButton id="btnRealizarBloqueio"
                       image="/imagens/block.png"
                       style="padding: 2px;"
                       title="Lan�ar bloqueio na catraca"
                       reRender="panelAutorizacaoFuncionalidade"
                       oncomplete="#{GestaoRemessasControle.msgAlert}"
                       action="#{GestaoRemessasControle.lancarBloqueio}"
                       ajaxSingle="true"
                       rendered="#{parc.acao != null && parc.acao.bloqueio && !parc.acessoBloqueado}"/>
    <a4j:commandButton id="btnRealizarLiberar"
                       image="/imagens/free.png"
                       style="padding: 2px;"
                       title="Desbloquear na catraca"
                       reRender="panelAutorizacaoFuncionalidade"
                       oncomplete="#{GestaoRemessasControle.msgAlert}"
                       action="#{GestaoRemessasControle.lancarDesbloqueio}"
                       ajaxSingle="true"
                       rendered="#{parc.acao != null && parc.acao.bloqueio && parc.acessoBloqueado}">
        <f:attribute name="pessoa" value="#{parc.pessoa}"/>
    </a4j:commandButton>

    <a4j:commandButton style="padding: 2px;" title="Receber Parcela Agora"
                       value="Receber"
                       image="../images/1447093236_money.png"
                       action="#{GestaoRemessasControle.receberParcela}"/>
</rich:column>