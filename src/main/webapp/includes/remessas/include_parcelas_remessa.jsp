<%-- 
    Document   : include_parcelas_remessa
    Created on : 17/12/2012, 16:46:20
    Author     : waller
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../imports.jsp" %>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<a4j:outputPanel>

    <style>
        .rich-fileupload-list-decor{
            border:none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-toolbar-decor{
            background-color: transparent;
            border:none;
        }
        .rich-fileupload-table-td{
            border: none;
        }

        .rich-panel{
            background-color: transparent;
            border:none;
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-panel-body {
            padding: 0 0 0 0;
            margin: 0 0 0 0;
        }

        .rich-fileupload-ico-add {
            background-image: url(images/drive-upload.png);
        }
        .rich-fileupload-button{
            background-color: transparent;
            background-image: none;
        }
        .rich-fileupload-button-border{
            border: none;
        }
        .rich-fileupload-button-light, .rich-fileupload-button-press{
            background-image: none;
            background-color: transparent;
        }
        .alinhar{
            vertical-align: top !important;
        }
        
        .rich-datascr-act{
            font-size: 17px;
        }
    </style>

    <h:panelGrid columns="1" columnClasses="w50 alinhar,w50 alinhar" width="100%" rowClasses="margin-v-10"
                 styleClass="forcar-v-margin">
        <!--  ----------  CONTATO CLIENTE---------------------->
        <rich:panel rendered="#{fn:length(GestaoRemessasControle.agrupamentoParcelas.parcelasContato) > 0}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda, direita, direita" width="100%">
                    <h:outputText  value="#{msg['CONTATO']}" style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup>
                        <h:outputText  value="Total Itens: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.totalContato} " styleClass="text"/>
                        <h:outputText  value="Total Valores: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.valorContato}" styleClass="text">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink id="btnExcel1"
                                             title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportarContato}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <i class="fa-icon-table" ></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF1"
                                             title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportarContato}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>
                                

                                <i class="fa-icon-print" ></i>
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable rowKeyVar="status" id="tblParcelasRemessaContato" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.agrupamentoParcelas.parcelasContato}"
                            rows="#{DataScrollerControle.nrRows}" var="parc">
                <%@include file="/includes/remessas/include_dadosParcela.jsp" %>
            </rich:dataTable>
            <rich:datascroller for="tblParcelasRemessaContato" status="false" renderIfSinglePage="false"/>
            <br/>
        </rich:panel>

        <!--  ----------  TROCAR CARTÃO CLIENTE---------------------->
        <rich:panel rendered="#{fn:length(GestaoRemessasControle.agrupamentoParcelas.parcelasTrocarCartao) > 0}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda, direita,direita" width="100%">
                    <h:outputText  value="#{msg['TROCAR_CARTAO']}" style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup>
                        <h:outputText  value="Total Itens: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.totalTrocarCartao} " styleClass="text"/>
                        <h:outputText  value="Total Valores: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.valorTrocarCartao}" styleClass="text">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink id="btnExcel2"
                                             title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportarTrocar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <i class="fa-icon-table" ></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF2" title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportarTrocar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>

                                
                                <i class="fa-icon-print" ></i>
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable rowKeyVar="status" id="tblParcelasRemessaTrocar" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.agrupamentoParcelas.parcelasTrocarCartao}"
                            rows="#{DataScrollerControle.nrRows}" var="parc">
                <%@include file="/includes/remessas/include_dadosParcela.jsp" %>
                <rich:column>
                    <f:facet name="header">
                        <h:selectBooleanCheckbox  title="Marcar/desmarcar todas as parcelas"
                                                  value="#{GestaoRemessasControle.marcarTodosTrocarCartao}">

                            <a4j:support action="#{GestaoRemessasControle.marcarDesmarcarTrocarCartao}"
                                         event="onclick" reRender="panelConteudo,panelTotaisParcelas"/>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox  title="Marcar/desmarcar esta parcela"
                                              value="#{parc.parcelaEscolhida}">
                        <a4j:support event="onclick" reRender="tblParcelasRemessa,panelTotaisParcelas" 
                                     action="#{GestaoRemessasControle.totalizar}"
                                     status="false"/>
                    </h:selectBooleanCheckbox>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="tblParcelasRemessaTrocar" status="false" renderIfSinglePage="false"/>
            <h:panelGroup style="position: block; ">
                <a4j:commandLink value="Bloquear" styleClass="pure-button"
                                 title="Bloquear acesso na catraca dos alunos selecionados"
                                 reRender="panelAutorizacaoFuncionalidade"
                                 oncomplete="#{GestaoRemessasControle.msgAlert}"
                                 action="#{GestaoRemessasControle.lancarBloqueios}"
                                 ajaxSingle="true"></a4j:commandLink>
                <a4j:commandLink value="Desbloquear" styleClass="pure-button margin-h-10 margin-v-10"
                                 title="Desbloquear acesso na catraca dos alunos selecionados"
                                 reRender="panelAutorizacaoFuncionalidade"
                                 oncomplete="#{GestaoRemessasControle.msgAlert}"
                                 action="#{GestaoRemessasControle.lancarDesbloqueios}"
                                 ajaxSingle="true"></a4j:commandLink>
            </h:panelGroup>
            <br/>
        </rich:panel>
        <!--  ----------  CONTATO CIELO---------------------->
        <rich:panel rendered="#{fn:length(GestaoRemessasControle.agrupamentoParcelas.parcelasContatoCielo) > 0}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda, direita,direita" width="100%">
                    <h:outputText  value="#{msg['REALIZAR_CONTATO_CIELO']}" style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup>
                        <h:outputText  value="Total Itens: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.totalContatoCielo} " styleClass="text"/>
                        <h:outputText  value="Total Valores: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.valorContatoCielo}" styleClass="text">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink id="btnExcel3"
                                             title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportarContatoCielo}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <i class="fa-icon-table" ></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF3" title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportarContatoCielo}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>
                                <i class="fa-icon-print" ></i> 
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable rowKeyVar="status" id="tblParcelasContatoCielo" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.agrupamentoParcelas.parcelasContatoCielo}"
                            rows="#{DataScrollerControle.nrRows}" var="parc">
                <%@include file="/includes/remessas/include_dadosParcela.jsp" %>
                <rich:column>
                    <f:facet name="header">
                        <h:selectBooleanCheckbox  title="Marcar/desmarcar todas as parcelas"
                                                  value="#{GestaoRemessasControle.marcarTodosCielo}">

                            <a4j:support action="#{GestaoRemessasControle.marcarDesmarcarCielo}"
                                         event="onclick" reRender="panelConteudo,panelTotaisParcelas, criarRemessaCielo"/>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox  title="Marcar/desmarcar esta parcela"
                                              value="#{parc.parcelaEscolhida}">
                        <a4j:support event="onclick" reRender="tblParcelasRemessa,panelTotaisParcelas, criarRemessaCielo"
                                     action="#{GestaoRemessasControle.totalizar}"
                                     status="false"/>
                    </h:selectBooleanCheckbox>
                </rich:column>
            </rich:dataTable>
            <rich:datascroller for="tblParcelasContatoCielo" status="false" renderIfSinglePage="false"/>
            <a4j:commandLink id="criarRemessaCielo" styleClass="pure-button margin-v-10"
                             process="tblParcelasContatoCielo"
                             oncomplete="Richfaces.showModalPanel('mdlCriarRemessaCielo');"
                             reRender="panelConteudo, formmdlCriarRemessaCielo"
                             style="right:20px;" value="Nova Remessa"/>
            <br/>
        </rich:panel>
        <!--  ----------  REENVIO---------------------->

        <rich:panel rendered="#{fn:length(GestaoRemessasControle.agrupamentoParcelas.parcelasReenvio) > 0}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda, direita, direita" width="100%">
                    <h:outputText  value="#{msg['REALIZAR_REENVIO']}" style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup>
                        <h:outputText  value="Total Itens: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.totalReenvio} " styleClass="text"/>
                        <h:outputText  value="Total Valores: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.valorReenvio}" styleClass="text">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink id="btnExcel4"
                                             title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportarReenvio}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <i class="fa-icon-table" ></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF4" title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportarReenvio}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>
                                <i class="fa-icon-print" ></i>
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable rowKeyVar="status" id="tblParcelasReenvio" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.agrupamentoParcelas.parcelasReenvio}"
                            rows="#{DataScrollerControle.nrRows}" var="parc">
                <%@include file="/includes/remessas/include_dadosParcela.jsp" %>

                <rich:column>
                    <f:facet name="header">
                        <h:selectBooleanCheckbox  title="Marcar/desmarcar todas as parcelas"
                                                  value="#{GestaoRemessasControle.marcarTodosReenvio}">

                            <a4j:support action="#{GestaoRemessasControle.marcarDesmarcarReenvio}"
                                         event="onclick" reRender="panelConteudo,panelTotaisParcelas, criarRemessaReenvio"/>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox  title="Marcar/desmarcar esta parcela"
                                              value="#{parc.parcelaEscolhida}">
                        <a4j:support event="onclick" reRender="tblParcelasRemessa,panelTotaisParcelas, criarRemessaReenvio"
                                     action="#{GestaoRemessasControle.totalizar}"
                                     status="false"/>
                    </h:selectBooleanCheckbox>
                </rich:column>

            </rich:dataTable>
            <rich:datascroller for="tblParcelasReenvio" status="false" renderIfSinglePage="false"/>
            <a4j:commandLink value="Reagendar" styleClass="pure-button"
                             title="Reagendar data de vencimento das parcelas"
                             action="#{AlterarVencimentoParcelasControle.alterarParcelas}" ajaxSingle="true"
                             oncomplete="#{AlterarVencimentoParcelasControle.msgAlert}"
                             reRender="modalAlterarVencimento"></a4j:commandLink>

            <a4j:commandLink id="criarRemessaReenvio" styleClass="pure-button margin-v-10 margin-h-10"
                             process="tblParcelasReenvio"
                             oncomplete="Richfaces.showModalPanel('mdlCriarRemessaReenvio');"
                             reRender="panelConteudo, formmdlCriarRemessaReenvio"
                             style="right:20px;" value="Nova Remessa"/>
            <br/>
        </rich:panel>
        <!--  ----------  outros---------------------->

        <rich:panel rendered="#{fn:length(GestaoRemessasControle.agrupamentoParcelas.parcelasOutros) > 0}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda,direita,direita" width="100%">
                    <h:outputText  value="#{msg['OUTROS']}" style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup>
                        <h:outputText  value="Total Itens: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.totalOutros} " styleClass="text"/>
                        <h:outputText  value="Total Valores: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.valorOutros}" styleClass="text">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink id="btnExcel5"
                                             title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportarOutros}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <i class="fa-icon-table" ></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF5"
                                             title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportarOutros}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>

                                <i class="fa-icon-print"></i>
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable rowKeyVar="status" id="tblParcelasOutros" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.agrupamentoParcelas.parcelasOutros}"
                            rows="#{DataScrollerControle.nrRows}" var="parc">
                <%@include file="/includes/remessas/include_dadosParcela.jsp" %>
            </rich:dataTable>
            <rich:datascroller for="tblParcelasOutros" status="false" renderIfSinglePage="false"/>
            <br/>
        </rich:panel>
        <!--  ----------  SEM AÇAO---------------------->

        <rich:panel rendered="#{fn:length(GestaoRemessasControle.agrupamentoParcelas.parcelasSemAcao) > 0}">
            <f:facet name="header">
                <h:panelGrid columns="3" columnClasses="esquerda,direita,direita" width="100%">
                    <h:outputText value="#{GestaoRemessasControle.agrupar ? msg_aplic['prt_acoes_remessas_config_semacao'] : 'Parcelas'}"
                                  style="font-weight:bold" styleClass="text"/>
                    <h:panelGroup>
                        <h:outputText  value="Total Itens: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.totalSemAcao} " styleClass="text"/>
                        <h:outputText  value="Total Valores: " style="font-weight:bold" styleClass="text"/>
                        <h:outputText  value="#{GestaoRemessasControle.agrupamentoParcelas.valorSemAcao}" styleClass="text">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </h:panelGroup>
                    <h:panelGroup layout="block">
                        <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                            <a4j:commandLink id="btnExcel6"
                                             title="Gerar excel"
                                             styleClass="pure-button pure-button-xsmall"
                                             actionListener="#{GestaoRemessasControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="3">

                                <f:attribute name="tipo" value="xls"/>
                                <f:attribute name="atributos" value="empresa_Apresentar=Unidade,prefixo=Tipo,pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,situacaoRetorno=Cod.Retorno,numeroParcela=Nr. da parcela,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                
                                <i class="fa-icon-table" ></i>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnPDF6" title="Imprimir PDF"
                                             styleClass="pure-button pure-button-xsmall margin-h-10"
                                             actionListener="#{GestaoRemessasControle.exportar}"
                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                             accesskey="4">

                                <f:attribute name="tipo" value="pdf"/>
                                <f:attribute name="atributos" value="pessoa_Apresentar=Pessoa,categoriaAlunoApresentar=Categoria,codigoEmpresa=Empresa,codigoContrato=Contrato,codigo=Codigo,nrTentativas=Nr.Tentativas,situacaoRetorno=Cod.Retorno,descricao=Descrição,dataCobranca=Data,valorParcelaArredondar=Valor"/>
                                <f:attribute name="prefixo" value="Parcelas"/>
                                <f:attribute name="titulo" value="Parcelas"/>
                                <i class="fa-icon-print" ></i>
                            </a4j:commandLink>


                        </h:panelGroup>

                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
            <rich:dataTable rowKeyVar="status" id="tblParcelasSemAcao" width="100%" styleClass="pure-table-striped"
                            columnClasses="colunaCentralizada" value="#{GestaoRemessasControle.agrupamentoParcelas.parcelasSemAcao}"
                            rows="#{DataScrollerControle.nrRows}" var="parc">
                <%@include file="/includes/remessas/include_dadosParcela.jsp" %>

                <rich:column rendered="#{!GestaoRemessasControle.agrupar}">
                    <f:facet name="header">
                        <h:selectBooleanCheckbox  title="Marcar/desmarcar todas as parcelas"
                                                  value="#{GestaoRemessasControle.marcarTodosSemAcao}">

                            <a4j:support action="#{GestaoRemessasControle.marcarDesmarcarSemAcao}"
                                         event="onclick" reRender="panelConteudo,panelTotaisParcelas"/>
                        </h:selectBooleanCheckbox>
                    </f:facet>
                    <h:selectBooleanCheckbox  title="Marcar/desmarcar esta parcela"
                                              id="selecionarParcela"
                                              value="#{parc.parcelaEscolhida}">
                        <a4j:support event="onclick" reRender="tblParcelasRemessa,panelTotaisParcelas" 
                                     action="#{GestaoRemessasControle.totalizar}"
                                     status="false"/>
                    </h:selectBooleanCheckbox>
                </rich:column>

            </rich:dataTable>
            <rich:datascroller for="tblParcelasSemAcao" status="false" renderIfSinglePage="false"/>
        </rich:panel>
        <br/>
    </h:panelGrid>




    <h:panelGrid width="100%" columnClasses="colunaEsquerda, colunaDireita" title="Registros por página" columns="2">
        <h:panelGrid columns="2" >

            <h:outputText  value="Registros por página: " style="font-weight:bold" styleClass="text"/>
            <rich:inputNumberSpinner id="numPainelParcelas" inputSize="4" style="border: 0px solid;"
                                     styleClass="form remessasRPPagina" enableManualInput="true"
                                     minValue="1"
                                     value="#{DataScrollerControle.nrRows}">
                <a4j:support event="onchange"
                             reRender="panelConteudo" />
            </rich:inputNumberSpinner>
        </h:panelGrid>
    </h:panelGrid>

    <h:panelGrid id="panelTotaisParcelas" columns="2" columnClasses="alinhar">
        <h:panelGroup rendered="#{not empty GestaoRemessasControle.listaParcelas}" styleClass="alinhar">
            <fieldset>
                <legend class="legend">Legenda</legend>
                <h:panelGrid columns="2" width="100%">
                    <h:panelGroup layout="block" style="border:none; width: 8px; height: 8px; background-color:red;"/>
                    <h:outputText value="Parcelas Repescagem" styleClass="text"/>
                    <h:panelGroup layout="block" style="border:none; width: 8px; height: 8px; background-color:blue;"/>
                    <h:outputText value="Parcelas em Aberto" styleClass="text"/>
                </h:panelGrid>
            </fieldset>
        </h:panelGroup>

        <h:panelGroup rendered="#{not empty GestaoRemessasControle.listaParcelas}">
            <c:if test="${GestaoRemessasControle.tipoCobrancaPacto != null && GestaoRemessasControle.tipoCobrancaPacto.codigo == 0}">
            <fieldset>
                <legend class="legend">Saldo de Transações</legend>
                <h:panelGrid columns="2" style="vertical-align:top;" rowClasses="remessasOpcoesFix">
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      style="#{GestaoRemessasControle.styleSituacaoCreditoDCC}"
                                      value="#{GestaoRemessasControle.situacaoCreditoDCC}"/>
                    </h:panelGroup>
                </h:panelGrid>
            </fieldset>
            </c:if>
            <fieldset>
                <legend class="legend">Total Geral</legend>
                <h:panelGrid columns="6" cellpadding="1" cellspacing="1" columnClasses="text,text">
                    <rich:column>
                        <h:outputText style="font-weight:bold"
                                      value="Total Itens: "/>
                        <h:outputText value="#{fn:length(GestaoRemessasControle.listaParcelas)}"/>
                    </rich:column>
                    <rich:column>
                        <h:outputText style="font-weight:bold"
                                      value="Total Valores: "/>
                        <h:outputText value="#{GestaoRemessasControle.somaItensEmAberto + GestaoRemessasControle.somaItensRepescagem}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>

                    <rich:column>
                        <h:outputText style="font-weight:bold"
                                      value="Itens Repescagem: "/>
                        <h:outputText value="#{GestaoRemessasControle.qtdItensRepescagem}"/>
                    </rich:column>
                    <rich:column>
                        <h:outputText style="font-weight:bold"
                                      value="Valores Repescagem: "/>
                        <h:outputText value="#{GestaoRemessasControle.somaItensRepescagem}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>

                    <rich:column>
                        <h:outputText style="font-weight:bold"
                                      value="Itens em Aberto: "/>
                        <h:outputText value="#{GestaoRemessasControle.qtdItensEmAberto}"/>
                    </rich:column>
                    <rich:column>
                        <h:outputText style="font-weight:bold"
                                      value="Valores Em Aberto "/>
                        <h:outputText value="#{GestaoRemessasControle.somaItensEmAberto}">
                            <f:converter converterId="FormatadorNumerico" />
                        </h:outputText>
                    </rich:column>



                </h:panelGrid>

            </fieldset>
            <h:panelGroup rendered="#{!GestaoRemessasControle.agrupar}">

                <fieldset>
                    <legend class="legend">Totais Seleção</legend>
                    <h:panelGrid columns="6" cellpadding="1" cellspacing="1" columnClasses="text,text">
                        <rich:column>
                            <h:outputText style="font-weight:bold"
                                          value="Total Itens: "/>
                            <h:outputText value="#{GestaoRemessasControle.qtdItensEmAbertoSelecao + GestaoRemessasControle.qtdItensRepescagemSelecao}"/>
                        </rich:column>
                        <rich:column>
                            <h:outputText style="font-weight:bold"
                                          value="Total Valores: "/>
                            <h:outputText value="#{GestaoRemessasControle.somaItensEmAbertoSelecao + GestaoRemessasControle.somaItensRepescagemSelecao}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </rich:column>

                        <rich:column>
                            <h:outputText style="font-weight:bold"
                                          value="Itens Repescagem: "/>
                            <h:outputText value="#{GestaoRemessasControle.qtdItensRepescagemSelecao}"/>
                        </rich:column>
                        <rich:column>
                            <h:outputText style="font-weight:bold"
                                          value="Valores Repescagem: "/>
                            <h:outputText value="#{GestaoRemessasControle.somaItensRepescagemSelecao}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </rich:column>

                        <rich:column>
                            <h:outputText style="font-weight:bold"
                                          value="Itens em Aberto: "/>
                            <h:outputText value="#{GestaoRemessasControle.qtdItensEmAbertoSelecao}"/>
                        </rich:column>
                        <rich:column>
                            <h:outputText style="font-weight:bold"
                                          value="Valores Em Aberto "/>
                            <h:outputText value="#{GestaoRemessasControle.somaItensEmAbertoSelecao}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </rich:column>
                    </h:panelGrid>

                    <h:panelGroup layout="block" style="width:100%;">
                        <h:panelGroup style="text-align:right;width:100%;" layout="block">
                            <a4j:commandButton id="criarRemessa" rendered="#{GestaoRemessasControle.qtdTotalSelecao > 0}"
                                               oncomplete="Richfaces.showModalPanel('mdlGeracaoRemessa');"
                                               reRender="panelConteudo, formmdlGeracaoRemessa"
                                               style="right:20px;" value="Criar Remessa Selecionados"/>
                        </h:panelGroup>
                    </h:panelGroup>

                </fieldset>
            </h:panelGroup>
        </h:panelGroup>

    </h:panelGrid>
</a4j:outputPanel>
