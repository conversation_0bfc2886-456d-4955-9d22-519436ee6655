<h:panelGroup id="painelPagamentoPinpad">

    <h:inputHidden id="respostaPinpad"
                   value="#{MovPagamentoControle.pinPadPedidoVO.paramsResp}"/>

    <script>

        function cobrarGetcard() {
            try {
                console.log('Entrei cobrarGetcard');

                alterarMsgModal('Iniciando TEF');

                jQuery.ajax({
                    type: "POST",
                    url: '${MovPagamentoControle.urlServicoGetCardScope}/RealizarTransacaoASync',
                    dataType: "json",
                    data: '${MovPagamentoControle.bodyPinPadGetCard}',
                    success: function (data) {
                        console.log('cobrarGetcard --> success');
                        console.log(data);

                        try {
                            gravarLogGetCard('cobrarGetcard - success', JSON.stringify(data));
                        } catch (ex) {
                            console.log(ex);
                        }

                        var idTransacao = data.idTransacao;
                        salvarIdExternoGetCard(idTransacao);

                        alterarMsgModal('Iniciando TEF');

                        console.log('cobrarGetcard --> fim sucesso');
                    },
                    error: function (request, status, error) {
                        console.log('cobrarGetcard --> error');
                        console.log(request);
                        console.log(status);
                        console.log(error);

                        try {
                            gravarLogGetCard('cobrarGetcard - error', request);
                        } catch (ex) {
                            console.log(ex);
                        }

                        erroGetCard('Falha ao inicializar GetCard, tente novamente e caso persista verifique se o programa controladorScope est� inicializado. IPP');
                    },
                    async: false
                });
            } catch (ex) {
                console.log(ex);
            }
        }

        function abortarOperacaoGetcard() {
            try {
                console.log('Entrei abortarOperacaoGetcard');

                jQuery.ajax({
                    type: "POST",
                    url: '${MovPagamentoControle.urlServicoGetCardScope}/AbortarTransacao',
                    dataType: "json",
                    success: function (data) {
                        console.log('abortarOperacaoGetcard --> success');
                        console.log(data);

                        try {
                            gravarLogGetCard('abortarOperacaoGetcard - success', JSON.stringify(data));
                        } catch (ex) {
                            console.log(ex);
                        }

                        console.log('abortarOperacaoGetcard --> fim sucesso');
                    },
                    error: function (request, status, error) {
                        console.log('abortarOperacaoGetcard --> error');
                        console.log(error);
                        console.log(request);

                        try {
                            gravarLogGetCard('abortarOperacaoGetcard - error', request);
                        } catch (ex) {
                            console.log(ex);
                        }

                        var mensagem = '';
                        try {
                            mensagem = request.responseText;
                            var obj = JSON.parse(request.responseText);
                            mensagem = obj.mensagem;
                        } catch (ex) {
                            console.log('Erro GETCARD [converterJSON] ' + ex);
                        }
                        Notifier.error(mensagem, "Erro abortar GetCard");
                    },
                    async: false
                });
            } catch (ex) {
                console.log(ex);
            }
        }

        function consultarGetcard() {
            try {
                console.log('Entrei consultarGetcard');

                jQuery.ajax({
                    type: "POST",
                    url: '${MovPagamentoControle.urlServicoGetCardScope}/ResultadoTransacao?idTransacao=${MovPagamentoControle.pinPadPedidoVO.idExterno}',
                    dataType: "json",
                    success: function (data) {
                        console.log('consultarGetcard --> success');
                        console.log(data);

                        try {
                            gravarLogGetCard('consultarGetcard - success', JSON.stringify(data));
                        } catch (ex) {
                            console.log(ex);
                        }

                        try {
                            if (data.response.Sucesso === true) {
                                console.log('consultarGetcard | sucesso concluirPinpadGetCard');
                                concluirPinpadGetCard(JSON.stringify(data));
                            } else if (data.response.Sucesso === false &&
                                data.response.Processando === false) {
                                erroGetCard(JSON.stringify(data));
                                console.log('consultarGetcard | erroGetCard ');
                            } else {
                                if (data.response.MensagemOperador) {
                                    alterarMsgModal(data.response.MensagemOperador.replace("Cartao Credito", "").replace("Cartao Debito", ""));
                                }
                                console.log('consultarGetcard | else ');
                            }

                        } catch (ex) {
                            console.log('Erro obter mensagem consultarGetcard GETCARD: ' + ex);
                        }
                        console.log('consultarGetcard --> fim sucesso');
                    },
                    error: function (request, status, error) {
                        console.log('consultarGetcard --> error');
                        console.log(error);
                        console.log(status);
                        console.log(request);

                        try {
                            gravarLogGetCard('consultarGetcard - error', request);
                        } catch (ex) {
                            console.log(ex);
                        }

                        var mensagem ='Falha consultar GetCard';
                        try {
                            mensagem = request.responseText;
                            var obj = JSON.parse(request.responseText);
                            mensagem = obj.response.Mensagem;
                            alterarMsgModal(mensagem);
                        } catch (ex) {
                            console.log('consultarGetcard -- > Erro GETCARD [converterJSON] ' + ex);
                        }

                        Notifier.error(mensagem, "Erro consultar GetCard");
                    },
                    async: false
                });
            } catch (ex) {
                console.log(ex);
            }
        }

        function alterarMsgModal(msg) {
            try {
                document.getElementById('formModalPinpad:mensagemPinPad').innerHTML = msg;
            } catch (ex) {
                console.log(ex);
            }
        }
    </script>
</h:panelGroup>
