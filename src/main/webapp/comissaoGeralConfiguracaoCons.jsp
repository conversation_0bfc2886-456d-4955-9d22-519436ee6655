<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script>
    jQuery.noConflict();
</script>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view locale="#{SuperControle.idioma}">
    <title>${msg_aplic.prt_TaxaComissao_tituloForm}</title>

    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_TaxaComissao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-comissao-para-consultor/"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <%-- FIM HEADER --%>

    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>

                <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-12-12 margin-0-auto titulo-topo">
                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3"></h:panelGroup>
                    <h:panelGroup layout="block" styleClass="pure-u-1-3 text-right">
                        <h:panelGroup layout="block" styleClass="controles">
                            <a4j:commandLink id="btnLog"
                                                 styleClass="exportadores margin-h-10"
                                                 action="#{ComissaoGeralConfiguracaoControle.realizarConsultaLogObjetoGeral}"
                                                        oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                                    <h:outputText title="visualizar log geral da entidade" styleClass="btn-print-2 log"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="btnNovo"
                                             style="margin-top: 6px;"
                                             styleClass="pure-button pure-button-primary "
                                             action="#{ComissaoGeralConfiguracaoControle.novo}"
                                             accesskey="1">
                                &nbsp ${msg_bt.btn_cadastrar_novo}
                            </a4j:commandLink>

                            <a4j:commandLink id="excluirtudo"
                                             style="margin-top: 6px; margin-left: 10px"
                                             styleClass="pure-button "
                                             accesskey="1" oncomplete="Richfaces.showModalPanel('modalExcluirTudo');">
                                <i class="fa-icon-trash" style="margin-right: 10px"></i>
                                Excluir tudo
                            </a4j:commandLink>

                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <h:panelGroup id="tabelaReiniciar">
                    <table id="tblComissaoGeralConfiguracao"
                           class="tabelaComissaoGeralConfiguracao pure-g-r pure-u-11-12 margin-0-auto">
                        <thead>
                        <tr>
                            <th>COD</th>
                            <th>DUR.</th>
                            <th>SIT.</th>
                            <th>${msg_aplic.prt_TaxaComissao_valorEspontaneo}</th>
                            <th>${msg_aplic.prt_TaxaComissao_valorFixoAgendado}</th>
                            <th>${msg_aplic.prt_TaxaComissao_porcentagemEspontaneo}</th>
                            <th>${msg_aplic.prt_TaxaComissao_porcentagemAgendado}</th>
                            <th>VIG. INÍCIO</th>
                            <th>VIG. FINAL</Th>
                            <th>EMPRESA</th>
                            <th>EXCLUIR</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </h:panelGroup>

                <a4j:jsFunction name="jsEditarServ" action="#{ComissaoGeralConfiguracaoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>
        </h:form>

        <rich:modalPanel id="modalExcluir" autosized="false" width="400" height="200" shadowOpacity="true" styleClass="novaModal">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Excluir taxa"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hidelinkExcluir"/>
                    <rich:componentControl for="modalExcluir" attachTo="hidelinkExcluir"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>


            <h:form id="formExclusao">
                <input id="chavePrimariaexc" type="hidden" value="" name="chavePrimariaexc"/>
                <h:panelGroup style="font-size: 16px" layout="block">
                    <h:outputText value="Tem certeza que deseja excluir a taxa?"/>
                </h:panelGroup>
                <center style="margin-top: 20px">
                    <h:panelGroup>
                        <a4j:commandButton id="sim"  styleClass="botoes nvoBt" reRender="tabelaReiniciar"
                                           action="#{ComissaoGeralConfiguracaoControle.excluirTelaLista}"
                                           oncomplete="Richfaces.hideModalPanel('modalExcluir');reloadTabela();#{ComissaoGeralConfiguracaoControle.mensagemNotificar}"
                                           value="Sim">
                        </a4j:commandButton>
                        <rich:spacer width="30px;"/>
                        <a4j:commandButton id="nao"  styleClass="botoes nvoBt btSec"
                                           value="Não"
                                           oncomplete="Richfaces.hideModalPanel('modalExcluir');">
                        </a4j:commandButton>
                    </h:panelGroup>

                </center>
            </h:form>
        </rich:modalPanel>


        <rich:modalPanel id="modalExcluirTudo" autosized="false" width="400" height="200" shadowOpacity="true" styleClass="novaModal">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Excluir todas as taxas"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                            id="hidelinkExcluirTudo"/>
                    <rich:componentControl for="modalExcluirTudo" attachTo="hidelinkExcluirTudo"
                                           operation="hide" event="onclick"/>
                </h:panelGroup>
            </f:facet>


            <h:form id="formExclusaoTudo">
                <input id="chavePrimariaexc" type="hidden" value="" name="chavePrimariaexc"/>
                <h:panelGroup style="font-size: 16px" layout="block">
                    <h:outputText value="Tem certeza que deseja excluir TODAS as taxas?"/>
                </h:panelGroup>
                <center style="margin-top: 20px">
                    <h:panelGroup>
                        <a4j:commandButton id="sim"  styleClass="botoes nvoBt" reRender="tabelaReiniciar"
                                           action="#{ComissaoGeralConfiguracaoControle.excluirTodasAsTaxas}"
                                           oncomplete="Richfaces.hideModalPanel('modalExcluirTudo');reloadTabela();#{ComissaoGeralConfiguracaoControle.mensagemNotificar}"
                                           value="Sim">
                        </a4j:commandButton>
                        <rich:spacer width="30px;"/>
                        <a4j:commandButton id="nao"  styleClass="botoes nvoBt btSec"
                                           value="Não"
                                           oncomplete="Richfaces.hideModalPanel('modalExcluirTudo');">
                        </a4j:commandButton>
                    </h:panelGroup>

                </center>
            </h:form>
        </rich:modalPanel>

        <jsp:include page="includes/include_carregando_ripple.jsp"/>
    </h:panelGroup>

    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script type="text/javascript">
        var excluir = false;

        function jsEditar(){
            if(excluir){
                excluir = false;
            }else{
                jsEditarServ();
            }
        }
        function deletarTaxa(codigo){
            jQuery("#chavePrimariaexc").val(codigo);
            excluir = true;
            Richfaces.showModalPanel('modalExcluir');
        }
        jQuery(window).on("load", function () {
            iniciarTabela("tabelaComissaoGeralConfiguracao", "${contexto}/prest/contrato/comissao", 0, "desc", "", false);
        });

        function reloadTabela(){
            iniciarTabela("tabelaComissaoGeralConfiguracao", "${contexto}/prest/contrato/comissao", 0, "desc", "", false);
        }
    </script>
</f:view>
