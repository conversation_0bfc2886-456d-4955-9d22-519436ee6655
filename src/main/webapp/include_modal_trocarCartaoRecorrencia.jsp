<%-- 
    Document   : include_modal_trocarCartaoRecorrencia
    Created on : 03/08/2011, 14:01:22
    Author     : alcides
--%>

<%@include file="/includes/imports.jsp" %>
<a4j:outputPanel>
    <rich:modalPanel id="modalPanelTrocarCartao" autosized="true" width="600" height="200"  shadowOpacity="true">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText id="ttTrocaCartao" value="Trocar cart�o recorr�ncia"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkTrocarCartao"/>
                <rich:componentControl for="modalPanelTrocarCartao" attachTo="hidelinkTrocarCartao" operation="hide"  event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formTrocarCartao">


            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Trocar cart�o do contrato de recorr�ncia"/>
                </h:panelGrid>

                <rich:panel>
                    <c:if test="${TrocarCartaoRecorrenciaControle.existeParcela && !TrocarCartaoRecorrenciaControle.trocarCartaoSemCobrarParcela}">

                        <h:outputText id="msgTrocaCartao" value="#{msg_aplic.prt_troca_Cartao_Recorrencia}"/>

                    </c:if>
                    <c:if test="${not TrocarCartaoRecorrenciaControle.existeParcela}">
                        <h:panelGrid columns="2" columnClasses="colunaCentralizada">
                            <h:graphicImage value="images/warning.png"/>
                            <h:outputText id="msgSemParcela" style="horizontal-align: center; color: #DF0000; font-family: Arial,Verdana,sans-serif; font-size: 11px;" value="#{msg_aplic.prt_troca_Cartao_Recorrencia_sem_parcela}"/>
                        </h:panelGrid>
                    </c:if> 
                    <a4j:outputPanel rendered="#{TrocarCartaoRecorrenciaControle.trocarCartaoSemCobrarParcela}">
                        <jsp:include page="include_pagamentocartaocredito.jsp" flush="true"/>
                    </a4j:outputPanel>      
                </rich:panel>
            </h:panelGrid>

            <c:if test="${TrocarCartaoRecorrenciaControle.existeParcela}">
                <h:panelGrid columns="4" rowClasses="tablelistras textsmall" columnClasses="colunaCentralizada" width="100%">
                    <h:panelGroup style="width : 30%">
                        <h:outputText style="font-weight: bold;" value="Parcela" />
                    </h:panelGroup>
                    <h:panelGroup style="width : 25%">
                        <h:outputText style="font-weight: bold;" value="Data de Lan�amento" />
                    </h:panelGroup>
                    <h:panelGroup style="width : 25%">
                        <h:outputText style="font-weight: bold;" value="Vencimento" />
                    </h:panelGroup>
                    <h:panelGroup style="width : 20%">
                        <h:outputText style="font-weight: bold;" value="Valor" />
                    </h:panelGroup>


                    <h:panelGroup style="width : 30%">
                        <h:outputText id="parcelaDescricao"  value="#{TrocarCartaoRecorrenciaControle.proximaParcelaEA.descricao}"/>
                    </h:panelGroup>
                    <h:panelGroup style="width : 25%">
                        <h:outputText id="parcelaLancamento" value="#{TrocarCartaoRecorrenciaControle.proximaParcelaEA.dataRegistro_Apresentar}"/>
                    </h:panelGroup>
                    <h:panelGroup style="width : 25%">
                        <h:outputText id="parcelaVencimento" value="#{TrocarCartaoRecorrenciaControle.proximaParcelaEA.dataVencimento_Apresentar}"/>
                    </h:panelGroup>
                    <h:panelGroup style="width : 20%">
                        <h:outputText id="parcelaValor" value="#{TrocarCartaoRecorrenciaControle.proximaParcelaEA.valorParcela}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>

                </h:panelGrid>
                <br/>
                <center>
                    <h:outputText rendered="#{TrocarCartaoRecorrenciaControle.existeParcela && TrocarCartaoRecorrenciaControle.existeParcelaAnteriorEmRemessa}" id="msgParcelaEmRemessa" styleClass="mensagemDetalhada" value="*Existe parcela com vencimento anterior a apresentada, por�m est� vinculada a uma remessa aguardando processamento. "></h:outputText>
                  </center>
                 <br/>
                <center>
                    <a4j:commandButton id="btnAvancarPagamento" image="/imagens/btn_Confirmar.png"
                                       action="#{TrocarCartaoRecorrenciaControle.avancarPagamento}">
                    </a4j:commandButton>
                </center>
            </c:if>
            <c:if test="${not TrocarCartaoRecorrenciaControle.existeParcela}">
                 <br/>
                <center>
                    <h:outputText rendered="#{!TrocarCartaoRecorrenciaControle.existeParcela && TrocarCartaoRecorrenciaControle.existeParcelaAnteriorEmRemessa}" id="msgParcelaEmRemessaAberta" styleClass="mensagemDetalhada" value="*Existe parcela em aberto, por�m est� vinculada a uma remessa aguardando processamento. "></h:outputText>
                  </center>
                <br/>
                
                <center>
                    <a4j:commandButton id="btnOK" image="/imagens/botaoOk.png"
                                       oncomplete="Richfaces.hideModalPanel('modalPanelTrocarCartao');">
                    </a4j:commandButton>
                </center>
            </c:if>		
        </a4j:form>
    </rich:modalPanel>
</a4j:outputPanel>


