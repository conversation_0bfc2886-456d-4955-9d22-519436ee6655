<%@include file="includes/imports.jsp" %>

<script type="text/javascript" src="./script/foto_upload_qrcode.js"></script>
<script type="text/javascript" src="./bootstrap/jquery.js"></script>
<script type="text/javascript" src="./script/capturaFoto/webcam.min.js"></script>
<script type="text/javascript" src="script/packJQueryPlugins.min.js"></script>
<link   type="text/css" rel="stylesheet" href="./css/visualizarDocs.css"/>

<rich:modalPanel id="visualizacaoDocs" autosized="true" styleClass="novaModal noMargin" shadowOpacity="true" width="850"
                 height="750" onshow="initializeModalFotoUploadQrcode();">

    <div id="imagensOriginais" style="display: none;">
        <h:inputText id="doc_documento" value="#{TelaClienteControle.docs.documentos_Apresentar}" />
        <h:inputText id="doc_endereco" value="#{TelaClienteControle.docs.endereco_Apresentar}" />
        <h:inputText id="doc_atestado" value="#{TelaClienteControle.docs.atestado_Apresentar}" />
        <h:inputText id="doc_anexo1" value="#{TelaClienteControle.docs.anexo1_Apresentar}" />
        <h:inputText id="doc_anexo2" value="#{TelaClienteControle.docs.anexo2_Apresentar}" />
        <h:inputText id="doc_anexoCancelamento" value="#{TelaClienteControle.docs.anexoCancelamento_Apresentar}" />
    </div>

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Documentos associados ao contrato #{TelaClienteControle.docs.contrato.codigo}"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkvisualizacaoDocs"/>
            <rich:componentControl for="visualizacaoDocs" attachTo="hidelinkvisualizacaoDocs"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <div id="areaErro_doc" class="imageUploadBox" style="display: none;">
        <h:outputText id="msg_erro_doc" styleClass="instrucao negrito red" style="margin-top: 50px;" value=""/>
        <label class="pure-button pure-button-primary" style="margin-top: 10px;" onclick="sair();">
            Fechar Tela
            <i class="fa-icon-remove-sign" style="margin-left: 5px;"></i>
        </label>
    </div>

    <div id="areaSucesso_doc" class="imageUploadBox" style="display: none;">
        <h:outputText id="msg_sucesso_doc" styleClass="instrucao negrito texto-size-18" style="margin-top: 50px;"/>
        <label class="pure-button pure-button-primary" style="margin-top: 10px;" onclick="sair();">
            Fechar Tela
            <i class="fa-icon-remove-sign" style="margin-left: 5px;"></i>
        </label>
    </div>

    <div id="doc_foto_upload_qrcode" class="imageUploadBox">
        <h:panelGroup id="menu_foto_upload_qrcode">
            <ul class="menuHorizontalList">
                <li class="menuHorizontalListItem">
                    <a id="link_doc_webcam" href="#" class="menuHorizontalListItemLink"
                       title="Tirar fotos dos documentos do contrato">Webcam
                    </a>
                </li>
                <li class="menuHorizontalListItem">
                    <a id="link_doc_upload" href="#" class="menuHorizontalListItemLink"
                       title="Realizar upload de documentos do contrato">Escolher do Computador
                    </a>
                </li>
                <li class="menuHorizontalListItem">
                    <a id="link_doc_qrcode" href="#" class="menuHorizontalListItemLink"
                       title="Assinar contrato">QRCode
                    </a>
                </li>
            </ul>
        </h:panelGroup>

        <div id="blocoDocClientes" style="height: 45vh;" class="divisao">
            <div id="qrcode_tab">
                <h:panelGroup style="width: 100%; text-align: center;" layout="block"
                              rendered="#{TelaClienteControle.docs.assinatura eq null or fn:length(TelaClienteControle.docs.assinatura) eq 0}">
                    <h:outputText style="font-size: 14px;"
                                  value="Voc� ainda n�o adicionou documentos para este contrato. Fa�a isso usando o Assinatura Digital. "/>
                    <h:outputText style="font-size: 14px;" value="#{msg_aplic.prt_modal_assinatura_digital_texto}"/>
                    <h:outputLink style="font-size: 14px; margin-left: 5px;"
                                  value="#{LoginControle.urlAssinaturaDigitalDireta}"
                                  target="_blank">Assinatura Digital</h:outputLink>
                    <h:graphicImage url="#{LoginControle.urlAssinaturaDigital}"/>
                </h:panelGroup>
                <h:panelGroup rendered="#{fn:length(TelaClienteControle.docs.assinatura) gt 0}" styleClass="instrucao negrito">
                    Contrato j� foi assinado.
                </h:panelGroup>
            </div>

            <div id="webcam_tab">
                <div class="instrucao negrito">
                    Escolha a imagem que deseja mudar antes de capturar a foto.
                </div>

                <h:outputText id="msg_erro_webcam" styleClass="instrucao negrito red" style="margin-top: 20px; margin-bottom: 20px;" value=""/>

                <div id="video_doc_container" style="width: 400px; height: 300px; margin-bottom: 10px; margin-left: 190px;"></div>

                <label id="doc_snapshot_btn" class="upload_img_btn pure-button pure-button-primary" onclick="capturarFoto();">
                    Capturar Foto
                    <i class="fa-icon-camera" style="margin-left: 5px;"></i>
                </label>

                <div id="doc_rotulos_imagens_webcam" style="display: inline-block; margin-top: 10px; padding-top: 5px;" class="divisao">
                    <div class="rotulo_imagem">documentos</div>
                    <div class="rotulo_imagem">endere�o</div>
                    <div class="rotulo_imagem">atestado</div>
                    <div class="rotulo_imagem">anexo1</div>
                    <div class="rotulo_imagem">anexo2</div>
                    <div class="rotulo_imagem">anexo cancelamento</div>
                </div>

                <div id="doc_imagens_webcam" style="margin-top: 5px;">
                    <h:graphicImage id="preview_web1" styleClass="espaco_imagem imagem_selecao"
                                    style="margin-right: 3px;"
                                    url="#{TelaClienteControle.docs.documentos}"
                                    onclick="selectImage('preview_web1', 1);"/>

                    <h:graphicImage id="preview_web2" styleClass="espaco_imagem imagem_selecao"
                                    style="margin-right: 3px;"
                                    url="#{TelaClienteControle.docs.endereco}"
                                    onclick="selectImage('preview_web2', 2);"/>

                    <h:graphicImage id="preview_web3" styleClass="espaco_imagem imagem_selecao"
                                    style="margin-right: 3px;"
                                    url="#{TelaClienteControle.docs.atestado}"
                                    onclick="selectImage('preview_web3', 3);"/>

                    <h:graphicImage id="preview_web4" styleClass="espaco_imagem imagem_selecao"
                                    style="margin-right: 3px;"
                                    url="#{TelaClienteControle.docs.anexo1}"
                                    onclick="selectImage('preview_web4', 4);"/>

                    <h:graphicImage id="preview_web5" styleClass="espaco_imagem imagem_selecao"
                                    url="#{TelaClienteControle.docs.anexo2}"
                                    onclick="selectImage('preview_web5', 5);"/>

                    <h:graphicImage id="preview_web6" styleClass="espaco_imagem imagem_selecao"
                                    title="O arquivo de cancelamento n�o pode ser alterado fora do cancelamento"
                                    style="cursor: default"
                                    url="#{TelaClienteControle.docs.anexoCancelamento}"/>
                </div>

                <div id="doc_botoes_imagens_webcam" style="margin-top: 5px;">
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(1);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview_web1', '', 1);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(2);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview_web2', '', 2);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(3);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview_web3', '', 3);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(4);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview_web4', '', 4);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(5);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview_web5', '', 5);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem excluir_imagem" title="Ampliar Imagem"
                       onclick="ampliar(6);"></i>
                    <%--Anexo de cancelamento s� pode ser colocado no cancelamento--%>
                </div>

                <div id="doc_botoes_webcam" class="divisao" style="margin-top: 10px; padding-top: 5px;">
                    <label class="upload_img_btn pure-button pure-button-primary" style="margin-top: 10px;"
                           onclick="salvarImagens();">
                        Salvar Altera��es
                        <i class="fa-icon-save" style="margin-left: 5px;"></i>
                    </label>

                    <label class="upload_img_btn pure-button pure-button-primary" style="margin-top: 10px;"
                           onclick="desfazerAlteracoesWebcam();">
                        Desfazer Altera��es
                        <i class="fa-icon-undo" style="margin-left: 5px;"></i>
                    </label>
                </div>
            </div>

            <div id="upload_tab" class="uploadBox verticallyCentered labelPequena">
                <div class="instrucao negrito">
                    Escolha a imagem para alterar.
                </div>

                <div style="display: inline-block; margin-top: 100px;">
                    <div class="rotulo_imagem">documentos</div>
                    <div class="rotulo_imagem">endere�o</div>
                    <div class="rotulo_imagem">atestado</div>
                    <div class="rotulo_imagem">anexo1</div>
                    <div class="rotulo_imagem">anexo2</div>
                    <div class="rotulo_imagem">anexo cancelamento</div>
                </div>

                <div id="upload_imagens">
                    <h:graphicImage id="preview1" styleClass="espaco_imagem imagem_selecao"
                                    url="#{TelaClienteControle.docs.documentos_Apresentar}"
                                    onclick="document.getElementById('uploadImage1').click();"/>
                    <input id="uploadImage1" type="file" name="myPhoto"
                           onchange="PreviewImage('uploadImage1', 'preview1', 1, 800);"
                           accept="image/*" style="display: none;"/>

                    <h:graphicImage id="preview2" styleClass="espaco_imagem imagem_selecao"
                                    url="#{TelaClienteControle.docs.endereco_Apresentar}"
                                    onclick="document.getElementById('uploadImage2').click();"/>
                    <input id="uploadImage2" type="file" name="myPhoto"
                           onchange="PreviewImage('uploadImage2', 'preview2', 2, 800);"
                           accept="image/*" style="display: none;"/>

                    <h:graphicImage id="preview3" styleClass="espaco_imagem imagem_selecao"
                                    url="#{TelaClienteControle.docs.atestado_Apresentar}"
                                    onclick="document.getElementById('uploadImage3').click();"/>
                    <input id="uploadImage3" type="file" name="myPhoto"
                           onchange="PreviewImage('uploadImage3', 'preview3', 3, 800);"
                           accept="image/*" style="display: none;"/>

                    <h:graphicImage id="preview4" styleClass="espaco_imagem imagem_selecao"
                                    url="#{TelaClienteControle.docs.anexo1_Apresentar}"
                                    onclick="document.getElementById('uploadImage4').click();"/>
                    <input id="uploadImage4" type="file" name="myPhoto"
                           onchange="PreviewImage('uploadImage4', 'preview4', 4, 800);"
                           accept="image/*" style="display: none;"/>

                    <h:graphicImage id="preview5" styleClass="espaco_imagem imagem_selecao"
                                    url="#{TelaClienteControle.docs.anexo2_Apresentar}"
                                    onclick="document.getElementById('uploadImage5').click();"/>
                    <input id="uploadImage5" type="file" name="myPhoto"
                           onchange="PreviewImage('uploadImage5', 'preview5', 5, 800);"
                           accept="image/*" style="display: none;"/>

                    <h:graphicImage id="preview6" styleClass="espaco_imagem imagem_selecao"
                                    style="cursor: default;"
                                    url="#{TelaClienteControle.docs.anexoCancelamento_Apresentar}"/>
                </div>

                <div>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(1);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview1', 'uploadImage1', 1);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(2);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview2', 'uploadImage2', 2);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(3);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview3', 'uploadImage3', 3);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(4);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview4', 'uploadImage4', 4);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(5);"></i>
                    <i class="fa-icon-remove linkAzul texto-size-18 excluir_imagem" title="Remover Imagem"
                       onclick="limparImagem('preview5', 'uploadImage5', 5);"></i>
                    <i class="fa-icon-search linkAzul texto-size-18 ampliar_imagem" title="Ampliar Imagem"
                       onclick="ampliar(6);"></i>
                    <%--A imagem do cancelamento s� pode ser alterada no Cancelamento de contrato--%>
                </div>

                <div class="divisao" style="margin-top: 20px; padding-top: 10px;">
                    <label class="upload_img_btn pure-button pure-button-primary" onclick="salvarImagens();">
                        Salvar Altera��es
                        <i class="fa-icon-save" style="margin-left: 5px;"></i>
                    </label>

                    <label class="upload_img_btn pure-button pure-button-primary" onclick="desfazerAlteracoesUpload();">
                        Desfazer Altera��es
                        <i class="fa-icon-undo" style="margin-left: 5px;"></i>
                    </label>
                </div>
            </div>
        </div>
    </div>
</rich:modalPanel>

<div onclick="fecharImagem();" style=" display:none; width: 100%; height: 100vh;cursor: pointer;min-height: 130vh; background-color: rgba(0, 0, 0, 0.5); top: 0; z-index: 99999; position: absolute;" id="idcontratoview">
    <i class="fa-icon-remove" style="position: absolute; right: 20px; top: 20px; color: white; font-size: 30px;"></i>
    <img id="doccontratoview" style="height: 80vh; margin: 0 auto; margin-top: 10vh; display: block; "/>
</div>

