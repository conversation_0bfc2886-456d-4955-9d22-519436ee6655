<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">

<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <script type="text/javascript">
        function atualizarPendencias() {
            document.getElementById('form:atualizarPendencias').click();
        }
    </script>

    <style type="text/css">
        td.gridMetas {
            text-align: center;
            width: 240px;
        }
    </style>

    <rich:modalPanel id="panelAutorizacao" autosized="true"
                     shadowOpacity="true" width="300" height="200"
                     onshow="document.getElementById('formAutorizacao:codigo').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText
                    value="#{msg_aplic.prt_GrupoColaboradorParticipante_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hiperlinkAutorizacao"/>
                <rich:componentControl for="panelAutorizacao"
                                       attachTo="hiperlinkAutorizacao" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formAutorizacao" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" width="100%">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_AberturaMeta_codigo}"/>
                        <rich:spacer width="5px;"/>
                        <h:inputText id="codigo" size="5" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{AberturaMetaControle.aberturaMetaVO.responsavelLiberacaoTrocaColaboradorResponsavel.codigo}">
                            <a4j:support event="onchange" focus="formAutorizacao:senha"
                                         action="#{AberturaMetaControle.consultarSubstitutoColaborador}"
                                         reRender="formAutorizacao:userName"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{AberturaMetaControle.aberturaMetaVO.responsavelLiberacaoTrocaColaboradorResponsavel.username}"/>
                        <rich:spacer width="10px;"/>
                        <h:outputText styleClass="tituloCampos" id="userName"
                                      value="#{AberturaMetaControle.aberturaMetaVO.responsavelLiberacaoTrocaColaboradorResponsavel.username}"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_AberturaMeta_senha}"/>
                        <rich:spacer width="5px;"/>
                        <h:inputSecret autocomplete="off" id="senha" onblur="blurinput(this);"
                                       onfocus="focusinput(this);" styleClass="form"
                                       value="#{AberturaMetaControle.aberturaMetaVO.responsavelLiberacaoTrocaColaboradorResponsavel.senha}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                             width="100%">
                    <a4j:commandButton
                        oncomplete="#{AberturaMetaControle.richModalPanelColaboradorResponsavel}"
                        action="#{AberturaMetaControle.verificarPermissaoUsuario}"
                        image="./imagensCRM/botaoGravar.png" accesskey="5"
                        styleClass="botoes"
                        reRender="formAutorizacao:mensagemConsultaAutorizacao, panelColaboradorResponsavel"/>


                </h:panelGrid>
                <h:panelGrid id="mensagemConsultaAutorizacao" columns="1"
                             width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{AberturaMetaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{AberturaMetaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>


    <rich:modalPanel id="panelColaboradorResponsavel" autosized="true"
                     shadowOpacity="true" width="550" height="500"
                     onshow="document.getElementById('formColaboradorResponsavel:consultarColaboradorResponsavel').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText
                    value="#{msg_aplic.prt_GrupoColaboradorParticipante_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hiperlinkColaboradorResponsavel"/>
                <rich:componentControl for="panelColaboradorResponsavel"
                                       attachTo="hiperlinkColaboradorResponsavel" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formColaboradorResponsavel">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="5" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu styleClass="campos" id="consulta" required="true"
                                     value="#{AberturaMetaControle.campoConsultaColaboradorSubstituido}">
                        <f:selectItems
                            value="#{AberturaMetaControle.tipoConsultaComboColaboradorResponsavelSubstituido}"/>
                        <a4j:support event="onchange" reRender="panelGridForm"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{AberturaMetaControle.valorConsultaColaboradorSubstituido}"/>
                    <a4j:commandButton id="consultarColaboradorResponsavelSubstituto"
                                       reRender="formColaboradorResponsavel" styleClass="botoes"
                                       value="#{msg_bt.btn_consultar}"
                                       action="#{AberturaMetaControle.consultarColaboradorResponsavelParaSubstituicao}"
                                       image="./imagensCRM/botaoConsultar.png"
                                       title="#{msg.msg_consultar_dados}" accesskey="2"/>
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaColaboradorResponsavel"
                                width="100%" headerClass="consulta"
                                rowClasses="linhaImpar, linhaPar"
                                columnClasses="colunaCentralizada"
                                value="#{AberturaMetaControle.listaConsultaColaboradorResponsavel}"
                                rows="10" var="usuario">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{usuario.colaboradorVO.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_nome}"/>
                        </f:facet>
                        <h:outputText value="#{usuario.colaboradorVO.pessoa.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Colaborador_cpf}"/>
                        </f:facet>
                        <h:outputText value="#{usuario.colaboradorVO.pessoa.cfp}"/>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton
                            action="#{AberturaMetaControle.selecionarColaboradorResponsavelSubstituido}"
                            reRender="formAberturaMeta:colaboradorResponsavel, formAberturaMeta:panelGridMensagensAberturaMeta"
                            oncomplete="Richfaces.hideModalPanel('panelColaboradorResponsavel')"
                            value="#{msg_bt.btn_selecionar}" styleClass="botoes"
                            image="./imagensCRM/botaoEditar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center"
                                   for="formColaboradorResponsavel:resultadoConsultaColaboradorResponsavel"
                                   maxPages="10" id="scResultadoColaboradorResponsavel"/>
                <h:panelGrid id="mensagemConsultaColaboradorResponsavel" columns="1"
                             width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"
                                      value="#{AberturaMetaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada"
                                      value="#{AberturaMetaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <!-- INICIO - Abertura de Meta -->
    <rich:modalPanel id="panelAberturaMeta" autosized="true" top="70" shadowOpacity="true" width="550">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Abertura de Meta"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkAberturaMeta"/>
                <rich:componentControl for="panelAberturaMeta"
                                       attachTo="hidelinkAberturaMeta" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formAberturaMeta">

            <h:panelGrid columns="1"
                         style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                         columnClasses="colunaCentralizada" width="100%">
                <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_AberturaMeta_tituloForm}">
                    <h:outputLink value="#{SuperControle.urlWikiCRM}Opera��es:Abertura_da_Meta_do_Dia"
                                  title="Clique e saiba mais: Abertura de Meta do Dia" target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                </h:outputText>
            </h:panelGrid>

            <h:panelGrid id="panelGridAberturaMeta" columns="1" width="100%"
                         styleClass="tabForm">

                <h:panelGrid columns="2" width="100%" styleClass="tabForm">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_AberturaMeta_dia}"/>
                    <h:panelGroup>
                        <a4j:outputPanel>
                            <rich:calendar id="dia"
                                           oninputchange="return validar_Data(this.id);"
                                           value="#{AberturaMetaControle.aberturaMetaVO.dia}"
                                           enableManualInput="true" popup="true" inputSize="10"
                                           datePattern="dd/MM/yyyy" showApplyButton="false"
                                           cellWidth="24px" cellHeight="24px" style="width:200px"
                                           inputClass="campos" showFooter="false">
                                <a4j:support event="oninputchange" reRender="formAberturaMeta"/>
                                <a4j:support event="onchanged" reRender="formAberturaMeta"/>
                            </rich:calendar>
                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                        </a4j:outputPanel>

                        <h:panelGroup
                            rendered="#{AberturaMetaControle.aberturaRetroativa}">
                            &nbsp;<h:outputText
                                value="Abertura retroativa." styleClass="titulo5"/>
                            <h:outputLink
                                value="#{SuperControle.urlWikiCRM}Opera��es:Abertura_da_Meta_do_Dia#Metas_Retroativas"
                                title="Clique e saiba mais: Abertura de Meta Retroativa" target="_blank">
                                <h:graphicImage styleClass="linkWiki"
                                                style="padding-bottom:10px;" url="imagens/wiki_link2.gif"/>
                            </h:outputLink>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_AberturaMeta_colaboradorResponsavel}"/>
                    <h:panelGroup>
                        <h:inputText id="colaboradorResponsavel" size="50" maxlength="10"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" readonly="true"
                                     value="#{AberturaMetaControle.aberturaMetaVO.colaboradorResponsavel.nome}"/>
                        <a4j:commandButton reRender="panelAutorizacao" status="none;"
                                           oncomplete="Richfaces.showModalPanel('panelAutorizacao')"
                                           image="imagens/informacao.gif"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_AberturaMeta_responsavelCadastro}"/>
                    <h:panelGroup>
                        <h:inputText id="responsavelCadastro" size="50" maxlength="10"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" readonly="true"
                                     value="#{AberturaMetaControle.aberturaMetaVO.responsavelCadastro.nome}"/>
                    </h:panelGroup>
                </h:panelGrid>

                <h:panelGrid columns="2" width="100%"
                             columnClasses="colunaCentralizada">
                    <h:panelGrid columns="1" width="100%"
                                 columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="botaoCalcularAberturaMeta"
                                               action="#{AberturaMetaControle.calcularMetaDia}"
                                               reRender="formAberturaMeta, panelGridAberturaMeta, panelColaboradorResponsavel, tabelaMeta "
                                               value="#{msg_bt.btn_gravar}"
                                               image="./imagensCRM/botaoCalcularMeta.png"
                                               title="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes"/>
                            <rich:spacer width="20px"/>
                            <a4j:commandButton id="salvarAberturaMeta"
                                               rendered="#{AberturaMetaControle.apresentarBotaoGravarEMensagem}"
                                               oncomplete="atualizarPendencias(); Richfaces.hideModalPanel('panelAberturaMeta');"
                                               action="#{AberturaMetaControle.gravar}"
                                               reRender="formAberturaMeta, panelGridPrincipal, panelIndicador, form"
                                               value="#{msg_bt.btn_gravar}"
                                               image="./imagensCRM/botaoGravar.png"
                                               title="#{msg.msg_gravar_dados}" accesskey="2"
                                               styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>

                <h:panelGroup>
                    <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                            <td valign="top">
                                <rich:dataGrid id="listaAberturaMeta" width="100%"
                                               headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                               columnClasses="gridMetas" columns="2" elements="10"

                                               value="#{AberturaMetaControle.aberturaMetaVO.fecharMetaVosVenda}"
                                               var="fecharMetaVO"
                                               rendered="#{AberturaMetaControle.apresentarBotaoGravarEMensagem}">
                                    <f:facet name="header">
                                        <h:outputText
                                            value="#{msg_aplic.prt_AberturaMeta_metaDoDiaVenda}"
                                            styleClass="titulo3"/>
                                    </f:facet>
                                    <h:panelGrid
                                        rendered="#{fecharMetaVO.metaCalculada && !fecharMetaVO.apresentarMensagemRetroativo}"
                                        columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.identificadorMeta_ApresentarMeta}"
                                            styleClass="titulo3"/>
                                        <h:inputText id="valorMeta" size="15"
                                                     value="#{fecharMetaVO.meta}"
                                                     rendered="#{!fecharMetaVO.apresentarImagemFaturamento}"
                                                     onkeypress="Tecla(this.id)"
                                                     readonly="#{fecharMetaVO.css_ApresentarReadOnly}"
                                                     style="#{fecharMetaVO.css_Apresentar}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                        <h:inputText id="valorMeta1" size="15"
                                                     value="#{fecharMetaVO.meta}"
                                                     rendered="#{fecharMetaVO.apresentarImagemFaturamento}"
                                                     onkeypress="Tecla(this.id)"
                                                     readonly="#{fecharMetaVO.css_ApresentarReadOnly}"
                                                     style="#{fecharMetaVO.css_Apresentar}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGrid>
                                    <h:panelGrid
                                        rendered="#{fecharMetaVO.apresentarMensagemRetroativo && fecharMetaVO.metaCalculada}"
                                        columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.identificadorMeta_ApresentarMeta}"
                                            styleClass="titulo3"/>
                                        <h:outputText value="#{msg.msg_meta_retroativa}"
                                                      styleClass="titulo5"/>
                                    </h:panelGrid>
                                    <h:panelGrid id="gridMetaNaoCalculada"
                                                 rendered="#{!fecharMetaVO.metaCalculada}"
                                                 columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.identificadorMeta_ApresentarMeta}"
                                            styleClass="titulo3"/>
                                        <h:outputText value="#{msg.msg_meta_nao_calculada}"
                                                      styleClass="metaNaoCalculada">
                                        </h:outputText>
                                        <rich:toolTip for="gridMetaNaoCalculada" followMouse="true" direction="top-right"
                                                      style="width:300px; height:90px; " showDelay="200">
                                            <h:outputText styleClass="tituloCampos"
                                                          value="#{msg.msg_tip_meta_nao_calculada}"/>
                                        </rich:toolTip>
                                    </h:panelGrid>
                                </rich:dataGrid>
                            </td>
                            <td valign="top">
                                <rich:dataGrid id="listaAberturaMetaRetencao" width="100%"
                                               headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                               columnClasses="gridMetas" columns="1" elements="5"
                                               value="#{AberturaMetaControle.aberturaMetaVO.fecharMetaVosRetencao}"
                                               var="fecharMetaVO"
                                               rendered="#{AberturaMetaControle.apresentarBotaoGravarEMensagem}">
                                    <f:facet name="header">
                                        <h:outputText
                                            value="#{msg_aplic.prt_AberturaMeta_metaDoDiaRetencao}"
                                            styleClass="titulo3"/>
                                    </f:facet>
                                    <h:panelGrid
                                        rendered="#{!fecharMetaVO.apresentarMensagemRetroativo && fecharMetaVO.metaCalculada}"
                                        columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.identificadorMeta_ApresentarMeta}"
                                            styleClass="titulo3"/>
                                        <h:inputText id="valorMetaRetencao" size="15"
                                                     value="#{fecharMetaVO.meta}" onkeypress="Tecla(this.id)"
                                                     readonly="#{fecharMetaVO.css_ApresentarReadOnly}"
                                                     style="#{fecharMetaVO.css_Apresentar}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGrid>
                                    <h:panelGrid
                                        rendered="#{fecharMetaVO.apresentarMensagemRetroativo && fecharMetaVO.metaCalculada}"
                                        columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.identificadorMeta_ApresentarMeta}"
                                            styleClass="titulo3"/>
                                        <h:outputText value="#{msg.msg_meta_retroativa}"
                                                      styleClass="titulo5"/>
                                    </h:panelGrid>
                                    <h:panelGrid id="gridMetaNaoCalculada"
                                                 rendered="#{!fecharMetaVO.metaCalculada}"
                                                 columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.identificadorMeta_ApresentarMeta}"
                                            styleClass="titulo3"/>
                                        <h:outputText value="#{msg.msg_meta_nao_calculada}"
                                                      styleClass="metaNaoCalculada">
                                        </h:outputText>
                                        <rich:toolTip for="gridMetaNaoCalculada" followMouse="true" direction="top-right"
                                                      style="width:300px; height:90px; " showDelay="200">
                                            <h:outputText styleClass="tituloCampos"
                                                          value="#{msg.msg_tip_meta_nao_calculada}"/>
                                        </rich:toolTip>
                                    </h:panelGrid>

                                </rich:dataGrid>
                            </td>
                        </tr>
                        <tr>
                            <td valign="top">
                                <rich:dataGrid id="listaAberturaMetaEstudio" width="100%"
                                               headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                                               columnClasses="gridMetas" columns="2" elements="10"
                                               value="#{AberturaMetaControle.aberturaMetaVO.fecharMetaVosEstudio}"
                                               var="fecharMetaVO"
                                               rendered="#{AberturaMetaControle.apresentarBotaoGravarEMensagem && LoginControle.apresentarLinkEstudio}">
                                    <f:facet name="header">
                                        <h:outputText
                                            value="#{msg_aplic.prt_AberturaMeta_metaDoDiaEstudio}"
                                            styleClass="titulo3"/>
                                    </f:facet>
                                    <h:panelGrid columns="2" width="100%" columnClasses="w40, w60">
                                        <h:outputText
                                            value="#{fecharMetaVO.fase.descricao}"
                                            styleClass="titulo3"/>
                                        <h:inputText id="valorMetaEstudio" size="15"
                                                     value="#{fecharMetaVO.meta}" onkeypress="Tecla(this.id)"
                                                     readonly="#{fecharMetaVO.css_ApresentarReadOnly}"
                                                     style="#{fecharMetaVO.css_Apresentar}">
                                            <f:converter converterId="FormatadorNumerico"/>
                                        </h:inputText>
                                    </h:panelGrid>
                                </rich:dataGrid>
                            </td>
                        </tr>
                    </table>
                </h:panelGroup>
            </h:panelGrid>


            <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                <h:panelGrid id="panelGridMensagensAberturaMeta" columns="1"
                             width="100%">
                    <h:outputText id="msgModalAberturaMeta" styleClass="mensagem"
                                  value="#{AberturaMetaControle.mensagem}"/>
                    <h:outputText id="msgModalAberturaMetaDet"
                                  styleClass="mensagemDetalhada"
                                  value="#{AberturaMetaControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>

        </h:form>
    </rich:modalPanel>
    <!-- FIM - Abertura de Meta -->

    <!-- INICIO - Consultar Meta -->
    <rich:modalPanel id="panelAberturaMetaCons" autosized="true" shadowOpacity="true" width="650" height="250">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consultar Meta"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkpanelAberturaMetaCons"/>
                <rich:componentControl for="panelAberturaMetaCons"
                                       attachTo="hidelinkpanelAberturaMetaCons" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formAberturaMetaCons">
            <h:commandLink
                action="#{AberturaMetaControle.liberarBackingBeanMemoria}"
                id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm"
                         width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario"
                                  value="#{msg_aplic.prt_ConsultaMeta_tituloForm}"/>
                </h:panelGrid>
                <h:panelGrid columns="6" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" id="consulta"
                                     required="true"
                                     value="#{AberturaMetaControle.controleConsulta.campoConsulta}">
                        <f:selectItems
                            value="#{AberturaMetaControle.tipoConsultaComboFechamentoDia}"/>
                        <a4j:support event="onchange" reRender="panelGridForm"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta"
                                 rendered="#{!AberturaMetaControle.apresentarCalendarDia}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{AberturaMetaControle.controleConsulta.valorConsulta}"/>
                    <a4j:outputPanel>
                        <rich:calendar id="dia"
                                       rendered="#{AberturaMetaControle.apresentarCalendarDia}"
                                       oninputchange="return validar_Data(this.id);"
                                       value="#{AberturaMetaControle.dataConsultaFechamentoDia}"
                                       enableManualInput="true" popup="true" inputSize="10"
                                       datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px"
                                       cellHeight="24px" style="width:200px" inputClass="campos"
                                       showFooter="false"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </a4j:outputPanel>

                    <h:outputText rendered="#{AberturaMetaControle.usuarioLogado.administrador}" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Colaborador_empresa}"/>
                    <h:panelGroup rendered="#{AberturaMetaControle.usuarioLogado.administrador}">
                        <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         value="#{AberturaMetaControle.codigoEmpresaConsulta}">
                            <f:selectItem itemValue="" itemLabel="-- Selecione --"/>
                            <f:selectItems value="#{AberturaMetaControle.listaSelectItemEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:panelGroup>
                        <h:outputText styleClass="tituloCampos"
                                      value="#{msg_aplic.prt_FechamentoDia_metaEmAberto}"/>
                        <rich:spacer width="5px"/>
                        <h:selectBooleanCheckbox
                            value="#{AberturaMetaControle.metaEmAberto}"/>
                    </h:panelGroup>
                    <a4j:commandButton id="consultar" styleClass="botoes"
                                       value="#{msg_bt.btn_consultar}"
                                       reRender="formAberturaMetaCons:panelGridForm"
                                       action="#{AberturaMetaControle.consultarAberturaMetas}"
                                       image="./imagensCRM/botaoConsultar.png"
                                       title="#{msg.msg_consultar_dados}" accesskey="2"/>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta"
                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{AberturaMetaControle.listaConsultaFechamentoDia}"
                             rendered="#{!empty AberturaMetaControle.listaConsultaFechamentoDia}"
                             var="fechamentoDia">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FechamentoDia_dia}"/>
                        </f:facet>
                        <a4j:commandLink id="dia"
                                         action="#{AberturaMetaControle.selecionarAberturaDia}"
                                         reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                         value="#{fechamentoDia.dia_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText
                                value="#{msg_aplic.prt_FechamentoDia_colaboradorResponsavel}"/>
                        </f:facet>
                        <a4j:commandLink id="colaboradorResponsavel"
                                         action="#{AberturaMetaControle.selecionarAberturaDia}"
                                         reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                         value="#{fechamentoDia.colaboradorResponsavel.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText
                                value="#{msg_aplic.prt_FechamentoDia_responsavelCadstroColuna}"/>
                        </f:facet>
                        <a4j:commandLink id="responsavelCadastro"
                                         action="#{AberturaMetaControle.selecionarAberturaDia}"
                                         reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                         value="#{fechamentoDia.responsavelCadastro.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FechamentoDia_metaEmAberto}"/>
                        </f:facet>
                        <a4j:commandLink id="metaEmAberto"
                                         action="#{AberturaMetaControle.selecionarAberturaDia}"
                                         reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                         value="#{fechamentoDia.metaEmAberto_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Empresa"/>
                        </f:facet>
                        <a4j:commandLink id="empresa"
                                         action="#{AberturaMetaControle.selecionarAberturaDia}"
                                         reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                         value="#{fechamentoDia.empresaVO.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton value="#{msg_bt.btn_editar}"
                                           action="#{AberturaMetaControle.selecionarAberturaDia}"
                                           reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                           oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                           image="./imagensCRM/botaoEditar.png"
                                           title="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>
                <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                             width="100%">

                    <a4j:outputPanel id="painelPaginacao">
                        <h:panelGroup id="painelPaginacaoManual"
                                      rendered="#{!empty AberturaMetaControle.listaConsultaFechamentoDia}">

                            <a4j:commandLink id="pagiInicial" styleClass="tituloCampos"
                                             value="  <<  "
                                             reRender="formAberturaMetaCons:items,formAberturaMetaCons:paginaatual,formAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarPrimeiro}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagInicial"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos"
                                             value="  <  "
                                             reRender="formAberturaMetaCons:items,formAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarAnterior}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagAnterior"/>
                            </a4j:commandLink>
                            <h:outputText id="paginaAtual" styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_msg_pagina} #{AberturaMetaControle.confPaginacao.paginaAtualDeTodas}"
                                          rendered="true"/>
                            <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos"
                                             value="  >  "
                                             reRender="formAberturaMetaCons:items,formAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarPosterior}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagPosterior"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="pagiFinal" styleClass="tituloCampos"
                                             value="  >>  "
                                             reRender="formAberturaMetaCons:items,formAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarUltimo}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagFinal"/>
                            </a4j:commandLink>

                            <h:outputText id="totalItens" styleClass="tituloCampos"
                                          value=" [#{msg_aplic.prt_msg_itens} #{AberturaMetaControle.confPaginacao.numeroTotalItens}]"
                                          rendered="true"/>

                        </h:panelGroup>

                    </a4j:outputPanel>


                </h:panelGrid>

                <h:panelGrid id="panelGridMensagens" columns="1" width="100%"
                             styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{AberturaMetaControle.sucesso}"
                                         image="./imagensCRM/sucesso.png"/>
                        <h:commandButton rendered="#{AberturaMetaControle.erro}"
                                         image="./imagensCRM/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{AberturaMetaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{AberturaMetaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:form>
    </rich:modalPanel>
    <!-- FIM - Consultar Meta -->


    <!-- INICIO - Fechamento de Meta do dia -->
    <rich:modalPanel id="panelFecharAberturaMetaCons" autosized="true" shadowOpacity="true" width="450" height="220">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Fechamento de Meta do Dia"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkpanelFecharAberturaMetaCons"/>
                <rich:componentControl for="panelFecharAberturaMetaCons"
                                       attachTo="hidelinkpanelFecharAberturaMetaCons" operation="hide"
                                       event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <h:form id="formFecharAberturaMetaCons">
            <h:commandLink
                action="#{AberturaMetaControle.liberarBackingBeanMemoria}"
                id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm"
                         width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_FechamentoDia_tituloForm}">
                        <h:outputLink value="#{SuperControle.urlWikiCRM}Opera��es:Fechamento_da_Meta_do_Dia"
                                      title="Clique e saiba mais: Fechamento da Meta do Dia" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>
                <h:panelGrid columns="7" footerClass="colunaCentralizada"
                             width="100%">
                    <h:outputText styleClass="tituloCampos"
                                  value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form" id="consulta"
                                     required="true"
                                     value="#{AberturaMetaControle.controleConsulta.campoConsulta}">
                        <f:selectItems
                            value="#{AberturaMetaControle.tipoConsultaComboFechamentoDia}"/>
                        <a4j:support event="onchange" reRender="panelGridForm"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta"
                                 rendered="#{!AberturaMetaControle.apresentarCalendarDia}"
                                 onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form"
                                 value="#{AberturaMetaControle.controleConsulta.valorConsulta}"/>
                    <a4j:outputPanel>
                        <rich:calendar id="dia"
                                       rendered="#{AberturaMetaControle.apresentarCalendarDia}"
                                       oninputchange="return validar_Data(this.id);"
                                       value="#{AberturaMetaControle.dataConsultaFechamentoDia}"
                                       enableManualInput="true" popup="true" inputSize="10"
                                       datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px"
                                       cellHeight="24px" style="width:200px" inputClass="campos"
                                       showFooter="false"/>
                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    </a4j:outputPanel>
                    <h:outputText rendered="#{AberturaMetaControle.usuarioLogado.administrador}" styleClass="tituloCampos"
                                  value="#{msg_aplic.prt_Colaborador_empresa}"/>
                    <h:panelGroup rendered="#{AberturaMetaControle.usuarioLogado.administrador}">
                        <h:selectOneMenu id="empresa" styleClass="form" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         value="#{AberturaMetaControle.codigoEmpresaConsulta}">
                            <f:selectItem itemValue="" itemLabel="-- Selecione --"/>
                            <f:selectItems value="#{AberturaMetaControle.listaSelectItemEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <a4j:commandButton id="consultar" styleClass="botoes"
                                       value="#{msg_bt.btn_consultar}"
                                       reRender="formFecharAberturaMetaCons:panelGridForm"
                                       action="#{AberturaMetaControle.consultarAberturaMetas}"
                                       image="./imagensCRM/botaoConsultar.png"
                                       title="#{msg.msg_consultar_dados}" accesskey="2"/>
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta"
                             rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{AberturaMetaControle.listaConsultaFechamentoDia}"
                             rendered="#{!empty AberturaMetaControle.listaConsultaFechamentoDia}"
                             var="fechamentoDia">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FechamentoDia_dia}"/>
                        </f:facet>
                        <a4j:commandLink id="dia"
                                         action="#{AberturaMetaControle.selecionarFechamentoAberturaDia}"
                                         reRender="formFecharAberturaMetaCons, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDiaGrava}"
                                         value="#{fechamentoDia.dia_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText
                                value="#{msg_aplic.prt_FechamentoDia_colaboradorResponsavel}"/>
                        </f:facet>
                        <a4j:commandLink id="colaboradorResponsavel"
                                         action="#{AberturaMetaControle.selecionarFechamentoAberturaDia}"
                                         reRender="formFecharAberturaMetaCons, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDiaGrava}"
                                         value="#{fechamentoDia.colaboradorResponsavel.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText
                                value="#{msg_aplic.prt_FechamentoDia_responsavelCadstroColuna}"/>
                        </f:facet>
                        <a4j:commandLink id="responsavelCadastro"
                                         action="#{AberturaMetaControle.selecionarFechamentoAberturaDia}"
                                         reRender="formFecharAberturaMetaCons, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDiaGrava}"
                                         value="#{fechamentoDia.responsavelCadastro.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_FechamentoDia_metaEmAberto}"/>
                        </f:facet>
                        <a4j:commandLink id="metaEmAberto"
                                         action="#{AberturaMetaControle.selecionarFechamentoAberturaDia}"
                                         reRender="formFecharAberturaMetaCons, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDiaGrava}"
                                         value="#{fechamentoDia.metaEmAberto_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Empresa"/>
                        </f:facet>
                        <a4j:commandLink id="empresa"
                                         action="#{AberturaMetaControle.selecionarAberturaDia}"
                                         reRender="formAberturaMeta, panelGridPrincipal, panelIndicador"
                                         oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDia}"
                                         value="#{fechamentoDia.empresaVO.nome}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton value="#{msg_bt.btn_editar}"
                                           action="#{AberturaMetaControle.selecionarFechamentoAberturaDia}"
                                           reRender="formFecharAberturaMetaCons, panelGridPrincipal, panelIndicador"
                                           oncomplete="#{AberturaMetaControle.mostrarTelaFormularioFechamentoDiaGrava}"
                                           image="./imagensCRM/botaoEditar.png"
                                           title="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>

                </h:dataTable>
                <h:panelGrid columns="1" columnClasses="colunaCentralizada"
                             width="100%">

                    <a4j:outputPanel id="painelPaginacao">
                        <h:panelGroup id="painelPaginacaoManual"
                                      rendered="#{AberturaMetaControle.exibirPaginacao}">

                            <a4j:commandLink id="pagiInicial" styleClass="tituloCampos"
                                             value="  <<  "
                                             reRender="formFecharAberturaMetaCons:items,formFecharAberturaMetaCons:paginaatual,formFecharAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarPrimeiro}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagInicial"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos"
                                             value="  <  "
                                             reRender="formFecharAberturaMetaCons:items,formFecharAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarAnterior}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagAnterior"/>
                            </a4j:commandLink>
                            <h:outputText id="paginaAtual" styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_msg_pagina} #{AberturaMetaControle.confPaginacao.paginaAtualDeTodas}"
                                          rendered="true"/>
                            <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos"
                                             value="  >  "
                                             reRender="formFecharAberturaMetaCons:items,formFecharAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarPosterior}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagPosterior"/>
                            </a4j:commandLink>
                            <a4j:commandLink id="pagiFinal" styleClass="tituloCampos"
                                             value="  >>  "
                                             reRender="formFecharAberturaMetaCons:items,formFecharAberturaMetaCons:painelPaginacaoManual"
                                             rendered="#{AberturaMetaControle.confPaginacao.apresentarUltimo}"
                                             actionListener="#{AberturaMetaControle.consultarPaginadoListener}">
                                <f:attribute name="pagNavegacao" value="pagFinal"/>
                            </a4j:commandLink>

                            <h:outputText id="totalItens" styleClass="tituloCampos"
                                          value=" [#{msg_aplic.prt_msg_itens} #{AberturaMetaControle.confPaginacao.numeroTotalItens}]"
                                          rendered="true"/>

                        </h:panelGroup>

                    </a4j:outputPanel>


                </h:panelGrid>


                <h:panelGrid id="panelGridMensagens" columns="1" width="100%"
                             styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{AberturaMetaControle.sucesso}"
                                         image="./imagensCRM/sucesso.png"/>
                        <h:commandButton rendered="#{AberturaMetaControle.erro}"
                                         image="./imagensCRM/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"
                                          value="#{AberturaMetaControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{AberturaMetaControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>

        </h:form>
    </rich:modalPanel>
    <!-- FIM - Fechamento de Meta do dia -->


    <h:form id="form">
        <html>
            <jsp:include page="include_headCRM.jsp" flush="true"/>
            <body class="crm">
                <table width="100%" height="100%" border="0" cellpadding="0"
                       cellspacing="0" style="background-color: #EFEFEE;">
                    <tr>
                        <td height="77" align="left" valign="top" class="bgtop topoZW">
                            <jsp:include page="include_topo_novo.jsp" flush="true"/>
                            <jsp:include page="include_menu_crm_flat.jsp" flush="true"/>
                                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="top">
                            <h:panelGroup layout="block" id="conteudoCRM">
                                <jsp:include page="inicioCRM.jsp"/>
                            </h:panelGroup>
                        </td>
                    </tr>
                    <tr>
                        <td height="84" align="left" valign="top" class="bgrodapeSemLateral">
                            <jsp:include page="include_rodapeCRM.jsp" flush="true"/>
                        </td>
                    </tr>
                </table>
                <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha" id="btnAtualizaPagina"/>
                <a4j:commandButton id="botaoAtualizar" action="#{PendenciasCRMControle.consultarPendencias}"
                                   reRender="panelGridIndicadorVendas, panelIndicadorDeRetencao, panelIndicador"
                                   style="visibility: hidden;"/>
                <a4j:commandButton id="botaoAtualizarVenda" action="#{AberturaMetaControle.atualizarMetaDiaPorColaboradorVenda}"
                                   reRender="panelGridIndicadorVendas" style="visibility: hidden;"/>
            </body>
        </html>
    </h:form>

    <%@include file="/include_load_configs.jsp" %>

    <h:panelGroup id="includesCRM">
        <%@include file="/includes/crm/include_crm_metasMensais.jsp" %>
        <%@include file="/include_modal_expiracaoSenha.jsp" %>
    </h:panelGroup>

</f:view>
