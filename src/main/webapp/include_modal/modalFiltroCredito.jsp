<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalFiltroCredito"  styleClass="novaModal noMargin noOverflow"
                 width="700" height="230">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalFiltroCredito" value="FILTRO - EXTRATO CR�DITO TREINO" />
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hdlinkmodalFiltroCredito"/>
            <rich:componentControl for="modalFiltroCredito" attachTo="hdlinkmodalFiltroCredito" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>
    <a4j:form ajaxSubmit="true">
        <h:panelGrid  columnClasses="colunaDireita, colunaEsquerda" style="margin-top: 10px; margin-right: 10px; margin-left: 10px;" columns="2">
            <h:panelGroup id="labelOperacao">
                <h:outputText styleClass="rotuloCampos"
                              rendered="#{(!TelaClienteControle.aulasMarcadasComCreditoExtra)}"
                              value="Opera��o:" ></h:outputText>
            </h:panelGroup>

            <h:panelGroup id="comboOperacaoCredito">
                <h:selectOneMenu value="#{TelaClienteControle.codigoOperacaoCreditoSelecionado}"
                                 rendered="#{(!TelaClienteControle.aulasMarcadasComCreditoExtra)}">
                    <f:selectItems value="#{TelaClienteControle.listaOperacaoCreditoTreino}"/>
                </h:selectOneMenu>
            </h:panelGroup>

            <h:panelGroup id="labelAulaMarcadaComCreditoExtra">
                <h:outputText styleClass="rotuloCampos"
                              rendered="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}"
                              value="Aulas marcadas com cr�dito extra:" ></h:outputText>
            </h:panelGroup>
            <h:panelGroup id="chkAulaMarcadaComCreditoExtra">
                <h:selectBooleanCheckbox value="#{TelaClienteControle.aulasMarcadasComCreditoExtra}"
                                         rendered="#{TelaClienteControle.contratoSelecionado.contratoDuracao.contratoDuracaoCreditoTreinoVO.tipoHorarioCreditoTreinoEnum eq 'HORARIO_TURMA'}">
                    <a4j:support event="onchange" reRender="labelOperacao, comboOperacaoCredito"></a4j:support>
                </h:selectBooleanCheckbox>
            </h:panelGroup>

            <h:outputText styleClass="rotuloCampos" value="Data opera��o:" />

            <h:panelGroup>
                <h:panelGroup styleClass="dateTimeCustom">
                    <rich:calendar id="idInputDateIniOperacao"
                                   inputSize="10"
                                   value="#{TelaClienteControle.dataInicioOperacaoExtratoCredito}"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   buttonIcon="imagens_flat/calendar-button.svg"
                                   zindex="2"
                                   showWeeksBar="false" />
                    <h:message for="idInputDateIniOperacao"  styleClass="mensagemDetalhada"/>
                    <rich:jQuery id="mskData2" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                </h:panelGroup>
                <h:outputText styleClass="rotuloCampos" value=" at� " />
                <h:panelGroup styleClass="dateTimeCustom">
                    <rich:calendar id="idInputDateFimOperacao"
                                   inputSize="10"
                                   value="#{TelaClienteControle.dataFimOperacaoExtratoCredito}"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"
                                   buttonIcon="imagens_flat/calendar-button.svg"/>

                    <h:message for="idInputDateFimOperacao"  styleClass="mensagemDetalhada"/>
                    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                    <a4j:commandButton alt="Limpar per�odo de datas"
                                       style="padding-left: 10px"
                                       action="#{TelaClienteControle.limparFiltroDataExtratoCredito}"
                                       reRender="idInputDateFimOperacao, idInputDateIniOperacao"
                                       image="./imagens/limpar.gif" />
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGrid>
        <h:panelGrid style="margin-top: 25px" columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <a4j:commandLink id="idAplicarFiltroCreditoTreino"
                                 action="#{TelaClienteControle.aplicarFiltroExtratoCreditoTreino}"
                                 oncomplete="#{TelaClienteControle.mensagemNotificar}; #{TelaClienteControle.msgAlert}"
                                 reRender="pgTableCreditoTreino"
                                 styleClass="botaoPrimario texto-size-14">
                    <h:outputText style="font-size: 14px" value="OK"/>
                </a4j:commandLink>
                <a4j:commandLink id="idLimparFiltrosCredito"
                                 value="Limpar filtros"
                                 action="#{TelaClienteControle.limparFiltroExtratoCreditoTreino}"
                                 style="margin-left: 10px"
                                 reRender="pgTableCreditoTreino"
                                 styleClass="botaoSecundario texto-size-14"
                                 oncomplete="Richfaces.hideModalPanel('modalFiltroCredito');">
                </a4j:commandLink>
            </h:panelGroup>

        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>
