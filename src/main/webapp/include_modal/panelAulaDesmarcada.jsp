<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalPanelAulaDesmarcada" autosized="true" shadowOpacity="true" styleClass="novaModal noMargin"
                 minWidth="600" height="200">

    <f:facet name="header">
        <h:panelGroup id="tituloModal">
            <h:outputText id="lblModalPanelAulaDesmarcada" value="Desmarcar Aula"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hpAulaDesmarcada"/>
            <rich:componentControl for="modalPanelAulaDesmarcada" attachTo="hpAulaDesmarcada" operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formAulaDesmarcada">

        <h:panelGrid columns="1" columnClasses="colunaCentralizada" style="margin: 20px 20px 20px 20px;">

            <h:panelGrid columns="2" rowClasses="h40">
                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Cliente:"/>
                <h:outputText value="#{ClienteControle.aulaDesmarcadaVO.clienteVO.matricula} -
                              #{ClienteControle.aulaDesmarcadaVO.clienteVO.pessoa.nome}"
                              style="font-size: 16px;font-weight: normal;color: #777;"/>

                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Contrato:"/>
                <h:outputText value="#{ClienteControle.aulaDesmarcadaVO.contratoVO.codigo}"
                              style="font-size: 16px;font-weight: normal;color: #777;"/>


                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Aula Desmarcada:"/>
                <h:panelGroup>
                    <a4j:commandLink actionListener="#{ConsultarTurmaControle.mostrarDadosTurma}"
                                     reRender="self"
                                     style="padding: 5px;font-weight:normal;font-family: Arial;font-size: 16px;color: #29abe2;"
                                     title="Clique para visualizar alunos matriculados nesta turma e nesta data"
                                     oncomplete="abrirPopup('./listaAlunosTurma.jsp', 'ConsultarTurmaControle', 780, 595);">

                        <h:outputText value="#{ClienteControle.aulaDesmarcadaVO.horarioTurmaVO.diaSemana_Apresentar} -
                                      #{ClienteControle.aulaDesmarcadaVO.horarioTurmaVO.horaInicial} �s #{ClienteControle.aulaDesmarcadaVO.horarioTurmaVO.horaFinal}"/>

                        <f:attribute name="diaSemana"
                                     value="#{ClienteControle.aulaDesmarcadaVO.horarioTurmaVO.diaSemana}"/>
                        <f:attribute name="dataBase" value="#{ClienteControle.aulaDesmarcadaVO.dataOrigem}"/>
                        <f:attribute name="ht" value="#{ClienteControle.aulaDesmarcadaVO.horarioTurmaVO}"/>

                    </a4j:commandLink>

                    <h:panelGroup styleClass="dateTimeCustom" style="font-size: 11px !important;">
                        <rich:calendar value="#{ClienteControle.aulaDesmarcadaVO.dataOrigem}"
                                       id="dataInicial"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                    </h:panelGroup>
                </h:panelGroup>

                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Turma original: "
                              rendered="#{ClienteControle.selecionarTurmaDesmarcarAula}"/>
                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Turma: "
                              rendered="#{!ClienteControle.selecionarTurmaDesmarcarAula}"/>
                <h:outputText value="#{ClienteControle.horarioTurmaSelecionado.identificadorTurma}"
                              style="font-size: 16px;font-weight: normal;color: #777;"/>


                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Professor: "/>
                <h:outputText value="#{ClienteControle.horarioTurmaSelecionado.professor.pessoa.nome}"
                              style="font-size: 16px;font-weight: normal;color: #777;"/>

                <h:outputText style="font-weight: bold;font-size: 16px;color: #777;" value="Adicionar na turma: "
                              rendered="#{ClienteControle.selecionarTurmaDesmarcarAula}"/>

                <h:panelGroup rendered="#{ClienteControle.selecionarTurmaDesmarcarAula}" layout="block"
                              styleClass="block cb-container">
                    <h:selectOneMenu value="#{ClienteControle.aulaDesmarcadaVO.turmaDestino}">
                        <f:selectItems value="#{ClienteControle.turmas}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
            </h:panelGrid>

            <a4j:commandLink id="gravarDesmarcacaoAula"
                             reRender="formAulaDesmarcada,form:panelTurmas,form:panelOpracoesContrato,panelAutorizacaoFuncionalidade"
                             styleClass="pure-button pure-button-primary"
                             style="margin-top: 20px;"
                             oncomplete="#{ClienteControle.msgAlert};"
                             action="#{ClienteControle.gravarAulaDesmarcada}"
                             accesskey="2">
                Gravar
            </a4j:commandLink>

            <h:panelGrid columns="1" width="100%">
                <h:outputText styleClass="mensagem" value="#{ClienteControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" value="#{ClienteControle.mensagemDetalhada}"/>
            </h:panelGrid>

        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>
