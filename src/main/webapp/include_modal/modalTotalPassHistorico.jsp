<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@include file="../includes/imports.jsp" %>

<rich:modalPanel id="modalTotalPassHistorico" domElementAttachment="parent"
                 autosized="true" shadowOpacity="false" width="800"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText id="lblModalTotalPassHistorico" value="Hist�rico TotalPass"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign "
                    id="hidelinkTotalPassHistorico"/>
            <rich:componentControl for="modalTotalPassHistorico" attachTo="hidelinkTotalPassHistorico"
                                   operation="hide" event="onclick">
            </rich:componentControl>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formTotalPassHisto" ajaxSubmit="true">
        <h:panelGroup layout="block" styleClass="paginaFontResponsiva"
                      style="width: 100%; text-align: center; max-height: 250px; overflow-y: auto">

            <table class="tblHeaderLeft semZebra" style="margin-bottom: 5px;">
                <thead>
                <tr>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="TIPO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="DATA DO REGISTRO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="URI"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="APYKEY"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="TEMPO DE RESPOSTA"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="RESPOSTA"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="STATUS"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="TIPO"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="IP"/>
                    </th>
                    <th>
                        <h:outputText styleClass="texto-size-14 cinza negrito" style="color: #777"
                                      value="JSON"/>
                    </th>
                </tr>
                </thead>

                <tbody>
                <a4j:repeat var="total" value="#{TelaClienteControle.listaTotalPass}">
                    <tr>
                        <td>
                            <h:outputText
                                    value="#{total.origem}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.dataRegistro_Apresentar}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.uri}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.apikey}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.tempoResposta}ms"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.resposta}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.status}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.tipo}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.ip}"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                        <td>
                            <h:outputText
                                    value="#{total.json }"
                                    style="text-decoration: none; text-align: left; font-size: 14px; color: #29AAE2;"/>
                        </td>
                    </tr>
                </a4j:repeat>
                </tbody>
            </table>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
