* {
    font-family: <PERSON><PERSON>;
}

@media only screen
and (min-device-width: 320px)
and (max-device-width: 480px) {

    .texto-header {
        font-size: 40px !important;
    }

    .caixaAzul {
        height:10vh !important;
    }

    .icones {
        width: 5vh !important;
        margin-top: 2.5vh !important;
    }

    .infoAssinatura {
        padding-top: 0.5vh !important;
        font-size: 30px !important;
    }

}

header {
    background-color: #094771;
    box-shadow: 0 3px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 9px 0 rgba(0, 0, 0, 0.19);
    border-color: #e7e7e7;
    position: fixed;
    z-index: 3;
    min-height: 10vh;
    height: 10vh;
    line-height: 10vh;
    width: 100%;
    display: block;
    text-align: center;
    color: #FFFFFF;
    top: 0;
    left: 0;
    font-weight: bold;
    font-size: 3vh;
}

header a {
    font-size: 3vh;
}

#main {
    margin-top: 10vh;
}

.search, .removesearch {
    float: right;
    margin-right: 2vh;
}

.menu, .voltar {
    float: left;
    display: block;
    padding: 0 2vh;
}

a {
    cursor: pointer;
}

.caixa.contrato {
    display: flex;
}

.caixa.retrair {
    cursor: pointer;
}

.caixa.retrair:not(.retraido) .fa-icon-chevron-right {
    display: none;
}

.retraido .caixa.contrato, .retraido .fa-icon-chevron-down {
    display: none;
}

.caixa {
    height: 10vh;
    border-bottom: #dedede 1px solid;
    width: calc(100% - 4vh);
    margin-left: 1vh;
    padding: 0 1vh;
}

.lblTitulo [class^="fa-icon-"], [class*=" fa-icon-"] {
    margin-right: 10px;
    margin-bottom: 10px;
}

.lblTitulo {
    font-weight: bold;
    color: #333333;
    font-size: 3vh;
}

.fotoAluno {
    height: 7vh;
    width: 7vh;
    margin-top: 2vh;
    border-radius: 6vh;
    display: inline-block;
    margin-left: 2vh;
}

.nomeAluno {
    font-size: 2vh;
    text-transform: capitalize;
}

.infoAssinatura {
    display: block;
    color: #777777;
    font-size: 2vh;
    font-style: italic;
}

.info {
    display: inline-block;
    margin-top: 2vh;
    margin-left: 3vh;
    width: 100%;
}

.caixacontrato {
    padding: 2vh 1vh;
}

.caixatitlecancel {
    text-align: center;
    margin-top: 8%;
    margin-bottom: 3%;
}

.caixaFacial{
    display: none;
}

.facial .caixaFacial{
    width: 100%;
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999999;
    background-color: white;
    height: 100vh;
}
.assinar .caixaAssinatura, .assinarParQ .caixaAssinaturaParQ, .assinarTermoResponsabilidade .caixaAssinaturaTermoResponsabilidade, .documentos .caixaDocumentos, .validar .caixaValidar, .assinando .voltar, .assinar .voltar, .validar .voltar, .documentos .voltar, .atestado .caixaAtestado, .cartaovacina .caixaCartaoVacina, .cartaovacina .voltar,
.cartaoVisualizar .caixaCartaoVisualizar, .cartaoVisualizar .voltar, .validarTermoResponsabilidade .caixaValidarTermoResponsabilidade,
.caixaMenu .voltar, .fotoPerfil .caixaFoto, .fotoPerfil.assinando .enviarFotoAssinando, .parQ .formularioParQ {
    display: block;
}

.resize-container {
    position: relative;
    display: inline-block;
    cursor: move;
    margin: 0 auto;
}

.resize-container img {
    display: block
}

.resize-container:hover img,
.resize-container:active img {
    outline: 2px dashed rgba(222, 60, 80, .9);
}

.resize-handle-ne,
.resize-handle-se,
.resize-handle-nw,
.resize-handle-sw {
    position: absolute;
    display: block;
    width: 8px;
    height: 8px;
    background: rgba(222, 60, 80, .9);
    z-index: 1;
}

.resize-handle-nw {
    top: -5px;
    left: -5px;
    cursor: nw-resize;
}

.resize-handle-sw {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize;
}

.resize-handle-ne {
    top: -5px;
    right: -5px;
    cursor: ne-resize;
}

.resize-handle-se {
    bottom: -5px;
    right: -5px;
    cursor: se-resize;
}

.overlay {
    position: relative;
    left: calc(50% - 22vh);
    top: calc(50% - 22vh);
    z-index: 2;
    width: 45.5vh;
    height: 45.5vh;
    border: solid 2px rgba(222, 60, 80, .9);
    box-sizing: content-box;
    pointer-events: none;
}

.overlay:after,
.overlay:before {
    content: '';
    position: absolute;
    display: block;
    width: 45.5vh;
    height: 6vh;
    border-left: dashed 2px rgba(222, 60, 80, .9);
    border-right: dashed 2px rgba(222, 60, 80, .9);
}

.overlay:before {
    top: -6vh;
    left: -2px;
}

.overlay:after {
    bottom: -6vh;
    left: -2px;
}

.overlay-inner:after,
.overlay-inner:before {
    content: '';
    position: absolute;
    display: block;
    width: 6vh;
    height: 45.5vh;
    border-top: dashed 2px rgba(222, 60, 80, .9);
    border-bottom: dashed 2px rgba(222, 60, 80, .9);
}

.overlay-inner:before {
    left: -6vh;
    top: -2px;
}

.overlay-inner:after {
    right: -6vh;
    top: -2px;
}

.tempbutton {
    position: relative;
    display: inline-block;
    margin-left: 2em;
    margin-right: 1em;
}

.pseudocanvas {
    display: none;
}

.imageFotoAluno {
    height: 100%;
}

.caixaAssinatura.big, .caixaAssinaturaParQ.big, .caixaAssinaturaTermoResponsabilidade.big {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #ffffff;
    height: 100vh;
    z-index: 9;
}

.big .m-signature-pad--body
canvas, .big .m-signature-pad {
    height: 70vh;
}

.caixaAssinatura, .caixaAssinaturaParQ, .caixaAssinaturaTermoResponsabilidade, .caixaDocumentos, .caixaValidar, .caixaValidarTermoResponsabilidade, .caixaAtestado, .caixaFoto, .enviarFotoAssinando, .caixaCartaoVacina, .caixaCartaoVisualizar,
.formularioParQ {
    position: relative;
    width: 100%;
    display: none;
}

.caixa.assinaturaResponsavel {
    text-align: center;
    border-bottom: none;
    color: #777777;
    margin-top: 3vh;
}

.concluido .caixa.btn {
    background-color: #ffffff;
    color: #094771;
    position: absolute;
    left: 0;
    bottom: 0;
    margin-bottom: 3vh;
}

.caixa.btn {
    background-color: #094771;
    border-bottom: none;
    line-height: 10vh;
    text-align: center;
    font-size: 3vh;
    color: #ffffff;
    font-weight: bold;
}

.labelPequena {
    width: 100%;
    text-align: center;
    font-size: 2vh;
    margin-bottom: 3vh;
}

.preview.foto {
    width: 30vh;
    height: 30vh;
    overflow: hidden;
    margin: 0 auto;
    display: block;
    background-color: #777777;
}

.preview {
    width: calc(100% - 4vh);
    margin-bottom: 5vh;
    margin-left: 2vh;
}

.caixaMenor .caixaPreview {
    width: 50%;
    margin-left: 15%;
}

.caixaPreview .preview {
    width: 100%;
    margin-left: 0;
    height: 17vh;
    object-fit: cover;
}

.caixaPreview {
    padding: 10%;
    border: #dedede 1px solid;
    border-radius: 30px;
}

.caixaMenor {
    width: 49%;
    display: inline-block;
    margin-top: 3vh;
    text-align: center;
}

.caixaMenor2 {
    width: 30%;
    display: inline-block;
    margin-top: 10px;
    text-align: center;
}

.concluido .ok, .jaAssinado .ok, .previewGrande.show, .pesquisando .removesearch, .concluido .caixaConcluir, .cadastrado .ok {
    display: block;
}

.previewGrande {
    position: absolute;
    width: 100%;
    display: none;
    top: 0;
    min-height: 130vh;
    background-color: rgba(0, 0, 0, 0.5);
}

.pesquisando #tituloHeader, .pesquisando .search {
    display: none !important;
}

.concluido header, .campoPesquisa, .removesearch, .ok, .jaAssinado #concluirValidar, .voltar, .assinar .menu, .validar .menu, .documentos .menu, .assinando .menu {
    display: none;
}

.campoPesquisa:focus {
    outline: none;
}

.pesquisando .campoPesquisa {
    display: inline-block;
    line-height: 9vh;
    top: 0;
    position: absolute;
    width: 80%;
    left: 10%;
    font-size: 4vh;
    color: #ffffff;
    text-align: center;
    background-color: transparent;
    border: none;
    text-transform: uppercase;
    font-weight: bold;
}

.caixaConcluir span {
    font-size: 4vh;
    font-weight: bold;
}

.caixaConcluir i {
    font-size: 35vh;
    font-weight: normal;
    margin-top: 25vh;
    display: block;
}

.caixaConcluir {
    background-color: #094771;
    width: 100%;
    height: 100vh;
    color: #FFFFFF;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;

}

#painelmenu {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100vh;
    margin: 0 auto;
    z-index: 99;
    background-color: rgba(255, 255, 255, .7);
}

#painelmenu .caixaMenu {
    width: 75%;
    margin-left: -75%;
    background-color: #ffffff;
    height: 100vh;
    box-shadow: 5px 0px 15px #888888;
}

.caixaMenu header {
    position: relative;
}

.blur {
    -webkit-filter: blur(20px);
    -moz-filter: blur(20px);
    -o-filter: blur(20px);
    -ms-filter: blur(20px);
    filter: blur(20px);
}

#empresas select {
    font-size: 3vh;
    width: 90%;
    background: transparent;
    padding: 2vh;
    border: none;

}

#username, #empresas {
    width: 100%;
    padding-top: 2vh;
    text-align: center;
    color: #777777;
    font-weight: bold;
    font-size: 3vh;
}

#empresas {
    font-weight: normal !important;
}

.caixaAzul {
    background-color: #094771;
    width: 75%;
    height: 17vh;
    margin-left: 12.5%;
    text-align: center;
    border-radius: 10px;
}

.caixa.atestado {
    line-height: 7vh;
    border-bottom: none;
    text-align: center;
    color: #777;
    width: 75%;
    margin-left: 12.5%;
    margin-top: 30vh;
    font-size: 4vh;
}


.caixaAzul.principal {
    border: #777777 6px solid;
    background-color: #ffffff;
}


/*------------------------------------------------------Inicio Carregando------------------------------------------------------------------*/
.spinnerCarregando {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(255, 255, 255, .6);
    margin: 0 auto;
    z-index: 9999999999;
    left: 0;
    padding: 50vh 0;
    text-align: center;
}

.spinnerCarregando > div {
    width: 18px;
    height: 18px;
    background-color: #094771;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
    animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinnerCarregando .bounce1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.spinnerCarregando .bounce2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0)
    }
    40% {
        -webkit-transform: scale(1.0)
    }
}

@keyframes sk-bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    40% {
        -webkit-transform: scale(1.0);
        transform: scale(1.0);
    }
}

.itemMenu.principal {
    width: 100%;
}

.itemMenu {
    display: inline-block;
    width: 49%;
    vertical-align: top;
}

@media screen and (orientation: portrait) {
    #empresas select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: url(../imagens_flat/caret-down.png) 92% / 4% no-repeat #fff;
    }
}

@media screen and (orientation: landscape) {
    .tools.clearfix span {
        line-height: 70px;
        font-size: 55px;
    }

    #empresas select {
        border: 1px solid #777;
    }

    .preview {
        width: 30vw;
        margin-left: 35vw;
    }

    .caixaMenor {
        width: 49%;
    }

    .caixaMenor .caixaPreview {
        width: 25%;
        margin-left: 27%;
    }

    .atestado.caixaPreview .preview {
        width: 40%;
    }

    .itemMenu {
        width: 32%;
        margin-top: 6vh;
    }

}

.rotate90 {
    transform: rotate(90deg) translateY(0%);
    -webkit-transform: rotate(90deg) translateY(0%);
    -ms-transform: rotate(90deg) translateY(0%);
}

.rotate180 {
    transform: rotate(180deg) translate(0%, 0%);
    -webkit-transform: rotate(180deg) translate(0%, 0%);
    -ms-transform: rotate(180deg) translateX(0%);
}

.rotate270 {
    transform: rotate(270deg) translateX(0%);
    -webkit-transform: rotate(270deg) translateX(0%);
    -ms-transform: rotate(270deg) translateX(0%);
}

.selectPequeno {

    width: 18px;
    height: 18px;
    left: calc(50% - 18px/2 - 278.95px);
    top: 618.59px;
    background: #FFFFFF;
    border-radius: 4px;
}

#perguntasParQ {
    margin: 24px 40px;
    color: #51555a;
}

.termoDeAceite {
    margin: 24px 40px;
    color: #51555a;
}

.titleTermoAceite {
    width: calc(100% - 8vh);
    text-align: center;
    font-weight: bold;
    font-style: normal;
    padding: 4vh;
    font-size: 2vh;
    display: block;
    color: #777777;
}

.textoTermoAceite {
    font-size: 1.6vh;
}

.texto_alert_termo {
    color: #f50000;
}

.text-capitalize {
    text-transform: capitalize;
}

.texto-pergunta {
    border-bottom: 1px solid #c3c3c3;
}

.div-pergunta-parq {
    margin: 40px 0;
}

.div-resposta-parq {
    margin: 12px 0;
    display: flex;
    flex-wrap: wrap;
}

.radio-btn-parq {
    flex: 0 0 50%;
    max-width: 50%;
}

.textarea-parq {
    flex: 0 0 50%;
    max-width: 50%;
}

.text-observacao {
    margin: 0 0 8px;
}

.textarea-parq textarea {
    width: 100%;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    resize: none;
}

.radio-btn-sim {
    margin-right:12px
}

.assine-aqui {
    position: absolute;
    top: 37%;
    width: 95%;
    text-align: center;
    font-weight: bold;
    color: #777777;
    font-size: 7vw;
    border-top: #777777 1px solid;
    margin-left: 2.5%;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 20px;
    border: 1px solid #888;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.modal-options{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-left: 20%;
    margin-right: 20%;
}
#abrirModal{
    background-color: #094771;
    border-bottom: none;
    padding: 10px;
    width: 100%;
    text-align: center;
    font-size: 2vh;
    color: #ffffff;
    font-weight: bold;
}
#abrirModal2{
    background-color: #094771;
    border-bottom: none;
    padding: 10px;
    width: 100%;
    text-align: center;
    font-size: 2vh;
    color: #ffffff;
    font-weight: bold;
}
#abrirModalParq{
    background-color: #094771;
    border-bottom: none;
    padding: 10px;
    width: 100%;
    text-align: center;
    font-size: 2vh;
    color: #ffffff;
    font-weight: bold;
}
.buttonRemover{
    background-color: #094771;
    font-size: 3vh;
    padding: 10px;
    border: none;
    text-align: center;
    font-size: 3vh;
    color: #ffffff;
    font-weight: bold;
}

.buttonFechar{
    font-size: 3vh;
    padding: 10px;
    border: none;
    text-align: center;
    font-size: 3vh;
    font-weight: bold;
}
