<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_Feriado_tituloForm}"/>
    </title>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <rich:modalPanel id="panelCidade" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formCidade:consultarCidade').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_Feriado_consultarCidade}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hiperlinkCidade"/>
                <rich:componentControl for="panelCidade" attachTo="hiperlinkCidade" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCidade" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%">

                    <h:panelGroup>
                        <h:selectOneMenu  id="pais" onblur="blurinput(this);"   onfocus="focusinput(this);"   styleClass="form" value="#{FeriadoControle.feriadoVO.pais.codigo}" >
                            <a4j:support  event="onchange" reRender="estado,cidade" action="#{FeriadoControle.montarListaSelectItemEstado}"/>
                            <f:selectItems  value="#{FeriadoControle.listaSelectItemPais}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_pais" action="#{FeriadoControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                        <h:message for="pais" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText    value="#{msg_aplic.prt_Pessoa_estado}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectOneMenu  id="estado" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{FeriadoControle.feriadoVO.estado.codigo}" >
                            <f:selectItems  value="#{FeriadoControle.listaSelectItemEstado}" />
                            <a4j:support  event="onchange" reRender="cidade" action="#{FeriadoControle.montarListaSelectItemCidade}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_estado" action="#{FeriadoControle.montarListaSelectItemEstado}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:estado"/>
                        <h:message for="estado" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText    value="#{msg_aplic.prt_Pessoa_cidade}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:selectOneMenu  id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{FeriadoControle.feriadoVO.cidade.codigo}" >
                            <f:selectItems  value="#{FeriadoControle.listaSelectItemCidade}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_cidade" action="#{FeriadoControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                        <h:message for="cidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>


                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCidade" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento" value="#{FeriadoControle.listaConsultarCidade}" rows="10" var="cidade">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cidade_codigo}"/>
                        </f:facet>
                        <h:outputText value="#{cidade.codigo}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cidade_nome}"/>
                        </f:facet>
                        <h:outputText value="#{cidade.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cidade_pais}"/>
                        </f:facet>
                        <h:outputText value="#{cidade.pais.nome}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Cidade_estado}"/>
                        </f:facet>
                        <h:outputText value="#{feriado.estado_Apresentar}"/>
                    </rich:column>
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <a4j:commandButton action="#{FeriadoControle.selecionarCidade}" focus="cidade" reRender="form, formCidade" oncomplete="Richfaces.hideModalPanel('panelCidade')" value="#{msg_bt.btn_selecionar}" styleClass="botoes" image="./imagensCRM/botaoSelecionar.png"/>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formCidade:resultadoConsultaCidade" maxPages="10" id="scResultadoCidade"/>
                <h:panelGrid id="mensagemConsultaCidade" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{FeriadoControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{FeriadoControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <c:set var="titulo" scope="session" value="${msg_aplic.prt_Feriado_tituloForm}"/>
        <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-editar-excluir-feriados/"/>

        <h:panelGroup layout="block" styleClass="pure-g-r">
            <f:facet name="header">
                <jsp:include page="topoReduzido_material_crm.jsp"/>
            </f:facet>
        </h:panelGroup>

        <h:form id="form">
            <h:commandLink action="#{FeriadoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_Feriado_tituloForm}">
                        <h:outputLink value="#{SuperControle.urlBaseConhecimento}como-cadastrar-editar-excluir-feriados/"
                                      title="Clique e saiba mais: Feriado" target="_blank">
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </h:outputText>
                </h:panelGrid>

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"  width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_dia}" />
                    <h:panelGroup>
                        <a4j:outputPanel layout="block">
                            <rich:calendar id="dia" value="#{FeriadoControle.feriadoVO.dia}" oninputchange="return validar_Data(this.id);" enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px" cellHeight="24px" inputClass="campos" showFooter="false"/>
                        </a4j:outputPanel>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao" size="50" maxlength="50" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form" value="#{FeriadoControle.feriadoVO.descricao}" />
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_pais}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="pais" onblur="blurinput(this);"   onfocus="focusinput(this);"   styleClass="form" value="#{FeriadoControle.feriadoVO.pais.codigo}" >
                            <a4j:support  event="onchange" reRender="estado,cidade" action="#{FeriadoControle.montarListaSelectItemEstado}"/>
                            <f:selectItems  value="#{FeriadoControle.listaSelectItemPais}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_pais" action="#{FeriadoControle.montarListaSelectItemPais}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:pais"/>
                        <h:message for="pais" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FeriadoControle.estadoObrigatorio}" >
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_estado}" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FeriadoControle.estadoObrigatorio}">
                        <h:selectOneMenu  id="estado" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{FeriadoControle.feriadoVO.estado.codigo}" >
                            <f:selectItems  value="#{FeriadoControle.listaSelectItemEstado}" />
                            <a4j:support  event="onchange" reRender="cidade" action="#{FeriadoControle.montarListaSelectItemCidade}"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_estado" action="#{FeriadoControle.montarListaSelectItemEstado}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:estado"/>
                        <h:message for="estado" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FeriadoControle.cidadeObrigatorio}">
                        <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_cidade}" />
                    </h:panelGroup>
                    <h:panelGroup rendered="#{FeriadoControle.cidadeObrigatorio}">
                        <h:selectOneMenu  style="vertical-align: text-top" id="cidade" onblur="blurinput(this);"  onfocus="focusinput(this);"   styleClass="form" value="#{FeriadoControle.feriadoVO.cidade.codigo}" >
                            <f:selectItems  value="#{FeriadoControle.listaSelectItemCidade}" />
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_cidade" action="#{FeriadoControle.montarListaSelectItemCidade}" image="imagens/atualizar.png" immediate="true" ajaxSingle="true" reRender="form:cidade"/>
                        <h:message for="cidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>




                </h:panelGrid>
                <h:panelGroup layout="block" style="text-align: center">
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_nacional}" />
                    <h:selectBooleanCheckbox id="nacional" styleClass="campos" value="#{FeriadoControle.feriadoVO.nacional}">
                        <a4j:support event="onclick" reRender="form" action="#{FeriadoControle.escolherFeriadoNacional}" />
                    </h:selectBooleanCheckbox>
                    <rich:spacer width="65"/>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_estadual}" />
                    <rich:spacer width="5"/>
                    <h:selectBooleanCheckbox id="estadual" styleClass="campos" value="#{FeriadoControle.feriadoVO.estadual}">
                        <a4j:support event="onclick" reRender="form" action="#{FeriadoControle.escolherFeriadoEstadual}" />
                    </h:selectBooleanCheckbox>
                    <rich:spacer width="65"/>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_Feriado_naoRecorrente}" />
                    <rich:spacer width="5"/>
                    <h:panelGroup  id="naoRecorrente" >
                        <h:selectOneMenu  id="comboNaoRecorrente" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{FeriadoControle.feriadoVO.feriadoRecorrenteItem}" >
                            <f:selectItems  value="#{FeriadoControle.listSelectItemRecorrente}" />
                            <a4j:support  event="onchange" reRender="naoRecorrente" action="#{FeriadoControle.selecionarTipoFeriado}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <a4j:support event="onclick" reRender="form:nacional, form:estadual, form:naoRecorrente" action="#{FeriadoControle.escolherFeriado}" />
                </h:panelGroup>

                <c:if test="${FeriadoControle.exibirReplicarRedeEmpresa}">
                    <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                        </br>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%">
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_PlanoReplicarEmpresa_tituloForm}"/>
                        </f:facet>
                        </br>
                        <h:panelGrid columns="3" style="border-style: solid;" id="contadorReplicaPlano"
                                     columnClasses="colunaCentralizada, colunaCentralizada, colunaCentralizada"
                                     width="100%">
                            <h:outputText value="Unidades" styleClass="botoes nvoBt"/>
                            <h:outputText value="Replicadas" styleClass="botoes nvoBt"/>
                            <h:outputText value="Não Replicadas" styleClass="botoes nvoBt"/>
                            <h:outputText value="#{FeriadoControle.listaFeriadoRedeEmpresaSize}"
                                          style="font-size: 20pt; font-weight: bold;"/>
                            <h:outputText value="#{FeriadoControle.listaFeriadoRedeEmpresaSincronizado}"
                                          style="color: #0f4c36; font-size: 20pt; font-weight: bold;"/>
                            <h:outputText
                                    value="#{FeriadoControle.listaFeriadoRedeEmpresaSize - FeriadoControle.listaFeriadoRedeEmpresaSincronizado}"
                                    style="color: #8b0000; font-size: 20pt; font-weight: bold;"/>
                        </h:panelGrid>
                        <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar"
                                     columnClasses="colunaCentralizada" width="100%">

                            <h:dataTable id="listaEmpresasReplicar" width="100%" headerClass="subordinado"
                                         styleClass="tabFormSubordinada"
                                         rowClasses="linhaImpar, linhaPar"
                                         columnClasses="colunaEsquerda, colunaEsquerda, colunaCentralizada, colunaEsquerda"
                                         style="text-align: center;"
                                         value="#{FeriadoControle.listaFeriadoRedeEmpresa}"
                                         var="feriadoRedeEmpresaReplicacao">
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_nomeUnidade}"/>
                                    </f:facet>
                                    <h:outputText value="#{feriadoRedeEmpresaReplicacao.nomeUnidade}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_chave}"/>
                                    </f:facet>
                                    <h:outputText value="#{feriadoRedeEmpresaReplicacao.chave}"/>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value=""/>
                                    </f:facet>
                                    <h:panelGroup>
                                        <a4j:commandButton id="replicarPlano"
                                                           reRender="listaEmpresasReplicar, contadorReplicaPlano"
                                                           ajaxSingle="true" immediate="true"
                                                           rendered="#{!feriadoRedeEmpresaReplicacao.dataAtualizacaoInformada}"
                                                           action="#{FeriadoControle.replicarFeriadoRedeEmpresaUnica}"
                                                           value="Replicar"/>
                                        <h:graphicImage url="./images/check.png"
                                                        rendered="#{feriadoRedeEmpresaReplicacao.dataAtualizacaoInformada}"/>
                                    </h:panelGroup>
                                </h:column>
                                <h:column>
                                    <f:facet name="header">
                                        <h:outputText value="#{msg_aplic.prt_PlanoRedeEmpresa_mensagemSituacao}"/>
                                    </f:facet>
                                    <h:outputText value="#{feriadoRedeEmpresaReplicacao.mensagemSituacao}"/>
                                </h:column>
                            </h:dataTable>
                        </h:panelGrid>
                    </h:panelGrid>
                </c:if>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="novo" immediate="true" action="#{FeriadoControle.novo}"  value="#{msg_bt.btn_novo}" title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="5px"/>
                            <a4j:commandButton id="salvar" action="#{FeriadoControle.gravar}" reRender="form" oncomplete="#{FeriadoControle.mensagemNotificar}"  value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <rich:spacer width="5px"/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{FeriadoControle.msgAlert}" action="#{FeriadoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <rich:spacer width="5px"/>
                            <h:commandButton id="consultar" immediate="true" action="#{FeriadoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <rich:spacer width="5px"/>
                            <a4j:commandLink  id="btnlogferiado" action="#{FeriadoControle.realizarConsultaLogObjetoSelecionado}" reRender="form"
                                                oncomplete="#{FeriadoControle.oncompleteLog}"
                                                style="display: inline-block; padding: 8px 15px;"
                                                styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-search"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:codigo").focus();
</script>
