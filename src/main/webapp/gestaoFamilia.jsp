<%--
  Created by IntelliJ IDEA.
  User: anderson
  Date: 22/01/19
  Time: 20:44
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/css_bi_1.4.css" rel="stylesheet" type="text/css"/>

<%@include file="/includes/imports.jsp" %>
<script src="./script/gestao_Turma.js" type="text/javascript"></script>
<head>

    <jsp:include page="include_head.jsp" flush="true"/>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
    <link href="../css/telaCliente.css" rel="stylesheet" type="text/css"/>
    <link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
    <link href="../beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
    <link href="../beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>

</head>

<style type="text/css">
    .textoCarregando {
        margin-left: 0px !important;
    }

    .gr-totalizador {
        width: calc(100% / 4 - 1px);
        height: 150px;
        font-size: 23px;
    }

</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Famílias"/>
    </title>
    <h:form id="form">
        <a4j:keepAlive beanName="GestaoTurmaControle"/>
        <html>
        <body>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup id="panelConteudo">


                                <h:panelGroup layout="block" styleClass="container-box zw_ui especial " id="caixabifamilia">
                                    <h:panelGroup styleClass="container-box-header" layout="block">
                                        <h:panelGroup layout="block" styleClass="margin-box">
                                            <h:panelGrid columns="2" style="width: 100%;">
                                                <h:panelGroup>
                                                    <h:outputText value="Gestão de Famílias"
                                                                  style="float: left; margin-top: 13px"
                                                                  styleClass="container-header-titulo"/>
                                                    <h:outputLink styleClass="linkWiki"
                                                                  style="float: left; margin-top: 13px"
                                                                  value="#{SuperControle.urlBaseConhecimento}para-que-serve-o-gestao-de-turma/"
                                                                  title="Clique e saiba mais: Gestão de Famílias" target="_blank">
                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                    </h:outputLink>
                                                </h:panelGroup>

                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" id="painelSelecionados" styleClass="margin-box">
                                        <h:panelGroup layout="block" styleClass="gr-container-totalizador">
                                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                                <h:outputText style="display: block;"
                                                              styleClass="bi-font-family bi-table-text"
                                                              value="Cadastradas"/>
                                                <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                                                 id="btnfamiliatodas"
                                                                 oncomplete="abrirPopup('familias.jsp', 'Risco', 1024, 595);"
                                                                 action="#{BIFamiliaControle.familias}"
                                                                 value="#{BIFamiliaControle.bi.cadastradas}"/>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                                <h:outputText style="display: block;"
                                                              styleClass="bi-font-family bi-table-text" value="Ativas"/>
                                                <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                                                 id="btfamiliaativas"
                                                                 action="#{BIFamiliaControle.familiasAtivas}"
                                                                 oncomplete="abrirPopup('familias.jsp', 'Risco', 1024, 595);"
                                                                 value="#{BIFamiliaControle.bi.ativas}"/>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                                <h:outputText style="display: block;"
                                                              styleClass="bi-font-family bi-table-text"
                                                              value="Ativas com risco"/>
                                                <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                                                 action="#{BIFamiliaControle.familiasRisco}"
                                                                 oncomplete="abrirPopup('familias.jsp', 'Risco', 1024, 595);"
                                                                 value="#{BIFamiliaControle.bi.risco}"/>
                                            </h:panelGroup>

                                            <h:panelGroup layout="block" styleClass="gr-totalizador">
                                                <h:outputText style="display: block;"
                                                              styleClass="bi-font-family bi-table-text"
                                                              value="Ativas com integrante inativo"/>
                                                <a4j:commandLink styleClass="gr-totalizador-text bi-font-family"
                                                                 action="#{BIFamiliaControle.familiasInativo}"
                                                                 oncomplete="abrirPopup('familias.jsp', 'Risco', 1024, 595);"
                                                                 value="#{BIFamiliaControle.bi.comInativo}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <div style="border-bottom: 1px solid #E5E5E5;">
                                            <div class="slide_wrapper"
                                                 style="height: auto;width: 50%; display: inline-block; border-right: 1px solid #E5E5E5;margin-top: 10px;">
                                                <div class="item" style="height: auto; margin-bottom: 30px"><span
                                                        class="mesmafamilia">Maior valor de contratos ativos</span>
                                                    <span class="causa">Juntos os contratos somam R$ <h:outputText
                                                            value="#{BIFamiliaControle.bi.valor}"><f:converter
                                                            converterId="FormatadorNumerico"/></h:outputText></span>
                                                    <h:panelGroup layout="block" styleClass="pessoas">
                                                        <a4j:repeat value="#{BIFamiliaControle.bi.maiorValor.integrantes}"
                                                                   var="i">
                                                            <h:panelGroup layout="block"
                                                                          styleClass="pessoa #{fn:length(BIFamiliaControle.bi.maiorValor.integrantes) eq 3 ? 'tres' : fn:length(BIFamiliaControle.bi.maiorValor.integrantes) gt 3 ? 'quatro' : ''}">
                                                                <h:panelGroup styleClass="foto">
                                                                    <h:graphicImage value="#{i.urlFoto}"/>
                                                                </h:panelGroup>
                                                                <span class="nome">
                                                                    <h:outputText style="text-transform: capitalize" value="#{i.nome}"/>
                                                                </span>
                                                            </h:panelGroup>
                                                        </a4j:repeat>
                                                    </h:panelGroup>

                                                </div>

                                            </div>


                                            <div class="slide_wrapper"
                                                 style="height: auto; width: 49%; display: inline-block;">
                                                <div class="item" style="height: auto; margin-bottom: 30px"><span
                                                        class="mesmafamilia">Ativa com mais acessos</span>
                                                    <span class="causa">São <h:outputText value="#{BIFamiliaControle.bi.acessos}"></h:outputText> acessos desde <h:outputText value="#{BIFamiliaControle.bi.acessosDesde}"><f:convertDateTime pattern="dd/MM/yyyy" /></h:outputText> </span>
                                                    <h:panelGroup layout="block" styleClass="pessoas">
                                                        <a4j:repeat value="#{BIFamiliaControle.bi.maisAcessos.integrantes}"
                                                                    var="i">
                                                            <h:panelGroup layout="block"
                                                                          styleClass="pessoa #{fn:length(BIFamiliaControle.bi.maisAcessos.integrantes) eq 3 ? 'tres' : fn:length(BIFamiliaControle.bi.maisAcessos.integrantes) gt 3 ? 'quatro' : ''}">
                                                                <h:panelGroup styleClass="foto">
                                                                    <h:graphicImage value="#{i.urlFoto}"/>
                                                                </h:panelGroup>
                                                                <span class="nome">
                                                                    <h:outputText style="text-transform: capitalize" value="#{i.nome}"/>
                                                                </span>
                                                            </h:panelGroup>
                                                        </a4j:repeat>
                                                    </h:panelGroup>

                                                </div>

                                            </div>
                                        </div>

                                        <div style="border-bottom: 1px solid #E5E5E5;">
                                            <div class="slide_wrapper"
                                                 style="height: auto;width: 50%; display: inline-block; border-right: 1px solid #E5E5E5;margin-top: 10px;">
                                                <div class="item" style="height: auto; margin-bottom: 30px"><span
                                                        class="mesmafamilia">Ativa há mais tempo</span>
                                                    <span class="causa">Ativa desde <h:outputText value="#{BIFamiliaControle.bi.desde}"><f:convertDateTime pattern="dd/MM/yyyy" /></h:outputText> </span>
                                                    <h:panelGroup layout="block" styleClass="pessoas">
                                                        <a4j:repeat value="#{BIFamiliaControle.bi.maisTempo.integrantes}"
                                                                    var="i">
                                                            <h:panelGroup layout="block"
                                                                          styleClass="pessoa #{fn:length(BIFamiliaControle.bi.maisTempo.integrantes) eq 3 ? 'tres' : fn:length(BIFamiliaControle.bi.maisTempo.integrantes) gt 3 ? 'quatro' : ''}">
                                                                <h:panelGroup styleClass="foto">
                                                                    <h:graphicImage value="#{i.urlFoto}"/>
                                                                </h:panelGroup>
                                                                <span class="nome">
                                                                    <h:outputText style="text-transform: capitalize" value="#{i.nome}"/>
                                                                </span>
                                                            </h:panelGroup>
                                                        </a4j:repeat>
                                                    </h:panelGroup>

                                                </div>

                                            </div>


                                            <div class="slide_wrapper"
                                                 style="height: auto; width: 49%; display: inline-block">
                                                <div class="item" style="height: auto; margin-bottom: 30px"><span
                                                        class="mesmafamilia">Ativa mais recente</span>
                                                    <span class="causa">O contrato mais recente inicia em <h:outputText value="#{BIFamiliaControle.bi.recente}"><f:convertDateTime pattern="dd/MM/yyyy" /></h:outputText></span>
                                                    <h:panelGroup layout="block" styleClass="pessoas">
                                                        <a4j:repeat value="#{BIFamiliaControle.bi.maisRecente.integrantes}"
                                                                    var="i">
                                                            <h:panelGroup layout="block"
                                                                          styleClass="pessoa #{fn:length(BIFamiliaControle.bi.maisRecente.integrantes) eq 3 ? 'tres' : fn:length(BIFamiliaControle.bi.maisRecente.integrantes) gt 3 ? 'quatro' : ''}">
                                                                <h:panelGroup styleClass="foto">
                                                                    <h:graphicImage value="#{i.urlFoto}"/>
                                                                </h:panelGroup>
                                                                <span class="nome">
                                                                    <h:outputText style="text-transform: capitalize" value="#{i.nome}"/>
                                                                </span>
                                                            </h:panelGroup>
                                                        </a4j:repeat>
                                                    </h:panelGroup>

                                                </div>

                                            </div>
                                        </div>

                                        <jsp:include page="includes/include_sugestao_familia.jsp"/>

                                    </h:panelGroup>
                                </h:panelGroup>


                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        </body>
        </html>

    </h:form>

    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
</f:view>
