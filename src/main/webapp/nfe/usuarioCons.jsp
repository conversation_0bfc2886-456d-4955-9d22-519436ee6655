<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>

<head><script type="text/javascript" language="javascript" src="../script/script.js"></script></head>

<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gerenciador de Usuários"/>
    </title>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzidoNFe.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:panelGrid columns="1" footerClass="colunaCentralizada" width="100%">
                <h:panelGrid columns="1"
                             style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="Gerenciador de Usuários">
                        <%--<h:outputLink value="#{SuperControle.urlWiki}Cadastros:Cadastros_Auxiliares:Grupo"--%>
                        <%--title="Clique e saiba mais: Grupo com Desconto" target="_blank"--%>
                        <%--rendered="true">--%>
                        <%--<h:graphicImage styleClass="linkWiki" url="../imagens/wiki_bco.gif"/>--%>
                        <%--</h:outputLink>--%>
                    </h:outputText>
                </h:panelGrid>
                <h:panelGrid columns="4" width="100%">
                    <h:outputText styleClass="tituloCampos" value="Consultar por: "/>
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                     id="consulta" required="true"
                                     value="#{UsuarioNFeControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{UsuarioNFeControle.tipoConsultaCombo}"/>
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form" value="#{UsuarioNFeControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes" value="Consultar"
                                     image="../imagens/botaoConsultar.png"
                                     alt="Consultar Dados"
                                     action="#{UsuarioNFeControle.irPaginaInicial}"/>
                </h:panelGrid>

                <h:panelGroup id="listagemDeUsuarios">
                    <rich:dataTable id="items" width="100%" headerClass="consulta"
                                    rowClasses="linhaPar, linhaImpar "
                                    columnClasses="centralizado, centralizado, centralizado,
                                    centralizado, centralizado, centralizado"
                                    value="#{UsuarioNFeControle.listaConsulta}"
                                    rows="10" var="usuario">
                        <%--rendered="#{UsuarioNFeControle.apresentarResultadoConsulta}"--%>

                        <rich:column sortBy="#{usuario.id_Usuario}">
                            <f:facet name="header">
                                <h:outputText value="Código"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText value="#{usuario.id_Usuario}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column sortBy="#{usuario.nome}">
                            <f:facet name="header">
                                <h:outputText value="Nome"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText value="#{usuario.nome}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column sortBy="#{usuario.usuario}">
                            <f:facet name="header">
                                <h:outputText value="Usuário"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText value="#{usuario.usuario}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column sortBy="#{usuario.status}">
                            <f:facet name="header">
                                <h:outputText value="Situaçao"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText rendered="#{usuario.status}" value="Ativo"/>
                                <h:outputText rendered="#{!usuario.status}" value="Inativo"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column sortBy="#{usuario.perfilUsuarioNFe.nomePerfilUsuario}">
                            <f:facet name="header">
                                <h:outputText value="Perfil"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText value="#{usuario.perfilUsuarioNFe.nomePerfilUsuario}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column sortBy="#{usuario.perfilUsuarioNFe.empresa.nomeFantasia}">
                            <f:facet name="header">
                                <h:outputText value="Empresa"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:outputText value="#{usuario.perfilUsuarioNFe.empresa.nomeFantasia}"/>
                            </h:panelGroup>
                        </rich:column>

                        <rich:column>
                            <f:facet name="header">
                                <h:outputText value="Opções"/>
                            </f:facet>
                            <h:panelGroup>
                                <h:commandButton action="#{UsuarioNFeControle.editar}" value="#{msg_bt.btn_editar}"
                                                 image="../imagens/botaoEditar.png" alt="#{msg.msg_editar_dados}"
                                                 styleClass="botoes"/>
                            </h:panelGroup>
                        </rich:column>
                    </rich:dataTable>
                </h:panelGroup>

                <h:panelGrid width="100%" footerClass="colunaCentralizada">
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{UsuarioNFeControle.apresentarResultadoConsulta}"
                                      binding="#{UsuarioNFeControle.apresentarLinha}" layout="center">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false"
                                           binding="#{UsuarioNFeControle.apresentarPrimeiro}"
                                           action="#{UsuarioNFeControle.irPaginaInicial}"/>
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false"
                                           binding="#{UsuarioNFeControle.apresentarAnterior}"
                                           action="#{UsuarioNFeControle.irPaginaAnterior}"/>
                            <h:outputText styleClass="tituloCampos"
                                          value="#{msg_aplic.prt_msg_pagina} #{UsuarioNFeControle.paginaAtualDeTodas}"
                                          rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false"
                                           binding="#{UsuarioNFeControle.apresentarPosterior}"
                                           action="#{UsuarioNFeControle.irPaginaPosterior}"/>
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false"
                                           binding="#{UsuarioNFeControle.apresentarUltimo}"
                                           action="#{UsuarioNFeControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{UsuarioNFeControle.sucesso}" image="../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{UsuarioNFeControle.erro}" image="../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{UsuarioNFeControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{UsuarioNFeControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{UsuarioNFeControle.novo}" value="#{msg_bt.btn_novo}"
                                         styleClass="botoes" image="../imagens/botaoNovo.png"
                                         alt="#{msg.msg_novo_dados}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>
<script type="text/javascript">
    document.getElementById("form:valorConsulta").focus();
</script>