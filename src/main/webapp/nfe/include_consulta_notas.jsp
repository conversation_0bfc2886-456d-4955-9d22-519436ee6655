<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="../includes/imports.jsp" %>

<style type="text/css">
    .rich-table-row:hover {
        background-color: #6AFF98;
    }

    .row-selected {
        background-color: #6ABCFB;
    }
</style>


<h:panelGroup rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
    <h:outputText styleClass="text" value="Ordenar por:"/>

    <h:selectOneMenu style="margin-bottom: 10px;" id="ordenador" value="#{NotaFiscalDeServicoControle.ordenador}">
        <f:selectItems value="#{NotaFiscalDeServicoControle.listaOrdenador}"/>
        <a4j:support event="onchange" action="#{NotaFiscalDeServicoControle.consultar}"
                     reRender="gerenciadorNotas"/>
    </h:selectOneMenu>
</h:panelGroup>

<h:panelGroup layout="block" style="padding: 0 0 0 0; float:right;vertical-align:middle;">
    <c:choose>
        <c:when test="${LoginControle.usuarioLogado.administrador}">
            <h:outputText value="Empresa:" style="vertical-align:top;" styleClass="text"/>
            <h:selectOneMenu
                    style="width:220px;vertical-align:top; 'background-color: #D2D2D2; color: #666666;'"
                    onblur="blurinput(this);"
                    onfocus="focusinput(this);"
                    id="listaEmpresasCadastradas"
                    title="Empresa para Consultar"
                    value="#{NotaFiscalDeServicoControle.filtro.idEmpresa}">
                <f:selectItems value="#{NotaFiscalDeServicoControle.listaSelectEmpresasCadastradas}"/>
            </h:selectOneMenu>
            <a4j:commandButton
                    style="vertical-align:top;margin-bottom: 10px;"
                    reRender="listagemDeNotas, preVisualizar, totalizadores, listagemDeNotasNFCe, pnlAcoes"
                    value="Pesquisar" title="Pesquisar"
                    action="#{NotaFiscalDeServicoControle.filtrar}">
            </a4j:commandButton>
        </c:when>
        <c:otherwise>
            <h:outputText
                    value="Vencimento certificado: #{LoginControle.empresaNFe.dataVencimentoCertificado_Apresentar}"
                    styleClass="tituloCamposDestaque12"
                    rendered="#{LoginControle.empresaNFe.municipio.requerCertificado}"
                    style="font-weight: bold; padding-right: 15px;"/>
            <h:outputText value="#{LoginControle.empresa.razaoSocial}" styleClass="tituloCamposDestaque12"
                          style="font-weight: bold;"/>
        </c:otherwise>
    </c:choose>
</h:panelGroup>
<!-- Esse cara é responsável pelo menu superior Direito!-->

<%-- LISTAGEM DE NOTAS NFS-e --%>
<h:panelGroup id="listagemDeNotas" rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 0}">
    <rich:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaPar, linhaImpar "
                    columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, centralizado,
                    centralizado, centralizado, centralizado"
                    value="#{NotaFiscalDeServicoControle.listaConsulta}" rows="30" var="nota">

        <rich:column>
            <f:facet name="header">
                <h:selectBooleanCheckbox value="#{NotaFiscalDeServicoControle.marcarTodos}">
                    <a4j:support event="onclick" action="#{NotaFiscalDeServicoControle.marcarTodosItens}"
                                 reRender="listagemDeNotas"/>
                </h:selectBooleanCheckbox>
            </f:facet>
            <h:selectBooleanCheckbox value="#{nota.marcado}"/>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="ID Lote"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.idLote}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Série RPS"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.serieRPS}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Nº RPS"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.numeroRPS}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Nº Nota"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.numeroNota}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Razão Social do Cliente"/>
            </f:facet>
            <h:panelGroup layout="block" id="panelNota${nota.idRPS}" style="display: -webkit-inline-box;">
                <h:panelGroup layout="block" id="nome${nota.idRPS}">
                    <h:outputText value="#{nota.razaoSocialCons}"/>
                </h:panelGroup>
                <h:panelGroup layout="block" rendered="#{nota.apresentarNomeAluno}" id="resp${nota.idRPS}" style="margin-left: 5px;">
                    <i class="fa-icon-info-circle"
                       title="Nota emitida no nome do responsável. Clique na Lupa em 'Executar Ação' para consultar o aluno e informações da nota."></i>
                </h:panelGroup>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Dt. Emissão"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.dataEmissao}">
                    <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                       timeZone="America/Sao_Paulo"/>
                </h:outputText>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Valor"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="R$ "/>
                <h:outputText value="#{nota.valorServicos}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Status"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText styleClass="#{nota.classeDoStatus}" rendered="#{!nota.notaNaoAutorizada}"
                              value="#{nota.status}"/>
                <a4j:commandLink id="linkStatusMotivo" style="margin-left: 8px" reRender="modalMotivo, mensagemNFeErro"
                                 rendered="#{nota.notaNaoAutorizada}"
                                 value="#{nota.status}" styleClass="#{nota.classeDoStatus}"
                                 actionListener="#{NotaFiscalDeServicoControle.prepararMotivoDaNota}"
                                 oncomplete="#{rich:component('modalMotivo')}.show();" title="Visualizar Motivo">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Dt. Processamento"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.dataProcessamento}">
                    <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                       timeZone="America/Sao_Paulo"/>
                </h:outputText>
            </h:panelGroup>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Email Enviado"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.emailFoiEnviado}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Executar Ação"/>
            </f:facet>
            <h:panelGroup>

                <a4j:commandButton id="btnReenvioNfse" style="margin-left: 8px"
                                   rendered="#{nota.notaNaoAutorizada && nota.idReenvio == 0 && !nota.excluido}"
                                   action="#{NotaFiscalDeServicoControle.prepararReenvioNfse}"
                                   image="./imagens/nfe/reenviar_nota.png" styleClass="botoes"
                                   title="Reenviar Nota"
                                   reRender="modalReenvioNfse, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalReenvioNfse')}.show();"/>

                <a4j:commandButton id="btnVisualizarNota" style="margin-left: 8px"
                                   action="#{NotaFiscalDeServicoControle.visualizarNota}"
                                   image="./imagens/nfe/visualizar_nota.png" styleClass="botoes"
                                   title="Visualizar Nota"
                                   reRender="modalVisualizar, visualizacaoRapida, visualizacaoRapida_descricao,
                                   itens, preVisualizar, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalVisualizar')}.show();"/>

                <h:commandButton id="btnImprimirNota" style="margin-left:8px;"
                                 rendered="#{nota.notaEstaAutorizada && !nota.existeDadosImpressao}"
                                 actionListener="#{NotaFiscalDeServicoControle.downloadPDF}"
                                 title="Download do PDF da Nota"
                                 image="./imagens/nfe/imprimir_pdf.png">
                </h:commandButton>

                <h:outputLink
                        id="btnImprimirNotaWeb"
                        rendered="#{nota.notaEstaAutorizada && nota.existeDadosImpressao}"
                        value="#{nota.linkDownloadPDF}"
                        title="Download do PDF da Nota"
                        target="_blank">
                    <h:graphicImage value="/imagens/nfe/imprimir_pdf.png"
                                    style="margin-left:8px;"/>
                </h:outputLink>

                <h:commandButton id="btnDownloadXml" style="margin-left:8px;"
                                 actionListener="#{NotaFiscalDeServicoControle.downloadXML}" immediate="true"
                                 title="Download do XML da Nota"
                                 image="./imagens/nfe/xml-2.png"/>

                <h:commandButton id="btnDownloadXMLNFSeCancelamento" style="margin-left:8px;"
                                 rendered="#{nota.notaEstaCancelada}"
                                 actionListener="#{NotaFiscalDeServicoControle.downloadXMLNFSeCancelamento}" immediate="true"
                                 title="Download do XML de Cancelamento da Nota"
                                 image="./imagens/nfe/xml-cancelamento.png"/>

                <a4j:commandButton id="btnAtualizarStatus" style="margin-left: 8px"
                                   action="#{NotaFiscalDeServicoControle.atualizarNota}"
                                   image="./imagens/nfe/atualiza_status.png" styleClass="botoes"
                                   title="Sincronizar Nota com o Banco de Dados"
                                   reRender="listagemDeNotas, mensagemNFeErro"/>

                <a4j:commandButton id="btnCancelarNota" style="margin-left: 8px"
                                   rendered="#{(LoginControle.usuarioLogado.administrador
                                               || LoginControle.perfilNFe.permiteCancelarNota)
                                               && nota.notaEstaAutorizada && nota.podeCancelarNota}"
                                   image="./imagens/nfe/excluir_nota.png" styleClass="botoes"
                                   reRender="modalJustificativa, mensagemNFeErro"
                                   actionListener="#{NotaFiscalDeServicoControle.prepararNotaParaCancelar}"
                                   title="Solicitar Cancelamento da Nota"
                                   oncomplete="#{rich:component('modalJustificativa')}.show();">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandButton>

                <a4j:commandButton id="btnEnviarEmail" style="margin-left: 8px"
                                   rendered="#{nota.status == 'Autorizado'}"
                                   reRender="modalEmail, mensagemNFeErro"
                                   actionListener="#{NotaFiscalDeServicoControle.prepararNotaParaEnvialEmail}"
                                   oncomplete="#{rich:component('modalEmail')}.show();"
                                   title="Solicitar Envio de Email"
                                   image="./imagens/nfe/enviar_nota.png" styleClass="botoes">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandButton>

                <a4j:commandButton id="btnHistoricoNota" style="margin-left: 8px"
                                   reRender="modalHistorico, mensagemNFeErro"
                                   actionListener="#{NotaFiscalDeServicoControle.monteHistoricoDaNota}"
                                   oncomplete="#{rich:component('modalHistorico')}.show();"
                                   title="Histórico da Nota"
                                   image="./imagens/nfe/historico_nota.png" styleClass="botoes">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandButton>

                <a4j:commandButton id="btnLogNota" style="margin-left: 8px"
                                   value="Log"
                                   reRender="modalLogNota, mensagemNFeErro"
                                   rendered="#{LoginControle.usuarioLogado.administrador}"
                                   actionListener="#{NotaFiscalDeServicoControle.monteLogDaNota}"
                                   oncomplete="#{rich:component('modalLogNota')}.show();"
                                   title="Log da Nota"
                                   styleClass="botoes">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandButton>
            </h:panelGroup>
        </rich:column>
    </rich:dataTable>

    <rich:jQuery selector="#items tbody tr:even"
                 query="click(function(){jQuery(this).siblings().removeClass('row-selected');jQuery(this).removeClass('linhaPar');jQuery(this).addClass('row-selected')})"/>
    <rich:jQuery selector="#items tbody tr:odd"
                 query="click(function(){jQuery(this).siblings().removeClass('row-selected');jQuery(this).removeClass('linhaImpar');jQuery(this).addClass('row-selected')})"/>

    <h:panelGrid id="paginacaoNotas" width="100%" footerClass="colunaCentralizada">
        <f:facet name="footer">
            <h:panelGroup rendered="#{NotaFiscalDeServicoControle.apresentarResultadoConsulta}"
                          binding="#{NotaFiscalDeServicoControle.apresentarLinha}" layout="center">
                <a4j:commandLink styleClass="tituloCampos" value="  <<  "
                                 rendered="#{NotaFiscalDeServicoControle.primeiraPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaInicial}"
                                 reRender="listagemDeNotas, conteudo, mensagemNFeErro"/>
                <a4j:commandLink styleClass="tituloCampos" value="  <  "
                                 rendered="#{NotaFiscalDeServicoControle.voltarPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaAnterior}"
                                 reRender="listagemDeNotas, conteudo, mensagemNFeErro"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{msg_aplic.prt_msg_pagina} #{NotaFiscalDeServicoControle.paginacao}"
                              rendered="true"/>
                <h:outputText styleClass="tituloCampos" style="margin-left: 5px"
                              value="[Total: #{NotaFiscalDeServicoControle.totalDeNotas}]"
                              rendered="true"/>
                <a4j:commandLink styleClass="tituloCampos" value="  >  "
                                 rendered="#{NotaFiscalDeServicoControle.proximaPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaPosterior}"
                                 reRender="listagemDeNotas, conteudo, mensagemNFeErro"/>
                <a4j:commandLink styleClass="tituloCampos" value="  >>  "
                                 rendered="#{NotaFiscalDeServicoControle.ultimaPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaFinal}"
                                 reRender="listagemDeNotas, conteudo, mensagemNFeErro"/>
            </h:panelGroup>
        </f:facet>
    </h:panelGrid>
</h:panelGroup>

<%-- LISTAGEM DE NOTAS NFC-e --%>
<h:panelGroup id="listagemDeNotasNFCe" rendered="#{NotaFiscalDeServicoControle.filtro.tipoNota == 1}">
    <rich:dataTable id="itemsNFCe" width="100%" headerClass="consulta" rowClasses="linhaPar, linhaImpar"
                    columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado, centralizado, centralizado, centralizado, centralizado"
                    value="#{NotaFiscalDeServicoControle.listaConsulta}" rows="30" var="nota">

        <rich:column>
            <f:facet name="header">
                <h:selectBooleanCheckbox value="#{NotaFiscalDeServicoControle.marcarTodos}">
                    <a4j:support event="onclick" action="#{NotaFiscalDeServicoControle.marcarTodosItens}"
                                 reRender="listagemDeNotasNFCe"/>
                </h:selectBooleanCheckbox>
            </f:facet>
            <h:selectBooleanCheckbox value="#{nota.marcado}"/>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="ID_NFCe"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.id_NFCe}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Número Envio"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.numeroEnvio}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Razão Social do Cliente"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.destNome}"/>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Dt. Emissão"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="#{nota.dataHoraEmissao}">
                    <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                       timeZone="America/Sao_Paulo"/>
                </h:outputText>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Status"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText styleClass="#{nota.classeDoStatus}" rendered="#{!nota.notaNaoAutorizada}"
                              value="#{nota.statusApresentar}"/>
                <a4j:commandLink id="linkStatusMotivo" style="margin-left: 8px" reRender="modalMotivo, mensagemNFeErro"
                                 rendered="#{nota.notaNaoAutorizada}"
                                 value="#{nota.statusApresentar}" styleClass="#{nota.classeDoStatus}"
                                 actionListener="#{NotaFiscalDeServicoControle.prepararMotivoDaNFCe}"
                                 oncomplete="#{rich:component('modalMotivo')}.show();" title="Visualizar Motivo">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandLink>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Valor Total"/>
            </f:facet>
            <h:panelGroup>
                <h:outputText value="R$ "/>
                <h:outputText value="#{nota.valorTotal}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </h:panelGroup>
        </rich:column>

        <rich:column filterEvent="onkeyup">
            <f:facet name="header">
                <h:outputText value="Inutilizada"/>
            </f:facet>
            <h:panelGroup>
                <h:commandButton id="btnDownloadXmlInutilizadaNFCe" style="margin-left:8px;"
                                 rendered="#{nota.inutilizado}"
                                 actionListener="#{NotaFiscalDeServicoControle.downloadXMLInutilizadaNFCe}" immediate="true"
                                 title="Download do XML da NFC-e Inutilizada"
                                 image="./imagens/nfe/xml-2.png"/>

                <a4j:commandButton id="btnLogInutilizadaNFCe" style="margin-left: 8px"
                                   value="Log"
                                   rendered="#{nota.qtdTentativaInutilizado > 0}"
                                   reRender="modalLogInutilizar, mensagemNFeErro"
                                   actionListener="#{NotaFiscalDeServicoControle.monteLogInutilizarNFCE}"
                                   oncomplete="#{rich:component('modalLogInutilizar')}.show();"
                                   title="Log das Tentativas de Inutilizar a NFC-e"
                                   styleClass="botoes">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandButton>
            </h:panelGroup>
        </rich:column>

        <rich:column>
            <f:facet name="header">
                <h:outputText value="Executar Ação"/>
            </f:facet>
            <h:panelGroup>

                <a4j:commandButton id="btnReenvioNFCe" style="margin-left: 8px"
                                   rendered="#{nota.notaNaoAutorizada && nota.idReenvio == 0}"
                                   action="#{NotaFiscalDeServicoControle.prepararReenvioNFCe}"
                                   image="./imagens/nfe/reenviar_nota.png" styleClass="botoes"
                                   title="Reenviar NFCe"
                                   reRender="modalReenvioNFCe, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalReenvioNFCe')}.show();"/>

                <a4j:commandButton id="btnVisualizarNFCe" style="margin-left: 8px"
                                   action="#{NotaFiscalDeServicoControle.visualizarNFCe}"
                                   image="./imagens/nfe/visualizar_nota.png" styleClass="botoes"
                                   title="Visualizar NFC-e"
                                   reRender="modalVisualizarNFCe, visualizacaoRapida, visualizacaoRapida_descricao,
                                   itens, preVisualizar, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalVisualizarNFCe')}.show();"/>

                <h:outputLink
                        id="btnImprimirNFCeWeb"
                        rendered="#{nota.notaEstaAutorizada}"
                        value="#{nota.linkDownloadPDF}"
                        title="Download do PDF da NFC-e"
                        target="_blank">
                    <h:graphicImage value="/imagens/nfe/imprimir_pdf.png"
                                    style="margin-left:8px;"/>
                </h:outputLink>

                <h:commandButton id="btnDownloadXmlNFCe" style="margin-left:8px;"
                                 rendered="#{nota.notaEstaAutorizada}"
                                 actionListener="#{NotaFiscalDeServicoControle.downloadXMLNFCe}" immediate="true"
                                 title="Download do XML da NFC-e"
                                 image="./imagens/nfe/xml-2.png"/>

                <h:commandButton id="btnDownloadXMLNFCeCancelamento" style="margin-left:8px;"
                                 rendered="#{nota.notaEstaCancelada}"
                                 actionListener="#{NotaFiscalDeServicoControle.downloadXMLNFCeCancelamento}" immediate="true"
                                 title="Download do XML de Cancelamento da NFC-e"
                                 image="./imagens/nfe/xml-cancelamento.png"/>

                <a4j:commandButton id="btnCancelarNota" style="margin-left: 8px"
                                   rendered="#{(LoginControle.usuarioLogado.administrador || LoginControle.perfilNFe.permiteCancelarNota) && nota.notaEstaAutorizada && nota.podeCancelarNota}"
                                   image="./imagens/nfe/excluir_nota.png" styleClass="botoes"
                                   reRender="modalJustificativa, mensagemNFeErro"
                                   actionListener="#{NotaFiscalDeServicoControle.prepararNFCeParaCancelar}"
                                   title="Solicitar Cancelamento da NFCe"
                                   oncomplete="#{rich:component('modalJustificativaNFCe')}.show();">
                    <f:attribute name="nota" value="#{nota}"/>
                </a4j:commandButton>

                <a4j:commandButton id="btnAtualizarStatus" style="margin-left: 8px"
                                   action="#{NotaFiscalDeServicoControle.atualizarNFCe}"
                                   image="./imagens/nfe/atualiza_status.png" styleClass="botoes"
                                   title="Sincronizar Nota com o Banco de Dados"
                                   reRender="listagemDeNotas, mensagemNFeErro"/>


            </h:panelGroup>
        </rich:column>


    </rich:dataTable>

    <rich:jQuery selector="#items tbody tr:even"
                 query="click(function(){jQuery(this).siblings().removeClass('row-selected');jQuery(this).removeClass('linhaPar');jQuery(this).addClass('row-selected')})"/>
    <rich:jQuery selector="#items tbody tr:odd"
                 query="click(function(){jQuery(this).siblings().removeClass('row-selected');jQuery(this).removeClass('linhaImpar');jQuery(this).addClass('row-selected')})"/>

    <h:panelGrid id="paginacaoNotasNFCe" width="100%" footerClass="colunaCentralizada">
        <f:facet name="footer">
            <h:panelGroup rendered="#{NotaFiscalDeServicoControle.apresentarResultadoConsulta}" layout="center">
                <a4j:commandLink styleClass="tituloCampos" value="  <<  "
                                 rendered="#{NotaFiscalDeServicoControle.primeiraPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaInicial}"
                                 reRender="listagemDeNotasNFCe, conteudo, mensagemNFeErro"/>
                <a4j:commandLink styleClass="tituloCampos" value="  <  "
                                 rendered="#{NotaFiscalDeServicoControle.voltarPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaAnterior}"
                                 reRender="listagemDeNotasNFCe, conteudo, mensagemNFeErro"/>
                <h:outputText styleClass="tituloCampos"
                              value="#{msg_aplic.prt_msg_pagina} #{NotaFiscalDeServicoControle.paginacao}"
                              rendered="true"/>
                <h:outputText styleClass="tituloCampos" style="margin-left: 5px"
                              value="[Total: #{NotaFiscalDeServicoControle.totalDeNotas}]"
                              rendered="true"/>
                <a4j:commandLink styleClass="tituloCampos" value="  >  "
                                 rendered="#{NotaFiscalDeServicoControle.proximaPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaPosterior}"
                                 reRender="listagemDeNotasNFCe, conteudo, mensagemNFeErro"/>
                <a4j:commandLink styleClass="tituloCampos" value="  >>  "
                                 rendered="#{NotaFiscalDeServicoControle.ultimaPagina}"
                                 action="#{NotaFiscalDeServicoControle.irPaginaFinal}"
                                 reRender="listagemDeNotasNFCe, conteudo, mensagemNFeErro"/>
            </h:panelGroup>
        </f:facet>
    </h:panelGrid>
</h:panelGroup>

<h:panelGrid id="totalizadores" columns="4" width="100%" style="text-align:center;">

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasCancelado > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasCancelado} - Notas Canceladas"/>
        <br/>
        <h:outputText styleClass="tituloCamposVermelhoGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorCancelado_Apresentar}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasNaoAutorizado > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasNaoAutorizado} - Notas Não Autorizadas"/>
        <br/>
        <h:outputText styleClass="tituloCamposGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorNaoAutorizado_Apresentar}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasAutorizado > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasAutorizado} - Notas Autorizadas"/>
        <br/>
        <h:outputText styleClass="tituloCamposVerdeGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorAutorizado_Apresentar}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasEnviando > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasEnviando} - Notas Enviando"/>
        <br/>
        <h:outputText styleClass="tituloCamposGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorEnviando_Apresentar}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasProcessando > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasProcessando} - Notas Processando"/>
        <br/>
        <h:outputText styleClass="tituloCamposGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorProcessando_Apresentar}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasCancelando > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasCancelando} - Notas Cancelando"/>
        <br/>
        <h:outputText styleClass="tituloCamposGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorCancelando_Apresentar}"/>
    </h:panelGroup>

    <h:panelGroup layout="block" rendered="#{NotaFiscalDeServicoControle.qtdNotasReenviado > 0}">
        <h:outputText value="#{NotaFiscalDeServicoControle.qtdNotasReenviado} - Notas Reenviadas"/>
        <br/>
        <h:outputText styleClass="tituloCamposGrande"
                      value="#{NotaFiscalDeServicoControle.totalizadorReenviado_Apresentar}"/>
    </h:panelGroup>
</h:panelGrid>

<rich:modalPanel id="alertaLoginDiretoModuloNotas"
                 domElementAttachment="parent"
                 showWhenRendered="#{NotaFiscalDeServicoControle.apresentarAlertaLoginDiretoModuloNotas}"
                 styleClass="novaModal" autosized="true" shadowOpacity="true" width="550" height="150">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Novidade" />
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Configuração para acesso direto ao módulo de notas" />
        </h:panelGrid>
    </h:panelGrid>

    <rich:panel>
        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:outputText value="#{NotaFiscalDeServicoControle.mensagemAlertaLoginDiretoModuloNotas}" />
        </h:panelGrid>

        <h:panelGrid columns="2" width="100%">
            <h:panelGrid columns="2">
                <h:selectBooleanCheckbox style="vertical-align: middle"
                                         value="#{NotaFiscalDeServicoControle.checkNaoMostrarAlertaAcessoDiretoModuloNotas}">
                </h:selectBooleanCheckbox>
                <h:outputText style="margin-left: 5px;color: #777777" value="Não mostrar este aviso novamente" />
            </h:panelGrid>

            <a4j:commandButton id="btnFecharAlertaLoginDiretoModuloNotas"
                               styleClass="botoes nvoBt"
                               value="Ok"
                               action="#{NotaFiscalDeServicoControle.clickOkAlertaAcessoDiretoModuloNotas}"
                               oncomplete="Richfaces.hideModalPanel('alertaLoginDiretoModuloNotas')">
            </a4j:commandButton>

        </h:panelGrid>
    </rich:panel>

</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalVisualizar" autosized="true" width="800"
                 height="300" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Prévisualização da Nota"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkPreview"/>
            <rich:componentControl for="modalVisualizar" attachTo="hidelinkPreview"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <table width="100%" border="0" align="left" cellpadding="0" cellspacing="0"
           style="padding: 10px; background-color: #e6e6e6;">

        <tr>
            <td align="left" valign="top" style="padding-bottom: 5px;">
                <div style="clear: both;" class="text">
                    <p style="margin-bottom: 6px;">
                        <img src="../images/arrow2.gif" width="16" height="16"
                             style="vertical-align: middle; margin-right: 6px;" alt="arrow2">
                        <h:outputText style="font-weight: bold" value="Pré-Visualização"/>

                            <%--<a4j:commandButton id="btnFecharPreVisualizacao"--%>
                            <%--style="float: right;"--%>
                            <%--image="/imagens/close.png"--%>
                            <%--action="#{NotaFiscalDeServicoControle.fecharPreVisualizacao}"--%>
                            <%--reRender="preVisualizar"/>--%>
                    </p>

                    <div class="sep" style="margin: 4px 0 5px 0;">
                        <img src="../images/shim.gif" alt="shim">
                    </div>
                </div>
            </td>
        </tr>

        <tr style="background-color: #FFF">
            <td align="left" valign="top">
                <h:panelGrid id="visualizacaoRapida" columns="2" columnClasses="classEsquerda, classDireita"
                             width="100%" border="0" cellspacing="3" cellpadding="1" styleClass="textsmall">


                    <h:outputText style="font-weight: bold" value="Número RPS:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.numeroRPS}"/>

                    <h:outputText style="font-weight: bold" value="Número da Nota:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.numeroNota}"/>


                    <h:outputText style="font-weight: bold" value="Data de Emissão:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.dataEmissao}">
                        <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                           timeZone="America/Sao_Paulo"/>
                    </h:outputText>

                    <h:outputText style="font-weight: bold" value="Data de Processamento:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.dataProcessamento}">
                        <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                           timeZone="America/Sao_Paulo"/>
                    </h:outputText>

                    <h:outputText style="font-weight: bold" value="Valor Total:"/>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.valorServicos}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>

                    <h:outputText style="font-weight: bold" value="Descrição da Nota:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.descricao}"/>

                    <h:outputText style="font-weight: bold" value="Chave de Acesso:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.codigoVerificacao}"/>

                    <h:outputText style="font-weight: bold" value="CPF/CNPJ:"/>
                    <h:outputText id="cnpjVisualizar" value="#{NotaFiscalDeServicoControle.notaVO.cpfCnpjFormatado}"/>

                    <h:outputText style="font-weight: bold" value="Razão Social do Cliente:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.razaoSocialCons}"/>

                    <h:outputText style="font-weight: bold" value="Nome Aluno:"
                                  rendered="#{NotaFiscalDeServicoControle.notaVO.apresentarNomeAluno}"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.nomeAluno}"
                                  rendered="#{NotaFiscalDeServicoControle.notaVO.apresentarNomeAluno}"/>

                    <h:outputText style="font-weight: bold" value="Telefone:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.telefoneCons}"/>

                    <h:outputText style="font-weight: bold" value="E-mail:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.emailCons}"/>

                    <h:outputText style="font-weight: bold" value="Logradouro:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.logradouroCons}"/>

                    <h:outputText style="font-weight: bold" value="Numero Endereço:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.numeroEnderecoCons}"/>

                    <h:outputText style="font-weight: bold" value="Complemento Endereço:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.complementoEnderecoCons}"/>

                    <h:outputText style="font-weight: bold" value="Bairro:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.bairroCons}"/>

                    <h:outputText style="font-weight: bold" value="CEP:"/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaVO.cepCons}"/>

                </h:panelGrid>
            </td>
        </tr>

        <tr style="background-color: #FFF">
            <td>
                <rich:dataTable id="itens" width="100%" rowClasses="linhaPar, linhaImpar"
                                reRender="paginaAtual, paginaAtualTop, painelPaginacaoTop, painelPaginacao"
                                columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                                rows="30" var="item" value="#{ItemNFSeControle.listaDeItens}">


                    <rich:column filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Ordem"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.ordem}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.descricao}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column filterEvent="onkeyup">
                        <f:facet name="header">
                            <h:outputText value="Quantidade"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.quantidade}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Valor Unitário"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="R$ "/>
                            <h:outputText value="#{item.valorUnitario}">
                                <f:converter converterId="FormatadorNumerico"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Tributável"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{item.tributavel}"/>
                        </h:panelGroup>
                    </rich:column>

                </rich:dataTable>
            </td>
        </tr>
    </table>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalHistorico" autosized="true" width="800"
                 height="300" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Histórico da Nota"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkHistorico"/>
            <rich:componentControl for="modalHistorico" attachTo="hidelinkHistorico"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Histórico da Nota"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelHistoricoNota">
                <rich:dataTable id="operacoesRPS" width="100%" headerClass="consulta"
                                rowClasses="linhaPar, linhaImpar"
                                columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                                value="#{NotaFiscalDeServicoControle.historicoDaNota}"
                                rows="20" var="operacao">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.id_OperacaoRPS}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Usuário"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.nomeDoUsuario}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Data"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.dataHora}">
                                <f:convertDateTime pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Operação"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.operacao}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Descrição"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.descricao}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Observação"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.observacao}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Finalizado"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{operacao.concluido}"/>
                        </h:panelGroup>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalEmail" autosized="true" width="400"
                 height="150" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Solicitar envio de Email"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkEmail"/>
            <rich:componentControl for="modalEmail" attachTo="hidelinkEmail"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Solicitar envio de email"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelSolicitarEmail">

                <h:outputText style="font-size:12px; font-style: italic; font-family: Arial, bold;  text-align: left;"
                              value="O email não será atualizado no sistema que originou a informação"/>

                <h:panelGrid columns="2" columnClasses="classEsquerda, classDireita" width="100%">
                    <h:outputText styleClass="tituloCampos" value="Email:"/>
                    <h:inputText id="emailEnvioEmail" size="60" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{NotaFiscalDeServicoControle.notaParaEnviarEmail.emailConsParaEnvio}"/>
                </h:panelGrid>

                <a4j:commandButton id="botaoSolicitarEnvio"
                                   value="Solicitar o Envio de Email"
                                   action="#{NotaFiscalDeServicoControle.enviarEmail}"
                                   reRender="listagemDeNotas, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalEmail')}.hide();"/>
                <a4j:commandButton id="btnFecharEmail" value="Fechar" onclick="fireElement('hidelinkEmail');"/>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalMotivo" autosized="true" width="400" height="150"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Motivo"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkMotivo"/>
            <rich:componentControl for="modalMotivo" attachTo="hidelinkMotivo" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
            <h:outputText styleClass="tituloFormulario" value="Motivo de não autorização"/>
        </h:panelGrid>

        <h:panelGroup id="panelMotivo" layout="block">
            <rich:panel style="overflow-y: auto; overflow-x: auto; max-height: 400px; max-width: 800px;">
                <h:outputText value="#{NotaFiscalDeServicoControle.motivo}"/>
            </rich:panel>
        </h:panelGroup>
    </h:panelGrid>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalJustificativa" autosized="true" width="600"
                 height="200" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Justificativa"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkJustificativa"/>
            <rich:componentControl for="modalJustificativa" attachTo="hidelinkJustificativa"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Justificativa para Cancelamento de Nota"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelBotaoConfirmarTroca">

                <div>
                    <h:inputText id="justificativa" maxlength="50"
                                 value="#{NotaFiscalDeServicoControle.notaParaCancelar.justificativaParaCancelar}"
                                 style="width: 300px"/>
                </div>

                <a4j:commandButton id="botaoCancelarNota"
                                   value="Cancelar Nota"
                                   action="#{NotaFiscalDeServicoControle.cancelarNota}"
                                   reRender="listagemDeNotas, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalJustificativa')}.hide();"/>
                <a4j:commandButton id="btnFecharT" value="Fechar" onclick="fireElement('hidelinkJustificativa');"/>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalJustificativaNFCe" autosized="true" width="600"
                 height="200" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Justificativa"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkJustificativaNFCe"/>
            <rich:componentControl for="modalJustificativaNFCe" attachTo="hidelinkJustificativaNFCe"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Justificativa para Cancelamento de NFCe"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup>

                <div>
                    <h:inputText id="justificativaNFCe" maxlength="50"
                                 value="#{NotaFiscalDeServicoControle.nfceParaCancelar.justificativaParaCancelar}"
                                 style="width: 300px"/>
                </div>

                <a4j:commandButton id="botaoCancelarNotaNFCe"
                                   value="Cancelar NFCe"
                                   action="#{NotaFiscalDeServicoControle.cancelarNotaNFCe}"
                                   reRender="listagemDeNotas, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalJustificativaNFCe')}.hide();"/>

                <a4j:commandButton id="btnFecharJustiNFCe" value="Fechar" onclick="fireElement('hidelinkJustificativaNFCe');"/>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>


<rich:modalPanel domElementAttachment="parent" id="modalReenvioNfse"
                 onshow="document.getElementById('formReeviar:cfpReenvio').focus();" autosized="true" width="550"
                 height="150" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Reenviar Nota Fiscal"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideLinkModalReenvioNfse"/>
            <rich:componentControl for="modalReenvioNfse" attachTo="hideLinkModalReenvioNfse"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>


    <a4j:form id="formReeviar">

        <h:panelGrid style="font-size:12px; font-style: italic; font-family: Arial;  text-align: left;">
            ${NotaFiscalDeServicoControle.aviso}
        </h:panelGrid>

        <h:panelGroup layout="block" style="height: 480px; overflow-y: auto">
            <h:panelGrid columns="2" columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText styleClass="tituloCampos" value="RPS:"/>
                <h:outputText styleClass="tituloCampos" value="#{NotaFiscalDeServicoControle.notaReenviar.idRPS}"/>

                <h:outputText styleClass="tituloCampos" value="Dt. Emissão Original:"/>
                <h:outputText value="#{NotaFiscalDeServicoControle.notaReenviar.dataEmissao}" styleClass="tituloCampos">
                    <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                       timeZone="America/Sao_Paulo"/>
                </h:outputText>

                <h:outputText styleClass="tituloCampos" value="Dt. Emissão:"/>
                <rich:calendar id="dataReemissao"
                               value="#{NotaFiscalDeServicoControle.notaReenviar.dataEmissaoReenvio}"
                               inputSize="7"
                               inputClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                               datePattern="dd/MM/yyyy"
                               enableManualInput="true"
                               zindex="2"
                               showWeeksBar="false">
                </rich:calendar>

                <h:outputText styleClass="tituloCampos" style="color: red" value="ATENÇÃO:"/>
                <h:outputText styleClass="tituloCampos" style="color: red"
                              value="Para reenvio com data retroativa deve-se verificar qual o prazo permitido pelo municipio."/>


                <h:outputText styleClass="tituloCampos" value="Razão Social:"/>
                <h:inputText id="razaoSocialReenvio" size="60" maxlength="120" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.razaoSocialCons}"/>

                <h:outputText styleClass="tituloCampos" value="CPF/CNPJ:"/>
                <h:inputText id="cfpReenvio" rendered="#{!NotaFiscalDeServicoControle.notaReenviar.cnpj}" size="14"
                             maxlength="14"
                             onkeypress="return mascara(this.form, 'formReeviar:cfpReenvio', '999.999.999-99', event);"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.cpfCnpjCons}"/>
                <h:inputText id="cnpjReenvio" rendered="#{NotaFiscalDeServicoControle.notaReenviar.cnpj}" size="17"
                             maxlength="17"
                             onkeypress="return mascara(this.form, 'formReeviar:cfpReenvio', '99.999.999/9999-99', event);"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.cpfCnpjCons}"/>


                <h:outputText styleClass="tituloCampos" value="Insc.Municipal:"/>
                <h:inputText id="inscMunicipallReenvio" size="15"
                             maxlength="15"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.inscricaoMunicipalCons}"/>

                <h:outputText styleClass="tituloCampos" value="Insc.Estadual:"/>
                <h:inputText id="inscEstadualReenvio" size="15"
                             maxlength="15"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.inscricaoEstadual}"/>

                <h:outputText styleClass="tituloCampos" value="CFDF:"/>
                <h:inputText id="cfdfReenvio" size="15"
                             maxlength="15"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.cfdf}"/>


                <h:outputText styleClass="tituloCampos" value="CEP:"/>
                <h:inputText id="cepReenvio" size="10" maxlength="10"
                             onkeypress="return mascara(this.form, 'formReeviar:cepReenvio', '99.999-999', event);"
                             onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.cepCons}"/>

                <h:outputText styleClass="tituloCampos" value="Email:"/>
                <h:inputText id="emailReenvio" size="40" maxlength="40" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.emailCons}"/>

                <h:outputText styleClass="tituloCampos" value="Endereço:"/>
                <h:inputText id="enderecoReenvio" size="60" maxlength="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.logradouroCons}"/>

                <h:outputText styleClass="tituloCampos" value="Complemento:"/>
                <h:inputText id="complementoReenvio" size="60" maxlength="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.complementoEnderecoCons}"/>

                <h:outputText styleClass="tituloCampos" value="Numero:"/>
                <h:inputText id="numeroReenvio" size="60" maxlength="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.numeroEnderecoCons}"/>

                <h:outputText styleClass="tituloCampos" value="Bairro:"/>
                <h:inputText id="bairroReenvio" size="60" maxlength="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.bairroCons}"/>

                <h:outputText styleClass="tituloCampos" value="Telefone:"/>
                <h:inputText id="telefoneReenvio" size="60" maxlength="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.telefoneCons}"/>

                <h:outputText styleClass="tituloCampos" value="Descrição:"/>
                <h:inputText id="descricaoReenvio" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.descricao}"/>

                <h:outputText styleClass="tituloCampos" value="Observação:"/>
                <h:inputText id="observacaoReenvio" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.observacao}"/>

                <h:outputText styleClass="tituloCampos" value="Alíquota PIS:"/>
                <h:inputText id="aliquotaPIS" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.aliquotaPIS}"/>

                <h:outputText styleClass="tituloCampos" value="Alíquota COFINS:"/>
                <h:inputText id="aliquotaCOFINS" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.aliquotaCOFINS}"/>

                <h:outputText styleClass="tituloCampos" value="Alíquota IRRF:"/>
                <h:inputText id="aliquotaIRRF" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.aliquotaIR}"/>

                <h:outputText styleClass="tituloCampos" value="Código Lista Serviço: "/>
                <h:inputText id="itemListaServico" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.itemListaServico}"/>

                <h:outputText styleClass="tituloCampos" value="Código de Tributação do Município"/>
                <h:inputText id="codigoTributacaoMunicipio" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.codigoTributacaoMunicipio}"/>

                <h:outputText styleClass="tituloCampos" value="Código CNAE: "/>
                <h:inputText id="codigoCnae" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.codigoCnae}"/>

                <h:outputText styleClass="tituloCampos" value="Exigibilidade ISS: "/>
                <h:inputText id="exigibilidadeISS" size="60" onblur="blurinput(this);"
                             onfocus="focusinput(this);" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.notaReenviar.exigibilidadeISS}"/>
            </h:panelGrid>
        </h:panelGroup>

        <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada">

            <a4j:commandButton id="botaoReenviar"
                               value="Reenviar NFSE"
                               styleClass="botoes nvoBt"
                               action="#{NotaFiscalDeServicoControle.reenviarNfse}"
                               reRender="listagemDeNotas, mensagemNFeErro"
                               oncomplete="#{rich:component('modalReenvioNfse')}.hide();"/>

            <a4j:commandButton id="botalFecharReenvio" value="Fechar"
                               styleClass="botoes nvoBt btSec"
                               onclick="#{rich:component('modalReenvioNfse')}.hide();"/>

        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>


<rich:modalPanel domElementAttachment="parent" id="modalLogNota" autosized="true" width="800"
                 height="300" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Log Operação da Nota"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkLogNota"/>
            <rich:componentControl for="modalLogNota" attachTo="hidelinkLogNota"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Log Operação da Nota"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelHistoricoLogNota" layout="block"
                          style="overflow-y: auto; overflow-x: auto; max-height: 400px; max-width: 800px;">
                <rich:dataTable id="logOperacoesRPS" width="100%" headerClass="consulta"
                                rowClasses="linhaPar, linhaImpar"
                                columnClasses="centralizado, centralizado, centralizado, centralizado"
                                value="#{NotaFiscalDeServicoControle.logOperacaoNota}"
                                rows="100" var="log">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.id_LogOperacaoRPS}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="ID_Operacao"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.id_OperacaoRPS}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Data"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.dataHora}">
                                <f:convertDateTime pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                                   timeZone="America/Sao_Paulo"/>
                            </h:outputText>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Resultado"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.resultado}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Não Autorizar Nota"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandButton id="naoAutorizarNota"
                                               value="Não Autorizar"
                                               rendered="#{NotaFiscalDeServicoControle.permiteNaoAutorizar}"
                                               reRender="listagemDeNotas, mensagemNFeErro"
                                               actionListener="#{NotaFiscalDeServicoControle.naoAutorizarNotaLog}"
                                               oncomplete="#{rich:component('modalLogNota')}.hide();"
                                               title="Colocar a nota como não autorizada com esse motivo"
                                               styleClass="botoes">
                                <f:attribute name="log" value="#{log}"/>
                            </a4j:commandButton>
                        </h:panelGroup>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>


<rich:modalPanel domElementAttachment="parent" id="modalExcluirNotas" autosized="true" width="500"
                 height="200" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Excluir Notas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkExcluir"/>
            <rich:componentControl for="modalExcluirNotas" attachTo="hidelinkExcluir"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Excluir Notas Fiscais do Módulo NFSe"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelExcluirNotas">

                <h:outputText style="font-size:12px; font-style: italic; font-family: Arial, bold;  text-align: left;"
                              value="As notas serão excluídas SOMENTE do Módulo NFSe!"/>
                <br/>
                <h:outputText
                        style="font-size:12px; font-style: italic; font-family: Arial, bold;  text-align: left; color: red"
                        value="As notas NÃO SERÃO RETORNADAS para o envio no Gestão de Notas!"/>

                <br/>
                <br/>
                <h:panelGroup layout="block" id="panelListaExc" style="display: inline-flex;">
                    <h:outputText styleClass="tituloCampos" value="Lista ID_Lote:"/>
                    <h:inputText id="listaRPSExcluir" size="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{NotaFiscalDeServicoControle.listaRPSExcluir}"/>
                </h:panelGroup>

                <br/>
                <h:outputText style="font-size:12px; font-style: italic; font-family: Arial, bold;  text-align: left;"
                              value="Informe a lista dos ID_Lote separados por VÍRGULA !"/>

                <br/>
                <br/>

                <a4j:commandButton id="botaoExcluirNotas"
                                   value="Excluir Notas"
                                   onclick="if(!confirm('Confirma exclusão das Notas Fiscais ?')){return false;};"
                                   action="#{NotaFiscalDeServicoControle.excluirNotas}"
                                   reRender="listagemDeNotas, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalExcluirNotas')}.hide();"/>

                <a4j:commandButton id="btnFecharExcluirNotas" value="Fechar" onclick="fireElement('hidelinkExcluir');"/>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>


<rich:modalPanel domElementAttachment="parent" id="modalVisualizarNFCe" autosized="true" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Visualização da NFC-e"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkPreviewNFCe"/>
            <rich:componentControl for="modalVisualizarNFCe" attachTo="hidelinkPreviewNFCe"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="panelGeralNFCe">

        <h:panelGroup layout="block" id="visualizacaoRapidaNFCe" styleClass="textsmall"
                      style="display: inline-flex; width: 100%;">

            <h:panelGroup layout="block" id="panelEsquerdaNFCe" style="width: 30%">

                <h:outputText style="font-weight: bold" value="ID_NFCe:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.id_NFCe}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="Número Envio:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.numeroEnvio}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="Data de Emissão:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.dataHoraEmissao}">
                    <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                       timeZone="America/Sao_Paulo"/>
                </h:outputText>
                <br/>

                <h:outputText style="font-weight: bold" value="Valor Total:"/>
                <h:panelGroup style="margin-left: 5px">
                    <h:outputText value="R$ "/>
                    <h:outputText value="#{NotaFiscalDeServicoControle.notaNFCe.valorTotal}">
                        <f:converter converterId="FormatadorNumerico"/>
                    </h:outputText>
                </h:panelGroup>
                <br/>

                <h:outputText style="font-weight: bold" value="Complemento NFC-e:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.complemento}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="CNAE:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.cnae}"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelCentroNFCe" style="width: 35%">

                <h:outputText style="font-weight: bold" value="Razão Social:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destNome}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="CPF/CNPJ:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destCPFCNPJ}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="Telefone:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destFone}"/>

            </h:panelGroup>

            <h:panelGroup layout="block" id="panelDireitaNFCe" style="width: 35%">
                <h:outputText style="font-weight: bold" value="Logradouro:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destLogradouro}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="Numero Endereço:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destNumero}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="Complemento Endereço:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destComplemento}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="Bairro:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destBairro}"/>
                <br/>

                <h:outputText style="font-weight: bold" value="CEP:"/>
                <h:outputText style="margin-left: 5px" value="#{NotaFiscalDeServicoControle.notaNFCe.destCEP}"/>

            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" style="text-align: center; padding: 10px" styleClass="textsmall"
                      rendered="#{not empty NotaFiscalDeServicoControle.notaNFCe.chave}">
            <h:outputText style="font-weight: bold" value="Chave: "/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notaNFCe.chave}"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="text-align: center; padding: 10px" styleClass="textsmall">
            <h:outputText style="font-weight: bold" value="Produtos"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="text-align: center; max-height: 350px; overflow-y: auto;"
                      styleClass="textsmall">
            <rich:dataTable id="itensNFCe" width="100%" rowClasses="linhaPar, linhaImpar"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                            rows="30" var="nfce" value="#{NotaFiscalDeServicoControle.notaNFCe.itensNFCe}">

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Descrição"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.descricao}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="NCM"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.ncm}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="CFOP"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.cfop}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Código Tributação Municipio" style="white-space: pre-wrap;"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.codigoMunicipioISSQN}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Código Lista Serviço" style="white-space: pre-wrap;"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.codigoListaServicoISSQN}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Alíquota COFINS" style="white-space: pre-wrap;"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.aliquotaCOFINS}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText value=" %"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor COFINS"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nfce.valorCOFINS}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Alíquota PIS" style="white-space: pre-wrap;"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.aliquotaPIS}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText value=" %"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor PIS"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nfce.valorPIS}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Alíquota ISSQN" style="white-space: pre-wrap;"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.aliquotaISSQN}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText value=" %"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor ISSQN"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nfce.valorISSQN}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Alíquota ICMS" style="white-space: pre-wrap;"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.aliquotaICMS}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText value=" %"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor ICMS"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nfce.valorICMS}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor Unitário"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nfce.valorUnitario}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Qtd"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nfce.quantidade}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor Total"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nfce.valorTotal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>

        <h:panelGroup layout="block" style="text-align: center; padding: 10px" styleClass="textsmall">
            <h:outputText style="font-weight: bold" value="Formas de Pagamento"/>
        </h:panelGroup>

        <h:panelGroup layout="block" style="text-align: center; max-height: 200px; overflow-y: auto;"
                      styleClass="textsmall">
            <rich:dataTable id="formasNFCe" width="100%" rowClasses="linhaPar, linhaImpar"
                            reRender="paginaAtual, paginaAtualTop, painelPaginacaoTop, painelPaginacao"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                            rows="30" var="forma"
                            value="#{NotaFiscalDeServicoControle.notaNFCe.formasPagamentoNFCe}">

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Forma Pagamento"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{forma.formaPagamento}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column>
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{forma.valor}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>


<rich:modalPanel domElementAttachment="parent" id="modalCancelarVariasNotas" autosized="true" width="600"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Solicitar Cancelamento de Várias Notas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideModalCancelarVariasNotas"/>
            <rich:componentControl for="modalCancelarVariasNotas" attachTo="hideModalCancelarVariasNotas"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="panelGeralCancelarVariasNotas">

        <h:panelGroup layout="block" id="panelInferiorCancelarVariasNotas"
                      style="padding-bottom: 20px;"
                      rendered="#{not empty NotaFiscalDeServicoControle.notasParaCancelar}">

            <h:panelGroup layout="block"
                          style="text-align: center; background: #5b7987; padding: 5px;">
                <h:outputText styleClass="tituloFormulario" value="Justificativa para o Cancelamento das Notas"/>
            </h:panelGroup>

            <h:panelGroup layout="block" id="panelJustificativaCancelarVariasNotas"
                          style="text-align: center; padding-top: 10px">
                <h:inputText id="justificativaVariasNotas" maxlength="50"
                             style="width: 300px" styleClass="form"
                             value="#{NotaFiscalDeServicoControle.justificativaParaCancelarVariasNotas}"/>
            </h:panelGroup>
        </h:panelGroup>


        <h:panelGroup layout="block"
                      style="text-align: center; background: #5b7987; padding: 5px;"
                      rendered="#{not empty NotaFiscalDeServicoControle.notasNaoPodeCancelar}">
            <h:outputText styleClass="tituloFormulario"
                          value="As notas abaixo não serão canceladas, pois não estão autorizadas ou já passaram do prazo de cancelamento"/>
        </h:panelGroup>


        <h:panelGroup layout="block" style="text-align: center; max-height: 200px; overflow-y: auto; margin-top: 5px;"
                      rendered="#{not empty NotaFiscalDeServicoControle.notasNaoPodeCancelar}"
                      styleClass="textsmall">

            <rich:dataTable id="dataTableNotasNaoPodeCancelar" width="100%" rowClasses="linhaPar, linhaImpar"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                            var="nota"
                            value="#{NotaFiscalDeServicoControle.notasNaoPodeCancelar}">

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="ID_RPS"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nota.idRPS}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Razão Social do Cliente"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nota.razaoSocialCons}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nota.valorServicos}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Status"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText styleClass="#{nota.classeDoStatus}" value="#{nota.status}"/>
                    </h:panelGroup>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>

        <h:panelGrid id="infoTotaisNotasCancelar"
                     columns="2" rowClasses="linhaPar,linhaPar,linhaImpar" columnClasses="classEsquerda, classDireita"
                     width="100%"
                     style="width:100%; text-align: center; padding-top: 25px; padding-bottom: 15px">

            <h:outputText value="Notas a serem canceladas:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notasParaCancelar_Total}"/>

            <h:outputText value="Não podem ser canceladas:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notasNaoPodeCancelar_Total}"/>

            <h:outputText value="Total de Notas Solicitadas:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notasNaoPodeCancelar_Total + NotaFiscalDeServicoControle.notasParaCancelar_Total}"/>

        </h:panelGrid>

        <h:panelGroup layout="block"
                      style="text-align: center; padding: 5px;"
                      rendered="#{empty NotaFiscalDeServicoControle.notasParaCancelar}">
            <h:outputText style="color: red; font-size: 14px;"
                          value="Não existem notas para serem canceladas!"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelBotoesCancelarVariasNotas"
                      style="width:100%; text-align: center; padding-top: 25px; padding-bottom: 20px">

            <a4j:commandButton id="botaoCancelarVariasNotas"
                               styleClass="botoes nvoBt"
                               rendered="#{not empty NotaFiscalDeServicoControle.notasParaCancelar}"
                               value="Cancelar Notas"
                               action="#{NotaFiscalDeServicoControle.cancelarVariasNota}"
                               reRender="listagemDeNotas, mensagemNFeErro, mensagemErroNotasCancelar"
                               oncomplete="#{NotaFiscalDeServicoControle.onComplete}"/>
            <h:outputText value=" " style="margin-left: 20px;"
                          rendered="#{not empty NotaFiscalDeServicoControle.notasParaCancelar}"/>

            <a4j:commandButton value="Fechar" onclick="#{rich:component('modalCancelarVariasNotas')}.hide();" styleClass="botoes nvoBt btSec"/>
        </h:panelGroup>

        <h:panelGrid columns="1" id="mensagemErroNotasCancelar">
            <h:outputText styleClass="mensagem" value="#{NotaFiscalDeServicoControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{NotaFiscalDeServicoControle.mensagemDetalhada}"/>
        </h:panelGrid>

    </h:panelGroup>
</rich:modalPanel>



<rich:modalPanel domElementAttachment="parent" id="modalInutilizarNFCE" autosized="true" width="600"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Inutilizar NFC-e"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideModalInutilizar"/>
            <rich:componentControl for="modalInutilizarNFCE" attachTo="hideModalInutilizar"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="panelGeralInutilizar">

        <h:panelGroup layout="block"
                      style="text-align: center; background: #5b7987; padding: 5px;"
                      rendered="#{not empty NotaFiscalDeServicoControle.listaNFCEInutilizarNaoPode}">
            <h:outputText styleClass="tituloFormulario"
                          value="As notas abaixo não serão inutilizadas, pois estão autorizadas ou canceladas"/>
        </h:panelGroup>


        <h:panelGroup layout="block" style="text-align: center; max-height: 200px; overflow-y: auto; margin-top: 5px;"
                      rendered="#{not empty NotaFiscalDeServicoControle.listaNFCEInutilizarNaoPode}"
                      styleClass="textsmall">

            <rich:dataTable id="dataTableInutilizarNaoPodeCancelar" width="100%" rowClasses="linhaPar, linhaImpar"
                            columnClasses="centralizado, centralizado, centralizado, centralizado, centralizado"
                            var="nota"
                            value="#{NotaFiscalDeServicoControle.listaNFCEInutilizarNaoPode}">

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="ID_RPS"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nota.id_NFCe}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Razão Social do Cliente"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="#{nota.destNome}"/>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Valor"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText value="R$ "/>
                        <h:outputText value="#{nota.valorTotal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </h:panelGroup>
                </rich:column>

                <rich:column filterEvent="onkeyup">
                    <f:facet name="header">
                        <h:outputText value="Status"/>
                    </f:facet>
                    <h:panelGroup>
                        <h:outputText styleClass="#{nota.classeDoStatus}" value="#{nota.status}"/>
                    </h:panelGroup>
                </rich:column>

            </rich:dataTable>
        </h:panelGroup>

        <h:panelGrid id="infoTotaisInutilizar"
                     columns="2" rowClasses="linhaPar,linhaPar,linhaImpar" columnClasses="classEsquerda, classDireita"
                     width="100%"
                     style="width:100%; text-align: center; padding-top: 25px; padding-bottom: 15px">

            <h:outputText value="Notas a serem inutilizadas:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notasInutilizar_Total}"/>

            <h:outputText value="Não podem ser inutilizadas:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notasInutilizarNaoPode_Total}"/>

            <h:outputText value="Total de NFC-e Solicitadas:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.notasNaoPodeCancelar_Total + NotaFiscalDeServicoControle.notasParaCancelar_Total}"/>

        </h:panelGrid>

        <h:panelGroup layout="block"
                      style="text-align: center; padding: 5px;"
                      rendered="#{empty NotaFiscalDeServicoControle.listaNFCEInutilizar}">
            <h:outputText style="color: red; font-size: 14px;"
                          value="Não existem notas para serem inutilizadas!"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelBotoesInutilizar"
                      style="width:100%; text-align: center; padding-top: 25px; padding-bottom: 20px">

            <a4j:commandButton id="botaoInutilizar"
                               styleClass="botoes nvoBt"
                               rendered="#{not empty NotaFiscalDeServicoControle.listaNFCEInutilizar}"
                               value="Inutilizar NFC-e"
                               action="#{NotaFiscalDeServicoControle.inutilizarNFCE}"
                               reRender="listagemDeNotas, mensagemErroInutilizar, mensagemErroNotasCancelar"
                               oncomplete="#{NotaFiscalDeServicoControle.onComplete}"/>
            <h:outputText value=" " style="margin-left: 20px;"
                          rendered="#{not empty NotaFiscalDeServicoControle.listaNFCEInutilizar}"/>

            <a4j:commandButton value="Fechar" onclick="#{rich:component('modalInutilizarNFCE')}.hide();" styleClass="botoes nvoBt btSec"/>
        </h:panelGroup>

        <h:panelGrid columns="1" id="mensagemErroInutilizar">
            <h:outputText styleClass="mensagem" value="#{NotaFiscalDeServicoControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{NotaFiscalDeServicoControle.mensagemDetalhada}"/>
        </h:panelGrid>

    </h:panelGroup>
</rich:modalPanel>




<rich:modalPanel domElementAttachment="parent" id="modalLogInutilizar" autosized="true" width="800"
                 height="300" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Log Inutilizar NFC-e"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideModalLogInutilizar"/>
            <rich:componentControl for="modalLogInutilizar" attachTo="hideModalLogInutilizar"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Log Inutilizar"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelLogInutilizar" layout="block"
                          style="overflow-y: auto; overflow-x: auto; max-height: 400px; max-width: 800px;">
                <rich:dataTable id="logInutlizarNFCE" width="100%" headerClass="consulta"
                                rowClasses="linhaPar, linhaImpar"
                                columnClasses="centralizado, centralizado, centralizado, centralizado"
                                value="#{NotaFiscalDeServicoControle.logNumerosInutilizar}"
                                rows="100" var="log">

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Código"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.id_NumerosInutilizar}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Num Inicial"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.numInicial}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Num Final"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.numFinal}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Finalizado"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.finalizadoApresentar}"/>
                        </h:panelGroup>
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="Retorno"/>
                        </f:facet>
                        <h:panelGroup>
                            <h:outputText value="#{log.retorno}"/>
                        </h:panelGroup>
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>



<rich:modalPanel domElementAttachment="parent" id="modalReenvioNFCe"
                 onshow="document.getElementById('formReeviarNFCe:destCPFCNPJ').focus();" autosized="true" width="550"
                 height="150" shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Reenviar NFCe"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideLinkModalReenvioNFCe"/>
            <rich:componentControl for="modalReenvioNFCe" attachTo="hideLinkModalReenvioNFCe"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>


    <a4j:form id="formReeviarNFCe">

        <h:panelGrid style="font-size:12px; font-style: italic; font-family: Arial;  text-align: left;">
            ${NotaFiscalDeServicoControle.aviso}
        </h:panelGrid>

        <h:panelGrid columns="2" columnClasses="classEsquerda, classDireita" width="100%" id="panelReenviarNFCe">

            <h:outputText styleClass="tituloCampos" value="NFCe:"/>
            <h:outputText styleClass="tituloCampos" value="#{NotaFiscalDeServicoControle.nfceReenviar.id_NFCe}"/>

            <h:outputText styleClass="tituloCampos" value="Dt. Emissão Original:"/>
            <h:outputText value="#{NotaFiscalDeServicoControle.nfceReenviar.dataHoraEmissao}" styleClass="tituloCampos">
                <f:convertDateTime type="both" dateStyle="full" pattern="dd/MM/yyyy - HH:mm:ss" locale="pt"
                                   timeZone="America/Sao_Paulo"/>
            </h:outputText>

            <h:outputText styleClass="tituloCampos" value="Dt. Emissão:"/>
            <rich:calendar id="dataReemissaoNFCe"
                           value="#{NotaFiscalDeServicoControle.nfceReenviar.dataEmissaoReenvio}"
                           inputSize="7"
                           inputClass="form"
                           oninputblur="blurinput(this);"
                           oninputfocus="focusinput(this);"
                           oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                           datePattern="dd/MM/yyyy"
                           enableManualInput="true"
                           zindex="2"
                           showWeeksBar="false">
            </rich:calendar>


            <h:outputText styleClass="tituloCampos" value="Razão Social:"/>
            <h:inputText id="destNome" size="60" maxlength="120" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destNome}"/>

            <h:outputText styleClass="tituloCampos" value="CPF/CNPJ:"/>
            <h:inputText id="destCPFCNPJ" rendered="#{!NotaFiscalDeServicoControle.nfceReenviar.destCPFCNPJ}" size="14"
                         maxlength="14"
                         onkeypress="return mascara(this.form, 'formReeviarNFCe:destCPFCNPJ', '999.999.999-99', event);"
                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destCPFCNPJ}"/>

            <h:outputText styleClass="tituloCampos" value="CEP:"/>
            <h:inputText id="destCEP" size="10" maxlength="10"
                         onkeypress="return mascara(this.form, 'formReeviarNFCe:destCEP', '99.999-999', event);"
                         onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destCEP}"/>

            <%--<h:outputText styleClass="tituloCampos" value="Email:"/>--%>
            <%--<h:inputText id="emailReenvio" size="40" maxlength="40" onblur="blurinput(this);"--%>
                         <%--onfocus="focusinput(this);" styleClass="form"--%>
                         <%--value="#{NotaFiscalDeServicoControle.notaReenviar.emailCons}"/>--%>

            <h:outputText styleClass="tituloCampos" value="Endereço:"/>
            <h:inputText id="destLogradouro" size="60" maxlength="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destLogradouro}"/>

            <h:outputText styleClass="tituloCampos" value="Complemento:"/>
            <h:inputText id="destComplemento" size="60" maxlength="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destComplemento}"/>

            <h:outputText styleClass="tituloCampos" value="Numero:"/>
            <h:inputText id="destNumero" size="60" maxlength="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destNumero}"/>

            <h:outputText styleClass="tituloCampos" value="Bairro:"/>
            <h:inputText id="destBairro" size="60" maxlength="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destBairro}"/>

            <h:outputText styleClass="tituloCampos" value="Telefone:"/>
            <h:inputText id="destFone" size="60" maxlength="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.destFone}"/>

            <h:outputText styleClass="tituloCampos" value="Complemento NFCe:"/>
            <h:inputText id="complementoNFCe" size="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.complemento}"/>

            <h:outputText styleClass="tituloCampos" value="CNAE:"/>
            <h:inputText id="cnaeNFCe" size="60" onblur="blurinput(this);"
                         onfocus="focusinput(this);" styleClass="form"
                         value="#{NotaFiscalDeServicoControle.nfceReenviar.cnae}"/>

            <h:outputText styleClass="tituloCampos" value="Estado:"/>
            <h:selectOneMenu id="estadoNFCe" value="#{NotaFiscalDeServicoControle.nfceReenviar.destCidade.uf}">
                <f:selectItems value="#{NotaFiscalDeServicoControle.listaEstado}"/>
                <a4j:support event="onchange" action="#{NotaFiscalDeServicoControle.buscarCidade}"
                             reRender="formReeviarNFCe:panelReenviarNFCe"/>
            </h:selectOneMenu>

            <h:outputText styleClass="tituloCampos" value="Cidade:"/>
            <h:selectOneMenu id="cidadeNFCe"
                             value="#{NotaFiscalDeServicoControle.nfceReenviar.destCidade.id_Municipio}">
                <f:selectItems value="#{NotaFiscalDeServicoControle.listaCidades}"/>
            </h:selectOneMenu>

        </h:panelGrid>

        <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada">

            <a4j:commandButton id="botaoReenviarNFCe"
                               value="Reenviar NFCe"
                               styleClass="botoes nvoBt"
                               action="#{NotaFiscalDeServicoControle.reenviarNFCe}"
                               reRender="listagemDeNotasNFCe, mensagemNFeErro"
                               oncomplete="#{rich:component('modalReenvioNFCe')}.hide();"/>

            <a4j:commandButton id="botalFecharReenvioNFCe" value="Fechar"
                               styleClass="botoes nvoBt btSec"
                               onclick="#{rich:component('modalReenvioNFCe')}.hide();"/>

        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>


<rich:modalPanel domElementAttachment="parent" id="modalSequenciaRPS"
                 onshow="document.getElementById('formSequenciaRPS:sequenciaRPS').focus();" autosized="true"
                 width="342"
                 height="140"
                 shadowOpacity="true">

    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Alterar Sequência RPS"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideLinkModalSequenciaRPS"/>
            <rich:componentControl for="modalSequenciaRPS" attachTo="hideLinkModalSequenciaRPS"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formSequenciaRPS">

        <h:panelGrid columns="2" columnClasses="classEsquerda, classDireita" width="100%">

            <h:outputText styleClass="tituloCampos"
                          style="font-weight: bold; font-size: 14px"
                          value="Sequência atual:"/>
            <h:outputText id="sequenciaRPSAtual"
                          styleClass="tituloCampos"
                          style="font-weight: bold; font-size: 16px"
                          value="#{NotaFiscalDeServicoControle.sequenciaRPSAtual}"/>

            <h:outputText styleClass="tituloCampos" value="Sequência RPS:"/>
            <h:inputText id="sequenciaRPS"
                         maxlength="50"
                         size="10"
                         styleClass="tituloCampos"
                         value="#{NotaFiscalDeServicoControle.sequenciaRPS}"/>

            <%--<h:outputText styleClass="tituloCampos" value="Sequência Lote:"/>--%>
            <%--<h:inputText id="sequenciaLote"--%>
                         <%--maxlength="50"--%>
                         <%--size="15"--%>
                         <%--styleClass="tituloCampos"--%>
                         <%--value="#{NotaFiscalDeServicoControle.sequenciaLote}"/>--%>

        </h:panelGrid>

        <h:panelGrid columns="2" width="100%" columnClasses="colunaCentralizada, colunaCentralizada" style="padding-top: 10px">

            <a4j:commandButton id="btnAlterarSequenciaRPS"
                               value="Alterar"
                               styleClass="botoes nvoBt"
                               action="#{NotaFiscalDeServicoControle.alterarSequenciaRPS}"
                               reRender="listagemDeNotasNFCe, mensagemNFeErro"
                               oncomplete="#{rich:component('modalSequenciaRPS')}.hide();"/>

            <a4j:commandButton id="btnFecharAlterarSequenciaRPS"
                               value="Fechar"
                               styleClass="botoes nvoBt btSec"
                               onclick="#{rich:component('modalSequenciaRPS')}.hide();"/>

        </h:panelGrid>

    </a4j:form>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalDesvincularNotas" autosized="true" width="500"
                 height="200" shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Desvincular Notas"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkDesvincular"/>
            <rich:componentControl for="modalDesvincularNotas" attachTo="hidelinkDesvincular"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
        <h:panelGrid columns="1"
                     style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                     columnClasses="colunaCentralizada" width="100%">
            <h:outputText styleClass="tituloFormulario" value="Desvincular Notas Fiscais do Módulo NFSe"/>
        </h:panelGrid>

        <rich:panel>
            <h:panelGroup id="panelDesvincularNotas">

                <h:outputText
                        style="font-size:12px; font-style: italic; font-family: Arial, bold;  text-align: left; color: red"
                        value="As notas NÃO SERÃO RETORNADAS para o envio no Gestão de Notas!"/>

                <br/>
                <br/>
                <h:panelGroup layout="block" id="panelListaDesvincular" style="display: inline-flex;">
                    <h:outputText styleClass="tituloCampos" value="Lista ID_Lote:"/>
                    <h:inputText id="listaLoteDesvincular" size="50" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form"
                                 value="#{NotaFiscalDeServicoControle.listaLoteDesvincular}"/>
                </h:panelGroup>

                <br/>
                <h:outputText style="font-size:12px; font-style: italic; font-family: Arial, bold;  text-align: left;"
                              value="Informe a lista dos ID_Lote separados por VÍRGULA !"/>

                <br/>
                <br/>

                <a4j:commandButton id="botaoDesvincularNotas"
                                   value="Desvincular Notas"
                                   onclick="if(!confirm('Confirma desvincular Notas Fiscais ?')){return false;};"
                                   action="#{NotaFiscalDeServicoControle.desvincularNotas}"
                                   reRender="listagemDeNotas, mensagemNFeErro"
                                   oncomplete="#{rich:component('modalDesvincularNotas')}.hide();"/>

                <a4j:commandButton id="btnFecharDesvincularNotas" value="Fechar" onclick="fireElement('hidelinkDesvincular');"/>
            </h:panelGroup>
        </rich:panel>
    </h:panelGrid>
</rich:modalPanel>

<rich:modalPanel domElementAttachment="parent" id="modalInutilizarNFE" autosized="true" width="600"
                 shadowOpacity="true">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Inutilizar NFe"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hideModalInutilizarNFe"/>
            <rich:componentControl for="modalInutilizarNFE" attachTo="hideModalInutilizarNFe"
                                   operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="panelGeralInutilizarNFe">

        <h:panelGroup layout="block"
                      style="text-align: center; background: #5b7987; padding: 5px;">
            <h:outputText styleClass="tituloFormulario"
                          value="Informe a série e o número inicial e final a serem inutilizadas"/>
        </h:panelGroup>

        <h:panelGrid id="panelGridInutilizarNFe"
                     columns="2" rowClasses="linhaPar,linhaPar,linhaPar" columnClasses="classEsquerda, classDireita"
                     width="100%"
                     style="width:100%; text-align: center; padding-top: 25px; padding-bottom: 15px">

            <h:outputText value="Série:"/>
            <h:inputText value="#{NotaFiscalDeServicoControle.serieInutilizarNFe}"/>

            <h:outputText value="Número Inicial:"/>
            <h:inputText value="#{NotaFiscalDeServicoControle.inicioInutilizarNFe}"/>

            <h:outputText value="Número Final:"/>
            <h:inputText value="#{NotaFiscalDeServicoControle.finalInutilizarNFe}"/>

        </h:panelGrid>


        <h:panelGroup layout="block" id="panelBotoesLogInutliNFE"
                      style="width:100%; text-align: center; padding-bottom: 5px">
            <a4j:commandButton id="btnLogInutilizadaNFCe" style="margin-left: 8px"
                               value="Histórico"
                               reRender="modalLogInutilizar, mensagemNFeErro, mensagemErroInutilizarNFe"
                               action="#{NotaFiscalDeServicoControle.montarHistoricoInutilizacaoNFE}"
                               oncomplete="#{rich:component('modalInutilizarNFE')}.hide();#{rich:component('modalLogInutilizar')}.show();"
                               title="Log das Tentativas de Inutilizar NFe"
                               styleClass="botoes"/>
        </h:panelGroup>

        <h:panelGroup layout="block" id="panelBotoesInutilizarNFe"
                      style="width:100%; text-align: center; padding-top: 15px; padding-bottom: 15px">

            <a4j:commandButton id="botaoInutilizarNFe"
                               styleClass="botoes nvoBt"
                               value="Inutilizar NFe"
                               action="#{NotaFiscalDeServicoControle.inutilizarNFE}"
                               reRender="listagemDeNotas, mensagemErroInutilizarNFe, mensagemErroNotasCancelar"
                               oncomplete="#{NotaFiscalDeServicoControle.onComplete}"/>

            <a4j:commandButton value="Fechar" onclick="#{rich:component('modalInutilizarNFE')}.hide();" styleClass="botoes nvoBt btSec"/>
        </h:panelGroup>

        <h:panelGrid columns="1" id="mensagemErroInutilizarNFe">
            <h:outputText styleClass="mensagem" value="#{NotaFiscalDeServicoControle.mensagem}"/>
            <h:outputText styleClass="mensagemDetalhada" value="#{NotaFiscalDeServicoControle.mensagemDetalhada}"/>
        </h:panelGrid>

    </h:panelGroup>
</rich:modalPanel>