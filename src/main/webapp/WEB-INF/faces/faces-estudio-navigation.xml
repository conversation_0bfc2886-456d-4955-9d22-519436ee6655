
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
              xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/*</from-view-id>
        <navigation-case>
            <from-outcome>loginEstudio</from-outcome>
            <to-view-id>/pages/estudio/indexEstudio.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>agendaIndividual</from-outcome>
            <to-view-id>/pages/estudio/agendaIndividual.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>agendaAmbiente</from-outcome>
            <to-view-id>/pages/estudio/agendaAmbiente.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>agendaProfissional</from-outcome>
            <to-view-id>/pages/estudio/agendaProfissional.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>agendaAmbienteProfissionalMenu</from-outcome>
            <to-view-id>/pages/estudio/agendaAmbienteProfissionalMenu.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>agendaMensal</from-outcome>
            <to-view-id>/agendaMensal.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>disponibilidade</from-outcome>
            <to-view-id>/pages/estudio/disponibilidade.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>pacote</from-outcome>
            <to-view-id>/pages/estudio/pacote.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioComissaoEstudio</from-outcome>
            <to-view-id>relatorioComissaoEstudio.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioGeralAgendamentosEstudio</from-outcome>
            <to-view-id>relatorioGeralAgendamentos.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>relatorioFechamentoDiario</from-outcome>
            <to-view-id>relatorioFechamentoDiario.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>RelatorioGeralClientesInativos</from-outcome>
            <to-view-id>relatorioGeralClientesInativos.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/estudio/pacoteCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/estudio/pacoteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/estudio/pacoteForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/estudio/pacoteCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>atualizarImagem</from-outcome>
            <to-view-id>/pages/estudio/pacoteForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/estudio/relatorioFechamentoDiario.jsp</from-view-id>
        <navigation-case>
            <from-outcome>telaCliente</from-outcome>
            <to-view-id>/modulo_visualiza_cliente.jsp?modulo=4bf2add2267962ea87f029fef8f75a2f</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/modulo_visualiza_cliente.jsp</from-view-id>
        <navigation-case>
            <from-outcome>questionario</from-outcome>
            <to-view-id>/tela4.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoSessaoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>transferencia</from-outcome>
            <to-view-id>/cancelamentoSessaoTransferenciaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelamentoListaCheque</from-outcome>
            <to-view-id>/cancelamentoSessaoListaChequeForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoSessaoTransferencia.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/cancelamentoSessaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoSessaoListaChequeForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/cancelamentoSessaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>finalizarCancelamento</from-outcome>
            <to-view-id>/cancelamentoSessaoFinalizadoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoSessaoFinalizadoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/cancelamentoSessaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/cancelamentoSessaoTransferenciaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/cancelamentoSessaoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>transferencia</from-outcome>
            <to-view-id>/cancelamentoSessaoTransferencia.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

</faces-config>
