<?xml version="1.0" encoding="ISO-8859-1"?>
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">
	<!-- Backing Beans -->
    <managed-bean>
        <managed-bean-name>InicioControle</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.oamd.controle.basico.InicioControle</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
    
    <!-- Representa as regras de autorização da aplicação -->
    <managed-bean>
        <managed-bean-name>Regras</managed-bean-name>
        <managed-bean-class>br.com.pactosolucoes.aas.authorization.rules.AutorizacaoRegras</managed-bean-class>
        <managed-bean-scope>session</managed-bean-scope>
    </managed-bean>
	
	<!-- Navigation Rules -->
    <navigation-rule>
        <description>Navegacao Descobridor</description>
        <from-view-id>/*</from-view-id>

        <navigation-case>
            <from-outcome>oamd</from-outcome>
            <to-view-id>/inicio.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>login</from-outcome>
            <to-view-id>/tela1.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>inicio</from-outcome>
            <to-view-id>/inicio.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>selEmpresa</from-outcome>
            <to-view-id>/inicio.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>erroInicio</from-outcome>
            <to-view-id>/inicio.jsp</to-view-id>
        </navigation-case>

        <navigation-case>
            <from-outcome>erroLogin</from-outcome>
            <to-view-id>/login.jsp</to-view-id>
            <redirect/>
        </navigation-case>

        <navigation-case>
            <from-outcome>login</from-outcome>
            <to-view-id>/tela1.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>robo</from-outcome>
            <to-view-id>/robo.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>nfe</from-outcome>
            <to-view-id>/indexNFe.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <from-view-id>/inicio.jsp</from-view-id>
        <navigation-case>

            <from-outcome>erro</from-outcome>
            <to-view-id>/inicio.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
</faces-config>


