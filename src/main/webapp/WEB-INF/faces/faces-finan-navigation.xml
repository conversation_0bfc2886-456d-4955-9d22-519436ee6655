
<faces-config version="1.2" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_1_2.xsd">
    <navigation-rule>
        <description>Navegação Geral do Sistema (todas as páginas)</description>
        <from-view-id>/*</from-view-id>
        <navigation-case>
            <from-outcome>pix</from-outcome>
            <to-view-id>/pages/pix/cobranca.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaCadastroConfigFinanceira</from-outcome>
            <to-view-id>/pages/finan/cadastros/telaCadastroConfigFinanceira.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaCadastroAuxiliares</from-outcome>
            <to-view-id>/pages/finan/cadastros/telaCadastroAuxiliares.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>dre</from-outcome>
            <to-view-id>/pages/finan/dre.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>financeiro</from-outcome>
            <to-view-id>/pages/finan/telaInicialFinan.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cadastrosFinan</from-outcome>
            <to-view-id>/pages/finan/cadastros/telaCadastro.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>relatoriosFinan</from-outcome>
            <to-view-id>/pages/finan/relatorios.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>lancamentos</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
         <navigation-case>
            <from-outcome>lancamentosCadastro</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>lancamentoFinanceiroRapido</from-outcome>
            <to-view-id>/pages/finan/lancamentoFinanceiroRapidoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>rateioIntegracao</from-outcome>
            <to-view-id>/pages/finan/rateioIntegracaoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>planoConta</from-outcome>
            <to-view-id>/pages/finan/planoContaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>centroCusto</from-outcome>
            <to-view-id>/pages/finan/centroCustoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>demonstrativoFinanceiro</from-outcome>
            <to-view-id>/pages/finan/demonstrativoFinanceiro.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>fechamentoCaixaPlanoConta</from-outcome>
            <to-view-id>/pages/finan/fechamentoCaixaPlanoContas.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>fluxoCaixa</from-outcome>
            <to-view-id>/pages/finan/fluxoCaixa.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoRecebiveis</from-outcome>
            <to-view-id>/pages/finan/gestaoRecebiveis.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>editarPagamento</from-outcome>
            <to-view-id>/pages/finan/edicaoPagamento.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>gestaoLotes</from-outcome>
            <to-view-id>/pages/finan/gestaoLotesCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>editarLote</from-outcome>
            <to-view-id>/pages/finan/gestaoLotesForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>metasFinanceiro</from-outcome>
            <to-view-id>/metasFinanceiroCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>editarMetasFinanceiro</from-outcome>
            <to-view-id>/metasFinanceiroForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>relatorioOrcamentarioConf</from-outcome>
            <to-view-id>/relOrcamentarioConf.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>editarRelatorioOrcamentarioConf</from-outcome>
            <to-view-id>/relOrcamentarioForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>metaFinanceiro</from-outcome>
            <to-view-id>metaFinan.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>telaCaixa</from-outcome>
            <to-view-id>/pages/finan/telaCaixa.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>bloqueioCaixa</from-outcome>
            <to-view-id>/pages/finan/bloqueiocaixa.jsp</to-view-id>
            <redirect />
        </navigation-case>
		
        <navigation-case>
            <from-outcome>resumoContas</from-outcome>
            <to-view-id>/pages/finan/resumoContas.jsp</to-view-id>
            <redirect />
        </navigation-case>

        <navigation-case>
            <from-outcome>editarContaContabil</from-outcome>
            <to-view-id>finanContaContabilForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultarContaContabil</from-outcome>
            <to-view-id>finanContaContabilCons.jsp</to-view-id>
            <redirect/>
        </navigation-case>

    </navigation-rule>
    <!-- TELA INICIAL -->
    <navigation-rule>
        <description>Navegacao Geral</description>
        <from-view-id>/pages/finan/telaInicialFinan.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect/>
        </navigation-case>
    </navigation-rule>

    <!-- LANCAMENTOS PAGAMENTO -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/telaLancamentosCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>editarRecebivel</from-outcome>
            <to-view-id>/pages/finan/gestaoRecebiveis.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>excluir</from-outcome>
            <to-view-id>/pages/finan/telaLancamentoExclusao.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
	<!--LANCAMENTO PAGAMENTOS-->
	<!-- LANCAMENTO RECEBIMENTO / PAGAMENTO -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/telaLancamentosForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelar</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>excluir</from-outcome>
            <to-view-id>/pages/finan/telaLancamentoExclusao.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
	<!--LANCAMENTO RECEBIMENTO / PAGAMENTO-->
	<!-- EXCLUSAO RECEBIMENTO / PAGAMENTO -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/telaLancamentoExclusao.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>voltar</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>cancelar</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>resumoContas</from-outcome>
            <to-view-id>/pages/finan/resumoContas.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
	<!--EXCLUSAO RECEBIMENTO / PAGAMENTO-->
	<!-- CADASTRO DE CONTAS -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/finanContaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/finanContaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/finanContaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/finanContaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/finanContaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/finanContaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
	<!-- FIM DO CADASTRO DE CONTAS -->
	<!-- CADASTRO DE TIPOS CONTAS -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/finanTipoContaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/finanTipoContaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/finanTipoContaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/finanTipoContaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/finanTipoContaForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/finanTipoContaCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
	<!-- FIM DO CADASTRO DE TIPOS CONTAS -->
	<!-- CADASTRO DE TIPOS DE DOCUMENTOS -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/finanTipoDocumentoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/finanTipoDocumentoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/finanTipoDocumentoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/finanTipoDocumentoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/finanTipoDocumentoForm.jsp</to-view-id>
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/finanTipoDocumentoCons.jsp</to-view-id>
        </navigation-case>
    </navigation-rule>
	<!-- FIM DO CADASTRO DE TIPOS DE DOCUMENTOS -->
	<!-- CADASTRO DO PLANO DE CONTAS -->
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/planoContaCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/finan/planoContaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/finan/planoContaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/planoContaForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/finan/planoContaForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/finan/planoContaCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
	<!-- FIM CADASTRO DO PLANO DE CONTAS -->
	<!-- CADASTRO DO CENTRO DE CUSTOS -->
    <navigation-rule>
        <description>Cadastro de centro de custos</description>
        <from-view-id>/pages/finan/centroCustoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/finan/centroCustoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/finan/centroCustoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/centroCustoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/pages/finan/centroCustoForm.jsp</to-view-id>
            <redirect />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/finan/centroCustoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
	<!-- FIM CADASTRO DO CENTRO DE CUSTOS -->
	 <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/gestaoLotesForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule>   
    
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/resumoContas.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>excluir</from-outcome>
            <to-view-id>/pages/finan/telaLancamentoExclusao.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule> 
    
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/telaCaixa.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule>


     <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/includes/include_LancamentosDF.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/includes/include_LancamentosDF.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>editarRecebivel</from-outcome>
            <to-view-id>/pages/finan/gestaoRecebiveis.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosCons.jsp</to-view-id>
            <redirect />
        </navigation-case>

    </navigation-rule>

        <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/recebivelAvulsoCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/recebivelAvulsoForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/recebivelAvulsoForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule>

    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/recebivelAvulsoForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novo</from-outcome>
            <to-view-id>/recebivelAvulsoForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>editar</from-outcome>
            <to-view-id>/recebivelAvulsoForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
        <navigation-case>
            <from-outcome>consultar</from-outcome>
            <to-view-id>/recebivelAvulsoCons.jsp</to-view-id>
            <redirect />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/gestaoRecebiveis.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule> 
    <navigation-rule>
        <description>Navegacao da pagina</description>
        <from-view-id>/pages/finan/gestaoLotesCons.jsp</from-view-id>
        <navigation-case>
            <from-outcome>novoLancamento</from-outcome>
            <to-view-id>/pages/finan/telaLancamentosForm.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule>
    <navigation-rule>
        <from-view-id>/pix/relatorioTransacoesPixForm.jsp</from-view-id>
        <navigation-case>
            <from-outcome>relatorio</from-outcome>
            <to-view-id>/pix/relatorioTransacoesPix.jsp</to-view-id>
            <redirect  />
        </navigation-case>
    </navigation-rule>
</faces-config>