<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script src="beta/js/jquery.js" type="text/javascript"></script>
<script src="beta/js/jquery.dataTables.min.js" type="text/javascript"></script>
<script src="beta/js/DT_bootstrap.js" type="text/javascript"></script>
<script src="beta/js/bootstrap-tab.js" type="text/javascript"></script>
<script src="script/jquery.maskedinput-1.7.6.js" type="text/javascript"></script>
<style>
    .dataTables_filter {
        display: none;
    }
</style>
<script>
    jQuery.noConflict();
</script>
<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>

    <title>${msg_aplic.prt_Indicacao_tituloForm}</title>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_Indicacao_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-emitir-um-relatorio-de-todas-as-indicacoes-feitas-no-mes/"/>

    <%-- INICIO HEADER --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material_crm.jsp"/>
        </f:facet>
    </h:panelGroup>
    <%-- FIM HEADER --%>

    <%-- INICIO CONTENT --%>
    <h:panelGroup layout="block" styleClass="pure-g-r">
        <h:form id="form" styleClass="pure-form pure-u-1">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <input type="hidden" value="${modulo}" name="modulo"/>
            <input id="chavePrimaria" type="hidden" value="" name="chavePrimaria"/>

            <%-- INICIO CONTENT --%>
            <h:panelGroup>
               <%-- INICIO COMANDOS --%>
                <h:panelGroup layout="block" styleClass="pure-g-r pure-u-11-12 margin-0-auto">

                    <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-1 margin-v-10">
                        <h:panelGroup layout="block" style="width: 100%;">
                            <h:panelGroup layout="block" style="width: 100%; float: left;">
                                <h:panelGroup layout="block">
                                    <h:outputText value="Período de : "/>
                                    <rich:calendar id="dtInicio"
                                                   inputSize="7"
                                                   value="#{IndicacaoControle.dataInicioConsulta}"
                                                   showWeekDaysBar="false"
                                                   inputClass="form"
                                                   showWeeksBar="false"
                                                   oninputfocus="focusinput(this);"
                                                   oninputblur="blurinput(this);"
                                                   enableManualInput="false"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy">
                                    </rich:calendar>
                                </h:panelGroup>

                                <h:panelGroup layout="block">
                                    <h:outputText value=" até "/>
                                    <rich:calendar id="dtFim"
                                                   inputSize="7"
                                                   value="#{IndicacaoControle.dataFimConsulta}"
                                                   showWeekDaysBar="false"
                                                   inputClass="form"
                                                   showWeeksBar="false"
                                                   oninputfocus="focusinput(this);"
                                                   oninputblur="blurinput(this);"
                                                   enableManualInput="false"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy">
                                    </rich:calendar>
                                </h:panelGroup>
                                <rich:spacer width="8"/>
                                <h:panelGroup>
                                    <a4j:commandButton id="limparPeriodoEmissao"
                                                       onclick="document.getElementById('form:dtInicioInputDate').value = '';
                                                       document.getElementById('form:dtFimInputDate').value='';"
                                                       image="/images/limpar.gif" title="Limpar data de Emissao."
                                                       status="false"/>
                                </h:panelGroup>

                                <rich:spacer width="8"/>

                                <a4j:commandLink styleClass="botoes nvoBt " style="size: 11px"
                                                 value="Atualizar" action="#{IndicacaoControle.montarResultadosIndicacao}"
                                                 reRender="dashboard"
                                                 oncomplete="recarregarTabela()">

                                    <i class="fa-icon-refresh"></i>
                                </a4j:commandLink>
                                <rich:spacer width="8"/>

                                <h:panelGroup>
                                    <h:inputText id="buscaInput" value="#{IndicacaoControle.filtroConsulta}">
                                    </h:inputText>
                                </h:panelGroup>


                                <a4j:commandLink id="btnExcel"
                                                 styleClass="exportadores margin-h-10 linkPadrao pull-right"
                                                 actionListener="#{IndicacaoControle.exportar}"
                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','IndicaÃ§Ã£o', 800,200);#{ExportadorListaControle.msgAlert}"
                                                 accesskey="3">
                                    <f:attribute name="tipo" value="xls"/>
                                    <f:attribute name="atributos" value="codigo=Código,nomeIndicado=Nome,nomeClienteIndicou=Cliente Indicou,nomeColaboradorIndicou=Colaborador Indicou,dataLancamento_Apresentar=Data de Cadastro,eventoIndicacao=Evento,responsavelCadastro=Responsável Cadastro,telefone=Telefone,telefoneIndicado=Telefone 2,email=Email"/>
                                    <f:attribute name="prefixo" value="Indicacao"/>
                                    <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 excel"/>
                                </a4j:commandLink>

                                <a4j:commandLink id="btnPDF"
                                                 styleClass="exportadores margin-h-10 linkPadrao pull-right"
                                                 actionListener="#{IndicacaoControle.exportar}"
                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','IndicaÃ§Ã£o', 800,200);#{ExportadorListaControle.msgAlert}"
                                                 accesskey="4">
                                    <f:attribute name="tipo" value="pdf"/>
                                    <f:attribute name="atributos" value="codigo=Cádigo,nomeIndicado=Nome,nomeClienteIndicou=Cliente Indicou,nomeColaboradorIndicou=Colaborador Indicou,dataLancamento_Apresentar=Data de Cadastro,eventoIndicacao=Evento,responsavelCadastro=Responsável Cadastro,telefone=Telefone,telefoneIndicado=Telefone 2,email=Email"/>
                                    <f:attribute name="prefixo" value="Indicacao"/>
                                    <f:attribute name="titulo" value="Indicação"/>
                                    <a4j:actionparam noEscape="true" name="paramsTableFiltrada" value="retornaDadosDT()"/>
                                    <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
                <h:panelGroup styleClass="pure-g-r pure-u-11-12 margin-0-auto" id="dashboard">
                    <h:panelGrid columns="2"
                                 style="display: inline-flex;float: left; font-family: sans-serif; margin: 30px 10px 10px 0px; padding: 21px; width: 290px; border: 1px solid #D7D8DB; box-sizing: border-box;"
                                 cellspacing="0" cellpadding="0">
                        <h:graphicImage url="/images/user.png"
                                        style="padding-left: 40px; padding-right: 35px;"/>
                        <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                            <h:outputText value="Indicados" style="font-size: 16px; color: #51555A; "/>
                            <h:outputText id="totalIndicadosText" value="#{IndicacaoControle.totalIndicados}"
                                          style="font-weight: bold; font-size: 32px; color: #51555A;"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="2"
                                 style="display: inline-flex;float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #1998FC; box-sizing: border-box;"
                                 cellspacing="0" cellpadding="0">
                        <h:graphicImage url="/images/user-check.png"
                                        style="padding-left: 40px; padding-right: 35px;"/>
                        <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                            <h:outputText value="Convertidos" style="font-size: 16px; color: #51555A; "/>
                            <h:outputText  id="totalIndicacaoConvertidaText" value="#{IndicacaoControle.indicadosConvertidos}"
                                          style="font-weight: bold; font-size: 32px; color: #51555A;"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="2"
                                 style="display: inline-flex;float: left; font-family: sans-serif; margin: 30px 10px 10px 18px; padding: 21px; width: 290px; border: 1px solid #2EC750; box-sizing: border-box;"
                                 cellspacing="0" cellpadding="0">
                        <h:graphicImage url="/images/file-text.png"
                                        style="padding-left: 40px; padding-right: 35px;"/>
                        <h:panelGrid columns="1" cellpadding="0" cellspacing="0">
                            <h:outputText value="Resultados" style="font-size: 16px; color: #51555A; "/>
                            <h:outputText id="totalResultadosText" value="#{IndicacaoControle.totalResultados}"
                                          style="font-weight: bold; font-size: 32px; color: #51555A;"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGroup>
                <%-- FIM COMANDOS --%>

                <table id="tblIndicacao" class="tabelaIndicacao pure-g-r pure-u-11-12 margin-0-auto">
                    <thead>
                    <th>${"Código"}</th>
                    <th>${"Nome Indicado"}</th>
                    <th>${"Cliente que Indicou"}</th>
                    <th>${"Colaborador que Indicou"}</th>
                    <th>${"Data de Cadastro"}</th>
                    <th>${"Data da Conversão"}</th>
                    <th>${"Evento"}</th>
                    <th>${"Responsável Cadastro"}</th>
                    </thead>
                    <tbody></tbody>
                </table>

                <a4j:jsFunction name="jsEditar" action="#{IndicacaoControle.editar}" reRender="mensagem"/>

            </h:panelGroup>
            <%-- FIM CONTENT --%>

            <%-- INICIO FOOTER --%>
            <h:panelGroup id="mensagem" layout="block" styleClass="pure-u-11-12 margin-0-auto">
                <h:graphicImage id="iconSucesso" rendered="#{IndicacaoControle.sucesso}" value="./imagens/sucesso.png"/>
                <h:graphicImage id="iconErro" rendered="#{IndicacaoControle.erro}" value="./imagens/erro.png"/>

                <h:outputText styleClass="mensagem" rendered="#{not empty IndicacaoControle.mensagem}"
                              value=" #{IndicacaoControle.mensagem}"/>
                <h:outputText styleClass="mensagemDetalhada" rendered="#{not empty IndicacaoControle.mensagemDetalhada}"
                              value=" #{IndicacaoControle.mensagemDetalhada}"/>
            </h:panelGroup>
            <%-- FIM FOOTER --%>
        </h:form>

    </h:panelGroup>

    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
    <script src="beta/js/ext-funcs.js" type="text/javascript"></script>

    <script src="beta/js/dt-server.js" type="text/javascript"></script>
    <script>
        function recarregarTabela() {
            var dtInicio= document.getElementById("form:dtInicioInputDate").value;
            var dtFim = document.getElementById("form:dtFimInputDate").value;
            var buscar = document.getElementById("form:buscaInput").value;

            var configs = tabelaAtual.dataTableSettings[0];
            var sEcho = configs.iDraw;
            var iDisplayStart = configs._iDisplayStart;
            var iDisplayLength = configs._iDisplayLength;
            var sSearch = jQuery(".filtroDT").val().toLowerCase();
            var iSortCol_0 = configs.aaSorting[0][0];
            var sSortDir_0 = configs.aaSorting[0][1];

            var data = {"dtInicio": dtInicio, "dtFim": dtFim,"sEcho": sEcho, "iDisplayStart": iDisplayStart,
                "iDisplayLength": iDisplayLength, "sSearch": sSearch, "buscar": buscar,
                "iSortCol_0": iSortCol_0, "sSortDir_0": sSortDir_0};

            tabelaAtual.dataTable().fnDestroy(0);
            iniTblServer("tabelaIndicacao", "${contexto}/prest/crm/indicacao", data);
        }

        jQuery(window).on("load", function () {
            var dtInicio= document.getElementById("form:dtInicioInputDate").value;
            var dtFim = document.getElementById("form:dtFimInputDate").value;
            var buscar = document.getElementById("form:buscaInput").value;

            var data = {"dtInicio": dtInicio, "dtFim": dtFim, "buscar": buscar};
            iniTblServer("tabelaIndicacao", "${contexto}/prest/crm/indicacao", data);
        });
    </script>
</f:view>
<script>
    document.getElementById("form:buscaInput").setAttribute("placeholder", "Pesquisar");
</script>
