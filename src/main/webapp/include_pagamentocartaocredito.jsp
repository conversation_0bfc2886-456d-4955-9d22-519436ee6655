<%@page pageEncoding="ISO-8859-1"%>
<%--
    Document   : include_pagamentocartaocredito
    Created on : 19/07/2011, 09:21:26
    Author     : waller
--%>

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<f:loadBundle var="CElabels" basename="propriedades.CElabels"/>
<f:loadBundle var="Mensagens" basename="propriedades.Mensagens"/>
<%@include file="includes/imports.jsp" %>
<link href="./css/pacto_flat.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<script>
    function formataCampo(form, event) {
        var formatacao = document.getElementById('form:tipoFormatacao');
        if (formatacao.value == 'CPF') {
            return mascara(form, 'form:cpfPortador', '999.999.999-99', event);
        } else
            return mascara(form, 'form:cpfPortador', '99.999.999/9999-99', event);
    }

    function trocaCampo(mostrar) {
        var formatacao = document.getElementById('form:tipoFormatacao');
        var campoMostrar = document.getElementById('form:cpfPortador');
        campoMostrar.value = '';
        formatacao.value = mostrar;
    }

    function adicionarPlaceHolderValidadeCartao() {
        try {
            if (document.getElementById("form:validade") != null) {
                document.getElementById("form:validade").setAttribute("placeholder", "MM/AA");
            }
        } catch (e) {
            console.log("ERRO adicionarPlaceHolderAutorizacaoCobranca: " + e);
        }
    }

    function mascaraCartaoCreditoCaixaEmAberto() {
        try {
            var v = document.getElementById('form:nrCartao').value;
            v = v.replace(/^(\d{4})(\d)/g, "$1 $2");
            v = v.replace(/^(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3");
            v = v.replace(/^(\d{4})\s(\d{4})\s(\d{4})(\d)/g, "$1 $2 $3 $4");
            document.getElementById('form:nrCartao').value = v;
        } catch (e) {
            console.log("ERRO mascaraCartaoCredito: " + e);
        }
    }


</script>
<style>
    .comboBandeirasCartao label {
        margin-left: 14px;
    }

    .comboBandeirasCartao input {
        position: absolute;
        margin-top: 10px;
    }

    .comboBandeirasCartao img {
        width: 50px;
    }

    .inputCartao input {
        margin-right: 10px;
    }

    .nomeAlunoClick{
    font-family: Arial;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
    }

    .nrParcelasCartao {
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        text-decoration: none;
        line-height: normal;
        font-weight: bold;
        text-transform: none;
        color: #005bab;
    }

    .autorizacaoSelecionar {
        text-align: center;
    }

    .grid {
        background: #FAFAFA;
    }

    .header {
        font-size: 16px;
        line-height: 20px;
        font-family: Arial;
        color: #383B3E;
        padding: 24px 0px 0px 14px;
    }

    .cartao {
        border: 1px solid #D3D5D7;
        box-sizing: border-box;
        border-radius: 10px;
        width: 240px;
        margin: 24px 0px 0px 14px;
    }

    .cartao .titleWhite {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #E7E7E7;
    }

    .cartao .titleGrey {
        font-family: Arial;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 18px;
        color: #6F747B;
    }

    .white {
        background: #FFFFFF;
        border-radius: 10px;
    }

    .gradient {
        background: radial-gradient(98.33% 251.73% at 96.88% 64%, #4AB5E3 2.89%, #1B9FFD 45.13%, #0078D0 72.63%, #005A93 100%);
    }

    .cartaoRodaPe {
        background: #013E6F !important;
        border-radius: 0px 0px 10px 10px;
    }

    .group {
        display: flex;
    }

    .sub-group {
        float: left;
        margin: 11px;
    }

    .title {
        font-family: Arial;
        font-style: normal;
        font-weight: normal;
        font-size: 14px;
        line-height: 16px;
        color: #6F747B;
    }

    .circleQuestion {
        width: 21px;
        height: 21px;
        border-radius: 100%;
        background: #D3D5D7;
        border: 1px solid #D3D5D7;
        color: #0078D0;
        display: flex;
        margin: 8px;
    }

</style>

<a4j:outputPanel layout="block" id="panelPagamentoCartaoAPF" styleClass="grid" style="margin-top: -30px;width: 59%">
    <!-- ------------------------ INICIO - CONFIGURACOES DO PAGAMENTO -------------------------------- -->
    <h:panelGroup id="panelConteudo">
        <h:panelGroup style="display: flex; justify-content: space-between;">
            <h:outputText rendered="#{PagamentoCartaoCreditoControle.urlTeste}"
                          styleClass="mensagemDetalhada"
                          value="Configuração usada é de teste, portanto o resultado dessa transaçao não ser financeiramente válido"/>
            <h3 class="header">Dados da cobrança</h3>

            <a4j:commandLink
                    styleClass="header"
                    style="font-size: 14px !important; padding-right: 20px; text-decoration: none"
                    rendered="#{MovPagamentoControle.movPagamentoVO.usarPagamentoAprovaFacil && !empty PagamentoCartaoCreditoControle.movPagamentoSelecionado}"
                    tabindex="13"
                    action="#{MovPagamentoControle.escolherOutraFormaPagamento}"
                    reRender="form"
                    oncomplete="montaModulomenu();"
                    value="Alterar forma de pagamento"/>
        </h:panelGroup>
        <h:panelGroup>

            <%--<h:outputText
                    rendered="#{not empty PagamentoCartaoCreditoControle.autorizacoes || not empty PagamentoCartaoCreditoControle.autorizacoesTemp}"
                    style="font-weight:normal" value="Usar outro Cartão "/>
            <h:selectBooleanCheckbox
                    rendered="#{not empty PagamentoCartaoCreditoControle.autorizacoes || not empty PagamentoCartaoCreditoControle.autorizacoesTemp}"
                    styleClass="form" style="padding-top: 0;"
                    value="#{PagamentoCartaoCreditoControle.usarOutroCartao}">
                <a4j:support event="onchange"
                             reRender="panelConteudo"
                             action="#{PagamentoCartaoCreditoControle.definirUsarOutroCartao}"/>
            </h:selectBooleanCheckbox>--%>

            <%-- UTILIZAR IDVINDI --%>
            <c:if test="${PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca && !PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}">

                <div class="sub-group" style="margin-left: 17px; max-width: 49%">
                    <div>
                        <h:outputText rendered="#{!PagamentoCartaoCreditoControle.labelColaborador}"
                                      styleClass="title"
                                      value="Nome do Aluno:"/>
                    </div>
                    <div>
                        <h:outputText rendered="#{PagamentoCartaoCreditoControle.labelColaborador}"
                                      styleClass="title tooltipster"
                                      title="A parcela que você irá receber é de um colaborador"
                                      value="Nome do colaborador:"/>
                    </div>
                    </br>
                    <div>
                        <a4j:commandLink value="#{MovPagamentoControle.nomePagadorAbreviado}"
                                         style="margin-top: 5px;"
                                         styleClass="linkAzul nomeAlunoClick upper"
                                         action="#{PagamentoCartaoCreditoControle.irParaTelaClienteColaborador}"
                                         oncomplete="#{PagamentoCartaoCreditoControle.onComplete};#{PagamentoCartaoCreditoControle.mensagemNotificar}"/>
                    </div>
                </div>
                <div class="sub-group" style="margin-left: 35px">
                    <div>
                        <h:outputText styleClass="title"
                                      value="Valor:"/>
                    </div>
                    </br>
                    <div>
                        <h:outputText value="R$ #{PagamentoCartaoCreditoControle.movPagamentoSelecionado.valorTotalApresentar}" styleClass="texto-font texto-size-16 texto-cor-cinza" style="margin-top: 5px;"/>
                    </div>
                </div>
                <div class="group" style="justify-content: flex-start;">
                    <div class="sub-group" style="margin-left: 35px">
                        <div>
                            <h:outputText styleClass="title"
                                          style="margin-left: 4px;"
                                          value="Data do Pagamento:"/>
                        </div>

                        <div style="margin-top: 5px;">
                            <h:panelGroup styleClass="dateTimeCustom" style=" margin-left: 3px">
                                <rich:calendar value="#{MovPagamentoControle.dataPagto}"
                                               inputSize="10"
                                               inputClass="form"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                            </h:panelGroup>
                        </div>
                    </div>
                </div>
                <div class="sep" style="margin:4px 0 5px 0;"/>


                <h:graphicImage style="width: 70px; height: 20px; margin-left: 13px; padding-top: 20px"
                                url="imagens/externo/logo-vindi.png"/>
                <br/>
                <br/>
                <h:panelGroup style="margin-top: 10px; display: inline-grid; margin-left: 13px">
                    <h:outputText style="font-weight: normal"
                                  value="Este aluno possui id vindi cadastrado em sua autorização de cobrança. Será utilizado o IdVindi para realizar a cobrança."/>
                    <br/>
                    <br/>
                   <h:outputText style="font-weight: normal"
                                 value="IdVindi do cliente: #{PagamentoCartaoCreditoControle.pessoaPagamento.idVindi} "/>
                    <br/>
                    <br/>
                </h:panelGroup>

                <!-- PARCELAS -->
                <h:outputText style="font-weight:normal; margin-left: 13px" value="Número de parcelas:"/>
                <h:panelGroup>
                    <h:selectOneMenu id="nrParcelasVindi"
                                     tabindex="7"
                                     valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                                     value="#{PagamentoCartaoCreditoControle.dadosPagamento.parcelas}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" style="margin-left: 5px">
                        <f:selectItems
                                value="#{MovPagamentoControle.listaSelectItemNrParcelaCartaoVindiOuCielo}"/>
                            <f:attribute name="movPagamento"
                                         value="#{PagamentoCartaoCreditoControle.movPagamentoSelecionado}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <br/>
                <br/>
                <br/>
                <h:outputText style="font-weight: normal; margin-left: 14px" value="Você também pode "/>
                <a4j:commandLink reRender="panelConteudo"
                                 style="margin-left: 1px"
                                 value="Informar um novo cartão"
                                 action="#{PagamentoCartaoCreditoControle.informarNovoCartao}">
                </a4j:commandLink>
            </c:if>


            <%-- UTILIZAR TOKEN CIELO --%>
            <c:if test="${!PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca && PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}">

                <div class="sub-group" style="margin-left: 17px; max-width: 49%">
                    <div>
                        <h:outputText rendered="#{!PagamentoCartaoCreditoControle.labelColaborador}"
                                      styleClass="title"
                                      value="Nome do Aluno:"/>
                    </div>
                    <div>
                        <h:outputText rendered="#{PagamentoCartaoCreditoControle.labelColaborador}"
                                      styleClass="title tooltipster"
                                      title="A parcela que você irá receber é de um colaborador"
                                      value="Nome do colaborador:"/>
                    </div>
                    </br>
                    <div>
                        <a4j:commandLink value="#{MovPagamentoControle.nomePagadorAbreviado}"
                                         style="margin-top: 5px;"
                                         styleClass="linkAzul nomeAlunoClick upper"
                                         action="#{PagamentoCartaoCreditoControle.irParaTelaClienteColaborador}"
                                         oncomplete="#{PagamentoCartaoCreditoControle.onComplete};#{PagamentoCartaoCreditoControle.mensagemNotificar}"/>
                    </div>
                </div>
                <div class="sub-group" style="margin-left: 35px">
                    <div>
                        <h:outputText styleClass="title"
                                      value="Valor:"/>
                    </div>
                    </br>
                    <div>
                        <h:outputText value="R$ #{PagamentoCartaoCreditoControle.movPagamentoSelecionado.valorTotalApresentar}" styleClass="texto-font texto-size-16 texto-cor-cinza" style="margin-top: 5px;"/>
                    </div>
                </div>
                <div class="group" style="justify-content: flex-start;">
                    <div class="sub-group" style="margin-left: 35px">
                        <div>
                            <h:outputText styleClass="title"
                                          style="margin-left: 4px;"
                                          value="Data do Pagamento:"/>
                        </div>

                        <div style="margin-top: 5px;">
                            <h:panelGroup styleClass="dateTimeCustom" style=" margin-left: 3px">
                                <rich:calendar value="#{MovPagamentoControle.dataPagto}"
                                               inputSize="10"
                                               inputClass="form"
                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                               oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);"
                                               oninputchange="return validar_Data(this.id);"
                                               datePattern="dd/MM/yyyy"
                                               enableManualInput="true"
                                               zindex="2"
                                               showWeeksBar="false"/>
                            </h:panelGroup>
                        </div>
                    </div>
                </div>
                <div class="sep" style="margin:4px 0 5px 0;"/>

                <h:graphicImage style="width: 70px; height: 20px; margin-left: 13px; padding-top: 20px;"
                                url="imagens/externo/cielo-logo-new.png"/>
                <br/>
                <br/>
                <h:panelGroup style="margin-top: 10px; display: inline-grid; margin-left: 13px">
                    <h:outputText style="font-weight: normal"
                                  value="Este aluno possui token Cielo cadastrado em sua autorização de cobrança. Será utilizado o token para realizar a cobrança."/>
                    <br/>
                    <br/>
                    <h:outputText style="font-weight: bold"
                                  styleClass="tooltipster"
                                  title="Token que será utilizado para realizar a cobrança deste aluno"
                                  value="Token Cielo do cliente: "/>
                    <h:outputText style="font-weight: normal"
                                  styleClass="tooltipster"
                                  title="Token que será utilizado para realizar a cobrança deste aluno"
                                  value="#{PagamentoCartaoCreditoControle.dadosPagamento.tokenCielo} "/>
                    <br/>
                    <br/>
                </h:panelGroup>

                <!-- PARCELAS -->
                <h:outputText style="font-weight:normal; margin-left: 13px" value="Número de parcelas:"/>
                <h:panelGroup>
                    <h:selectOneMenu id="nrParcelasCielo"
                                     tabindex="7"
                                     valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                                     value="#{PagamentoCartaoCreditoControle.dadosPagamento.parcelas}"
                                     onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" style="margin-left: 5px">
                        <f:selectItems
                                value="#{MovPagamentoControle.listaSelectItemNrParcelaCartaoVindiOuCielo}"/>
                            <f:attribute name="movPagamento"
                                         value="#{PagamentoCartaoCreditoControle.movPagamentoSelecionado}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <br/>
                <br/>
                <br/>
                <h:outputText style="font-weight: normal; margin-left: 14px" value="Você também pode "/>
                <a4j:commandLink reRender="panelConteudo"
                                 style="margin-left: 1px"
                                 value="Informar um novo cartão"
                                 action="#{PagamentoCartaoCreditoControle.informarNovoCartao}">
                </a4j:commandLink>
            </c:if>

            <c:if test="${!PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca && !PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}">
                <div class="sub-group" style="margin-left: 17px; max-width: 49%">
                    <div>
                        <h:outputText rendered="#{!PagamentoCartaoCreditoControle.labelColaborador}"
                                      styleClass="title"
                                      value="Nome do Aluno:"/>
                    </div>
                    <div>
                        <h:outputText rendered="#{PagamentoCartaoCreditoControle.labelColaborador}"
                                      styleClass="title tooltipster"
                                      title="A parcela que você irá receber é de um colaborador"
                                      value="Nome do colaborador:"/>
                    </div>
                    </br>
                    <div>
                        <a4j:commandLink value="#{MovPagamentoControle.nomePagadorAbreviado}"
                                         style="margin-top: 5px;"
                                         styleClass="linkAzul nomeAlunoClick upper"
                                         action="#{PagamentoCartaoCreditoControle.irParaTelaClienteColaborador}"
                                         oncomplete="#{PagamentoCartaoCreditoControle.onComplete};#{PagamentoCartaoCreditoControle.mensagemNotificar}"/>
                    </div>
                </div>
                <div class="sub-group" style="margin-left: 35px">
                    <div>
                    <h:outputText styleClass="title"
                                  value="Valor:"/>
                    </div>
                        </br>
                    <div>
                        <h:outputText value="R$ #{PagamentoCartaoCreditoControle.movPagamentoSelecionado.valorTotalApresentar}" styleClass="texto-font texto-size-16 texto-cor-cinza" style="margin-top: 5px;"/>
                    </div>
                </div>
                <div class="group" style="justify-content: flex-start;">
                    <div class="sub-group" style="margin-left: 35px">
                        <div>
                        <h:outputText styleClass="title"
                                      style="margin-left: 4px;"
                                      value="Data do Pagamento:"/>
                        </div>

                    <div style="margin-top: 5px;">
                        <h:panelGroup styleClass="dateTimeCustom" style=" margin-left: 3px">
                            <rich:calendar value="#{MovPagamentoControle.dataPagto}"
                                           inputSize="10"
                                           inputClass="form"
                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                        </h:panelGroup>
                    </div>
                    </div>
                </div>

                <div class="sep" style="margin:4px 0 5px 0;"/>

                <h:panelGroup layout="block"
                              style="display: flex;">
                    <h:panelGroup layout="block" id="cardCartao"
                                  styleClass="cartao #{PagamentoCartaoCreditoControle.styleClassCartao}">
                        <div style="padding: 10px">
                            <h:panelGroup layout="block" id="chipcard" style="margin: 16px;">
                                <h:graphicImage value="imagens_flat/icon-chip.svg" width="25" height="18"/>
                            </h:panelGroup>

                            <h:panelGroup layout="block" id="nomeTitularCartao" style="text-transform: uppercase">
                                <h:outputText styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoTitles}"
                                              value="#{PagamentoCartaoCreditoControle.dadosPagamento.nomeTitular == '' ? 'Nome Titular do cartão' : PagamentoCartaoCreditoControle.dadosPagamento.nomeTitular}"/>
                            </h:panelGroup>

                            <div style="display: flex; justify-content: space-between; margin: 3% 0 3% 0;">
                                <h:panelGroup id="numeroCartaoApresentar">
                                    <h:outputText styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoTitles}"
                                                  value="#{PagamentoCartaoCreditoControle.dadosPagamento.numero == '' || PagamentoCartaoCreditoControle.dadosPagamento.numero == null ? '**** **** **** ****' : PagamentoCartaoCreditoControle.dadosPagamento.numero}"/>
                                </h:panelGroup>
                                <h:panelGroup id="datesCartao">
                                    <h:outputText styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoTitles}"
                                                  value="#{PagamentoCartaoCreditoControle.dadosPagamento.validade == '' ? '00/00' : PagamentoCartaoCreditoControle.dadosPagamento.validade}"/>
                                </h:panelGroup>
                            </div>
                            <h:panelGroup layout="block"
                                          rendered="#{PagamentoCartaoCreditoControle.styleClassCartaoRodape == 'cartaoRodaPe' ? false : true}"
                                          style="border: 1px solid #D3D5D7; margin: auto;"/>
                        </div>
                        <h:panelGroup layout="block"
                                      styleClass="#{PagamentoCartaoCreditoControle.styleClassCartaoRodape}">
                            <h:panelGroup style="display: flex; justify-content: space-between; padding: 10px;">
                                <div>
                                    <h:panelGroup layout="block" id="imagemCartao"
                                                  rendered="#{PagamentoCartaoCreditoControle.dadosPagamento.band != null && !PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca && !PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}">
                                        <h:graphicImage
                                                url="/imagens_flat/#{PagamentoCartaoCreditoControle.dadosPagamento.band.imagem}-icon.svg"
                                                width="30" height="30"/>
                                    </h:panelGroup>
                                </div>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                    <h:panelGroup layout="block" style="margin-left: 2%; margin-block-start: auto;">
                        <a4j:commandLink action="#{PagamentoCartaoCreditoControle.limparDadosDoCartao}"
                                         style="text-decoration: none"
                                         reRender="form:panelConteudo">
                            <i class="fa-icon-eraser texto-size-18"></i>
                            <h:outputText styleClass="title" value="Limpar dados"/>
                        </a4j:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

                <div class="group" style="padding-top: 10px">

                    <!-- CONVÊNIO DE COBRANÇA-->
                    <div class="sub-group">
                        <h:outputText styleClass="title" value="Convênio de cobrança"/>
                        </br>
                        <h:panelGroup layout="block" styleClass="block cb-container" style="margin-top: 5px;">
                            <h:selectOneMenu id="comboTipoTransacao"
                                             disabled="#{PagamentoCartaoCreditoControle.dadosPagamento.usarIdVindiCobranca or PagamentoCartaoCreditoControle.dadosPagamento.usarTokenCieloCobranca}"
                                             value="#{PagamentoCartaoCreditoControle.convenioCobrancaSelecionado}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form" style="padding-top: 0;">
                                <f:selectItems value="#{PagamentoCartaoCreditoControle.convenioCobrancaSelectItem}"/>
                                <a4j:support event="onchange" reRender="panelConteudo"
                                             action="#{PagamentoCartaoCreditoControle.acaoMudarConvenioCobranca}"
                                             oncomplete="adicionarPlaceHolderValidadeCartao()"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>
                    <div class="sub-group">
                        <h:outputText styleClass="title" value="CPF/CNPJ Titular"/>
                        </br>
                        <h:inputText id="cpfTitularAutorizacaoCobranca"
                                     value="#{PagamentoCartaoCreditoControle.dadosPagamento.cpfCnpjPortador}"
                                     style="margin-top: 5px; width: 110px; height: 32px !important;"
                                     tabindex="1"
                                     size="20" styleClass="form"
                                     onfocus="focusinput(this);"
                                     onblur="blurinput(this);"
                                     maxlength="20"/>
                    </div>
                </div>

                <!-- NOME DO TITULAR -->
                <div class="group">
                   <div class="sub-group">
                        <h:outputText styleClass="title" value="Nome titular do cartão"/>
                        </br>
                        <h:inputText id="nomeTitular"
                                     value="#{PagamentoCartaoCreditoControle.dadosPagamento.nomeTitular}"
                                     size="50" styleClass="form"
                                     style="text-transform: uppercase; margin-top: 10px; width: 80%"
                                     onkeyup="tabAutom(this)"
                                     onkeypress="return permiteSomenteLetra(this.form, this.id, event);"
                                     tabindex="2" onfocus="focusinput(this);" onblur="blurinput(this);"
                                     maxlength="50">
                            <a4j:support event="onchange" reRender="nomeTitularCartao, cardCartao, panelConteudo"
                                         action="#{PagamentoCartaoCreditoControle.inicializarCard}"
                                         oncomplete="document.getElementById('form:nrCartao').focus()"/>
                        </h:inputText>
                    </div>
                </div>

                <!-- NÚMERO DO CARTÃO -->
                <div class="group">
                    <div class="sub-group">
                        <h:outputText styleClass="title" value="Número do cartão"/>
                        </br>
                        <h:inputText id="nrCartao"
                                     value="#{PagamentoCartaoCreditoControle.dadosPagamento.numero}"
                                     styleClass="form"
                                     style="margin-top: 5px; width: 83%"
                                     maxlength="19"
                                     tabindex="3"
                                     onkeypress="mascaraCartaoCreditoCaixaEmAberto()">
                            <a4j:support event="onchange"
                                         reRender="numeroCartaoApresentar, panelConteudo, cardCartao, selectBandeira"
                                         action="#{PagamentoCartaoCreditoControle.buscaBandeiraCartaoOperadora}"
                                         oncomplete="document.getElementById('form:validade').focus();mascaraCartaoCreditoCaixaEmAberto()"/>
                        </h:inputText>
                    </div>
                        <%-- BANDEIRA --%>
                    <div class="sub-group" style="margin-left: -20px;">
                        <h:outputText styleClass="title"
                                      value="Bandeira"/>
                        </br>
                        <h:panelGroup layout="block" styleClass="block cb-container" style="margin-top: 5px; width: 136%;min-width: 104px;" id="selectBandeira">
                            <h:selectOneMenu
                                    value="#{PagamentoCartaoCreditoControle.operadoraCartao}"
                                    tabindex="4"
                                    onblur="blurinput(this);" onfocus="focusinput(this);"
                                    styleClass="form" style="padding-top: 0;">
                                <f:selectItems value="#{PagamentoCartaoCreditoControle.operadorasCartaoCredito}"/>
                                <a4j:support event="onchange" reRender="panelConteudo, cardCartao"
                                             action="#{PagamentoCartaoCreditoControle.selecionaOperadora}"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>
                </div>

                <div class="group">
                    <!-- VENCIMENTO -->
                    <div class="sub-group">
                        <h:outputText styleClass="title" value="Data de vencimento"/>
                        </br>

                        <h:inputText id="validade" value="#{PagamentoCartaoCreditoControle.dadosPagamento.validade}"
                                     style="width: 80px; height: 32px !important; margin-right: 1rem; margin-top: 5px;"
                                     onkeypress="return mascara(this.form, this.id, '99/99', event);"
                                     styleClass="inputTextClean" onfocus="focusinput(this);"
                                     tabindex="5"
                                     onblur="blurinput(this);">
                            <a4j:support event="onchange" reRender="datesCartao" oncomplete="document.getElementById('form:codSeguranca').focus()"/>
                        </h:inputText>
                    </div>

                    <!-- CODIGO SEGURANCA -->
                    <div class="sub-group" style="margin-left: 38px;">
                        <h:outputText styleClass="title" value="CVV"/>
                        </br>
                        <h:panelGroup layout="block" style="display: flex; align-items: center;">
                            <h:inputSecret id="codSeguranca"
                                           value="#{PagamentoCartaoCreditoControle.dadosPagamento.codigoSeguranca}"
                                           onkeypress="return mascara(this.form, this.id, '9999', event);"
                                           tabindex="6"
                                           onkeyup="somenteNumeros(this);"
                                           size="4" styleClass="inputTextClean"
                                           style="width: 68px; height: 32px !important;"
                                           onfocus="focusinput(this);" onblur="blurinput(this);"
                                           maxlength="4">
                            </h:inputSecret>
                            <h:panelGroup>
                                <h:panelGroup id="botaoHelp"
                                              layout="block"
                                              styleClass="circleQuestion">
                                    <h:outputText style="margin: auto;" value="?"/>
                                    <rich:toolTip for="botaoHelp">
                                        <h:panelGrid width="189" columns="2">
                                            <rich:panel
                                                    rendered="#{PagamentoCartaoCreditoControle.dadosPagamento.band.id != 4}"
                                                    header="#{PagamentoCartaoCreditoControle.dadosPagamento.band.descricao}">
                                                <h:graphicImage width="189"
                                                                value="images/cartao_visa_cvc2.gif"/>
                                                <h:outputText
                                                        value="O código de segurança está localizado no verso do cartão e corresponde aos três últimos dígitos da faixa numérica."/>
                                            </rich:panel>
                                            <rich:panel
                                                    rendered="#{PagamentoCartaoCreditoControle.dadosPagamento.band.id == 4}"
                                                    header="American Express">
                                                <h:graphicImage width="189"
                                                                value="images/cartao_amex_cvc2.gif"/>
                                                <h:outputText
                                                        value="O código de segurança está localizado na parte frontal do cartão American Express e corresponde aos quatro dígitos localizados do lado direito acima da faixa numérica do cartão."/>
                                            </rich:panel>
                                        </h:panelGrid>
                                    </rich:toolTip>
                                </h:panelGroup>
                            </h:panelGroup>
                        </h:panelGroup>
                    </div>
                </div>

                <div class="group">

                    <!-- NÚMERO DE PARCELAS-->
                    <div class="sub-group">
                        <h:outputText styleClass="title" value="N° de parcelas"/>
                        </br>
                        <h:panelGroup layout="block" styleClass="block cb-container" style="margin-top: 5px; width: 108%">
                            <h:selectOneMenu id="nrParcelasAPF"
                                             tabindex="7"
                                             valueChangeListener="#{MovPagamentoControle.atualizarOperadoraCartao}"
                                             value="#{PagamentoCartaoCreditoControle.dadosPagamento.parcelas}"
                                             onblur="blurinput(this);" onfocus="focusinput(this);"
                                             styleClass="form">
                                <f:selectItems
                                        value="#{MovPagamentoControle.listaSelectItemNrParcelaCartao}"/>
                                <a4j:support event="onchange"
                                             reRender="panelParcStone">
                                    <f:attribute name="movPagamento"
                                                 value="#{PagamentoCartaoCreditoControle.movPagamentoSelecionado}"/>
                                </a4j:support>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>

                <h:panelGroup id="panelParcStone">
                        <%-- PARCELAMENTO STONE --%>
                    <div class="sub-group" style="margin-left: 55px">
                        <h:outputText rendered="#{PagamentoCartaoCreditoControle.convenioStone && PagamentoCartaoCreditoControle.dadosPagamento.parcelas > 1}"
                                      styleClass="title tooltipster"
                                      title="Informe o tipo de parcelamento Stone"
                                      value="Tipo de parcelamento"/>
                        </br>
                        <h:panelGroup rendered="#{PagamentoCartaoCreditoControle.convenioStone && PagamentoCartaoCreditoControle.dadosPagamento.parcelas > 1}"
                                      layout="block"
                                      styleClass="block cb-container"
                                      style="margin-top: 5px;">
                            <h:selectOneMenu id="tipoparcelamentostone"
                                             value="#{PagamentoCartaoCreditoControle.tipoParcelamentoStone}"
                                             valueChangeListener="#{PagamentoCartaoCreditoControle.atualizarTipoPagamentoStone}"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             style="padding-top: 0;">
                                <a4j:support event="onchange" focus="tipoparcelamentostone" reRender="panelConteudo"/>
                                <f:selectItem itemLabel=""/>
                                <f:selectItem itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_lojista}"
                                              itemValue="MCHT"/>
                                <f:selectItem itemLabel="#{msg_aplic.prt_tipo_parcelamento_stone_emissor}"
                                              itemValue="ISSR"/>
                            </h:selectOneMenu>
                        </h:panelGroup>
                    </div>
                </div>
                </h:panelGroup>
            </c:if>

            <!-- ------------------------ PAINEL DE MENSAGENS -------------------------------- -->

            <table width="100%">
                <tr>
                    <td>
                        <h:panelGrid id="mensagens">
                            <h:outputText id="msgFormasPag" styleClass="mensagem"
                                          value="#{PagamentoCartaoCreditoControle.mensagem}"/>
                            <h:outputText id="msgFormasPagDet" styleClass="mensagemDetalhada"
                                          value="#{PagamentoCartaoCreditoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>
        </h:panelGroup>

        <a4j:jsFunction name="gatewayTokenVindi">
            <a4j:actionparam name="param1"
                             assignTo="#{PagamentoCartaoCreditoControle.dadosPagamento.gatewayTokenVindi}"/>
        </a4j:jsFunction>

        <h:panelGroup layout="block" id="panelScriptVindiPublica">
            <script>
                function cadastrarCardVindiPublica() {
                    try {

                        jQuery("[id='form:gatewayTokenVindi']").val('');

                        var numeroCartao = jQuery("[id='form:nrCartao']").val();
                        var nomeTitular = jQuery("[id='form:nomeTitular']").val();
                        var codSeguranca = jQuery("[id='form:codSeguranca']").val();
                        var anoValidade = jQuery("[id='form:validade']").val();
                        var band = '${PagamentoCartaoCreditoControle.dadosPagamento.band.descricaoParaVindi}';

                        var profile = {
                            "holder_name": nomeTitular,
                            "card_expiration": anoValidade,
                            "card_number": numeroCartao,
                            "card_cvv": codSeguranca,
                            "payment_method_code": "credit_card",
                            "payment_company_code": band
                        };

                        console.log(profile);

                        var authorization = ("Basic " + '${PagamentoCartaoCreditoControle.chavePublicaGatewayTokenVindi}');

                        jQuery.ajax({
                            type: "POST",
                            url: '${PagamentoCartaoCreditoControle.urlGatewayTokenVindi}',
                            dataType: "json",
                            beforeSend: function (xhr) {
                                xhr.setRequestHeader('Authorization', authorization);
                            },
                            data: profile,
                            success: function (data) {
                                console.log(data);
                                gatewayTokenVindi(data.payment_profile.gateway_token);
                                Richfaces.showModalPanel('panelUsuarioSenha');
                            },
                            error: function (request, status, error) {
                                console.log(error);
                                console.log(request.responseText);
                                Notifier.error(request.responseText, "Erro ao cadastrar cartão Vindi");
                            },
                            async: false
                        });
                    } catch (ex) {
                        console.log(ex);
                    }
                }
            </script>
        </h:panelGroup>
    </h:panelGroup>


    <%--GRID SELECIONAR AUTORIZAÇÃO QUANDO TEM MAIS DE UMA--%>
    <h:panelGroup>
        <table style="display: flex">
        <tr>
        <c:if test="${fn:length(PagamentoCartaoCreditoControle.autorizacoes) > 1}">
            <td style="vertical-align: top;" width="40%">
                <rich:dataTable id="tabelaCartoes" width="100%" style="margin-right: 15px"
                                value="#{PagamentoCartaoCreditoControle.autorizacoes}"
                                var="auto">

                    <rich:column width="30%" styleClass="autorizacaoSelecionar">
                        <f:facet name="header">
                            <h:outputText value="Número do Cartão"/>
                        </f:facet>
                        <a4j:commandLink reRender="form:panelConteudo, form:tabelaCartoes"
                                         action="#{PagamentoCartaoCreditoControle.selecionarAutorizacaoCobrancaCliente}">
                            <h:outputText rendered="#{auto.autorizacaoUtilizandoIdVindiPessoa && !auto.autorizacaoUtilizandotokenCielo}"
                                          value="Vindi"/>
                            <h:outputText rendered="#{auto.autorizacaoUtilizandotokenCielo && !auto.autorizacaoUtilizandoIdVindiPessoa}"
                                          value="Cielo"/>
                            <h:outputText id="cartaoNumero"
                                          rendered="#{!auto.autorizacaoUtilizandoIdVindiPessoa && !auto.autorizacaoUtilizandotokenCielo}"
                                          value="#{auto.cartaoMascarado}"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column width="50%" styleClass="autorizacaoSelecionar">
                        <f:facet name="header">
                            <h:outputText value="Titular do Cartão"/>
                        </f:facet>
                        <a4j:commandLink reRender="form:panelConteudo, form:tabelaCartoes"
                                         action="#{PagamentoCartaoCreditoControle.selecionarAutorizacaoCobrancaCliente}">
                            <h:outputText rendered="#{auto.autorizacaoUtilizandoIdVindiPessoa && !auto.autorizacaoUtilizandotokenCielo}"
                                          value="IdVindi: #{PagamentoCartaoCreditoControle.pessoaPagamento.idVindi}"/>
                            <h:outputText rendered="#{auto.autorizacaoUtilizandotokenCielo && !auto.autorizacaoUtilizandoIdVindiPessoa}"
                                          value="Token Cielo: #{auto.tokenCielo}"/>
                            <h:outputText id="cartaoNomeTitular"
                                          rendered="#{!auto.autorizacaoUtilizandotokenCielo && !auto.autorizacaoUtilizandoIdVindiPessoa}"
                                          value="#{auto.nomeTitularCartao}"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column width="20%" styleClass="autorizacaoSelecionar">
                        <f:facet name="header">
                            <h:outputText value="Validade"/>
                        </f:facet>
                        <a4j:commandLink reRender="form:panelConteudo, form:tabelaCartoes"
                                         action="#{PagamentoCartaoCreditoControle.selecionarAutorizacaoCobrancaCliente}">
                            <h:outputText id="cartaoValidade" style="text-align: left"
                                          rendered="#{!auto.autorizacaoUtilizandoIdVindiPessoa}"
                                          value="#{auto.validadeCartao}"/>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column width="10%" styleClass="autorizacaoSelecionar">
                        <f:facet name="header">
                            <h:outputText value=""/>
                        </f:facet>
                        <a4j:commandLink reRender="form:panelConteudo, form:tabelaCartoes"
                                         action="#{PagamentoCartaoCreditoControle.selecionarAutorizacaoCobrancaCliente}">
                            <h:panelGroup rendered="#{auto.selecionado}"><i
                                    class="fa-icon-ok green"></i></h:panelGroup>
                            <h:panelGroup rendered="#{!auto.selecionado}"><i
                                    class="fa-icon-ok gray"></i></h:panelGroup>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
            </td>
        </c:if>
    </h:panelGroup>


</a4j:outputPanel>

<rich:modalPanel id="panelAguardandoConfirmacaoPagamento" autosized="true"
                 shadowOpacity="true" width="400"
                 styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Atenção"/>
        </h:panelGroup>
    </f:facet>

    <h:panelGroup layout="block" id="panelModalAguardandoConfirmacaoPagamento" style="padding: 15px">
        <h:panelGroup layout="block">
            <h:outputText styleClass="texto-size-20"
                          escape="false"
                          value="O pagamento foi enviado com sucesso, porém a operadora ainda não confirmou o pagamento.
                          <br/>Assim que a confirmaçao for recebida, ela será exibida na tela de gestão de transações"/>
        </h:panelGroup>
        <br/>
        <br/>
        <h:panelGroup layout="block" style="text-align: center; padding: 15px 0 0 0;">
            <a4j:commandLink styleClass="pure-button pure-button-primary"
                             reRender="form"
                             value="Fechar"
                             action="#{PagamentoCartaoCreditoControle.fecharAlertaAguardandoPagamento}">
            </a4j:commandLink>
        </h:panelGroup>
    </h:panelGroup>
</rich:modalPanel>

<!-- ------------------------ FIM - CONFIGURACOES DO PAGAMENTO -------------------------------- -->

