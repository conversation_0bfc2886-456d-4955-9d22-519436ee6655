<%@include file="includes/imports.jsp" %>

<script>
    function showModalQRCode() {
        document.getElementById('form:idmdlqrcode').style.display = 'block';
    }
    function showQRCode() {
        document.getElementById('form:painelmodalacessoappgestor').style.display = 'block';
        document.getElementById('form:paineldadosqrcode').style.display = 'none';
        document.getElementById('form:btnenviaremailAppg').click();
    }

    function hideModalQRCode() {
        document.getElementById('form:idmdlqrcode').style.display = 'none';
    }
                var diferencaQrCode = 30 * 1000;
                var dataFinalQrCode = new Date(new Date().getTime() + diferencaQrCode);
    function startQRCode() {
        diferencaQrCode = 30 * 1000;
        dataFinalQrCode = new Date(new Date().getTime() + diferencaQrCode);
        startCountdownQRCode();
    }
                function startCountdownQRCode() {
                    var agoraQrCode = new Date();
                    var numberCountdown = document.getElementById('sessaoQRCode');
                    diferencaQrCode = dataFinalQrCode.getTime() - agoraQrCode.getTime();
                    var texto = Math.floor(diferencaQrCode / 1000);
                    if (diferencaQrCode > 0) {
                        if (document.all) {
                            numberCountdown.innerText = texto;
                        } else {
                            numberCountdown.textContent = texto;
                        }
                        setTimeout("startCountdownQRCode()", 500);
                    } else {
            hideModalQRCode();
                    }
                }


</script>

<h:panelGroup layout="block" style="z-index: 9999; background-color: rgba(208,208,208,.5) !important; display: none;" styleClass="rich-mpnl-mask-div"
              id="idmdlqrcode">


    <h:panelGroup layout="block" style="position: absolute; width:500px; height:auto; top:100px; margin-left: calc(50% - 250px)"
                  rendered="#{LoginControle.qrCodeAutorizado}"
                  styleClass="novaModal mdlSimples">
        <h:panelGroup layout="block" styleClass="rich-mpnl-header-cell" style="line-height: 74px;">
            <h:panelGroup layout="block" styleClass="rich-mpnl-header">
                <h:outputText value="#{msg_aplic.prt_modal_app_gestor_titulo}" rendered="#{LoginControle.tipoQR.nome eq 'APPGESTOR' }"/>
                <h:outputText value="#{msg_aplic.prt_modal_app_assinatura_digital}" rendered="#{LoginControle.tipoQR.nome eq 'ASSINATURA_DIGITAL' }"/>
                <h:outputText value="#{msg_aplic._app_cartao_vacina}" rendered="#{LoginControle.tipoQR.nome eq 'CART�O DE VACINA' }"/>
                <a4j:commandLink status="false" onclick="hideModalQRCode();" style="position: absolute; right: 15px; top: 35px; font-size: 16px !important;">
                    <h:outputText styleClass="linkPadrao texto-cor-branco fa-icon-remove-sign"
                                  style="font-size: 16px !important;"
                                  id="hidelinkmodalacessoappgestor"/>
                </a4j:commandLink>

            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup layout="block" style="font-size: 14px; width: calc(100% - 30px); padding: 15px;" rendered="#{LoginControle.tipoQR.nome eq 'ASSINATURA_DIGITAL' }">
            <h:panelGroup style="width: 100%; text-align: center;"
                          layout="block"
                          id="painelmodalacessoassinaturadigital">
                <div>
                    <h:outputText value="#{msg_aplic.prt_modal_assinatura_digital_texto}"/>
                    <a4j:commandLink
                            value="Assinatura Digital"
                            style="font-size: 14px; margin-left: 5px;"
                            action="#{LoginControle.notificarAssinaturaDigital}"
                            oncomplete="window.open('#{LoginControle.urlAssinaturaDigitalDireta}', '_blank');">
                    </a4j:commandLink>
                </div>

            </h:panelGroup>
            <h:panelGrid width="100%" columns="3" style="font-size: 14px; margin-bottom: 10px;" rendered="false">
                <h:outputText value="Itens obrigat�rios:"/>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_FOTO_ALUNO']}">
                            <a4j:support event="onclick" 
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Foto do aluno"/>
                </h:panelGroup>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_COMPROVANTE_ENDERECO']}">
                            <a4j:support event="onclick" 
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Comprovante de endere�o"/>
                </h:panelGroup>
                
                <h:outputText/>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_DOCUMENTOS']}">
                            <a4j:support event="onclick" 
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Documentos"/>
                </h:panelGroup>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_APTIDAO_FISICA']}">
                            <a4j:support event="onclick" 
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Atestado de aptid�o f�sica"/>
                </h:panelGroup>
                
            </h:panelGrid>
            
            
            
        </h:panelGroup>

        <h:panelGroup layout="block" style="font-size: 14px; width: calc(100% - 30px); padding: 15px;" rendered="#{LoginControle.tipoQR.nome eq 'CARTAO_VACINA' }">
            <h:panelGroup style="width: 100%; text-align: center;"
                          layout="block"
                          id="painelmodalacessocartaovacinacao">
                <div>
                    <h:outputText value="#{msg_aplic.prt_modal_cartao_vacina_texto}"/>
                    <a4j:commandLink
                            value="Cart�o de Vacina"
                            style="font-size: 14px; margin-left: 5px;"
                            action="#{LoginControle.notificarCartaoVacina}"
                            oncomplete="window.open('#{LoginControle.urlAssinaturaDigitalDireta}', '_blank');">
                    </a4j:commandLink>
                </div>

            </h:panelGroup>

            <h:panelGrid width="100%" columns="3" style="font-size: 14px; margin-bottom: 10px;" rendered="false">
                <h:outputText value="Itens obrigat�rios:"/>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_FOTO_ALUNO']}">
                            <a4j:support event="onclick"
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Foto do aluno"/>
                </h:panelGroup>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_COMPROVANTE_ENDERECO']}">
                            <a4j:support event="onclick"
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Comprovante de endere�o"/>
                </h:panelGroup>

                <h:outputText/>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_DOCUMENTOS']}">
                            <a4j:support event="onclick"
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Documentos"/>
                </h:panelGroup>
                <h:panelGroup>
                    <div class="chk-fa-container inline">
                        <h:selectBooleanCheckbox value="#{LoginControle.mapaConfigs['ASSINATURA_APTIDAO_FISICA']}">
                            <a4j:support event="onclick"
                                         action="#{LoginControle.gravarConfigAssinatura}"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </div>
                    <h:outputText value="Atestado de aptid�o f�sica"/>
                </h:panelGroup>

            </h:panelGrid>



        </h:panelGroup>
        
        <h:panelGroup layout="block" style="width: calc(100% - 30px); padding: 15px;" rendered="#{LoginControle.tipoQR.nome eq 'APPGESTOR' and LoginControle.exibirQrCodeUsuarioChave}">

            <h:panelGroup style="width: 100%; text-align: center; display: none;"
                          layout="block"
                          id="painelmodalacessoappgestor">
                <h3>Expira em <span style="font-family: 'Oxygen Mono',sans-serif;" id="sessaoQRCode">
                    </span> segundos</h3>

            </h:panelGroup>

            <h:panelGroup id="paineldadosqrcode">
                <div style="font-size: 14px; margin-bottom: 25px;">
                    <h:outputText value="#{msg_aplic.prt_modal_app_gestor_texto}"/>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlWiki}App_do_Gestor:Acesso"
                                  title="App do Gestor: Acesso"
                                  target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                </div>
                <input name="email" id="email_fake" style="visibility: hidden" />
                <input type="password" name="password" id="password_fake" style="visibility: hidden" />


                <div style="margin-bottom: 25px;">
                    <h:outputText styleClass="rotuloCampos" value="E-MAIL" style="margin-right: 20px;"/>
                    <h:outputText value="#{LoginControle.emailQRCode}" rendered="#{!LoginControle.novoEmailQR}"/>
                    <h:inputText id="emailsenhaQR" styleClass="inputTextClean" size="30" maxlength="64"
                                 style="margin-left:5px"  rendered="#{LoginControle.novoEmailQR}"
                                 value="#{LoginControle.emailQRCode}"/>

                </div>
                <div>
                    <h:outputText styleClass="rotuloCampos" value="SENHA" style="margin-right: 20px;"/>
                    <h:inputSecret id="senhaQR" styleClass="inputTextClean" size="14" maxlength="64"
                                   style="margin-left:5px"
                                   onkeypress="validarEnterSenha(event,'form:btnAutorizarAppg');"
                                   value="#{LoginControle.senhaQRCode}"/>
                </div>
                <script>
                    jQuery("#form").submit(function() {
                        return false;
                    });
                </script>
                <h:panelGroup layout="block" styleClass="container-botoes" style="margin-top: 25px;">
                    <a4j:commandButton id="btnAutorizarAppg" value="#{msg_aplic.prt_modal_app_gestor_gerar}"
                                     styleClass="botaoPrimario texto-size-16"
                                     oncomplete="#{LoginControle.msgAlert}"
                                     action="#{LoginControle.validarSenhaQRCode}"
                                     reRender="painelmodalacessoappgestor"/>

                    <a4j:commandLink id="btnenviaremailAppg" status="false"
                                     style="display:none;" action="#{LoginControle.enviarEmailUsuarioApp}"/>


                </h:panelGroup>

            </h:panelGroup>
        </h:panelGroup>

        </h:panelGroup>
</h:panelGroup>
