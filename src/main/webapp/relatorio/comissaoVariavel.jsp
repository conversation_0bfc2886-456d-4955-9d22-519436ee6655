<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <script type="text/javascript" language="javascript" src="../script/script.js"></script>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script src="${root}/script/packJQueryPlugins.min.js" type="text/javascript" ></script>

    <script>
        function montarToolDelay() {
            jQuery('.tooltipdelay').tooltipster({
                timer: 2000,
                interactive: true,
                contentAsHTML: true,
                theme: 'tooltipster-light'
            });
        }

    </script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css">
<link href="../css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<title>Comissão para consultor</title>
<link href="../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>


<f:view>
    <jsp:include page="../includes/include_carregando_ripple.jsp" flush="true"/>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_acesso_autorizacao_comissaoParaConsultor}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-gerar-o-relatorio-de-comissao-para-os-consultores/"/>
    <c:set var="iconeWikiEquivalentes" scope="request" value="true"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
    <rich:modalPanel id="panelColaborador" autosized="true" styleClass="novaModal" shadowOpacity="true" width="600" height="250">

        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Consultor"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink1"/>
                <rich:componentControl for="panelColaborador" attachTo="hidelink1" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formColaborador" ajaxSubmit="true" styleClass="font-size-Em-max">

            <h:panelGrid columns="1"  width="100%">

                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%" styleClass="font-size-Em-max">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultacolaborador" value="#{ComissaoControle.campoConsultarConsultor}">
                            <f:selectItems value="#{ComissaoControle.tipoConsultaComboConsultor}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaColaborador" size="10" value="#{ComissaoControle.valorConsultarConsultor}"/>
                    <a4j:commandLink id="btnConsultarColaborador" reRender="formColaborador"
                                     action="#{ComissaoControle.consultarConsultor}" styleClass="botaoPrimario texto-size-14-real"
                                     value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaColaborador" width="100%" styleClass="tabelaSimplesCustom" rendered="#{not empty ComissaoControle.listaConsultarConsultor}"
                                value="#{ComissaoControle.listaConsultarConsultor}" rows="5" var="colaborador">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza text-bold" value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ComissaoControle.selecionarConsultor}" focus="colaborador" reRender="form"
                                             styleClass="linkPadrao texto-font texto-size-14-real texto-cor-azul"
                                             id="selecionarColaborador"
                                             oncomplete="Richfaces.hideModalPanel('panelColaborador')" value="#{colaborador.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink action="#{ComissaoControle.selecionarConsultor}" focus="colaborador" reRender="form"
                                         styleClass="linkPadrao texto-font texto-size-14-real texto-cor-azul"
                                         oncomplete="Richfaces.hideModalPanel('panelColaborador')"
                                         title="#{msg.msg_selecionar_dados}" >
                            Selecionar <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formColaborador:resultadoConsultaColaborador" renderIfSinglePage="false" maxPages="10" styleClass="scrollPureCustom"
                                   id="scResultadoColaborador"/>
                <h:panelGrid id="mensagemConsultaColaborador" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ComissaoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ComissaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelAtendente" autosized="true" shadowOpacity="true" width="600" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta Atendente"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink2"/>
                <rich:componentControl for="panelAtendente" attachTo="hidelink2" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formAtendente" ajaxSubmit="true" styleClass="font-size-Em-max">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%"  styleClass="font-size-Em-max">
                    <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza" value="#{msg.msg_consultar_por}"/>
                    <h:panelGroup layout="block" styleClass="cb-container">
                        <h:selectOneMenu id="consultaatendente" value="#{ComissaoControle.campoConsultarAtendente}">
                            <f:selectItems value="#{ComissaoControle.tipoConsultaComboAtendente}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:inputText id="valorConsultaAtendente" size="10" value="#{ComissaoControle.valorConsultarAtendente}"/>
                    <a4j:commandLink id="btnConsultarAtendente" reRender="formColaborador:mensagemConsultaAtendente,
                                       formAtendente"
                                     action="#{ComissaoControle.consultarAtendente}" styleClass="botaoPrimario texto-size-14-real"
                                     value="#{msg_bt.btn_consultar}" title="#{msg.msg_consultar_dados}"/>
                </h:panelGrid>

                <rich:dataTable id="resultadoConsultaAtendente" width="100%" styleClass="tabelaSimplesCustom" rendered="#{not empty ComissaoControle.listaConsultarAtendente}"
                                value="#{ComissaoControle.listaConsultarAtendente}" rows="5" var="atendente">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-size-14-real texto-cor-cinza text-bold" value="#{msg_aplic.prt_Usuario_colaborador}"/>
                        </f:facet>
                        <h:panelGroup>
                            <a4j:commandLink action="#{ComissaoControle.selecionarAtendente}" focus="atendente" reRender="form"
                                             styleClass="linkPadrao texto-font texto-size-14-real texto-cor-azul"
                                             oncomplete="Richfaces.hideModalPanel('panelAtendente')" value="#{atendente.pessoa.nome}"/>
                        </h:panelGroup>
                    </rich:column>
                    <rich:column styleClass="col-text-align-right" headerClass="col-text-align-right">
                        <a4j:commandLink action="#{ComissaoControle.selecionarAtendente}" focus="atendente" reRender="form"
                                         styleClass="linkPadrao texto-font texto-size-14-real texto-cor-azul"
                                         oncomplete="Richfaces.hideModalPanel('panelAtendente')"
                                         title="#{msg.msg_selecionar_dados}" >
                            Selecionar <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formAtendente:resultadoConsultaAtendente" maxPages="10" renderIfSinglePage="false" styleClass="scrollPureCustom"
                                   id="scResultadoAtendente"/>
                <h:panelGrid id="mensagemConsultaAtendente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ComissaoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ComissaoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="../topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <a4j:keepAlive beanName="ExportadorListaControle"/>
            <h:panelGrid columns="1" width="100%">
                <hr style="border-color: #e6e6e6;"/>
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <c:if test="${LoginControle.usuarioLogado.administrador}">
                        <h:outputText value="Empresa"/>
                        <h:selectOneMenu id="empresa" onfocus="focusinput(this);" styleClass="form"
                                         value="#{ComissaoControle.empresa.codigo}">
                            <f:selectItems value="#{ComissaoControle.listaEmpresas}"/>

                            <a4j:support event="onchange" reRender="form" action="#{ComissaoControle.verificarEmpresaSelecionada}" ></a4j:support>
                        </h:selectOneMenu>
                    </c:if>


                    <h:outputText styleClass="tituloCampos" value="Tipo dos Dados"/>
                    <h:panelGroup>
                        <h:selectOneRadio id="tipodados" styleClass="tituloCampos" layout="lineDirection" value="#{ComissaoControle.tipoRelatorioEscolhido}">
                            <f:selectItems value="#{ComissaoControle.tipoRelatorioDF}"/>
                            <a4j:support event="onchange" reRender="form" action="#{ComissaoControle.verificarTipoSelecionado}"/>
                        </h:selectOneRadio>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Período" rendered="#{ComissaoControle.tipoRelatorioEscolhido != 2}"/>
                    <h:panelGroup rendered="#{ComissaoControle.tipoRelatorioEscolhido != 2}">
                        <h:panelGroup>
                            <rich:calendar id="dataInicio"
                                           value="#{ComissaoControle.dataInicioL}"
                                           inputSize="10"
                                           inputClass="form calendarioComissaoVariavel"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                        <h:outputText styleClass="tituloCampos" style="position:relative; top:0; left:10px;"
                                      value="#{msg_aplic.prt_CaixaPorOperador_ate}"/>
                        <rich:spacer width="12px"/>
                        <h:panelGroup>
                            <rich:calendar id="dataTermino"
                                           value="#{ComissaoControle.dataFinalL}"
                                           inputSize="10"
                                           inputClass="form calendarioComissaoVariavel"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="true"/>
                            <h:message for="dataTermino" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Competência" rendered="#{ComissaoControle.tipoRelatorioEscolhido == 2}"/>
                    <h:panelGroup rendered="#{ComissaoControle.tipoRelatorioEscolhido == 2}">
                        <h:panelGroup>
                            <rich:calendar id="dataCompetencia"
                                           value="#{ComissaoControle.dataCompetencia}"
                                           inputSize="10"
                                           inputClass="form calendarComissaoVarialMes"
                                           styleClass="mes"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           datePattern="MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataCompetencia" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Somente recebimento a partir"
                                  rendered="#{ComissaoControle.tipoRelatorioEscolhido != 2 && ComissaoControle.tipoRelatorioEscolhido != 4}"/>
                    <h:panelGroup rendered="#{ComissaoControle.tipoRelatorioEscolhido != 2 && ComissaoControle.tipoRelatorioEscolhido != 4}">
                        <h:panelGroup>
                            <rich:calendar id="dataInicioRec"
                                           value="#{ComissaoControle.dataInicioR}"
                                           inputSize="10"
                                           inputClass="form calendarioComissaoVariavel"
                                           oninputblur="blurinput(this);"
                                           oninputfocus="focusinput(this);"
                                           oninputchange="return validar_Data(this.id);"
                                           datePattern="dd/MM/yyyy"
                                           enableManualInput="true"
                                           zindex="2"
                                           showWeeksBar="false"/>
                            <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                        </h:panelGroup>
                    </h:panelGroup>


                <h:outputText styleClass="tituloCampos" value="Somente contratos lançados a partir"
                              rendered="#{ComissaoControle.tipoRelatorioEscolhido == 1 or ComissaoControle.tipoRelatorioEscolhido == 2 or ComissaoControle.tipoRelatorioEscolhido == 3}"/>
                <h:panelGroup rendered="#{ComissaoControle.tipoRelatorioEscolhido == 1 or ComissaoControle.tipoRelatorioEscolhido == 2 or ComissaoControle.tipoRelatorioEscolhido == 3}">
                    <h:panelGroup>
                        <rich:calendar id="dataInicioContratosLancadosAPartir"
                                       value="#{ComissaoControle.dataContratosLancadosAPartir}"
                                       inputSize="10"
                                       inputClass="form calendarioComissaoVariavel"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false"/>
                        <h:message for="dataInicio" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                </h:panelGroup>

                    <%--responsavel pelo contrato --%>

                    <h:outputText styleClass="tituloCampos" value="Operador Responsável"
                                  rendered="#{ComissaoControle.tipoRelatorioEscolhido != 4 && ComissaoControle.tipoRelatorioEscolhido != 2}"/>
                    <h:outputText styleClass="tituloCampos" value="Responsável Laçamento"
                                  rendered="#{ComissaoControle.tipoRelatorioEscolhido == 4}"/>
                    <h:panelGroup rendered="#{ComissaoControle.tipoRelatorioEscolhido != 2}">
                        <h:panelGroup rendered="#{ComissaoControle.tipoRelatorioEscolhido != 4}">
                            <h:selectOneMenu id="tipoResponsavel" styleClass="tituloCampos" value="#{ComissaoControle.tipoResponsavel}" >
                                <f:selectItems  value="#{ComissaoControle.listaSelectItemTipoResponsavel}"/>
                            </h:selectOneMenu>
                            <rich:spacer width="5px"/>
                        </h:panelGroup>

                        <h:inputText id="nomeAtendente" size="40" maxlength="40" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{ComissaoControle.atendente.pessoa.nome}"/>
                        <a4j:commandButton id="consultarAtendente"
                                           oncomplete="Richfaces.showModalPanel('panelAtendente'), setFocus(formColaborador,'formColaborador:valorConsultarAtendente')"
                                           alt="Consultar Atendente" image="../imagens/informacao.gif"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="LimparAtendente" image="../imagens/limpar.gif" reRender="form:nomeAtendente"
                                           action="#{ComissaoControle.limparAtendente}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Nome do consultor"/>
                    <h:panelGroup>
                        <h:inputText id="nomeConsultor" size="40" maxlength="40" readonly="true" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="form" value="#{ComissaoControle.consultor.pessoa.nome}"/>
                        <a4j:commandButton id="consultarConsultor"
                                           oncomplete="Richfaces.showModalPanel('panelColaborador'), setFocus(formColaborador,'formColaborador:valorConsultarConsultor')"
                                           alt="Consultar Consultor" image="../imagens/informacao.gif"/>
                        <rich:spacer width="5px"/>
                        <a4j:commandButton id="LimparConsultor" image="../imagens/limpar.gif" reRender="form:nomeConsultor"
                                           action="#{ComissaoControle.limparConsultor}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Duração"/>
                    <rich:dataGrid id="dtgDuracao" value="#{ComissaoControle.periodicidadesPossiveis}" style="background: none"
                                   width="100%" columns="5" columnClasses="semBorda" styleClass="semBorda"
                                   cellpadding="0" cellspacing="0" var="contratoDuracao">
                        <h:panelGroup>
                            <h:selectBooleanCheckbox id="slctModalide" value="#{contratoDuracao.selecionado}"/>
                            <rich:spacer width="5"/>
                            <h:outputText styleClass="titulo3" value="#{contratoDuracao.numeroMeses}"/>
                        </h:panelGroup>
                    </rich:dataGrid>

                    <h:outputText styleClass="tituloCampos" value="Tipo do pagamento"/>
                    <h:panelGroup>
                        <h:selectManyCheckbox id="pagamento" styleClass="tituloCampos" value="#{ComissaoControle.tipoContrato}">
                            <f:selectItem itemValue="MA" itemLabel="Matrícula"/>
                            <f:selectItem itemValue="RE" itemLabel="Rematrícula"/>
                            <f:selectItem itemValue="RN" itemLabel="Renovação"/>
                        </h:selectManyCheckbox>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Impressão por:"/>
                    <h:panelGroup id="pnlOpcaoImpressao">
                        <h:selectOneRadio id="impressao" styleClass="tituloCampos" value="#{ComissaoControle.opcaoImpressao}">
                            <f:selectItem itemValue="CO" itemLabel="Consultor Contrato"/>
                            <f:selectItem itemValue="CA" itemLabel="Consultor Atual"/>
                            <c:if test="${(ComissaoControle.tipoRelatorioEscolhido eq 1) or (ComissaoControle.tipoRelatorioEscolhido eq 3)}">
                                <f:selectItem itemValue="SO" itemLabel="Somente Operador"/>
                            </c:if>
                            <f:selectItem itemValue="RL" itemLabel="Somente Responsável Lançamento"/>
                            <a4j:support event="onchange" reRender="pnlOpcaoImpressao"/>
                        </h:selectOneRadio>
                        <h:outputText rendered="#{ComissaoControle.opcaoImpressao eq 'CO'}" value="Irá trazer a comissão agrupada pelos consultores do contrato (no caso de produtos, consultor do cliente na época da venda)"/>
                        <h:outputText rendered="#{ComissaoControle.opcaoImpressao eq 'CA'}" value="Trará o relatório de comissão agrupado pelo consultor atual do cliente"/>
                        <h:outputText rendered="#{ComissaoControle.opcaoImpressao eq 'SO'}" value="Agrupará o relatório de comissão por quem lançou o recibo no sistema"/>
                        <h:outputText rendered="#{ComissaoControle.opcaoImpressao eq 'RL'}" value="Agrupará o relatório de comissão por quem lançou a venda no sistema"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Modo de visualização"/>
                    <h:panelGroup>
                        <h:selectOneRadio id="visualizacao" styleClass="tituloCampos" value="#{ComissaoControle.visualizacao}">
                            <f:selectItem itemValue="AP" itemLabel="Detalhado"/>
                            <f:selectItem itemValue="A" itemLabel="Totalizado por Aluno"/>
                            <f:selectItem itemValue="S" itemLabel="Totalizado por Duração"/>
                        </h:selectOneRadio>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Valor de Comissões"/>
                    <h:panelGroup>
                        <h:selectOneRadio id="comissao" styleClass="tituloCampos" value="#{ComissaoControle.tipoValorComissoes}">
                            <f:selectItem itemValue="PORC" itemLabel="Por porcentagem"/>
                            <f:selectItem itemValue="FIXO" itemLabel="Por valor fixo"/>
                        </h:selectOneRadio>
                    </h:panelGroup>

                    <c:if test="${(ComissaoControle.empresa.pagarComissaoSeAtingirMetaFinanceira)}">
                        <h:outputText style="font-weight: bold" styleClass="tituloCampos" value="Observação:"/>
                    </c:if>


                    <c:if test="${(ComissaoControle.empresa.pagarComissaoSeAtingirMetaFinanceira)}">
                        <h:outputText styleClass="tituloCampos" value="#{ComissaoControle.msgInformativa}"/>
                    </c:if>

                    <c:if test="${(!ComissaoControle.empresa.pagarComissaoProdutos)}">
                        <h:panelGroup layout="block" style="margin-top: 12px">
                            <h:outputText style="font-weight: bold;" styleClass="tituloCampos" value="Observação:"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="margin-top: 12px">
                            <h:outputText style="font-weight: bold; color: #2BAF50;" styleClass="tituloCampos" value="Caso queira emitir comissão para produtos, por favor vá nas Configurações de Empresa."/>
                        </h:panelGroup>
                    </c:if>

                    <h:outputText styleClass="tituloCampos tooltipdelay"
                                  value="Considerar data de compensação original" rendered="#{ComissaoControle.tipoRelatorioEscolhido == 1}"/>
                    <h:panelGroup  rendered="#{ComissaoControle.tipoRelatorioEscolhido == 1}">
                        <h:panelGrid styleClass="tooltipdelay">
                            <h:selectBooleanCheckbox id="recebiveisCompensacaoOriginal"
                                                     value="#{ComissaoControle.considerarCompensacaoOriginal}"/>
                        </h:panelGrid>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos tooltipdelay"
                                  title="#{ComissaoControle.titleDesconsiderar}"
                                  value="Desconsiderar cheques que estão na conta do tipo pendência/devolução/custódia no financeiro" rendered="#{(ComissaoControle.tipoRelatorioEscolhido == 1) || (ComissaoControle.tipoRelatorioEscolhido == 3)}"/>
                    <h:panelGroup  rendered="#{(ComissaoControle.tipoRelatorioEscolhido == 1) || (ComissaoControle.tipoRelatorioEscolhido == 3)}">


                        <h:panelGrid title="#{ComissaoControle.titleDesconsiderar}" styleClass="tooltipdelay">
                            <h:selectBooleanCheckbox id="retirarRecebiveisComPendecia"
                                                     disabled="true"
                                                     value="#{ComissaoControle.retirarRecebiveisComPendecia}"/>
                        </h:panelGrid>

                        <script>
                            montarToolDelay();
                        </script>

                    </h:panelGroup>

                    <c:if test="${(ComissaoControle.empresa.pagarComissaoProdutos)}">
                        <h:panelGroup layout="block" style="margin-top: 12px">
                            <h:outputText style="font-weight: bold; margin-top: 8px" styleClass="tituloCampos" value="Observação:"/>
                        </h:panelGroup>
                        <h:panelGroup layout="block" style="margin-top: 12px">
                            <h:outputText style="font-weight: bold;" styleClass="tituloCampos" value="Este relatório também apresentará Produtos com Comissão."/>
                        </h:panelGroup>
                    </c:if>

                </h:panelGrid>


                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGroup layout="block" styleClass="container-botoes">
                        <a4j:commandLink id="imprimirPDF"
                                         action="#{ComissaoControle.imprimirPDF}"
                                         oncomplete="#{ComissaoControle.nomeRefRelatorioGeradoAgora}"
                                         accesskey="2" styleClass="botoes nvoBt"
                                         value="Gerar Relatório(PDF) "
                                         style="padding: 11px 15px"
                                         reRender="form">
                            <i  style="font-size: 20px;vertical-align: sub;margin-left: 5px;" class="fa-icon-print"></i>
                        </a4j:commandLink>
                        <a4j:commandLink id="btnExcel"
                                         styleClass="botoes nvoBt"
                                         actionListener="#{ComissaoControle.exportar}"
                                         oncomplete="#{ComissaoControle.msgAlert} "
                                         style="padding: 11px 15px"
                                         accesskey="3"
                                         reRender="form">
                            <f:attribute name="tipo" value="xls"/>
                            <f:attribute name="atributos"
                                         value="matriculaCliente=Matrícula,nomePessoa=Aluno,codigoCliente=Cod.Cliente,codigoPessoa=Cod Pessoa,codigoConsultorResponsavel=Cod. Consultor,consultorResponsavel=Consultor Responsável,configuracao=Configuração,codigoContrato=Contrato,situacaoContrato=Situação Contrato,codigoProduto=Cod.Produto,codigoResponsavelLancamento=Cod.Responsavel Lancamento,responsavelLancamento=Responsável Lançamento,tipoContrato=Tipo Contrato,duracaoContrato=Duração,contratoAgendadoEspontaneo=Agendado/Espontaneo,nomePlano=Plano,nomeProduto=Produto,valorContrato=Valor Contrato,valorProduto=Valor Produto,codigoRecibo=Recibo,codigoResponsavelRecebimento=Cod.Responsável Recebimento,responsavelRecebimento=responsavelRecebimento,formaPagamento=FormaPagamento,qtdParcelas=qteParcelas,dataPagamento=DataPagamento,dataCompensacao=DataCompensacao,valor=ValorPagamento,valorDaComissao=Valor Comissão,dataLancamentoContrato=Lanç. Contrato"/>
                            <f:attribute name="prefixo" value="ComissaoConsultor"/>
                            <h:outputText style="fontfont-size: 20px;vertical-align: sub;color: white;" title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel"/>
                        </a4j:commandLink>
                    </h:panelGroup>

                    <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText value=" "/>
                            </h:panelGrid>
                            <h:panelGrid columns="1" width="100%">
                                <h:outputText styleClass="mensagem" value="#{ComissaoControle.mensagem}"/>
                                <h:outputText styleClass="mensagemDetalhada" value="#{ComissaoControle.mensagemDetalhada}"/>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>

                </h:panelGrid>
            </h:panelGrid>

            <rich:jQuery id="mskData" selector=".calendarioComissaoVariavel" timing="onload" query="mask('99/99/9999')" />
            <rich:jQuery selector=".calendarComissaoVarialMes" timing="onload" query="mask('99/9999')" />
        </h:form>
    </h:panelGrid>


</f:view>
