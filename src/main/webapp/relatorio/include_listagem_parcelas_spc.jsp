<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@include file="/includes/verificaModulo.jsp" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<style>

    .situacaoBallon {
        border-radius: 100px;
        color: white;
        padding: 4px 18px 4px 16px;
        text-align: center;
        display: inline-block;
        width: 80px;
        height: 15px;
        line-height: normal;
    }

    .rich-table-subheader {
        line-height: normal;
    }
</style>
<h:panelGrid id="listagemParcelas" rendered="#{not empty ParcelaEmAbertoSPCControleRel.listaParcelas}" columns="1"
             width="100%" cellpadding="0" cellspacing="0">
    <h:panelGrid columns="1" width="100%">
        <h:panelGrid width="100%" style="text-align: right;">
            <h:panelGroup layout="block">
                <c:if test="${modulo eq 'zillyonWeb'}">
                    <a4j:commandLink id="btnExcel"
                                     styleClass="exportadores linkPadrao"
                                     actionListener="#{ParcelaEmAbertoSPCControleRel.exportar}"
                                     oncomplete="#{ParcelaEmAbertoSPCControleRel.abrirRelatorio}"
                                     accesskey="3">
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="#{ParcelaEmAbertoSPCControleRel.atributos}"/>
                        <f:attribute name="prefixo" value="ParcelaEmAberto"/>
                        <h:outputText title="Exportar para o formato Excel" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                    <a4j:commandLink styleClass="exportadores margin-h-10 linkPadrao"
                                     action="#{ParcelaEmAbertoSPCControleRel.imprimirHorizontal}"
                                     oncomplete="#{ParcelaEmAbertoSPCControleRel.abrirRelatorio}"
                                     reRender="form">
                        <h:outputText title="Exportar para o formato PDF" styleClass="btn-print-2 pdf"/>
                    </a4j:commandLink>
                </c:if>
            </h:panelGroup>
        </h:panelGrid>

        <rich:dataTable width="100%" styleClass="tabelaSimplesCustom" id="tabelaRes" style="line-height: 45px; white-space: nowrap;"
                        value="#{ParcelaEmAbertoSPCControleRel.listaParcelas}" rows="50" var="resumoPessoa"
                        rowKeyVar="status">
            <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:selectBooleanCheckbox id="selecionarTodasParcelas"
                                             value="#{ParcelaEmAbertoSPCControleRel.marcarTodasParcelas}"
                                             title="Selecionar todas as parcelas">
                        <a4j:support event="onclick" action="#{ParcelaEmAbertoSPCControleRel.marcarTodasPacelasFinanceiro}" reRender="tabelaRes"/>
                    </h:selectBooleanCheckbox>
                </f:facet>
                <h:selectBooleanCheckbox id="selecionarUmaParcela" styleClass="tooltipster"
                                         rendered="#{not resumoPessoa.permiteSelecionar and empty resumoPessoa.erro}"
                                         disabled="#{not resumoPessoa.permiteSelecionar}"
                                         title="Esta parcela está cancelada ou não está vencida, por isso não pode ser negativada"
                                         value="#{resumoPessoa.selecionada}"/>

                <h:selectBooleanCheckbox id="selecionarUmaParcelaComErros" styleClass="tooltipster"
                                         rendered="#{not empty resumoPessoa.erro}"
                                         disabled="#{not empty resumoPessoa.erro}"
                                         title="Existem erros relacionados a essa parcela, não será possível selecionar ela para negativar!"
                                         value="#{resumoPessoa.selecionada}"/>

                <h:selectBooleanCheckbox rendered="#{resumoPessoa.permiteSelecionar and empty resumoPessoa.erro}"
                                         value="#{resumoPessoa.selecionada}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.matricula}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Matrícula"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              style="text-align: left; width: 75px;" value="#{resumoPessoa.matricula}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.nome}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="Cliente: #{resumoPessoa.nome}"
                              value="#{resumoPessoa.nome}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.pessoaResponsavel.nome}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left" rendered="#{ParcelaEmAbertoSPCControleRel.renderizarColunasPessoaResponsavel}">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Nome Responsável"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="Responsável Financeiro: #{resumoPessoa.pessoaResponsavel.nome}"
                              value="#{resumoPessoa.pessoaResponsavel.nome}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.primeiroTelefone}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Celular"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="#{resumoPessoa.telefones}"
                              value="#{resumoPessoa.primeiroTelefone}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.codigoVendaApresentar}" styleClass="col-text-align-center"
                         headerClass="col-text-align-center">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Contrato"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.codigoVendaApresentar}">
                </h:outputText>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.descricaoParcela}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Parcela"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="#{resumoPessoa.descricaoParcela}"
                              value="#{resumoPessoa.descricaoParcelaAbreviado}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.situacao_Apresentar}" styleClass="col-text-align-left tooltipster"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="SITUAÇÃO"/>
                </f:facet>
                <h:outputText
                        styleClass="tooltipster texto-size-14-real texto-cor-cinza texto-font situacaoBallon"
                        style="#{resumoPessoa.situacao eq 'EA' ? 'background: #DB2C3D;' : resumoPessoa.situacao eq 'PG' ? 'background: #48D567;' : 'background: #80858C;'}"
                        title="#{resumoPessoa.situacao eq 'EA' ? 'Aguardando Pagamento' : resumoPessoa.situacao eq 'CA' ? resumoPessoa.dataCancelamento_Hint : resumoPessoa.dataPagamento_Hint}"
                        value="#{resumoPessoa.situacao_Apresentar}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.valorMultasJuros}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Valor"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              title="Valor: #{ParcelaEmAbertoSPCControleRel.empresaLogado.moeda} #{resumoPessoa.valor_Apresentar} + (Multas e Juros)"
                              value="#{ParcelaEmAbertoSPCControleRel.empresaLogado.moeda} #{resumoPessoa.valorMultasJuros_Apresentar}"/>
            </rich:column>
            <c:if test="${ParcelaEmAbertoSPCControleRel.gerarMultasJuros}">
                <rich:column sortBy="#{resumoPessoa.multas}" styleClass="col-text-align-left"
                             headerClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Multa"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{ParcelaEmAbertoSPCControleRel.empresaLogado.moeda} #{resumoPessoa.multas}"/>
                </rich:column>
                <rich:column sortBy="#{resumoPessoa.juros}" styleClass="col-text-align-left"
                             headerClass="col-text-align-left">
                    <f:facet name="header">
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Juros"/>
                    </f:facet>
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="#{ParcelaEmAbertoSPCControleRel.empresaLogado.moeda} #{resumoPessoa.juros}"/>
                </rich:column>
            </c:if>
            <rich:column sortBy="#{resumoPessoa.dataFatura}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Faturamento"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.dataFaturamento_Apresentar}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.dateVencimento}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                                  value="Vencimento"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font"
                              value="#{resumoPessoa.dateVencimento_Apresentar}"/>
            </rich:column>
            <rich:column sortBy="#{resumoPessoa.cpfApresentar}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="CPF"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.cpfApresentar}"/>
            </rich:column>

            <rich:column sortBy="#{resumoPessoa.pessoaResponsavel.cfp}" styleClass="col-text-align-left"
                         headerClass="col-text-align-left" rendered="#{ParcelaEmAbertoSPCControleRel.renderizarColunasPessoaResponsavel}">
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="CPF do Responsável"/>
                </f:facet>
                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="#{resumoPessoa.pessoaResponsavel.cfp}"/>
            </rich:column>

            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" rendered="#{ParcelaEmAbertoSPCControleRel.renderizarColunaErros}"
                         style="max-width: 100px; overflow: hidden; white-space:nowrap; text-overflow: ellipsis;" >
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Erro"/>
                </f:facet>

                <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              value="#{resumoPessoa.erro}"
                              title="#{resumoPessoa.erro}"/>

            </rich:column>

            <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left" style="max-width: 100px; overflow: hidden; white-space:nowrap; text-overflow: ellipsis;" >
                <f:facet name="header">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-font" value="Situação SPC"/>
                </f:facet>
                <h:outputText rendered="#{resumoPessoa.incluidaSpc}"
                              styleClass="tooltipster texto-size-14-real texto-cor-branco texto-font situacaoBallon"
                              style="background: #333333"
                              title="#{resumoPessoa.situacaoSpc}"
                              value="SPC"/>

                <h:outputText rendered="#{not resumoPessoa.incluidaSpc and resumoPessoa.situacaoSpc != ''}"
                              styleClass="texto-size-14-real texto-cor-cinza texto-font tooltipster"
                              value="#{resumoPessoa.situacaoSpc}"
                              title="#{resumoPessoa.situacaoSpc}"/>

                <h:outputText rendered="#{not resumoPessoa.incluidaSpc and not resumoPessoa.erroInclusaoSpc}"
                              styleClass="tooltipster texto-size-14-real texto-cor-branco texto-font situacaoBallon"
                              style=""
                              value=""/>
            </rich:column>

            <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                <a4j:commandLink styleClass="linkPadrao texto-size-16-real texto-cor-azul"
                                 action="#{ParcelaEmAbertoSPCControleRel.irParaTelaCliente}"
                                 oncomplete="#{ParcelaEmAbertoSPCControleRel.msgAlert};#{ParcelaEmAbertoSPCControleRel.mensagemNotificar}">
                    <f:param name="state" value="AC"/>
                    <i class="fa-icon-search"></i>
                </a4j:commandLink>
            </rich:column>
        </rich:dataTable>
        <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" align="center"
                           for="v20_form:tabelaRes" maxPages="10" id="sctabelaRes"/>
    </h:panelGrid>
</h:panelGrid>

