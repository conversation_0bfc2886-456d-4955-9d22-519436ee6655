<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 30/07/2015
  Time: 16:31
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head>
  <%@include file="/includes/include_import_minifiles.jsp" %>
  <script type="text/javascript" language="javascript" src="../script/script.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>

  <title>
    <h:outputText value="#{msg_aplic.prt_CompetenciaSintetico_tituloFormPessoas}"/>
  </title>
  <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
    <f:facet name="header">

      <jsp:include page="../topoReduzido.jsp"/>

    </f:facet>
    <html>
    <body onload="fireElement('form:botaoAtualizarPagina')"/>
    <h:form id="form" >
      <a4j:commandButton id="botaoAtualizarPagina" reRender="form" style="display:none"/>
      <h:panelGrid columns="1" width="100%" >
        <h:panelGrid columns="1" style="height:25px; background-image:url('../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
          <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_CompetenciaSintetico_tituloFormModalidade}  - Total: #{fn:length(CompetenciaSinteticoControleRel.listaResumoModalidade)}"/>
        </h:panelGrid>
        <h:panelGrid width="100%" style="margin-bottom: 8px;text-align: right;">
          <h:panelGroup layout="block">
            <%--BOTÃO EXCEL--%>
            <a4j:commandButton id="exportarExcel"
                               image="../imagens/btn_excel.png"
                               style="margin-left: 8px;"
                               actionListener="#{ExportadorListaControle.exportar}"
                               rendered="#{not empty CompetenciaSinteticoControleRel.listaResumoModalidade}"
                               value="Excel"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes">
              <f:attribute name="lista" value="#{CompetenciaSinteticoControleRel.listaResumoModalidade}"/>
              <f:attribute name="tipo" value="xls"/>
              <f:attribute name="atributos" value="contrato=Codigo contrato,nomeModalidade_Apresentar=Nome,nrVezesSemana=N° vezes semana,valorModalidade=Valor Modalidade"/>
              <f:attribute name="prefixo" value="ModalidadePorContrato"/>
            </a4j:commandButton>
            <%--BOTÃO PDF--%>
            <a4j:commandButton id="exportarPdf"
                               style="margin-left: 8px;"
                               image="../imagens/imprimir.png"
                               actionListener="#{ExportadorListaControle.exportar}"
                               rendered="#{not empty CompetenciaSinteticoControleRel.listaResumoModalidade}"
                               value="PDF"
                               oncomplete="abrirPopup('../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                               accesskey="2" styleClass="botoes">
              <f:attribute name="lista" value="#{CompetenciaSinteticoControleRel.listaResumoModalidade}"/>
              <f:attribute name="tipo" value="pdf"/>
              <f:attribute name="atributos" value="contrato=Codigo contrato,nomeModalidade_Apresentar=Nome,nrVezesSemana=N° vezes semana,valorModalidade=Valor Modalidade"/>
              <f:attribute name="prefixo" value="CompetenciaMovimentacaoModalidade"/>
            </a4j:commandButton>
          </h:panelGroup>
        </h:panelGrid>
        <rich:dataTable id="listaModalidade" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                        value="#{CompetenciaSinteticoControleRel.listaResumoModalidade}" rows="100" var="resumoModalidade"  rowKeyVar="status">
          <%@include file="/pages/ce/includes/include_contador_richtable.jsp" %>
          <rich:column>
            <f:facet name="header">
              <h:outputText value="Codigo contrato" />
            </f:facet>
            <h:outputText value="#{resumoModalidade.contrato}"/>

          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText value="Codigo modalidade" />
            </f:facet>
            <h:outputText value="#{resumoModalidade.modalidade.codigo}"/>

          </rich:column>
          <rich:column >
            <f:facet name="header">
              <h:outputText value="Nome" />
            </f:facet>
            <h:outputText value="#{resumoModalidade.nomeModalidade_Apresentar}" />
           </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText value="N° vezes semana" />
            </f:facet>
            <h:outputText value="#{resumoModalidade.nrVezesSemana}" />
          </rich:column>
          <rich:column>
            <f:facet name="header">
              <h:outputText value="Valor Modalidade" />
            </f:facet>
            <h:outputText value="#{resumoModalidade.valorModalidade}" />
          </rich:column>
         </rich:dataTable>
        <rich:datascroller
                align="center"
                for="form:listaModalidade"
                id="scResultadoConsultaListaPessoa"
                rendered="#{!empty CompetenciaSinteticoControleRel.listaResumoModalidade}" />
      </h:panelGrid>
    </h:form>
    </body>
    </html>
  </h:panelGrid>
</f:view>

