<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ConvenioDesconto_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ConvenioDesconto_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._de_Contrato:Convênio_de_Desconto"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">

        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ConvenioDescontoControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"  size="10" maxlength="10" readonly="true" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="camposSomenteLeitura" value="#{ConvenioDescontoControle.convenioDescontoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText rendered="#{ConvenioDescontoControle.usuarioLogado.administrador}" styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_empresa}"/>
                    <h:panelGroup rendered="#{ConvenioDescontoControle.usuarioLogado.administrador}">
                        <h:selectOneMenu id="empresa"  onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoVO.empresa.codigo}">
                            <f:selectItems value="#{ConvenioDescontoControle.selectItemEmpresas}"/>
                        </h:selectOneMenu>
                        <h:message for="empresa" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_responsavelAutorizacao}" />
                    <h:panelGroup>
                        <h:inputText  id="responsavelAutorizacao"  readonly="true" size="5" maxlength="7" styleClass="camposSomenteLeitura" value="#{ConvenioDescontoControle.convenioDescontoVO.responsavelAutorizacao.codigo}" />
                        <h:outputText  value="#{ConvenioDescontoControle.convenioDescontoVO.responsavelAutorizacao.nome}" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_descricao}" />
                    <h:panelGroup>
                        <h:inputText  id="descricao"  size="50" maxlength="50" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoVO.descricao}" />
                        <h:message for="descricao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_dataAssinatura}" />
                    <h:panelGroup>
                        <rich:calendar id="dataAssinatura" value="#{ConvenioDescontoControle.convenioDescontoVO.dataAssinatura}" 
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />

                        <h:message for="dataAssinatura" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_dataInicioVigencia}" />
                    <h:panelGroup>
                        <rich:calendar id="dataInicioVigencia"
                                       value="#{ConvenioDescontoControle.convenioDescontoVO.dataInicioVigencia}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />

                        <h:message for="dataInicioVigencia" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_dataFinalVigencia}" />
                    <h:panelGroup>
                        <rich:calendar id="dataFinalVigencia"
                                       value="#{ConvenioDescontoControle.convenioDescontoVO.dataFinalVigencia}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />

                        <h:message for="dataFinalVigencia" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_dataAutorizacao}" />
                    <h:panelGroup>
                        <rich:calendar id="dataAutorizacao"
                                       value="#{ConvenioDescontoControle.convenioDescontoVO.dataAutorizacao}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />

                        <h:message for="dataAutorizacao" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_isentarMatricula}" />
                    <h:selectBooleanCheckbox id="isentarMatricula" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoVO.isentarMatricula}"/>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDesconto_isentarRematricula}" />
                    <h:selectBooleanCheckbox id="isentarRematricula" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoVO.isentarRematricula}"/>

                </h:panelGrid>
                <h:panelGrid id="panelConvenioDescontoConfiguracao" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_tituloForm}"/>
                    </f:facet>
                    <h:panelGrid columns="2" width="100%" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita">
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_duracao}" />
                        <h:panelGroup>
                            <h:inputText id="ConvenioDescontoConfiguracao_duracao" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoConfiguracaoVO.duracao}" />
                        </h:panelGroup>
                        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_tipoDesconto}" />
                        <h:selectOneMenu  id="ConvenioDescontoConfiguracao_tipodesconto" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"  value="#{ConvenioDescontoControle.convenioDescontoConfiguracaoVO.tipoDesconto}">
                            <a4j:support event="onchange" reRender="form" focus="ConvenioDescontoConfiguracao_tipodesconto"/>
                            <f:selectItems value="#{ConvenioDescontoControle.listaSelectItemTipoDescontoConvenioDescontoConfiguracao}"/>
                        </h:selectOneMenu>

                        <h:outputText  rendered="#{ConvenioDescontoControle.desenhaValorEspecifico}" value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_valorDesconto}" />
                        <h:inputText  rendered="#{ConvenioDescontoControle.desenhaValorEspecifico}"  id="valorDesconto" size="20" onkeypress="return Tecla(event);" maxlength="20" onblur="blurinput(this);"   onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoConfiguracaoVO.valorDesconto}" >
                            <f:converter converterId="FormatadorNumerico" />
                        </h:inputText>

                        <h:outputText  rendered="#{ConvenioDescontoControle.desenhaPercentualDesconto}" value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_porcentagemDesconto}" />
                        <h:inputText rendered="#{ConvenioDescontoControle.desenhaPercentualDesconto}" id="porcentagemDesconto" onkeypress="return Tecla(event);" size="20" maxlength="20" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form" value="#{ConvenioDescontoControle.convenioDescontoConfiguracaoVO.porcentagemDesconto}" >
                            <f:converter converterId="FormatadorNumerico" />
                        </h:inputText>

                    </h:panelGrid>
                    <a4j:commandButton action="#{ConvenioDescontoControle.adicionarConvenioDescontoConfiguracao}" reRender="panelConvenioDescontoConfiguracao, panelMensagemErro" focus="form:ConvenioDescontoConfiguracao_duracao" value="#{msg_bt.btn_adicionar}" image= "./imagens/botaoAdicionar.png" accesskey="5" styleClass="botoes"/>

                    <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                        <h:dataTable id="convenioDescontoConfiguracaoVO" width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                                     rowClasses="linhaImparSubordinado, linhaParSubordinado" columnClasses="colunaAlinhamento"
                                     value="#{ConvenioDescontoControle.convenioDescontoVO.convenioDescontoConfiguracaoVOs}" var="convenioDescontoConfiguracao">
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_duracao}" />
                                </f:facet>
                                <h:outputText  value="#{convenioDescontoConfiguracao.duracao}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_valorDesconto}" />
                                </f:facet>
                                <h:outputText  value="#{convenioDescontoConfiguracao.valorDesconto}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_porcentagemDesconto}" />
                                </f:facet>
                                <h:outputText  value="#{convenioDescontoConfiguracao.porcentagemDesconto}">
                                    <f:converter converterId="FormatadorNumerico" />
                                </h:outputText>
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_aplic.prt_ConvenioDescontoConfiguracao_tipoDesconto}" />
                                </f:facet>
                                <h:outputText  value="#{convenioDescontoConfiguracao.tipoDesconto_Apresentar}" />
                            </h:column>
                            <h:column>
                                <f:facet name="header">
                                    <h:outputText  value="#{msg_bt.btn_opcoes}" />
                                </f:facet>
                                <h:panelGroup>
                                    <h:commandButton id="editarItemVenda" action="#{ConvenioDescontoControle.editarConvenioDescontoConfiguracao}" value="#{msg_bt.btn_editar}" image="./imagens/botaoEditar.png" accesskey="6" styleClass="botoes"/>

                                    <h:outputText value="    "/>

                                    <h:commandButton id="removerItemVenda" immediate="true" action="#{ConvenioDescontoControle.removerConvenioDescontoConfiguracao}" value="#{msg_bt.btn_excluir}" image="./imagens/botaoRemover.png" accesskey="7" styleClass="botoes"/>
                                </h:panelGroup>
                            </h:column>
                        </h:dataTable>
                    </h:panelGrid>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelMensagemErro" columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{ConvenioDescontoControle.sucesso}"  image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ConvenioDescontoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{ConvenioDescontoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ConvenioDescontoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandLink id="novo" immediate="true" action="#{ConvenioDescontoControle.novo}" value="#{msg_bt.btn_novo}"  title="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>

                            <h:outputText value="    "/>

                            <a4j:commandLink id="salvar" action="#{ConvenioDescontoControle.gravar}" value="#{msg_bt.btn_gravar}" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>

                            <h:outputText value="    "/>

                        <h:panelGroup id="grupoBtnExcluir">
                            <a4j:commandLink id="excluir"  reRender="mdlMensagemGenerica"
                                             oncomplete="#{ConvenioDescontoControle.msgAlert}" action="#{ConvenioDescontoControle.confirmarExcluir}"
                                             value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                        </h:panelGroup>

                            <h:outputText value="    "/>

                            <a4j:commandLink id="consultar" immediate="true" action="#{ConvenioDescontoControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" title="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>

                            <a4j:commandLink action="#{ConvenioDescontoControle.realizarConsultaLogObjetoSelecionado}"
                                             reRender="form" oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                             title="Visualizar Log" styleClass="botoes nvoBt btSec">
                                <i style="text-decoration: none" class="fa-icon-list"/>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
<script>
    document.getElementById("form:descricao").focus();
</script>