<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ControleLog_tituloForm}"/>
    </title>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ControleLog_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-utilizar-o-controle-de-log/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{ControleLogControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_ControleLog_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" required="true" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{ControleLogControle.controleLogVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_descricaoLog}" />
                    <h:inputText  id="descricaoLog" size="10" maxlength="10" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ControleLogControle.controleLogVO.descricaoLog}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_data}" />
                    <h:panelGroup>
                        <rich:calendar id="data"
                                       value="#{ControleLogControle.controleLogVO.data}"
                                       inputSize="10"
                                       inputClass="form"
                                       oninputblur="blurinput(this);"
                                       oninputfocus="focusinput(this);"
                                       oninputchange="return validar_Data(this.id);"
                                       datePattern="dd/MM/yyyy"
                                       enableManualInput="true"
                                       zindex="2"
                                       showWeeksBar="false" />
                        <%--h:inputText  id="data" onkeypress="return mascara(this.form, 'form:data', '99/99/9999', event);" required="true" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{ControleLogControle.controleLogVO.data}" >
                            <f:convertDateTime pattern="dd/MM/yyyy"/>
                        </h:inputText--%>
                        <h:message for="data"  styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_entidade}" />
                    <h:panelGroup>
                        <h:inputText  id="entidade" required="true" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{ControleLogControle.controleLogVO.entidade}" />
                        <h:message for="entidade" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_responsavel}" />
                    <h:panelGroup>
                        <h:inputText  id="responsavel" required="true" size="10" maxlength="10" styleClass="camposObrigatorios" value="#{ControleLogControle.controleLogVO.responsavel}" />
                        <h:message for="responsavel" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_campo}" />
                    <h:panelGroup>
                        <h:inputText  id="campo" required="true" size="50" maxlength="50" styleClass="camposObrigatorios" value="#{ControleLogControle.controleLogVO.campo}" />
                        <h:message for="campo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_valorAnterior}" />
                    <h:panelGroup>
                        <h:inputText  id="valorAnterior" required="true" size="70" maxlength="100" styleClass="camposObrigatorios" value="#{ControleLogControle.controleLogVO.valorAnterior}" />
                        <h:message for="valorAnterior" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ControleLog_valorPosterior}" />
                    <h:panelGroup>
                        <h:inputText  id="valorPosterior" required="true" size="70" maxlength="100" styleClass="camposObrigatorios" value="#{ControleLogControle.controleLogVO.valorPosterior}" />
                        <h:message for="valorPosterior" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>               
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
</f:view>
<script>
    document.getElementById("form:descricaoLog").focus();
</script>