<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<script type="text/javascript" language="javascript" src="hoverform.js"></script>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/bootstrap-tabs.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Gestão de Personal Trainer"/>
    </title>

    <rich:modalPanel id="panelPersonal" autosized="true" shadowOpacity="true" width="550" height="300" onshow="document.getElementById('formPersonal:consultarPersonal').focus();" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_GestaoPersonal_consultarColaborador}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkPersonal"/>
                <rich:componentControl for="panelPersonal" attachTo="hiperlinkPersonal" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formPersonal" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%" columnClasses="esquerda, esquerda, direita, direita" styleClass="font-size-Em-max">
                    <h:outputText styleClass="text-size-14-real texto-font texto-cor-cinza texto-upper texto-bold" value="#{msg_aplic.prt_GestaoPersonal_nomePersonal}"/>
                    <h:inputText id="valorConsultarPersonal"
                                 onkeypress="if (event.keyCode == 13) {event.target.blur();event.stopPropagation();return false;} else {return true;}"
                                 styleClass="campos" value="#{GestaoPersonalControle.valorConsultarPersonal}"/>
                    <rich:spacer width="10"/>
                    <a4j:commandLink id="btnConsultarPersonal" reRender="formPersonal"
                                     action="#{GestaoPersonalControle.consultarPersonal}" styleClass="botaoPrimario texto-size-14-real"
                                     value="#{msg_bt.btn_consultar}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaPersonal" width="100%" styleClass="tabelaSimplesCustom" rendered="#{not empty GestaoPersonalControle.listaPersonal}"
                                value="#{GestaoPersonalControle.listaPersonal}" rows="7" var="Personal">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_GestaoPersonal_codigo}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{Personal.codigo}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_GestaoPersonal_nome}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{Personal.pessoa.nome}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                        <a4j:commandLink id="btnSelPersonal" action="#{GestaoPersonalControle.selecionarPersonal}" focus="Personal"
                                         reRender="form:panelGeral, formPersonal, mensagem, mensagem2,listaAlunos, totalLancado, listaParcelas, pnlAcoes"
                                         oncomplete="Richfaces.hideModalPanel('panelPersonal')"
                                         styleClass="linkPadrao texto-size-14-real texto-cor-azul">
                            Selecionar <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="resultadoConsultaPersonal" renderIfSinglePage="false" styleClass="scrollPureCustom" maxPages="7" id="scResultadoPersonal"/>
                <h:panelGrid id="mensagemConsultaPersonal" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{GestaoPersonalControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{GestaoPersonalControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelCliente" autosized="true" shadowOpacity="true" styleClass="novaModal" width="590" height="300" onshow="document.getElementById('formCliente:consultarCliente').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_VendaAvulsa_consultarCliente}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkCliente"/>
                <rich:componentControl for="panelCliente" attachTo="hiperlinkCliente" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formCliente" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%" styleClass="font-size-Em-max">
                    <h:outputText styleClass="text-size-14-real texto-font texto-cor-cinza texto-bold texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_nomeCliente}"/>
                    <h:inputText id="valorConsultarCliente"
                                 onkeypress="if (event.keyCode == 13) {event.target.blur();event.stopPropagation();return false;} else {return true;}"
                                 styleClass="campos" value="#{GestaoPersonalControle.valorConsultarCliente}"/>
                    <a4j:commandLink id="btnConsultarCliente" reRender="formCliente" action="#{GestaoPersonalControle.consultarClientes}"
                                     styleClass="botaoPrimario texto-size-14-real" value="#{msg_bt.btn_consultar}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaCliente" rendered="#{not empty GestaoPersonalControle.listaClientes}" width="100%"
                                styleClass="tabelaSimplesCustom" value="#{GestaoPersonalControle.listaClientes}" rows="7" var="cliente">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_Cliente_codigo}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{cliente.codigo}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_Cliente_pessoa}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{cliente.pessoa.nome}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_Cliente_situacao}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{cliente.situacao_Apresentar}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_Cliente_matricula}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-size-14-real texto-cor-cinza" value="#{cliente.matricula}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <a4j:commandLink id="btnSelAluno" action="#{GestaoPersonalControle.selecionarCliente}" focus="cliente"
                                         reRender="listaAlunos, mensagem, mensagem2, formCliente" oncomplete="#{GestaoPersonalControle.mensagemNotificar};Richfaces.hideModalPanel('panelCliente')"
                                         styleClass="linkPadrao texto-cor-azul" style="display: inline-flex">
                            Selecionar <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" styleClass="scrollPureCustom" renderIfSinglePage="false" for="formCliente:resultadoConsultaCliente" maxPages="10" id="scResultadoCliente"/>
                <h:panelGrid id="mensagemConsultaCliente" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{GestaoPersonalControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{GestaoPersonalControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelDesconto" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550" height="150">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Consulta de Desconto Para Produto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hidelink5"/>
                <rich:componentControl for="panelDesconto" attachTo="hidelink5" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formDesconto" ajaxSubmit="true">
            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                <rich:dataTable id="resultadoConsultaDesconto" width="100%" styleClass="tabelaSimplesCustom"
                                value="#{GestaoPersonalControle.listaDescontos}" rendered="#{not empty GestaoPersonalControle.listaDescontos}"  rows="5" var="desconto">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg_aplic.prt_Desconto_descricao}" />
                        </f:facet>
                        <h:outputText  styleClass="texto-size-16-real texto-cor-cinza texto-font" value="#{desconto.descricao}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper"  value="#{msg_aplic.prt_Desconto_tipoDesconto}"/>
                        </f:facet>
                        <h:outputText  styleClass="texto-size-16-real texto-cor-cinza texto-font" value="#{desconto.tipoProduto_Apresentar}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper"  value="#{msg_aplic.prt_Desconto_valor}"/>
                        </f:facet>
                        <h:outputText   styleClass="texto-size-16-real texto-cor-cinza texto-font" value="R$ " rendered="#{desconto.apresentarDescontoValor}"/>
                        <h:outputText  styleClass="texto-size-16-real texto-cor-cinza texto-font" value="#{desconto.valor}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                        <h:outputText    styleClass="texto-size-16-real texto-cor-cinza texto-font" value=" %" rendered="#{desconto.apresentarDescontoPorcentagem}"/>
                    </rich:column>
                    <rich:column>
                        <a4j:commandLink id="btnSelDesconto" action="#{GestaoPersonalControle.selecionarDesconto}"
                                         reRender="mensagem, listaAlunos, mensagem2,totalLancado"
                                         oncomplete="Richfaces.hideModalPanel('panelDesconto')"
                                         title="#{msg.msg_selecionar_dados}" styleClass="linkPadrao texto-size-14-real texto-cor-azul">
                            Selecionar <i class="fa-icon-arrow-right"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formDesconto:resultadoConsultaDesconto" maxPages="10" renderIfSinglePage="false" styleClass="scrollPureCustom"
                                   id="scResultadoDesconto" />
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelProduto" styleClass="novaModal" autosized="true" shadowOpacity="true" width="550"
                     height="300" onshow="document.getElementById('formProduto:consultarProduto').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{msg_aplic.prt_ItemVendaAvulsa_consultarProduto}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkProduto"/>
                <rich:componentControl for="panelProduto" attachTo="hiperlinkProduto" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formProduto" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="4" width="100%" styleClass="font-size-Em-max">
                    <h:outputText styleClass="texto-size-14-real texto-cor-cinza texto-bold texto-upper" value="#{msg.msg_consultar_por}"/>
                    <h:inputText id="valorConsultarProduto"
                                 onkeypress="if (event.keyCode == 13) {event.target.blur();event.stopPropagation();return false;} else {return true;}"
                                 styleClass="campos" value="#{GestaoPersonalControle.valorConsultarProduto}"/>
                    <a4j:commandLink id="btnConsultarProduto"
                                     reRender="formProduto"
                                     action="#{GestaoPersonalControle.consultarProduto}" styleClass="botaoPrimario texto-size-14-real"
                                     value="#{msg_bt.btn_consultar}" />
                </h:panelGrid>
                <rich:dataTable id="resultadoConsultaProduto" width="100%" rendered="#{not empty GestaoPersonalControle.listaProdutos}"
                                styleClass="tabelaSimplesCustom"
                                value="#{GestaoPersonalControle.listaProdutos}" rows="7" var="produto">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-cor-cinza texto-bold texto-size-14-real" value="CDIGO"/>
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-cor-cinza texto-size-14-real" value="#{produto.codigo}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText styleClass="texto-font texto-cor-cinza texto-bold texto-size-14-real" value="DESCRIÇÃO"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14-real"  value="#{produto.descricao}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <a4j:commandLink id="btnSelProduto" action="#{GestaoPersonalControle.selecionarProduto}" focus="produto"
                                         reRender="listaAlunos, mensagem, mensagem2,totalLancado"
                                         title="Seleciona o Produto para o aluno"
                                         oncomplete="Richfaces.hideModalPanel('panelProduto')"
                                         styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                            <i class="fa-icon-user"></i>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column>
                        <a4j:commandLink id="btnSelProdutoTodos" action="#{GestaoPersonalControle.aplicarProdutoAlunosMarcados}" focus="produto"
                                         reRender="listaAlunos, mensagem, mensagem2,totalLancado"
                                         title="Seleciona o Produto para todos os alunos marcados"
                                         oncomplete="Richfaces.hideModalPanel('panelProduto')"
                                         styleClass="linkPadrao texto-cor-azul texto-size-14-real">
                            <i class="fa-icon-group"></i>
                        </a4j:commandLink>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formProduto:resultadoConsultaProduto" maxPages="10" id="scResultadoProduto" renderIfSinglePage="false" styleClass="scrollPureCustom"/>
                <h:panelGrid id="mensagemConsultaProduto" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem" value="#{GestaoPersonalControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{GestaoPersonalControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <rich:modalPanel id="panelpp" autosized="true" styleClass="novaModal" shadowOpacity="true" width="550" height="260">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Produtos da Parcela #{GestaoPersonalControle.parcela.codigo}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                        id="hiperlinkpp"/>
                <rich:componentControl for="panelpp" attachTo="hiperlinkpp" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formpp" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="100%">
                <rich:dataTable id="listapp" width="100%" styleClass="tabelaSimplesCustom" rendered="#{not empty GestaoPersonalControle.parcela.personal.alunos}"
                                value="#{GestaoPersonalControle.parcela.personal.alunos}" rows="7" var="aluno">
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-font texto-cor-cinza texto-bold texto-size-14-real"  value="#{msg_aplic.prt_GestaoPersonal_aluno}"/>
                        </f:facet>
                        <h:outputText  styleClass="texto-font texto-cor-cinza texto-size-14-real"  value="#{aluno.aluno.pessoa.nome}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-font texto-cor-cinza texto-bold texto-size-14-real"  value="#{msg_aplic.prt_GestaoPersonal_produto}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14-real"  value="#{aluno.produto.descricao}"/>
                    </rich:column>
                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                        <f:facet name="header">
                            <h:outputText  styleClass="texto-font texto-cor-cinza texto-bold texto-size-14-real" value="#{msg_aplic.prt_GestaoPersonal_valorProduto}"/>
                        </f:facet>
                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14-real"  value="R$ "/>
                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14-real"  value="#{aluno.valorFinal}">
                            <f:converter converterId="FormatadorNumerico"/>
                        </h:outputText>
                    </rich:column>
                </rich:dataTable>
                <rich:datascroller align="center" for="formpp:listapp" maxPages="10" id="scResultadopp" renderIfSinglePage="false" styleClass="scrollPureCustom"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />

    <h:form id="form" >
        <a4j:keepAlive beanName="ExportadorListaControle"/>
        <html>
        <jsp:include page="include_head.jsp" flush="true" />
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Gestão de Personal Trainer" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-taxa-mensal-para-personal-gestao-de-personal/"
                                                      title="Clique e saiba mais: Gestao de Personal Trainer" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup id="panelCaixaAberto"  layout="block" styleClass="margin-box">
                                    <h:panelGrid id="mensagem" columns="2" width="100%" styleClass="tabMensagens" style="margin:0 0 5px 0;">
                                        <h:panelGrid columns="1" width="100%">
                                            <h:outputText id="msgPersonal2" styleClass="mensagem" value="#{GestaoPersonalControle.mensagem}"/>
                                            <h:outputText id="msgPersonalDet2" styleClass="mensagemDetalhada" value="#{GestaoPersonalControle.mensagemDetalhada}"/>
                                        </h:panelGrid>
                                    </h:panelGrid>

                                    <h:panelGrid id="panelGeral" columns="2" width="100%" columnClasses="colunaEsquerda, colunaEsquerda" cellpadding="5" styleClass="font-size-Em-max">
                                        <h:outputText styleClass="rotuloCampos"
                                                      rendered="#{GestaoPersonalControle.apresentarDtLancamento}"
                                                      value="#{msg_aplic.prt_GestaoPersonal_dataLancamento}"/>
                                        <h:panelGroup id="panelDtLancamento" styleClass="dateTimeCustom"
                                                      rendered="#{GestaoPersonalControle.apresentarDtLancamento}">
                                            <rich:calendar id="dtLancamento"
                                                           value="#{GestaoPersonalControle.dataLancamentoParcela}"
                                                           inputSize="15"
                                                           showWeekDaysBar="false"
                                                           inputClass="form"
                                                           showWeeksBar="false"
                                                           oninputfocus="focusinput(this);"
                                                           oninputblur="blurinput(this);"
                                                           buttonIcon="/imagens_flat/calendar-button.svg"
                                                           enableManualInput="true"
                                                           oninputchange="return validar_Data(this.id);"
                                                           datePattern="dd/MM/yyyy">

                                                <a4j:support event="onchanged" reRender="panelMesReferencia"/>
                                            </rich:calendar>
                                            <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" style="height: 25px"/>
                                        <h:panelGroup layout="block" style="height: 25px"/>

                                        <h:outputText styleClass="rotuloCampos"
                                                      value="#{msg_aplic.prt_GestaoPersonal_empresa}"
                                                      rendered="#{GestaoPersonalControle.apresentarEmpresa}"/>
                                        <h:panelGroup rendered="#{GestaoPersonalControle.apresentarEmpresa}">
                                            <h:panelGroup layout="block" styleClass="cb-container">
                                                <h:selectOneMenu id="empresa" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);"
                                                                 styleClass="form"
                                                                 value="#{GestaoPersonalControle.empresa.codigo}">
                                                    <f:selectItems value="#{GestaoPersonalControle.listaEmpresas}"/>
                                                    <a4j:support event="onchange"
                                                                 action="#{GestaoPersonalControle.limparDados}"
                                                                 reRender="panelGeral, formProduto, formDesconto, formCliente, formPersonal"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                            <rich:spacer width="5"/>
                                            <a4j:commandLink id="atualizar_empresa"
                                                             action="#{GestaoPersonalControle.montarListaSelectItemEmpresa}"
                                                             style="vertical-align:middle;"
                                                             styleClass="linkPadrao texto-cor-azul texto-size-14-real"
                                                             immediate="true"
                                                             ajaxSingle="true"
                                                             reRender="form:empresa">
                                                <i class="fa-icon-refresh"></i>
                                            </a4j:commandLink>
                                        </h:panelGroup>

                                        <h:outputText styleClass="rotuloCampos texto-upper" value="#{msg_aplic.prt_GestaoPersonal_nomePersonal} "/>
                                        <h:panelGroup id="panelGroupPersonal" styleClass="vertical-align=middle">
                                            <h:inputText id="nomeComprador" size="50" readonly="true"
                                                         maxlength="50" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="inputTextClean"
                                                         value="#{GestaoPersonalControle.personal.pessoa.nome}"/>
                                            <rich:spacer width="5"/>
                                            <a4j:commandLink id="btnConsultaPersonal"
                                                             oncomplete="Richfaces.showModalPanel('panelPersonal')"
                                                             style="vertical-align:middle;"
                                                             styleClass="linkPadrao texto-size-16-real texto-cor-azul"
                                                             title="#{msg_aplic.prt_GestaoPersonal_consultarColaborador}">
                                                <i class="fa-icon-plus-sign"></i>
                                            </a4j:commandLink>
                                            <rich:spacer width="5"/>
                                        </h:panelGroup>
                                        <h:outputText styleClass="rotuloCampos texto-upper" value="#{msg_aplic.prt_GestaoPersonal_mes}"/>
                                        <h:panelGroup id="panelMesReferencia">
                                            <h:inputText id="mesReferencia" size="15" readonly="true" style="vertical-align: middle;"
                                                         maxlength="15" onblur="blurinput(this);"
                                                         onfocus="focusinput(this);" styleClass="inputTextClean"
                                                         value="#{GestaoPersonalControle.apresentarMesReferencia}"/>
                                            <h:panelGroup layout="block" styleClass="dateTimeCustom" style="display: inline-block; vertical-align: middle;">
                                                <rich:calendar id="calendario"
                                                               value="#{GestaoPersonalControle.mesReferencia}"
                                                               showInput="false"
                                                               zindex="2"
                                                               buttonIcon="/imagens_flat/calendar-button.svg"
                                                               showWeeksBar="false">
                                                    <a4j:support event="onchanged" reRender="panelMesReferencia"/>
                                                </rich:calendar>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                    </h:panelGrid>
                                    <h:panelGroup layout="block" styleClass="container-botoes">
                                        <a4j:commandLink id="consultar" styleClass="botaoPrimario texto-size-16-real"
                                                         action="#{GestaoPersonalControle.consultarAlunos}"
                                                         title="#{msg.msg_consultar_dados}" accesskey="2"
                                                         reRender="listaAlunos, totalLancado, mensagem, listaParcelas, mensagem2">
                                            <i class="fa-icon-search " ></i> &nbsp ${msg_bt.btn_consultar}
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:panelGroup id="pnlAcoes">
                                        <h:panelGroup rendered="#{GestaoPersonalControle.personalSelecionado}">
                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                            <h:panelGrid width="60%" columns="1" style="margin:5px 0 15px 0;">
                                                <rich:dataTable width="100%" value="#{GestaoPersonalControle.listaParcelas}"
                                                                var="parcela" id="listaParcelas" rows="5"
                                                                styleClass="tabelaSimplesCustom">
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper" value="Parcela"/>
                                                        </f:facet>
                                                        <h:outputText  styleClass="texto-cor-cinza exto-size-14-real" value="#{parcela.codigo}"/>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="Descrição"/>
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-cor-cinza exto-size-14-real"  value="#{parcela.descricao}"/>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper" value="Valor R$"/>
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-cor-cinza exto-size-14-real"  value="#{parcela.valorParcela}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="Vencimento"/>
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-cor-cinza exto-size-14-real"  value="#{parcela.dataVencimento_Apresentar}"/>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-center" headerClass="col-text-align-center">
                                                        <h:panelGroup>
                                                            <a4j:commandButton id="visualizarAlunos" image="images/icon_cadastros.gif" title="Ver os Produtos Desta Parcela."
                                                                               oncomplete="Richfaces.showModalPanel('panelpp')" reRender="panelpp, formpp"
                                                                               action="#{GestaoPersonalControle.verLista}"/>
                                                        </h:panelGroup>

                                                        <h:panelGroup>
                                                            <rich:spacer width="5" rendered="#{parcela.pago}"/>
                                                            <a4j:commandButton id="parcelaPaga" image="imagens/dinheiro_desabilitado.png"
                                                                               title="Parcela Paga."
                                                                               rendered="#{parcela.pago}"/>

                                                            <rich:spacer width="5" rendered="#{!parcela.pago}"/>
                                                            <a4j:commandButton id="pagarParcela" rendered="#{!parcela.pago}"
                                                                               action="#{GestaoPersonalControle.pagarParcela}"
                                                                               reRender="form"
                                                                               title="Pagar Parcela."
                                                                               image="imagens/dinheiro.png" >

                                                            </a4j:commandButton>
                                                        </h:panelGroup>

                                                    </rich:column>
                                                </rich:dataTable>
                                                <rich:datascroller styleClass="scrollPureCustom" renderIfSinglePage="false" for="listaParcelas" maxPages="1000"/>

                                            </h:panelGrid>
                                            <h:panelGroup layout="block" styleClass="container-botoes">
                                                <a4j:commandLink id="btnAddAluno" action="#{GestaoPersonalControle.confirmarAdicionarVinculos}"
                                                                 styleClass="botaoPrimario texto-size-16-real texto-cor-branco"
                                                                 oncomplete="#{GestaoPersonalControle.mensagemNotificar}" title="Adicionar Aluno"
                                                                 reRender="listaAlunos, panelAutorizacaoFuncionalidade">
                                                    <i class="fa-icon-plus-sign"></i>
                                                </a4j:commandLink>

                                                <a4j:commandLink id="btnRetAluno" action="#{GestaoPersonalControle.confirmarRetirarVinculos}"
                                                                 title="Retirar Aluno" styleClass="botaoSecundario texto-size-16-real texto-cor-vermelho"
                                                                 style="margin-left: 8px"
                                                                 reRender="panelAutorizacaoFuncionalidade">
                                                    <i class="fa-icon-minus-sign"></i>
                                                </a4j:commandLink>

                                            </h:panelGroup>
                                            <h:panelGrid id="alunos" columns="1" width="100%" styleClass="tabFormSubordinada">
                                                <rich:dataTable id="listaAlunos" styleClass="tabelaSimplesCustom"
                                                                value="#{GestaoPersonalControle.listaAlunos}" var="aluno">
                                                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_aluno}" />
                                                        </f:facet>
                                                        <h:outputText styleClass="texto-cor-cinza texto-size-14-real "  value="#{aluno.aluno.pessoa.nome} #{aluno.nomeEmpresa}" />
                                                    </rich:column>
                                                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_produto}" />
                                                        </f:facet>
                                                        <h:outputText  styleClass="texto-cor-cinza texto-size-14-real "  value="#{aluno.produto.descricao}" />
                                                        <rich:spacer width="10"/>
                                                        <a4j:commandButton id="btnProduto" action="#{GestaoPersonalControle.escolherAluno}" reRender="panelProduto"
                                                                           oncomplete="Richfaces.showModalPanel('panelProduto')" rendered="#{aluno.livre}"
                                                                           image="imagens/informacao.gif" title="#{msg_aplic.prt_ItemVendaAvulsa_consultarProduto}"/>
                                                    </rich:column>
                                                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_mes}" />
                                                        </f:facet>
                                                        <h:outputText  styleClass="texto-cor-cinza texto-size-14-real "  value="#{GestaoPersonalControle.apresentarMesReferencia}" />
                                                    </rich:column>
                                                    <rich:column  styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_valorProduto} R$ " />
                                                        </f:facet>

                                                        <h:outputText  styleClass="texto-cor-cinza texto-size-14-real "  value="#{aluno.produto.valorFinal}" >
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_desconto} R$" />
                                                        </f:facet>

                                                        <h:outputText  styleClass="texto-cor-cinza texto-size-14-real "  value="#{aluno.valorDesconto}"
                                                                       rendered="#{!aluno.mostrarDesconto}">
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                        <h:outputText value=" %" rendered="#{aluno.produto.desconto.apresentarDescontoPorcentagem}"/>
                                                        <h:panelGroup rendered="#{aluno.livre}">
                                                            <rich:spacer width="10"/>
                                                            <a4j:commandButton id="btnDesconto" oncomplete="Richfaces.showModalPanel('panelDesconto')" reRender="formDesconto"
                                                                               image="imagens/informacao.gif" title="Consultar Desconto"
                                                                               action="#{GestaoPersonalControle.consultarDesconto}" />
                                                            <rich:spacer width="5" />
                                                            <a4j:commandButton id="btnChave" image="./images/icon_chave.png" title="Informar Valor do Produto com Desconto"
                                                                               action="#{GestaoPersonalControle.validarPermissaoDescontoProduto}"
                                                                               reRender="panelAutorizacaoFuncionalidade"/>
                                                            <h:outputText >
                                                                <f:converter converterId="FormatadorNumerico"/>
                                                            </h:outputText>
                                                            <h:inputText rendered="#{aluno.mostrarDesconto}"
                                                                         value="#{aluno.desconto.valor}"
                                                                         style="text-align:right;" id="descontoPlanoProdutoSugerido"
                                                                         size="10">
                                                                <f:converter converterId="FormatadorNumerico"/>
                                                            </h:inputText>
                                                            <a4j:commandButton rendered="#{aluno.mostrarDesconto}"
                                                                               action="#{GestaoPersonalControle.editarDescontoProduto}"
                                                                               image="./images/tick.png" title="Aplicar Novo Valor"
                                                                               reRender="listaAlunos, form:panelCaixaAberto"/>

                                                            <rich:spacer width="5" />
                                                            <a4j:commandButton reRender="listaAlunos, panelCaixaAberto"  id="limparTabelaDesconto" immediate="true"
                                                                               action="#{GestaoPersonalControle.limparCampoDesconto}" image="images/limpar.gif"
                                                                               title="Limpar Desconto"/>
                                                        </h:panelGroup>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:outputText  styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="#{msg_aplic.prt_GestaoPersonal_total} R$" />
                                                        </f:facet>

                                                        <h:outputText  styleClass="texto-cor-cinza texto-size-14-real "  value="#{aluno.valorFinal}" >
                                                            <f:converter converterId="FormatadorNumerico"/>
                                                        </h:outputText>
                                                    </rich:column>
                                                    <rich:column styleClass="col-text-align-left" headerClass="col-text-align-left">
                                                        <f:facet name="header">
                                                            <h:panelGroup id="toggleMarcacao">
                                                                <h:selectBooleanCheckbox id="toggleMarcarAlunos"
                                                                                         title="Marcar Todos Alunos"
                                                                                         value="#{GestaoPersonalControle.todosMarcados}">
                                                                    <a4j:support event="onclick"
                                                                                 action="#{GestaoPersonalControle.marcarTodos}"
                                                                                 reRender="listaAlunos, totalLancado"/>
                                                                </h:selectBooleanCheckbox>

                                                                <h:outputText  styleClass="texto-cor-cinza texto-bold texto-size-14-real texto-upper"  value="Opções"/>
                                                            </h:panelGroup>
                                                        </f:facet>
                                                        <h:selectBooleanCheckbox id="alunoEscolhido" disabled="#{!aluno.livre}"
                                                                                 value="#{aluno.marcado}">
                                                            <a4j:support event="onclick"
                                                                         focus="alunoEscolhido"
                                                                         action="#{GestaoPersonalControle.calcularTotal}"
                                                                         reRender="mensagem,totalLancado, mensagem2, toggleMarcacao"/>
                                                        </h:selectBooleanCheckbox>
                                                        <h:panelGroup rendered="#{aluno.gerado}">
                                                            <rich:spacer width="5"/>
                                                            <h:graphicImage id="parcelaGerada" url="images/quadrado_azul.gif" title="Parcela Para Este Cliente Esta Gerada." />
                                                        </h:panelGroup>
                                                        <h:panelGroup rendered="#{aluno.pago}">
                                                            <rich:spacer width="5"/>
                                                            <h:graphicImage id="parcelaPaga" url="images/quadrado_verde.gif" title="Parcela Para Este Cliente Esta Paga." />
                                                        </h:panelGroup>
                                                        <h:panelGroup rendered="#{aluno.vencido}">
                                                            <rich:spacer width="5"/>
                                                            <h:graphicImage id="parcelaVencida" url="images/quadrado_vermelho.gif" title="Parcela Para Este Cliente Esta Vencida."/>
                                                        </h:panelGroup>
                                                        <h:panelGroup rendered="#{aluno.livre}">
                                                            <rich:spacer width="5"/>
                                                            <h:graphicImage id="naoNegociado" url="images/quadrado_cinza.gif" title="Aluno No Negociado Ainda."/>
                                                        </h:panelGroup>
                                                    </rich:column>
                                                </rich:dataTable>
                                            </h:panelGrid>
                                            <div class="sep" style="margin:4px 0 10px 0;"><img src="images/shim.gif"></div>
                                            <h:panelGrid width="100%" columns="2" columnClasses="colunaDireita">
                                                <h:panelGroup>
                                                    <h:outputText styleClass="tituloCampos" value="Legenda:"/>
                                                    <rich:spacer width="10"/>
                                                    <h:graphicImage url="images/quadrado_azul.gif" title="Parcela Para Este Cliente Esta Gerada."/>
                                                    <rich:spacer width="10"/>
                                                    <h:outputText styleClass="tituloCampos" value="Negociado"/>
                                                    <rich:spacer width="10"/>
                                                    <h:graphicImage url="images/quadrado_verde.gif" title="Parcela Para Este Cliente Esta Paga."/>
                                                    <rich:spacer width="10"/>
                                                    <h:outputText styleClass="tituloCampos" value="Pago"/>
                                                    <rich:spacer width="10"/>
                                                    <h:graphicImage url="images/quadrado_vermelho.gif" title="Parcela Para Este Cliente Esta Vencida."/>
                                                    <rich:spacer width="10"/>
                                                    <h:outputText styleClass="tituloCampos" value="Vencido"/>
                                                    <rich:spacer width="10"/>
                                                    <h:graphicImage url="images/quadrado_cinza.gif" title="Aluno No Negociado Ainda."/>
                                                    <rich:spacer width="10"/>
                                                    <h:outputText styleClass="tituloCampos" value="Livre"/>
                                                    <rich:spacer width="10"/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGrid width="100%" columns="1" columnClasses="colunaDireita">
                                                <h:panelGroup>
                                                    <h:graphicImage url="imagens/dinheiro_desabilitado.png" title="Parcela Paga."/>
                                                    <rich:spacer width="10"/>
                                                    <h:outputText styleClass="tituloCampos" value="Parcela Paga"/>
                                                    <rich:spacer width="10"/>
                                                    <h:graphicImage url="imagens/dinheiro.png" title="Pagar Parcela."/>
                                                    <rich:spacer width="10"/>
                                                    <h:outputText styleClass="tituloCampos" value="Pagar Parcela"/>
                                                    <rich:spacer width="10"/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGroup layout="block" styleClass="pure-form pure-form-aligned pure-u-2-3">
                                                <a4j:commandLink id="btnExcel"
                                                                 styleClass="pure-button pure-button-small"
                                                                 actionListener="#{GestaoPersonalControle.exportar}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                 accesskey="3">
                                                    <f:attribute name="tipo" value="xls"/>
                                                    <f:attribute name="atributos" value="personal=Personal,nomeAluno=Aluno,descricaoProduto=Produto,mesReferencia=Ms,valorProduto=Valor,valorDesconto=Desconto,valorFinal=ValorFinal"/>
                                                    <f:attribute name="prefixo" value="Personal"/>

                                                    <i class="fa-icon-excel" ></i> &nbsp Excel
                                                </a4j:commandLink>

                                                <a4j:commandLink id="btnPDF"
                                                                 styleClass="pure-button pure-button-small margin-h-10"
                                                                 actionListener="#{GestaoPersonalControle.exportar}"
                                                                 oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                 accesskey="4">
                                                    <f:attribute name="tipo" value="pdf"/>
                                                    <f:attribute name="atributos" value="personal=Personal,nomeAluno=Aluno,descricaoProduto=Produto,mesReferencia=Ms,valorProduto=Valor,valorDesconto=Desconto,valorFinal=ValorFinal"/>
                                                    <f:attribute name="prefixo" value="Personal"/>

                                                    <i class="fa-icon-pdf" ></i> &nbsp PDF
                                                </a4j:commandLink>
                                            </h:panelGroup>
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepadding2" >
                                                <tr>
                                                    <td align="left" valign="top">
                                                        <table id="tablepreviewtotal" width="100%" border="0" cellspacing="0" cellpadding="0" class="tablepreviewtotal">
                                                            <tr>
                                                                <td width="50%" align="right" valign="middle">Total Lan&ccedil;ado =
                                                                    <span class="verde">R$
                                                                                <h:outputText id="totalLancado" value="#{GestaoPersonalControle.valorTotal}"><f:converter converterId="FormatadorNumerico"/></h:outputText>
                                                                            </span>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                            <h:panelGrid id="mensagem2" columns="2" width="100%" styleClass="tabMensagens" style="margin:0 0 5px 0;">
                                                <h:panelGrid columns="1" width="100%">
                                                    <h:outputText id="msgPersonal" styleClass="mensagem" value="#{GestaoPersonalControle.mensagem}"/>
                                                    <h:outputText id="msgPersonalDet" styleClass="mensagemDetalhada" value="#{GestaoPersonalControle.mensagemDetalhada}"/>
                                                </h:panelGrid>
                                            </h:panelGrid>
                                            <h:panelGrid columns="2" width="100%" columnClasses="colunaDireita">
                                                <h:panelGroup>
                                                    <a4j:commandLink
                                                        action="#{GestaoPersonalControle.realizarConsultaLogObjetoSelecionado}"
                                                        reRender="form"
                                                        oncomplete="fireElementFromAnyParent('form:btnAtualizaTempo');abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                        title="Visualizar Log"
                                                        styleClass="botoes nvoBt btSec">
                                                        <i style="text-decoration: none" class="fa-icon-list"/>
                                                    </a4j:commandLink>
                                                    <a4j:commandLink id="btnFecharNegociacao"
                                                                     styleClass="pure-button pure-button-primary "
                                                                     action="#{GestaoPersonalControle.fecharNegociacao}"
                                                                     oncomplete="#{GestaoPersonalControle.mensagemNotificar}"
                                                                     reRender="form:panelCaixaAberto" >
                                                    Fechar Negociação &nbsp <i class="fa-icon-arrow-right" ></i>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>

                            </h:panelGroup>

                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>
            <jsp:include page="include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
        </html>
    </h:form>
    <%@include file="includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>

</f:view>
