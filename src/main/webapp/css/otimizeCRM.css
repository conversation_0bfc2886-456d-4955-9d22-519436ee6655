alinhamentoSuperiora:link {
	text-decoration: none;
}


a:visited {
	text-decoration: none;
}

a:hover {
	text-decoration: underline overline;
	color: #fd8448
}

a:active {
	text-decoration: underline overline;
	color: #fd8448
}

.form {
	font-family: Trebuchet MS, Helvetica, sans-serif;
	font-size: 10px;
	color: #515151;
	border: solid #8eb3c3 1px;
	padding-left: 4px;
	padding-top: 4px;
	height: 23px;
}

table.tabForm {
	background-color: #FAE9D2;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#FAE9D2, endColorStr=#ffffff,GradientType=0);
}

table.tabFormSubordinada {
	background-color: #FAE9D2;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#FFFFFF,
            endColorStr=#FAE9D2,GradientType=0);
}

td.colunaEsquerda {
	vertical-align: top;
	text-align: left;
}

td.colunaDireita {
	vertical-align: top;
	text-align: right;
}

td.colunaCentralizada {
	vertical-align: top;
	text-align: center;
}

.camposSomenteLeitura {
	background-color: #EEEEEE;
	font: 10px Trebuchet MS, arial, helvetica, sans-serif;
	color: #cc9933;
	margin-right: 20px;
	border: 1px solid #cc9933;
}

.tituloTotal {
	font: 24px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	color: #000000;
	text-align: right;
}

#panel1 {
	float: left;
}


#panel2 {
	float: left;
}

/*.rich-panel-header{
    background-color: red;
}*/
.camposObrigatorios {
	background-color: #DBDBDB;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr =#fae9d1,endColorStr=#ffffff,GradientType=1);
	font: 10px Trebuchet MS, arial, helvetica, sans-serif;
	color: #C67700;
	border: 1px solid #CCCCCC;
	border-bottom-color: #FF9900;
}

.tituloCamposDestaqueNegrito {
	font: 16pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	color: #000000;
	font-weight: bold;
	text-align: right;
}
.tituloCampos{
	font: 9px verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #474747;
	text-align: right;
}


.camposIndicadorVendas {
	font: 10px Tahoma, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #333333;
	text-align: right;
}
.camposIndicadorVendasMeta {
	font: 12px Tahoma, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #0098DA;
	text-align: right;
	background-color: #FFFBD6;
}
.camposIndicadorVendasMetaAtingida {
	font: 12px Tahoma, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #EF3E41; 
	text-align: right;
	background-color: #FFFBD6;
}
.camposIndicadorVendasMetaPorcentagem {
	font: 12px Tahoma, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #F58634;
	text-align: right;
	background-color: #FFFBD6;
}
.camposAberturaMeta {
	font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	color: #474747;
	text-align: right;
}
.tituloCamposAberturaMeta {
	font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #474747;
	text-align: right;
}
.tituloRichPanel{
	font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #000000;
	text-align: center;
}
.camposAgenda {
	font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #474747;
	text-align: right;
}
.camposAgendado {
	font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #333333;
	text-align: right;
}
.dataLancamento {
	font: 8px  verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: maroon;
	text-align: right;
}

.mensagemTelefoneNaoEncontrado {
	font: 10px 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	font-weight: bold;
	color: #333333;
	text-align: right;
}

.tituloFormulario {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 11px;
	font-weight: bold;
	text-decoration: none;
	color: #000000;
}

.campos {
	background-color: #fae9d1;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#fae9d1,
            endColorStr=#ffffff,GradientType=1);
	font: 10px Trebuchet MS, arial, helvetica, sans-serif;
	color: #000000;
	margin-right: 20px;
	border: 1px solid #CCCCCC;
}

table.tabMensagens {
	background-color: #FAE9D2;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#ffffff,
            endColorStr=#FAE9D2, GradientType=0);
}

.mensagem {
	font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
	color: #333333;
	text-align: right;
}
.mensagemDetalhada {
    font: 10pt 'Trebuchet MS', verdana, arial, helvetica, sans-serif;
    color: #DF0000;
    text-align: right;
}

tr.linhaPar:not(.notSize){
	font-size: 8pt;
	font-family: 'Trebuchet MS', verdana;
}

tr.linhaImpar:not(.notSize){
	font-size: 8pt;
	font-family: 'Trebuchet MS', verdana;
	background-color: #FCECCF;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#FFFFFF,
            endColorStr=#FFFFFF,GradientType=0);
}

tr.linhaParSubordinado {
	font-size: 8pt;
	font-family: 'Trebuchet MS', verdana;
}

tr.linhaImparSubordinado {
	font-size: 8pt;
	font-family: 'Trebuchet MS', verdana;
	background-color: #FFFFFF;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#FFFFFF, endColorSt=#FFFFFF,GradientType=0);
}

th.consulta {
	background-color: #FAE9D2;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#FFFFFF, endColorStr=#FAE9D2,GradientType=0);
	font-size: 8pt;
	font-weight: bold;
}


th.subordinado {
	background-color: #FAE9D2;
	filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr=#FFFFFF,
            endColorStr=#FAE9D2,GradientType=0);
	font-size: 8pt;
	font-weight: bold;
}

.botoes {
	
}

.w30 {
	width: 30%;
	text-align: left;
	vertical-align: top;
}

.w5 {
	width: 5%;
	text-align: left;
	vertical-align: top;
}

.w10 {
	width: 10%;
	text-align: left;
	vertical-align: top;
}

.w20 {
	width: 20%;
	text-align: left;
	vertical-align: top;
}

.w33 {
	width: 33%;
	text-align: left;
	vertical-align: top;
}

.w30 {
	width: 30%;
	text-align: left;
	vertical-align: top;
}

.w40 {
	width: 40%;
	text-align: left;
	vertical-align: top;
}

.w50 {
	width: 50%;
	text-align: left;
	vertical-align: top;
}

.w60 {
	width: 60%;
	text-align: left;
	vertical-align: top;
}

.w70 {
	width: 70%;
	text-align: left;
	vertical-align: top;
}

.w80 {
	width: 80%;
	text-align: left;
	vertical-align: top;
}

.w90 {
	width: 90%;
	text-align: left;
	vertical-align: top;
}

.w100 {
	width: 100%;
	text-align: left;
	vertical-align: top;
}

.w200 {
	width: 200px;
	text-align: left;
	vertical-align: top;
}
.semBorda{
    border: none;
}
/****** head *******/