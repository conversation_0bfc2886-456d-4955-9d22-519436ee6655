.selecionado .opcaoPagamentoHead .cinza {
    color: white;
}

.selecionado .opcaoPagamentoHead {
    background-color: #094771;
}

.aberto .opcaoPagamentoHead {
    border-bottom: 1px solid #e5e5e5;
}

.opcaoPagamentoHead .chk-fa-container input {
    margin-top: -2px;
}

.opcaoPagamentoHead .chk-fa-container {
    display: inline-block !important;
    width: 2%;
    margin-left: 10px;
}

.opcaoPagamentoHead .valortotal {
    float: right;
    margin-right: 40px;
}

.opcaoPagamento.aberto {

}

.opcaoPagamento.selecionado {

}

.detalhesPagamento .bloco:first-child {
    padding: 0px;
}

.opcaoPagamentoBody .bloco, .detalhesPagamento .bloco {
    display: inline-block;
    padding: 0 0 0 20px;
}

.opcaoPagamento {
    -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.35);
    -moz-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.35);
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.35);
    height: auto;
    min-height: 40px;
    line-height: 40px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.opcaoPagamentoHead i {
    float: right;
    margin-top: 10px;
    font-size: 20px;
    color: #e5e5e5;
}

.opcaoPagamentoHead a {
    display: inline-block;
    width: 96%;
    font-size: 14px;
}

.opcaoPagamentoBody {
    display: none;
}

.opcaoPagamento:not(.aberto) .fa-icon-chevron-up, .aberto.opcaoPagamento .fa-icon-chevron-down {
    display: none;
}

.aberto .opcaoPagamentoBody {
    display: block;
    min-height: 100px;
}

.addNovoCartao {
    display: block;
    padding: 15px;
    text-align: right;
}

a.addObservacao {
    margin-left: 20px;
    font-size: 14px !important;
}

.bloco .dados {
    margin-top: 10px;
    font-size: 20px;
}

.bloco .dados a .cinza {
    font-size: 20px;
}

.bloco tr.linhaImpar {
    background-color: #fff;
}

.bloco th.consulta {
    text-align: center;
    padding: 10px 15px;
    color: #777;
    background-color: #e5e5e5;
    border-color: #ddd;
    font-family: Arial;
    font-size: 14px;
    text-transform: uppercase;
}

.bloco .rich-table {
    box-shadow: 0 1px 1px rgba(0, 0, 0, .3);
    border: none !important;
}

.fa-icon-toggle-on:before {
    content: "\f205"
}

.novaModal.modalcheques.noMargin .rich-mpnl-body{
    padding: 15px;
}

.container-imagem {
    overflow: initial !important;
    height: 100vh !important;
}
