.caixa-build i{
    font-size: 60px;
    margin-top: 125px;
    display: block;
    color: lightgrey;
}
.caixa-build{
    position: relative;
    max-width: initial;
    padding: 12px 12px 0;
    vertical-align: top;
    background: white;
    text-align: center;
    box-shadow: rgba(0, 0, 0, 0.1) 1px 0px 5px 0px;
    width: 250px;
    height: 350px;
    display: inline-block;
    border: 1px solid #e6e6e6;
    margin-right: 20px;
    margin-top: 20px;
    overflow: hidden;
    cursor: pointer;
}
.modelosSms .caixa-build{
    height: 200px;
}
.modelosSms .caixa-build i{
    margin-top: 45px;
}
.modelosSms .caixa-build .btn-builder i{
    margin-top: 0px;
}
.top-aba-builder:hover{
    text-decoration: none;
}
.top-aba-builder{
    display: inline-block;
    padding: 0 15px 10px;

}

.botoes-builder-secundarios{
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
}
.botoes-builder{
    position: absolute;
    top: 5px;
    right: 13px;
}
.thumb-html{
    width: 250px;
    height: 300px;
    pointer-events: none;
    transform: translate(-98px,-86px) scale(.5, .5);
}
.hover-layer{
    width: 250px;
    height: 300px;
    position: absolute;
    display: none;
    left: 0;
    top: 0;
    text-align: center;
    cursor: pointer;
    background: rgba(9, 71, 113,0.89);
    width: 100%;
    height: 350px;
}
.btn-builder i{
    color: #FFFFFF !important;
    font-size: 16px;
    margin-right: 10px;
    margin-top: 0px;
}
.btn-builder:hover i{
    color: #074871 !important;
}
.btn-builder:hover{
    background-color: #fff;
    text-decoration: none;
    color: #074871;
}
.caixa-build:hover i{
    color: #074871;
}
.btn-builder{
    color: #fff;
    display: block;
    margin: 10px;
    padding: 10px;
    background-color: transparent;
    background-image: none;
    font-size: 14px;
    box-shadow: inset 0 0 0 1px #cbc7da;
}
a{
    cursor: pointer;
}

.rodape-build{
    position: absolute;
    height: 50px;
    background: #FFFFFF;
    border-top: 1px solid #e6e6e6;
    left: 0;
    bottom: 0;
    text-align: center;
    width: 100%;
    line-height: 50px;
}
.caixa-build:hover .hover-layer{
    display: block;
}
.rich-table-sortable-header{
    text-transform: uppercase;
    color: #777777;
    font-size: 14px !important;
}
.dataTable.tableCliente td.columnClass a{
    color: #777777;
    text-transform: capitalize;
    font-size: 16px !important;
}
.rich-table-cell {
    border-right: none !important;
    border-bottom: none !important;
}
.rich-table {
    border-top: none !important;
    border-left: none !important;
}
.novaModal .tituloCampos{
    margin: 6px 0;
}
.dataTable.tableCliente th.consulta {
    background-color: #FFF !important;
    border-right: none !important;
}
.dataTable.tableCliente td a, .tituloTotalizador{
    padding: 0;
}
a.configs:hover{
    text-decoration: none;
}
a.configs{
    margin-left: 10px;
    font-size: 25px;

}
.saldos{
    position: absolute;
    right: 0;
    top: -35px;
    text-align: right;
}
.saldos .saldo{
    display: inline-block;
    margin-left: 10px;
    text-align: left;
    width: 280px;
    height: 60px;
    padding: 5px 10px;
    border: 1px solid #e6e6e6;
    box-shadow: rgba(0, 0, 0, 0.1) 1px 0px 5px 0px;
}
.titulo-saldo{
    color: #989898;
    font-size: 10px;
    display: block;
    font-weight: bold;
}
.saldo-valor{
    display: flex;
    color: #333;
    font-weight: bold;
    font-size: 14px;
    justify-content: space-between;
}
.saldo-expira{
    text-align: right;
    color: #989898;
    font-size: 10px;
    font-weight: bold;
}
.barra-saldo{
    width: 100%;
    height: 12px;
    background-color: #E5E5E5;
    display: block;
    margin-top: 5px;
    border-radius: 10px;
    overflow: hidden;
}
.inside-barra-saldo{
    background-color: #094771;
    height: 12px;
}