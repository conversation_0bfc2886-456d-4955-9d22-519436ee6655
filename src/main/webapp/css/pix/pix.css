.pix-qr-container {
    flex-direction: column;
    display: flex;
    background-color: #FAFAFA;
    padding-left: 66px;
    padding-right: 66px;
    padding-top: 13px;
    padding-bottom: 46px;
    font-size: 12px;
    align-items: center;
    font-family: Arial;
    line-height: 18px;
}

.pix-qr-image {
    width: 150px;
}

.pix-qr-row {
    display: flex;
    justify-content: center;
    width: 317px;
    padding: 10px 0px 10px 0px;
}

.pix-qr-row-divider {
    width: 240px;
    border-bottom: 1px solid #C7C9CC;
}

.pix-qr-row-text {
    flex-grow: 1;
    color: #5F6369;
}

.pix-qr-row-number {
    flex-grow: 2;
    font-weight: bold;
}

.pix-qr-row-image {
    flex-grow: 1;
    cursor: pointer;
}

.pix-qr-copy-icon-container {
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

.pix-qr-copy-icon {
    width: 18px;
    width: 18px;
}

.pix-qr-button {
    color: #FFFFFF;
    background-color: #28A24E;
    box-shadow: 0px 4px 6px #E4E5E6;
    border-radius: 4px;
    padding: 8px 20px 8px 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    line-height: 20px;
    line-height: 20px;
    width: 220px;
    cursor: pointer;
}

.box-pix {
    border: 2px solid #074871;
    box-sizing: border-box;
    border-radius: 4px;
}

.pix-qr-button a {
    color: #FFFFFF;
}

.pix-qr-row.status {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.pix-qr-row.status > status {
    font-size: 12px;
    line-height: 18px;
}

.pix-qr-button-icon {
    height: 18px;
    width: 18px;
    color: #ffffff;
    margin-right: 10px;
}

.pix-qr-button.email {
    background: #074871;
}