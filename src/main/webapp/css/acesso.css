/*
Design by Free CSS Templates
http://www.freecsstemplates.org
Released for free under a Creative Commons Attribution 2.5 License
*/

body {
    padding: 0;
    margin: 0 0 0 0;
    background: #033976 url(../images/img01.jpg) repeat-x left top;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 13px;
    color: #000000;
}

h1, h2, h3 {
    margin-top: 0;
    color: #6E6C00;
}

h1 {
    font-size: 2em;
}

h2 {
    font-size: 1.6em;
}

h3 {
    font-size: 1em;
}

ul {
    list-style-image: url(../images/img07.gif);
}

a {
    text-decoration: none;
    color: #FFFFFF;
}

a:hover {
    border-bottom: none;
}

a img {
    border: none;
}

img.left {
    float: left;
    margin: 0 20px 0 0;
}

img.right {
    float: right;
    margin: 0 0 0 20px;
}

/*Conteudo*/
#conteudo {
    float: left;
    height: 78%;
    width: 100%;
    background: url(../images/img04NovaA.jpg) no-repeat left top;
}

#tela{
    height: 550px;
    width: 980px;
    margin: 10px auto 0 auto;
}

#foto{
    float: left;
    width: 240px;
    height: 320px;
    margin: 10px 20px 10px 20px;
    background-color: #4682B4;
}

.fotoAluno {
    width: 230px;
    height: 310px;
    margin: 5px 5px 5px 5px;
    border-width: 1px;
    border: blue;
}

.dadosAluno{
    float: right;
    width: 690px;
    height: 320px;
    margin: 10px 0 10px 0;
}

.tabelaAluno{
    width: 98%;
    margin: 0 10px 0 0px;
    font-size: 22px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
}

/* Header */

#header {
    width: 980px;
    height: 450px;
    margin: 0 auto;
    background: url(../images/img03.jpg) no-repeat right top;
}

#logo {
    float: left;
    width: 490px;
    height: 100px;
    margin: 0 auto;
    background: url(../images/img02.jpg) no-repeat left top;
}

#logo h1, #logo p {
    float: left;
    margin: 0;
    color: #033976;
}

#logo h1 {
    padding: 50px 0 0 0;
    text-transform: lowercase;
    font-weight: normal;
    font-size: 3em;
}

#logo p {
    text-transform: uppercase;
    padding: 72px 0 0 3px;
    font-size: 10px;
    color: #033976;
}

#logo a {
    border: none;
    text-decoration: none;
    color: #033976;
}

/* Menu */

#menu {
    float: right;
    width: 490px;
    margin: 0 auto;
}

#menu ul {
    margin: 0;
    padding: 40px 0 0 25px;
    list-style: none;
}

#menu li {
    display: inline;
}

#menu a {
    display: block;
    float: left;
    margin: 0 3px 0 0;
    padding: 10px 15px 10px 15px;
    border: none;
    text-decoration: none;
    text-transform: uppercase;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 11px;
    color: #569DF5;
}

#menu a:hover {
    margin: 0 3px 0 0;
    padding: 10px 15px 10px 15px;
    color: #B2B2B2;
}

#menu .current_page_item a {
    background: none;
    margin: 0 3px 0 0;
    padding: 10px 15px 10px 0px;
    color: #FFFFFF;
}

#menu .current_page_item input {
    border: none; 
    float: right;
    background: none;
    padding: 0 0 0 0;
    color: #FFFFFF;
    font-size: 32px;
}

/* Wrapper */

#wrapper {
}

/* Page */

#page {
    width: 980px;
    margin: 0 auto;
    padding: 0 0;
    background: url(../images/img05.jpg) repeat-y left top;
}

#pageBloq {
    width: 980px;
    margin: 0 auto;
    padding: 0 0;
    background: url(../images/img05Bloq.jpg) repeat-y left top;
}

#pageLib {
    width: 980px;
    margin: 0 auto;
    padding: 0 0;
    background: url(../images/img05Lib.jpg) repeat-y left top;
}

#page-bg {
    padding: 11px 24px;
}

/* Latest Post */

#latest-post {
    padding: 20px;
    border: 1px dashed #8D8D8D;
}

/* Content */

.content {
    float: left;
    width: 100%;
}

.post {
    padding: 0px 10px 0px 15px;
    height: 20%;
}

.post h1 {
    font-weight: normal;
    font-size: 30px;
}

.post h2 {
    font-size: 24px;
}

.post h4 {
    padding: 0px 10px 0px 15px;
    font-size: 20px;
}

.title {
    margin: 0;
    padding: 15px 15px 0px 15px;
    font-weight: normal;
}

.title a {
    border-bottom: none;
    font-weight: bold;
    font-size: 32px;
}

.title a:hover {
}

.byline {
    margin: 0 15px 20px 15px;
    text-transform: uppercase;
}

.entry {
    padding: 0px 15px;
}

.links {
    font-size: 11px;
}

.links a {
    display: block;
    border: none;
    color: #FFFFFF;
}

.links a:hover {
}

.links .more {
    float: right;
    text-transform: uppercase;
}

.links .comments {
    float: left;
    padding-left: 20px;
}
/* Sidebars */

#sidebar1 {
}

#sidebar2 {
}

.sidebar {
    float: left;
    width: 220px;
    padding-right: 20px;
}

.sidebar ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.sidebar li {
    padding: 0 0 20px 0;
}

.sidebar li ul {
    padding: 0px 15px;
}

.sidebar li li {
    padding: 8px 0 8px 0px;
    border-bottom: #2C5FAB solid 1px;
}


.sidebar li h2 {
    margin: 0 0 10px 0;
    padding: 7px 15px 5px 15px;
    background: url(../images/img07.jpg) no-repeat left top;
    font-size: 16px;
    font-weight: normal;
    color: #FFFFFF;
}

.sidebar p {
    padding: 0px 15px;
}

/* Search */

#searchform {
    margin: 0;
    padding: 20px 15px;
}

#searchform br {
    display: none;
}

#s {
    margin: 0;
    padding: 2px 2px;
    width: 165px;
    height: 18px;
    border: none;
    background: #FFFFFF;
    font-size: 10px;
    color: #000000;
}

#x {
    margin: 1px 0 0 0;
    padding: 2px 5px;
    height: 24px;
    border: none;
    background: #000000;
    text-decoration: none;
    text-transform: uppercase;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 10px;
    color: #FFFFFF;
}
/* Calendar */

#calendar_wrap {
    padding: 0;
    text-align: center;
}

#calendar_wrap table {
    width: 100%;
}

#calendar_wrap th {
}

#calendar_wrap td {
}

#calendar_wrap tfoot td {
    border: none;
}

#calendar_wrap tfoot td#prev {
    text-align: left;
    font-weight: bold;
    border: none;
}

#calendar_wrap tfoot td#prev a {
    border: none;
}

#calendar_wrap tfoot td#next {
    text-align: right;
    font-weight: bold;
    border: none;
}

#calendar_wrap tfoot td#next a {
    border: none;
}

/* Footer */

#footer {
    width: 980px;
    margin: 0 auto;
    padding: 5px 0;
    height: 50px;
    background: url(../images/img06.jpg) no-repeat left top;
}

#footer p {
    margin: 0;
    padding: 20px 0 0 0;
    text-align: center;
    text-transform: uppercase;
    font-size: 10px;
    color: #FFFFFF;
}

#footer a {
    color: #569DF5;
}