<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<h:panelGroup layout="block" id="paineldadosalunolateral" styleClass="menuLateral pull-left" >
    <!-- inicio box -->
    <div class="box-novo" >

        <div class="tudo step1">
        <a4j:outputPanel layout="block"  style="width: 100%; position: relative; z-index: 1" styleClass="containerfoto" id="panelFoto">
            <a4j:commandLink onclick="if (!confirm('Confirma exclusão da foto?')){return false;}"
                             oncomplete="location.reload();"
                             action="#{ClienteControle.removerFoto}"
                             reRender="panelFoto"
                             id="btnRemoverFoto"
                             title="Clique para remover a foto"
                             style="position: absolute;margin-top: 20px;"
                             styleClass="tooltipsterright linkAzul btnfotoOverlay">
                <i id="btnRemoverFotoIcon" class="fa-icon-trash texto-size-18"></i>
            </a4j:commandLink>
            <a4j:commandLink 
                actionListener="#{CapturaFotoControle.selecionarPessoa}"
                action="#{CapturaFotoControle.vazio}"
                id="btnAlterarFoto"
                title="Clique para alterar a foto"
                style="position: absolute;"
                oncomplete="setAttributesModalCapFoto(
                        '#{TelaClienteControle.key}',
                        '#{TelaClienteControle.cliente.pessoa.codigo}',
                        '#{TelaClienteControle.contextPath}');
                    Richfaces.showModalPanel('modalCapFotoHTML5');"
                styleClass="tooltipsterright linkAzul btnfotoOverlay">
                <i id="alterarFotoIcon" class="fa-icon-camera texto-size-18"></i>
            </a4j:commandLink>

            <a4j:jsFunction name="updateFoto" action="#{TelaClienteControle.recarregarFotoEAtualizarVersaoTreino}" reRender="panelFoto"/>

            <div class="col-text-align-center googleAnalytics">
                <h:graphicImage styleClass="imagemAluno" rendered="#{SuperControle.fotosNaNuvem}" id="imagemAluno"
                                style="width: 150px; height: 150px;"
                                url="#{TelaClienteControle.paintFotoDaNuvem}">
                </h:graphicImage>
                <a4j:mediaOutput rendered="#{!SuperControle.fotosNaNuvem}"
                                 element="img" id="imagem1"
                                 cacheable="false" session="false"
                                 styleClass="imagemAluno"
                                 createContent="#{TelaClienteControle.paintFoto}"
                                 value="#{ImagemData}" mimeType="image/jpeg">
                    <f:param value="#{SuperControle.timeStamp}" name="time"/>
                    <f:param value="#{TelaClienteControle.cliente.pessoa.codigo}" name="time"/>
                </a4j:mediaOutput>
            </div>
            <f:attribute name="pessoa" value="#{TelaClienteControle.cliente.pessoa.codigo}"/>

        </a4j:outputPanel>
            <h:outputText styleClass="fa-icon-heartbeat tooltipster texto-size-30"
                          title="Cliente Parq+ <br/>OBS: #{TelaClienteControle.mensagemParq}"
                          style="color: #ff3333; float: right; margin-top: -30px; position: relative;"
                          rendered="#{TelaClienteControle.cliente.parqPositivo}"/>
            <a4j:commandLink id="removerObjecao"
                             action="#{TelaClienteControle.prepararRemoverObjecao}"
                             rendered="#{TelaClienteControle.cliente.objecao.codigo > 0}"
                             oncomplete="Richfaces.showModalPanel('modalRemoverObjecaoDefinitiva')"
                             reRender="modalRemoverObjecaoDefinitiva">
                <h:graphicImage value="/faces/imagens/objecao-cliente.svg"
                                width="35"
                                styleClass="tooltipster texto-size-30"
                                title="<div class='esquerda' style='width: 250px'>
<div class='display:block;'><span class='text' style='font-weight: bold; color: red'>Clique para remover</span></div>
<div class='display:block;'><span class='text' style='font-weight: bold'>Objeção Definitva: </span> <span class='text'>#{TelaClienteControle.cliente.objecao.descricao} </span> </div>
</div>"
                                style="float: left;  margin-top: -33px; position: relative; z-index: 10;"/>
            </a4j:commandLink>
            <%--Div responsavel por mostrar os ícones na tela do cliente referente a usuário, facial e biometria encontra-se cadastrada--%>
            <div style="padding: 5px 15px 0 15px; width: auto; text-align: center">
                    <div style="">
                        <c:if test="${TelaClienteControle.cliente.usuarioMovelVO.ativo && TelaClienteControle.cliente.usuarioMovelVO.codigo > 0}">
                            <h:graphicImage value="/faces/imagens/icon_aluno_app.svg"
                                            width="30"
                                            styleClass="tooltipster texto-size-30"
                                            title="Usuário Móvel Cadastrado"/>
                        </c:if>
                        <c:if test="${!TelaClienteControle.cliente.usuarioMovelVO.ativo || TelaClienteControle.cliente.usuarioMovelVO.codigo == 0}">
                            <h:graphicImage value="/faces/imagens/icon_aluno_app_desabilitado.svg"
                                            width="30"
                                            styleClass="tooltipster texto-size-30"
                                            title="Usuário Móvel Não Cadastrado"/>
                        </c:if>
                        <c:if test="${TelaClienteControle.cliente.pessoa.assinaturaBiometriaFacialB}">
                            <h:graphicImage value="/faces/imagens/icon_aluno_face.svg"
                                            width="30"
                                            style="margin-left: 25px"
                                            styleClass="tooltipster texto-size-30"
                                            title="Amostra Facial Cadastrada"/>
                        </c:if>
                        <c:if test="${!TelaClienteControle.cliente.pessoa.assinaturaBiometriaFacialB}">
                            <h:graphicImage value="/faces/imagens/icon_aluno_face_desabilitado.svg"
                                            width="30"
                                            style="margin-left: 25px"
                                            styleClass="tooltipster texto-size-30"
                                            title="Amostra Facial Não Cadastrada"/>
                        </c:if>
                        <c:if test="${TelaClienteControle.cliente.pessoa.assinaturaBiometriaDigitalB}">
                            <h:graphicImage value="/faces/imagens/icon_aluno_digital.svg"
                                            width="30"
                                            style="margin-left: 25px"
                                            styleClass="tooltipster texto-size-30"
                                            title="Amostra Digital Cadastrada"/>
                        </c:if>
                        <c:if test="${!TelaClienteControle.cliente.pessoa.assinaturaBiometriaDigitalB}">
                            <h:graphicImage value="/faces/imagens/icon_aluno_digital_desabilitado.svg"
                                            width="30"
                                            style="margin-left: 25px"
                                            styleClass="tooltipster texto-size-30"
                                            title="Amostra Digital Não Cadastrada"/>
                        </c:if>
                    </div>
            </div>
        <div class="w100 mtop10 col-text-align-center" notranslate="notranslate">
            <h:outputText id="clienteNomeAuto"
                          styleClass="negrito cinzaEscuro pl5"
                          style="text-transform: capitalize;"
                          value="#{TelaClienteControle.cliente.pessoa.nomeMinusculo}"/>
        </div>
        <div class="w100 col-text-align-center">
            <h:outputText styleClass="textsmall negrito cinza pl5" value="MAT: "/>
            <h:outputText  id="matriculaAluno" styleClass="textsmall cinza pl5" value="#{TelaClienteControle.cliente.matricula}"/>
        </div>
            <c:if test="${TelaClienteControle.cliente.pessoa.idVindi != null and  TelaClienteControle.cliente.pessoa.idVindi > 0}">
                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="ID VINDI: "/>
                    <h:outputText styleClass="textsmall cinza pl5" value="#{TelaClienteControle.cliente.pessoa.idVindi}"/>
                </div>
            </c:if>

            <c:if test="${TelaClienteControle.cliente.sesc}">
                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="HAB SESC: "/>
                    <h:outputText styleClass="textsmall cinza pl5"
                                  value="#{TelaClienteControle.cliente.matriculaSesc}"/>
                </div>

                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="VALIDADE CARTÃO: "/>
                    <h:outputText styleClass="textsmall cinza pl5"
                                  value="#{TelaClienteControle.cliente.dataValidadeCarteirinha_Apresentar}"/>
                </div>

                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="RENDA: "/>
                    <h:outputText styleClass="textsmall cinza pl5"
                                  value="#{TelaClienteControle.cliente.rendaApresentar}"/>
                </div>
            </c:if>

            <c:if test="${TelaClienteControle.cliente.pessoa.idMaxiPago != null and  TelaClienteControle.cliente.pessoa.idMaxiPago > 0}">
                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="ID MaxiPago: "/>
                    <h:outputText styleClass="textsmall cinza pl5"
                                  value="#{TelaClienteControle.cliente.pessoa.idMaxiPago}"/>
                </div>
            </c:if>
            <c:if test="${(TelaClienteControle.usuarioLogado.username eq 'PACTOBR') || (TelaClienteControle.usuarioLogado.username eq 'admin')}">
                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="COD CLIENTE: "/>
                    <h:outputText id="codigoCliente" styleClass="textsmall cinza pl5" value="#{TelaClienteControle.cliente.codigo}"/>
                </div>
            </c:if>
            <c:if test="${(TelaClienteControle.usuarioLogado.username eq 'PACTOBR') || (TelaClienteControle.usuarioLogado.username eq 'admin')}">
                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value=" COD PESSOA: "/>
                    <h:outputText id="codigoPessoa" styleClass="textsmall cinza pl5" value="#{TelaClienteControle.cliente.pessoa.codigo}"/>
                </div>
            </c:if>

            <c:if test="${TelaClienteControle.nivelMGB != null and not empty TelaClienteControle.nivelMGB}">
                <div class="w100 col-text-align-center">
                    <h:outputText styleClass="textsmall negrito cinza pl5" value="NÍVEL MGB: "/>
                    <h:outputText id="nivelMGB" styleClass="textsmall cinza pl5" value="#{TelaClienteControle.nivelMGB}"/>
                </div>
            </c:if>

            <c:if test="${LoginControle.permissaoAcessoMenuVO.verificarClientesAtivos && (TelaClienteControle.cliente.apresentarDesverificarCliente || TelaClienteControle.cliente.apresentarVerificarCliente)}">
                <h:panelGroup id="pnlVerificacao" styleClass="w100 col-text-align-center mtop10">
                    <a4j:commandLink styleClass="tooltipster linkAzul pl5"
                                     id="verificarCliente"
                                     title="A <b>verificação de cliente</b> serve para dizer que o mesmo <b>não</b> apresenta erro de dados proveniente a importação dos dados"
                                     rendered="#{TelaClienteControle.cliente.apresentarVerificarCliente}"
                                     action="#{TelaClienteControle.verificarCliente}"
                                     oncomplete="carregarTooltipsterContrato();"
                                     reRender="form:pnlVerificacao">
                        <i class="fa-icon-ok-sign"></i> Verificar
                    </a4j:commandLink>

                    <h:outputText styleClass="textsmall cinza pl5"
                                  rendered="#{TelaClienteControle.cliente.apresentarDesverificarCliente}"
                                  value="Verificado em: #{TelaClienteControle.cliente.verificadoEm_Apresentar}"
                                  title="#{TelaClienteControle.cliente.verificadoEm_Hint}"/>
                    <br/>
                    <a4j:commandLink styleClass="linkAzul pl5"
                                     id="desverificarImp"
                                     rendered="#{TelaClienteControle.cliente.apresentarDesverificarCliente}"
                                     action="#{TelaClienteControle.desverificarCliente}"
                                     oncomplete="carregarTooltipsterContrato();"
                                     reRender="form:pnlVerificacao">
                        <i class="fa-icon-ok-sign"></i> Desverificar
                    </a4j:commandLink>
                </h:panelGroup>
            </c:if>

            <c:if test="${TelaClienteControle.cliente.pessoa.dataNasc != null}">
                <div class="blocoMeioaMeio mtop10">
                    <h:outputText styleClass="textsmall negrito cinza block pl5"
                                  value="IDADE"/>
                    <h:outputText id="cliIdade"
                                  styleClass="textsmall cinza block pl5"
                                  value="#{TelaClienteControle.idadeCliente} #{TelaClienteControle.idadeCliente > 1 ? 'anos' : 'ano'}"/>
                </div>
                <div class="blocoMeioaMeio mtop10">
                    <h:outputText styleClass="textsmall negrito cinza block" value="ANIVERSÁRIO"/>
                    <h:outputText styleClass="textsmall cinza block"
                                  value="#{TelaClienteControle.cliente.pessoa.dataNasc}">
                        <f:convertDateTime pattern="dd 'de' MMMM"/>
                    </h:outputText>
                </div>
            </c:if>

        <div class="w100 mtop10" style="text-align: right;">
                    <a4j:commandLink id="editDadosPessoais" styleClass="linkAzul"
                                     actionListener="#{ClienteControle.abrirAba}"
                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 1100, 622);">
                        <i id="editDadosPessoaisIcon" class="fa-icon-edit"></i> Dados Pessoais
                        <f:attribute name="aba" value="abadadosPessoais"/>
                    </a4j:commandLink>
        </div>

        <div class="w100 mtop10">
            <h:outputText styleClass="textsmall negrito cinza block pl5" value="SALDO DO ALUNO"
                          style="texto-size-18ize: 13px !important;"/>
        </div>
        </div>

        <h:panelGroup layout="block"
                      styleClass="w100 step2 tudo #{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'}">
            <a4j:commandLink styleClass="texto-size-16 pl5 #{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'}"
                             id="valorClienteCCRS" action="#{MovimentoContaCorrenteClienteControle.novo}"
                             oncomplete="abrirPopup('movimentoContaCorrenteClienteCons.jsp', 'MovimentoContaCorrenteCliente', 800, 595);">
                <h:outputText styleClass="#{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'}"
                              value="#{TelaClienteControle.cliente.empresa.moeda} ">
                </h:outputText>
                <h:outputText id="valorClienteCC"
                              styleClass="#{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'}"
                              value="#{TelaClienteControle.cliente.saldoContaCorrente}">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:outputText>
            </a4j:commandLink>

            <a4j:commandLink id="devolverSaldo" styleClass="tooltipster"
                             style="float: right; margin-right: 5px;"
                             rendered="#{TelaClienteControle.cliente.saldoContaCorrente != 0 }"
                             action="#{ClienteControle.pegarClienteTelaClienteComTratamento}"
                             title="#{TelaClienteControle.cliente.saldoContaCorrente > 0 ? 'Devolver Crédito' : 'Ajustar Débito' } Conta Corrente"
                             oncomplete="abrirPopup('ajustesSaldoContaCorrente.jsp', 'AjusteSaldoContaCorrenteCliente', 780, 595);">
                <h:outputText id="devolverSaldoIcon" styleClass="fa-icon-dollar texto-size-18 #{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'}"></h:outputText>
            </a4j:commandLink>

            <a4j:commandLink id="transferirSaldo" styleClass="#{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'} tooltipster"
                             style="float: right; margin-right: 5px;"
                             rendered="#{TelaClienteControle.cliente.saldoContaCorrente > 0 }"
                             title="Transferir #{TelaClienteControle.cliente.saldoContaCorrente > 0 ? 'Crédito' : 'Débito' } Conta Corrente"
                             action="#{MovimentoContaCorrenteClienteControle.novoTransferencia}"
                             oncomplete="abrirPopup('transferenciaContaClienteForm.jsp', 'TranfereciaContaCorrenteCliente', 780, 595);">
                <h:outputText id="transferirSaldoIcon" styleClass="fa-icon-exchange texto-size-18 #{TelaClienteControle.cliente.saldoContaCorrente < 0 ? 'red' : 'green'}"></h:outputText>
            </a4j:commandLink>

            <a4j:commandLink id="debitoContaCorrente"
                             style="float: right; margin-right: 5px;"
                             rendered="#{TelaClienteControle.cliente.saldoContaCorrente < 0}"
                             styleClass="tooltipster"
                             oncomplete="abrirPopup('gerarProdutoContaCorrenteForm.jsp', 'gerarProdutoContaCorrenteCliente', 780, 595);"
                             action="#{MovimentoContaCorrenteClienteControle.novoGerarParcelaDebito}"
                             reRender="form:panelMensagemInferior,form:panelMensagemSuperior, form:saldo"
                             title="Receber Débito da Conta Corrente">
                <h:outputText styleClass="fa-icon-money texto-size-18 red"></h:outputText>
            </a4j:commandLink>
        </h:panelGroup>
        <h:panelGroup rendered="#{TelaClienteControle.empresaLogado.trabalharComPontuacao}">
            <div class="w100 mtop10">
                <h:outputText styleClass="textsmall negrito cinza block pl5" value="PONTUAÇÃO ALUNO"
                              style="texto-size-18ize: 13px !important;"/>
            </div>
            <h:panelGroup layout="block"
                          style="font-weight:bold;background-color: #BFE6F6;padding: 5px 0;"
                          styleClass="w100 step2 tudo">
                <a4j:commandLink styleClass="texto-size-16 pl5" id="saldoPontoscliente"
                                 style="font-weight:bold;background-color: #BFE6F6;padding: 5px 0;"
                                 action="#{TelaClienteControle.irParaTelaHistoricoPontos}"
                                 oncomplete="abrirPopup('relatorio/historicoPontosResumo.jsp', 'Histotico Pontos Por Aluno', 1024, 700);">
                    <h:outputText id="valorCliente"
                                  style="font-weight:bold;background-color: #BFE6F6;padding: 5px 0;margin-left: 5px;"
                                  value="#{TelaClienteControle.valorPontuacao} #{TelaClienteControle.labelPontos}">
                    </h:outputText>
                </a4j:commandLink>
                
                <a4j:commandLink id="lancarBrinde" styleClass="tooltipster"
                             style="float: right; margin-right: 5px;"
                             title="Lançar Brinde"
                             action="#{TelaClienteControle.irParaTelaLancarBrinde}"
                             oncomplete="abrirPopup('lancarBrindeClienteCons.jsp', 'LancarBrindeCliente', 800, 595);">
                    <h:outputText id="lancarBrindeIcon" styleClass="fa-icon-plus-sign texto-size-18"></h:outputText>
                </a4j:commandLink>
                
                <a4j:commandLink id="ajustePontuacao" styleClass="tooltipster"
                             style="float: right; margin-right: 5px;"
                             title="Ajuste de Pontuação"
                             action="#{TelaClienteControle.irParaTelaAjustePontuacao}"
                             oncomplete="abrirPopup('ajustePontosCliente.jsp', 'AjustePontosCliente', 800, 595);">
                    <h:outputText id="ajustePontuacaoIcon" styleClass="fa-icon-tasks texto-size-18"></h:outputText>
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>

    </div>
    <h:panelGroup layout="block" styleClass="menuLateral tudo" style="width: 100%">
        <h:panelGroup layout="block" styleClass="grupoMenuLateral" style="padding-top: 2%;">

            <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{TelaClienteControle.apresentarTopo && TelaClienteControle.menuContrato}">
                <a4j:commandLink id="btnNovoContrato"
                                 rendered="#{!TelaClienteControle.novaNegociacaoPadrao}"
                                 styleClass=" linkFuncionalidade"
                                 action="#{ContratoControle.novoContratoViaTelaJsf}"
                                 reRender="panelIncludeMensagem,idpainelcontrato,idlistacontratos"
                                 oncomplete="#{ClienteControle.mensagemNotificar}#{ClienteControle.mostrarRichModalPanelListaContratosARenovarOuRematricular}">Novo Contrato
                </a4j:commandLink>

                <a4j:commandLink id="btnNovoContratoNovaNegociacao"
                                 styleClass=" linkFuncionalidade"
                                 rendered="#{TelaClienteControle.novaNegociacaoPadrao}"
                                 action="#{TelaClienteControle.abrirNovNegociacao}"
                                 oncomplete="#{TelaClienteControle.msgAlert}#{TelaClienteControle.mensagemNotificar}">Novo Contrato
                </a4j:commandLink>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                              title="Clique e saiba mais: Novo Contrato"
                              target="_blank">
                    <i id="wikiNovoContratoMenuIcon" class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral ">
                    <h:panelGroup layout="block" styleClass="botao-hidden">
                        <a4j:commandLink styleClass=" linkFuncionalidadeNovo" id="cadastroCliente"
                                       value="Cadastro">
                            <i id="cadastroClienteIcon" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                               style="font-family: FontAwesome;margin-left: 4px;"></i>
                        </a4j:commandLink>
                        <h:outputLink styleClass="linkWikiNovo"
                                      value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                      title="Clique e saiba mais: Cadastro"
                                      target="_blank">
                            <i id="wikiCadastroCliente" class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                        <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                            <c:if test="${!param.readOnly}">
                                <h:panelGroup layout="block" styleClass="dropdown-item">
                                    <h:panelGroup layout="block" styleClass="container-item">
                                        <a4j:commandLink id="btnNovoAtestado"
                                                         styleClass=""
                                                         actionListener="#{AtestadoControle.prepararAtestado}"
                                                         oncomplete="abrirPopup('atestadoAptidaoFisica.jsp', 'AptidaoFisica', 880, 650);">Novo Atestado
                                            <f:attribute name="cliente" value="#{TelaClienteControle.cliente}"/>
                                        </a4j:commandLink>
                                        <h:outputLink styleClass="linkWikiNovo"
                                                      value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                                      title="Clique e saiba mais: Atestado de Aptidão Física" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                            </c:if>
                            <h:panelGroup layout="block" styleClass="dropdown-item"
                                          rendered="#{LoginControle.validarTreinoLoginApp && TelaClienteControle.apresentarLinkAlunoTreino}">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <h:outputLink value="#{TelaClienteControle.urlAbrirClienteTreinoWeb}"
                                                  target="_blank"
                                                  id="urlIncluirAlunoAbrirParQTreinoWeb">
                                        <h:outputText value="Abrir Cliente Treino"/>
                                        <a4j:support event="onclick"
                                                     action="#{TelaClienteControle.notificarRecursoEmpresaTreinoZW}"/>
                                    </h:outputLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink styleClass=" " id="vincularCarteitaMenuCadastroCliente"
                                                     actionListener="#{ClienteControle.abrirAba}"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText id="vincularCarteitaMenuCadastroClienteText" value="Vincular a uma Carteira"/>
                                        <f:attribute name="aba" value="abaVinculo"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink actionListener="#{ClienteControle.abrirAba}" id="addClassificacaoMenuCadastroTelaCliente"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText id="addClassificacaoMenuCadastroTelaClienteText" value="Adicionar classificação"/>
                                        <f:attribute name="aba" value="abaGrupo"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink actionListener="#{ClienteControle.abrirAba}" id="associarGrupoMenuCadastroTelaCliente"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText id="associarGrupoMenuCadastroTelaClienteText" value="Associar a Grupos"/>
                                        <f:attribute name="aba" value="abaGrupo"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="editCobranca"
                                                     actionListener="#{ClienteControle.abrirAba}"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText id="editCobrancaText" value="Cobrança"/>
                                        <f:attribute name="aba" value="abaCobranca"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="editEndereco"
                                                     actionListener="#{ClienteControle.abrirAba}"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText id="editEnderecoText" value="Endereço"/>
                                        <f:attribute name="aba" value="abaEndereco"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="editEmail"
                                                     actionListener="#{ClienteControle.abrirAba}"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText value="E-mail"/>
                                        <f:attribute name="aba" value="abaEmail"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item"  rendered="#{ClienteControle.pessoaVO.pessoaFisica}">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="editFamiliares"
                                                     actionListener="#{ClienteControle.abrirAba}"
                                                     oncomplete="abrirPopup('clienteForm.jsp', 'ClienteCadastro', 880, 595); return false;">
                                        <h:outputText value="Familiares"/>
                                        <f:attribute name="aba" value="abaDependentes"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item"
                                          rendered="#{LoginControle.permissaoAcessoMenuVO.alterarMatricula}">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="editAlterarMatricula"
                                                     action="#{TelaClienteControle.prepararAlterarMatricula}"
                                                     oncomplete="Richfaces.showModalPanel('panelAlterarMatricula')"
                                                     reRender="panelAlterarMatricula">
                                        <h:outputText value="Alterar número de matrícula"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                            <h:panelGroup layout="block" styleClass="dropdown-item"
                                          rendered="#{LoginControle.permissaoAcessoMenuVO.adicionarAlterarSenhaAcesso}">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="editSenhaAcesso"

                                                     action="#{ClienteControle.abriTelaDefinirSenhaAcesso}"
                                                     oncomplete="abrirPopup('definirSenhaAcesso.jsp', 'Definir senha de acesso', 500, 300);">
                                        <h:outputText id="editSenhaAcessoText" value="Definir senha de acesso"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>

                            <h:panelGroup layout="block" styleClass="dropdown-item"
                                          rendered="#{LoginControle.permissaoAcessoMenuVO.permiteRegistrarAcessoAvulso}">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="abrirTelaRegistrarAcesso"
                                                     action="#{TelaClienteControle.abrirTelaRegistrarAcesso}"
                                                     oncomplete="abrirPopup('./relatorio/registrarAcessoAvulso.jsp', 'Registrar Acesso Avulso', 1024, 700);">
                                        <h:outputText id="abrirTelaRegistrarAcessoText" value="Registrar acesso manual"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>

                        </h:panelGroup>
                    </h:panelGroup>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral ">
                <h:panelGroup layout="block" styleClass="botao-hidden">
                    <a4j:commandLink styleClass=" linkFuncionalidadeNovo"
                                     id="relaciomento"
                                   value="Relacionamento">
                        <i id="relaciomentoText" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 4px;"></i>
                    </a4j:commandLink>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                  title="Clique e saiba mais: Relacionamento"
                                  target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink action="#{QuestionarioClienteControle.abrirTelaBV}"
                                                 id="verBoletinsVisita"
                                                 oncomplete="abrirPopup('questionarioClienteForm.jsp', 'Questionario', 780, 595); return false;">
                                    <h:outputText id="verBoletinsVisitaText" value="Ver Boletins de Visitas"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <c:if test="${!param.readOnly and TelaClienteControle.cliente.situacao eq 'VI'}">
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink action="#{TelaClienteControle.notificarRecursoClickTelaCliente}"
                                                     onclick="Richfaces.showModalPanel('panelNovaVisita')"
                                                     id="registrarVovaVisita">
                                        <h:outputText id="registrarVovaVisitaText" value="Registrar nova visita"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${!param.readOnly and LoginControle.permissaoAcessoMenuVO.modeloOrcamento}">
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink id="realizarOrcamento"
                                                    action="#{OrcamentoControle.preencherCliente}">
                                        <h:outputText id="realizarOrcamentoText" value="Realizar Orçamento"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>
                        <c:if test="${!param.readOnly}">
                            <h:panelGroup layout="block" styleClass="dropdown-item">
                                <h:panelGroup layout="block" styleClass="container-item">
                                    <a4j:commandLink action="#{MetaCRMControle.inicializarContatoAvulso}"
                                                     id="realizarContato"
                                                     oncomplete="abrirPopup('newRealizarContatoForm.jsp', 'Realizar Contato Avulso', 850, 700);">
                                        <h:outputText id="realizarContatoText" value="Realizar Contato"/>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGroup>
                        </c:if>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink action="#{TelaClienteControle.abrirHistoricoContato}" id="historicoContatoClienteMenu"
                                                 oncomplete="abrirPopup('historicoContatoClienteForm.jsp', 'HistoricoContatoCliente', 512, 530); return false;">
                                    <h:outputText id="historicoContatoClienteMenuText" value="Histórico de Contatos"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink action="#{IndicacaoControle.historicoIndicacao}" id="historicoIndicacaoMenu"
                                                 oncomplete="abrirPopup('historicoIndicacao.jsp', 'HistoricoIndicacao', 512, 530); return false;">
                                    <h:outputText id="historicoIndicacaoMenuText" value="Histórico de Indicações"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item"
                                      rendered="#{TelaClienteControle.clienteVO.empresa.usarParceiroFidelidade}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink action="#{TelaClienteControle.abrirHistoricoDotz}" id="historicoDotzMenu"
                                                 oncomplete="abrirPopup('relatorio/historicoPontosParceiroResumo.jsp', 'HistoricoDotz', 850, 595); return false;">
                                    <h:outputText id="historicoDotzMenuText" value="Histórico Dotz"/>
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink value="Lançar Mensagem para Catraca" id="lancarMansagemCatraca"
                                                 action="#{ClienteControle.editarClienteMensagemCatraca}"
                                                 reRender="formPanelMensagem"
                                                 oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink value="Lançar Aviso ao Consultor" id="lancarAvisoConsultor"
                                                 action="#{ClienteControle.editarClienteMensagemConsultor}"
                                                 reRender="formPanelMensagem"
                                                 oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink value="Lançar Aviso Médico" id="lancarAvisoMedico"
                                                 action="#{ClienteControle.editarClienteMensagemMedico}"
                                                 reRender="formPanelMensagem"
                                                 oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink value="Lançar Objetivo do Aluno" id="lancarObjetivoAluno"
                                                 action="#{ClienteControle.editarClienteMensagemObjetivo}"
                                                 reRender="formPanelMensagem"
                                                 oncomplete="Richfaces.showModalPanel('panelClienteMensagem')">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <%--<h:panelGroup layout="block" styleClass="dropdown-item">--%>
                            <%--<h:panelGroup layout="block" styleClass="container-item">--%>
                                <%--<a4j:commandLink value="Convite de Aula Experimental"--%>
                                                 <%--action="#{TelaClienteControle.abrirModalEnviarConvite}"--%>
                                                 <%--reRender="formPanelMensagem"--%>
                                                 <%--oncomplete="#{TelaClienteControle.msgAlert}">--%>
                                <%--</a4j:commandLink>--%>
                            <%--</h:panelGroup>--%>
                        <%--</h:panelGroup>--%>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral "
                          rendered="#{TelaClienteControle.apresentarTopo}">
                <h:panelGroup layout="block" styleClass="botao-hidden">
                    <a4j:commandLink styleClass=" linkFuncionalidadeNovo" id="vendaAvulsaTelaCliente"
                                   value="Venda Avulsa">
                        <i id="vendaAvulsaTelaCliente" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 4px;"></i>
                    </a4j:commandLink>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlBaseConhecimento}como-fazer-uma-venda-de-produto-para-aluno-venda-avulsa/"
                                  title="Clique e saiba mais: Venda Avulsa"
                                  target="_blank">
                        <i id="wikiVendaAvulsaTelaClienteIcon" class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="linkVendaProduto" value="Produto ou Serviço"
                                                 rendered="#{!TelaClienteControle.novaTelaVendaAvulsa}"
                                                 actionListener="#{VendaAvulsaControle.prepare}"
                                                 action="#{VendaAvulsaControle.novo}">
                                </a4j:commandLink>
                                <a4j:commandLink id="linkVendaProdutoNova" value="Produto ou Serviço"
                                                 rendered="#{TelaClienteControle.novaTelaVendaAvulsa}"
                                                 action="#{TelaClienteControle.abrirNovoVendaAvulsa}"
                                                 oncomplete="#{TelaClienteControle.msgAlert}">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item" rendered="#{LoginControle.permissaoAcessoMenuVO.aulaAvulsaDiaria}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="linkVendaDiaria" value="Diária"  oncomplete="#{AulaAvulsaDiariaControle.mensagemNotificar}"
                                                 action="#{AulaAvulsaDiariaControle.novoDiariaCliente}"/>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item" rendered="#{TelaClienteControle.apresentarDayUse}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="linkVendaDayUse" value="Day Use" oncomplete="#{AulaAvulsaDiariaControle.mensagemNotificar}"
                                                 action="#{AulaAvulsaDiariaControle.novoDayUseCliente}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup layout="block" styleClass="dropdown-item" rendered="#{TelaClienteControle.apresentarDiaPlus}">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="linkVendaDiaPlus" value="Dia Plus" oncomplete="#{AulaAvulsaDiariaControle.mensagemNotificar}"
                                                 action="#{AulaAvulsaDiariaControle.novoDiaPlusCliente}"/>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>


            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral "
                          rendered="#{TelaClienteControle.apresentarTopo}">
                <h:panelGroup layout="block" styleClass="botao-hidden">
                    <a4j:commandLink styleClass=" linkFuncionalidadeNovo" id="gymPassTelaCliente"
                                   value="GymPass">
                        <i id="gymPassTelaClienteIcon" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 4px;"></i>
                    </a4j:commandLink>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                  title="Clique e saiba mais: GymPass"
                                  target="_blank">
                        <i id="wikiGymPassTelaClienteIcon" class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="novoGymPass"
                                                 value="Cadastro"
                                                 reRender="modalGymPass"
                                                 action="#{TelaClienteControle.novoGymPass}"
                                                 oncomplete="Richfaces.showModalPanel('modalGymPass');">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="historicoGymPass"
                                                 value="Histórico"
                                                 reRender="modalGymPassHistorico"
                                                 oncomplete="Richfaces.showModalPanel('modalGymPassHistorico');"
                                                 action="#{TelaClienteControle.abrirHistoricoGymPass}">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral "
                          rendered="#{TelaClienteControle.apresentarTopo}">
                <h:panelGroup layout="block" styleClass="botao-hidden">
                    <a4j:commandLink styleClass=" linkFuncionalidadeNovo" id="totalPassTelaCliente"
                                   value="TotalPass">
                        <i id="totalPassTelaClienteIcon" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 4px;"></i>
                    </a4j:commandLink>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                  title="Clique e saiba mais: TotalPass"
                                  target="_blank">
                        <i id="wikiTotalPassTelaClienteIcon" class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="novoTotalPass"
                                                 value="Autorizar"
                                                 reRender="modalTotalPass"
                                                 action="#{TelaClienteControle.registrarTotalPass}"
                                                 oncomplete="Richfaces.showModalPanel('modalTotalPass');">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="historicoTotalPass" style="margin-right: 20px;"
                                                 oncomplete="abrirPopup('logTotalPassCliente.jsp', 'Acessos', 1024, 622);"
                                                 action="#{ClienteControle.pegarClienteTelaClienteComTratamento}"
                                                 value="Histórico" >
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral "
                          rendered="#{TelaClienteControle.apresentarTopo and (TelaClienteControle.convitesDireito gt 0 or TelaClienteControle.exibirConvites)}">
                <h:panelGroup layout="block" styleClass="botao-hidden">
                    <h:commandLink styleClass=" linkFuncionalidadeNovo" id="conviteTelaCliente"
                                   value="Convidados">
                        <i id="conviteTelaClienteIcon" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 4px;"></i>
                    </h:commandLink>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                  title="Clique e saiba mais: Convidado"
                                  target="_blank">
                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="novoConvidado"
                                                 value="Novo"
                                                 reRender="modalConvidado"
                                                 action="#{ConvidadoControle.novoConvidado}"
                                                 oncomplete="#{ConvidadoControle.msgAlert}">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="historicoConvidado"
                                                 value="Histórico"
                                                 reRender="modalConvidadoHistorico"
                                                 oncomplete="Richfaces.showModalPanel('modalConvidadoHistorico');"
                                                 action="#{ConvidadoControle.abrirHistoricoConvidado}">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="grupoMenuItem dropDownSimples menu-lateral "
                          rendered="#{ClienteControle.clienteVO.empresa.utilizarPactoPrint}">
                <h:panelGroup layout="block" styleClass="botao-hidden">
                    <a4j:commandLink styleClass=" linkFuncionalidadeNovo" id="carteirinhaTelaCliente"
                                     value="Carteirinha">
                        <i id="carteirinhaTelaClienteIcon" class=" fa-icon-caret-right linkFuncionalidadeNovo"
                           style="font-family: FontAwesome;margin-left: 4px;"></i>
                    </a4j:commandLink>
                    <h:outputLink styleClass="linkWikiNovo"
                                  value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                  title="Clique e saiba mais: Carteirinha"
                                  target="_blank">
                        <i id="wikiCarteirinhaTelaClienteIcon" class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                    <h:panelGroup layout="block" styleClass="dropdown-content h3em">
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup id="solicitarCarteirinha" layout="block" styleClass="container-item">
                                <a4j:commandLink id="novaCarteirinha"
                                                 value="Solicitar Carteirinha"
                                                 reRender="mdlMensagemGenerica"
                                                 action="#{ClienteControle.confirmarSocitarCarteirinha}"
                                                 oncomplete="#{ClienteControle.mensagemNotificar}">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                        <h:panelGroup layout="block" styleClass="dropdown-item">
                            <h:panelGroup layout="block" styleClass="container-item">
                                <a4j:commandLink id="historicoCarteirinha"
                                                 value="Histórico de Carteirinhas"
                                                 reRender="modalCarteirinhaHistorico"
                                                 oncomplete="Richfaces.showModalPanel('modalCarteirinhaHistorico');"
                                                 action="#{TelaClienteControle.abrirHistoricoCarteirinha}">
                                </a4j:commandLink>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuLateral"
                      rendered="#{TelaClienteControle.apresentarTopo && (LoginControle.permissaoAcessoMenuVO.caixaEmAberto  || LoginControle.usuarioLogado.administrador)}">
            <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
                <i class="fa-icon-money"></i> Financeiro
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="grupoMenuItem">
                <a4j:commandLink id="linkCaixaEmAberto" action="#{ClienteControle.caixa}"
                                 styleClass="rich-label-text-decor linkFuncionalidade">
                    <h:outputText id="linkCaixaEmAbertoText" value="Caixa em Aberto"/>
                </a4j:commandLink>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"
                              title="Clique e saiba mais: Caixa em Aberto" target="_blank">
                    <i id="wikiCaixaAbertoTelaCliente" class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="grupoMenuItem">
                <a4j:commandLink id="linkCompartilhar" oncomplete="#{TelaClienteControle.msgAlert}"
                                 styleClass="rich-label-text-decor linkFuncionalidade"
                                 action="#{TelaClienteControle.validarLinkPagamento}"
                                 reRender="panelIncludeMensagem,painelCompartilhar,panelSupeCompartilharPagamento">
                    <h:outputText id="linkCompartilhar_link" value="Link para Pagamento Online"/>
                </a4j:commandLink>
                <h:outputLink styleClass="linkWiki"
                              value="#{SuperControle.urlBaseConhecimento}como-faco-para-enviar-link-de-pagamento-aos-meus-alunos/"
                              title="Clique e saiba mais: Compartilhar link" target="_blank">
                    <i id="wikiLinkcompartilhar" class="fa-icon-question-sign" style="font-size: 18px"></i>
                </h:outputLink>
            </h:panelGroup>
            <c:if test="${LoginControle.permissaoAcessoMenuVO.apresentarLinkCadastrarCartaoOnline}">
                <h:panelGroup layout="block" styleClass="grupoMenuItem">
                    <a4j:commandLink id="linkCompartilharCard" oncomplete="#{TelaClienteControle.msgAlert}"
                                     styleClass="rich-label-text-decor linkFuncionalidade"
                                     action="#{TelaClienteControle.validarLinkCartao}"
                                     reRender="panelIncludeMensagem,painelCompartilhar,panelSupeCompartilharPagamento">
                        <h:outputText id="linkCompartilhar_linkCard" value="Link Cadastrar Cartão Online"/>
                    </a4j:commandLink>
                    <h:outputLink styleClass="linkWiki"
                                  value="#{SuperControle.urlBaseConhecimento}como-enviar-link-para-o-aluno-cadastrar-o-cartao-online/"
                                  title="Clique e saiba mais: Compartilhar link" target="_blank">
                        <i id="wikiLinkcompartilharCard" class="fa-icon-question-sign" style="font-size: 18px"></i>
                    </h:outputLink>
                </h:panelGroup>
            </c:if>
        </h:panelGroup>

<%--        <jsp:include page="include_familiares.jsp" flush="true"/>--%>

        <h:panelGroup layout="block" styleClass="grupoMenuLateral">
            <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
                <i class="fa-icon-list"></i> Log
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="grupoMenuItem">
                <a4j:commandLink id="linkVisualisarLog" styleClass="linkFuncionalidade"
                                 action="#{ClienteControle.realizarConsultaLogObjetoSelecionadoPessoa}"
                                 oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);">
                    <h:outputText id="linkVisualisarLogText" value="Visualizar LOG" />
                </a4j:commandLink>
            </h:panelGroup>
        </h:panelGroup>
    </h:panelGroup>
</h:panelGroup>

<rich:modalPanel id="panelNovaVisita"
                 styleClass="novaModal"
                 resizeable="false"
                 shadowOpacity="false"
                 width="300"
                 height="250">
    <f:facet name="header">
        <h:outputText value="Registro de nova visita" />
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade" id="linkPadraoFechar" />
            <rich:componentControl for="panelNovaVisita"
                                   attachTo="linkPadraoFechar"
                                   operation="hide"
                                   event="onclick" />
        </h:panelGroup>
    </f:facet>
    <div style="text-align: center;margin-top: 25px;">
        <span style="font: 14px Arial;color: #777;text-align: center;vertical-align: middle;">Deseja gravar um registro de nova visita?</span>
    </div>
    <div style="margin-top: 43px;">
        <h:panelGroup layout="block" styleClass="container-botoes">
            <a4j:commandLink action="#{ClienteControle.registrarNovaVisita}"
                             style="margin-right: 20px;"
                             oncomplete="#{ClienteControle.mensagemNotificar};Richfaces.hideModalPanel('panelNovaVisita')"
                             accesskey="2"
                             title="Salvar o novo registro de visita"
                             styleClass="botaoPrimario texto-font texto-size-16">
                <h:outputText value="Sim"/>
            </a4j:commandLink>
            <a4j:commandLink onclick="Richfaces.hideModalPanel('panelNovaVisita')"
                             title="Fechar e não registrar"
                             style="margin-left: 20px;"
                             accesskey="4"
                             styleClass="botaoSecundario texto-font texto-size-16" >
                <h:outputText value="Não"/>
            </a4j:commandLink>
        </h:panelGroup>
    </div>
</rich:modalPanel>
