<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 18/04/2016
  Time: 09:21
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@include file="includes/imports.jsp" %>
<script type="text/javascript">
    // Função que atribui foco para o próximo campo do formulário de forma dinâmica.
    function setFocusOnNextInput(e) {
        var idFormulario = e.id.split(":");
        idFormulario = idFormulario[0];
        var currentElement = e;
        var formElements = document.getElementById(idFormulario).elements;
        for (var i = 0; i < formElements.length; i++) {
            var element = formElements[i];
            if (element.type.toUpperCase() == "TEXT" || element.tagName.toUpperCase() == "SELECT") {
                if (currentElement.id == element.id) {
                    i++;
                    element = formElements[i];
                    element.focus();
                }
            }
        }
    }

    function noenter() {
        if (window.event && window.event.keyCode == 13) {
            window.event.preventDefault();
            return false;
        }
        return true;
    }

    function setFocusConfirmar() {
        document.getElementById("form:salvar1").focus();
    }

    function somenteNumeros(num) {
        var er = /[^0-9.]/;
        er.lastIndex = 0;
        var campo = num;
        if (er.test(campo.value)) {
            campo.value = "";
        }
    }

    function inputSomenteNumeros(event) {
        var charCode = event.which || event.keyCode;
        if ((charCode >= 48 && charCode <= 57) || charCode == 8 || charCode == 46 || (charCode >= 37 && charCode <= 40)) {
            return true;
        }
        return false;
    }

</script>
<style>
    .tamanhoInputPequeno {
        width: 135px;
    }

    .tamanhoInputMedio {
        width: 270px;
    }

    .tamanhoInputGrande {
        width: 430px;
    }
</style>
<%--<table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 15px;">--%>
<%--<tr>--%>
<%--<td>--%>
<%--<h:panelGroup layout="block" id="msgCima">--%>
<%--<h:panelGrid columns="3" width="100%" styleClass="tabMensagens bg" style="margin:0 20px 20px 20px;width: calc(100% - 40px);">--%>
<%--<h:panelGrid columns="1" width="100%">--%>
<%--<f:verbatim>--%>
<%--<h:outputText value=" "/>--%>
<%--</f:verbatim>--%>
<%--</h:panelGrid>--%>
<%--<h:outputText rendered="#{ClienteControle.erro}"--%>
<%--styleClass="fa-icon-warning-sign linkPadrao texto-size-25 texto-cor-vermelho"/>--%>
<%--<h:panelGrid columns="1" width="100%">--%>
<%--<h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{ClienteControle.mensagem}"/>--%>
<%--<h:outputText styleClass="texto-font texto-size-16 texto-cor-vermelho"--%>
<%--value="#{ClienteControle.mensagemDetalhada}"/>--%>
<%--<h:outputText styleClass="texto-font texto-size-16 texto-cor-vermelho"--%>
<%--value="#{ClienteControle.mensagemEmail}"/>--%>
<%--</h:panelGrid>--%>
<%--</h:panelGrid>--%>
<%--</h:panelGroup>--%>
<%--</td>--%>
<%--</tr>--%>
<%--</table>--%>
<table id="tabela1" width="100%" border="0" cellspacing="0" cellpadding="0" class="tabelaForm">
    <c:if test="${ClienteControle.configuracaoSistema.matriculaApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                MATRÍCULA
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.matriculaOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup style="margin-right:100px;">
                    <h:inputText disabled="true" id="matricula"
                                 style="font-family:Arial, Helvetica, sans-serif;color:#767676;"
                                 size="6"
                                 styleClass="tamanhoInputPequeno"
                                 maxlength="10" value="#{ClienteControle.clienteVO.matricula}"/>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.matriculaPendente}"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.usuarioLogado.administrador}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                EMPRESA<h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                                     style="padding:4px;"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                        <h:selectOneMenu id="empresa" styleClass="inputTextClean "
                                         disabled="#{!ClienteControle.clienteVO.abilitarEmpresa}"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         value="#{ClienteControle.clienteVO.empresa.codigo}">
                            <f:selectItems value="#{ClienteControle.listaSelectItemEmpresa}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <a4j:commandLink id="atualizar_empresa" action="#{ClienteControle.montarListaSelectItemEmpresa}"
                                     style="margin-left: 5px"
                                     styleClass="fa-icon-refresh texto-cor-azul texto-size-16 linkPadrao"
                                     ajaxSingle="true" reRender="empresa"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>
    <c:if test="${LoginControle.apresentarLinkEstudio}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="CLIENTE SESSÃO" rendered="#{LoginControle.apresentarLinkEstudio}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup rendered="#{LoginControle.apresentarLinkEstudio}" layout="block"
                              styleClass="chk-fa-container">
                    <h:selectBooleanCheckbox id="iptTipoCadastro" value="#{ClienteControle.clienteSessao}"/>
                    <span/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>
    <c:if test="${ClienteControle.configuracaoSistema.nomeApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_nome_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.nomeOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="nomeCliente" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form tamanhoInputGrande"
                             size="55" maxlength="80" value="#{ClienteControle.pessoaVO.nome}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.nomePendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.nomeRegistroApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_nome_registro_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.nomeRegistroOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="nomeRegistroCliente" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form tamanhoInputGrande"
                             size="55" maxlength="80" value="#{ClienteControle.pessoaVO.nomeRegistro}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.nomeRegistroPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.dataNascApresentar && ClienteControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText styleClass="text-font texto-size-14 texto-bold texto-cor-cinza"
                                              style="display: inline-block;width: auto"
                                              value="#{msg_aplic.prt_Pessoa_dataNasc_MMDDYYY_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-14"
                                              rendered="#{ClienteControle.configuracaoSistema.dataNascOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup id="dataNascClienteMMDDYYYY">

                    <rich:calendar id="dataNascMMDDYYYY"
                                   value="#{ClienteControle.pessoaVO.dataNasc}"
                                   inputSize="10"
                                   inputClass="form tamanhoInputPequeno dataNasc"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   datePattern="MM/dd/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                        <a4j:support event="onchange"
                                     action="#{ClienteControle.validarIdade}"
                                     oncomplete="#{ClienteControle.erro ? ClienteControle.mensagemNotificar : ''}"
                                     status="false" ajaxSingle="true"/>
                        <a4j:support event="onfocus" status="false"
                                     oncomplete="this.setSelectionRange(0, this.value.length);" ajaxSingle="true"/>
                    </rich:calendar>
                    <rich:jQuery id="mskDataNascMMDDYYYY" selector=".dataNascMMDDYYYY" timing="onload"
                                 query="mask('99/99/9999')"/>
                </h:panelGroup>
                <h:message for="dataNascMMDDYYYY" styleClass="mensagemDetalhada"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.dataNascPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.dataNascApresentar && !ClienteControle.configuracaoSistema.utilizarFormatoMMDDYYYDtNascimento}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText styleClass="text-font texto-size-14 texto-bold texto-cor-cinza"
                                              style="display: inline-block;width: auto"
                                              value="#{msg_aplic.prt_Pessoa_dataNasc_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-14"
                                              rendered="#{ClienteControle.configuracaoSistema.dataNascOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup id="dataNascCliente">

                    <rich:calendar id="dataNasc"
                                   value="#{ClienteControle.pessoaVO.dataNasc}"
                                   inputSize="10"
                                   inputClass="form tamanhoInputPequeno dataNasc"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false">
                        <a4j:support event="onchange"
                                     action="#{ClienteControle.validarIdade}"
                                     oncomplete="#{ClienteControle.erro ? ClienteControle.mensagemNotificar : ''}"
                                     status="false" ajaxSingle="true"/>
                        <a4j:support event="onfocus" status="false"
                                     oncomplete="this.setSelectionRange(0, this.value.length);" ajaxSingle="true"/>
                    </rich:calendar>
                    <rich:jQuery id="mskDataNasc" selector=".dataNasc" timing="onload" query="mask('99/99/9999')"/>
                </h:panelGroup>
                <h:message for="dataNasc" styleClass="mensagemDetalhada"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.dataNascPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.categoriaApresentar}">
        <tr>
            <td colspan="2">
                <table style="width: 650px; table-layout: fixed;">
                    <tr>
                        <td style="width: 50%; vertical-align: top;">
                        <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                            <h:outputText value="CATEGORIA" />
                            <h:outputText
                                    styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                    style="padding:4px;"
                                    rendered="#{ClienteControle.configuracaoSistema.categoriaOb}" />
                        </span>
                            <h:panelGroup layout="block">
                                <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                                    <h:selectOneMenu
                                            id="categoria"
                                            onblur="blurinput(this);"
                                            onfocus="focusinput(this);"
                                            styleClass="inputTextClean"
                                            value="#{ClienteControle.clienteVO.categoria.codigo}">
                                        <f:selectItems value="#{ClienteControle.listaSelectItemCategoria}" />
                                    </h:selectOneMenu>
                                </h:panelGroup>
                                <a4j:commandLink
                                        id="atualizar_categoria"
                                        action="#{ClienteControle.montarListaSelectItemCategoria}"
                                        styleClass="fa-icon-refresh texto-size-16 texto-cor-azul linkPadrao padding-5"
                                        immediate="true"
                                        ajaxSingle="true"
                                        reRender="categoria" />
                                <a4j:commandLink id="consultaDadosCategoria"
                                                 action="#{ProfissaoControle.inicializarProfisaoControle}"
                                                 title="Cadastrar Categoria" reRender="formCategoria"
                                                 styleClass="fa-icon-plus-sign texto-size-16 texto-cor-azul linkPadrao padding-5"
                                                 oncomplete="Richfaces.showModalPanel('panelCategoria'), setFocus(formCategoria,'formCategoria:nome');"/>
                            </h:panelGroup>
                        </td>
                        <c:if test="${ClienteControle.configEmpresaSesiHabilitada}">
                            <td style="width: 50%; vertical-align: top;">
                                <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                    <h:outputText value="EMPRESA" />
                                </span>
                                <h:panelGroup layout="block">
                                    <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                                        <h:selectOneMenu
                                                id="selectEmpresaFornecedor"
                                                onblur="blurinput(this);"
                                                onfocus="focusinput(this);"
                                                styleClass="inputTextClean"
                                                value="#{ClienteControle.clienteVO.empresaFornecedor.codigo}">
                                            <f:selectItems value="#{ClienteControle.listaSelectItemEmpresaFornecedor}" />
                                        </h:selectOneMenu>
                                    </h:panelGroup>
                                    <a4j:commandLink
                                            id="atualizarEmpresaFornecedor"
                                            action="#{ClienteControle.montarListaSelectItemEmpresaFornecedor}"
                                            style="margin-left: 5px"
                                            styleClass="fa-icon-refresh texto-size-16 texto-cor-azul linkPadrao padding-5"
                                            ajaxSingle="true"
                                            reRender="empresa" />
                                </h:panelGroup>
                            </td>
                        </c:if>

                    </tr>
                </table>
            </td>
        </tr>
    </c:if>



    <c:if test="${ClienteControle.configuracaoSistema.nomeMaeApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_nomeMae_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.nomeMaeOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="nomeMae" onblur="blurinput(this);" styleClass="form tamanhoInputGrande"
                             onfocus="focusinput(this);"
                             size="50" maxlength="50" value="#{ClienteControle.pessoaVO.nomeMae}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.nomeMaePendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.cpfMaeApresentar && !ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]}  DA MÃE"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cpfMaeOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:inputText id="CPFMae" size="10" maxlength="14" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                 styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfMae}">
                        <a4j:support event="onchange" action="#{ClienteControle.validarCPFMae}" status="false"
                                     oncomplete="#{ClienteControle.msgAlert}" ajaxSingle="true"/>
                    </h:inputText>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.cpfMaePendente}"/>
                    <rich:spacer width="20"/>
                    <h:outputText value="(Digitar somente números)"
                                  styleClass="texto-font texto-cor-cinza texto-size-14"/>
                </h:panelGroup>

            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.cpfMaeApresentar && ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td height="27" align="left" valign="middle" width="250 " id="identificadorMaeIntenacionalClienteNovo">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]}  DA MÃE"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cpfMaeOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:inputText id="CPFMaeClienteNovoInternacional" size="10" maxlength="14" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfMae}">
                    </h:inputText>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.cpfMaePendente}"/>
                </h:panelGroup>

            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.rgMaeApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_RgMae_responsavel_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.rgMaeOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:inputText id="RGMaeNovoClienteInternacional" size="10" maxlength="20" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.rgMae}"/>

                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.rgMaePendente}"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.nomePaiApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_nomePai_responsavel_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.nomePaiOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="nomePai" onblur="blurinput(this);" styleClass="form tamanhoInputGrande"
                             onfocus="focusinput(this);"
                             size="50" maxlength="50" value="#{ClienteControle.pessoaVO.nomePai}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.nomePaiPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.cpfPaiApresentar && !ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]} DO PAI"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cpfPaiOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:inputText id="CPFPai" size="10" maxlength="14" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                 styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfPai}">
                        <a4j:support event="onchange" action="#{ClienteControle.validarCPFPai}" focus="CPFPai"
                                     status="false"
                                     oncomplete="#{ClienteControle.msgAlert}" ajaxSingle="true"/>
                    </h:inputText>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.cpfPaiPendente}"/>
                    <rich:spacer width="20"/>
                    <h:outputText value="(Digitar somente números)"
                                  styleClass="texto-font texto-cor-cinza texto-size-14"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.cpfPaiApresentar && ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]} DO PAI"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cpfPaiOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:inputText id="CPFPaiNovoClienteInternacional" size="10" maxlength="14" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfPai}"/>

                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.cpfPaiPendente}"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.rgPaiApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_RgPai_responsavel_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.rgPaiOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:inputText id="RGPaiNovoClienteInternacional" size="10" maxlength="20" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.rgPai}"/>

                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.rgPaiPendente}"/>
                </h:panelGroup>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.sexoApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_sexo_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.sexoOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup>
                    <h:selectOneRadio id="sexo" onblur="blurinput(this);" styleClass="inputTextClean"
                                      style="font-size: 16px !important;"
                                      onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.sexo}">
                        <f:selectItems value="#{ClienteControle.listaSelectItemSexoPessoa}"/>
                    </h:selectOneRadio>
                </h:panelGroup>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.sexoPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.generoApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="350">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_genero_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.generoOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                    <h:selectOneMenu id="genero" onblur="blurinput(this);" styleClass="inputTextClean"
                                     onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.genero}">
                        <f:selectItems value="#{ClienteControle.listaSelectItemGeneroPessoa}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.generoPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.profissaoApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_profissao_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.profissaoOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                    <h:selectOneMenu id="profissao" onblur="blurinput(this);" styleClass="inputTextClean"
                                     onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.profissao.codigo}">
                        <f:selectItems value="#{ClienteControle.listaSelectItemProfissao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <a4j:commandLink id="atualizar_profissao" action="#{ClienteControle.montarListaSelectItemProfissao}"
                                 style="margin-left: 5px"
                                 styleClass="fa-icon-refresh texto-size-16 texto-cor-azul linkPadrao padding-5"
                                 immediate="true" ajaxSingle="true" reRender="profissao"/>
                <a4j:commandLink id="consultaDadosProfissao" action="#{ProfissaoControle.inicializarProfisaoControle}"
                                 title="Cadastrar Profissão"
                                 reRender="formProfissao"
                                 styleClass="fa-icon-plus-sign texto-size-16 texto-cor-azul linkPadrao padding-5"
                                 oncomplete="Richfaces.showModalPanel('panelProfissao'), setFocus(formProfissao,'formProfissao:nomeProfissao');"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.profissaoPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.grauInstrucaoApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_grauInstrucao_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.grauInstrucaoOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                    <h:selectOneMenu id="grauIntrucao" onblur="blurinput(this);" onfocus="focusinput(this);"
                                     styleClass="inputTextClean"
                                     style="font-family:Arial, Helvetica, sans-serif;color:#767676 ;"
                                     value="#{ClienteControle.pessoaVO.grauInstrucao.codigo}">
                        <f:selectItems value="#{ClienteControle.listaSelectItemGrauInstrucao}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <a4j:commandLink id="atualizar_GrauDeInstrucao"
                                 action="#{ClienteControle.montarListaSelectItemGrauInstrucao}"
                                 styleClass="fa-icon-refresh texto-size-16 texto-cor-azul linkPadrao padding-5"
                                 immediate="true"
                                 ajaxSingle="true" reRender="grauIntrucao"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.grauInstrucaoPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.estadoCivilApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_estadoCivil_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-14"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.estadoCivilOb}"/>
                             </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup layout="block" styleClass="cb-container tamanhoInputPequeno">
                    <h:selectOneMenu id="estadoCivil" onblur="blurinput(this);" styleClass="inputTextClean"
                                     onfocus="focusinput(this);" value="#{ClienteControle.pessoaVO.estadoCivil}">
                        <f:selectItems value="#{ClienteControle.listaSelectItemEstadoCivilPessoa}"/>
                    </h:selectOneMenu>
                </h:panelGroup>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-14" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.estadoCivilPendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.cpfApresentar && !ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup id="cpfMsk">
                    <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                        <h:panelGroup id="cpfAluno"
                                      rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo == 0}">
                            <h:inputText id="cpf" size="10" maxlength="14" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cfp}">
                                <a4j:support event="onchange" action="#{ClienteControle.validarClienteSimples}"
                                             status="false"
                                             oncomplete="if (#{ClienteControle.apresentarModalClienteCpfIgual}) Richfaces.showModalPanel(modalValidacaoCPF);"
                                             reRender="panelExistePessoa, msgBaixo, msgCima,modalValidacaoCPF, pnlMsgClienteRestricaoTela2"
                                             ajaxSingle="true"/>
                            </h:inputText>
                            <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                          rendered="#{ClienteControle.configuracaoSistema.cpfPendente}"/>
                            <rich:spacer width="20"/>
                            <h:outputText value="(Digitar somente números)"
                                          styleClass="texto-font texto-cor-cinza texto-size-14"/>
                        </h:panelGroup>

                        <h:panelGroup id="pnlMsgClienteRestricaoTela2">
                            <h:panelGroup styleClass="col-md-11 msg-cliente-restricao"
                                          rendered="#{not empty ClienteControle.mensagemClienteRestricao}">
                                <i class="pct pct-alert-triangle" style="margin: 0px 10px 0px 0px"></i>
                                <h:outputText value="#{ClienteControle.mensagemClienteRestricao}"/>
                            </h:panelGroup>
                        </h:panelGroup>

                        <h:panelGroup id="cpfREsponsavel"
                                      rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo != 0}">
                            <h:inputText disabled="true" id="cpfRes" size="10" maxlength="14" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                         styleClass="form tamanhoInputPequeno"
                                         value="#{ClienteControle.clienteVO.pessoaResponsavel.cfp}"/>
                            <rich:spacer width="20"/>
                            <h:outputText value="#{ClienteControle.clienteVO.pessoaResponsavel.nome}"
                                          styleClass="rotuloCampos"
                                          style="font-weight: bold;"/>
                            <rich:spacer width="20"/>
                            <a4j:commandLink id="btnRemoverResp" action="#{ClienteControle.removerResponsavelCPF}"
                                             value="Remover Responsável"
                                             reRender="cpfMsk" title="remover Responsavel" accesskey="2"
                                             styleClass="botaoSecundario texto-font-16"/>
                        </h:panelGroup>
                    </c:if>

                    <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                        <h:panelGroup id="cpfAlunoIntern"
                                      rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo == 0}">
                            <h:inputText id="cpf" size="10" maxlength="15" onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cfp}"/>

                            <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                          rendered="#{ClienteControle.configuracaoSistema.cpfPendente}"/>
                            <rich:spacer width="20"/>
                            <h:outputText value="(Digitar somente números)"
                                          styleClass="texto-font texto-cor-cinza texto-size-14"/>
                        </h:panelGroup>

                        <h:panelGroup id="cpfREsponsavelIntern"
                                      rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo != 0}">
                            <h:inputText disabled="true" id="cpfResIntern" size="10" maxlength="15"
                                         onblur="blurinput(this);"
                                         onfocus="focusinput(this);"
                                         styleClass="form tamanhoInputPequeno"
                                         value="#{ClienteControle.clienteVO.pessoaResponsavel.cfp}"/>

                            <rich:spacer width="20"/>
                            <h:outputText value="#{ClienteControle.clienteVO.pessoaResponsavel.nome}"
                                          styleClass="rotuloCampos"
                                          style="font-weight: bold;"/>
                            <rich:spacer width="20"/>
                            <a4j:commandLink id="btnRemoverRespIntern" action="#{ClienteControle.removerResponsavelCPF}"
                                             value="Remover Responsável"
                                             reRender="cpfMsk" title="remover Responsavel" accesskey="2"
                                             styleClass="botaoSecundario texto-font-16"/>
                        </h:panelGroup>
                    </c:if>
                </h:panelGroup>
            </td>
        </tr>
        <tr>
            <td>
                <span style="display: inline-block;width: auto;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">RNE</span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="rneAluno" onblur="blurinput(this);" styleClass="form tamanhoInputMedio"
                             onfocus="focusinput(this);"
                             size="18" maxlength="32" value="#{ClienteControle.pessoaVO.rne}"/>
            </td>
        </tr>
        <tr>
            <td>
                <span style="display: inline-block;width: auto;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">PASSAPORTE</span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="passaporteAluno" onblur="blurinput(this);" styleClass="form tamanhoInputMedio"
                             onfocus="focusinput(this);"
                             size="18" maxlength="32" value="#{ClienteControle.pessoaVO.passaporte}"/>
            </td>
        </tr>
    </c:if>
    <c:if test="${ClienteControle.configuracaoSistema.utilizarServicoSesiSC and ClienteControle.configuracaoSistema.apresentarCnpjSesi}">
        <tr>
            <td>
                <span style="display: inline-block;width: auto;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">CNPJ Sesi Indústria</span>
                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                              style="padding:4px;"
                              rendered="#{!ClienteControle.configuracaoSistema.pendenteCnpjSesi and ClienteControle.configuracaoSistema.objCnpjSesi}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                              rendered="#{ClienteControle.configuracaoSistema.pendenteCnpjSesi}"/>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="cnpjSesi" onblur="blurinput(this);" styleClass="form tamanhoInputMedio"
                             onfocus="focusinput(this);"
                             onkeypress="return mascara(this.form, this.id, '99.999.999/9999-99', event);"
                             size="18" maxlength="32" value="#{ClienteControle.pessoaVO.cnpjSesi}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.cpfApresentar && ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[0]}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cfpOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:panelGroup id="cpfMskInternacionalClienteNovo">
                    <h:panelGroup id="cpfAlunoInternacionalClienteNovo"
                                  rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo == 0}">
                        <h:inputText id="cpfInternacional" size="10" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cfp}">
                        </h:inputText>

                        <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                      rendered="#{ClienteControle.configuracaoSistema.cpfPendente}"/>
                        <rich:spacer width="20"/>
                    </h:panelGroup>

                    <h:panelGroup id="cpfREsponsavel"
                                  rendered="#{ClienteControle.clienteVO.pessoaResponsavel.codigo != 0}">
                        <h:inputText disabled="true" id="cpfRes" size="10" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);"
                                     styleClass="form tamanhoInputPequeno"
                                     value="#{ClienteControle.clienteVO.pessoaResponsavel.cfp}">
                        </h:inputText>
                        <rich:spacer width="20"/>
                        <h:outputText value="#{ClienteControle.clienteVO.pessoaResponsavel.nome}"
                                      styleClass="rotuloCampos"
                                      style="font-weight: bold;"/>
                        <rich:spacer width="20"/>
                        <a4j:commandLink id="btnRemoverResp" action="#{ClienteControle.removerResponsavelCPF}"
                                         value="Remover Responsável"
                                         reRender="cpfMskInternacional" title="remover Responsavel" accesskey="2"
                                         styleClass="botaoSecundario texto-font-16"/>
                    </h:panelGroup>
                </h:panelGroup>
            </td>
        </tr>
        <tr>
            <td>
                <span style="display: inline-block;width: auto;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">RNE</span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="rneAluno" onblur="blurinput(this);" styleClass="form tamanhoInputMedio"
                             onfocus="focusinput(this);"
                             size="18" maxlength="32" value="#{ClienteControle.pessoaVO.rne}"/>
            </td>
        </tr>
        <tr>
            <td>
                <span style="display: inline-block;width: auto;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">PASSAPORTE</span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="passaporteAluno" onblur="blurinput(this);" styleClass="form tamanhoInputMedio"
                             onfocus="focusinput(this);"
                             size="18" maxlength="32" value="#{ClienteControle.pessoaVO.passaporte}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.rgApresentar && !ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td style="padding: 0px;">
                <table>
                    <tbody>
                    <tr>
                        <td height="27" align="left" valign="middle" width="120">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[1]}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.rgOb}"/>
                            </span>
                        </td>
                        <td height="27" align="left" valign="middle">
                            <span style="display: inline-block;width: auto;"
                                  class="text-font texto-size-14 texto-bold texto-cor-cinza">ORGÃO EXPEDIDOR</span>
                        </td>
                        <td height="27" align="left" valign="middle">
                            <span style="display: inline-block;width: auto;"
                                  class="text-font texto-size-14 texto-bold texto-cor-cinza">UF</span>
                        </td>
                        <td>

                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle">
                            <h:inputText id="rg" size="5" onblur="blurinput(this);"
                                         styleClass="form tamanhoInputPequeno" onfocus="focusinput(this);"
                                         maxlength="20" value="#{ClienteControle.pessoaVO.rg}"/>


                            <h:outputText style="display: inline-block;width: auto;"
                                          styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                          rendered="#{ClienteControle.configuracaoSistema.rgPendente}"/>
                        </td>
                        <td>
                            <h:inputText id="rgOrgao" onblur="blurinput(this);" styleClass="form tamanhoInputPequeno"
                                         onfocus="focusinput(this);"
                                         size="5" maxlength="10" value="#{ClienteControle.pessoaVO.rgOrgao}"/>

                        </td>
                        <td>
                            <h:panelGroup layout="block" styleClass="cb-container tamanhoInputPequeno">
                                <h:selectOneMenu id="rgUf" onblur="blurinput(this);" onfocus="focusinput(this);"
                                                 styleClass="inputTextClean"
                                                 value="#{ClienteControle.pessoaVO.rgUf}">
                                    <f:selectItems value="#{ClienteControle.listaSelectItemRgUfPessoa}"/>
                                </h:selectOneMenu>
                            </h:panelGroup>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </c:if>


    <c:if test="${ClienteControle.configuracaoSistema.rgApresentar && ClienteControle.configuracaoSistema.usarSistemaInternacional}">
        <tr>
            <td style="padding: 0px;">
                <table>
                    <tbody>
                    <tr>
                        <td height="27" align="left" valign="middle" width="120">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{ClienteControle.displayIdentificadorFront[1]}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.rgOb}"/>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td align="left" valign="middle">
                            <h:inputText id="rg" size="5" onblur="blurinput(this);"
                                         styleClass="form tamanhoInputPequeno" onfocus="focusinput(this);"
                                         maxlength="20" value="#{ClienteControle.pessoaVO.rg}"/>

                            <h:outputText style="display: inline-block;width: auto;"
                                          styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                          rendered="#{ClienteControle.configuracaoSistema.rgPendente}"/>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </c:if>

    <tr>
        <td height="27" align="left" valign="middle" width="250">
                        <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                            <h:outputText value="#{msg_aplic.prt_Pessoa_dataCadastro_maiusculo}"/>
                        </span>
        </td>
    </tr>
    <tr>
        <td align="left" valign="middle">
            <h:inputText style="color:gray;" id="dataCadastro" value="#{ClienteControle.pessoaVO.dataCadastro}"
                         size="10"
                         readonly="true"
                         styleClass="form tamanhoInputPequeno">
                <f:convertDateTime pattern="dd/MM/yyyy" locale="#{SuperControle.localeDefault}"
                                   timeZone="#{SuperControle.timeZoneDefault}"/>
            </h:inputText>
        </td>
    </tr>
    <c:if test="${ClienteControle.configuracaoSistema.telefoneEmergenciaApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                        <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                            <h:outputText value="#{msg_aplic.prt_telefone_emergencia_maiusculo}"/>
                            <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                            style="padding:4px;"
                                            rendered="#{ClienteControle.configuracaoSistema.telefoneEmergenciaOb}"/>
                        </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="telefoneEmergencia" size="13"
                             maxlength="13"
                             onblur="blurinput(this);"
                             onkeypress="return mascara(this.form, this.id , '(99)999999999', event);"
                             onfocus="focusinput(this);"
                             styleClass="form tamanhoInputPequeno"
                             value="#{ClienteControle.pessoaVO.telefoneEmergencia}">
                    <a4j:support event="onchange"
                                 status="false"
                                 action="#{ClienteControle.verificarTelefoneResidencialExistePassivoIndicado}"
                                 oncomplete="if(validar_Telefone(this.id) != false) ;"
                                 reRender="msgBaixoInt, msgCimaInt"/>
                    <a4j:support event="onblur"
                                 action="#{ClienteControle.verificarTelefoneResidencialExistePassivoIndicado}"
                                 oncomplete="if(validar_Telefone(this.id) != false) ;"
                                 status="false"
                                 reRender="msgBaixoInt, msgCimaInt"/>
                </h:inputText>
            </td>
        </tr>
    </c:if>
</table>

<!-- fim item --> <!-- inicio item -->
<c:if test="${ClienteControle.exibirHeaderCadastroRespFinanceiro}">
    <div style="clear: both;margin:20px 0px" class="container-botoes bg-cinza">
        <h:outputText rendered="#{ClienteControle.configuracaoSistema.nomeRespFinanceiroApresentar}" styleClass="texto-font texto-size-14 texto-cor-cinza texto-cor-cinza texto-bold"
                      value="RESPONSÁVEL FINANCEIRO"/>
    </div>
    <div style="margin: 10px 56px; display: flex; align-items: center; gap: 5px;">
        <h:selectBooleanCheckbox value="#{ClienteControle.habilitarCamposResponsavelFinanceiro}">
            <a4j:support event="onclick" reRender="tabela_respFin" ajaxSingle="true"/>
        </h:selectBooleanCheckbox>
        <h:outputText value="Cadastrar responsável financeiro"
                      styleClass="text-font texto-size-14 texto-bold texto-cor-cinza" />
    </div>
</c:if>

<a4j:outputPanel id="tabela_respFin">
    <table style="width: auto;" border="0" cellspacing="0" cellpadding="0" class="tabelaForm">
        <!-- CAMPO NOME RESP FINANCEIRO --->
        <c:if test="${ClienteControle.configuracaoSistema.nomeRespFinanceiroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                        <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                            <h:outputText value="NOME RESP. FINANCEIRO"/>
                            <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                          style="padding:4px;"
                                          rendered="#{ClienteControle.configuracaoSistema.nomeRespFinanceiroOb}"/>
                            <h:outputText styleClass="fa-icon-double-asterisk texto-cor-cinza-3 texto-size-12"
                                          style="padding:4px;"
                                          rendered="#{ClienteControle.configuracaoSistema.nomeRespFinanceiroPendente}"/>
                        </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:inputText id="nomeRespFinanceiro_inputText" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputGrande" disabled="#{!ClienteControle.habilitarCamposResponsavelFinanceiro}"
                                 size="55" maxlength="80" value="#{ClienteControle.pessoaVO.nomeRespFinanceiro}"/>
                </td>
            </tr>
        </c:if>
        <!-- CAMPO CPF RESP FINANCEIRO --->
        <c:if test="${ClienteControle.configuracaoSistema.cpfRespFinanceiroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                        <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                            <h:outputText value="CPF RESP. FINANCEIRO"/>
                            <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                          style="padding:4px;"
                                          rendered="#{ClienteControle.configuracaoSistema.cpfRespFinanceiroOb}"/>
                        </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="left">
                        <h:inputText id="cpfRespFinanceiro_inputText" size="10" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" disabled="#{!ClienteControle.habilitarCamposResponsavelFinanceiro}"
                                     onkeypress="return mascara(this.form, this.id, '999.999.999-99', event);"
                                     styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.cpfRespFinanceiro}">
                        </h:inputText>
                        <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                      style="padding:4px;"
                                      rendered="#{ClienteControle.configuracaoSistema.cpfRespFinanceiroPendente}"/>
                        <rich:spacer width="20"/>
                        <h:outputText value="(Digitar somente números)"
                                      styleClass="texto-font texto-cor-cinza texto-size-14"/>
                </td>
            </tr>
        </c:if>
        <!-- CAMPO RG RESP FINANCEIRO --->
        <c:if test="${ClienteControle.configuracaoSistema.rgRespFinanceiroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                        <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                            <h:outputText value="RG RESP. FINANCEIRO"/>
                            <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                          style="padding:4px;"
                                          rendered="#{ClienteControle.configuracaoSistema.rgRespFinanceiroOb}"/>
                        </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="left">
                    <h:panelGroup>
                        <h:inputText id="rgRespFinanceiro_inputText" size="10" maxlength="14" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" onkeypress="return inputSomenteNumeros(event);"
                                     disabled="#{!ClienteControle.habilitarCamposResponsavelFinanceiro}"
                                     styleClass="form tamanhoInputPequeno" value="#{ClienteControle.pessoaVO.rgRespFinanceiro}">
                        </h:inputText>
                        <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                      style="padding:4px;"
                                      rendered="#{ClienteControle.configuracaoSistema.rgRespFinanceiroPendente}"/>
                        <rich:spacer width="20"/>
                        <h:outputText value="(Digitar somente números)"
                                      styleClass="texto-font texto-cor-cinza texto-size-14"/>
                    </h:panelGroup>
                </td>
            </tr>
        </c:if>
        <!-- EMAIL RG RESP FINANCEIRO --->
        <c:if test="${ClienteControle.configuracaoSistema.emailRespFinanceiroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="E-MAIL RESP. FINANCEIRO"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.emailRespFinanceiroOb}"/>
                                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.emailRespFinanceiroPendente}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:inputText id="emailRespFinanceiro_inputText" onblur="blurinput(this);" onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputGrande" maxlength="50" disabled="#{!ClienteControle.habilitarCamposResponsavelFinanceiro}"
                                 value="#{ClienteControle.pessoaVO.emailRespFinanceiro}">
                        <%--<a4j:support event="onblur" focus="email"--%>
                        <%--oncomplete="#{ClienteControle.erro ? '' : 'setFocusOnNextInput(this);'}"--%>
                        <%--actionListener="#{ClienteControle.verificarEmailExiste}"--%>
                        <%--action="#{ClienteControle.verificarEmailExiste}" reRender="msgBaixo, msgCima"/>--%>
                    </h:inputText>

                </td>
            </tr>
        </c:if>

    </table>
</a4j:outputPanel>

<div style="clear: both;margin: 20px 0px;" class="container-botoes bg-cinza">
    <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-cor-cinza texto-bold" value="CONTATO"/>
</div>
<table id="tabela2" style="width: auto;" border="0" cellspacing="0" cellpadding="0" class="tabelaForm">
    <c:if test="${ClienteControle.configuracaoSistema.telefoneApresentar}">
        <tr>
            <td height="27" align="left" valign="middle">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Telefone_residencial_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.telefoneOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="ddiTelefoneResidencial" size="2" maxlength="3" style="margin-right: 3px"
                                 value="#{ClienteControle.telefoneResidencialVO.ddi}" title="DDI"/>
                    <h:inputText id="residencialInt" size="13"
                                 maxlength="13"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.telefoneResidencialVO.numero}">
                        <a4j:support event="onchange"
                                     action="#{ClienteControle.verificarTelefoneResidencialExistePassivoIndicado}"
                                     status="false"
                                     reRender="msgBaixo, msgCima" focus="residencialInt"/>
                        <a4j:support event="onblur"
                                     status="false"
                                     action="#{ClienteControle.verificarTelefoneResidencialExistePassivoIndicado}"
                                     reRender="msgBaixo, msgCima" focus="residencialInt"/>
                    </h:inputText>
                </c:if>
                
                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="residencial" size="13"
                                 maxlength="13"
                                 onblur="blurinput(this);"
                                 onkeydown="mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.telefoneResidencialVO.numero}">
                        <a4j:support event="onchange"
                                     status="false"
                                     action="#{ClienteControle.verificarTelefoneResidencialExistePassivoIndicado}"
                                     oncomplete="if(validar_Telefone(this.id) != false) ;"
                                     reRender="msgBaixoInt, msgCimaInt"/>
                        <a4j:support event="onblur"
                                     action="#{ClienteControle.verificarTelefoneResidencialExistePassivoIndicado}"
                                     oncomplete="if(validar_Telefone(this.id) != false) ;"
                                     status="false"
                                     reRender="msgBaixoInt, msgCimaInt"/>
                    </h:inputText>
                </c:if>
                <span style="display: inline-block;width: auto;margin-left: 20px;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-14"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.descricaoTefOb && ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                                <h:outputText style="display: inline-block;width: auto;"
                                              value="#{msg_aplic.prt_Telefone_descricao_maiusculo}"
                                              rendered="#{ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                            </span>
                <h:inputText id="descricaoRes" size="27" maxlength="20"
                             rendered="#{ClienteControle.configuracaoSistema.descricaoTefObApresentar}"
                             styleClass="form tamanhoInputMedio"
                             value="#{ClienteControle.telefoneResidencialVO.descricao}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.telefonePendente}"/>
            </td>
        </tr>

        <tr>
            <td height="27" align="left" valign="middle">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Telefone_comercial_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.telefoneOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="ddiTelefoneComercial" size="2" maxlength="3" style="margin-right: 3px"
                                 value="#{ClienteControle.telefoneComercialVO.ddi}" title="DDI"/>
                    <h:inputText id="comercialInt" size="20"
                                 maxlength="13"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.telefoneComercialVO.numero}">

                        <a4j:support event="onchange"
                                     action="#{ClienteControle.verificarTelefoneComercialExistePassivoIndicado}"
                                     status="false"
                                     reRender="msgBaixo, msgCima" focus="comercialInt"/>
                        <a4j:support event="onblur"
                                     status="false"
                                     action="#{ClienteControle.verificarTelefoneComercialExistePassivoIndicado}"
                                     reRender="msgBaixo, msgCima" focus="comercialInt"/>
                    </h:inputText>
                </c:if>
                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="comercial" size="20"
                                 maxlength="13"
                                 onblur="blurinput(this);"
                                 onkeydown="mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.telefoneComercialVO.numero}">

                        <a4j:support event="onchange"
                                     status="false"
                                     action="#{ClienteControle.verificarTelefoneComercialExistePassivoIndicado}"
                                     oncomplete="if(validar_Telefone(this.id) != false) ;"
                                     reRender="msgBaixo, msgCima"/>
                        <a4j:support event="onblur"
                                     status="false"
                                     action="#{ClienteControle.verificarTelefoneComercialExistePassivoIndicado}"
                                     oncomplete="if(validar_Telefone(this.id) != false) ;"
                                     reRender="msgBaixo, msgCima"/>
                    </h:inputText>
                </c:if>
                <span style="display: inline-block;width: auto;margin-left: 20px;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                 <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                               style="padding:4px;"
                                               rendered="#{ClienteControle.configuracaoSistema.descricaoTefOb && ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                                <h:outputText style="display: inline-block;width: auto;"
                                              value="#{msg_aplic.prt_Telefone_descricao_maiusculo}"
                                              rendered="#{ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                            </span>
                <h:inputText id="descricaoCom" size="27" maxlength="20" styleClass="form tamanhoInputMedio"
                             value="#{ClienteControle.telefoneComercialVO.descricao}"
                             rendered="#{ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.telefonePendente}"/>
            </td>
        </tr>

        <tr>
            <td height="27" align="left" valign="middle">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Telefone_celular_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.telefoneOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="ddiCelularInt" size="2" maxlength="3" style="margin-right: 3px"
                                 value="#{ClienteControle.telefoneCelularVO.ddi}" title="DDI"/>
                    <h:inputText id="celularInt" size="13"
                                 maxlength="13"
                                 onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.telefoneCelularVO.numero}">
                        <a4j:support event="onchange"
                                     status="false"
                                     action="#{ClienteControle.verificarTelefoneCelularExistePassivoIndicado}"
                                     reRender="msgBaixo, msgCima" focus="celularInt"/>
                        <a4j:support event="onblur"
                                     action="#{ClienteControle.verificarTelefoneCelularExistePassivoIndicado}"
                                     status="false"
                                     reRender="msgBaixoInt, msgCimaInt" focus="celularInt"/>
                    </h:inputText>
                </c:if>
                <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                    <h:inputText id="celular" size="13"
                                 maxlength="13"
                                 onblur="blurinput(this);"
                                 onkeydown="mascara(this.form, this.id , '(99)999999999', event);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.telefoneCelularVO.numero}">
                        <a4j:support event="onchange"
                                     action="#{ClienteControle.verificarTelefoneCelularExistePassivoIndicado}"
                                     status="false"
                                     oncomplete="if(validar_Telefone(this.id) != false) ;"
                                     reRender="msgBaixo, msgCima"/>
                        <a4j:support event="onblur"
                                     action="#{ClienteControle.verificarTelefoneCelularExistePassivoIndicado}"
                                     status="false"
                                     oncomplete="if(validar_Telefone(this.id) != false) ;"
                                     reRender="msgBaixo, msgCima"/>
                    </h:inputText>
                </c:if>

                <span style="display: inline-block;width: auto;"
                      class="text-font texto-size-14 texto-bold texto-cor-cinza">
                               <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                             style="padding:4px;"
                                             rendered="#{ClienteControle.configuracaoSistema.descricaoTefOb && ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                                <h:outputText style="display: inline-block;width: auto;margin-left: 20px;"
                                              value="#{msg_aplic.prt_Telefone_descricao_maiusculo}"
                                              rendered="#{ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                            </span>
                <h:inputText id="descricaoCel" size="27" maxlength="20" styleClass="form tamanhoInputMedio"
                             value="#{ClienteControle.telefoneCelularVO.descricao}"
                             rendered="#{ClienteControle.configuracaoSistema.descricaoTefObApresentar}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.telefonePendente}"/>
            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.emailApresentar}">
        <tr>
            <td height="27" align="left" valign="middle">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_email_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.emailOb}"/>
                                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.emailPendente}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="email" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form tamanhoInputGrande" maxlength="50"
                             value="#{ClienteControle.emailVO.email}">
                    <%--<a4j:support event="onblur" focus="email"--%>
                    <%--oncomplete="#{ClienteControle.erro ? '' : 'setFocusOnNextInput(this);'}"--%>
                    <%--actionListener="#{ClienteControle.verificarEmailExiste}"--%>
                    <%--action="#{ClienteControle.verificarEmailExiste}" reRender="msgBaixo, msgCima"/>--%>
                </h:inputText>

            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.contatoEmergenciaApresentar}">
        <tr>
            <td height="27" align="left" valign="middle">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_contato_emergencia_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.contatoEmergenciaOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="contatoEmergencia" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form tamanhoInputGrande" maxlength="30"
                             value="#{ClienteControle.pessoaVO.contatoEmergencia}">
                </h:inputText>

            </td>
        </tr>
    </c:if>

    <c:if test="${ClienteControle.configuracaoSistema.webPageApresentar}">
        <tr>
            <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_webPage_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.webPageOb}"/>
                            </span>
            </td>
        </tr>
        <tr>
            <td align="left" valign="middle">
                <h:inputText id="webPage" size="50" onblur="blurinput(this);" onfocus="focusinput(this);"
                             styleClass="form tamanhoInputGrande"
                             value="#{ClienteControle.pessoaVO.webPage}"/>
                <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12" style="padding:4px;"
                              rendered="#{ClienteControle.configuracaoSistema.webPagePendente}"/>
            </td>
        </tr>
    </c:if>
</table>
<!-- fim item --> <!-- inicio item -->
<div style="clear: both;margin:20px 0px" class="container-botoes bg-cinza">
    <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza texto-cor-cinza texto-bold"
                  value="ENDEREÇO RESIDENCIAL"/>
</div>

<h:panelGroup layout="block" id="containerEndereco">
    <table id="tabela3" width="100%" border="0" cellspacing="0" cellpadding="0" class="tabelaForm">
        <c:if test="${ClienteControle.configuracaoSistema.cepApresentar}">
            <c:if test="${ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                <tr>
                    <td height="27" align="left" valign="middle" width="250">
                    <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                        <h:outputText value="#{msg_aplic.prt_Endereco_cep_maiusculo}"/>
                        <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                      style="padding:4px;"
                                      rendered="#{ClienteControle.configuracaoSistema.cepOb}"/>
                    </span>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="middle">
                        <h:inputText id="CEP" onblur="blurinput(this);" styleClass="form tamanhoInputPequeno"
                                     onfocus="focusinput(this);"
                                     size="40" maxlength="10"
                                     value="#{ClienteControle.enderecoResidencialVO.cep}">
                        </h:inputText>
                    </td>
                </tr>
            </c:if>
            <c:if test="${!ClienteControle.configuracaoSistema.usarSistemaInternacional}">
                <tr>
                    <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Endereco_cep_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cepOb}"/>
                            </span>
                    </td>
                </tr>
                <tr>
                    <td align="left" valign="middle">
                        <a4j:jsFunction name="consultarCep"
                                        oncomplete="#{ClienteControle.erro ? '' : 'setFocusOnNextInput(this);'}"
                                        action="#{ClienteControle.consultarCEP}"
                                        reRender="CEP, clienteEndereco, clienteComplemento, clienteBairro, pais, estado, cidade,msgBaixo,msgCima"/>
                        <h:inputText id="CEP" onblur="blurinput(this);" styleClass="form tamanhoInputPequeno"
                                     onfocus="focusinput(this);"
                                     size="40" maxlength="10"
                                     onkeydown="noenter();mascara(this.form, this.id, '99.999-999', event);"
                                     onkeyup="if(this.value.length == 10){consultarCep();}"
                                     value="#{ClienteControle.enderecoResidencialVO.cep}">
                        </h:inputText>
                        <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                      style="padding:4px;"
                                      rendered="#{ClienteControle.configuracaoSistema.cepPendente}"/>
                        <rich:spacer width="5"/>
                        <h:outputText styleClass="texto-size-14 texto-cor-cinza" value="Não sabe seu cep?"/>
                        <a4j:commandLink id="consultaDadosCep" title="Consultar CEP" reRender="formConsultarCEP"
                                         value="Clique aqui!"
                                         oncomplete="Richfaces.showModalPanel('panelCEP');setFocus(formConsultarCEP,'formConsultarCEP:estadoCEP');"
                                         styleClass="texto-size-14 texto-cor-azul linkPadrao padding-5"/>
                    </td>
                </tr>
            </c:if>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.enderecoApresentar || ClienteControle.configuracaoSistema.cepApresentar || ClienteControle.configuracaoSistema.enderecoComplementoApresentar || ClienteControle.configuracaoSistema.bairroApresentar || ClienteControle.configuracaoSistema.numeroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Endereco_enderecoOb_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.enderecoOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:inputText id="clienteEndereco" size="40" maxlength="40" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputGrande"
                                 value="#{ClienteControle.enderecoResidencialVO.endereco}"/>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.enderecoPendente}"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.numeroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Endereco_numeroOb_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.numeroOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:inputText size="10" id="numero" maxlength="10" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" styleClass="form tamanhoInputPequeno"
                                 value="#{ClienteControle.enderecoResidencialVO.numero}"/>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.numeroPendente}"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.enderecoComplementoApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Endereco_complementoOb_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.enderecoComplementoOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:inputText size="40" id="clienteComplemento" onblur="blurinput(this);"
                                 onfocus="focusinput(this);" maxlength="40"
                                 styleClass="form tamanhoInputGrande"
                                 value="#{ClienteControle.enderecoResidencialVO.complemento}"/>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.enderecoComplementoPendente}"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.bairroApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Endereco_bairroOb_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.bairroOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:inputText id="clienteBairro" size="40" maxlength="35" onblur="blurinput(this);"
                                 onfocus="focusinput(this);"
                                 styleClass="form tamanhoInputGrande"
                                 value="#{ClienteControle.enderecoResidencialVO.bairro}"/>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.bairroPendente}"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.paisApresentar or ClienteControle.configuracaoSistema.estadoApresentar or ClienteControle.configuracaoSistema.cidadeApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_pais_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.paisOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:panelGroup layout="block" styleClass="cb-container tamanhoInputPequeno">
                        <h:selectOneMenu id="pais" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="inputTextClean" value="#{ClienteControle.pessoaVO.pais.codigo}">
                            <a4j:support event="onchange" ajaxSingle="true" reRender="estado,cidade" focus="pais"
                                         status="false"
                                         action="#{ClienteControle.montarListaSelectItemEstado}"/>
                            <f:selectItems value="#{ClienteControle.listaSelectItemPais}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.paisPendente}"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.estadoApresentar or ClienteControle.configuracaoSistema.cidadeApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_estado_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.estadoOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                        <h:selectOneMenu id="estado" onblur="blurinput(this);"
                                         styleClass="inputTextClean tamanhoInputMedio"
                                         onfocus="focusinput(this);"
                                         value="#{ClienteControle.pessoaVO.estadoVO.codigo}">
                            <a4j:support event="onchange" ajaxSingle="true" reRender="cidade"
                                         status="false"
                                         action="#{ClienteControle.montarListaSelectItemCidade}" focus="estado"/>
                            <f:selectItems value="#{ClienteControle.listaSelectItemEstado}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>
                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.estadoPendente}"/>
                </td>
            </tr>
        </c:if>

        <c:if test="${ClienteControle.configuracaoSistema.cidadeApresentar}">
            <tr>
                <td height="27" align="left" valign="middle" width="250">
                            <span class="text-font texto-size-14 texto-bold texto-cor-cinza">
                                <h:outputText value="#{msg_aplic.prt_Pessoa_cidade_maiusculo}"/>
                                <h:outputText styleClass="fa-icon-asterisk texto-cor-cinza-3 texto-size-12"
                                              style="padding:4px;"
                                              rendered="#{ClienteControle.configuracaoSistema.cidadeOb}"/>
                            </span>
                </td>
            </tr>
            <tr>
                <td align="left" valign="middle">
                    <h:panelGroup layout="block" styleClass="cb-container tamanhoInputMedio">
                        <h:selectOneMenu id="cidade" onblur="blurinput(this);" onfocus="focusinput(this);"
                                         styleClass="inputTextClean"
                                         value="#{ClienteControle.pessoaVO.cidade.codigo}">
                            <f:selectItems value="#{ClienteControle.listaSelectItemCidade}"/>
                        </h:selectOneMenu>
                    </h:panelGroup>

                    <h:outputText styleClass="fa-icon-double-asterisk texto-cor-vermelho texto-size-12"
                                  style="padding:4px;"
                                  rendered="#{ClienteControle.configuracaoSistema.cidadePendente}"/>
                </td>
            </tr>
            <tr>
                <td>
                    <a4j:commandLink id="consultaDadosCidades" title="Cadastrar Cidade" reRender="formCidade"
                                     oncomplete="Richfaces.showModalPanel('panelCidade')"
                                     styleClass="linkPadrao " onblur="setFocusConfirmar();">
                        <h:outputText styleClass="texto-size-16 texto-cor-azul texto-font" value="Adicionar cidade  "/>
                        <h:outputText styleClass="fa-icon-plus-sign  texto-font texto-size-16 texto-cor-azul"/>
                    </a4j:commandLink>
                </td>
            </tr>
        </c:if>
    </table>
</h:panelGroup>
<%--<h:panelGroup layout="block" id="msgBaixo">--%>
<%--<h:panelGrid columns="3" width="100%" styleClass="tabMensagens bg" style="margin:0 20px 20px 20px;width: calc(100% - 40px);">--%>
<%--<h:panelGrid columns="1" width="100%">--%>
<%--<f:verbatim>--%>
<%--<h:outputText value=" "/>--%>
<%--</f:verbatim>--%>
<%--</h:panelGrid>--%>
<%--<h:outputText rendered="#{ClienteControle.erro}"--%>
<%--styleClass="fa-icon-warning-sign linkPadrao texto-size-25 texto-cor-vermelho"/>--%>
<%--<h:panelGrid columns="1" width="100%">--%>
<%--<h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{ClienteControle.mensagem}"/>--%>
<%--<h:outputText styleClass="texto-font texto-size-16 texto-cor-vermelho"--%>
<%--value="#{ClienteControle.mensagemDetalhada}"/>--%>
<%--<h:outputText styleClass="texto-font texto-size-16 texto-cor-vermelho"--%>
<%--value="#{ClienteControle.mensagemEmail}"/>--%>
<%--</h:panelGrid>--%>
<%--</h:panelGrid>--%>
<%--</h:panelGroup>--%>


