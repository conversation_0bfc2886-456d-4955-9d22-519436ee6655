<%-- 
    Created on : 08/05/2012, 16:56:25
    Author     : Waller
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>
<script type="text/javascript" src="script/jquery.maskedinput-1.2.2.js"></script>
<input type="hidden" name="modulo" value="${modulo}"/>
<script type="text/javascript" language="javascript">
    function buildZebraTable(tableId) {
        var table=document.getElementById(tableId);
        if(!table){return};

        // get all <tr> table elements
        var trows=table.getElementsByTagName('tr');
        //table.className = 'tablelistras textsmall';
        for(var j = 0; j < trows.length; j++){
            // assign CSS class to even and odd rows
            trows[j].className = j % 2 == 0 ? 'par' : '';
            if (j % 2 == 0){
                for (var k = 0; k < trows[j].childNodes.length; k ++){
                    trows[j].childNodes[k].className = 'par';
                }

            }
        }
    }

    function validar(){

        if (document.getElementById('formConsultarCEP:estadoCEP').value == ""
            && document.getElementById('formConsultarCEP:cidadeCEP').value == ""
            && document.getElementById('formConsultarCEP:bairroCEP').value == ""
            && document.getElementById('formConsultarCEP:logradouroCEP').value == ""){

            alert("Ao menos um parâmetro deve ser informado!");
            return false;
        }
        return true;
    }

    // run 'buildZebraTable()' function when web page is loaded
    window.onload=function zebra() {
        buildZebraTable('tabela1');
        buildZebraTable('tabela2');
        buildZebraTable('tabela3');
    }
</script>

<table width="100%" height="100%" border="0" align="left" cellpadding="0" cellspacing="0" class="text" style="margin-bottom: 20px;">
    <tr>
        <td width="19" height="50" align="left" valign="top"><img
                src="images/box_centro_top_left.gif" width="19" height="50"></td>
        <td align="left" valign="top"
            background="images/box_centro_top.gif"
            style="padding: 0px 0 0 0;">
            <table width="100%" border="0" align="left" cellpadding="0"
                   cellspacing="0" class="text">
                <tr>
                    <td align="left" valign="top" style="padding: 11px 0 0 0; width: 200px">
                                                        <span class="tituloboxcentro">
                                                            Cadastrar cliente
                                                        </span>
                        <h:outputLink styleClass="linkWiki"
                                      value="#{SuperControle.urlBaseConhecimento}como-cadastrar-um-cliente-visitante-aluno/"
                                      title="Clique e saiba mais: Informações do Cliente"
                                      target="_blank" >
                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                        </h:outputLink>
                    </td>

                </tr>
            </table>
        </td>
        <td width="19" align="left" valign="top"><img
                src="images/box_centro_top_right.gif" width="19" height="50"></td>
    </tr>


    <tr>
        <td align="left" valign="top" background="images/box_centro_left.gif"></td>
        <td align="left" valign="top" bgcolor="#ffffff" style="padding: 15px 15px 5px 15px;">
            <!-- inicio botões -->
            <h:panelGroup id="botesSuperior" style="padding-bottom: 10px;height: 50px;line-height: 50px;" layout="block">
                <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: right;display: inline-block;">
                    <a4j:commandLink id="salvar"
                                     value="Confirmar"
                                     styleClass="botaoPrimario texto-size-16 linkPadrao"
                                     action="#{ClienteControle.novoQuestionario}"
                                     rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                     title="#{msg.msg_gravar_dados}" accesskey="2">
                    </a4j:commandLink>
                </h:panelGroup>
                <rich:spacer width="5px"/>
                <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: left;display: inline-block;">
                    <a4j:commandLink id="cancelar"
                                     value="Cancelar"
                                     styleClass="botaoSecundario texto-size-16 linkPadrao"
                                     action="cancelar"
                                     rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                     title="Cancelar inclusão de um Cliente">
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
            <!-- fim botões -->
             <jsp:include page="include_form_novo_cliente.jsp" flush="true"/>
            <!-- inicio botões -->
            <h:panelGroup id="botesInferior" style="padding-bottom: 10px;height: 50px;line-height: 50px;" layout="block">
                <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: right;display: inline-block;">
                    <a4j:commandLink id="salvar1"
                                     value="Confirmar"
                                     styleClass="botaoPrimario texto-size-16 linkPadrao"
                                     action="#{ClienteControle.novoQuestionario}"
                                     rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                     title="#{msg.msg_gravar_dados}" accesskey="2">
                    </a4j:commandLink>
                </h:panelGroup>
                <rich:spacer width="5px"/>
                <h:panelGroup layout="block" style="width: 250px;height: 100%;text-align: left;display: inline-block;">
                    <a4j:commandLink id="cancelar1"
                                     value="Cancelar"
                                     styleClass="botaoSecundario texto-size-16 linkPadrao"
                                     action="cancelar"
                                     rendered="#{!ClienteControle.cadastroDadosObrigatoriosCliente}"
                                     title="Cancelar inclusão de um Cliente">
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGroup>
            <!-- fim botões -->
        </td>
        <td align="left" valign="top" background="images/box_centro_right.gif"></td>
    </tr>
    <tr>
        <td width="19" height="20" align="left" valign="top"><img
                src="images/box_centro_bottom_left.gif" width="19" height="20"></td>
        <td align="left" valign="top"
            background="images/box_centro_bottom.gif"></td>
        <td align="left" valign="top"><img
                src="images/box_centro_bottom_right.gif" width="19" height="20"></td>
    </tr>
</table>
