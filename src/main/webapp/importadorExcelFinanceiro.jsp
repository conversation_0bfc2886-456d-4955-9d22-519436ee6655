<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<script type="text/javascript" src="script/demonstrativoFinan.js"></script>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <html>

    <title><h:outputText
            value="Importador Excel"/></title>
    <c:set var="titulo" scope="session" value="Importador de lançamentos via excel"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <a4j:loadScript src="script/jquery.maskedinput-1.2.2.js"/>
    <h:form id="form">
        <a4j:keepAlive beanName="ExportadorListaControle"/>

        <head>
            <%@include file="pages/finan/includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript"
                    src="${contextoFinan}/script/telaInicial.js"></script>
        </head>

        <body style="background-color: #FFFFFF;">
        <hr style="border-color: #e6e6e6;"/>
        <h:panelGrid columns="1" width="100%">
            <h:panelGrid columns="2"
                         rowClasses="linhaPar,linhaImpar"
                         columnClasses="classEsquerda, classDireita" width="100%">

                <h:outputText value="Arquivo:" styleClass="tituloCampos"></h:outputText>
                <h:panelGroup>
                    <rich:fileUpload
                            fileUploadListener="#{ImportacaoFinanceiroExcelOldControle.uploadArquivoListener}"
                            immediateUpload="true" id="imagemModeloUpload"
                            acceptedTypes="xls,xlsx" allowFlash="false"
                            listHeight="58px"
                            cancelEntryControlLabel="Cancelar"
                            addControlLabel="Adicionar"
                            clearControlLabel="Remover"
                            clearAllControlLabel="Remover Todos"
                            doneLabel="Concluído"
                            sizeErrorLabel="Limite de tamanho atingido"
                            uploadControlLabel="Carregar"
                            transferErrorLabel="Erro na transferência"
                            stopControlLabel="Parar"
                            stopEntryControlLabel="Parar"
                            progressLabel="Carregando"
                            maxFilesQuantity="1">
                        <a4j:support event="onuploadcomplete" reRender="painelImportacao"/>
                    </rich:fileUpload>
                    <a4j:support event="onclear" action="#{ImportacaoFinanceiroExcelOldControle.removerArquivo}"
                                 reRender="painelImportacao, mensagens"></a4j:support>
                    <a4j:support event="onuploadcanceled" action="#{ImportacaoFinanceiroExcelOldControle.removerArquivo}"
                                 reRender="painelImportacao, mensagens"></a4j:support>
                </h:panelGroup>

            </h:panelGrid>

        </h:panelGrid>

        <h:panelGroup id="painelImportacao">
            <table>
                <tr>
                    <a4j:repeat var="i" value="#{ImportacaoFinanceiroExcelOldControle.idxs}">
                        <th><h:outputText value="#{ImportacaoFinanceiroExcelOldControle.cabecalho[i]}"></h:outputText></th>
                    </a4j:repeat>
                </tr>
                <a4j:repeat var="l" value="#{ImportacaoFinanceiroExcelOldControle.linhas}">
                    <tr>
                        <a4j:repeat var="ix" value="#{ImportacaoFinanceiroExcelOldControle.idxs}">
                            <td>
                                <h:outputText value="#{l[ix]}" rendered="#{l[ix] ne null}"></h:outputText>
                                <h:outputText value=" - " rendered="#{l[ix] eq null}"></h:outputText>
                            </td>
                        </a4j:repeat>
                    </tr>

                </a4j:repeat>

            </table>

        </h:panelGroup>


        </body>
    </h:form>

    </html>
    <%@include file="pages/finan/pessoaSimplificado.jsp" %>
</f:view>
