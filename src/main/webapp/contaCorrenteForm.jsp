<%@page contentType="text/html;charset=UTF-8" %> 
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_ContaCorrente_tituloForm}"/>
    </title>
    <%-- INICIO HEADER --%>
    <c:set var="titulo" scope="session" value="${msg_aplic.prt_ContaCorrente_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:Config._Financeiras:Conta_Corrente"/>
    <f:facet name="header">
        <jsp:include page="topoReduzido_material.jsp"/>
    </f:facet>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <hr style="border-color: #e6e6e6;"/>
            <h:commandLink action="#{ContaCorrenteControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContaCorrente_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ContaCorrenteControle.contaCorrenteVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContaCorrente_agencia}" />
                    <h:panelGroup>
                        <h:inputText  id="agencia" onkeypress="return Tecla(event);" size="10" maxlength="15" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ContaCorrenteControle.contaCorrenteVO.agencia}" />
                        <h:message for="agencia" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContaCorrente_agenciaDV}" />
                    <h:panelGroup>
                        <h:inputText  id="agenciaDV" onkeypress="return Tecla(event);" size="5" maxlength="5" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ContaCorrenteControle.contaCorrenteVO.agenciaDV}" />
                        <h:message for="agenciaDV" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContaCorrente_contaCorrente}" />
                    <h:panelGroup>
                        <h:inputText  id="contaCorrente" onkeypress="return Tecla(event);" size="15" maxlength="15" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ContaCorrenteControle.contaCorrenteVO.contaCorrente}" />
                        <h:message for="contaCorrente" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContaCorrente_contaCorrenteDV}" />
                    <h:panelGroup>
                        <h:inputText  id="contaCorrenteDV" onkeypress="return Tecla(event);" size="5" maxlength="5" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ContaCorrenteControle.contaCorrenteVO.contaCorrenteDV}" />
                        <h:message for="contaCorrenteDV" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_ContaCorrente_banco}" />
                    <h:panelGroup>
                        <h:selectOneMenu  id="banco" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{ContaCorrenteControle.contaCorrenteVO.banco.codigo}" >
                            <f:selectItems  value="#{ContaCorrenteControle.listaSelectItemBanco}" />
                            <a4j:support reRender="form" action="#{ContaCorrenteControle.verificarBanco}"
                                         event="onchange"/>
                        </h:selectOneMenu>
                        <a4j:commandButton id="atualizar_banco" action="#{ContaCorrenteControle.montarListaSelectItemBanco}" image="imagens/atualizar.png" ajaxSingle="true" reRender="form:banco"/>
                        <h:message for="banco" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    
                    <h:outputText rendered="#{ContaCorrenteControle.apresentarCodigoOperacao}" styleClass="tituloCampos" value="Codigo Operação:" />
                    <h:inputText  id="codigoOperacao" onkeypress="return Tecla(event);" 
                                  size="5" maxlength="5" styleClass="form" onblur="blurinput(this);"  
                                  onfocus="focusinput(this);" 
                                  rendered="#{ContaCorrenteControle.apresentarCodigoOperacao}" 
                                  value="#{ContaCorrenteControle.contaCorrenteVO.codigoOperacao}" />
                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton rendered="#{ContaCorrenteControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{ContaCorrenteControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{ContaCorrenteControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{ContaCorrenteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton id="novo" immediate="true" action="#{ContaCorrenteControle.novo}" value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1" styleClass="botoes nvoBt btSec"/>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="salvar" action="#{ContaCorrenteControle.gravar}" value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes nvoBt"/>
                            <h:outputText value="    "/>

                            <h:panelGroup id="grupoBtnExcluir" >
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{ContaCorrenteControle.msgAlert}" action="#{ContaCorrenteControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>

                            <%--<a4j:commandButton id="excluir" onclick="if(!confirm('Confirma exclusão dos dados?')){return true;}" action="#{ContaCorrenteControle.excluir}" value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>--%>
                            <h:outputText value="    "/>
                            <a4j:commandButton id="consultar" immediate="true" action="#{ContaCorrenteControle.inicializarConsultar}" value="#{msg_bt.btn_voltar_lista}" alt="#{msg.msg_consultar_dados}" accesskey="4" styleClass="botoes nvoBt btSec"/>
                            <a4j:commandLink action="#{ContaCorrenteControle.realizarConsultaLogObjetoSelecionado}"
                                               reRender="form"
                                               oncomplete="abrirPopup('visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);fireElementFromAnyParent('form:btnAtualizaTempo')"
                                               title="Visualizar Log" styleClass="botoes nvoBt btSec" style="display: inline-block;padding-bottom: 10px;">
                                <i class="fa-icon-list"></i>
                            </a4j:commandLink>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:agencia").focus();
</script>