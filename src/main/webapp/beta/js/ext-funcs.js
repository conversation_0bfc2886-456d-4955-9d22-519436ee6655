jQuery('#myTab a').click(function (e) {
    e.preventDefault();
    jQuery(this).tab('show');
});
/****************** DataTables ********************/
var tabelaAtual; // Tabela atualmente usada na tela !!!!!!!! Problema em telas com duas tabelas ou mais
var selCount = 0;
var cadastrosCheckados = [];
var pagCheckadas = [];
var itemPPagina = 10;
var allmarcado = false;
var clienteModal = "";

function fixTabsConteudo(tabCls) {
    var seletor = "div." + tabCls + ":not(:first-child)";
    jQuery(seletor).removeClass("active");
}
function onCompletaDTable(tabCls) {
    var classTabela = tabCls;
    var tabelaSeletor = "." + classTabela;
    jQuery(tabelaSeletor).dataTable();
}

function demoTables(tabelaSeletor) {
    tabelaAtual = jQuery(tabelaSeletor).dataTable({
        "sDom": "<'pure-g-r pure-u-1'<'pure-u-1-3 text-left'l><'pure-u-1-3 text-center'r><'pure-u-1-3 text-right'<'dataTables_filter searchbox-open'>>>t<'pure-g-r pure-u-1'<'pure-u-1-2 text-left'i><'pure-u-1-2'p>>"
    });

    var rExps = { a: '[a\xE0-\xE6]', e: '[e?\xE8-\xEB]', i: '[i\xEC-\xEF]', o: '[o\xF2-\xF6]', u: '[u\xF9-\xFC]', n: '[n\xF1]'};

// Recreating the removed label and search input
    var label = jQuery('<label />', {text: tabelaAtual.fnSettings().oLanguage.sSearch});

    jQuery('<input />', {
        'type': 'text'
    }).bind('keyup.DT', function () {
        filterValue = '';
        searchValue = jQuery(this).val().toLowerCase();

// We strip the accents first
        jQuery.each(rExps, function (key, value) {
            searchValue = searchValue.replace(new RegExp(value, 'g'), key);
        });

// Then we recreate a regex to match the accents into the datatable
        for (i = 0; i < searchValue.length; i++) {

            if (rExps[searchValue[i]] != undefined)
                filterValue += rExps[searchValue[i]];
            else
                filterValue += searchValue[i];
        }

        tabelaAtual.fnFilter(filterValue, null, true);
    }).appendTo(label);

    label.appendTo('.dataTables_filter');
}

function iniciarTabelaPaginacao(tabCls, url, eCustomSort, eAscDesc, eCustData, eNullRem, isFullWidth, isMultClick, isCheckBox, hasHidCol, hidCols, modalIdentifier, diffSort) {
    iniciarTabela(tabCls, url, eCustomSort, eAscDesc, eCustData, eNullRem, isFullWidth, isMultClick, isCheckBox, hasHidCol, hidCols, modalIdentifier,  diffSort, "POST", true);
}

function iniciarTabela(tabCls, url, eCustomSort, eAscDesc, eCustData, eNullRem, isFullWidth, isMultClick, isCheckBox, hasHidCol, hidCols, modalIdentifier, diffSort, aServerMethod, bServerSide) {
    var classTabela = tabCls;
    var tabelaSeletor = "." + classTabela;
    var dadosData = eCustData;
    var removerNulls = eNullRem;
    var objInit = {
        "bProcessing": true,
        "bDeferRender": false,
        "bAutoWidth": false,
        "bSortClasses": false,
        "sAjaxSource": url,
        "aLengthMenu": [
            [5, 10, 15, 25, 50, 100, 200, 500, -1],
            [5, 10, 15, 25, 50, 100, 200, 500, "Todos"]
        ],
        "iDisplayLength": 10,
        "fnInitComplete": function () {
            jQuery(".dataTables_filter label input").focus();
        },
        "fnCreatedRow": function (nRow, aData, iDataIndex) {
            if (typeof(isMultClick) != "undefined" && isMultClick) {
                if (typeof(isCheckBox) != "undefined" && isCheckBox) {
                    jQuery(nRow).children("td:first-child").html('<input type="checkbox" class="alumarcado" name="' + aData[1] + '" value="' + aData[1] + '">');
                    jQuery(nRow).children("td:first-child").on('click', function (e) {
                        if (e.target !== this) {
                            return;
                        }
                        if (jQuery(this).children("input").prop('checked')) {
                            jQuery(this).children(".alumarcado").prop("checked", false).trigger("change");
                        } else {
                            jQuery(this).children(".alumarcado").prop("checked", true).trigger("change");
                        }
                    });
                    jQuery(nRow).children("td:gt(0)").on('click', function (e) {
                        clienteModal = jQuery(nRow).children("td:nth-child(1)").children("input").val();
                        carregarSmartBoxModal();
                    });
                } else {
                    jQuery(nRow).on("click", function () {
                        // Do what you must!
                    });
                }
            } else {
                jQuery(nRow).on("click", function () {
                    jQuery("#chavePrimaria").val(aData[0]);
                    jsEditar();
                });
            }

            if (typeof(removerNulls) == "boolean" && removerNulls) {
                var filhosLinha = jQuery(nRow).children("td");
                jQuery.each(filhosLinha, function (key, value) {
                    var celula = value;
                    if (jQuery(celula).html() == "null") {
                        jQuery(celula).html("<span> - </span>");
                    } else if (jQuery(celula).html() == "") {
                        jQuery(celula).html("<span> - </span>");
                    } else if (jQuery(celula).html() == " ") {
                        jQuery(celula).html("<span> - </span>");
                    }
                });
            }
            if (typeof(dadosData) == "number") {
                var respIndex = dadosData;
                if (aData[dadosData] != "null" && aData[dadosData] != "") {
                    var dtaSplit = (aData[dadosData].split(" "));
                    var dataNasc;
                    if (dtaSplit.length < 2) {
                        dataNasc = new Date(aData[dadosData]);
                    } else {
                        dataNasc = new Date(dtaSplit[0]);
                        respIndex = dadosData - 1;
                    }
                    dataNasc = (parseInt(dataNasc.getUTCDate()) < 10 ? '0' : '') + dataNasc.getUTCDate() + "/" + (parseInt(dataNasc.getUTCMonth()) < 9 ? '0' : '') + (parseInt(dataNasc.getUTCMonth()) + 1) + "/" + dataNasc.getUTCFullYear();
                    var selecColuna = "td:nth-child(" + (respIndex + 1) + ")";
                    jQuery(nRow).children(selecColuna).html(dataNasc);
                }
            } else if (typeof(dadosData) == "object") {
                jQuery.each(dadosData, function (key, value) {
                    var dataNasc = new Date(aData[value]);
                    var selecColuna;
                    if (!isNaN(parseInt(dataNasc.getUTCDate()))) {
                        dataNasc = (parseInt(dataNasc.getUTCDate()) < 10 ? '0' : '') + dataNasc.getUTCDate() + "/" + (parseInt(dataNasc.getUTCMonth()) < 9 ? '0' : '') + (parseInt(dataNasc.getUTCMonth()) + 1) + "/" + dataNasc.getUTCFullYear();
                        selecColuna = "td:nth-child(" + (value + 1) + ")";
                        jQuery(nRow).children(selecColuna).html(dataNasc);
                    } else {
                        selecColuna = "td:nth-child(" + (value + 1) + ")";
                        jQuery(nRow).children(selecColuna).html("<span> - </span>");
                    }
                  
                });
            }
            jQuery(nRow).addClass("index-"+ aData[0]);
        },
        "fnDrawCallback": function (oSettings) {
            var strPagina = jQuery("#DataTables_Table_0_length label select").val();
            var itPPagina = parseInt(strPagina);
            if ((itemPPagina != itPPagina) && !allmarcado && (pagCheckadas.length > 0)) {
                resetSelecConta();
            }
            itemPPagina = itPPagina;
            var cuRow = this.$("tbody tr");
            if (allmarcado) {
                jQuery.each(cuRow, function (key, value) {
                    var elem = value;
                    var item = jQuery(elem).children("td:first-child").children("input");

                    if (cadastrosCheckados.indexOf(jQuery(item).val()) > -1) {
                        jQuery(elem).children("td:first-child").children("input").prop("checked", true);
                    } else {
                        jQuery(elem).children("td:first-child").children("input").prop("checked", false);
                    }
                });
            } else {
                jQuery(".alumarcado").prop("checked", false);
                var isPagChecked = (pagCheckadas.indexOf(this.fnPagingInfo().iPage) > -1);
                if (isPagChecked) {
                    jQuery(".selTodosPag").prop("checked", true);
                    jQuery.each(cuRow, function (key, value) {
                        var elem = value;
                        jQuery(elem).children("td:first-child").children("input").prop("checked", true);
                    });
                } else {
                    jQuery(".selTodosPag").prop("checked", false);
                    jQuery.each(cuRow, function (key, value) {
                        var elem = value;
                        var item = jQuery(elem).children("td:first-child").children("input");
                        if (cadastrosCheckados.indexOf(jQuery(item).val()) > -1) {
                            jQuery(elem).children("td:first-child").children("input").prop("checked", true);
                        } else {
                            jQuery(elem).children("td:first-child").children("input").prop("checked", false);
                        }
                    });
                }
            }
            jQuery('.'+tabCls).find('td').addClass('texto-cor-cinza texto-size-12-real alinhamentoEsquerda');
        }
    };

    if (aServerMethod != null) {
        objInit.aServerMethod = aServerMethod;
    }

    if (bServerSide != null) {
        objInit.bServerSide = bServerSide;
    }

    if (typeof(hasHidCol) != "undefined" && hasHidCol) {
        if (typeof (objInit["aoColumnDefs"]) == "undefined") {
            objInit["aoColumnDefs"] = [];
        }
        objInit["aoColumnDefs"].push({"bVisible": false, "aTargets": hidCols});
    }

    if (typeof(diffSort) != "undefined" && diffSort) {
        if (typeof (objInit["aoColumnDefs"]) == "undefined") {
            objInit["aoColumnDefs"] = [];
        }
        for (var zz = 0; zz < diffSort.length; zz++) {
            objInit["aoColumnDefs"].push({"aDataSort": [diffSort[zz][0]], "aTargets": [diffSort[zz][1]]});
        }
        console.log(objInit["aoColumnDefs"]);
    }

    if (typeof(isCheckBox) != "undefined" && isCheckBox) {
        if (typeof (objInit["aoColumnDefs"]) == "undefined") {
            objInit["aoColumnDefs"] = [];
        }
        objInit["aoColumnDefs"].push({"bSortable": false, "aTargets": [0]});

        jQuery(document).delegate(".alumarcado", "change", function () {
            var isso = jQuery(this).val();
            if (this.checked) {
                if (cadastrosCheckados.indexOf(isso) < 0) {
                    cadastrosCheckados.push(isso);
                }
            } else {
                if (cadastrosCheckados.indexOf(isso) >= 0) {
//                    cadastrosCheckados.splice(isso);
                    var remover = cadastrosCheckados.indexOf(isso);
                    cadastrosCheckados.splice(remover, 1)
                }
            }

            selCount = cadastrosCheckados.length;
            var stringConta = "" + selCount;
            if (selCount > 1) {
                stringConta += " alunos marcados"
            } else if (selCount == 1) {
                stringConta += " aluno marcado"
            } else {
                selCount = 0;
                jQuery(".selAll").html("Marcar todos");
                stringConta += " alunos marcados";
                jQuery(".selTodos").prop("checked", false);
                pagCheckadas = [];
                allmarcado = false;
                jQuery(".selTodosPag").prop("disabled", false);
            }
            jQuery(".aluConta").html(stringConta);
            reRenderSelecionados();
        });
    }

    if (typeof(eCustomSort) != "undefined" && typeof(eCustomSort) != "string") {
        if (typeof(eAscDesc) == "string") {
            objInit["aaSorting"] = [
                [eCustomSort, eAscDesc]
            ];
        } else {
            objInit["aaSorting"] = [
                [eCustomSort, "asc"]
            ];
        }
    }

    if (typeof(isFullWidth) != "undefined" && isFullWidth == true) {
        objInit["sDom"] = "<'pure-g-r pure-u-1'<'pure-u-1-1 text-center'r>>t<'pure-g-r pure-u-1'<'pure-u-1-3 paginas'p><'pure-u-1-3 mostrando'i><'pure-u-1-3 itensPaginas'l>>";
    }

    tabelaAtual = jQuery(tabelaSeletor).dataTable(objInit);
    if(typeof(isCheckBox) == "undefined" && !isCheckBox) {
        tabelaAtual.find('th').each(function (i) {
            var celH = jQuery(this);
            var texto = celH.text();
            celH.text('');
            celH.append('<span>' + texto + '</span>');

        });
    }
    
    var rExps = { a: '[a\xE0-\xE6]', e: '[e?\xE8-\xEB]', i: '[i\xEC-\xEF]', o: '[o\xF2-\xF6]', u: '[u\xF9-\xFC]', n: '[n\xF1]'};
    tabelaAtual.addClass('tabelaSimplesCustom');
    tabelaAtual.find('th').addClass('texto-cor-cinza texto-size-12-real texto-bold texto-upper');
// Recreating the removed label and search input
    var label = jQuery('<label />', {text: tabelaAtual.fnSettings().oLanguage.sSearch,'class':'pull-left texto-cor-azul','style':'margin-top:0px'});
    label.appendTo('.dataTables_filter');
    var divSearch =  jQuery('<div style="display: inline-block;margin-left:10px;width: 50%;text-align: left;float: left"></div>');
    divSearch.appendTo('.dataTables_filter');
    jQuery('<input />', {
        'type': 'text',
        'class': 'searchbox-input toggle'
    }).bind('keyup.DT', function () {
        filterValue = '';
        searchValue = jQuery(this).val().toLowerCase();

// We strip the accents first
        jQuery.each(rExps, function (key, value) {
            searchValue = searchValue.replace(new RegExp(value, 'g'), key);
        });

// Then we recreate a regex to match the accents into the datatable
       for (i = 0; i < searchValue.length; i++) {

           if (rExps[searchValue[i]] != undefined)
               filterValue += rExps[searchValue[i]];
           else
               filterValue += searchValue[i];
       }

       tabelaAtual.fnFilter(filterValue, null, true);
   }).addClass("filtroDT").appendTo(divSearch);
   jQuery('<i />', {
       'class': 'fa-icon-search searchbox-icon'
   }).appendTo(divSearch);
   jQuery("form#form").bind('keypress', function (e) {
       if (e.keyCode == 13) {
           return false; // prevent the button click from happening
       }
   });
   
   var submitIcon = jQuery('.searchbox-icon');
   var inputBox = jQuery('.searchbox-input');
   var searchBox = jQuery('.dataTables_filter');
   var isOpen = false;

   submitIcon.addClass('lupaIcone');
   inputBox.addClass('lupaInputBox');

   submitIcon.click( function(){
       if(isOpen == false){
           inputBox.removeClass('toggle');
           inputBox.focus();
           isOpen = true;
       } else {
           inputBox.addClass('toggle');
           inputBox.focusout();
           isOpen = false;
       }
   });
    inputBox.on('focusout',function () {
        inputBox.addClass('toggle');
        isOpen = false;
    });
   submitIcon.mouseup(function(){
       return false;
   });
   searchBox.mouseup(function(){
       return false;
   });
   jQuery(document).mouseup(function(){
       if(isOpen == true){
           jQuery('.searchbox-icon').css('display','inline-block');
           submitIcon.click();
       }
   });

}

function printaTela() {
    jQuery("#fitroConsulta").val(jQuery(".dataTables_filter input").val());
}

function selecTodos() {

        selCount = tabelaAtual.fnSettings().fnRecordsDisplay();
        var stringConta = "" + tabelaAtual.fnSettings().fnRecordsDisplay();
        stringConta += " alunos marcados";
        jQuery(".aluConta").html(stringConta);
        allmarcado = true;
        cadastrosCheckados = [];

        var data = tabelaAtual._('tr', {"filter": "applied"});
        data.each(function (a) {
            cadastrosCheckados.push(a[1]);
        });

        jQuery(".selAll").html("Desmarcar todos");
        jQuery(".selTodosPag").prop("disabled", true);
        tabelaAtual.fnDraw();

}

function retornaArrayCheckados() {
    return  "" + cadastrosCheckados;
}

function retornaArrayItensPagina() {
    return  "" + tabelaAtual.$("tbody tr").length;
}

function resetSelecConta() {
    selCount = 0;
    jQuery(".selAll").html("Marcar todos");
    var stringConta = 0;
    stringConta += " alunos marcados";
    jQuery(".aluConta").html(stringConta);
    jQuery(".selTodos").prop("checked", false);
    cadastrosCheckados = [];
    pagCheckadas = [];
    allmarcado = false;
    jQuery(".selTodosPag").prop("disabled", false);
    tabelaAtual.fnDraw();
}

function setSanListener() {
    jQuery(document).delegate(".headerSanfona", "click", function () {
        jQuery(this).toggleClass("headermarcado");
    });
    jQuery(document).delegate(".linhaClicka", "click", function () {
        jQuery(".linhaClicka").removeClass("linhaSelec");
        jQuery(this).addClass("linhaSelec");
    });
}

function selecTodosPagina() {
    var cuRow = tabelaAtual.$("tbody tr");
    var isPagChecked = (pagCheckadas.indexOf(tabelaAtual.fnPagingInfo().iPage) > -1);
    if (isPagChecked) {
        var remPagIndice = pagCheckadas.indexOf(tabelaAtual.fnPagingInfo().iPage);
        pagCheckadas.splice(remPagIndice, 1);
        jQuery.each(cuRow, function (key, value) {
            var elem = value;
            jQuery(elem).children("td:first-child").children("input").prop("checked", false);
            var valor = jQuery(elem).children("td:first-child").children("input").prop("value");
            if (cadastrosCheckados.indexOf(valor) > -1) {
                var indRemCodigo = cadastrosCheckados.indexOf(valor);
                cadastrosCheckados.splice(indRemCodigo, 1)
            }
        });
    } else {
        pagCheckadas.push(tabelaAtual.fnPagingInfo().iPage);
        jQuery.each(cuRow, function (key, value) {
            var elem = value;
            jQuery(elem).children("td:first-child").children("input").prop("checked", true);
            var valor = jQuery(elem).children("td:first-child").children("input").prop("value");
            if (cadastrosCheckados.indexOf(valor) < 0) {
                cadastrosCheckados.push(valor)
            }
        });
    }
    selCount = cadastrosCheckados.length;
    var stringConta = "" + selCount;
    if (selCount != 1) {
        stringConta += " alunos marcados"
    } else {
        stringConta += " aluno marcado"
    }
    jQuery(".aluConta").html(stringConta);
}

function retornaCodCliModal() {
    return clienteModal;
}


function retornaDadosDT(checkbox) {
    var valorFiltro = "''";
    if (jQuery(".filtroDT").val() != "") {
        valorFiltro = jQuery(".filtroDT").val();
    }
    var dados = tabelaAtual.fnSettings().aaSorting;
    var coluna = "th:nth-child(" + (dados[0][0] + 1) + ")";
    if (checkbox !== undefined){
        coluna = "th:nth-child(" + (dados[0][0]) + ")";
    }
    var nomeColuna = jQuery(".dataTable").children("thead").children("tr").children(coluna).html();
    return "[" + nomeColuna + "," + (dados[0][1]) + "," + valorFiltro + "]";
}
