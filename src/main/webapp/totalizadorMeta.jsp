<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-min.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/pure-ext.css" type="text/css" rel="stylesheet"/>
<link href="./beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes" />
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao" />
<f:loadBundle var="msg" basename="propriedades.Mensagens" />

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<body>
    <f:view>
        <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
        <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
            <f:facet name="header">
                <f:verbatim>
                    <jsp:include page="topoReduzidoCRM.jsp"/>
                </f:verbatim>
            </f:facet>
            <h:panelGrid columns="1" width="100%">
                <h:form id="formTotalizadorMeta">
                    <h:commandLink action="#{TotalizadorMetaControle.liberarBackingBeanMemoria}"
                                   id="idLiberarBackingBeanMemoria" style="display: none" />
                    <h:panelGrid id="panelGridForm" columns="1" width="100%">
                        <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%"
                                     style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;">
                            <h:outputText styleClass="tituloFormulario" value="Totalizador de Metas"></h:outputText>
                        </h:panelGrid>
                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">
					<h:outputText  rendered="#{TotalizadorMetaControle.usuarioLogado.administrador}"  styleClass="tituloCampos" value="#{msg_aplic.prt_Colaborador_empresa}" />
                    	<h:panelGroup rendered="#{TotalizadorMetaControle.usuarioLogado.administrador}">
                        <h:selectOneMenu  id="empresa" styleClass="form"  onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{TotalizadorMetaControle.codigoEmpresaConsulta}" >
                        	<f:selectItem itemValue="" itemLabel="-- Selecione --"/>
                            <f:selectItems  value="#{TotalizadorMetaControle.listaSelectItemEmpresa}" />
                        </h:selectOneMenu>
                    	</h:panelGroup>
                            <!-- Periodo -->
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FechamentoDia_periodode}" />
                            <a4j:outputPanel id="periodo">
                                <rich:calendar id="dataInicio" value="#{TotalizadorMetaControle.dataInicio}"
                                               inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                                <rich:spacer width="15"/>
                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FechamentoDia_ate}" />
                                <rich:spacer width="15"/>
                                <rich:calendar id="dataTermino" value="#{TotalizadorMetaControle.dataTermino}"
                                               inputSize="10" inputClass="form" oninputblur="blurinput(this);"
                                               oninputfocus="focusinput(this);" oninputchange="return validar_Data(this.id);"
                                               oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);"
                                               datePattern="dd/MM/yyyy" enableManualInput="true" zindex="2" showWeeksBar="false" />
                                <rich:spacer width="15"/>
                            </a4j:outputPanel>

                            <!-- Botoes do Periodo -->
                            <rich:spacer/>
                            <h:panelGroup>
                                <a4j:commandLink id="mesAnterior"
                                                   styleClass="pure-button pure-button-small pure-button-primary"
                                                   value="#{msg_bt.btn_mesAnterior}"
                                                   reRender="periodo"
                                                   action="#{TotalizadorMetaControle.mesAnterior}"
                                                   title="#{msg.msg_data_periodo_anterior}"/>
                                <rich:spacer width="20"/>
                                <a4j:commandLink id="mesAtual"
                                                   styleClass="pure-button pure-button-small pure-button-primary"
                                                   value="#{msg_bt.btn_mesAtual}"
                                                   reRender="periodo"
                                                   action="#{TotalizadorMetaControle.mesAtual}"
                                                   title="#{msg.msg_data_periodo_atual}"/>
                            </h:panelGroup>

                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_FechamentoDia_metaEmAberto}" />
                            <h:selectBooleanCheckbox id="metasAberto" value="#{TotalizadorMetaControle.metaEmAberto}" />
                        </h:panelGrid>
                        <h:panelGrid id="panelListaColaborador" columns="1" width="100%">
                            <rich:dataTable id="listaColaborador" width="100%" columnClasses="colunaEsquerda" rowClasses="linhaImpar"
                                            styleClass="semBorda" value="#{TotalizadorMetaControle.listaGrupoColaborador}" var="grupoColaborador">
                                <rich:column styleClass="semBorda">
                                    <f:facet name="header">
                                        <h:outputText styleClass="titulo3" value="Colaboradores Responsáveis: "/>
                                    </f:facet>
                                    <a4j:commandButton styleClass="botoes" title="Visualizar Participantes do Grupo"
                                                       action="#{TotalizadorMetaControle.selecionarGrupoColaboradorParticipante}"
                                                       image="./imagensCRM/botaoAdicionarGrupos.png" reRender="panelListaColaborador" />
                                    <rich:spacer width="5px;" />
                                    <a4j:commandLink styleClass="botoes" title="Visualizar Participantes do Grupo" reRender="panelListaColaborador"
                                                     action="#{TotalizadorMetaControle.selecionarGrupoColaboradorParticipante}">
                                        <h:outputText styleClass="tituloCamposAberturaMeta" value="#{grupoColaborador.descricao}" />
                                    </a4j:commandLink>
                                    <rich:dataGrid value="#{grupoColaborador.grupoColaboradorParticipanteVOs}" var="participante"
                                                   width="100%" columns="3" elements="#{grupoColaborador.totalParticipantes}" columnClasses="semBorda"
                                                   styleClass="semBorda" rendered="#{grupoColaborador.abrirSimpleTooglePanelPassivo}"
                                                   cellpadding="0" cellspacing="0">
                                        <h:panelGroup>
                                            <h:selectBooleanCheckbox value="#{participante.grupoColaboradorParticipanteEscolhido}">
                                                <a4j:support event="onclick" action="#{TotalizadorMetaControle.selecionarParticipante}" reRender="listaColaborador"/>
                                            </h:selectBooleanCheckbox>
                                            <rich:spacer width="5"/>
                                            <h:outputText styleClass="titulo3" value="#{participante.colaboradorParticipante.pessoa.primeiroNomeConcatenado}" />
                                        </h:panelGroup>
                                    </rich:dataGrid>
                                </rich:column>
                            </rich:dataTable>
                        </h:panelGrid>
                        <h:panelGrid id="panelGridMensagens" columns="1" width="100%" styleClass="tabMensagens">
                            <rich:spacer style="display:block" height="20"/>
                            <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                                <%--<a4j:commandButton id="consultar" styleClass="botoes" value="#{msg_bt.btn_consultar}"--%>
                                                   <%--reRender="formTotalizadorMeta:panelGridForm"--%>
                                                   <%--action="#{TotalizadorMetaControle.totalizarMeta}"--%>
                                                   <%--image="./imagensCRM/botaoConsultar.png" title="#{msg.msg_consultar_dados}"--%>
                                                   <%--accesskey="2" />--%>
                                <a4j:commandLink id="consultar" action="#{TotalizadorMetaControle.totalizarMeta}"
                                                 reRender="formTotalizadorMeta:panelGridForm"
                                                 styleClass="pure-button pure-button-small pure-button-primary"
                                                 title="#{msg.msg_consultar_dados}"
                                                 accesskey="2" >
                                    <i class="fa-icon-search"></i> &nbsp Buscar
                                </a4j:commandLink>
                            </h:panelGrid>
                            <h:panelGrid columns="3" width="100%">
                                <h:panelGrid columns="1" width="100%">
                                    <f:verbatim>
                                        <h:outputText value=" " />
                                    </f:verbatim>
                                </h:panelGrid>
                                <h:commandButton rendered="#{TotalizadorMetaControle.sucesso}" image="./imagensCRM/sucesso.png" />
                                <h:commandButton rendered="#{TotalizadorMetaControle.erro}" image="./imagensCRM/erro.png" />
                                <h:panelGrid columns="1" width="100%">
                                    <h:outputText styleClass="mensagem" value="#{TotalizadorMetaControle.mensagem}" />
                                    <h:outputText styleClass="mensagemDetalhada" value="#{TotalizadorMetaControle.mensagemDetalhada}" />
                                </h:panelGrid>
                            </h:panelGrid>
                        </h:panelGrid>
                    </h:panelGrid>
                </h:form>
            </h:panelGrid>
        </h:panelGrid>
    </f:view>
</body>