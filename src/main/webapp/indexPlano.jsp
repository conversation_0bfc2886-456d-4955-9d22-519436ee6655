<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>

<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">
<link href="css_pacto.css">

<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/time_1.3.js"></script>
</head>
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item2" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box form-flat">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Produtos, Planos e Turmas"
                                                      styleClass="container-header-titulo"/>
                                    </h:panelGroup>
                                </h:panelGroup>


                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
                                        <h:panelGrid columnClasses="w33,w33,w33" columns="3" width="100%"
                                                     cellpadding="0" cellspacing="0">
                                            <h:panelGrid columns="1" style="height:100%" width="100%" cellpadding="5"
                                                         cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoCategoriaProdutoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.categoriaProduto}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Categoria de Produto">
                                                            <f:attribute name="funcionalidade"
                                                                         value="CATEGORIA_PRODUTO"/>
                                                        </a4j:commandLink>
                                                    </div>
                                                    <h:outputText styleClass="tituloCampos"
                                                                  id="categoriaProdutoDescritivoText"
                                                                  rendered="#{!LoginControle.permissaoAcessoMenuVO.categoriaProduto}"
                                                                  value="Categoria de Produto"/>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Para manter a organização em sua Academia, obter relatórios mais precisos e facilitar a manutenção dos dados, sempre agrupe seus produtos em Categorias.
                                                                                      "/>
                                                </h:panelGroup>
                                                <h:outputText styleClass="text quote-text"
                                                              value="Categoria Alimentícios poderia conter os produtos Sanduiche Natural, Suco de Laranja. Categoria Serviços poderia conter os produtos Fisioterapia, Drenagem Linfática, Avaliação Física, Pilates, etc."/>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoPlanoDescritivo"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.plano}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         styleClass="tituloCampos">
                                                            <f:attribute name="funcionalidade" value="PLANO"/>
                                                            <h:outputText value="PLANO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="planoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.plano}"
                                                                      value="Plano"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Aqui está o principal cadastro de seu sistema, quanto mais completo estiver, mais fácil será lançar suas vendas no Sistema e você notará uma grande flexibilidade ao formar várias combinações para se chegar com exatidão, no plano desejado pelo Aluno, incluindo descontos, produtos, modalidades diferentes e convênios, tudo em uma mesma janela de fácil acesso e manuseio."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoTipoPlanoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.planoTipo}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         value="Tipo de Plano"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}">
                                                            <f:attribute name="funcionalidade" value="TIPO_PLANO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.planoTipo}"
                                                                      value="Tipo de plano"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Este cadastro contém configurações fiscais e financeiras
                                                        que são utilizadas ao gerar as parcelas do contrato.
                                                        Este cadastro é obrigatório durante o cadastro do plano."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="condicaoPagamentoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.condicaoPagamento}"
                                                                         value="Condição de Pagamento"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}">
                                                            <f:attribute name="funcionalidade"
                                                                         value="CONDICAO_DE_PAGAMENTO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="condicaoPagamentoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.condicaoPagamento}"
                                                                      value="Condição de Pagamento"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Cadastre aqui todas as possíveis condições de pagamento permitidas em sua academia. Quanto mais completo estiver o cadastro, mais fácil será o processo de recebimento de Plano.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Você poderia cadastrar as seguintes condições de pagamento: À vista, 3x Sem Entrada, Entrada + 2x, etc."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="turmaDescritivo" styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.turma}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         onclick="abrirPopup('turmaCons.jsp', 'Turma', 800, 595 );"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Turma">
                                                            <f:attribute name="funcionalidade" value="TURMA"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos" id="turmaDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.turma}"
                                                                      value="Turma"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Aqui serão cadastradas todas as turmas de sua Academia, ou mais especificamente, em quais horários a modalidade da Turma cadastrada é oferecida. Quanto mais completo for seu cadastro de turmas mais fácil será o processo de inclusão dos Alunos e a procura por vagas.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Cadastraremos Natação Infantil, onde o horário de Acesso se dá no período da manhã entre 07:00 e 11:00 horas de segunda a sexta-feira. Além disso, iremos definir o ambiente, anteriormente cadastrado, como Piscina Infantil."/>
                                                </h:panelGroup>
                                            </h:panelGrid>
                                            <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5"
                                                         cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoProdutoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.produto}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Produto">
                                                            <f:attribute name="funcionalidade" value="PRODUTO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="produtoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.produto}"
                                                                      value="Produto"/>
                                                    </div>

                                                    <rich:spacer height="25px"/>
                                                    <rich:spacer width="10px"/>
                                                    <h:outputText styleClass="text"
                                                                  value="Cadastre todos os produtos vendidos em sua Academia e defina o preço pago por cada um deles. Não se esqueça de definir uma Categoria, é muito importante. Defina nomes consistentes e lembre-se que estas informações irão para os Relatórios."/>

                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoDescontoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.desconto}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Desconto">
                                                            <f:attribute name="funcionalidade" value="DESCONTO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="descontoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.desconto}"
                                                                      value="Desconto"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Aqui você poderá cadastrar todos os tipos de descontos aplicados em sua Academia, desconto para planos, produtos, matrículas, rematrículas, etc. Além disso, você pode definir a forma de desconto como sendo por porcentagem ou valor fixo.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Você poderia cadastrar um desconto chamado Pagamento à Vista e definir o valor do mesmo como 5%. Cadastrar desconto Isenção de Matrícula e definir seu valor como 100%."/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="modalidadeDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.modalidade}"
                                                                         onclick="abrirPopup('modalidadeCons.jsp', 'Modalidade', 800, 595);"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Modalidade">
                                                            <f:attribute name="funcionalidade" value="MODALIDADE"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="modalidadeDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.modalidade}"
                                                                      value="Modalidade"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Aqui serão cadastradas todas as modalidades oferecidas em sua Academia. Será necessário atribuir qual o Valor mensal para aquela modalidade. É possível também, obrigar ou sugerir a venda de algum produto específico para os alunos que adquirirem a modalidade específica.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup rendered="#{LoginControle.apresentarLinkZW}">
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Poderíamos cadastrar a modalidade Musculação, obrigar a venda de Avaliação Física e sugerir a venda de Luvas."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="ambienteDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.ambiente}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         onclick="abrirPopup('ambienteCons.jsp', 'Ambiente', 800, 595);"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Ambiente">
                                                            <f:attribute name="funcionalidade" value="AMBIENTE"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="ambienteDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.ambiente}"
                                                                      value="Ambiente"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Se você trabalha com Modalidades, sabe que elas geralmente ocorrem em locais específicos. Cadastrando Ambientes será possível, definir exatamente onde será realizada a Aula. Defina os Ambientes de acordo com suas necessidades e estrutura de sua Academia.
                                                                                      "/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Ao cadastrar uma modalidade de Natação você poderia definir o Ambiente como Piscina Infantil, ou Piscina 02."/>
                                                </h:panelGroup>

                                            </h:panelGrid>
                                            <h:panelGrid rendered="#{LoginControle.apresentarLinkZW}" columns="1"
                                                         width="100%" style="height:100%" cellpadding="5"
                                                         cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="novoComposicaoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.composicao}"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Pacote">
                                                            <f:attribute name="funcionalidade" value="PACOTE"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="composicaoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.composicao}"
                                                                      value="Pacote"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Você pode agrupar várias modalidades em Pacotes, a fim de facilitar o processo de recebimento dos planos. Fazendo isso você pode aplicar descontos especiais para os Alunos que decidem adquirir mais de um tipo de atividade.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Você poderia criar um pacote com as modalidades Musculação e Natação e conceder desconto para os Alunos que adquirirem este pacote."/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="nivelTurmaDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.nivelTurma}"
                                                                         oncomplete="reRenderMenuLateral();"
                                                                         onclick="abrirPopup('nivelTurmaCons.jsp', 'NivelTurma', 800, 595);"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistorinoFuncionalidade}"
                                                                         value="Nível Turma">
                                                            <f:attribute name="funcionalidade" value="NIVEL_TURMA"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="nivelTurmaDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.nivelTurma}"
                                                                      value="Nível Turma"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="	Dividindo suas turmas em níveis pode facilitar o controle e ainda evitar que pessoas iniciantes façam atividade junto com outras mais experientes. Cadastre os níveis de suas turmas e evite problemas futuros.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:outputText styleClass="text quote-text"
                                                              value="Você poderia cadastrar Iniciante, Intermediário, Profissional, etc."/>
                                                <h:panelGroup>
                                                    <rich:spacer height="25px"/>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="horarioDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Horário">
                                                            <f:attribute name="funcionalidade" value="HORARIO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="horarioDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.horario}"
                                                                      value="Horário"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="	Podemos definir horários específicos de Acesso a Academia, horários promocionais, impedir acesso em determinados dias entre outras operações. Fazendo isso, você organizará quando cada Aluno poderá acessar seu estabelecimento.
                                                                                  "/>
                                                </h:panelGroup>
                                                <h:outputText styleClass="text quote-text"
                                                              value="Horário Livre que permitiria acesso em qualquer dia e horário, Horário Executivo, que poderia ser definido como horário promocional. Crie seus horários de acordo com sua necessidade e regras de negócio."/>


                                            </h:panelGrid>

                                            <h:panelGrid style="height:100%" columns="1" width="100%" cellpadding="5"
                                                         cellspacing="5">
                                                <h:panelGroup>
                                                    <div>
                                                        <h:outputText styleClass="fa-icon-pushpin textoCorAzul"/>
                                                        <rich:spacer width="5px"/>
                                                        <a4j:commandLink id="convenioDescontoDescritivo"
                                                                         styleClass="tituloCampos"
                                                                         oncomplete="#{FuncionalidadeControle.abrirPopUp}"
                                                                         rendered="#{LoginControle.permissaoAcessoMenuVO.convenioDesconto}"
                                                                         actionListener="#{MenuAcessoFacilControle.addHistoricoPreparaFuncionalidade}"
                                                                         value="Convênio de Desconto">
                                                            <f:attribute name="funcionalidade"
                                                                         value="CONVENIO_DE_DESCONTO"/>
                                                        </a4j:commandLink>
                                                        <h:outputText styleClass="tituloCampos"
                                                                      id="convenioDescontoDescritivoText"
                                                                      rendered="#{!LoginControle.permissaoAcessoMenuVO.convenioDesconto}"
                                                                      value="Convênio de Desconto"/>
                                                    </div>
                                                    <rich:spacer height="10px"/>
                                                    <h:outputText styleClass="text" value="Aqui poderão ser cadastrados todos os Convênios de Desconto oferecidos por sua Academia. Você poderá definir um valor diferente de desconto para cada duração de plano lançada no Cadastro e o valor do desconto poderá ser estipulado em percentual ou valores fixos. Além disso, você poderá escolher se o Aluno deste convênio irá ou não, pagar taxas de matrícula e rematrícula.
	                                                                                  "/>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="text quote-text"
                                                                  value="Poderíamos cadastrar um convênio para a Pacto Soluções Tecnológicas, onde planos Mensais não ganhariam desconto, planos Trimestrais teriam 3% de desconto, Semestrais, 7% de desconto e Anuais, teriam 10% de desconto."/>
                                                </h:panelGroup>


                                            </h:panelGrid>

                                        </h:panelGrid>
                                    </h:panelGrid>
                                </h:panelGroup>

                            </h:panelGroup>


                        </h:panelGroup>

                        <jsp:include page="include_box_menulateral.jsp">
                            <jsp:param name="menu" value="ADM-CADASTROS_PRODUTOS_PLANOS_TURMAS"/>
                        </jsp:include>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>

        </h:panelGroup>

        <!-- ----------------------------------------------------------------------------------------------------------------------------------- -->


    </h:form>
</f:view>

