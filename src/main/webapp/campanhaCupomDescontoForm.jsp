<%@page contentType="text/html;charset=UTF-8" %>
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
    <script type="text/javascript" language="javascript" src="script/vanilla-masker.min.js"></script>
</head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="${root}/css/telaCliente.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title><h:outputText value="Campanha Cupom Desconto"/></title>
    <c:set var="titulo" scope="session" value="Campanha Cupom Desconto"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-cadastrar-campanha-de-cupom-desconto/"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js"/>
        <h:form id="form">

            <style>
                .chk-fa-container input[type="checkbox"] + span {
                    margin-top: 4px;
                }

                .contratoSelecionado tbody tr {
                    background-color: #ffffff;
                }

                .texto-size-14 {
                    font-size: 14px !important;
                }

                .caixainfo .texto-size-14 {
                    font-size: 11px !important;
                }
            </style>

            <input type="hidden" value="${modulo}" name="modulo"/>
            <hr style="border-color: #e6e6e6;"/>

            <h:commandLink action="#{CampanhaCupomDescontoControle.liberarBackingBeanMemoria}"
                           id="idLiberarBackingBeanMemoria" style="display: none"/>
            <h:panelGrid columns="1" width="100%">

                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita"
                             width="100%" styleClass="novaModal">
                    <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Cidade_codigo}"/>
                    <h:panelGroup>
                        <h:inputText id="codigo" size="10" maxlength="10" readonly="true" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="camposSomenteLeitura"
                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.id}"/>
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="* Descrição campanha:"/>
                    <h:panelGroup>
                        <h:inputText id="nome" size="40" maxlength="40" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                </h:panelGrid>

                <rich:tabPanel width="100%">
                    <rich:tab id="tabCadastro" label="Cadastro">
                        <h:panelGrid id="panelCadastro" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada" styleClass="novaModal">
                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_vigenciaDe}"/>
                                <h:panelGroup styleClass="dateTimeCustom"
                                              style="font-size: 11px !important;">
                                    <rich:calendar id="vigenciaDe"
                                                   value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.vigenciaInicial}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   showWeeksBar="false"/>
                                    <h:message for="vigenciaDe" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Plano_vigenciaAte}"/>
                                <h:panelGroup styleClass="dateTimeCustom"
                                              style="font-size: 11px !important;">
                                    <rich:calendar id="vigenciaAte"
                                                   styleClass="dateTimeCustom"
                                                   value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.vigenciaFinal}"
                                                   inputSize="10"
                                                   inputClass="form"
                                                   oninputblur="blurinput(this);"
                                                   oninputfocus="focusinput(this);"
                                                   oninputchange="return validar_Data(this.id);"
                                                   datePattern="dd/MM/yyyy"
                                                   enableManualInput="true"
                                                   zindex="2"
                                                   buttonIcon="/imagens_flat/calendar-button.svg"
                                                   showWeeksBar="false"/>
                                    <rich:jQuery id="mskDataAte" selector=".rich-calendar-input" timing="onload"
                                                 query="mask('99/99/9999')"/>
                                    <h:message for="vigenciaAte" styleClass="mensagemDetalhada"/>
                                </h:panelGroup>

                                <c:if test="${CampanhaCupomDescontoControle.redeEmpresaVO ne null}">
                                    <h:outputText styleClass="tituloCampos"
                                                  value="#{msg_aplic.prt_CupomDesconto_redeEmpresa}"/>
                                    <h:selectBooleanCheckbox
                                            value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.aplicarParaRede}"/>
                                </c:if>


                                <h:outputText styleClass="tituloCampos"
                                              rendered="false"
                                              value="* Prêmio por indicação:"/>
                                <h:panelGroup rendered="false">
                                    <h:selectOneMenu id="comboTipoPremioPorIndicacao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.tipoPremio}">
                                        <a4j:support event="onchange" reRender="lblPremioProduto, inpPremioProduto"/>
                                        <f:selectItems
                                                value="#{CampanhaCupomDescontoControle.listaSelectItemTipoPremio}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos"
                                              rendered="false"
                                              value="* Tipo distribuição cupom:"/>
                                <h:panelGroup rendered="false">
                                    <h:selectOneMenu id="comboTipoDistribuicao" onblur="blurinput(this);"
                                                     onfocus="focusinput(this);" styleClass="form"
                                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.tipoDistribuicaoCupom}">
                                        <a4j:support event="onchange"
                                                     reRender="pgLabelQtdeCupomGerar, pgInputQtdeCupomGerar"/>
                                        <f:selectItems
                                                value="#{CampanhaCupomDescontoControle.listaSelectItemTipoDistribuicao}"/>
                                    </h:selectOneMenu>
                                </h:panelGroup>

                                <h:outputText styleClass="tituloCampos" value="Total de lotes:"/>
                                <h:outputText styleClass="tituloCampos" id="idTotalLote"
                                              style="vertical-align: middle; font-size: 14px"
                                              value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.totalLote}"/>

                                <h:outputText styleClass="tituloCampos" value="Quantidade cupom extra:"/>
                                <h:outputText styleClass="tituloCampos" id="idCupomExtra"
                                              style="vertical-align: middle; font-size: 14px"
                                              value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.quantidadeCupomExtra}"/>

                                <h:outputText styleClass="tituloCampos" value="Total Cupons Utilizados:"/>
                                <h:panelGroup>
                                    <h:outputText styleClass="tituloCampos" id="idCupomUtilizado"
                                                  style="vertical-align: middle; font-size: 14px"
                                                  value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.totalCupomUtilizado}"/>
                                    <a4j:commandLink id="atualizar_CupomUtilizado"
                                                     action="#{CampanhaCupomDescontoControle.atualizarTotalCuponsUtilizados}"
                                                     style="vertical-align: middle; padding-left: 5px"
                                                     title="Consultar Total Cupons Utilizados."
                                                     immediate="true" ajaxSingle="true"
                                                     reRender="form:idCupomUtilizado">
                                        <i class="fa-icon-refresh"></i>
                                    </a4j:commandLink>
                                </h:panelGroup>
                            </h:panelGrid>

                            <!--- INICIO RESTRIÇÃO DE PLANOS -->
                            <h:panelGrid columns="1" width="100%">
                                <h:panelGroup layout="block" styleClass="painelDadosAluno"
                                              style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;"
                                              id="pgRestPlano">
                                    <div class="tituloPainelAluno">
                                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                                      value="RESTRINGIR PLANOS PARA ESTA CAMPANHA"
                                                      style="line-height: 40px;font-size: 14px !important;"/>

                                    </div>

                                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="classEsquerda, classDireita" width="100%"
                                                     styleClass="novaModal">

                                            <h:outputText styleClass="tituloCampos" value="Descrição do Plano:"/>
                                            <h:panelGroup layout="block" styleClass="block cb-container">
                                                <h:selectOneMenu
                                                        value="#{CampanhaCupomDescontoControle.descricaoPlanoRestringir}"
                                                        id="comboPlano"
                                                        styleClass="form">
                                                    <f:selectItems
                                                            value="#{CampanhaCupomDescontoControle.listaPlanos}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <a4j:commandButton id="addRetricaoPlano"
                                                           action="#{CampanhaCupomDescontoControle.adicionarRestricaoPlano}"
                                                           oncomplete="#{CampanhaCupomDescontoControle.mensagemNotificar}"
                                                           reRender="tableRestricaoPlano, panelRestricaoPlano, panelMensagem, panelMensagemErro, pgRestPlano"
                                                           styleClass="botoes nvoBt botaoPrimarioGrande "
                                                           accesskey="9"
                                                           value="Adicionar restrição">

                                        </a4j:commandButton>


                                        <c:if test="${fn:length(CampanhaCupomDescontoControle.listaRestricaoPlano) > 0}">
                                            <h:dataTable width="100%"
                                                         id="tableRestricaoPlano"
                                                         rows="3"
                                                         styleClass="tblHeaderLeft contratoSelecionado"
                                                         columnClasses="colunaAlinhamento, colunaAlinhamento"
                                                         value="#{CampanhaCupomDescontoControle.listaRestricaoPlano}"
                                                         var="restricaoPlano">
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="PLANO"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet>
                                                    <h:outputText value="#{restricaoPlano}"
                                                                  styleClass="texto-size-14 cinza"/>
                                                </h:column>

                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_bt.btn_opcoes}"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet><a4j:commandButton id="removerRestricaoPlano"
                                                                                 value="Remover "
                                                                                 oncomplete="#{CampanhaCupomDescontoControle.mensagemNotificar}"
                                                                                 action="#{CampanhaCupomDescontoControle.removerRestricaoPlano}"
                                                                                 style="vertical-align: middle;"
                                                                                 reRender="pgRestPlano, tableRestricaoPlano, panelRestricaoPlano, panelMensagem, panelMensagemErro"
                                                                                 styleClass="botoes nvoBt btSec bt">
                                                    <f:setPropertyActionListener value="#{restricaoPlano}"
                                                                                 target="#{CampanhaCupomDescontoControle.descricaoPlanoRestringir}"></f:setPropertyActionListener>
                                                </a4j:commandButton>
                                                </h:column>
                                            </h:dataTable>
                                            <rich:datascroller styleClass="scrollPureCustom" align="center"
                                                               for="tableRestricaoPlano"
                                                               maxPages="3"/>

                                        </c:if>
                                    </div>
                                </h:panelGroup>
                                <!--- FIM RESTRIÇÃO DE PLANOS -->

                                <!--- INICIO PREMIOS PORTADOR CUPOM -->
                                <h:panelGroup layout="block" styleClass="painelDadosAluno"
                                              style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;"
                                              id="pgPremPort">
                                    <div class="tituloPainelAluno">
                                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                                      value="PRÊMIOS AO PORTADOR DO CUPOM"
                                                      style="line-height: 40px;font-size: 14px !important;"/>

                                    </div>

                                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                                     id="panelPremioPortador"
                                                     columnClasses="classEsquerda, classDireita" width="100%"
                                                     styleClass="novaModal">

                                            <h:outputText styleClass="tituloCampos" value="Tipo prêmio:"/>
                                            <h:panelGroup layout="block" styleClass="block cb-container">
                                                <h:selectOneMenu styleClass="form"
                                                                 id="comboTipoPremio"
                                                                 value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio}">
                                                    <f:selectItems
                                                            value="#{CampanhaCupomDescontoControle.listaTipoPremioPortador}"/>
                                                    <a4j:support event="onchange"
                                                                 reRender="pgPremPort, labelDescricaoTipoPremio,inputDescricaoTipoPremio"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                            <h:panelGroup id="labelDescricaoTipoPremio" layout="block">
                                                <h:outputText styleClass="tituloCampos"
                                                              rendered="#{(!(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq ''))}"
                                                              value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.labelDescricaoTipoPremioPortador}"/>
                                            </h:panelGroup>
                                            <h:panelGroup
                                                    rendered="#{(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq 'PRODUTO') or (CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq 'MENSALIDADE')}"
                                                    id="inputDescricaoTipoPremio" layout="block"
                                                    styleClass="block cb-container">
                                                <h:selectOneMenu
                                                        rendered="#{(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq 'PRODUTO')}"
                                                        styleClass="form"
                                                        id="comboProdutoPremio"
                                                        value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.descricaoPremio}">
                                                    <f:selectItems
                                                            value="#{CampanhaCupomDescontoControle.listaProdutos}"/>
                                                </h:selectOneMenu>

                                                <h:selectOneMenu
                                                        rendered="#{(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq 'MENSALIDADE')}"
                                                        styleClass="form"
                                                        value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.descricaoPremio}">
                                                    <f:selectItems
                                                            value="#{CampanhaCupomDescontoControle.listaDescricaoMensalidades}"/>
                                                </h:selectOneMenu>

                                            </h:panelGroup>

                                            <h:outputText styleClass="tituloCampos"
                                                          rendered="#{(!(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq ''))}"
                                                          value="Descrição do Plano:"/>
                                            <h:panelGroup
                                                    rendered="#{(!(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq ''))}"
                                                    layout="block" styleClass="block cb-container">
                                                <h:selectOneMenu styleClass="form"
                                                                 id="comboPlanoPremio"
                                                                 value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.descricaoPlano}">
                                                    <f:selectItems
                                                            value="#{CampanhaCupomDescontoControle.listaPlanos}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>

                                            <h:outputText value="Desconto:" styleClass="tituloCampos"
                                                          rendered="#{(!(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq ''))}"/>
                                            <h:panelGroup
                                                    rendered="#{(!(CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.tipoPremio eq ''))}"
                                                    layout="block" styleClass="block cb-container">
                                                <h:selectOneMenu styleClass="form"
                                                                 value="#{CampanhaCupomDescontoControle.tipoDesconto}">
                                                    <f:selectItems
                                                            value="#{CampanhaCupomDescontoControle.listaTipoDesconto}"/>
                                                    <a4j:support event="onchange"
                                                                 reRender="pgDescontoValor, pgDescontoPercentual"/>
                                                </h:selectOneMenu>
                                                <h:panelGroup id="pgDescontoValor">
                                                    <h:inputText onkeypress="return Tecla(event);" size="10"
                                                                 maxlength="6"
                                                                 rendered="#{CampanhaCupomDescontoControle.tipoDesconto eq 'VALOR'}"
                                                                 id="idDescontoValor"
                                                                 styleClass="form"
                                                                 value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.valorDesconto}">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:inputText>
                                                    <script>
                                                        VMasker(document.getElementById("form:idDescontoValor")).maskMoney({
                                                            precision: 2,
                                                            separator: ',',
                                                            delimiter: '.',
                                                            zeroCents: false
                                                        });
                                                    </script>
                                                </h:panelGroup>

                                                <h:panelGroup id="pgDescontoPercentual">
                                                    <h:inputText onkeypress="return Tecla(event);" size="10"
                                                                 maxlength="6"
                                                                 rendered="#{CampanhaCupomDescontoControle.tipoDesconto eq 'PERCENTUAL'}"
                                                                 id="idDescontoPercentual"
                                                                 styleClass="form"
                                                                 value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO.percentualDesconto}">
                                                        <f:converter converterId="FormatarPercentual"/>
                                                    </h:inputText>
                                                    <script>
                                                        VMasker(document.getElementById("form:idDescontoPercentual")).maskMoney({
                                                            precision: 2,
                                                            separator: ',',
                                                            delimiter: '.',
                                                            zeroCents: false
                                                        });
                                                    </script>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <a4j:commandButton id="addPremioPortador"
                                                           action="#{CampanhaCupomDescontoControle.adicionarPremioPortador}"
                                                           oncomplete="#{CampanhaCupomDescontoControle.mensagemNotificar}"
                                                           reRender="pgPremPort, tablePremioPortador, panelPremioPortador, panelMensagem, panelMensagemErro, inputDescricaoTipoPremio"
                                                           styleClass="botoes nvoBt botaoPrimarioGrande "
                                                           accesskey="9"
                                                           value="Adicionar prêmio">

                                        </a4j:commandButton>

                                        <c:if test="${fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaPremioPortador) > 0}">
                                            <h:dataTable width="100%"
                                                         id="tablePremioPortador"
                                                         styleClass="tblHeaderLeft contratoSelecionado"
                                                         columnClasses="colunaAlinhamento, colunaAlinhamento"
                                                         rows="3"
                                                         value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaPremioPortador}"
                                                         var="premioPortador">
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Tipo prêmio"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet>
                                                    <h:outputText value="#{premioPortador.tipoPremio}"
                                                                  styleClass="texto-size-14 cinza"/>
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Descrição"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet>
                                                    <h:outputText value="#{premioPortador.descricaoPremio}"
                                                                  styleClass="texto-size-14 cinza"/>
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Plano"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet>
                                                    <h:outputText value="#{premioPortador.descricaoPlano}"
                                                                  styleClass="texto-size-14 cinza"/>
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Valor do desconto"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet>
                                                    <h:outputText value="#{premioPortador.valorDesconto}"
                                                                  styleClass="texto-size-14 cinza">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                </h:column>
                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="Percentual de desconto"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet>
                                                    <h:outputText value="#{premioPortador.percentualDesconto}"
                                                                  styleClass="texto-size-14 cinza">
                                                        <f:converter converterId="FormatarPercentual"/>
                                                    </h:outputText>
                                                </h:column>

                                                <h:column>
                                                    <f:facet name="header">
                                                        <h:outputText value="#{msg_bt.btn_opcoes}"
                                                                      styleClass="texto-size-14 cinza negrito upper"/>
                                                    </f:facet><a4j:commandButton id="removerPremioPortador"
                                                                                 value="Remover "
                                                                                 oncomplete="#{CampanhaCupomDescontoControle.mensagemNotificar}"
                                                                                 action="#{CampanhaCupomDescontoControle.removerPremioPortador}"
                                                                                 style="vertical-align: middle;"
                                                                                 reRender="pgPremPort, tablePremioPortador, panelPremioPortador, panelMensagem, panelMensagemErro"
                                                                                 styleClass="botoes nvoBt btSec bt">
                                                    <f:setPropertyActionListener value="#{premioPortador}"
                                                                                 target="#{CampanhaCupomDescontoControle.campanhaCupomDescontoPremioPortadorVO}"></f:setPropertyActionListener>
                                                </a4j:commandButton>
                                                </h:column>
                                            </h:dataTable>
                                            <rich:datascroller styleClass="scrollPureCustom" align="center"
                                                               for="tablePremioPortador" maxPages="3"/>

                                        </c:if>
                                    </div>
                                </h:panelGroup>
                                <!--- FIM PREMIOS PORTADOR CUPOM -->


                                <h:panelGroup layout="block" styleClass="painelDadosAluno"
                                              style="width: calc(98.5% - 10px); height: auto; font-family: Arial,Verdana,sans-serif;"
                                              id="pgCupons"
                                              rendered="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.id != null and CampanhaCupomDescontoControle.campanhaCupomDescontoVO.id != 0}">
                                    <div class="tituloPainelAluno">
                                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                                      value="CUPONS"
                                                      style="line-height: 40px;font-size: 14px !important;"/>

                                    </div>

                                    <div class="conteudoDadosCliente" style="min-height: 50px; text-align: center">
                                        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                                     columnClasses="classEsquerda, classDireita" width="100%"
                                                     styleClass="novaModal" style="height: 4vh">
                                            <h:panelGroup layout="block" rendered="#{(fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomAleatorios) > 0) or (fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomNomeFixo) > 0)}">
                                                <h:panelGroup>
                                                    <a4j:commandLink id="btnExcelCupomDesconto"
                                                                     styleClass="botoes nvoBt btSec"
                                                                     style="vertical-align: middle"
                                                                     actionListener="#{CampanhaCupomDescontoControle.exportarListaCupom}"
                                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','campanhaCupomDesconto', 800,200);#{ExportadorListaControle.msgAlert}"

                                                                     accesskey="3">
                                                        <f:attribute name="tipo" value="xls"/>
                                                        <f:attribute name="lista"
                                                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupom}"/>
                                                        <f:attribute name="atributos"
                                                                     value="#{CampanhaCupomDescontoControle.colunasImpressaoCupons}"/>
                                                        <f:attribute name="prefixo"
                                                                     value="CampanhaCupomDesconto#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>
                                                        <f:attribute name="titulo"
                                                                     value="Campanha Cupom Desconto"/>
                                                        <f:attribute name="subTitulo"
                                                                     value="Campanha: #{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>

                                                        <i class="fa-icon-excel"></i> &nbsp Excel
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                                <h:panelGroup>
                                                    <a4j:commandLink id="btnPDFCupomDesconto"
                                                                     styleClass="botoes nvoBt btSec"
                                                                     style="vertical-align: middle"
                                                                     actionListener="#{CampanhaCupomDescontoControle.exportarListaCupom}"
                                                                     oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','campanhaCupomDesconto', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                     accesskey="4">
                                                        <f:attribute name="tipo" value="pdf"/>
                                                        <f:attribute name="lista"
                                                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupom}"/>
                                                        <f:attribute name="atributos"
                                                                     value="#{CampanhaCupomDescontoControle.colunasImpressaoCupons}"/>
                                                        <f:attribute name="prefixo"
                                                                     value="CampanhaCupomDesconto"/>
                                                        <f:attribute name="titulo"
                                                                     value="Campanha Cupom Desconto"/>
                                                        <f:attribute name="subTitulo"
                                                                     value="Campanha: #{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>
                                                        <i class="fa-icon-pdf"></i> &nbsp PDF
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGrid>
                                        <a4j:commandButton id="gerarLoteCupomExtra"
                                                           action="#{CampanhaCupomDescontoControle.novoLoteCupomDesconto}"
                                                           oncomplete="#{CampanhaCupomDescontoControle.msgAlert}"
                                                           reRender="formLoteCupom"
                                                           styleClass="botoes nvoBt botaoPrimarioGrande "
                                                           accesskey="9"
                                                           value="Novo Lote Cupom">
                                        </a4j:commandButton>
                                        <c:if test="${fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomAleatorios) > 0}">
                                            <div class="tituloPainelAluno">
                                                <h:outputText styleClass="negrito cinzaEscuro pl20"
                                                              value="CUPONS ALEATÓRIOS"
                                                              style="line-height: 40px;font-size: 14px !important;"/>

                                            </div>
                                            <h:panelGroup id="pgTableCupomDesc">
                                                <h:dataTable width="100%"
                                                             id="listaCupomDesconto"
                                                             styleClass="tblHeaderLeft contratoSelecionado"
                                                             columnClasses="colunaAlinhamento, colunaAlinhamento"
                                                             rows="5"
                                                             value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomAleatorios}"
                                                             var="cupom">
                                                    <h:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Data geração"
                                                                          styleClass="texto-size-14 cinza negrito upper"/>
                                                        </f:facet>
                                                        <h:outputText value="#{cupom.dataLancamento_Apresentar}"
                                                                      styleClass="texto-size-14 cinza"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Lote"
                                                                          styleClass="texto-size-14 cinza negrito upper"/>
                                                        </f:facet>
                                                        <h:outputText value="#{cupom.lote}"
                                                                      styleClass="texto-size-14 cinza"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Cupom"
                                                                          styleClass="texto-size-14 cinza negrito upper"/>
                                                        </f:facet>
                                                        <h:outputText value="#{cupom.numeroCupom}"
                                                                      styleClass="texto-size-14 cinza"/>
                                                    </h:column>
                                                    <h:column>
                                                        <f:facet name="header">
                                                            <h:outputText value="Data prêmio portador cupom"
                                                                          styleClass="texto-size-14 cinza negrito upper"/>
                                                        </f:facet>
                                                        <h:outputText
                                                                value="#{cupom.dataPremioPortadorCupom_Apresentar}"
                                                                styleClass="texto-size-14 cinza"/>
                                                    </h:column>

                                                </h:dataTable>
                                                <rich:datascroller styleClass="scrollPureCustom" align="center"
                                                                   for="listaCupomDesconto"
                                                                   maxPages="5" id="scCupomDesconto"/>
                                                <h:panelGrid id="pgTotalCuponsGerado" columns="1"
                                                             styleClass="textsmall" width="100%"
                                                             columnClasses="colunaDireita">
                                                    <h:outputLabel styleClass="texto-size-14 cinza"
                                                                   value="Total:">${fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomAleatorios)}</h:outputLabel>
                                                </h:panelGrid>
                                            </h:panelGroup>

                                        </c:if>

                                <c:if test="${fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomNomeFixo) > 0}">
                                    <div class="tituloPainelAluno">
                                        <h:outputText styleClass="negrito cinzaEscuro pl20"
                                                      value="CUPONS PERSONALIZADOS"
                                                      style="line-height: 40px;font-size: 14px !important;"/>

                                    </div>
                                    <h:panelGroup id="pgTableCupomDescFixo">
                                        <h:dataTable width="100%"
                                                     id="listaCupomDescontoFixo"
                                                     styleClass="tblHeaderLeft contratoSelecionado"
                                                     columnClasses="colunaAlinhamento, colunaAlinhamento"
                                                     rows="5"
                                                     value="#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomNomeFixo}"
                                                     var="cupomFixo">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Data geração"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupomFixo.dataLancamento_Apresentar}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Lote"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupomFixo.lote}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Cupom"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupomFixo.numeroCupom}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Quantidade de cupons"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupomFixo.qtdCuponsNomeFixo}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Quantidade de cupons utilizados"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupomFixo.qtdUtilizadoCuponsNomeFixo}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>

                                        </h:dataTable>
                                        <rich:datascroller styleClass="scrollPureCustom" align="center"
                                                           for="listaCupomDescontoFixo"
                                                           maxPages="5" id="scCupomDescontoFixo"/>
                                        <h:panelGrid id="pgTotalCuponsGeradoFixo" columns="1"
                                                     styleClass="textsmall" width="100%"
                                                     columnClasses="colunaDireita">
                                            <h:outputLabel styleClass="texto-size-14 cinza"
                                                           value="Total:">${fn:length(CampanhaCupomDescontoControle.campanhaCupomDescontoVO.listaCupomNomeFixo)}</h:outputLabel>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </c:if>
                                    </div>
                                </h:panelGroup>

                            </h:panelGrid>

                        </h:panelGrid>
                    </rich:tab>

                    <rich:tab id="tabConsultarCupom" label="Consultar Utilização Cupom" styleClass="novaModal">
                        <h:panelGrid id="panelConsultarCupom" columns="1" width="100%" headerClass="subordinado"
                                     columnClasses="colunaCentralizada">

                            <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
                                         columnClasses="classEsquerda, classDireita" width="100%">

                                <h:outputText styleClass="tituloCampos" value="Unidade:"/>
                                <h:panelGroup layout="block" styleClass="block cb-container">
                                    <h:selectOneMenu id="comboUnidade"
                                                     styleClass="form"
                                                     style="vertical-align: middle"
                                                     value="#{CampanhaCupomDescontoControle.empresaFinanceiroSelecionada}">
                                        <a4j:support event="onchange"
                                                     action="#{CampanhaCupomDescontoControle.selecionarEmpresa}"/>
                                        <f:selectItems
                                                value="#{CampanhaCupomDescontoControle.listaSelectItemUnidadesRede}"/>
                                    </h:selectOneMenu>

                                </h:panelGroup>
                                <h:outputText styleClass="tituloCampos" value="Cupom:"/>
                                <h:panelGroup>
                                    <h:inputText size="10"
                                                 styleClass="form"
                                                 value="#{CampanhaCupomDescontoControle.numeroCupomPesquisar}"/>
                                </h:panelGroup>

                            </h:panelGrid>
                            <h:panelGrid id="pgPesquisrCupm" columnClasses="colunaCentralizada" columns="1"
                                         width="100%">
                                <a4j:commandButton id="consultarCampCupom"
                                                   reRender="pgResConsultaCupom, pgMensagemCadCampanha, pgListaConsulta"
                                                   action="#{CampanhaCupomDescontoControle.consultarCupons}"
                                                   style="vertical-align: middle"
                                                   value="Consultar"
                                                   styleClass="botoes nvoBt btSec"
                                                   accesskey="3"/>
                            </h:panelGrid>
                                <h:panelGrid id="pgResConsultaCupom" columnClasses="colunaEsqueda, colunaDireita"
                                             columns="2" width="100%">

                                        <h:panelGroup rendered="#{fn:length(CampanhaCupomDescontoControle.listaConsultaHistoricoUtilizacaoCupom) >= 1}">
                                            <h:panelGroup>
                                            <a4j:commandLink id="btnExcelConsCupomDesconto"
                                                             styleClass="botoes nvoBt btSec"
                                                             style="vertical-align: middle"
                                                             actionListener="#{CampanhaCupomDescontoControle.exportarResultadoConsultaCupom}"
                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','campanhaCupomDesconto', 800,200);#{ExportadorListaControle.msgAlert}"

                                                             accesskey="3">
                                                <f:attribute name="tipo" value="xls"/>
                                                <f:attribute name="lista"
                                                             value="#{CampanhaCupomDescontoControle.listaConsultaHistoricoUtilizacaoCupom}"/>
                                                <f:attribute name="atributos"
                                                             value="#{CampanhaCupomDescontoControle.colunasImpressaoCuponsUtilizados}"/>
                                                <f:attribute name="prefixo"
                                                             value="ResultadoCampanhaCupomDesconto#{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>
                                                <f:attribute name="titulo" value="Resultado Campanha Cupom Desconto"/>
                                                <f:attribute name="subTitulo"
                                                             value="Campanha: #{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>

                                                <i class="fa-icon-excel"></i> &nbsp Excel
                                            </a4j:commandLink>
                                        </h:panelGroup>

                                        <h:panelGroup>
                                            <a4j:commandLink id="btnPDFConsCupomDesconto"
                                                             styleClass="botoes nvoBt btSec"
                                                             style="vertical-align: middle"
                                                             actionListener="#{CampanhaCupomDescontoControle.exportarResultadoConsultaCupom}"
                                                             oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','campanhaCupomDesconto', 800,200);#{ExportadorListaControle.msgAlert}"
                                                             accesskey="4">
                                                <f:attribute name="tipo" value="pdf"/>
                                                <f:attribute name="lista"
                                                             value="#{CampanhaCupomDescontoControle.listaConsultaHistoricoUtilizacaoCupom}"/>
                                                <f:attribute name="atributos"
                                                             value="#{CampanhaCupomDescontoControle.colunasImpressaoCuponsUtilizados}"/>
                                                <f:attribute name="prefixo" value="ResultadoCampanhaCupomDesconto"/>
                                                <f:attribute name="titulo" value="Resultado Campanha Cupom Desconto"/>
                                                <f:attribute name="subTitulo"
                                                             value="Campanha: #{CampanhaCupomDescontoControle.campanhaCupomDescontoVO.descricaoCampanha}"/>
                                                <i class="fa-icon-pdf"></i> &nbsp PDF
                                            </a4j:commandLink>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                </h:panelGrid>


                                <h:panelGrid id="pgListaConsulta" columns="1" columnClasses="centralizado" width="100%">
                                    <h:panelGroup rendered="#{fn:length(CampanhaCupomDescontoControle.listaConsultaHistoricoUtilizacaoCupom) >= 1}">
                                        <h:dataTable width="100%"
                                                     id="listaCupomConsultada"
                                                     styleClass="tblHeaderLeft contratoSelecionado"
                                                     columnClasses="colunaAlinhamento, colunaAlinhamento"
                                                     rows="5"
                                                     value="#{CampanhaCupomDescontoControle.listaConsultaHistoricoUtilizacaoCupom}"
                                                     var="cupom">
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Cupom"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.numeroCupom}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Data prêmio portador"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.dataPremioPortadorCupom_Apresentar}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Nome portador"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.nomePortadorCupom}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Unidade portador"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.nomeUnidadePortador}"
                                                              styleClass="texto-size-14 cinza"/>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Total prêmio produtos portador"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.valorPremioProdutosPortadorCupom}"
                                                              styleClass="texto-size-14 cinza">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Total prêmio mensalidade portador"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.valorPremioMensalidadePortadorCupom}"
                                                              styleClass="texto-size-14 cinza">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Total geral prêmio portador"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.valorPremioPortadorCupom}"
                                                              styleClass="texto-size-14 cinza">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value="Contrato"
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText value="#{cupom.contrato}"
                                                              styleClass="texto-size-14 cinza">
                                                </h:outputText>
                                            </h:column>
                                            <h:column>
                                                <f:facet name="header">
                                                    <h:outputText value=""
                                                                  styleClass="texto-size-14 cinza negrito upper"/>
                                                </f:facet>
                                                <h:outputText rendered="#{cupom.contratoEstornado}" value="(ESTORNADO)"
                                                              styleClass="texto-size-14 cinza texto-cor-vermelho">
                                                </h:outputText>
                                            </h:column>
                                        </h:dataTable>
                                        <rich:datascroller styleClass="scrollPureCustom" align="center"
                                                           for="listaCupomConsultada" maxPages="5"
                                                           id="scCupomConsultado"/>
                                        <h:panelGrid id="pgTotalCuponsConsultados" columns="1" styleClass="textsmall"
                                                     width="100%" columnClasses="colunaDireita">
                                            <h:outputLabel styleClass="texto-size-14 cinza"
                                                           value="Total:">${fn:length(CampanhaCupomDescontoControle.listaConsultaHistoricoUtilizacaoCupom)}</h:outputLabel>
                                        </h:panelGrid>
                                    </h:panelGroup>
                                </h:panelGrid>
                        </h:panelGrid>
                    </rich:tab>
                </rich:tabPanel>


                <h:panelGrid columns="1" width="100%" id="pgMensagemCadCampanha" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <f:verbatim>
                                <h:outputText value=" "/>
                            </f:verbatim>
                        </h:panelGrid>
                        <h:commandButton rendered="#{CampanhaCupomDescontoControle.sucesso}"
                                         image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{CampanhaCupomDescontoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem" value="#{CampanhaCupomDescontoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada"
                                          value="#{CampanhaCupomDescontoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">

                        <c:if test="${modulo eq 'zillyonWeb'}">
                            <h:panelGroup>
                                <h:commandButton id="novo" immediate="true"
                                                 action="#{CampanhaCupomDescontoControle.novo}"
                                                 value="#{msg_bt.btn_novo}" alt="#{msg.msg_novo_dados}" accesskey="1"
                                                 styleClass="botoes nvoBt btSec"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="salvar" action="#{CampanhaCupomDescontoControle.gravar}"
                                                   reRender="form, pgCupons, pgMensagemCadCampanha, codigo"
                                                   value="#{msg_bt.btn_gravar}" alt="#{msg.msg_gravar_dados}"
                                                   accesskey="2" styleClass="botoes nvoBt"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>
                                <a4j:commandButton id="excluir"
                                                   reRender="mdlMensagemGenerica"
                                                   action="#{CampanhaCupomDescontoControle.abrirModalExcluirCupomDesconto}"
                                                   oncomplete="#{CampanhaCupomDescontoControle.msgAlert}"
                                                   value="#{msg_bt.btn_excluir}" alt="#{msg.msg_excluir_dados}"
                                                   accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                                <f:verbatim>
                                    <h:outputText value="    "/>
                                </f:verbatim>


                                <h:commandButton id="consultar" immediate="true"
                                                 action="#{CampanhaCupomDescontoControle.inicializarConsultar}"
                                                 value="#{msg_bt.btn_consultar}" alt="#{msg.msg_consultar_dados}"
                                                 accesskey="4" styleClass="botoes nvoBt btSec"/>
                                <rich:spacer width="15px"/>
                                <a4j:commandLink
                                        action="#{CampanhaCupomDescontoControle.realizarConsultaLogObjetoSelecionado}"
                                        reRender="form"
                                        oncomplete="#{CampanhaCupomDescontoControle.oncompleteLog}"
                                        title="Visualizar Log"
                                        style="display: inline-block; padding: 8px 15px; margin-left: -6px;"
                                        styleClass="botoes nvoBt btSec">
                                    <i style="text-decoration: none" class="fa-icon-list"/>
                                </a4j:commandLink>

                            </h:panelGroup>

                        </c:if>

                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>


    <rich:modalPanel id="modalNovoLoteCupomDesconto" autosized="true"
                     onshow="document.getElementById('formLoteCupom:quantidadeLoteCupom').focus()"
                     shadowOpacity="true" width="600" height="250" styleClass="novaModal">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Novo Lote de Cupom de Desconto"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer"
                                id="hidelinkLoteCupom"/>
                <rich:componentControl for="modalNovoLoteCupomDesconto"
                                       attachTo="hidelinkLoteCupom" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>

        <a4j:form id="formLoteCupom">
            <h:panelGrid columns="1" width="100%"
                         footerClass="colunaCentralizada" headerClass="subordinado">
                <h:panelGrid columns="2" headerClass="subordinado" width="100%">
                    <h:outputText styleClass="tituloCampos" value="Gerar cupom aleatório: "/>
                    <h:panelGroup styleClass="chk-fa-container" style="position: absolute; margin-top: -1.2vh">
                        <h:selectBooleanCheckbox id="gerarCupomAleatorio"
                                                 value="#{CampanhaCupomDescontoControle.gerarNomeCupomAleatorio}"
                                                 styleClass="form">
                            <a4j:support event="onclick"
                                         reRender="formLoteCupom"/>
                        </h:selectBooleanCheckbox>
                        <span/>
                    </h:panelGroup>

                    <h:outputText rendered="#{!CampanhaCupomDescontoControle.gerarNomeCupomAleatorio}"
                                  styleClass="tituloCampos" value="Nome do cupom:"/>
                    <h:panelGroup rendered="#{!CampanhaCupomDescontoControle.gerarNomeCupomAleatorio}">
                        <h:inputText id="nomeCupomFixo"
                                     styleClass="form"
                                     maxlength="25"
                                     value="#{CampanhaCupomDescontoControle.nomeCupomEspecifico}"/>
                    </h:panelGroup>

                    <h:outputText styleClass="tituloCampos" value="Quantidade de cupons a gerar:"/>
                    <h:panelGroup>
                        <h:inputText id="quantidadeLoteCupom"
                                     title="Informe a quantidade total de cupons a serem gerados para este lote."
                                     onkeypress="return mascaraTodos(this.form, 'formLoteCupom:quantidadeLoteCupom', '999999999', event);"
                                     size="5" maxlength="6" onblur="blurinput(this);"
                                     onfocus="focusinput(this);" styleClass="form"
                                     value="#{CampanhaCupomDescontoControle.quantidadeCupomLote}"/>
                    </h:panelGroup>
                    <h:outputText styleClass="tituloCampos" value="Observações:"/>
                    <h:panelGroup>
                        <h:inputTextarea id="observacoes" rows="5" styleClass="inputTextClean" onblur="blurinput(this);" style="width: 250px;height: 50px;"
                                         onfocus="focusinput(this);" value="#{CampanhaCupomDescontoControle.observacaoLoteCupom}"/>
                    </h:panelGroup>
                </h:panelGrid>

            </h:panelGrid>
            <h:panelGrid id="mensagemLoteCupom" columns="3"
                         width="100%" styleClass="tabMensagens">
                <h:panelGrid columns="1" width="100%">

                    <h:outputText value=" "/>

                </h:panelGrid>
                <h:panelGroup>
                    <a4j:commandButton rendered="#{CampanhaCupomDescontoControle.sucesso}"
                                       image="./imagens/sucesso.png"/>
                    <a4j:commandButton rendered="#{CampanhaCupomDescontoControle.erro}"
                                       image="./imagens/erro.png"/>
                </h:panelGroup>
                <h:panelGrid columns="1" width="100%">
                    <h:outputText styleClass="mensagem"
                                  value="#{CampanhaCupomDescontoControle.mensagem}"/>
                    <h:outputText styleClass="mensagemDetalhada"
                                  value="#{CampanhaCupomDescontoControle.mensagemDetalhada}"/>
                </h:panelGrid>
            </h:panelGrid>
            <a4j:commandButton id="gravarLoteCupomDesc"
                               style="margin-left: 255px;"
                               action="#{CampanhaCupomDescontoControle.gravarNovoLoteCupomDesconto}"
                               reRender="pgCupons, mensagemLoteCupom, listaCupomDesconto, idTotalLote, idCupomExtra, pgTotalCuponsGerado, pgTableCupomDescFixo, pgTableCupomDesc, pgListaCupomCarregado"
                               oncomplete="#{CampanhaCupomDescontoControle.mensagemNotificar};#{CampanhaCupomDescontoControle.msgAlert}"
                               value="Gravar"
                               accesskey="2" styleClass="botoes nvoBt"/>

        </a4j:form>
    </rich:modalPanel>
    <%@include file="includes/include_modal_mensagem_generica.jsp" %>
</f:view>
<script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
<script type="text/javascript">
    jQuery.noConflict();
</script>
<script>
    document.getElementById("form:nome").focus();
</script>
