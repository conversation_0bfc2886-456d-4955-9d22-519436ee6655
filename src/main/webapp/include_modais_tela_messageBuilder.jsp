<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <%@include file="/includes/include_import_minifiles.jsp" %>
</head>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="./beta/css/pure-min.css" rel="stylesheet" type="text/css">
<link href="./beta/css/pure-ext.css" rel="stylesheet" type="text/css">
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="./css/smartbox/smartbox.css" rel="stylesheet" type="text/css">

<link href="./beta/css/font-awesome_2.0.min.css" rel="stylesheet" type="text/css">

<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/beta/js/DT_bootstrap.js"></script>
<script type="text/javascript" src="./beta/js/ext-funcs.js"></script>
<link href="${pageContext.request.contextPath}/bootstrap/bootplus.css" rel="stylesheet">
<c:set var="contexto" value="${pageContext.request.contextPath}" scope="request"/>

<style type="text/css">
    td.gridMetas {
        text-align: center;
        width: 240px;
    }

    .rich-table-cell.historicoContato {
        padding: 5px 8px 5px 8px !important;
    }

    .rich-mpnl-content.historicoContato {
        border: none !important;
        border-color: white;
    }

    .rich-table-thead.historicoContato {
        background: none !important;
        border: none !important;
    }

    .mensagemTextoPadrao{
        display: block;
        height: 435px;
        overflow: auto;
    }
</style>

<%--MODAL DE TAG DA EMPRESA--%>
<rich:modalPanel id="panelEmail" autosized="true" shadowOpacity="true" width="300" height="150" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="Marcadores de Email"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidePanelEmail"/>
            <rich:componentControl for="panelEmail" attachTo="hidePanelEmail"
                                   operation="hide"
                                   event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formMarcadorEmail" ajaxSubmit="true">
        <h:panelGrid columns="1" width="95%" columnClasses="colunaCentralizada">
            <rich:dataTable id="MarcadoEmail" width="440px" headerClass="consulta" rowClasses="linhaImpar, linhaPar"
                            columnClasses="colunaAlinhamento" var="marcadorEmail" rows="40"
                            value="#{MetaCRMControle.listaSelectItemMarcadoEmail}">
                <f:facet name="header">
                    <h:outputText value="Email"/>
                </f:facet>
                <rich:column width="170px">
                    <f:facet name="header">
                        <h:outputText value="Tags"/>
                    </f:facet>
                    <h:outputText styleClass="campos" value="#{marcadorEmail.nome}"/>
                </rich:column>
                <rich:column width="240px">
                    <f:facet name="header">
                        <h:outputText value="Op??es"/>
                    </f:facet>
                    <a4j:commandLink action="#{MetaCRMControle.executarInsercaoTag}" reRender="mensagemEmail"
                                     oncomplete="Richfaces.hideModalPanel('panelEmail');"
                                     title="Adicionar">
                        <i class="fa-icon-plus-sign" style="font-size: large;  color: darkblue"></i>
                    </a4j:commandLink>
                </rich:column>
            </rich:dataTable>
        </h:panelGrid>
    </a4j:form>
</rich:modalPanel>

<%@include file="/include_load_configs.jsp" %>

<h:panelGroup id="includesCRM">
    <%@include file="/include_modal_expiracaoSenha.jsp" %>
</h:panelGroup>


