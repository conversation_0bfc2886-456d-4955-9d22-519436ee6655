function validarAmbiente() {
    limparMensagem('ambiente');
    limparMensagem('valorPerfilEventoAmbiente');
    limparMensagem('nrMaximoConv');

    var ambiente = document.getElementById('form:ambiente');
    var valorPerfilEventoAmbiente = document.getElementById('form:valorPerfilEventoAmbiente');
    var nrMaximoConv = null;

    nrMaximoConv = document.getElementById('form:numMaxConvidados').value;

    var validade = true;

    if (ambiente == null || ambiente.value == null || ambiente.value == "") {
        exibirMensagem(
            montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.ambiente\']}"/>'),
            'ambiente');
        validade = false;
    }

    if (valorPerfilEventoAmbiente == null
        || valorPerfilEventoAmbiente.value == null
        || valorPerfilEventoAmbiente.value == "") {
        exibirMensagem(
            montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.valor\']}"/>'),
            'valorPerfilEventoAmbiente');
        validade = false;
    }

    if (nrMaximoConv == 0 || nrMaximoConv.value == 0) {
        exibirMensagem(
            montarMsgObrigatoriedade('<h:outputText value="#{CElabels[\'entidade.nrMaxConv\']}"/>'),
            'nrMaximoConv');
        validade = false;
    }

    return validade;
}