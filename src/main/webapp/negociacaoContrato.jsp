<%--
  Created by IntelliJ IDEA.
  User: Rafael
  Date: 31/03/2016
  Time: 14:53
  To change this template use File | Settings | File Templates.
--%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<head>
    <script type="text/javascript" language="javascript" src="hoverform.js"></script>
    <script type="text/javascript" language="javascript" src="script/negociacaoContrato_1.0.min.js"></script>
    <script type="text/javascript" language="javascript" src="script/gobackblock.js"></script>
    <link href="css/css_contrato.1.0.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
    <link href="./css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" language="javascript" src="bootstrap/jquery.js"></script>
    <script type="text/javascript" src="script/tooltipster/jquery.tooltipster.min.js"></script>
    <script type="text/javascript" src="script/jquery.maskedinput-1.7.6.js"></script>
    <link href="./css/faqtoid.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="script/faqtoid_0.1.js"></script>
</head>
<style>
    .btn-experimente {
        box-shadow: 0 2px 2px 0 rgba(169,169,169,.14), 0 3px 1px -2px rgba(169,169,169,.2), 0 1px 5px 0 rgba(169,169,169,.12);
        transition: .2s ease-in;
        background-color: #fff !important;
        color: #67757c !important;
        border-color: #b1b8bb !important;
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        border: 1px solid transparent;
        transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
        background: #fff !important;
        padding: 7px 12px !important;
        /*font-size: 15px !important;*/
        line-height: 1.5;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
    }

    .btn-experimente:hover {
        box-shadow: 0 14px 26px -12px rgba(169,169,169,.42), 0 4px 23px 0 rgba(0,0,0,.12), 0 8px 10px -5px rgba(169,169,169,.2);
        color: #212529 !important;
        background-color: #b3b2b2 !important;
        border-color: #acacac !important;
    }

    .fechado-zwui .div-geral-experimente {
        padding: 1.5% 0px 0 115px;
        width: calc(100% - 152px);
    }
    .div-geral-experimente {
        display: grid;
        padding: 1.5% 0px 0 40px;
        width: calc(100% - 80px);
        grid-template-columns: 2fr 0fr;
    }

    .div-experimente2 {
        justify-content: end;
        display: flex;
        padding-left: 15px;
    }

    .div-experimente {
        background-color: #fff !important;
        border-radius: 5px;
        font-family: "Nunito Sans",sans-serif !important;
        padding: 10px;
        align-items: center;
        display: flex;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 30px;
        height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(13px);
        -ms-transform: translateX(13px);
        transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 17px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .padrao-con {
        display: flex;
        color: #797D86;
        font-size: 16px;
        font-weight: 400;
        line-height: 16px;
        padding: 30px 0px 0 40px;
        width: calc(100% - 80px);
        justify-content: flex-end;
    }
    .padrao-con label{
        margin-left: 8px;
    }

    .padrao-con .clicavel {
        cursor: pointer;
    }
</style>
<script>
    jQuery.noConflict();


    $(function() {carregarTooltipster(); });


</script>

<%@page contentType="text/html" %>
<%@page pageEncoding="UTF-8" %>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>
<%@taglib prefix="jsfChart" uri="http://sourceforge.net/projects/jsf-comp" %>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<body class="paginaFontResponsiva">
<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <jsp:include page="includes/include_modais_contrato.jsp" flush="true"/>
    <jsp:include page="include_head.jsp" flush="true"/>
    <h:form id="form" style="margin-bottom: 0px"  onkeypress="return event.keyCode != 13; " >
        <div id="msgAviso" style="display:none;">
            <span><img src="desabilitaBotao.png"></span>
            <span>Não é permitido voltar pelo botão do browser.</span>
        </div>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW">
                <h:panelGroup layout="block" styleClass="tudo">
                <jsp:include page="include_topo_novo.jsp" flush="true"/>
                </h:panelGroup>
                <jsp:include page="include_menu_zw_flat.jsp" flush="true"/>
            </h:panelGroup>
            <script type="text/javascript" language="javascript">
              setDocumentCookie('popupsImportante', 'close',1);
            </script>

            <h:panelGroup layout="block" styleClass="caixaCorpo tabelasSemHover">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central" style="position:relative;">

                            <h:panelGroup layout="block" styleClass="div-geral-experimente"
                                          rendered="#{!ContratoControle.temPlanoPacote}">

                                <h:panelGroup layout="block"
                                              styleClass="div-experimente">
                                    <h:graphicImage value="images/pct-circle-exclamation.svg" style="width: 16px; padding-right: 5px; padding-bottom: 2px;"/>
                                    <h:outputText value="Experimente agora a nova versão e obtenha o máximo de resultados e agilidade em suas vendas com nosso mais novo recurso de sugestão por inteligência artificial."
                                                  styleClass="texto-size-14"/>
                                </h:panelGroup>


                            </h:panelGroup>

                            <c:if test="${not ContratoControle.temPlanoPacote}">
                                <span class="padrao-con"
                                      id="div-switch-nova-versao">
                                    <span class="clicavel" onclick="abrirNovaTelaNegociacao()">Usar nova versão</span>
                                    <label class="switch clicavel" onclick="abrirNovaTelaNegociacao()">
                                        <input type="checkbox"
                                               id="switch-nova-versao"
                                               onclick="abrirNovaTelaNegociacao()">
                                        <span class="slider round"></span>
                                    </label>
                                </span>
                            </c:if>


                            <a4j:jsFunction name="abrirNovaTelaNegociacao"
                                            oncomplete="#{ContratoControle.msgAlert}"
                                            action="#{ContratoControle.abrirNovNegociacao}"></a4j:jsFunction>

                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial " style="margin-top: 30px !important;">


                                <h:panelGroup layout="block" styleClass="tudo container-box-header">

                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText styleClass="container-header-titulo"
                                                      value="#{msg_aplic.prt_negociacao_contrato}"/>

                                        <h:outputLink styleClass="linkWiki tooltipster"
                                                      rendered="#{ContratoControle.contratoVO.renovarContrato}"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                      title="Clique e saiba mais: Renovação"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>

                                        <h:outputLink styleClass="linkWiki tooltipster"
                                                      rendered="#{ContratoControle.contratoVO.rematricularContrato}"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                      title="Clique e saiba mais: Rematrícula"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>

                                        <h:outputLink styleClass="linkWiki tooltipster"
                                                      rendered="#{!ContratoControle.contratoVO.renovarContrato && !ContratoControle.contratoVO.rematricularContrato}"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                      title="Clique e saiba mais: Negociação de Contrato"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>

                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup id="datasContrato" styleClass="caixainfonegociacao tudo" layout="block" rendered="#{ContratoControle.valorParcelasAtraso > 1 || ContratoControle.contratoVO.renovarContrato}"
                                           >
                                    <h:panelGroup layout="block" style="display: inline-block;width: 57%"  rendered="#{ContratoControle.contratoVO.renovarContrato}"  >
                                        <h:panelGroup layout="block" style="width: 100%;height: 100%;" styleClass="caixaInfoContrato bg-cinza">
                                        <h:panelGroup styleClass="margin-container" layout="block" >
                                            <h:outputText
                                                    styleClass="tituloLabel texto-font texto-bold texto-cor-cinza texto-size-14"
                                                    value="CONTRATO ANTERIOR"/>
                                            <h:outputText
                                                    styleClass="valorCampos texto-cor-cinza texto-font texto-size-14"
                                                    value="#{ContratoControle.vigenciaDeDoUltimoContrato_Apresentar}"/>
                                            <h:panelGroup layout="block" styleClass="timeLineSmall"/>
                                            <h:outputText
                                                    styleClass="valorCampos texto-cor-cinza texto-font texto-size-14"
                                                    value="#{ContratoControle.vigenciaAteAjustadaDoUltimoContrato_Apresentar}"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" rendered="#{ContratoControle.valorParcelasAtraso > 1}"
                                                  styleClass="caixaInfoFinan">
                                        <h:panelGroup styleClass="margin-container" layout="block">
                                            <h:panelGroup layout="block" style="cursor: pointer"
                                                          rendered="#{ContratoControle.valorParcelasAtraso > 1}">
                                                <div layout="block"
                                                     onmouseover="exibirElementoTooltip(this,jQuery(this).children('.tooltipElemento'))"
                                                     class="tooltipster">
                                                    <h:outputText
                                                            styleClass="texto-font texto-cor-vermelho texto-size-16"
                                                            value="Parcelas em aberto."/>
                                                    <h:outputText
                                                            styleClass="texto-font texto-cor-vermelho texto-bold texto-size-16"
                                                            value=" Valor total #{ContratoControle.valorParcelasAtraso_Apresentar}"/>
                                                    <h:panelGroup layout="block" styleClass="tooltipElemento"
                                                                  style="display: none;width:400px">
                                                        <h:panelGrid columns="3"
                                                                     columnClasses="colunaEsquerda,colunaEsquerda,colunaDireita"
                                                                     styleClass="tabelaSimplesCustom font-size-Em"
                                                                     width="100%"
                                                                     style="margin: 5px;display: inline-block;">
                                                            <c:forEach items="#{ContratoControle.parcelasEmAberto}"
                                                                       var="parcela">
                                                                <h:outputText
                                                                        styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                        value="#{parcela.descricao}"/>
                                                                <h:outputText
                                                                        styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                        value="#{parcela.dataVencimento_Apresentar}"/>
                                                                <h:outputText style="text-align:right;"
                                                                              styleClass="texto-font texto-size-16 texto-cor-vermelho"
                                                                              value="#{parcela.valorParcela_Apresentar}"/>
                                                            </c:forEach>
                                                        </h:panelGrid>
                                                    </h:panelGroup>
                                                </div>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <h:panelGroup id="confNegociacao" layout="block"
                                              styleClass="step1 containerConfNegociacao margin-container tudo">

                                    <h:panelGroup layout="block" styleClass="bg-cinza datasContrato">

                                        <h:panelGroup  layout="block"
                                                      style="display: inline-block;width: calc(50% - 40px);float: left;margin: 20px;">
                                            <h:outputText value="DATA LANÇAMENTO"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                                            <a4j:commandLink id="btnAlterarDataLancamento"
                                                             title="Alterar a data base do contrato."
                                                             styleClass="linkPadrao tooltipster"
                                                             reRender="panelConfirmarAlteracao"
                                                             oncomplete="Richfaces.showModalPanel('panelConfirmarAlteracao')">
                                                <h:outputText styleClass="fa-icon-edit texto-cor-azul texto-size-14"/>
                                            </a4j:commandLink>
                                            <h:panelGroup id="DataLancamento" layout="block" style="display: block">
                                                <h:outputText
                                                        styleClass="valorCampos texto-cor-cinza texto-font texto-size-14"
                                                        value="#{ContratoControle.dataBase_Apresentar}"/>

                                                <a4j:commandLink action="#{ContratoControle.limparDataBase}"
                                                               style="margin-left: 3px"
                                                               title="Limpar data base"
                                                                 reRender="confNegociacao,dados"
                                                               rendered="#{ContratoControle.dataBaseAlteradaSemRequest}"
                                                        styleClass="linkPadrao texto-cor-azul fa-icon-undo texto-size-14 tooltipster"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block"
                                                      style="display: inline-block;width: calc(50% - 40px);float: right;margin: 20px;">
                                            <h:outputText value="DATA RENOVAÇÃO"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                          rendered="#{ContratoControle.contratoVO.renovarContrato}"/>
                                            <h:outputText value="DATA INÍCIO"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                          rendered="#{ContratoControle.contratoVO.apresentarDataInicio}"/>
                                            <a4j:commandLink id="btnAlterarDataInicio"
                                                             title="Alterar a data início do contrato."
                                                             rendered="#{ContratoControle.contratoVO.apresentarDataInicio}"
                                                             styleClass="linkPadrao tooltipster"
                                                             reRender="formConfirmarAlteracaoInicioContrato"
                                                             oncomplete="Richfaces.showModalPanel('panelAlteracaoInicioContrato')">
                                                <h:outputText styleClass="fa-icon-edit texto-cor-azul texto-size-14"/>
                                            </a4j:commandLink>
                                            <h:panelGroup id="DataInicio" layout="block" style="display: block">
                                                <h:outputText
                                                        styleClass="valorCampos texto-cor-cinza texto-font texto-size-14"
                                                        rendered="#{ContratoControle.contratoVO.renovarContrato}"
                                                        value="#{ContratoControle.contratoVO.dataRenovarRealizada_Apresentar}"/>
                                                <h:outputText
                                                        styleClass="valorCampos texto-cor-cinza texto-font texto-size-14"
                                                        rendered="#{!ContratoControle.contratoVO.renovarContrato}"
                                                        value="#{ContratoControle.dataInicioContrato_Apresentar}"/>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="itemLinha" id="empresa"
                                                  style="margin-top: 20px;margin-bottom: 20px;"
                                                  rendered="#{ContratoControle.contratoVO.usuarioVO.administrador}">
                                        <h:outputText value="EMPRESA"
                                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                      style="display: block"/>
                                        <h:panelGroup layout="block" styleClass="cb-container"  style="margin-top: 10px;margin-bottom: 10px;height: 41px;width: 100%;">
                                            <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                                             value="#{ContratoControle.contratoVO.empresa.codigo}">
                                                <a4j:support event="onchange"
                                                             action="#{ContratoControle.montarListaSelectItemPlano}"
                                                             reRender="confNegociacao,dados"/>
                                                <f:selectItems value="#{ContratoControle.listaSelectItemEmpresa}"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="itemLinha"
                                                  style="margin-top: 20px;margin-bottom: 20px;">
                                        <h:outputText value="PLANO"
                                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                                        <h:outputLink styleClass="linkWiki tooltipster"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                      title="Clique e saiba mais: Negociação - Escolha do Plano"
                                                      target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                        <a4j:commandLink value="Tipo de Renovação: #{ContratoControle.contratoVO.opcaoSelecionadaRenovacaoPlanoCredito}"
                                                         id="linkTipoRenovacaoCredito"
                                                         action="#{ContratoControle.verificarRenovacaoAntecipadaPlanoCreditoParaNaoCredito}"
                                                         rendered="#{ContratoControle.renovacaoAntecipadaContratoCreditoParaNaoCredito}"
                                                         reRender="modalRenovarContratoCredito">

                                        </a4j:commandLink>
                                        <h:panelGroup id="pnlPlano" layout="block"
                                                      style="margin-top: 10px;margin-bottom: 10px;width: 100%;"
                                                      styleClass="cb-container">
                                            <h:inputHidden id="idPlanoCredito" value="#{ContratoControle.contratoVO.plano.vendaCreditoTreino}"></h:inputHidden>
                                            <h:inputHidden id="idMsgRenovacaoPlanoCredito" value="#{ContratoControle.contratoVO.informacaoRenovacaoCreditoTreino}"></h:inputHidden>
                                            <h:inputHidden id="idSituacaoContrato" value="#{ContratoControle.contratoVO.situacaoContrato}"></h:inputHidden>
                                            <h:selectOneMenu style="width: 100%" id="plano" onblur="blurinput(this);"
                                                             onfocus="focusinput(this);"
                                                             styleClass="texto-cor-cinza texto-font texto-size-16"
                                                             value="#{ContratoControle.contratoVO.plano.codigo}">
                                                <f:selectItems value="#{ContratoControle.listaSelectItemPlano}"/>
                                                <a4j:support event="onchange"
                                                             reRender="confNegociacao,dados,formModalAutorizacaoBolsa, modalRenovarContratoCredito,panelConsultarTurma,form:comboDuracaoCreditoTreino, form:pgModalidade"
                                                             action="#{ContratoControle.acaoSelecionarPlanoTelaNova}"
                                                             oncomplete="renderizarModalPermissao();mostrarOcultarMsgRenovacaoPlanoCredito();#{ContratoControle.msgAlert};#{ContratoControle.mensagemNotificar}"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <jsp:include page="includes/include_negociacao_pacote.jsp" flush="true"/>

                                    <h:panelGroup layout="block" id="pgDuracaoCreditoTreino"
                                                  rendered="#{ContratoControle.contratoVO.plano.vendaCreditoTreino}">
                                        <h:panelGrid columns="#{ContratoControle.contratoVO.plano.creditoSessao ? '1' : '2'}" width="80%">
                                            <h:outputText value="DURAÇÃO DO PLANO"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                          style="display: block;margin-top: 10px;margin-bottom: 10px"/>

                                            <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                          rendered="#{!ContratoControle.contratoVO.plano.creditoSessao}"
                                                          value="HORÁRIO"/>

                                            <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px">
                                                <h:selectOneMenu id="comboDuracaoCreditoTreino" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form"
                                                                 value="#{ContratoControle.codigoPlanoDuracaoSelecionado}">
                                                    <a4j:support event="onchange"
                                                                 reRender="comboDuracaoCreditoTreinoHorario, panelCreditoTreino,planoHorarioVO, tableDuracaoCreditoTreino,planoModalidadeVO, panelGridPlanoDuracaoVO, planoCondicaoPagamentoVO,panelAutorizacoesCobrancaCliente,
                                                                                                 detalhesNegociacao,duracao, total,total1,valorMensalModalidade, valorConvenioDescontoVO,modalidadeMarcada,horario,
                                                                                                 panelMesangem,apresentarDescontoConvenioPorValor,panelProdutoParcela, panelGroupRenovacaoRematricula,detalhesNegociacao, pgModalidade,pgDuracaoCreditoTreinoSessao"
                                                                 action="#{ContratoControle.acaoAlterarDuracaoCreditoTreino}"/>

                                                    <f:selectItems
                                                            value="#{ContratoControle.listaSelectItemDuracaoCreditoTreino}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>


                                            <h:panelGroup layout="block" styleClass="cb-container" style="height: 40px;"
                                                          rendered="#{!ContratoControle.contratoVO.plano.creditoSessao}">
                                                <h:selectOneMenu id="comboDuracaoCreditoTreinoHorario"
                                                                 onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form"
                                                                 value="#{ContratoControle.codigoHorarioCreditoTreinoSelecionado}">
                                                    <a4j:support event="onchange"
                                                                 reRender="confNegociacao,dados"
                                                                 action="#{ContratoControle.pesquisarPlanoDuracaoCreditoTreino}"/>
                                                    <f:selectItems
                                                            value="#{ContratoControle.listaSelectItemTipoHorarioCreditoTreino}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                        </h:panelGrid>

                                        <h:panelGroup id="panelCreditoTreino" layout="block" styleClass="panelMargin"
                                                      rendered="#{!ContratoControle.contratoVO.plano.creditoSessao}">

                                            <rich:dataTable id="tableDuracaoCreditoTreino" width="100%"
                                                            styleClass="tabelaSimplesCustom"
                                                            rendered="#{(ContratoControle.codigoPlanoDuracaoSelecionado > 0)}"
                                                            value="#{ContratoControle.listaPlanoDuracaoCreditoTreino}"
                                                            var="varPlanoDuracaoCreditoTreinoVO">
                                                <rich:column headerClass="colunaEsquerda" styleClass="colunaEsquerda">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="CRÉDITO TOTAL"/>
                                                    </f:facet>
                                                    <h:panelGroup layout="block" styleClass="checkbox-fa">
                                                      <a4j:commandLink action="#{ContratoControle.marcarPlanoDuracaoCreditoTreino}"
                                                                       styleClass="linkPadrao font-size-Em PLANO-DURACAO-CREDITO-TREINO-#{varPlanoDuracaoCreditoTreinoVO.codigo}"
                                                                       id="box"
                                                                     reRender="confNegociacao,dados">
                                                          <h:outputText styleClass="#{varPlanoDuracaoCreditoTreinoVO.selecionado ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'} texto-cor-azul texto-size-16"/>
                                                          <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza padding-5" style="font-family: Arial;"  value="#{varPlanoDuracaoCreditoTreinoVO.quantidadeCreditoCompra}" />
                                                      </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>

                                                <rich:column styleClass="colunaCentralizada" headerClass="colunaCentralizada"
                                                             rendered="#{ContratoControle.contratoVO.plano.creditoTreinoNaoCumulativo}">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="CRÉDITO MENSAL"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16"
                                                                  value="#{varPlanoDuracaoCreditoTreinoVO.quantidadeCreditoMensal} ">
                                                    </h:outputText>
                                                </rich:column>

                                                <rich:column styleClass="colunaCentralizada" headerClass="colunaCentralizada">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="VEZES/SEMANA"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16"
                                                            value="#{varPlanoDuracaoCreditoTreinoVO.numeroVezesSemana} ">
                                                    </h:outputText>
                                                </rich:column>

                                                <rich:column styleClass="colunaDireita" headerClass="colunaDireita">
                                                    <f:facet name="header">
                                                        <h:outputText  styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="VALOR"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16"
                                                            value="#{varPlanoDuracaoCreditoTreinoVO.valorUnitario} ">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                </rich:column>

                                                <rich:column styleClass="colunaDireita" headerClass="colunaDireita">
                                                    <f:facet name="header">
                                                        <h:outputText styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold" value="TOTAL"/>
                                                    </f:facet>
                                                    <h:outputText styleClass="texto-font texto-cor-cinza texto-size-16" value="#{varPlanoDuracaoCreditoTreinoVO.valorTotal} ">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                </rich:column>

                                            </rich:dataTable>

                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <jsp:include page="includes/include_negociacao_modalidade.jsp" flush="true"/>

                                    <h:panelGroup layout="block" id="pgDuracaoCreditoTreinoSessao"
                                                  style="text-align: right; background-color: #777; padding: 10px 10px 10px 0px;"
                                                  rendered="#{ContratoControle.contratoVO.plano.creditoSessao}">
                                        <h:outputText styleClass="texto-font texto-size-16 texto-cor-branco texto-bold"
                                                      value="TOTAL DE CRÉDITOS:"/>
                                        <h:outputText
                                                style="padding-left: 10px"
                                                styleClass="texto-font texto-size-16 texto-cor-branco"
                                                value="#{ContratoControle.totalCreditoSessao}"/>
                                    </h:panelGroup>

                                    <jsp:include page="includes/include_negociacao_produto.jsp" flush="true"/>

                                    <h:panelGroup id="pgConvenioDesconto" layout="block" styleClass="itemLinha">
                                        <h:panelGroup layout="block" style="margin-top: 20px;margin-bottom: 20px;"
                                                      rendered="#{ContratoControle.mostrarPanelModalidade && ContratoControle.contratoVO.plano.codigo > 0 }">
                                            <h:outputText value="CONVÊNIO"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"/>
                                            <h:outputLink styleClass="linkWiki tooltipster"
                                                          value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                          title="Clique e saiba mais: Negociação - Convênio"
                                                          target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                            <h:panelGroup id="selectConvenioDesconto" layout="block"
                                                          style="margin-top: 10px;margin-bottom: 10px;height: 40px;width: 100%;"
                                                          styleClass="cb-container">
                                                <h:selectOneMenu id="convenioDescontoVO" onblur="blurinput(this);"
                                                                 style="width:100%;" onfocus="focusinput(this);"
                                                                 styleClass="texto-cor-cinza texto-font texto-size-14"
                                                                 value="#{ContratoControle.contratoVO.convenioDesconto.codigo}">
                                                    <a4j:support event="onchange"
                                                                 reRender="#{ContratoControle.renderPanelSenhaDescontoConvenio}"
                                                                 oncomplete="#{ContratoControle.abrePanelSenhaDescontoConvenio}"
                                                                 action="#{ContratoControle.selecionarConvenioDescontoAtualizandoValores}"/>
                                                    <!-- reRender="confNegociacao,dados" -->
                                                    <f:selectItems value="#{ContratoControle.listaSelectItemConvenioDesconto}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>
                                            <h:panelGroup layout="block" style="margin:20px;">
                                                <a4j:repeat
                                                        value="#{ContratoControle.contratoVO.convenioDesconto.convenioDescontoConfiguracaoVOs}"
                                                        var="cd">
                                                    <h:panelGroup layout="block"
                                                                  style="width: 50%;display: inline-block;margin-bottom: 5px;">
                                                        <h:outputText
                                                                styleClass="texto-font texto-size-16 texto-cor-cinza-3"
                                                                value="Desconto para #{cd.duracao} #{cd.duracao >= 2 ? 'meses' : 'mês'}"/>
                                                        <h:outputText rendered="#{cd.apresentarPorcentagem}"
                                                                      style="margin-right: 20px;"
                                                                      styleClass="pull-right texto-font texto-size-16 texto-cor-cinza-3 texto-bold"
                                                                      value=" #{cd.porcentagemDesconto}%"/>
                                                        <h:outputText rendered="#{cd.apresentarValorEspecifico}"
                                                                      style="margin-right: 20px;"
                                                                      styleClass="pull-right texto-font texto-size-16 texto-cor-cinza-3 texto-bold"
                                                                      value=" #{ContratoControle.contratoVO.empresa.moeda} #{cd.valorDesconto}"/>
                                                    </h:panelGroup>
                                                </a4j:repeat>
                                            </h:panelGroup>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup id="pgDuracaoPlano" layout="block">
                                        <h:panelGroup layout="block"
                                                      rendered="#{!ContratoControle.contratoVO.plano.vendaCreditoTreino && not empty ContratoControle.contratoVO.plano.planoDuracaoVOs}">
                                            <h:outputText value="DURAÇÃO"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                          style="display: inline-block"/>
                                            <h:outputLink styleClass="linkWiki "
                                                          style="vertical-align: bottom;margin-left: 5px;"
                                                          value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                          title="Clique e saiba mais: Negociação - Duração do Plano" target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                            <rich:dataTable id="planoDuracaoVO" width="100%"
                                                            style="margin-top: 10px;margin-bottom: 10px"
                                                            styleClass="tabelaSimplesCustom grupoRadioButton"
                                                            value="#{ContratoControle.contratoVO.plano.planoDuracaoVOs}"
                                                            var="planoDuracao">
                                                <rich:column width="50%">
                                                    <h:panelGroup styleClass="radioButton" layout="block">

                                                        <h:selectBooleanCheckbox id="selectDuracaoPlano"
                                                                                 value="#{planoDuracao.duracaoEscolhida}"/>
                                                        <a4j:commandLink id="btnMarcarDuracao"
                                                                         styleClass="acaoSelecionar linkPadrao"
                                                                         action="#{ContratoControle.marcarDuracao}"
                                                                         reRender="confNegociacao,dados"
                                                                         oncomplete="notificarMensagemSemGrupoDescontoAplicavel();#{ContratoControle.mensagemNotificar}">
                                                            <h:outputText
                                                                    styleClass="#{planoDuracao.duracaoEscolhida ? 'fa-icon-circle-check' : 'fa-icon-circle-blank' } linkPadrao texto-size-16 texto-cor-cinza"/>
                                                            <h:outputText styleClass=" linkPadrao texto-size-16 texto-font texto-cor-cinza"
                                                                    style="font-family: Arial"
                                                                    value="#{planoDuracao.descricaoDuracao}"/>
                                                        </a4j:commandLink>


                                                    </h:panelGroup>
                                                </rich:column>
                                                <rich:column style="text-align: right;">

                                                    <h:outputText
                                                            styleClass="texto-font texto-size-14 texto-cor-cinza tooltipster"
                                                            title="Valor final da negociação"
                                                            value="#{ContratoControle.contratoVO.empresa.moeda} ">
                                                    </h:outputText>
                                                    <h:outputText
                                                            styleClass="texto-font texto-size-14 texto-cor-cinza tooltipster"
                                                            title="Valor Final do contrato"
                                                            value="#{planoDuracao.valorFinalContrato}">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>

                                                </rich:column>
                                                <rich:column style="text-align: right;">

                                                    <h:outputText styleClass="texto-font texto-size-14 texto-cor-cinza"
                                                                  value="#{planoDuracao.tipoOperacaoReducao ? ' - ' : ' + '}"/>

                                                    <h:outputText value="#{ContratoControle.contratoVO.empresa.moeda} "
                                                                  styleClass="texto-size-14 texto-font texto-cor-cinza"
                                                                  rendered="#{planoDuracao.apresentarValorEspecifico}"/>
                                                    <h:outputText value="#{planoDuracao.valorEspecifico}"
                                                                  styleClass="texto-size-14 texto-font texto-cor-cinza"
                                                                  rendered="#{planoDuracao.apresentarValorEspecifico}">
                                                        <f:converter converterId="FormatadorNumerico7Casa"/>
                                                    </h:outputText>

                                                    <h:outputText value="#{planoDuracao.percentualDesconto} "
                                                                  styleClass="texto-size-14 texto-font texto-cor-cinza"
                                                                  rendered="#{planoDuracao.apresentarValorDesconto}">
                                                        <f:converter converterId="FormatadorNumerico7Casa"/>
                                                    </h:outputText>
                                                    <h:outputText value=" %"
                                                                  styleClass="texto-size-14 texto-font texto-cor-cinza"
                                                                  rendered="#{planoDuracao.apresentarValorDesconto}"/>

                                                </rich:column>
                                            </rich:dataTable>
                                            <script>
                                                carregarEventosRadio();
                                            </script>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup id="panelDescontoAntecipado" styleClass="itemLinha"
                                                  style="margin-top: 20px;margin-bottom: 20px;">
                                        <h:panelGroup layout="block"
                                                      rendered="#{ContratoControle.mostrarDescontoRenovacaoAntecipada}">
                                            <h:outputText value="DESCONTO RENOVAÇÃO ANTECIPADA"
                                                          styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                          style="display: inline-block"/>
                                            <h:outputLink styleClass="linkWiki "
                                                          style="vertical-align: bottom;;margin-left: 5px;"
                                                          value="#{SuperControle.urlWiki}Inicial:Cadastros:Negociação#Desconto_de_Renova.C3.A7.C3.A3o_Antecipada"
                                                          title="Clique e saiba mais: Negociação - Desconto de Renovação Antecipada" target="_blank">
                                                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                            </h:outputLink>
                                            <rich:dataTable id="descontoAntecipado" width="100%"
                                                            style="margin-top: 10px;margin-bottom: 10px"
                                                            styleClass="tabelaSimplesCustom grupoRadioButton"
                                                            value="#{ContratoControle.listaDescontos}" var="desconto">

                                                <rich:column >
                                                    <h:panelGroup layout="block" styleClass="radioButton">
                                                        <h:selectBooleanCheckbox id="selectDescontoAntecipado"
                                                                                 value="#{desconto.descontoEscolhido}"/>
                                                        <a4j:commandLink styleClass="acaoSelecionar linkPadrao"
                                                                         id="descontoAntecipadoNegociacao"
                                                                         action="#{ContratoControle.marcarDesconto}"
                                                                         reRender="confNegociacao,dados,planoDuracaoVO">
                                                            <h:outputText style="width: 15px;"
                                                                    styleClass="tooltipster #{desconto.descontoEscolhido ? 'fa-icon-circle-check' : 'fa-icon-circle-blank'} textoImcompleto linkPadrao texto-size-16  texto-cor-cinza"
                                                                    title="#{desconto.descricao}"/>
                                                            <h:outputText
                                                                    styleClass="tooltipster textoImcompleto linkPadrao texto-size-16 texto-font texto-cor-cinza"
                                                                    style="font-family: Arial;width: 290px;margin-left: 5px;display: inline-block"
                                                                    title="#{desconto.descricao}"
                                                                    value="#{desconto.descricao}"/>
                                                        </a4j:commandLink>
                                                    </h:panelGroup>
                                                </rich:column>

                                                <rich:column width="20" styleClass="col-text-align-right">
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  value="#{desconto.tipoDesconto_Apresentar}"/>
                                                </rich:column>

                                                <rich:column styleClass="col-text-align-center" width="50" >
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  value="#{ContratoControle.contratoVO.empresa.moeda} "
                                                                  rendered="#{desconto.apresentarDescontoValor}"/>
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  value="#{desconto.valor} ">
                                                        <f:converter converterId="FormatadorNumerico"/>
                                                    </h:outputText>
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  value="%"
                                                                  rendered="#{desconto.apresentarDescontoPorcentagem}"/>
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  value=" dias."
                                                                  rendered="#{desconto.apresentarDescontoBonus}"/>
                                                </rich:column>

                                                <rich:column width="15%">
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  rendered="#{!ContratoControle.contratoVO.apresentarDiasAtrasoRenovacao}"
                                                                  value="#{desconto.nrDiasAntecipado} Dias Antecipados."/>
                                                    <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"
                                                                  rendered="#{ContratoControle.contratoVO.apresentarDiasAtrasoRenovacao}"
                                                                  value="#{desconto.nrDiasAntecipado} Dias Atrasados."/>
                                                </rich:column>
                                            </rich:dataTable>
                                            <script>
                                                carregarEventosRadio();
                                            </script>
                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup id="panelHorario" styleClass="itemLinha"
                                                  style="margin-top: 20px;margin-bottom: 20px;"
                                                  rendered="#{not empty ContratoControle.contratoVO.plano.planoHorarioVOs}">
                                        <h:outputText value="HORÁRIO"
                                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                      style="display: inline-block"/>
                                        <h:outputLink styleClass="linkWiki "
                                                      style="vertical-align: bottom;margin-left: 5px;"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-plano-normal-para-os-clientes/"
                                                      title="Clique e saiba mais: Negociação - Horário" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>
                                        <rich:dataTable id="planoHorarioVO"
                                                        styleClass="tabelaSimplesCustom grupoRadioButton"
                                                        style="margin-top: 10px;margin-bottom: 10px;height: 40px;width: 100%;"
                                                        width="100%"
                                                        value="#{ContratoControle.contratoVO.plano.planoHorarioVOs}"
                                                        var="planoHorario">
                                            <rich:column width="50%" styleClass="colunaEsquerda">
                                                <h:panelGroup styleClass="radioButton pull-left" layout="block">
                                                    <h:selectBooleanCheckbox id="selectDescontoAntecipado"
                                                                             value="#{planoHorario.horario.horarioEscolhida}"/>
                                                    <a4j:commandLink id="btnMarcarHorario"
                                                            styleClass="acaoSelecionar linkPadrao textoimcompleto"
                                                                     action="#{ContratoControle.marcarHorario}"
                                                                     disabled="#{ContratoControle.contratoVO.plano.vendaCreditoTreino}"
                                                                     reRender="confNegociacao,dados,planoDuracaoVO">
                                                        <h:outputText
                                                                styleClass="#{planoHorario.horario.horarioEscolhida ? 'fa-icon-circle-check' : 'fa-icon-circle-blank' } linkPadrao texto-size-16 texto-cor-cinza"/>
                                                        <h:outputText
                                                                styleClass="linkPadrao texto-size-16 texto-font texto-cor-cinza"
                                                                style="font-family: Arial"
                                                                value="#{planoHorario.horario.descricao}"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </rich:column>
                                            <rich:column width="25%"
                                                         rendered="#{planoHorario.apresentarValorEspecifico || planoHorario.apresentarValorDesconto }">
                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{planoHorario.tipoOperacao_Apresentar} "/>
                                            </rich:column>
                                            <rich:column width="25%" rendered="#{!planoHorario.apresentarValorDesconto }" styleClass="colunaDireita">

                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value=" "/>
                                            </rich:column>
                                            <rich:column width="25%"
                                                         rendered="#{planoHorario.apresentarValorEspecifico}" styleClass="colunaDireita">
                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{ContratoControle.contratoVO.empresa.moeda} "/>
                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value="#{planoHorario.valorEspecifico}">
                                                    <f:converter converterId="FormatadorNumerico"/>
                                                </h:outputText>
                                            </rich:column>
                                            <rich:column width="25%" rendered="#{!planoHorario.apresentarValorDesconto }" styleClass="colunaDireita">

                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value=" "/>
                                            </rich:column>
                                            <rich:column width="25%" rendered="#{planoHorario.apresentarValorDesconto}" styleClass="colunaDireita">
                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza"  value="#{planoHorario.percentualDesconto} ">
                                                    <f:converter converterId="FormatadorNumerico7Casa"/>
                                                </h:outputText>
                                                <h:outputText styleClass="texto-font texto-size-16 texto-cor-cinza" value=" %"/>
                                            </rich:column>

                                        </rich:dataTable>
                                        <script>
                                            carregarEventosRadio();
                                        </script>
                                    </h:panelGroup>

                                    <h:panelGroup id="camposDescontoRedesenhado" styleClass="itemLinha"
                                                  style="margin-top: 20px;margin-bottom: 20px;"
                                                  rendered="#{not empty ContratoControle.contratoVO.plano.planoHorarioVOs && ContratoControle.contratoVO.plano.aceitaDescontoExtra}">
                                        <h:outputText value="DESCONTO"
                                                      styleClass="texto-font texto-cor-cinza texto-size-14 texto-bold"
                                                      style="display:  inline-block"/>
                                        <h:outputLink styleClass="linkWiki "
                                                      style="vertical-align: bottom;margin-left: 5px;"
                                                      value="#{SuperControle.urlBaseConhecimento}tela-do-cliente-modulo-adm/"
                                                      title="Clique e saiba mais: Negociação - Desconto" target="_blank">
                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                        </h:outputLink>                                            
                                        
                                        <h:panelGroup layout="block">
                                            <h:panelGroup styleClass="grupoRadioButton" layout="block">
                                                <h:panelGroup layout="block"
                                                              styleClass="radioButton" style="padding: 1em;"
                                                              rendered="#{ContratoControle.contratoVO.possuiGrupoDesconto}">
                                                    <h:selectBooleanCheckbox disabled="true"/>
                                                    <a4j:commandLink styleClass="acaoSelecionar linkPadrao"
                                                                     disabled="true">
                                                        <h:outputText style="width: 15px;"
                                                                      styleClass="tooltipster fa-icon-circle-check textoImcompleto linkPadrao texto-size-16  texto-cor-cinza"/>
                                                        <h:outputText
                                                                styleClass="tooltipster textoImcompleto linkPadrao texto-size-16 texto-font texto-cor-cinza"
                                                                style="font-family: Arial;width: 290px;margin-left: 5px;display: inline-block"
                                                                value="#{ContratoControle.contratoVO.grupo.descricao}"/>
                                                    </a4j:commandLink>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" styleClass="cb-container"
                                                      style="margin-top: 10px;margin-bottom: 10px;height: 40px;width: 100%;">

                                            <h:selectOneMenu id="ProdutoDesconto" onblur="blurinput(this);"
                                                             style="width: 100%;"
                                                             onfocus="focusinput(this);" styleClass="form"
                                                             value="#{ContratoControle.contratoVO.desconto.codigo}">
                                                <f:selectItems
                                                        value="#{ContratoControle.listaConsultaProdutoDesconto}"/>
                                                <a4j:support event="onchange"
                                                             reRender="#{ContratoControle.updateComponente.valoresContrato},camposDescontoRedesenhado,formSenhaDesconto,panelValorDesconto, panelAutorizacaoFuncionalidade"
                                                             oncomplete="#{ContratoControle.mensagemNotificar}"
                                                             action="#{ContratoControle.gerenciarApresentarCamposDesconto}"/>
                                            </h:selectOneMenu>
                                        </h:panelGroup>

                                        <h:panelGroup layout="block" style="width:100%;">

                                            <h:panelGroup rendered="#{ContratoControle.apresentarCamposDesconto}"
                                                          style="height:40px;width: 30%;"
                                                          styleClass="cb-container input-group" layout="block">
                                                <h:selectOneMenu id="tipoDesconto" onblur="blurinput(this);"
                                                                 onfocus="focusinput(this);" styleClass="form"
                                                                 style="width:102%;"
                                                                 value="#{ContratoControle.contratoVO.tipoDesconto}">
                                                    <a4j:support event="onchange"
                                                                 action="#{ContratoControle.apresentarCamposDesconto}"
                                                                 reRender="semValorDesconto,valorDescontoPercentual,valorDescontoNumerico,panelValorDesconto,total,total1,detalhesNegociacao,panelProdutoParcela,valorMensalModalidade, panelMesangem,apresentarDescontoPorValor,camposDescontoRedesenhado,valorDescontoEspecifico,valorDescontoPorcentagem,modalidadeMarcada "/>
                                                    <f:selectItems
                                                            value="#{ContratoControle.listaSelectItemTipoDesconto}"/>
                                                </h:selectOneMenu>
                                            </h:panelGroup>

                                            <a4j:jsFunction name="calcularDescontoContrato"
                                                            action="#{ContratoControle.calcularDescontoContrato}"
                                                            oncomplete="#{ContratoControle.mensagemNotificar}"
                                                            reRender="planoDuracaoVO,mdlMensagemGenerica,#{ContratoControle.updateComponente.valoresContrato}"/>
                                            
                                            <h:panelGroup rendered="#{ContratoControle.apresentarCamposDesconto}" id="panelValorDesconto">
                                                <h:inputText id="valorDescontoNumerico"
                                                             rendered="#{ContratoControle.usarNumerico}"
                                                         styleClass="inputTextClean noborderleft"
                                                         value="#{ContratoControle.contratoVO.valorDesconto}"
                                                         onkeypress="return formatar_moeda(this,'.',',',event); "
                                                         onblur="blurinput(this);calcularDescontoContrato();"
                                                         onfocus="focusinput(this);"
                                                         style="width: calc(70% - 3px);vertical-align: middle;margin-left: 3px">
                                                <f:converter converterId="FormatadorNumerico7Casa"/>
                                                </h:inputText>
                                                
                                                <h:inputText id="valorDescontoPercentual"
                                                             rendered="#{ContratoControle.usarPorcentagem}"
                                                         styleClass="inputTextClean noborderleft"
                                                         maxlength="5" 
                                                         value="#{ContratoControle.contratoVO.valorDesconto}"
                                                         onkeypress="return formatar_percentuallimite(this,'.',',',event,5); "
                                                         onblur="blurinput(this);calcularDescontoContrato();"
                                                         onfocus="focusinput(this);"
                                                         style="width: calc(70% - 3px);vertical-align: middle;margin-left: 3px">
                                                <f:converter converterId="FormatarPercentual"/>
                                                </h:inputText>
                                            </h:panelGroup>

                                        </h:panelGroup>
                                    </h:panelGroup>

                                    <h:panelGroup layout="block" styleClass="separador"/>
                                    <h:panelGroup layout="block" id="containerOb">
                                        <a4j:commandLink styleClass="linkPadrao texto-font texto-size-14 texto-cor-azul"
                                                         rendered="#{!ContratoControle.exibirCamposObservacao}"
                                                         action="#{ContratoControle.exibirCampoObservacao}"
                                                         reRender="containerOb" value="Adicionar observação"/>
                                        <h:inputTextarea rendered="#{ContratoControle.exibirCamposObservacao}"
                                                         styleClass="inputTextClean noTransitions"
                                                         value="#{ContratoControle.contratoVO.observacao}"
                                                         rows="3"
                                                         style="width: 100%;max-width: 100%;"
                                                         onblur="blurinput(this);" onfocus="focusinput(this);"/>
                                    </h:panelGroup>
                                    <h:panelGroup layout="block" styleClass="separador"/>
                                </h:panelGroup>
                                <jsp:include page="includes/include_negociacao_dadosScroll.jsp" flush="true"/>
                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        <script>
            carregarTooltipster();
            atualizarEventosRenderizar();
        </script>
        <a4j:jsFunction reRender="panelAutorizacaoFuncionalidade" name="renderizarModalPermissao"/>
        <h:panelGroup layout="block" rendered="#{ContratoControle.contratoVO.informacaoRenovacaoCreditoTreino != null}">
            <script type="text/javascript">
                document.addEventListener('DOMContentLoaded', function () {
                    Notifier.custom('${ContratoControle.contratoVO.informacaoRenovacaoCreditoTreino}', 'Atenção', Notifier.MENS_INFO, false);
                }, false);

            </script>
        </h:panelGroup>
        <a4j:jsFunction action="#{DicasControle.marcarEsconder}" name="marcarExibirTutorial" reRender="containerFaqToid">
            <f:setPropertyActionListener value="#{DicasControle.exibirDicaTutorial}" target="#{DicasControle.naoMostrarMais}"/>
        </a4j:jsFunction>
        <h:panelGroup layout="block" rendered="#{DicasControle.exibirDicaTutorial}" id="containerFaqToid">
            <%--Retirado apenas o Passo a passo do FAQ para caso necessite colocar novamente, está o espaço reservado--%>
        </h:panelGroup>

        <h:inputHidden id="mensagemSemGrupoDescontoAplicavel" value="#{ContratoControle.contratoVO.mensagemSemGrupoDescontoAplicavel}"/>
    </h:form>
    <jsp:include page="includes/include_modal_dicas.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_renovacao_plano_credito.jsp" flush="true"/>
    <jsp:include page="includes/autorizacao/include_autorizacao_funcionalidade.jsp" flush="true"/>
    <jsp:include page="includes/include_panelMensagem_goBackBlock.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_mensagem_generica.jsp" flush="true"/>
    <jsp:include page="includes/include_modal_tipoProdutoAutorizacao.jsp" flush="true" />
</f:view>

<script type="application/javascript">
    function notificarMensagemSemGrupoDescontoAplicavel() {
        var value = document.getElementById('form:mensagemSemGrupoDescontoAplicavel').value;
        if (value != null && value !== undefined &&  value !== '') {
            Notifier.custom(value.toString(), 'Atenção', Notifier.MENS_WARN, false);
        }
    }
    document.addEventListener('DOMContentLoaded', function () {
        notificarMensagemSemGrupoDescontoAplicavel();
    }, false);
</script>
</body>
