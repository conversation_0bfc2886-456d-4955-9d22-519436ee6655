<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">
<link href="beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Tipo de plano"/>
    </title>
    <c:set var="titulo" scope="session" value="Tipo de plano"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}Cadastros:PPT:Plano#Dados_B.C3.A1sicos"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido_material.jsp"/>
        </f:facet>
        <hr style="border-color: #e6e6e6;"/>
        <h:form id="form">
            <h:panelGrid columns="1" width="100%">
                <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="Código" />
                    <h:panelGroup>
                        <h:inputText  id="codigo"
                                      size="10"
                                      maxlength="10"
                                      readonly="true"
                                      styleClass="camposSomenteLeitura"
                                      value="#{PlanoTipoControle.planoTipoVO.codigo}" />
                        <h:message for="codigo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="Nome" />

                    <h:panelGroup>
                        <h:inputText  id="nome"
                                      size="45"
                                      maxlength="45"
                                      styleClass="form"
                                      value="#{PlanoTipoControle.planoTipoVO.nome}" />
                        <h:message for="nome" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Ativo" />
                    <h:panelGroup>
                        <h:selectBooleanCheckbox id="ativo"
                                      styleClass="form"
                                      value="#{PlanoTipoControle.planoTipoVO.ativo}" />
                        <h:message for="ativo" styleClass="mensagemDetalhada"/>
                    </h:panelGroup>

                    <h:outputText  styleClass="tituloCampos" value="Tipo" />

                    <h:inputText  id="tipoplano"
                                  size="10"
                                  maxlength="10"
                                  styleClass="form"
                                  value="#{PlanoTipoControle.planoTipoVO.tipo}" />

                </h:panelGrid>

                <div class="tituloCampos" style="text-align: center;margin-top: 15px;">
                    <span>Códigos de operação financeira por tipo de produto: </span>
                </div>

                <a4j:repeat value="#{PlanoTipoControle.planoTipoVO.tiposProduto}" var="tipoProduto" >
                    <h:panelGrid columns="2"
                                 rowClasses="linhaImpar, linhaPar"
                                 columnClasses="classEsquerda, classDireita"
                                 width="100%">
                        <h:outputText  styleClass="tituloCampos" value="#{tipoProduto.tipoProdutoEnum.descricao}" />
                        <h:panelGroup>
                            <h:inputText size="45"
                                         maxlength="45"
                                         styleClass="form"
                                         value="#{tipoProduto.codigoOperacaoFinanceira}" />
                        </h:panelGroup>
                    </h:panelGrid>
                </a4j:repeat>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText value=" "/>
                        </h:panelGrid>
                        <h:commandButton  rendered="#{PlanoTipoControle.sucesso}" image="./imagens/sucesso.png"/>
                        <h:commandButton rendered="#{PlanoTipoControle.erro}" image="./imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagem"  value="#{PlanoTipoControle.mensagem}"/>
                            <h:outputText styleClass="mensagemDetalhada" value="#{PlanoTipoControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1"
                                 width="100%"
                                 columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <a4j:commandButton
                                    immediate="true"
                                    action="#{PlanoTipoControle.novo}"
                                    value="#{msg_bt.btn_novo}"
                                    alt="#{msg.msg_novo_dados}"
                                    accesskey="1"
                                    styleClass="botoes nvoBt btSec"/>

                            <a4j:commandButton action="#{PlanoTipoControle.gravar}"
                                               value="#{msg_bt.btn_gravar}"
                                               alt="#{msg.msg_gravar_dados}"
                                               accesskey="2"
                                               styleClass="botoes nvoBt"/>

                            <h:panelGroup id="grupoBtnExcluir">
                                <a4j:commandButton id="excluir"  reRender="mdlMensagemGenerica"
                                                   oncomplete="#{PlanoTipoControle.msgAlert}" action="#{PlanoTipoControle.confirmarExcluir}"
                                                   value="#{msg_bt.btn_excluir}" title="#{msg.msg_excluir_dados}" accesskey="3" styleClass="botoes nvoBt btSec btPerigo"/>
                            </h:panelGroup>


                            <a4j:commandButton immediate="true"
                                               action="#{PlanoTipoControle.inicializarConsulta}"
                                               value="#{msg_bt.btn_voltar_lista}"
                                               alt="#{msg.msg_consultar_dados}"
                                               accesskey="4"
                                               styleClass="botoes nvoBt btSec"/>

                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
    <%@include file="includes/include_modal_mensagem_generica.jsp"%>
</f:view>
<script>
    document.getElementById("form:nome").focus();
</script>