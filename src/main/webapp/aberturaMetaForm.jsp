<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<head><%@include file="/includes/include_import_minifiles.jsp" %></head>
<link href="./css/otimize.css" rel="stylesheet" type="text/css">

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<style type="text/css">
    body {
        margin: 0px;
        padding: 0px;
    }
</style>

<f:view>
    <jsp:include page="includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="#{msg_aplic.prt_AberturaMeta_tituloForm}"/>
    </title>

    <c:set var="titulo" scope="session" value="${msg_aplic.prt_AberturaMeta_tituloForm}"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlWiki}CRMWeb:Operações:AberturaDaMetaDoDia"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <f:facet name="header">
            <jsp:include page="topoReduzido.jsp"/>
        </f:facet>

        <h:form id="form">
            <h:commandLink action="#{AberturaMetaControle.liberarBackingBeanMemoria}" id="idLiberarBackingBeanMemoria" style="display: none" />
            <h:panelGrid id="panelGridForm" columns="1" styleClass="tabForm" width="100%">
                <h:panelGrid columns="1" style="height:25px; background-image:url('./imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{msg_aplic.prt_AberturaMeta_tituloForm}"/>
                </h:panelGrid>

                <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_codigo}" />
                    <h:panelGroup>
                        <h:inputText  id="codigo" size="10" maxlength="10" readonly="true" styleClass="camposSomenteLeitura" value="#{AberturaMetaControle.aberturaMetaVO.codigo}" />
                    </h:panelGroup>
                    <h:panelGroup>
       	    	        <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_colaboradorResponsavel}" />
        	            <h:inputText  id="colaboradorResponsavel" size="10" maxlength="10" styleClass="campos" value="#{AberturaMetaControle.aberturaMetaVO.colaboradorResponsavel}" />
                    </h:panelGroup>
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_responsavelCadastro}" />
                    <h:inputText  id="responsavelCadastro" size="10" maxlength="10" styleClass="campos" value="#{AberturaMetaControle.aberturaMetaVO.responsavelCadastro}" />
                    <h:outputText  styleClass="tituloCampos" value="#{msg_aplic.prt_AberturaMeta_dia}" />
                    <a4j:outputPanel>
                        <rich:calendar id="dia" oninputkeypress="return mascara(this.form, this.id, '99/99/9999', event);" value="#{AgendaControle.dataConsulta}" enableManualInput="true" popup="true" inputSize="10" datePattern="dd/MM/yyyy" showApplyButton="false" cellWidth="24px" cellHeight="24px" style="width:200px" inputClass="campos" showFooter="false"/>
                    </a4j:outputPanel>
                    <h:inputText  id="dia" onkeypress="return mascara(this.form, 'form:dia', '99/99/9999', event);" size="10" maxlength="10" styleClass="campos" value="#{AberturaMetaControle.aberturaMetaVO.dia}" >
                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                    </h:inputText>

                </h:panelGrid>

                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid id="panelGridMensagens" columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{AberturaMetaControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{AberturaMetaControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:panelGroup>
                            <h:commandButton id="salvar" action="#{AberturaMetaControle.gravar}" value="#{msg_bt.btn_gravar}" image="./imagensCRM/botaoGravar.png" title="#{msg.msg_gravar_dados}" accesskey="2" styleClass="botoes"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>        
    </h:panelGrid>
</f:view>
<script>
    document.getElementById("form:colaboradorResponsavel").focus();
</script>