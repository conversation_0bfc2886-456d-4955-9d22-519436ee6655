<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>

<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<link href="css_pacto.css" rel="stylesheet" type="text/css"/>
<link rel="icon" type="image/png" href=".${LoginControle.favIconModule}"/>

<style type="text/css">
    body {
        margin: 0;
        background-color: #fff;
        font-family: Arial, serif !important;
    }

</style>


<div align="center">
    <table width="100%" height="45" border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td align="left" valign="top">
                <h:panelGroup layout="block" styleClass="#{SuporteControle.classTopoCRM}"/>
            </td>
        </tr>
        <tr>
            <h:panelGrid columns="1" styleClass="tabitemmenu" width="100%">
                <h:outputLink value="#">
                    <h:outputText value="#{msg_menu.Menu_categoria}"/>
                </h:outputLink>
            </h:panelGrid>
        </tr>
    </table>
</div>

