<%@page pageEncoding="ISO-8859-1" %>
<%@include file="includes/imports.jsp" %>



<script type="text/javascript">
    $.noConflict();
</script>

<%-- Come<PERSON><PERSON> da Página  --%>
<h:form id="form">
    <rich:spacer height="25px;"></rich:spacer>
    <a href="#"
       class="expandirPlano">
     		   Expandir Tudo
    </a>
    &nbsp;
    <a href="#"
       class="expandirUmPlano">
     		   Expandir Um Nível
    </a>
    &nbsp;
    <a href="#"
       class="retrairUmPlano">
     		   Retrair Um Nível
    </a>
    &nbsp;
    <a href="#"
       class="retrairPlano">
     		   Retrair Tudo
    </a>

    <!-- ---------------------------------------------- INICIO - ARVORE DE DADOS ------------------------------------------------- -->
    <rich:panel id="dados">

        <table width="100%" >
            <tr>
                <td width="5%"><rich:spacer width="10px"></rich:spacer></td>


                <td width="95%">


                    <table  border="0"
                            class="planoContasTable"
                            id="dnd-planoContasTable"
                            cellspacing="0"
                            width="100%">
                        <thead>

                            <tr>

                                <td bgcolor="#FFFFFF" class="tituloBold" style="width: 45%;">

                                </td>

                                <td style="width: 8%; color: black;" class="codigoPlano" align="center">
                                    Código Interno
                                </td>

                                <c:if test="${PlanoContasControle.configuracaoLumi}">
                                    <td style="width: 8%; color: black;" class="codigoPlano" align="center">
                                        Código LUMI
                                    </td>
                                </c:if>


                                <td style="width: 15%; color: black;" class="equivalencia" align="center">
                                    Equivalência DRE
                                </td>
                                <td class="equivalencia" align="center" style="width: 15%; color: black;">
                                    Meta DRE
                                </td>
                                <td class="equivalencia" align="center" style="width: 8%;">
                                </td>
                            </tr>
                        <tbody>
                        </thead>
                            <c:forEach var="plano" varStatus="indice" items="${PlanoContasControle.listaPlanos}">

                                <c:if test="${not plano.rateio}">

                                    <%-- Definir as cores da tabela zebrada --%>
                                    <c:choose>
                                        <c:when test="${indice.count % 2 == 0}">
                                            <c:set var="corLinha" value="#FFFFFF" scope="request" />
                                            <c:set var="corLinhaRessaltada" value="#93DB70"
                                                   scope="request" />
                                        </c:when>
                                        <c:otherwise>
                                            <c:set var="corLinha" value="#DFE8EF" scope="request" />
                                            <c:set var="corLinhaRessaltada" value="#93DB70"
                                                   scope="request" />
                                        </c:otherwise>
                                    </c:choose>

                                    <c:choose>
                                        <c:when test="${fn:indexOf(plano.codigoPlano, '.') > 0}">
                                            <c:set var="noPai" value="${fn:substring(plano.codigoPlano,0, fn:length(plano.codigoPlano) -4)}" scope="request" />

                                            <tr bgcolor="${corLinha}" id="${plano.codigoNode}"
                                                onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                                onmouseout="mudar_cor(this,'${corLinha}');"

                                            class="child-of-${fn:replace(noPai,'.', '') + 0 }">


                                                <td class="tituloBold"   style="width: 35%;">
                                                    <c:out value="${plano.descricaoDetalhada}"></c:out>
                                                        <a href="#" name="naoMostrarBotaoJS"
                                                           title="Editar Plano de Contas"
                                                           class="fa fa-icon-pencil"
                                                       onclick="setarCodigoNivelPai('${noPai}', 'form:idPlanoPai'); preencherHiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${plano.codigo}"></c:out>')">
                                                    </a>
                                                     
                                                </td>

                                                <td class="codigoPlano" style="width: 8%;"  align="center">
                                                    <c:out value="${plano.codigo}"></c:out>
                                                </td>

                                                <c:if test="${PlanoContasControle.configuracaoLumi}">
                                                    <td class="codigoPlano" style="width: 8%;"  align="center">
                                                        <c:out value="${plano.codigoLumi}"></c:out>
                                                    </td>
                                                </c:if>


                                                <td class="equivalencia" style="width: 15%;"  align="center">
                                                       <c:out value="${plano.descricaoEquivalencia}"></c:out>
                                                </td>
                                                <td class="equivalencia" align="center" style="width: 15%;">
                                                      <c:out value="${plano.metaDescricaoDRE}"></c:out>
                                                </td>
                                            </tr>
                                        </c:when>
                                        <c:otherwise>
                                            <tr bgcolor="${corLinha}"
                                                onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                                onmouseout="mudar_cor(this,'${corLinha}');"
                                                id="${plano.codigoNode}">
                                                <td class="tituloBold">
                                                    <c:out value="${plano.descricaoDetalhada}"></c:out>
                                                    <a href="#" name="naoMostrarBotaoJS"
                                                       title="Editar Plano de Contas"
                                                       class="fa fa-icon-pencil"
                                                       onclick="preencherHiddenChamarBotao('form:chamaModalRateio','form:idEntidade','<c:out value="${plano.codigo}"></c:out>')">
                                                    </a>
                                                    <span class="previa"></span>

                                                </td>

                                                <td class="codigoPlano" style="width: 8%;"  align="center">
                                                    <c:out value="${plano.codigo}"></c:out>
                                                </td>

                                                <c:if test="${PlanoContasControle.configuracaoLumi}">
                                                    <td class="codigoPlano" style="width: 8%;"  align="center">
                                                        <c:out value="${plano.codigoLumi}"></c:out>
                                                    </td>
                                                </c:if>


                                                <td class="equivalencia" style="width: 15%;"  align="center">
                                                       <c:out value="${plano.descricaoEquivalencia}"></c:out>
                                                </td>
                                                <td class="equivalencia" align="center" style="width: 15%;">
                                                      <c:out value="${plano.metaDescricaoDRE}"></c:out>
                                                </td>
                                            </tr>
                                        </c:otherwise>
                                    </c:choose>
                                </c:if>

                                <c:if test="${plano.rateio}">
                                    <c:set var="noPai" value="${fn:substring(plano.codigoPlano,0, fn:length(plano.codigoPlano) -4)}" scope="request" />

                                    <tr bgcolor="${corLinha}" id="${plano.codigoNode}"
                                        onmouseover="mudar_cor(this,'${corLinhaRessaltada}');"
                                        onmouseout="mudar_cor(this,'${corLinha}');"
                                        class="child-of-${fn:replace(noPai,'.', '') + 0 }">
                                        <td class="tituloRateio">
                                            <c:if test="${not plano.existeRateio}">
                                                <c:out value="Rateio de Centro de Custos"></c:out>
                                            </c:if>
                                            <c:if test="${plano.existeRateio}">
                                                <!--  CENTRO DE CUSTOS -->
                                                <table border="0">
                                                    <tr>
                                                        <td class="titulotexto">Centro Custo</td>
                                                        <td class="titulotexto">%</td>
                                                    </tr>
                                                    <c:forEach items="${plano.listaRateiosCentroCustos}" var="previaRateio">
                                                        <tr>
                                                            <td class="texto">${previaRateio.nomeCentro}</td>
                                                            <td class="texto">${previaRateio.percentagem}</td>
                                                        </tr>
                                                    </c:forEach>
                                                </table>
                                            </c:if>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:forEach>

                        </tbody>
                    </table>
                </td>
            </tr>
        </table>
    </rich:panel>

    <h:inputHidden id="idEntidade" value="#{PlanoContasControle.codigoPlano}" />
    <h:inputHidden id="idPlanoPai" value="#{PlanoContasControle.codigoPlanoPaiSelecionado}" />
    <script>
        atualizarTreeViewPlanoContas(''+document.getElementById('form:idPlanoPai').value);
    </script>

    <!-- ---------------------------------------------- FIM - ARVORE DE DADOS ------------------------------------------------- -->
    <a4j:commandButton action="#{PlanoContasControle.editar}"
                       id="chamaModalRateio"
                       reRender="painelEdicaoPlanoContas"
                       style="visibility: hidden;"
                       oncomplete="Richfaces.showModalPanel('painelEdicaoPlanoContas');">
    </a4j:commandButton>

    <a4j:commandButton action="#{PlanoContasControle.montarArvorePlano}"
                       id="atualizaTreeDepoisRateio"
                       reRender="dados"
                       style="visibility: hidden;"
                       oncomplete="atualizarTreeViewPlanoContas('#{PlanoContasControle.codigoPlanoPaiSelecionado}');">
    </a4j:commandButton>


</h:form>

