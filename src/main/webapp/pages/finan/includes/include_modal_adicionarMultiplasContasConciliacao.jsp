<%-- HIBRAEL A ALVES - 21/10/2024 --%>
<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="imports.jsp" %>
<a4j:outputPanel>
  <rich:modalPanel id="modalPanelAdicionarMultiplasContasConciliacao" autosized="true"
                   styleClass="novaModal"
                   minWidth="720" height="350" width="720" shadowOpacity="true">
    <f:facet name="header">
      <h:panelGroup>
        <h:outputText value="Adicionar Múltiplas Contas de Conciliação"/>
      </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
     <a4j:form id="formFE">
        <a4j:commandButton id="btnFecharModalMultiplasContas" style="cursor:pointer; background:none; border:none; outline:none; position: relative; right: 13px;"
                           action="#{MovContaControle.fecharModalAdicionarMultiplasContas}"
                           oncomplete="#{rich:component('modalPanelAdicionarMultiplasContasConciliacao')}.hide();"
                           reRender="formAdicionarMultiplasContasConciliacao, form:panelDetalhesConciliacao, divDetalhesConciliacao">
          <f:facet name="label">
            <i class="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"></i>
          </f:facet>

        </a4j:commandButton>
      </a4j:form>
    </f:facet>
    <h:form id="formAdicionarMultiplasContasConciliacao">
      <h:panelGroup>
        <h:panelGrid id="panelInformarNovaContaConciliacao" columns="2" cellpadding="5" cellspacing="5">

          <h:outputLabel value="Conta a Pagar:" styleClass="tituloCampos"/>
          <h:inputText id="selecionarContaPagar" size="50" maxlength="80"
                       style="border-radius: 6px;"
                       disabled="#{MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo == 2}"
                       onkeypress="if (event.keyCode == 13) { document.getElementById('selecionarContaPagar:descricao').focus(); return false;};"
                       onfocus="focusinput(this);" styleClass="form #{!(MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo == 2) ? 'tooltipster' : ''}"
                       value="#{MovContaControle.pluggyTransactionAdicionarContas.movContaVO.descricao}"
                       title="Atenção! Para realizar o lançamento da Conta a Pagar desejada por esta tela, é necessário que a conta já esteja cadastrada no sistema.">
          </h:inputText>

          <rich:suggestionbox height="200" width="540"
                              for="selecionarContaPagar"
                              fetchValue="#{result}"
                              suggestionAction="#{MovContaControle.executarAutocompleteConsultaContasAPagarOuReceber}"
                              minChars="1" rowClasses="20"
                              status="true"
                              nothingLabel="Nenhuma conta a pagar encontrada!"
                              var="result" id="suggestionContaPagar">
            <a4j:support event="onselect" ignoreDupResponses="true"
                         reRender="formAdicionarMultiplasContasConciliacao, selecionarContaPagar, panelTableContasAdicionadas, panelInformarNovaContaConciliacao"
                         actionListener="#{MovContaControle.selecionarMovContaSuggestionBoxMultiplasContas}"
                         focus="descricao">
              <f:attribute name="transaction" value="#{MovContaControle.pluggyTransactionAdicionarContas}"/>
            </a4j:support>

            <h:column>
              <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="Descrição"/>
              </f:facet>
              <h:outputText value="#{result.descricao}"/>
            </h:column>
            <h:column>
              <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="Vencimento"/>
              </f:facet>
              <h:outputText value="#{result.dataVencimento_Apresentar}"/>
            </h:column>
            <h:column>
              <f:facet name="header">
                <h:outputText styleClass="textverysmall" value="Valor"/>
              </f:facet>
              <h:outputText value="R$ #{result.valorApresentar}"/>
            </h:column>
          </rich:suggestionbox>
          <rich:spacer width="15px"/>

        </h:panelGrid>

        <rich:separator height="2px"/>

        <h:outputText value="Contas a Pagar Selecionadas" styleClass="tituloCampos" style="display: block; width: 100%; font-size: 1.2em; text-align: center;"/>

        <%-- Tabela com as contas adicionadas --%>
        <h:panelGroup>
          <h:outputText value="Nenhuma conta adicionada"  styleClass="tituloCampos" style="display: block; width: 100%; font-size: 1.2em; text-align: center;"
            rendered="#{!MovContaControle.pluggyTransactionAdicionarContas.multiplasContasPreenchidas}"/>
        </h:panelGroup>

        <h:panelGroup id="panelTableContasAdicionadas" rendered="#{MovContaControle.pluggyTransactionAdicionarContas.multiplasContasPreenchidas}"
          style="max-height: 350px; overflow: auto;" layout="block">
          <table class="table-striped" id="tableContasAdicionadas" style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr>

                <th>
                  <h:outputText value="#{MovContaControle.labelContaPagarReceberConc}"/>
                </th>

                <th>
                  <h:outputText value="Vencimento"/>
                </th>

                <th>
                  <h:outputText value="Valor"/>
                </th>

                <th>
                  <h:outputText value="Plano de Contas"/>
                </th>

                <th>
                  <h:outputText value="Centro de Custo"/>
                </th>

                <th style="width: 8%;">
                  <h:outputText value="Ação" rendered="#{!(MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo == 2)}"/>
                </th>

              </tr>
            </thead>

            <tbody>
            <a4j:repeat value="#{MovContaControle.pluggyTransactionAdicionarContas.lstMovContaVO}" var="movConta">

              <tr>

                <td>
                  <h:outputText value="#{movConta.descricao}"/>
                </td>

                <td>
                  <h:outputText value="#{movConta.dataVencimento_Apresentar}"/>
                </td>

                <td>
                  <h:outputText value="#{movConta.valorApresentar}"/>
                </td>

                <td>
                  <h:outputText value="#{movConta.movContaRateios[0].planoContaVO.descricaoCurta}"/>
                </td>

                <td>
                  <h:outputText value="#{movConta.movContaRateios[0].centroCustoVO.descricaoCurta}"/>
                </td>

                <td>
                  <h:panelGroup rendered="#{!(MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo == 2)}">
                    <a4j:commandLink id="btnInformarPlanoContasCentroCusto"
                                     style="text-decoration: none !important;"
                                     reRender="formInformarPlanoContasCentroCusto"
                                     disabled="#{MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo == 2}"
                                     actionListener="#{MovContaControle.prepararDadosEAbrirModalInformarPlanoContasCentroCusto}"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                      <f:attribute name="movConta" value="#{movConta}"/>
                      <i class="fa-icon-edit tooltipster"
                         style="font-size: 14px; color: #1E60FA;"
                         title="Informar/Alterar Plano de Contas e Centro de Custa"></i>
                    </a4j:commandLink>
                    <h:outputText> | </h:outputText>
                    <a4j:commandLink id="btnRemoverContaSelecionada"
                                     style="text-decoration: none !important;"
                                     reRender="formAdicionarMultiplasContasConciliacao, selecionarContaPagar, panelTableContasAdicionadas, panelInformarNovaContaConciliacao"
                                     disabled="#{MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo == 2}"
                                     actionListener="#{MovContaControle.removerMovContaModalMultiplasContas}"
                                     oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
                      <f:attribute name="movConta" value="#{movConta}"/>
                      <i class="fa-icon-trash tooltipster"
                         style="font-size: 14px; color: #da2128;"
                         title="Remover Conta"></i>
                    </a4j:commandLink>
                  </h:panelGroup>
                </td>

              </tr>

            </a4j:repeat>
            </tbody>
          </table>
        </h:panelGroup>
      </h:panelGroup>

      <h:panelGrid columns="1" styleClass="centralizado" width="100%" style="margin-top: 6px">
        <h:panelGroup>

          <a4j:commandButton id="quitarMultiplasContas"
                             action="#{MovContaControle.prepararDadosEAbrirModalInformarFormaPagamentoMultiplasContas}"
                             rendered="#{MovContaControle.pluggyTransactionAdicionarContas.situacao.codigo != 2}"
                             value="Quitar/Conciliar"
                             reRender="formInformarFormaPagamentoMultiplasContas"
                             styleClass="botoes nvoBt"
                             style="background-color: #00c350 !important;"
                             oncomplete="#{MovContaControle.mensagemNotificar}#{MovContaControle.msgAlert}">
          </a4j:commandButton>

        </h:panelGroup>
      </h:panelGrid>

    </h:form>
  </rich:modalPanel>
</a4j:outputPanel>
