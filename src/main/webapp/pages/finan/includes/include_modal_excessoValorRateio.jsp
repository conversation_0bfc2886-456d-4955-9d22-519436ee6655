    <a4j:outputPanel>
        <rich:modalPanel id="modalPanelExcessoValorRateio" autosized="false" width="400" height="150"  shadowOpacity="true"  showWhenRendered="#{MovContaControle.abrirRichModalExcessoValorRateio}">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Confirma��o"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkExcessoValorRateio"/>
                    <rich:componentControl for="modalPanelExcessoValorRateio" attachTo="hidelinkExcessoValorRateio" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
            <h:form id="formExcessoValorRateio">
                <h:panelGrid width="100%">
                    <h:outputText styleClass="tituloCampos" value="O Valor do Lan�amento est� diferente da Soma das parcelas da Divis�o. Deseja ajustar o Valor do Lan�amento para a Soma das parcelas?"/>
                </h:panelGrid>
                <h:panelGrid width="100%" columnClasses="colunaCentralizada"  columns="2">
                    <a4j:commandButton id="confirmacao" reRender="modalPanelExcessoValorRateio, formLanc"
                                       action="#{MovContaControle.ajustarValorLancamento}"
                                       oncomplete="Richfaces.hideModalPanel('modalPanelExcessoValorRateio')"
                                       image="/imagens/OK_Modal.png"  styleClass="botoes">
                    </a4j:commandButton>
                    <a4j:commandButton id="cancelarConfirmacao"  action="#{MovContaControle.cancelarConfirmacao}"
                                       image="/imagens/Cancelar_Modal.png"
                                       oncomplete="Richfaces.hideModalPanel('modalPanelExcessoValorRateio');"
                                       reRender="panelMensagem"/>
                </h:panelGrid>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>