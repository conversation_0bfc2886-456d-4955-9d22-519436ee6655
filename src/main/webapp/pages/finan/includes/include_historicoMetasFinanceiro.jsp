<%-- 
    Document   : include_historicoMetasFinanceiro
    Created on : 13/02/2012, 08:52:38
    Author     : <PERSON><PERSON><PERSON>
--%>

<rich:modalPanel id="panelHistorico" autosized="true" shadowOpacity="true" width="750" height="450">
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="imagens/close.png" style="cursor:pointer" id="hidelink2"/>
            <rich:componentControl for="panelHistorico" attachTo="hidelink2" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formHistorico" ajaxSubmit="true">
        <h:panelGrid columns="1" rowClasses="linhaImpar, linhaPar" columnClasses="colunaEsquerda" width="100%" >
            <f:facet name="header">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;"
                             columnClasses="colunaCentralizada" width="100%">
                    <h:outputText value="Consulta Hist�rico de Metas" styleClass="tituloFormulario"/>
                </h:panelGrid>
            </f:facet>
            <h:panelGrid columns="5" width="100%" bgcolor="#EEEEEE" columnClasses="centralizado">
                <!-- EMPRESA -->
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_empresa}" 
                			   rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"/>
                <h:selectOneMenu id="empresa" styleClass="form" rendered="#{LoginControle.permissaoAcessoMenuVO.visualizarMetasFinanceirasTodasEmpresas}"
                                 value="#{MetaFinanceiroControle.empresaHistorico.codigo}" >
                    <f:selectItems value="#{MetaFinanceiroControle.listaSelectItemEmpresa}" />
                </h:selectOneMenu>

                <!-- MES/ANO -->
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Finan_MetaFinanceiro_anoMes}"/>
                <h:panelGroup id="de">
                    <h:inputText size="12" readonly="true"
                                 maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{MetaFinanceiroControle.apresentarPeriodoDe}" />
                    <rich:calendar value="#{MetaFinanceiroControle.periodoDe}"
                                   showInput="false" zindex="2" showWeeksBar="false">
                        <a4j:support event="onchanged" reRender="de"/>
                    </rich:calendar>

                    <h:outputText styleClass="tituloCampos" value=" at� "/>
                    <h:inputText size="12" readonly="true"
                                 maxlength="15" onblur="blurinput(this);" onfocus="focusinput(this);" styleClass="form"
                                 value="#{MetaFinanceiroControle.apresentarPeriodoAte}" />
                    <rich:calendar value="#{MetaFinanceiroControle.periodoAte}"
                                   showInput="false" zindex="2" showWeeksBar="false">
                        <a4j:support event="onchanged" reRender="de"/>
                    </rich:calendar>
                </h:panelGroup>

                <!-- BOTAO CONSULTA -->
                <a4j:commandButton id="consultar" styleClass="botoes" value="#{msg_bt.btn_consultar}"
                                   action="#{MetaFinanceiroControle.consultarHistoricoMetas}" reRender="listaHistorico, mensagens"
                                   image="/imagens/botaoConsultar.png" alt="#{msg.msg_consultar_dados}"/>
            </h:panelGrid>
            <rich:spacer height="10"/>

            <!-- RESULTADO DA CONSULTA -->
            <h:dataTable id="listaHistorico" width="100%" columnClasses="colunaEsquerda, centralizado, colunaEsquerda, colunaDireita,
                         colunaDireita, colunaDireita, colunaDireita, colunaDireita, colunaDireita, centralizado"
                         headerClass="subordinado" rowClasses="linhaImpar, linhaPar" value="#{MetaFinanceiroControle.historico}" var="meta">
                <!-- MES -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_mes}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.mes.descricao}"/>
                </h:column>

                <!-- ANO -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_ano}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.ano}"/>
                </h:column>

                <!-- DESCRICAO -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_descricao}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.descricao}"/>
                </h:column>

                <!-- META 1 -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta1}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.valores[0].valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <!-- META 2 -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta2}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.valores[1].valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <!-- META 3 -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta3}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.valores[2].valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <!-- META 4 -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta4}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.valores[3].valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <!-- META 5 -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_meta5}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.valores[4].valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <!-- META Atingida -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_atingida}" />
                    </f:facet>
                    <h:outputText styleClass="tituloCampos" value="#{meta.metaAtingida}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </h:column>

                <!-- OPCOES -->
                <h:column>
                    <f:facet name="header">
                        <h:outputText value="#{msg_aplic.prt_Finan_MetaFinanceiro_opcoes}" />
                    </f:facet>
                    <a4j:commandLink styleClass="botoes" value="Copiar" action="#{MetaFinanceiroControle.clonarHistorico}"
                                     oncomplete="Richfaces.hideModalPanel('panelHistorico')" reRender="form"/>
                </h:column>
            </h:dataTable>

            <rich:datascroller align="center" for="formHistorico:listaHistorico" maxPages="10"
                               id="scResultadoHistorico" />

            <!-- PANEL DE MENSAGENS -->
            <h:panelGrid id="mensagens" columns="1" width="100%" styleClass="tabMensagens">
                <h:outputText styleClass="mensagemDetalhada" value="#{MetaFinanceiroControle.mensagemDetalhada}" />
            </h:panelGrid>
        </h:panelGrid>
        <h:panelGroup layout="block" id="containerFuncMask">
            <script>
                carregarMaskInput();
            </script>
        </h:panelGroup>
    </a4j:form>
</rich:modalPanel>
