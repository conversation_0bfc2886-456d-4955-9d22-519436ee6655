<%@page pageEncoding="ISO-8859-1"%>
<%-- CONTA DEBITO --%>
<h:outputText rendered="#{MovContaControle.integracaoContabilAlterData}"
              value="Conta contábil devedora:"
              style="vertical-align: middle"
              styleClass="tituloCampos">

</h:outputText>
<h:panelGroup rendered="#{MovContaControle.integracaoContabilAlterData}">
    <h:inputText  id="nomeDebitoValor"
                  size="50"
                  style="vertical-align: middle"
                  maxlength="50"
                  onblur="blurinput(this);"
                  rendered="#{MovContaControle.integracaoContabilAlterData}"
                  onfocus="focusinput(this);"
                  styleClass="form"
                  value="#{MovContaControle.movContaVO.movContaContabilVO.contaContabilDebitoValor.descricao}" >
        <a4j:support event="onchange" action="#{MovContaControle.setarContaDebitoValorVazio}" reRender="form"/>
    </h:inputText>

    <rich:suggestionbox   height="200" width="400"
                          rendered="#{MovContaControle.integracaoContabilAlterData}"
                          for="nomeDebitoValor"
                          style="vertical-align: middle"
                          fetchValue="#{resultContaDevedoraValor.descricao}"
                          status="true"
                          nothingLabel="Nenhum registro  encontrado!"
                          suggestionAction="#{MovContaControle.executarAutocompleteConsultarContaContabil}"
                          minChars="1"
                          rowClasses="linhaImpar, linhaPar"
                          var="resultContaDevedoraValor"  id="suggestionDebitoValor">
        <a4j:support event="onselect"
                     reRender="form"
                     ignoreDupResponses="true"
                     action="#{MovContaControle.selecionarContaDevedoraValorSuggestionBox}">
        </a4j:support>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Descrição"  styleClass="textverysmall"/>
            </f:facet>
            <h:outputText styleClass="textverysmall" value="#{resultContaDevedoraValor.descricao}" />
        </h:column>
    </rich:suggestionbox>
</h:panelGroup>
