<%@include file="include_imports.jsp" %>

    <a4j:outputPanel>
        <rich:modalPanel id="modalConfirmaExclusaoPararAgendamento" autosized="true" width="600" height="130"  shadowOpacity="true" styleClass="novaModal">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Parar agendamento"></h:outputText>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:outputText
                            styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign"
                            id="hidelinkmodalConfirmaExclusaoPararAgendamento"/>
                    <rich:componentControl for="modalConfirmaExclusaoPararAgendamento"
                                           attachTo="hidelinkmodalConfirmaExclusaoPararAgendamento" operation="hide"  event="onclick" />
                </h:panelGroup>
            </f:facet>
            <h:form id="formConfirmaExclusaoPararAgendamento">
                <rich:panel>
                    <h:outputText styleClass="tituloDemonstrativo" id="msgConfirmacaoExclusao"
                                  value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoPararAgendamento}"/>
                    
                </rich:panel>
                <center style="margin: 15px;">
                    <a4j:commandLink id="sim"
                                       action="#{MovContaControle.excluirVindoDasParcelasFinalizando}"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusaoPararAgendamento');Richfaces.hideModalPanel('modalConfirmaExclusao');"
                                       reRender="form, formLanc" value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoPararAgendamentoConfirmar}"
                                       styleClass="botoes nvoBt">
                    </a4j:commandLink>

                    <a4j:commandLink id="nao"
                                       action="#{MovContaControle.excluirVindoDasParcelasSemValidar}"
                                       oncomplete="Richfaces.hideModalPanel('modalConfirmaExclusaoPararAgendamento');Richfaces.hideModalPanel('modalConfirmaExclusao');"
                                       reRender="form, formLanc" value="#{msg_aplic.prt_Finan_Lancamentos_confirmaExclusaoPararAgendamentoSomente}"
                                       styleClass="botoes nvoBt btSec">
                    </a4j:commandLink>


              	</center>
            </h:form>
        </rich:modalPanel>
    </a4j:outputPanel>
