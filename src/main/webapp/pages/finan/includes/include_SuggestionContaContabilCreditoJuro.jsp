<%@ taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%-- CONTA CREDITO --%>
<h:panelGroup id="pgLabelCreditoJuro">
    <h:outputText rendered="#{MovContaControle.integracaoContabilAlterData && MovContaControle.informarJuro}"
                  value="Conta cont�bil credora juro:"
                  style="vertical-align: middle"
                  styleClass="tituloCampos"></h:outputText>
</h:panelGroup>
<h:panelGroup id="sugestionCreditoJuro">
    <h:inputText  id="nomeCreditoJuro"
                  size="50"
                  style="vertical-align: middle"
                  maxlength="50"
                  onblur="blurinput(this);"
                  rendered="#{MovContaControle.integracaoContabilAlterData  && MovContaControle.informarJuro}"
                  onfocus="focusinput(this);"
                  styleClass="form"
                  value="#{MovContaControle.movContaContabilVO.contaContabilCreditoJuro.descricao}" >
        <a4j:support event="onchange" action="#{MovContaControle.setarContaCredoraJuroVazio}" reRender="form"/>
    </h:inputText>

    <rich:suggestionbox   height="200" width="400"
                          rendered="#{MovContaControle.integracaoContabilAlterData  && MovContaControle.informarJuro}"
                          for="nomeCreditoJuro"
                          status="true"
                          fetchValue="#{resultContaCredoraJuro.descricao}"
                          nothingLabel="Nenhum registro  encontrado!"
                          style="vertical-align: middle"
                          suggestionAction="#{MovContaControle.executarAutocompleteConsultarContaContabil}"
                          minChars="1"
                          rowClasses="linhaImpar, linhaPar"
                          var="resultContaCredoraJuro"  id="suggestionCreditoJuro">
        <a4j:support event="onselect"
                     reRender="form"
                     ignoreDupResponses="true"
                     focus="nomeDebitoJuro"
                     action="#{MovContaControle.selecionarContaCredoraJuroSuggestionBox}">
        </a4j:support>
        <h:column>
            <f:facet name="header">
                <h:outputText value="Descri��o"  styleClass="textverysmall"/>
            </f:facet>
            <h:outputText styleClass="textverysmall" value="#{resultContaCredoraJuro.descricao}" />
        </h:column>
    </rich:suggestionbox>
</h:panelGroup>


