<%-- 
    Document   : telaCaixa
    Created on : 19/03/2012, 10:53:22
    Author     : <PERSON><PERSON><PERSON>
--%>

<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />
<link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">

<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <a4j:keepAlive beanName="BloqueioCaixaControle"/>
    <h:form id="form">
        <!-- Inclui o elemento HEAD da pagina -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript" src="${contextoFinan}../script/telaInicial.js"></script>

        </head>

        <body>

        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item1" query="addClass('menuItemAtual')"/>
                <style>
                    .title-space{
                        margin: 10px 0;
                    }
                    .pure-button{
                        margin: 20px 10px 10px 0px;
                    }
                    div.cb-container{
                        margin-bottom: 20px;
                    }
                    .tabelaSimplesCustom:not(.noHover)>tbody>tr:hover {
                        background-color: #e5e5e5!important;
                        cursor: default;
                    }
                </style>

            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:outputText value="Bloqueio de caixa" styleClass="container-header-titulo"/>
                                        <h:outputLink styleClass="linkWiki"
                                                      value="#{SuperControle.urlBaseConhecimento}como-lancar-bloqueio-no-caixa-financeiro/"
                                                      title="Clique e saiba mais: Caixa Administrativo"
                                                      target="_blank" >
                                            <i class="fa-icon-question-sign" style="font-size: 18px; margin-left: 0.2em"></i>
                                        </h:outputLink>
                                    </h:panelGroup>
                                </h:panelGroup>
                                <h:panelGroup layout="block" styleClass="margin-box" style="display: table;"  id="formularioBloqueio">
                                    <h:panelGroup rendered="#{BloqueioCaixaControle.mostrarCampoEmpresa}"
                                                  layout="block"
                                                  styleClass="col-md-12">
                                        <div class="col-md-6">
                                            <div class="col-md-12 title-space">
                                                <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Empresa</span>
                                            </div>
                                            <div class="col-md-12 line-space">
                                                <div class="cb-container">
                                                    <h:selectOneMenu value="#{BloqueioCaixaControle.empresaSelecionada}">
                                                        <f:selectItems value="#{BloqueioCaixaControle.empresas}"/>
                                                        <a4j:support action="#{BloqueioCaixaControle.mudarEmpresa}"
                                                                     reRender="formularioBloqueio"
                                                                     event="onchange"/>
                                                    </h:selectOneMenu>
                                                </div>
                                            </div>
                                        </div>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{BloqueioCaixaControle.historico}">
                                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                              style="margin-bottom: 10px; display: block;">HIST�RICO DE BLOQUEIOS DE CAIXA</span>
                                        <rich:dataTable id="listaRecorrencia" width="100%"
                                                        headerClass="consulta"
                                                        columnClasses="" styleClass="tabelaSimplesCustom showCellEmpty"
                                                        rows="100"
                                                        value="#{BloqueioCaixaControle.bloqueios}" var="obj" >

                                            <rich:column width="15%" headerClass="col-text-align-left">
                                                <f:facet name="header">
                                                    <h:outputText value="Data bloqueio"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" value="#{obj.dataBloqueio}">
                                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                    </h:outputText>
                                                </h:panelGroup>
                                            </rich:column>
                                            <rich:column width="15%" headerClass="col-text-align-left" >
                                                <f:facet name="header">
                                                    <h:outputText value="Lan�amento"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" value="#{obj.lancamento}">
                                                        <f:convertDateTime pattern="dd/MM/yyyy HH:mm"/>
                                                    </h:outputText>
                                                </h:panelGroup>
                                            </rich:column>
                                            <rich:column width="30%" headerClass="col-text-align-left" >
                                                <f:facet name="header">
                                                    <h:outputText value="Empresa"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" value="#{obj.empresa.nome}"/>
                                                </h:panelGroup>
                                            </rich:column>
                                            <rich:column width="40%" headerClass="col-text-align-left" >
                                                <f:facet name="header">
                                                    <h:outputText value="Respons�vel"/>
                                                </f:facet>
                                                <h:panelGroup>
                                                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" value="#{obj.usuarioResponsavel.nomeAbreviado}"/>
                                                    <h:outputText styleClass="texto-size-16 texto-cor-cinza" rendered="#{not empty obj.userOamd}" value=" (#{obj.userOamd})"/>
                                                </h:panelGroup>
                                            </rich:column>

                                        </rich:dataTable>
                                        <a4j:commandLink styleClass="pure-button" 
                                                         action="#{BloqueioCaixaControle.sairHistorico}"
                                                         reRender="formularioBloqueio">
                                            <i class="fa-icon-chevron-left"></i>
                                            Voltar
                                        </a4j:commandLink>
                                    </h:panelGroup>
                                    <h:panelGroup rendered="#{!BloqueioCaixaControle.historico && BloqueioCaixaControle.bloqueioAtual != null && BloqueioCaixaControle.bloqueio == null}">
                                        <div class="col-md-12">
                                            <span class="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                                  style="margin-bottom: 10px; display: block;">BLOQUEIO ATUAL</span>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <h:panelGroup layout="block"  rendered="#{!BloqueioCaixaControle.mostrarCampoEmpresa}" 
                                                              styleClass="col-md-12 title-space">
                                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Empresa</span>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block"  rendered="#{!BloqueioCaixaControle.mostrarCampoEmpresa}"  
                                                              styleClass="col-md-12 line-space">
                                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                                  value="#{BloqueioCaixaControle.bloqueioAtual.empresa.nome}"/>
                                                </h:panelGroup>

                                                <div class="col-md-12 title-space">
                                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Data de bloqueio</span>
                                                </div>
                                                <div class="col-md-12 line-space">
                                                    <h:outputText styleClass="texto-size-20 texto-cor-cinza texto-font"
                                                                  value="#{BloqueioCaixaControle.bloqueioAtual.dataBloqueio}">
                                                        <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                    </h:outputText>
                                                </div>

                                                <div class="col-md-12 title-space">
                                                    <small class="texto-cor-cinza texto-font">Lan�ado em <h:outputText value="#{BloqueioCaixaControle.bloqueioAtual.lancamento}">
                                                            <f:convertDateTime pattern="dd/MM/yyyy '�s' HH:mm"/>
                                                        </h:outputText> por <h:outputText value="#{BloqueioCaixaControle.bloqueioAtual.usuarioResponsavel.nome}"/> 
                                                    <h:outputText rendered="#{not empty BloqueioCaixaControle.bloqueioAtual.userOamd}" value=" (#{BloqueioCaixaControle.bloqueioAtual.userOamd})"/>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <a4j:commandLink styleClass="pure-button pure-button-primary" 
                                                         action="#{BloqueioCaixaControle.novo}"
                                                         reRender="formularioBloqueio">
                                            <i class="fa-icon-plus"></i>
                                            Novo bloqueio
                                        </a4j:commandLink>
                                        <a4j:commandLink styleClass="pure-button" 
                                                         action="#{BloqueioCaixaControle.abrirHistorico}"
                                                         reRender="formularioBloqueio">
                                            <i class="fa-icon-history"></i>
                                            Hist�rico
                                        </a4j:commandLink>

                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{!BloqueioCaixaControle.historico && BloqueioCaixaControle.bloqueioAtual == null && BloqueioCaixaControle.bloqueio == null}">
                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <div class="col-md-12 title-space">
                                                    <span class="texto-size-16 texto-cor-cinza texto-font">Nenhum bloqueio de caixa foi cadastrado.</span>
                                                </div>
                                                <div class="col-md-12 line-space">
                                                    <a4j:commandLink styleClass="pure-button pure-button-primary" 
                                                                     action="#{BloqueioCaixaControle.novo}"
                                                                     reRender="formularioBloqueio">
                                                        <i class="fa-icon-plus"></i>
                                                        Novo bloqueio
                                                    </a4j:commandLink>
                                                </div>
                                            </div>
                                        </div>
                                    </h:panelGroup>

                                    <h:panelGroup rendered="#{!BloqueioCaixaControle.historico && BloqueioCaixaControle.bloqueio != null}">
                                        <span class="texto-size-14 texto-cor-cinza texto-font texto-bold"
                                              style="margin-bottom: 10px; display: block;">NOVO BLOQUEIO</span>

                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <h:panelGroup layout="block"  rendered="#{!BloqueioCaixaControle.mostrarCampoEmpresa}"
                                                              styleClass="col-md-12 title-space">
                                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Empresa</span>
                                                </h:panelGroup>
                                                <h:panelGroup layout="block"  rendered="#{!BloqueioCaixaControle.mostrarCampoEmpresa}"
                                                              styleClass="col-md-12 line-space">
                                                    <h:outputText styleClass="texto-size-14 texto-cor-cinza texto-font"
                                                                  value="#{BloqueioCaixaControle.bloqueio.empresa.nome}"/>
                                                </h:panelGroup>


                                                <div class="col-md-12 title-space">
                                                    <span class="texto-size-14 texto-cor-cinza texto-font texto-bold">Data de bloqueio</span>
                                                </div>
                                                <div class="col-md-12 line-space">
                                                    <h:panelGroup styleClass="dateTimeCustom">
                                                        <rich:calendar id="idInputDate"
                                                                       inputSize="10"
                                                                       value="#{BloqueioCaixaControle.bloqueio.dataBloqueio}"
                                                                       inputClass="form"
                                                                       oninputblur="blurinput(this);"
                                                                       oninputfocus="focusinput(this);"
                                                                       oninputchange="return validar_Data(this.id);"
                                                                       datePattern="dd/MM/yyyy"
                                                                       enableManualInput="true"
                                                                       buttonIcon="/imagens_flat/calendar-button.svg"
                                                                       zindex="2"
                                                                       showWeeksBar="false" />
                                                        <h:message for="idInputDate"  styleClass="mensagemDetalhada"/>
                                                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                                    </h:panelGroup>
                                                </div>
                                            </div>
                                        </div>
                                        <a4j:commandLink styleClass="pure-button pure-button-primary" 
                                                         action="#{BloqueioCaixaControle.gravar}"
                                                         oncomplete="#{BloqueioCaixaControle.msgAlert}"
                                                         reRender="formularioBloqueio,panelAutorizacaoFuncionalidade">
                                            <i class="fa-icon-save"></i>
                                            Gravar
                                        </a4j:commandLink>

                                        <a4j:commandLink styleClass="pure-button" 
                                                         action="#{BloqueioCaixaControle.limpar}"
                                                         reRender="formularioBloqueio">
                                            Cancelar
                                        </a4j:commandLink>



                                    </h:panelGroup>  
                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true" />
        </h:panelGroup>
        </body>
    </h:form>


    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="include_modalConfigGestaoRecebiveis.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>
</f:view>
