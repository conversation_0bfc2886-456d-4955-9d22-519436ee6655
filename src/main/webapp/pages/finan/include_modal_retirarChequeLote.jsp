<%@include file="includes/imports.jsp" %>
<rich:modalPanel id="modalRetirarChequeLote" autosized="true" shadowOpacity="true" width="400" height="150" styleClass="novaModal">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText  value="Retirar receb�vel do lote"/>
        </h:panelGroup>
    </f:facet>

    <f:facet name="controls">
        <h:panelGroup>
            <h:outputText
                    styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign btnSelecionarModalidade"
                    id="hidelinkRetirarLote"/>
            <rich:componentControl for="modalRetirarChequeLote" attachTo="hidelinkRetirarLote" operation="hide" event="onclick" />
        </h:panelGroup>
    </f:facet>

    <a4j:form id="formRetirarChequeLote">
        <h:panelGrid width="100%" columns="1">
            <h:outputText styleClass="tituloDemonstrativo"
                          rendered="#{GestaoLotesControle.apresentarListaChequesRetirados}"
                          value="#{msg_aplic.prt_finan_chequesSeremExcluidos}: "/>
            <h:outputText styleClass="tituloDemonstrativo"
                          rendered="#{GestaoLotesControle.apresentarListaCartoesRetirados}"
                          value="#{msg_aplic.prt_finan_cartoesSeremExcluidos}: "/>

            <rich:dataTable id="listaChequesRetirar" width="100%" styleClass="textverysmall" headerClass="subordinado"
                            columnClasses="colunaEsquerda, centralizado, centralizado, centralizado, centralizado,
                            centralizado, centralizado, colunaDireita, centralizado, centralizado"
                            rows="7"
                            value="#{GestaoLotesControle.chequesARetirar}" var="cheque"
                            rendered="#{GestaoLotesControle.apresentarListaChequesRetirados}">
                <rich:column>
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_finan_Pagador}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.primeiroNomePagador}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Cheque_banco}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.numeroBanco}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Cheque_agencia}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.agencia}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Cheque_conta}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.conta}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_numeroCheque}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.numero}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_finan_compensacao}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.dataCompensacao}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText>
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Cheque_valor}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cheque.valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </rich:column>
            </rich:dataTable>

            <rich:dataTable id="listaCartoesLote" width="100%" styleClass="textsmall"
                            columnClasses="colunaEsquerda, colunaEsquerda, centralizado, centralizado, centralizado,
                            colunaDireita, centralizado, centralizado"
                            headerClass="subordinado" rowClasses="linhaImpar, linhaPar"
                            rows="7"
                            value="#{GestaoLotesControle.cartoesARetirar}"
                            rendered="#{GestaoLotesControle.apresentarListaCartoesRetirados}" var="cartao">
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_nome}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cartao.nomePagador}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_OperadoraCartao_tituloForm}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cartao.operadora}" />
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataLancamento}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cartao.dataLancamento}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText>
                </rich:column>
                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_dataCompensacao}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cartao.dataCompensacao}">
                        <f:convertDateTime pattern="dd/MM/yyyy" />
                    </h:outputText>
                </rich:column>

                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Finan_GestaoRecebiveis_Autorizacao}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cartao.autorizacao}">
                    </h:outputText>
                </rich:column>

                <rich:column  >
                    <f:facet name="header">
                        <h:outputText style="font-weight: bold; font-size:9px;"
                                      value="#{msg_aplic.prt_Cheque_valor}" />
                    </f:facet>
                    <h:outputText style="font-weight: bold; font-size:9px;"
                                  value="#{cartao.valor}">
                        <f:converter converterId="FormatadorNumerico" />
                    </h:outputText>
                </rich:column>
            </rich:dataTable>
            <h:panelGrid columns="1" columnClasses="colunaCentralizada" width="100%">
                <h:panelGroup layout="block" style="margin: 0 auto;">
                        <rich:datascroller align="center"
                                           rendered="#{GestaoLotesControle.apresentarListaChequesRetirados}"
                                           for="listaChequesRetirar" maxPages="100"
                                           id="scResultadoListaChequeRetirar" />
                    <rich:datascroller align="center"
                                           rendered="#{GestaoLotesControle.apresentarListaCartoesRetirados}"
                                           for="listaCartoesLote" maxPages="100"
                                           id="scResultadoListaCartaoRetirar" />
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>

        <h:panelGrid width="100%" columns="1" style="vertical-align: top;">
            <h:outputText styleClass="tituloDemonstrativo" value="*#{msg_aplic.prt_Observacao}"/>
            <h:inputTextarea cols="45" style="width: 100%; height: 50px; resize: none;"
                             value="#{GestaoLotesControle.observacaoRetiradaLote}">
            </h:inputTextarea>
        </h:panelGrid>
        <div style="width: 100%; margin: 10px 0; text-align: center;">
            <a4j:commandLink value="Confirmar" action="#{GestaoLotesControle.confirmarExclusaoRecebivelLote}"
                             styleClass="pure-button inlineBlock pure-button-primary"
                             reRender="panelAutorizacaoFuncionalidade,listas, panelMensagem"
                             oncomplete="#{GestaoLotesControle.msgAlert}">
            </a4j:commandLink>
        </div>



    </a4j:form>
</rich:modalPanel>