<%@page contentType="text/html" %>
<%@page pageEncoding="ISO-8859-1" %>
<head>
    <%@include file="../../includes/include_import_minifiles.jsp"%>
    <script type="text/javascript" language="javascript" src="../../script/Notifier.js"></script>
</head>
<script type="text/javascript" language="javascript" src="../hoverform.js"></script>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
<title>Relatório Orçamentário</title>

<%@taglib prefix="f" uri="http://java.sun.com/jsf/core" %>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax" %>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich" %>

<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<%@include file="/includes/verificaModulo.jsp" %>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <c:set var="titulo" scope="session" value="Relatório Orçamentário"/>
    <c:set var="modulo" scope="request" value="financeiroWeb"/>
    <c:set var="urlWiki" scope="session" value="${SuperControle.urlBaseConhecimento}como-visualizar-o-relatorio-orcamentario/"/>
    <a4j:loadScript src="../../script/jquery.maskedinput-1.2.2.js"/>

    <f:facet name="header">
        <jsp:include page="../../topoReduzido_material.jsp"/>
    </f:facet>

    <h:form id="form" style="height: auto;overflow: visible;"  target="_blank">

        <h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar" columnClasses="classEsquerda, classDireita" width="100%" style="padding-left:0px;">
            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Período de Impressão:"/>
            <h:panelGroup styleClass="flex" layout="block">
                <div class="margenVertical">
                <h:panelGroup  styleClass="dateTimeCustom">
                    <rich:calendar id="dataInicioRelOrcamentario"
                                   value="#{RelatorioOrcamentarioControle.inicio}"
                                   inputSize="10"
                                   inputClass="form"
                                   oninputblur="blurinput(this);"
                                   oninputfocus="focusinput(this);"
                                   oninputchange="return validar_Data(this.id);"
                                   datePattern="dd/MM/yyyy"
                                   enableManualInput="true"
                                   zindex="2"
                                   showWeeksBar="false"
                                   buttonIcon="/imagens_flat/calendar-button.svg">
                    </rich:calendar>
                    <h:message for="dataInicioRelOrcamentario"  styleClass="mensagemDetalhada"/>
                </h:panelGroup>
            </h:panelGroup>

            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Tipo de Impressão:"/>
            <h:panelGroup
                    styleClass="font-size-em-max" >
                <div class="cb-container margenVertical">
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                     value="#{RelatorioOrcamentarioControle.tipoEmissao}">
                        <f:selectItem itemValue="1" itemLabel="1 mês" />
                        <f:selectItem itemValue="3" itemLabel="3 meses" />
                        <f:selectItem itemValue="6" itemLabel="6 meses" />
                        <f:selectItem itemValue="12" itemLabel="12 meses" />
                    </h:selectOneMenu>
                </div>
            </h:panelGroup>
            <h:outputText  styleClass="texto-size-14-real texto-cor-cinza texto-font texto-bold" value="Empresa:"/>
            <h:panelGroup
                    styleClass="font-size-em-max" >
                <div class="cb-container margenVertical">
                    <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                                     value="#{RelatorioOrcamentarioControle.empresaVO.codigo}">
                        <f:selectItems
                                value="#{RelatorioOrcamentarioControle.listaSelectItemEmpresa}" />
                        <a4j:support action="#{DemonstrativoFinanceiroControle.obterEmpresaEscolhida}" event="onchange"/>
                    </h:selectOneMenu>
                </div>
            </h:panelGroup>

        </h:panelGrid>

        <h:panelGrid columns="1" style="margin-top: 20px;" width="100%">
            <h:panelGrid id="mensagem" columns="1" width="100%" columnClasses="colunaEsquerda">
                <h:outputText styleClass="mensagemDetalhada"
                              value="#{RelatorioOrcamentarioControle.mensagemDetalhada}"/>
            </h:panelGrid>
            <h:panelGrid columns="1" width="100%"  columnClasses="colunaCentralizada">
                <h:panelGroup>
                    <a4j:commandLink
                            id="imprimirRelOrcamentario"
                            styleClass="pure-button pure-button-primary"
                            title="Imprimir PDF"
                            oncomplete="#{RelatorioOrcamentarioControle.msgAlert}"
                            actionListener="#{RelatorioOrcamentarioControle.exportarRelatorio}">
                        <f:attribute name="tipo" value="pdf"/>
                        <h:outputText style="font-size: 14px" value="Imprimir PDF"/>
                    </a4j:commandLink>

                    <a4j:commandLink id="btnExportExcel"
                                     styleClass="pure-button pure-button-primary"
                                     style="padding: 9px 15px;margin-left: 10px;"
                                     title="Gerar Excel"
                                     actionListener="#{RelatorioOrcamentarioControle.exportarExcel}"
                                     oncomplete="#{RelatorioOrcamentarioControle.urlRelOrcamentarioExcel}">
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos"
                                     value="#{RelatorioOrcamentarioControle.atributosExportarExcel}"/>
                        <f:attribute name="prefixo" value="RelOrcamentario"/>
                        <h:outputText style="font-size: 14px; color: white;" title="#{msg_aplic.prt_exportar_form_excel}" styleClass="btn-print-2 excel"/>
                    </a4j:commandLink>
                </h:panelGroup>
            </h:panelGrid>
        </h:panelGrid>
    </h:form>
</f:view>

<script type="text/javascript">
    document.getElementById("form:dataInicio").focus();
</script>
