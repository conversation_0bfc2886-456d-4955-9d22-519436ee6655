<%@page pageEncoding="ISO-8859-1" %>
<%--
    Document   : resumoContas
    Created on : 30/10/2012, 10:53:22
    Author     : <PERSON><PERSON>
--%>

<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session"/>
<link href="../../css/gestaoRecebiveis1.0.min.css" rel="stylesheet" type="text/css">
<link href="../../css/packcss1.0.min.css" rel="stylesheet" type="text/css">
<link href="../../css/pacto_flat_2.16.min.css" rel="stylesheet" type="text/css"/>
<link href="../../css_pacto.css" rel="stylesheet" type="text/css">
<link href="../../css/financeiro.css" rel="stylesheet" type="text/css">
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../css/tooltipster/tooltipster.css" rel="stylesheet" type="text/css"/>
<link href="../../css/tooltipster/tooltipster-light.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="../../script/tooltipster/jquery.tooltipster.min.js"></script>
<script type="text/javascript" language="javascript" src="../../bootstrap/jquery.js"></script>


<script>
    function atualizar() {
        document.getElementById('form:atualizar').click();
    }

    function fecharModalConciliar() {
        console.log("ACESSOU fecharModalConciliar - MODAL CONCILIAR SALDO");

        // let messageInfoDiv = document.getElementById('messageInfo');
        // let texto = messageInfoDiv.textContent || messageInfoDiv.innerText;
        // console.log("VALOR TEXTO messageInfoDiv: " + texto);

        setTimeout(function() {
            document.getElementById('hidelinkMCC').click();
            atualizar();
        }, 1000); // 1 segundo atraso para atualizar a tela e o botão fechar modal ficar disponível para click.
    }

</script>
<style type="text/css">
    .rich-table-row:hover {
        background-color: #CCC;
    }

    th.cabecalho {
        background-color: #3a7dc4 !important;
        height: 33px;
        font-family: Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        color: #FFF !important;
        border-color: #3a7dc4 !important;
        text-align: left;
    }

    .bordaEsquerda {
        height: 39px;
        padding-left: 5%;
        border-left: none;
        border-bottom: none;
        border-right: none;
        border-top: none;
        font-family: Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        color: #333 !important;
    }

    .textoLink {
        font-family: Helvetica, Arial, sans-serif !important;
        font-size: 14px !important;
        color: #333 !important;
    }

    .bordaBottomRight {
        border-bottom: solid 1px silver;
        border-right: solid 1px silver;
    }

    .padd {
        padding-left: 5% !important;
    }

    .movimentacao {
        background: url("./../../images/bot-movimentacao-32px-03.png") no-repeat 0 0;
        height: 32px;
        width: 32px;
    }

    .movimentacao:hover {
        background: url("./../../images/bot-movimentacao-32px-03.png") no-repeat 0 50%;
        height: 32px;
        width: 32px;
    }

    .movimentacao:active {
        background: url("./../../images/bot-movimentacao-32px-03.png") no-repeat 0 100%;
        height: 32px;
        width: 32px;
    }

    .botao-checkbox > span {
        font-size: 1vw;
    }
    .tituloQuitacao{
        left:30px;
        float: left;
        text-align: center;
        font-family: Arial, serif;
        font-size: 14px;
        font-weight:bold;
        color: #777;
    }
    .dataInicio{
        colspan:2;
        font-family: Arial, serif;
        font-size: 16px;
        color: #777;
    }

    .dataFim{
        colspan:2;
        font-family: Arial, serif;
        font-size: 16px;
        color: #777;
    }
</style>
<f:view>
    <jsp:include page="../../includes/include_carregando_ripple.jsp"/>
    <%@include file="includes/include_operacaoConta.jsp" %>
    <%@include file="includes/include_openBankTransferenciaMesmoBanco.jsp" %>
    <%@include file="includes/include_openBankTransferenciaOutrosBancos.jsp" %>
    <%@include file="includes/include_modalLancamentosDF.jsp" %>
    <%@include file="../../includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
    <rich:modalPanel id="panelExportarTabela" styleClass="novaModal" autosized="true" shadowOpacity="true" width="400" height="200">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Exportar resumo de contas"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:outputText
                        styleClass="linkPadrao texto-size-20 texto-cor-branco fa-icon-remove-sign" id="hiperlinkExportarTabela"/>
                <rich:componentControl for="panelExportarTabela" attachTo="hiperlinkExportarTabela"
                                       operation="hide"  event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formExportarTabela" ajaxSubmit="true" >
            <h:panelGrid columns="1" width="75%" cellpadding="8" styleClass="font-size-Em-max">
                <h:outputText style="margin-left: 110px;" styleClass="texto-size-14 texto-cor-cinza texto-font"  value="Selecione o formato:" rendered="#{not empty GerenciadorContaControle.listaMovimentacoes}"/>
                <h:outputText style="margin-left: 66px;" styleClass="texto-size-14 texto-cor-cinza texto-font texto-bold"  value="Não há dados a serem gerados" rendered="#{empty GerenciadorContaControle.listaMovimentacoes}"/>
                <h:panelGrid columns="2" style="margin-left: 60px;">
                    <a4j:commandLink id="exportarExcel"
                                       styleClass="pure-button pure-button-primary"
                                       style="float: right;"
                                       actionListener="#{ExportadorListaControle.exportar}"
                                       rendered="#{not empty GerenciadorContaControle.listaMovimentacoes}"
                                       oncomplete="abrirPopup('../../../UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','Transacoes', 800,200);#{ExportadorListaControle.msgAlert}"
                                       accesskey="2">
                        <f:attribute name="lista" value="#{GerenciadorContaControle.listaMovimentacoes}"/>
                        <f:attribute name="tipo" value="xls"/>
                        <f:attribute name="atributos" value="movContaVO=Codigo,dataQuitacaoMovConta=Data da Quitação,dataLancamentoMovConta=Data de Lançamento,descricao=Descrição,formaPagamento=Forma. Pagto,numeroDocumento=Nº Documento,NtipoEsDescricao=E/S,tipoOperacaoLancamentoDescricao=Operação,lote=Lote,valorFormatado=Valor,saldoFormatado=Saldo"/>
                        <f:attribute name="prefixo" value="ListaMovimentacoes"/>
                        <i class="fa-icon-file-excel"></i>
                        <h:outputText value="Excel"/>
                    </a4j:commandLink>
                    <a4j:commandLink id="exportarPdf"
                                     style="float: right;margin-left: 20px;"
                                     rendered="#{not empty GerenciadorContaControle.listaMovimentacoes}"
                                     styleClass="pure-button pure-button-primary"
                                     oncomplete="#{GerenciadorContaControle.msgAlert}">
                        <i class="fa-icon-file-pdf"></i>
                        <h:outputText value="PDF"/>
                    </a4j:commandLink>
                </h:panelGrid>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>

    <a4j:loadScript src="/script/jquery.maskedinput-1.2.2.js" />
    <h:form id="form">


        <!-- Inclui o elemento HEAD da pï¿½gina -->
        <head>
            <%@include file="includes/include_head_finan.jsp" %>
            <script type="text/javascript" language="javascript"
                    src="${contextoFinan}../script/telaInicial.js"></script>
        </head>
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_fin_flat.jsp" flush="true"/>
            </h:panelGroup>
            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">

                                <h:panelGroup styleClass="container-box-header" layout="block">
                                    <h:panelGroup layout="block" styleClass="margin-box">
                                        <h:panelGroup id="nometela">
                                            <h:outputText styleClass="container-header-titulo"  rendered="#{!GerenciadorContaControle.verMovimentacoes}"
                                                          value="#{msg_aplic.prt_Finan_Conta_ResumoConta}"/>
                                            <h:outputText styleClass="container-header-titulo"  rendered="#{GerenciadorContaControle.verMovimentacoes}"
                                                          value="#{msg_aplic.prt_Finan_Conta_Movimentacao} - #{GerenciadorContaControle.contaSelecionada.descricao}"/>
                                        </h:panelGroup>
                                    </h:panelGroup>
                                </h:panelGroup>

                                <style>
                                    .hdrSaldo{
                                        min-height: 130px;
                                        text-align: right !important;
                                    }
                                </style>

                                <h:panelGroup layout="block" styleClass="margin-box">
                                    <h:panelGroup id="painelResumoContas">
                                        <rich:dataTable id="items" width="100%"
                                                        headerClass="consulta"
                                                        styleClass="bordaBottomRight"
                                                        rowClasses="linhaPar,linhaImpar "
                                                        reRender="paginaAtual, paginaAtualTop,painelPaginacaoTop,painelPaginacao"
                                                        columnClasses="bordaEsquerda, bordaEsquerda, semBorda, bordaEsquerda direita,semBorda centralizado"
                                                        value="#{GerenciadorContaControle.listaContas}"
                                                        rows="100" var="conta"
                                                        cellpadding="0"
                                                        rendered="#{!GerenciadorContaControle.verMovimentacoes}"
                                                        cellspacing="0">
                                            <!-- Código -->
                                            <rich:column headerClass="cabecalho padd">
                                                <f:facet name="header">
                                                    <h:outputText value="#{msg_aplic.prt_codigo}"/>
                                                </f:facet>

                                                <a4j:commandLink
                                                        action="#{GerenciadorContaControle.selecionarConta}"
                                                        id="codigoConta"
                                                        reRender="painelResumoContas, nometela"
                                                        styleClass="textoLink">
                                                    <h:outputText value="#{conta.codigo}"/>
                                                </a4j:commandLink>

                                            </rich:column>
                                            <!-- Fim código -->
                                            <rich:column headerClass="cabecalho padd">
                                                <f:facet name="header">
                                                    <h:outputText
                                                            value="#{msg_aplic.prt_Finan_Conta_descricaoContaLimpo}"/>
                                                </f:facet>

                                                <a4j:commandLink styleClass="textoLink"
                                                                 action="#{GerenciadorContaControle.selecionarConta}"
                                                                 id="descricaoConta"
                                                                 reRender="painelResumoContas, nometela">
                                                    <h:outputText value="#{conta.descricao}"></h:outputText>
                                                </a4j:commandLink>

                                            </rich:column>

                                            <rich:column
                                                    style="background-color: #{conta.colorSaldo}; width: 2px;"
                                                    headerClass="cabecalho">


                                            </rich:column>

                                            <rich:column headerClass="cabecalho padd hdrSaldo" width="15%">
                                                <f:facet name="header" >
                                                    <h:outputText value="Saldo até #{GerenciadorContaControle.dtInicioApresentar}"/>
                                                </f:facet>
                                                <a4j:commandLink styleClass="textoLink"
                                                                 action="#{GerenciadorContaControle.selecionarConta}"
                                                                 id="saldoConta"
                                                                 reRender="painelResumoContas, nometela">
                                                    <h:outputText
                                                            value="#{conta.saldoAtualApresentar}"></h:outputText>
                                                </a4j:commandLink>


                                            </rich:column>

                                            <rich:column headerClass="cabecalho" width="15%">
                                                <table width="100%">
                                                    <tr>
                                                        <td width="100%" align="center"><a4j:commandLink
                                                                action="#{GerenciadorContaControle.selecionarConta}"
                                                                reRender="painelResumoContas, nometela">
                                                            <div class="movimentacao"></div>
                                                        </a4j:commandLink></td>
                                                    </tr>
                                                </table>

                                            </rich:column>
                                        </rich:dataTable>
                                        <br/>

                                        <!-- ------------------------ Movimentações Resumo de Contas ------------------------------- -->
                                        <h:panelGroup rendered="#{GerenciadorContaControle.verMovimentacoes}">
                                            <table cellpadding="3" style="width: 100%">
                                                <tr>
                                                    <td class="tituloQuitacao"  colspan="6">Quitação</td>
                                                </tr>
                                                <tr>

                                                    <td class="dataInicio" colspan="2">Data Início</td>
                                                    <td class="dataFim" colspan="2">Data Fim</td>
                                                </tr>

                                                <tr>
                                                    <td>
                                                        <div class="dateTimeCustom fonte-responsiva">
                                                            <rich:calendar id="dataIniAbertura"
                                                                           value="#{GerenciadorContaControle.inicio}"
                                                                           inputSize="6"
                                                                           inputClass="form"
                                                                           oninputblur="blurinput(this);"
                                                                           oninputfocus="focusinput(this);"
                                                                           oninputchange="return validar_Data(this.id);"
                                                                           enableManualInput="true"
                                                                           zindex="2"
                                                                           popup="true"
                                                                           datePattern="dd/MM/yyyy"
                                                                           showApplyButton="false"
                                                                           showWeeksBar="false"
                                                                           firstWeekDay="0"
                                                                           buttonIcon="/imagens_flat/calendar-button.svg"/>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Finan_Lancamentos_a}"/>
                                                    </td>
                                                    <td>
                                                        <div class="dateTimeCustom fonte-responsiva">
                                                            <rich:calendar id="dataFimAbertura"
                                                                           value="#{GerenciadorContaControle.fim}"
                                                                           inputSize="6"
                                                                           inputClass="form"
                                                                           oninputblur="blurinput(this);"
                                                                           oninputfocus="focusinput(this);"
                                                                           oninputchange="return validar_Data(this.id);"
                                                                           enableManualInput="true"
                                                                           zindex="2"
                                                                           popup="true"
                                                                           datePattern="dd/MM/yyyy"
                                                                           showApplyButton="false"
                                                                           showWeeksBar="false"
                                                                           firstWeekDay="0"
                                                                           buttonIcon="/imagens_flat/calendar-button.svg"/>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a4j:commandLink
                                                                id="atualizarMov"
                                                                oncomplete="#{GestaoRecebiveisControle.mensagemNotificar}"
                                                                reRender="painelResumoContas"
                                                                action="#{GerenciadorContaControle.atualizar}"
                                                                styleClass="pure-button pure-button-primary"
                                                                style="margin-right:10px; margin-left: 10px;"
                                                                title="#{msg.msg_consultar_dados}">
                                                            <i style="font-size: 14px" class="fa-icon-search"></i> &nbsp
                                                            <h:outputText style="font-size: 14px" value="Pesquisar"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                    <td style="width: 100%;">
                                                        <a4j:commandLink id="logMovimentacao"
                                                                         oncomplete="abrirPopup('../../visualizadorLogForm.jsp', 'VisualizadorLog', 785, 595);"
                                                                         styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                                         title="Visualizar Log"
                                                                         rendered="#{!GestaoRecebiveisControle.visaoConciliacao}"
                                                                         action="#{GerenciadorContaControle.realizarConsultaLogOperacaoConta}"
                                                                         style="margin-left: 10px; float: right;color:#29AAE2">
                                                            <i class="fa-icon-list"></i>
                                                        </a4j:commandLink>
                                                        <a4j:commandLink id="imprimirExtrato"
                                                                         action="#{GerenciadorContaControle.imprimir}"
                                                                         style="margin-left: 10px; float: right;color:#29AAE2"
                                                                         styleClass="tudo texto-cor-azul linkPadrao tooltipster"
                                                                         reRender="panelExportarTabela"
                                                                         oncomplete="Richfaces.showModalPanel('panelExportarTabela');">
                                                            <i class="fa-icon-print"></i>
                                                        </a4j:commandLink>
                                                    </td>
                                                </tr>
                                            </table>

                                            <br/>
                                            <div>
                                                <h:outputText rendered="#{GerenciadorContaControle.contaSelecionada.bancoOpenBankEnum.descricao eq 'STONE OPENBANK' &&
                                                                GerenciadorContaControle.contaStoneIncluida}"
                                                              styleClass="font-weight: bold; font-size: 12px; color: green"
                                                              value="Conta autenticada: #{GerenciadorContaControle.contaStoneDetalhes}"/>
                                            </div>
                                            <br/>

                                            <table width="100%">
                                                <tr>
                                                    <td align="left">
                                                        <h:outputText id="periodo"
                                                                      styleClass="tituloDemonstrativo"
                                                                      value="#{msg_aplic.prt_Finan_Lancamentos_periodo} #{GerenciadorContaControle.periodo}"/>
                                                    </td>
                                                    <td align="right">
                                                        <h:outputText id="saldoInicial"
                                                                      styleClass="tituloDemonstrativo"
                                                                      value="#{msg_aplic.prt_Finan_Conta_saldoAnterior}: #{MovPagamentoControle.empresaLogado.moeda} #{GerenciadorContaControle.saldoAnterior}"/>
                                                    </td>
                                                </tr>
                                            </table>

                                            <br/>
                                            <!-- ------------------------ Começo Detalhes Movimentações Resumo de Contas ------------------------------- -->
                                            <jsp:include page="includes/include_MovimentacoesResumoContas.jsp" flush="true"/>
                                            <!-- ------------------------ Fim Detalhes Movimentações Resumo de Contas ------------------------------- -->

                                            <!-- ------------------------ Começo Detalhes Movimentações OpenBank Resumo de Contas ------------------------------- -->
                                            <jsp:include page="includes/include_MovimentacoesOpenBankResumoContas.jsp" flush="true"/>
                                            <!-- ------------------------ Fim Detalhes Movimentações OpenBank Resumo de Contas ------------------------------- -->
                                            <br/>
                                            <h:panelGroup id="containerTotalizadores" styleClass="container-totalizador">
                                                <div class="col-md-3">
                                                    <div class="box-totalizador">
                                                        <div class="titulo-totalizador">Entrada</div>
                                                        <div class="corpo-totalizador">
                                                            <h:outputText
                                                                    style="color: #008000"
                                                                    value="#{MovPagamentoControle.empresaLogado.moeda} #{GerenciadorContaControle.entradaApresentar}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3" style="display: inline-block">
                                                    <div class="box-totalizador">
                                                        <div class="titulo-totalizador">Saída</div>
                                                        <div class="corpo-totalizador">
                                                            <h:outputText
                                                                    style="color: #da2128"
                                                                    value="#{MovPagamentoControle.empresaLogado.moeda} #{GerenciadorContaControle.saidaApresentar}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3" style="display: inline-block">
                                                    <div class="box-totalizador">
                                                        <div class="titulo-totalizador">Saldo final
                                                            <i class="fa-icon-question-sign tooltipster"
                                                               title="${MovPagamentoControle.titleSaldoFinalResumoContas}"
                                                               style="font-size: 18px"></i>
                                                        </div>
                                                        <div class="corpo-totalizador">
                                                            <h:outputText  style="color: #{GerenciadorContaControle.corExibirSaldoFinalResumoContas}"
                                                                    value="#{MovPagamentoControle.empresaLogado.moeda} #{GerenciadorContaControle.saldoApresentacao}"/>
                                                        </div>
                                                    </div>
                                                </div>
                                                <h:panelGroup id="containerDetalhesTotalizadores"
                                                              styleClass="col-md-3"
                                                              style="display: inline-block;"
                                                              rendered="#{not empty GerenciadorContaControle.listaMovimentacoesSelecionadas}">
                                                    <table width="100%" class="lista-totalizadores">
                                                        <tr>
                                                            <th style="text-align: left">Operação</th>
                                                            <th style="text-align: right">Valor</th>
                                                        </tr>
                                                        <c:forEach var="totalizadorTipoOperacao" items="#{GerenciadorContaControle.totalMovimentacoesSelecionadasPorTipoOperacao}">
                                                            <tr>
                                                                <td><h:outputText value="#{totalizadorTipoOperacao.label}"/></td>
                                                                <td style="text-align: right"><h:outputText value="#{totalizadorTipoOperacao.value}"/></td>
                                                            </tr>
                                                        </c:forEach>
                                                    </table>
                                                </h:panelGroup>
                                            </h:panelGroup>
                                            <table>
                                                <tr>
                                                    <td>
                                                        <a4j:commandLink id="voltarContas"
                                                                         rendered="#{!GerenciadorContaControle.vindoDoBI}"
                                                                         action="#{GerenciadorContaControle.resumoContas}"
                                                                         styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Voltar"/>
                                                        </a4j:commandLink>
                                                        <a4j:commandLink id="voltarBI"
                                                                         rendered="#{GerenciadorContaControle.vindoDoBI}"
                                                                         action="#{BIFinanceiroControle.inicializar}"
                                                                         styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Voltar"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                    <td>
                                                        <a4j:commandLink id="transferenciaResumoConta"
                                                                         action="#{GerenciadorContaControle.novaOperacao}"
                                                                         rendered="#{!GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}"
                                                                         oncomplete="#{GerenciadorContaControle.onCompleteNovaOperacao}"
                                                                         reRender="panelDeposito"
                                                                         styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Transferência"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                    <td>
                                                        <a4j:commandLink id="conciliarSaldo"
                                                                         action="#{GerenciadorContaControle.conciliarSaldo}"
                                                                         rendered="#{!GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}"
                                                                         oncomplete="#{GerenciadorContaControle.msgAlert}"
                                                                         reRender="modalConciliar"
                                                                         styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Conciliar Saldo"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                    <td>
                                                        <a4j:commandLink action="#{GerenciadorContaControle.transferirOpenBankMesmaConta}"
                                                                         rendered="#{GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}"
                                                                         oncomplete="#{GerenciadorContaControle.onCompleteNovaOperacao}"
                                                                         reRender="panelTransferenciaMesmoBanco"
                                                                         styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Transferência Mesmo Banco"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                    <td>
                                                        <a4j:commandLink action="#{GerenciadorContaControle.transferirOpenBankOutrosBancos}"
                                                                         rendered="#{GerenciadorContaControle.contaSelecionada.contaIntegracaoOpenBank}"
                                                                         oncomplete="#{GerenciadorContaControle.onCompleteNovaOperacao}"
                                                                         reRender="panelTransferenciaOutrosBancos"
                                                                         styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Transferência Outros Bancos"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                    <td>
                                                        <a4j:commandLink action="#{GerenciadorContaControle.abrirLancamentoRapido}"
                                                                       reRender="form"
                                                                         id="lancarContaResumoConta"
                                                                       styleClass="pure-button">
                                                            <h:outputText style="font-size: 14px" value="Lançar conta"/>
                                                        </a4j:commandLink>
                                                    </td>
                                                </tr>
                                            </table>
                                            <center>
                                                <a4j:commandButton id="atualizar"
                                                                 action="#{GerenciadorContaControle.atualizar}"
                                                                 reRender="form"
                                                                 style="display: none;"></a4j:commandButton>

                                            </center>
                                        </h:panelGroup>
                                        <!-- ------------------------ Fim Movimentações Resumo de Contas ------------------------------- -->

                                        <rich:jQuery id="mskData" selector=".rich-calendar-input" timing="onload" query="mask('99/99/9999')" />
                                    </h:panelGroup></td>
                                </h:panelGroup>

                            </h:panelGroup>
                        </h:panelGroup>
                        <jsp:include page="includes/include_box_menulateral.jsp" flush="true"/>
                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>

        </body>
    </h:form>

    <rich:modalPanel id="modalConciliar" autosized="true" width="380" height="250" shadowOpacity="true"
                     onshow="document.getElementById('formConciliar:txtValorConciliar').focus();">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText
                        value="#{msg_aplic.prt_Finan_Conta_Conciliar} - #{GerenciadorContaControle.contaSelecionada.descricao}"/>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkMCC"/>
                <rich:componentControl for="modalConciliar" attachTo="hidelinkMCC" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <h:form id="formConciliar">
            <h:panelGrid width="100%" columnClasses="colunaEsquerda" columns="2">

                <h:outputText styleClass="tituloDemonstrativo"
                              value="#{msg_aplic.prt_Finan_Conta_data_Conciliar}"></h:outputText>
                <rich:calendar id="dataIniAbertura"
                               value="#{GerenciadorContaControle.dataConciliar}"
                               inputSize="6"
                               inputClass="form"
                               oninputblur="blurinput(this);"
                               oninputfocus="focusinput(this);"
                               oninputchange="return validar_Data(this.id);"
                               datePattern="dd/MM/yyyy"
                               enableManualInput="true"
                               zindex="2"
                               showWeeksBar="false">
                    <a4j:support event="onchanged"
                                 action="#{GerenciadorContaControle.obterValorSaldoDataRetroativaConciliar}"
                                 reRender="formConciliar"></a4j:support>
                </rich:calendar>


                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_msg_usuario}"></h:outputText>
                <h:outputText styleClass="tituloDemonstrativo"
                              value="#{GerenciadorContaControle.usuarioLogado.nome}"></h:outputText>
                <h:outputText styleClass="tituloDemonstrativo"
                              value="#{msg_aplic.prt_Finan_Conta_saldo}"></h:outputText>
                <h:inputText styleClass="form" readonly="true"
                             value="#{GerenciadorContaControle.valorSaldoAtualConciliarFormatado}"></h:inputText>
                <h:outputText styleClass="tituloDemonstrativo"
                              value="#{msg_aplic.prt_Finan_Conta_Valor_Conciliar}"></h:outputText>
                <h:inputText styleClass="form" id="txtValorConciliar"
                             value="#{GerenciadorContaControle.valorConciliar}"
                             onkeyup="mascara_com_sinal_negativo(this);">
                    <f:converter converterId="FormatadorNumerico"/>
                </h:inputText>
                <h:outputText styleClass="tituloDemonstrativo" value="#{msg_aplic.prt_Observacao}"></h:outputText>
                <h:inputTextarea cols="22" style="height: 50px; resize: none;"
                                 value="#{GerenciadorContaControle.observacaoConciliar}"
                                 onkeypress="limitTextArea(this, 77)">
                </h:inputTextarea>
            </h:panelGrid>
            <br/>
            <h:outputText value="#{msg_aplic.prt_Finan_Conta_ConciliarMsg}"
                          styleClass="tituloDemonstrativo"></h:outputText>
            <br/>
            <center>
                <a4j:commandButton
                                   value="Confirmar" action="#{GerenciadorContaControle.confirmarConciliacaoConta}"
                                   oncomplete="#{GerenciadorContaControle.mensagemNotificar}; fecharModalConciliar();"
                                   reRender="panelAutorizacaoFuncionalidade"
                                   image="/images/finan/confirmar.png">
                </a4j:commandButton>
            </center>
        </h:form>

    </rich:modalPanel>

    <%@include file="includes/include_modalSelecaoPlanoConta.jsp" %>
    <%@include file="includes/include_modalSelecaoCentroCusto.jsp" %>
    <%@include file="includes/include_modal_abrirCaixa.jsp" %>
    <%@include file="include_modalConfigGestaoRecebiveis.jsp" %>
    <%@include file="includes/include_modal_consultarCaixa.jsp" %>
    <%@include file="includes/include_box_fecharCaixas.jsp" %>

</f:view>
