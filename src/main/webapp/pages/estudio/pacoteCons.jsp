<%@page contentType="text/html;charset=UTF-8" %>
<head><script type="text/javascript" language="javascript" src="../../script/script.js"></script></head>
<link href="../../css/otimize.css" rel="stylesheet" type="text/css">
<link href="../../beta/css/font-awesome_2.0.min.css" type="text/css" rel="stylesheet"/>
<script type="text/javascript" language="javascript" src="hoverform.js"></script>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<%@taglib prefix="rich" uri="http://richfaces.ajax4jsf.org/rich"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <title>
        <h:outputText value="Pacote"/>
    </title>
    <c:set var="titulo" scope="request" value="Pacote"/>
    <c:set var="urlWiki" scope="request" value="${SuperControle.urlBaseConhecimento}como-cadastrar-pacote-para-o-modulo-agenda-studio/"/>
    <f:facet name="header">
        <jsp:include page="../../topoReduzido_material.jsp"/>
    </f:facet>
    <hr style="border-color: #e6e6e6;"/>
    <h:panelGrid columns="1" width="100%" cellpadding="0" cellspacing="0">
        <h:form id="form">
            <h:panelGrid columns="1"  width="100%">
                <h:panelGrid columns="4" footerClass="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
                    <h:selectOneMenu onblur="blurinput(this);"  onfocus="focusinput(this);"
                                     styleClass="form" id="consulta" required="true" value="#{pacoteControle.controleConsulta.campoConsulta}">
                        <f:selectItems value="#{pacoteControle.tipoConsultaCombo}" />
                    </h:selectOneMenu>
                    <h:inputText id="valorConsulta" onblur="blurinput(this);"  onfocus="focusinput(this);" styleClass="form"
                                 value="#{pacoteControle.controleConsulta.valorConsulta}"/>
                    <h:commandButton id="consultar" type="submit" styleClass="botoes nvoBt btSec" value="#{msg_bt.btn_consultar}" action="#{pacoteControle.irPaginaInicial}"
                                     alt="#{msg.msg_consultar_dados}" accesskey="2" />
                </h:panelGrid>

                <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{pacoteControle.listaConsulta}" rendered="#{pacoteControle.apresentarResultadoConsulta}" rows="10" var="pacote">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Composicao_codigo}"/>
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="codigo" value="#{pacote.codigo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Titulo"/>
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="titulo" value="#{pacote.titulo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Valor" />
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="valor">
                            <h:outputText value="#{pacote.valorTotal}">
                                <f:converter converterId="FormatadorNumerico" />
                            </h:outputText>
                        </h:commandLink>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Validade Inicial"/>
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="valIni" >
                            <h:outputText value="#{pacote.validadeInicial}" converter="dataConverter"/>
                        </h:commandLink>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Validade Final"/>
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="valFin" >
                            <h:outputText value="#{pacote.validadeFinal}" converter="dataConverter"/>
                        </h:commandLink>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Desativado"/>
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="desat" >
                            <h:outputText value="#{pacote.desativado_Apresentar}"/>
                        </h:commandLink>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Genérico"/>
                        </f:facet>
                        <h:commandLink action="#{pacoteControle.editar}" id="gener" >
                            <h:outputText value="#{pacote.generico_Apresentar}"/>
                        </h:commandLink>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_bt.btn_opcoes}"/>
                        </f:facet>
                        <h:commandButton id="btnEditarPacote" action="#{pacoteControle.editar}" 
                                         value="#{msg_bt.btn_editar}" image="#{context}/imagens/botaoEditar.png"
                                         alt="#{msg.msg_editar_dados}" styleClass="botoes"/>
                    </h:column>
                </h:dataTable>
                <h:panelGrid width="100%" footerClass="colunaCentralizada">
                    <f:facet name="footer">
                        <h:panelGroup rendered="#{pacoteControle.apresentarResultadoConsulta}" binding="#{pacoteControle.apresentarLinha}">
                            <h:commandLink styleClass="tituloCampos" value="  <<  " rendered="false" binding="#{pacoteControle.apresentarPrimeiro}"
                                           action="#{pacoteControle.irPaginaInicial}"/>
                            <h:commandLink styleClass="tituloCampos" value="  <  " rendered="false" binding="#{pacoteControle.apresentarAnterior}"
                                           action="#{pacoteControle.irPaginaAnterior}"/>
                            <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{pacoteControle.paginaAtualDeTodas}" rendered="true"/>
                            <h:commandLink styleClass="tituloCampos" value="  >  " rendered="false" binding="#{pacoteControle.apresentarPosterior}"
                                           action="#{pacoteControle.irPaginaPosterior}"/>
                            <h:commandLink styleClass="tituloCampos" value="  >>  " rendered="false" binding="#{pacoteControle.apresentarUltimo}"
                                           action="#{pacoteControle.irPaginaFinal}"/>
                        </h:panelGroup>
                    </f:facet>
                </h:panelGrid>
                <h:panelGrid columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="3" width="100%" styleClass="tabMensagens">
                        <h:panelGrid columns="1" width="100%">

                            <h:outputText value=" "/>

                        </h:panelGrid>
                        <h:commandButton  rendered="#{pacoteControle.sucesso}" image="../../imagens/sucesso.png"/>
                        <h:commandButton rendered="#{pacoteControle.erro}" image="../../imagens/erro.png"/>
                        <h:panelGrid columns="1" width="100%">
                            <h:outputText id="msgPacote" styleClass="mensagem"  value="#{pacoteControle.mensagem}"/>
                            <h:outputText id="msgPacoteDet" styleClass="mensagemDetalhada" value="#{pacoteControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                        <h:commandButton id="novo" action="#{pacoteControle.acaoNovoPacote}" value="#{msg_bt.btn_novo}" styleClass="botoes nvoBt btSec" alt="#{msg.msg_novo_dados}" accesskey="1"/>
                    </h:panelGrid>
                </h:panelGrid>
            </h:panelGrid>
        </h:form>
    </h:panelGrid>
</f:view>