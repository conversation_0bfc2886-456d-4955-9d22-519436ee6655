<%-- 
    Document   : indisponibilidadeAmbiente
    Created on : May 7, 2012, 10:31:59 AM
    Author     : <PERSON>eo<PERSON>
--%>
<%@include file="includes/include_imports.jsp" %>
<!DOCTYPE html>
<html>
    <head>
        <script type="text/javascript" src="${contexto}/script/basico.js"></script>
        <link href="${contexto}/css/estudio.css" rel="stylesheet" type="text/css">        
    </head>
    <body>
        <h:panelGrid id="panelIndisponibilidadeAmbiente" columns="1" width="100%" headerClass="subordinado" columnClasses="colunaCentralizada">
            <f:facet name="header">
                <h:outputText value="Indisponibilidade"/>
            </f:facet>
            <h:panelGrid columns="2"  columnClasses="classEsquerda, classDireita" width="100%">
                <h:outputLabel styleClass="texto_disponibilidade"
                               value="Dia da Semana: "/>

                <h:selectOneMenu styleClass="texto_disponibilidade"
                                 id="diaDaSemanaSelect"
                                 onkeydown="return tabOnEnter(event, 'configuracaoEstudioControle-ambienteIndispVO-diaMesInputDate');"
                                 converter="diaDaSemanaConverter"
                                 value="#{configuracaoEstudioControle.ambienteIndispVO.diaSemana}">
                    <f:selectItem  itemLabel="Selecione o dia da semana" itemValue=""/>
                    <f:selectItems value="#{configuracaoEstudioControle.diaDaSemanaSelect}" />
                </h:selectOneMenu>

                <h:outputLabel
                    value="Dia M�s:" styleClass="texto_disponibilidade" />
                <rich:calendar
                    locale="pt_BR"
                    inputSize="10"
                    inputClass="form"
                    oninputblur="blurinput(this);"
                    oninputfocus="focusinput(this);"
                    oninputchange="return validar_Data(this.id);"
                    oninputkeypress="return mascara(this, '99/99/9999', event); "
                    oninputkeydown="return tabOnEnter(event, 'hInicial');"
                    datePattern="dd/MM/yyyy"
                    enableManualInput="true"
                    zindex="99"
                    showWeeksBar="false"
                    value="#{configuracaoEstudioControle.ambienteIndispVO.diaMes}"
                    id="configuracaoEstudioControle-ambienteIndispVO-diaMes"
                    popup="true"  styleClass="texto_disponibilidade">
                </rich:calendar>
                <h:outputText styleClass="texto_disponibilidade"
                              value="Hora Inicial:"/>
                <h:panelGrid columns="2">

                    <rich:inputNumberSpinner id="hInicial" 
                                             value="#{configuracaoEstudioControle.horaInicioIndispAmbiente}" 
                                             maxValue="24"/>
                    <h:outputText 
                        styleClass="texto_disponibilidade"
                        value=":00 (h)"/>
                </h:panelGrid>


                <h:outputText styleClass="texto_disponibilidade"
                              value="Hora Final:"/>
                <h:panelGrid columns="2">
                    <rich:inputNumberSpinner id="hFinal" 
                                             value="#{configuracaoEstudioControle.horaFinalIndispAmbiente}" 
                                             minValue="1"
                                             maxValue="24"/>
                    <h:panelGrid columns="3">
                        <h:outputText 
                            styleClass="texto_disponibilidade"
                            value=":00 (h)"/>
                        <rich:spacer width="10px"/>
                        <a4j:commandLink
                            styleClass="botoes nvoBt btSec"
                            value="Adicionar"
                            id="add"
                            focus="panelFiltroServico-servico-codigo"
                            reRender="configuracaoEstudioControle-listaAmbienteIndisp,diaDaSemanaSelect,configuracaoEstudioControle-ambienteIndispVO-diaMes,
                            hInicial, hFinal, panelMensagemErro, ambienteIndispVO-motivo"
                            action="#{configuracaoEstudioControle.acaoAdicionarAmbienteIndisp}"
                            title="Adicionar"></a4j:commandLink>
                    </h:panelGrid>
                </h:panelGrid>

                <h:outputText 
                    styleClass="texto_disponibilidade"
                    value="Motivo:"/>
                <h:inputText 
                    id="ambienteIndispVO-motivo"
                    onkeydown="return tabOnEnter(event, 'add');"
                    value="#{configuracaoEstudioControle.ambienteIndispVO.motivo}" 
                    size="59"
                    maxlength="60"/>

            </h:panelGrid>
            <h:panelGrid columns="1" width="100%" styleClass="tabFormSubordinada">
                <h:dataTable
                    id="configuracaoEstudioControle-listaAmbienteIndisp"
                    var="item"
                    value="#{configuracaoEstudioControle.listaAmbienteIndisp}"
                    width="100%" headerClass="subordinado" styleClass="tabFormSubordinada"
                    rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento">

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Dia da Semana"   />
                        </f:facet>
                        <h:outputText
                            value="#{item.diaSemana.descricao}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Dia do M�s"   />
                        </f:facet>
                        <h:outputText
                            converter="dataConverter"
                            value="#{item.diaMes}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Hora Inicial"  />
                        </f:facet>
                        <h:outputText
                            converter="timeConverter"
                            value="#{item.horaInicial}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText 
                                value="Hora Final" />
                        </f:facet>
                        <h:outputText
                            value="#{item.horaFinal}"
                            converter="timeConverter2"/>
                    </h:column>

                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="Motivo" />
                        </f:facet>
                        <h:outputText
                            value="#{item.motivo}"/>
                    </h:column>

                    <h:column>
                        <a4j:commandLink action="#{configuracaoEstudioControle.acaoRemoverAmbienteIndisp}" reRender="configuracaoEstudioControle-listaAmbienteIndisp">
                            <h:graphicImage value="/imagens/estudio/icon_delete.png" styleClass="botoes" style="cursor:pointer" id="rmvItem" />
                            <f:setPropertyActionListener target="#{configuracaoEstudioControle.ambienteIndispVO}" value="#{item}"/>
                        </a4j:commandLink>
                    </h:column>
                </h:dataTable>
            </h:panelGrid>
        </h:panelGrid>


    </body>
</html>
