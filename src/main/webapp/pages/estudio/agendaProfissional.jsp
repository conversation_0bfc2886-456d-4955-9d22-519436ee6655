<%-- 
    Document   : agendaProfissional
    Created on : Feb 28, 2012, 3:24:58 PM
    Author     : <PERSON>?nio
--%>


<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />


<html>
    <head>
        <script type="text/javascript" language="javascript" src="${contexto}/script/basico.js"></script>
        <script type="text/javascript" language="javascript" src="${root}/script/smartbox/smartbox.js"></script>
        <link href="${root}/css_pacto.css" rel="stylesheet" type="text/css">
        <link href="${root}/css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
        <jsp:include page="includes/include_head.jsp" />
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>

        <style>
            .cols{
                vertical-align:top;
            }
            .row_agenda {
                text-align: left;
            }
            .row_agenda_geral {
                border-width: 0;
                text-align: center;
            }
            .celula-agenda {
                border-style: solid;
            }
        </style>       
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
        <title>
            <h:outputText value="Agenda Profissional"/>
        </title>

        <a4j:form id="agendaGeral" prependId="false" ajaxSubmit="true" >
          <a4j:keepAlive beanName="ExportadorListaControle"/>
            <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                    <c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
                    <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                    <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                    <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
                </h:panelGroup>

                <h:panelGroup layout="block" styleClass="caixaCorpo">
                    <h:panelGroup layout="block" style="height: 80%;width: 100%">
                        <h:panelGroup layout="block">
                                <div style="text-align:center;">
                                    <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                        <tr>
                                                <%--CONTEUDO AGENDA PROFISSIONAL--%>
                                            <td id="lateralRetrair" align="left" valign="top">
                                                <h:panelGroup id="expansores" layout="block">

                                                    <h:panelGroup id="expansor1" styleClass="menulateral_restaurado" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                        <a4j:commandButton
                                                                image="/css/smartbox/botao_minimizar.png"
                                                                actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                status="statusHora"
                                                                reRender="tabelaConteudo,expansores"/>
                                                    </h:panelGroup>

                                                    <h:panelGroup id="expansor2" styleClass="menulateral_retraido" rendered="#{!SuperControle.exibirMenuLateralEstudio}">
                                                        <a4j:commandButton
                                                                image="/css/smartbox/botao_maximizar.png"
                                                                actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                status="statusHora"
                                                                reRender="tabelaConteudo,expansores"/>
                                                    </h:panelGroup>
                                                </h:panelGroup>

                                                <h:panelGrid width="100%" border="0" id="tabelaConteudo"
                                                             columns="3"
                                                             cellpadding="0" cellspacing="0">

                                                    <rich:column width="237" styleClass="#{SuperControle.exibirMenuLateralEstudio ? 'mostra' : 'esconde'}" style="padding: 10px 5px 0px 0px;vertical-align: top;">
                                                        <h:panelGroup layout="block" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                            <div class="cantos_t">
                                                                <div class="cantos_l">
                                                                    <div class="cantos_r">
                                                                        <div class="cantos_b">
                                                                            <div class="cantos_tl">
                                                                                <div class="cantos_tr">
                                                                                    <div class="cantos_bl">
                                                                                        <div class="cantos_br">
                                                                                            <div class="cantos">
                                                                                                <div style="padding: 20px 20px 20px 20px">
                                                                                                    <jsp:include page="includes/include_box_menulateral.jsp" />
                                                                                                    <jsp:include page="includes/include_box_descricao.jsp" />
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </h:panelGroup>

                                                    </rich:column>

                                                    <rich:column style="vertical-align: top;" width="100%">
                                                        <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                                            <h:panelGroup styleClass="container-box-header" layout="block">
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <h:outputText value="Agenda Profissional" styleClass="container-header-titulo"/>
                                                                    <h:outputLink styleClass="linkWiki"
                                                                                  value="#{SuperControle.urlBaseConhecimento}como-ver-a-agenda-profissional-do-modulo-studio/"
                                                                                  title="Agenda Profissional"
                                                                                  target="_blank">
                                                                        <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                    </h:outputLink>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <h:panelGroup layout="block" style="padding: 0 0 0 0; float:right;vertical-align:middle;">
                                                                        <a4j:commandButton
                                                                                image="#{context}/imagens/estudio/filtro_profissional.png"
                                                                                action="#{AgendaAmbienteColaboradorControle.buscarListaColaboradores}"
                                                                                oncomplete="#{rich:component('panelFiltro')}.show()"
                                                                                value="Filtro" reRender="agendaAmbienteProfissionalControle-listaColaborador, modalPanelErro,formPanelFiltro"
                                                                                title="Filtro">
                                                                            <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.parBotao}" value="true" />
                                                                        </a4j:commandButton>

                                                                        <a4j:commandButton id="btnAtualizar" style="vertical-align:top;"
                                                                                           image="#{context}/imagens/estudio/btn_atualizar.png" value="Atualizar"
                                                                                           title="Atualizar" action="#{AgendaAmbienteColaboradorControle.verPreferencias}"/>

                                                                        <a4j:commandButton id="btnExcel"
                                                                                           value="Excel" title="Exportar para excel"
                                                                                           styleClass="botoes"
                                                                                           image="#{context}/imagens/btn_excel.png"
                                                                                           actionListener="#{AgendaAmbienteColaboradorControle.exportar}"
                                                                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/vnd.ms-excel','AgendaProfissional', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                                           accesskey="3">
                                                                            <f:attribute name="lista" value="#{AgendaAmbienteColaboradorControle.listaAgenda}"/>
                                                                            <f:attribute name="tipo" value="xls"/>
                                                                            <f:attribute name="atributos" value="horaInicio_Apresentar=Hor�rio,colaboradorApresentar=Colaborador,clienteApresentar=Cliente,produtoApresentar=Produto"/>
                                                                            <f:attribute name="prefixo" value="AgendaProfissional"/>
                                                                            <i class="fa-icon-excel" ></i>
                                                                        </a4j:commandButton>

                                                                        <a4j:commandButton id="btnPDF"
                                                                                           image="#{context}/imagens/imprimir.png"
                                                                                           value="PDF" title="Imprimir PDF"
                                                                                           styleClass="pure-button pure-button-small margin-h-10"
                                                                                           actionListener="#{AgendaAmbienteColaboradorControle.exportar}"
                                                                                           oncomplete="abrirPopup('UpdateServlet?op=downloadfile&file=#{ExportadorListaControle.fileName}&mimetype=application/pdf','AgendaProfissional', 800,200);#{ExportadorListaControle.msgAlert}"
                                                                                           accesskey="4">
                                                                            <f:attribute name="tipo" value="pdf"/>
                                                                            <f:attribute name="atributos" value="horaInicio_Apresentar=Hor�rio,colaboradorApresentar=Colaborador,clienteApresentar=Cliente,produtoApresentar=Produto"/>
                                                                            <f:attribute name="prefixo" value="AgendaProfissional"/>
                                                                            <i class="fa-icon-pdf" ></i>
                                                                        </a4j:commandButton>

                                                                    </h:panelGroup>
                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <table cellpadding="0" cellspacing="0" width="100%" id="agenda_ambiente" style="border-collapse: collapse;">
                                                                        <tr style="height:30px;">

                                                                            <rich:column colspan="#{fn:length(AgendaAmbienteColaboradorControle.listaColaboradoresAnalisados)+2}"
                                                                                         style="text-align: center;"
                                                                                         styleClass="texto_agenda" >
                                                                                <h:outputText style="font-weight:bold;" value="#{AgendaAmbienteColaboradorControle.hojePorExtenso}"/>
                                                                            </rich:column>
                                                                        </tr>

                                                                        <tr>
                                                                            <td style="height:30px; text-align: center;">
                                                                                <h:outputText value="Hor�rio" style="text-align: center;" styleClass="texto_agenda"/>
                                                                            </td>
                                                                            <a4j:repeat value="#{AgendaAmbienteColaboradorControle.listaColaboradoresAnalisados}" var="item" rowKeyVar="idx"
                                                                                        binding="#{AgendaAmbienteColaboradorControle.ajaxRepeatColaborador}">
                                                                                <td style="height:30px;text-align: center;">
                                                                                    <h:outputText  value="#{AgendaAmbienteColaboradorControle.nomeColaboradorReduzido}"
                                                                                                   styleClass="texto_agenda" />
                                                                                </td>
                                                                            </a4j:repeat>
                                                                            <rich:column >
                                                                                <h:outputText value="Hor�rio"   styleClass="texto_agenda" style="text-align: center;"/>
                                                                            </rich:column>
                                                                        </tr>

                                                                        <a4j:repeat value="#{AgendaAmbienteColaboradorControle.listaHoras}"
                                                                                    binding="#{AgendaAmbienteColaboradorControle.ajaxRepeatHora}" var="hour" rowKeyVar="idh" >
                                                                            <tr>
                                                                                <td style="width: 50px; height:30px; text-align: center;" >
                                                                                    <h:outputText value="#{hour}" styleClass="texto_agenda" />
                                                                                </td>
                                                                                <a4j:repeat value="#{AgendaAmbienteColaboradorControle.listaColaboradoresAnalisados}"
                                                                                            binding="#{AgendaAmbienteColaboradorControle.ajaxRepeatAmbiente}"
                                                                                            var="item" rowKeyVar="idx">

                                                                                    <rich:column style="text-align: center; background-color: #{AgendaAmbienteColaboradorControle.verificarCorCelula}" styleClass="#{AgendaAmbienteColaboradorControle.verificarCorCelula}">
                                                                                        <h:panelGroup layout="block" style="float: right; font-size: xx-small; " >
                                                                                            <a4j:commandButton image="#{context}/imagens/estudio/adicionar_mais.png" title="Adicionar Agendamento"
                                                                                                               action="#{AgendaAmbienteColaboradorControle.verificarDisponibilidadeCelula}"
                                                                                                               reRender="panelAgendaAluno, modalPanelErro" value="Agendar"
                                                                                                               rendered="#{AgendaAmbienteColaboradorControle.podeEfetuarAgendamento}"/>
                                                                                        </h:panelGroup>
                                                                                        <h:panelGroup layout="block" style=" float: left;">
                                                                                            <rich:dataTable value="#{AgendaAmbienteColaboradorControle.buscarListaAgendas}" var="itensAgenda" rowKeyVar="idxAgendas" id="repeatItensAgendaProfissional"
                                                                                                            width="100%" border="none" align="center" styleClass="texto_agenda tabelaSemBorda" style="border-style:none; padding: 0 0 0 0;">
                                                                                                <rich:column styleClass="texto_agenda tabelaSemBorda" style="border-style:none; padding: 0 0 0 0;">
                                                                                                    <rich:dataTable
                                                                                                            columnsWidth="100%" value="#{itensAgenda}"
                                                                                                            var="itemAgenda" styleClass="texto_agenda tabelaSemBorda"
                                                                                                            rowClasses="row_agenda" style="border-style:none; padding: 0 0 0 0;" binding="#{AgendaAmbienteColaboradorControle.dataTableItemAgenda}">
                                                                                                        <h:inputHidden value="#{itemAgenda.clienteVO.pessoa.nome}"
                                                                                                                       binding="#{AgendaAmbienteColaboradorControle.inputNomeReduzido}"/>

                                                                                                        <rich:column style="border-style:none; padding: 0 0 0 0;background-color: #{AgendaAmbienteColaboradorControle.corStatus}; " >
                                                                                                            <a4j:commandLink action="#{AgendaAmbienteColaboradorControle.editarAgendamento}" style="color: black;"
                                                                                                                             id="commandLinkAjaxToolProfissional" reRender="toolTipAgenda"
                                                                                                                             status="statuagendaAmbienteColaboradorControlesHora">
                                                                                                                <h:outputText
                                                                                                                        value="#{itemAgenda.status.id}"
                                                                                                                        styleClass="text_agenda" style="cursor: pointer; display: inline;"
                                                                                                                        id="dataTable-statusIdProfissional"/>
                                                                                                                <h:outputText
                                                                                                                        value=" - #{AgendaAmbienteColaboradorControle.retornarNomeReduzido} - #{itemAgenda.produtoVO.descricao}"
                                                                                                                        styleClass="text_agenda" style="cursor: pointer;"
                                                                                                                        id="dataTable-nomeServicoProfissional" />
                                                                                                                <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.itemAgenda}" value="#{itemAgenda}" />
                                                                                                            </a4j:commandLink>
                                                                                                        </rich:column>

                                                                                                    </rich:dataTable>
                                                                                                </rich:column>

                                                                                            </rich:dataTable>
                                                                                        </h:panelGroup>
                                                                                    </rich:column>
                                                                                </a4j:repeat>

                                                                                <td style="width: 50px; height:30px; text-align: center;">
                                                                                    <h:outputText value="#{hour}" styleClass="texto_agenda" />
                                                                                </td>
                                                                            </tr>
                                                                        </a4j:repeat>
                                                                    </table>
                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <f:verbatim>

                                                                        <fieldset class="texto_agenda">
                                                                            <legend class="texto_agenda">Legendas</legend>
                                                                            <h:panelGrid columns="2" styleClass="colunaEsquerda,colunaDireita" width="100%">
                                                                                <h:panelGrid  columns="6">
                                                                                    <div class="cor_empresa_fechada" style="width: 20px; height: 20px;"></div>
                                                                                    <h:outputText value="Empresa Fechada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                    <div class="cor_feriado" style="width: 20px; height: 20px;"></div>
                                                                                    <h:outputText value="Feriado" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                    <div class="cor_indisponibilidade" style="width: 20px; height: 20px;"></div>
                                                                                    <h:outputText value="Indisponibilidade" style="padding: 0 10px 0 0;" styleClass="texto_agenda"  />

                                                                                    <div style="width: 20px; height: 20px;background-color:#cca8d0;"></div>
                                                                                    <h:outputText value="Falta Justificada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_falta_justificada}"/>

                                                                                    <div style="width: 20px; height: 20px;background-color:#7FFFD4;" ></div>
                                                                                    <h:outputText value="Falta e N�o Considera" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_falta_semDebito}" />

                                                                                    <div style="width: 20px; height: 20px;background-color:#f39b9b;"></div>
                                                                                    <h:outputText value="Falta e Considera" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_falta}"/>

                                                                                    <div  style="width: 20px; height: 20px;background-color:#a8d3af;"></div>
                                                                                    <h:outputText value="Sess�o Ministrada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_ministrada}"/>

                                                                                    <div  style="width: 20px; height: 20px;background-color:#a3ccee;"></div>
                                                                                    <h:outputText value="Sess�o Confirmada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_sessao_confirmada}" />

                                                                                    <div  style="width: 20px; height: 20px;background-color:#fcd5b4;"></div>
                                                                                    <h:outputText value="Remarcada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_sessao_remarcada}"/>

                                                                                    <div  style="width: 20px; height: 20px;background-color:#e6f1f1;"></div>
                                                                                    <h:outputText value="Sem Defini��o" style="padding: 0 10px 0 0;" styleClass="texto_agenda" title="#{msg_aplic.legenda_agenda_sem_definicao}"/>
                                                                                </h:panelGrid>
                                                                            </h:panelGrid>
                                                                        </fieldset>
                                                                    </f:verbatim>
                                                                </h:panelGroup>

                                                        </h:panelGroup>
                                                    </rich:column>
                                                </h:panelGrid>
                                            </td>
                                                <%--CONTEUDO AGENDA PROFISSIONAL - FIM--%>
                                        </tr>
                                    </table>
                                </div>
                        </h:panelGroup>
                    </h:panelGroup>
                </h:panelGroup>

                <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
            </h:panelGroup>
    </a4j:form>

        <rich:modalPanel id="panelFiltro" autosized="true" shadowOpacity="true"
                         onshow="focusAt('modalColaboradorCodg-colaborador-codigo');#{rich:component('keyEscFiltro')}.enable();"
                         showWhenRendered="#{AgendaAmbienteColaboradorControle.renderedModal}" width="500"
                         height="280" >
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Filtro"/>
                </h:panelGroup>
            </f:facet>
            <f:facet name="controls">
                <h:panelGroup>
                    <h:graphicImage value="#{context}/imagens/close.png" style="cursor:pointer" id="hidelinkPanelFiltro" />
                    <rich:componentControl for="panelFiltro" attachTo="hidelinkPanelFiltro"
                                           operation="hide" event="onclick"  >
                        <rich:hotKey
                            id="keyEscFiltro"
                            key="esc"
                            handler="#{rich:component('panelFiltro')}.hide();"/>
                        <a4j:support event="onclick" oncomplete="#{rich:component('keyEscFiltro')}.disable();" />
                    </rich:componentControl>
                </h:panelGroup>
            </f:facet>

            <a4j:form id="formPanelFiltro" ajaxSubmit="true" prependId="false">

                <rich:tabPanel
                    switchType="ajax" id="tabPanel" height="250">
                    <rich:tab
                        id="abaProfissional" focus="modalColaboradorCodg-colaborador-codigo"
                        name="Profissional"
                        label="Profissional">

                        <h:panelGrid columns="5" id="dadosDependenteColaborador" >
                            <h:outputLabel
                                value="N� do Profissional" />
                            <rich:spacer width="20px"/>
                            <h:outputLabel
                                value="Nome" />
                            <rich:spacer width="10px"/>
                            <rich:spacer width="10px"/>

                            <h:inputText
                                onblur="blurinput(this);"
                                title="C�digo do Colaborador"
                                onfocus="focusinput(this);"
                                styleClass="form"
                                maxlength="4"
                                onkeydown="return tabOnEnter(event, 'modalColaboradorCodg-colaborador-descricao');"
                                autocomplete="off"
                                size="3"
                                value="#{AgendaAmbienteColaboradorControle.colaboradorSelecionado.codigo}"
                                id="modalColaboradorCodg-colaborador-codigo">
                                <a4j:support event="onchange" oncomplete="focusAt('addButtonP');" action="#{AgendaAmbienteColaboradorControle.acaoProcurarColaborador}"
                                             reRender="modalColaboradorCodg-colaborador-descricao,modalColaboradorSuggestion, modalColaboradorCodg-colaborador-codigo, modalPanelErro"/>
                            </h:inputText>
                            <rich:spacer width="20px"/>
                            <h:inputText
                                onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                styleClass="form"
                                maxlength="20"
                                autocomplete="off"
                                onkeydown="return tabOnEnter(event, 'addButtonP');"
                                style="width:250px;"
                                value="#{AgendaAmbienteColaboradorControle.colaboradorSelecionado.pessoa.nome}"
                                id="modalColaboradorCodg-colaborador-descricao">
                            </h:inputText>
                            <rich:suggestionbox id="modalColaboradorSuggestion" for="modalColaboradorCodg-colaborador-descricao"
                                                width="250"
                                                title="Comece a digitar o nome do Colaborador e selecione um"
                                                suggestionAction="#{AgendaAmbienteColaboradorControle.listarColaboradores}" status="statusHora"
                                                var="item" fetchValue="#{item.pessoa.nome}"
                                                nothingLabel="Nenhum dado encontrado" >
                                <rich:column>
                                    <h:outputText value="#{item.pessoa.nome}"/>
                                </rich:column>
                                <a4j:support event="onselect" reRender="modalColaboradorCodg-colaborador-codigo">
                                    <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.colaboradorSelecionado}" value="#{item}" />
                                </a4j:support>
                            </rich:suggestionbox>
                            <a4j:commandButton
                                image="#{context}/imagens/estudio/adicionar.png"
                                id="addButtonP"
                                value="Adicionar"
                                oncomplete="#{AgendaAmbienteColaboradorControle.apresentarRichModalErro} ? focusAt('fecharButton') : focusAt('modalColaboradorCodg-colaborador-codigo');"
                                title="Adicionar" style="width:75px"
                                action="#{AgendaAmbienteColaboradorControle.acaoAdicionarColaborador}"
                                reRender="agendaAmbienteProfissionalControle-listaColaborador, modalColaboradorCodg-colaborador-descricao,
                                modalColaboradorCodg-colaborador-codigo, modalColaboradorSuggestion, modalPanelErro"/>
                        </h:panelGrid>

                        <rich:spacer height="5" />
                        <h:panelGrid columns="2">
                            <rich:scrollableDataTable
                                id="agendaAmbienteProfissionalControle-listaColaborador"
                                var="item"
                                height="130px"
                                width="462px"
                                frozenColCount="1"
                                value="#{AgendaAmbienteColaboradorControle.listaColaboradoresSelecionados}">
                                <rich:column sortable="false" width="25" id="columnCheckBox">
                                    <f:facet name="header">
                                        <h:selectBooleanCheckbox
                                            id="selecionarTodosColaboradores"
                                            value="#{AgendaAmbienteColaboradorControle.selecionarTodosColaboradores}">
                                            <a4j:support
                                                status ="statusHora"
                                                action="#{AgendaAmbienteColaboradorControle.acaoSelecionarTodosColaboradores}"
                                                event="onclick"
                                                reRender="itemSolicitacao-selecionado-colaborador">
                                            </a4j:support>
                                        </h:selectBooleanCheckbox>
                                    </f:facet>
                                    <h:selectBooleanCheckbox
                                        id="itemSolicitacao-selecionado-colaborador"
                                        value="#{item.selecionado}">
                                        <a4j:support action="#{AgendaAmbienteColaboradorControle.acaoSelecionarUmColaborador}"
                                                     status="statusHora"
                                                     event="onclick" reRender="selecionarTodosColaboradores">
                                        </a4j:support>
                                    </h:selectBooleanCheckbox>
                                </rich:column>

                                <rich:column width="65px" >
                                    <f:facet name="header">
                                        <h:outputText value="C�digo" />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.codigo}"
                                        style="float:right; margin-right: 5px;"/>
                                </rich:column>
                                <rich:column
                                    width="330px"
                                    sortBy="#{item.pessoa.nome}"
                                    filterBy="#{item.pessoa.nome}"
                                    filterEvent="onchange">
                                    <f:facet name="header">
                                        <h:outputText value="Nome" />
                                    </f:facet>
                                    <h:outputText
                                        value="#{item.pessoa.nome}"
                                        style="margin-left: 5px; position:relative;"/>
                                </rich:column>
                                <rich:column width="20px">
                                    <a4j:commandLink action="#{AgendaAmbienteColaboradorControle.acaoRemoverColaborador}" reRender="agendaAmbienteProfissionalControle-listaColaborador">
                                        <h:graphicImage value="#{context}/imagens/estudio/icon_delete.png" style="cursor:pointer" id="removerColaboradorSelecionado" />
                                        <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.colaboradorSelecionado}" value="#{item}"/>
                                    </a4j:commandLink>
                                </rich:column>
                            </rich:scrollableDataTable>
                        </h:panelGrid>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/pesquisar.png"
                            action="#{AgendaAmbienteColaboradorControle.acaoPesquisarColaborador}"
                            value="Pesquisar"
                            title="Pesquisar" style="width:75px; float:right; margin-right: 4px"
                            reRender="agendaGeral, panelFiltro, modalPanelErro" />

                    </rich:tab>
                </rich:tabPanel>
            </a4j:form>

        </rich:modalPanel>


        <rich:modalPanel id="toolTipAgenda" autosized="true" shadowOpacity="false" moveable="true"
                         showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarToolTip}" width="450"
                         height="80" >
            <f:facet name="header" >
                <h:outputText value="Dados"/>
            </f:facet>

            <a4j:form id="formToolTip" prependId="false" ajaxSubmit="true">
                <h:panelGrid columns="2" border="0" styleClass="texto_agenda tabela_agenda" style="text-align: left;" >
                    <h:outputText value="Aluno:"/>
                    <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.clienteVO.pessoa.nome}"/>
                    <h:outputText value="Ambiente:"/>
                    <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.ambienteVO.descricao}" />
                    <h:outputText value="Agendamento:"/>
                    <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.tipoHorarioVO.descricao}" />
                    <h:outputText value="Status: " />
                    <h:outputText value="#{AgendaAmbienteColaboradorControle.itemAgenda.status.descricao}" />
                </h:panelGrid>
                <h:panelGrid columns="4" border="0" style="float: right; border-style: none; border-width: 0px;">

                    <rich:column>
                        <a4j:commandLink
                                         style="font-size: 23px;color:rgba(56, 53, 53, 0.65);"
                                         action="#{AgendaAmbienteColaboradorControle.montarHistoricoBVClienteEditar}"
                                         title="Visualizar Boletim Visita"
                                         oncomplete="abrirPopup('../../questionarioClienteCRMForm.jsp', 'HistoricoBVCliente', 1000, 650);">
                            <i class="fa-icon-time"></i>
                        </a4j:commandLink>
                    </rich:column>
                    <rich:column>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/editar.png"
                            value="Editar" action="#{AgendaAmbienteColaboradorControle.buscarDetalhesAgenda}"
                            reRender="toolTipAgenda, panelAgendaAluno, modalPanelErro" style="cursor: pointer;">
                            <f:setPropertyActionListener target="#{AgendaAmbienteColaboradorControle.agendaDetalhada}" value="#{AgendaAmbienteColaboradorControle.itemAgenda}" />
                        </a4j:commandButton>
                    </rich:column>
                    <rich:column>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/excluir.png" value="Excluir" title="Excluir"
                            style="cursor: pointer" status="statusHora"
                            action="#{AgendaAmbienteColaboradorControle.verificarUsuarioSenhaResponsavel}"
                            oncomplete="#{AgendaAmbienteColaboradorControle.mensagemNotificar}"
                            reRender="toolTipAgenda,panelAutorizacaoFuncionalidade, panelAgendaAluno">
                            <f:setPropertyActionListener value="#{AgendaAmbienteColaboradorControle.itemAgenda}" target="#{AgendaAmbienteColaboradorControle.agendaSelecionada}" />
                        </a4j:commandButton>
                    </rich:column>
                    <rich:column>
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/fechar.png"
                            title="Fechar" style="cursor: pointer"
                            action="#{AgendaAmbienteColaboradorControle.acaoFecharToolTip}"
                            reRender="toolTipAgenda" >
                        </a4j:commandButton>
                    </rich:column>
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>
        <rich:modalPanel id="modalPanelSucesso" autosized="true" shadowOpacity="true"
                         showWhenRendered="#{AgendaAmbienteColaboradorControle.apresentarRichModalSucesso}" width="450"
                         height="80" onshow="focusAt('okButton');">
            <f:facet name="header">
                <h:panelGroup>
                    <h:outputText value="Informa��o do Agendamento"/>
                </h:panelGroup>
            </f:facet>
            <a4j:form id="formModalPanelSucesso" prependId="false">
                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{AgendaAmbienteColaboradorControle.mensagem}"
                                      escape="false"/>
                    </h:panelGrid>
                </h:panelGrid>
                <rich:spacer height="05px"/>
                <h:panelGrid style="position: relative; float:right; ">
                    <a4j:commandButton
                        image="#{context}/imagens/estudio/confirmar.png"
                        id="okButton"
                        value="Ok"
                        title="Ok" status="statusHora"
                        action="#{AgendaAmbienteColaboradorControle.acaoFecharModalSucesso}" reRender="modalPanelSucesso" />
                </h:panelGrid>
            </a4j:form>
        </rich:modalPanel>
        <%@include file="includes/include_modal_agenda_aluno.jsp" %>
        <%@include file="includes/include_modal_erro.jsp" %>
    </f:view>
</body>
</html>
