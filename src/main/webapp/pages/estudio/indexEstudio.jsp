<%-- 
    Document   : indexEstudio
    Created on : Feb 23, 2012, 11:10:43 AM
    Author     : GeoInova <PERSON>ões - <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1" %>
<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session"/>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form" prependId="false">
        <c:set var="moduloEstudio" scope="request" value="estudio"/>
        <html>
        <!-- Inclui o elemento HEAD da página -->
        <head>
            <script type="text/javascript" src="${root}/script/basico.js"></script>
            <jsp:include page="includes/include_head.jsp"/>
            <script type="text/javascript" language="javascript" src="${root}/script/telaInicial.js"></script>
            <script type="text/javascript" language="javascript" src="${root}/script/script2.js"></script>
            <script type="text/javascript" language="javascript" src="${root}/hoverform.js"></script>
            <script type="text/javascript" language="javascript" src="${root}/script/tooltipster/jquery.tooltipster.min.js"></script>
            <script type="text/javascript" language="javascript">
                setDocumentCookie('popupsImportante', 'close',1);
            </script>
        </head>
        <body>
        <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
            <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
            </h:panelGroup>

            <h:panelGroup layout="block" styleClass="caixaCorpo">
                <h:panelGroup layout="block" style="height: 80%;width: 100%">
                    <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                        <h:panelGroup layout="block" styleClass="container-imagem container-conteudo-central">
                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                            <h:panelGroup layout="block" styleClass="margin-box" style="text-align:center;">
                                            <center><img src="${root}/imagens/estudio/principal.png"
                                                         alt="Módulo Stúdio"/></center>

                            </h:panelGroup>
                            <a4j:commandButton style="visibility: hidden;" reRender="panelExpiracaoSenha"
                                               id="btnAtualizaPagina">
                            </a4j:commandButton>
                            </h:panelGroup>
                        </h:panelGroup>

                        <jsp:include page="includes/include_box_menulateral_sc.jsp" flush="true"/>

                    </h:panelGroup>
                </h:panelGroup>
            </h:panelGroup>

            <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
        </h:panelGroup>
    </h:form>
    <%@include file="/include_load_configs.jsp" %>
    <rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true"
                     onshow="focusAt('empresa');"
                     showWhenRendered="#{LoginControle.empresa.codigo == 0}"
                     width="450" height="100">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="Atenção!"></h:outputText>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formModalPanelErro" prependId="false">

            <h:panelGrid columns="2">
                <h:outputText value="Selecionar Empresa:"/>
                <h:selectOneMenu id="empresa" onblur="blurinput(this);"
                                 style="background: white;
                                         border: 1px solid #CCC;
                                         border-bottom-color: #999;
                                         border-right-color: #999;
                                         height:26px;
                                         color: black;
                                         margin: 5px;
                                         padding: 5px 8px 0 6px;
                                         padding-right: 5px;
                                         width:187px;
                                         vertical-align: middle;"
                                 onfocus="focusinput(this);"
                                 value="#{LoginControle.empresa.codigo}">
                    <f:selectItems value="#{LoginControle.listaSelectItemEmpresa}"/>
                </h:selectOneMenu>
            </h:panelGrid>

            <rich:spacer height="05px"/>
            <h:panelGrid style="position: relative; float:right; ">
                <a4j:commandButton
                        image="/imagens/estudio/confirmar.png"
                        value="Confirmar"
                        id="btnFecharErro"
                        focus="panelFiltroServico-servico-codigo"
                        onclick="#{rich:component('modalPanelErro')}.hide();"
                        action="#{LoginControle.acaoFecharModalEmpresaEstudio}" reRender="modalPanelErro"/>
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
    <%@include file="/include_modal_expiracaoSenha.jsp" %>
</f:view>
</body>
</html>