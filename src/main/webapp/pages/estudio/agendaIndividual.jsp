<%--
    Document   : agendaIndividual
    Created on : Feb 14, 2012, 3:24:58 PM
    Author     : GeoInova <PERSON>õ<PERSON> - <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="ISO-8859-1"%>
<%@include file="includes/include_imports.jsp" %>
<c:set var="moduloSession" value="1" scope="session" />

<html>
    <head>
        <script type="text/javascript" language="javascript" src="${root}/script/basico.js"></script>
        <script type="text/javascript" language="javascript" src="${root}/script/smartbox/smartbox.js"></script>
        <link href="${root}/css_pacto.css" rel="stylesheet" type="text/css">
        <link href="${root}/css/smartbox/smartbox.css" rel="stylesheet" type="text/css">
        <jsp:include page="includes/include_head.jsp" />
        <%@taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
    </head>
    <body>
        <f:view>
            <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
            <a4j:form id="agendaGeral" prependId="false" ajaxSubmit="true" >
                <h:panelGroup layout="block" style="display: inline-table;width: 100%;" styleClass="fundoCinza">
                    <h:panelGroup layout="block" styleClass="bgtop topoZW" rendered="#{MenuControle.apresentarTopo}">
                        <c:set var="contexto" value="${pageContext.request.contextPath}" scope="request" />
                        <jsp:include page="../../include_topo_novo.jsp" flush="true"/>
                        <jsp:include page="../../include_menu_zw_flat.jsp" flush="true"/>
                        <rich:jQuery selector=".item6" query="addClass('menuItemAtual')"/>
                    </h:panelGroup>

                    <h:panelGroup layout="block" styleClass="caixaCorpo">
                        <h:panelGroup layout="block" style="height: 80%;width: 100%">
                            <h:panelGroup layout="block" styleClass="caixaMenuLatel">
                                    <div style="text-align:center;">
                                        <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0" class="text" style="margin-bottom:20px;">
                                            <tr>
                                                    <%--CONTEUDO AGENDA INDIVIDUAL--%>
                                                <td id="lateralRetrair" align="left" valign="top">
                                                    <h:panelGroup id="expansores" layout="block">

                                                        <h:panelGroup id="expansor1" styleClass="menulateral_restaurado" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                            <a4j:commandButton
                                                                    image="/css/smartbox/botao_minimizar.png"
                                                                    actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                    reRender="tabelaConteudo,expansores"/>
                                                        </h:panelGroup>

                                                        <h:panelGroup id="expansor2" styleClass="menulateral_retraido" rendered="#{!SuperControle.exibirMenuLateralEstudio}">
                                                            <a4j:commandButton
                                                                    image="/css/smartbox/botao_maximizar.png"
                                                                    actionListener="#{SuperControle.toggleMenuLateralEstudio}"
                                                                    reRender="tabelaConteudo,expansores"/>
                                                        </h:panelGroup>
                                                    </h:panelGroup>

                                                    <h:panelGrid width="100%" border="0" id="tabelaConteudo"
                                                                 columns="3"
                                                                 cellpadding="0" cellspacing="0">

                                                        <rich:column width="237" styleClass="#{SuperControle.exibirMenuLateralEstudio ? 'mostra' : 'esconde'}" style="padding: 10px 5px 0px 0px;vertical-align: top;">
                                                            <h:panelGroup layout="block" rendered="#{SuperControle.exibirMenuLateralEstudio}">
                                                                <div class="cantos_t">
                                                                    <div class="cantos_l">
                                                                        <div class="cantos_r">
                                                                            <div class="cantos_b">
                                                                                <div class="cantos_tl">
                                                                                    <div class="cantos_tr">
                                                                                        <div class="cantos_bl">
                                                                                            <div class="cantos_br">
                                                                                                <div class="cantos">
                                                                                                    <div style="padding: 20px 20px 20px 20px">
                                                                                                        <jsp:include page="includes/include_box_menulateral_indi.jsp" />
                                                                                                        <jsp:include page="includes/include_box_descricao.jsp" />
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </h:panelGroup>

                                                        </rich:column>

                                                        <rich:column style="vertical-align: top;" width="100%">
                                                            <h:panelGroup layout="block" styleClass="container-box zw_ui especial ">
                                                                <h:panelGroup styleClass="container-box-header" layout="block">
                                                                    <h:panelGroup layout="block" styleClass="margin-box">
                                                                        <h:outputText value="Agenda Individual" styleClass="container-header-titulo"/>
                                                                        <h:outputLink styleClass="linkWiki"
                                                                                      value="#{SuperControle.urlBaseConhecimento}como-fazer-agendamento-de-sessao-atraves-da-agenda-individual/"
                                                                                      title="Agenda Individual"
                                                                                      target="_blank">
                                                                            <i class="fa-icon-question-sign" style="font-size: 18px"></i>
                                                                        </h:outputLink>
                                                                    </h:panelGroup>
                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <h:panelGroup layout="block" style="padding: 0 0 0 0; float:right;vertical-align:middle;">
                                                                        <h:outputText value="Agenda:" style="vertical-align:top;" styleClass="texto_agenda"/>
                                                                        <h:selectOneMenu
                                                                                style="width:100px;vertical-align:top;"
                                                                                id="listaAgenda"
                                                                                onblur="blurinput(this);"
                                                                                onfocus="focusinput(this);"
                                                                                value="#{agendaIndividualControle.tipoAgenda}"
                                                                                title="Agenda">
                                                                            <f:selectItem itemLabel="Selecione um!" itemValue="0" />
                                                                            <f:selectItem itemLabel="Profissional" itemValue="1"  />
                                                                            <f:selectItem itemLabel="Ambiente" itemValue="2" />
                                                                            <a4j:support event="onchange" action="#{agendaIndividualControle.gerarListaSelectIndividuais}"
                                                                                         reRender="listaTiposAgenda"/>
                                                                        </h:selectOneMenu>
                                                                        <h:selectOneMenu
                                                                                style="width:220px;vertical-align:top;
                                                                                    #{agendaIndividualControle.tipoAgenda == 0 ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                                                                onblur="blurinput(this);"
                                                                                onfocus="focusinput(this);"
                                                                                id="listaTiposAgenda"
                                                                                disabled="#{agendaIndividualControle.tipoAgenda == 0 or agendaIndividualControle.selecionarCampo == false}"
                                                                                value="#{agendaIndividualControle.agendaIndividualSelecionada}"
                                                                                title="Agenda Individual">
                                                                            <f:selectItem itemLabel="Selecione um!" itemValue="0"/>
                                                                            <f:selectItems value="#{agendaIndividualControle.listaSelectIndividuais}"/>
                                                                        </h:selectOneMenu>
                                                                        <a4j:commandButton
                                                                                style="vertical-align:top;"
                                                                                image="#{context}/imagens/estudio/pesquisar.png" reRender="agendaGeral"
                                                                                value="Pesquisar" title="Pesquisar" action="#{agendaIndividualControle.acaoPesquisar}" >
                                                                        </a4j:commandButton>
                                                                    </h:panelGroup>
                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <table  cellpadding="0" cellspacing="0" width="100%" id="agenda_ambiente" style="border-collapse: collapse;" >
                                                                        <tr style="height:30px;">
                                                                            <td style="width:30px; height:30px;">

                                                                            </td>
                                                                            <a4j:repeat value="#{agendaIndividualControle.listaSemanasTotal}" var="item" rowKeyVar="idx">
                                                                                <td style="text-align: center;">
                                                                                    <h:outputText value="#{item}"  styleClass="texto_agenda"/>
                                                                                </td>
                                                                            </a4j:repeat>
                                                                        </tr>
                                                                        <tr>
                                                                            <td style="height:30px; text-align: center; ">
                                                                                <h:outputText value="Horário" style="text-align: center;" styleClass="texto_agenda"/>
                                                                            </td>
                                                                            <a4j:repeat value="#{agendaIndividualControle.listaSemanasTotal}" var="item" rowKeyVar="idx">
                                                                                <td style="height:30px;width: 50px;text-align: center;"><h:outputText  value="#{idx + 1}" styleClass="texto_agenda" /></td>
                                                                            </a4j:repeat>
                                                                            <td style="height:30px; text-align: center;">
                                                                                <h:outputText value="Horário" styleClass="texto_agenda"/>
                                                                            </td>
                                                                        </tr>

                                                                        <a4j:repeat value="#{agendaIndividualControle.listaHoras}" var="hour" rowKeyVar="idh" binding="#{agendaIndividualControle.ajaxRepeatHora}">
                                                                            <tr style="cursor: pointer;">
                                                                                <td style="cursor: pointer; width: 50px; height:30px; text-align: center; visibility: visible" >
                                                                                    <h:outputText value="#{hour}"  styleClass="texto_agenda"/>
                                                                                </td>
                                                                                <a4j:repeat value="#{agendaIndividualControle.listaSemanasTotal}" var="item" rowKeyVar="dia" binding="#{agendaIndividualControle.ajaxRepeatDia}">
                                                                                    <rich:column id="columrich2" style="cursor: pointer; text-align: center; background-color: #{agendaIndividualControle.verificarCorCelula}"
                                                                                                 styleClass="#{agendaIndividualControle.verificarCorCelula}">
                                                                                        <a4j:support event="onclick"
                                                                                                     action="#{agendaIndividualControle.mostrarModalDetalhes}"
                                                                                                     reRender="modalPanelErro, modalPanelDetalhesAgenda, panelAgendaAluno, agendaGeral">
                                                                                            <f:setPropertyActionListener value="#{dia + 1}" target="#{agendaIndividualControle.diaGl}"/>
                                                                                            <f:setPropertyActionListener value="#{hour}" target="#{agendaIndividualControle.horaGl}"/>
                                                                                        </a4j:support>
                                                                                        <h:outputText style="cursor: pointer;" id="valoroutput2" value="#{agendaIndividualControle.verificarSiglaCelula}"/>
                                                                                    </rich:column>
                                                                                </a4j:repeat>
                                                                                <td style="width: 50px; height:30px; text-align: center;">
                                                                                    <h:outputText value="#{hour}" styleClass="texto_agenda" />
                                                                                </td>
                                                                            </tr>
                                                                        </a4j:repeat>
                                                                    </table>
                                                                </h:panelGroup>
                                                                <h:panelGroup layout="block" styleClass="margin-box">
                                                                    <f:verbatim>

                                                                        <fieldset class="texto_agenda">
                                                                            <legend class="texto_agenda">Legendas</legend>
                                                                            <h:panelGrid columns="2" styleClass="colunaEsquerda,colunaDireita" width="100%">
                                                                                <h:panelGrid  columns="2">
                                                                                    <div class="cor_empresa_fechada" style="width: 20px; height: 20px;"></div>
                                                                                    <h:outputText value="Empresa Fechada" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                    <div class="cor_feriado" style="width: 20px; height: 20px;"></div>
                                                                                    <h:outputText value="Feriado" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                    <div class="cor_indisponibilidade" style="width: 20px; height: 20px;"></div>
                                                                                    <h:outputText value="Indisponibilidade" style="padding: 0 10px 0 0;" styleClass="texto_agenda" />

                                                                                </h:panelGrid>

                                                                                <h:panelGrid columns="1" style="float:right;">
                                                                                    <h:outputText value="Tipo Horários:" styleClass="texto_agenda" />
                                                                                    <h:outputText value="#{agendaIndividualControle.componenteTipoHorario}" styleClass="texto_agenda" escape="false" />
                                                                                </h:panelGrid>
                                                                            </h:panelGrid>
                                                                        </fieldset>
                                                                    </f:verbatim>
                                                                </h:panelGroup>
                                                            </h:panelGroup>
                                                        </rich:column>
                                                    </h:panelGrid>
                                                </td>
                                                    <%--CONTEUDO AGENDA INDIVIDUAL - FIM--%>
                                            </tr>
                                        </table>
                                    </div>
                            </h:panelGroup>
                        </h:panelGroup>
                    </h:panelGroup>

                    <jsp:include page="../../include_rodape_flat.jsp" flush="true"/>
                </h:panelGroup>
            </a4j:form>

            <rich:modalPanel id="modalPanelErro" autosized="true" shadowOpacity="true" zindex="99"
                             showWhenRendered="#{agendaIndividualControle.apresentarModalErro}" width="450"
                             height="80" onshow="focusAt('modalPanelErro-fechar');">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Atenção!"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formModalPanelErro" prependId="false">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText styleClass="mensagemDetalhada" value="#{agendaIndividualControle.mensagemDetalhada}"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <rich:spacer height="05px"/>
                    <h:panelGrid style="position: relative; float:right; ">
                        <a4j:commandButton
                            id="modalPanelErro-fechar"
                            value="Fechar"
                            title="Fechar" 
                            action="#{agendaIndividualControle.acaoFecharModalErro}" reRender="modalPanelErro" />
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>


            <rich:modalPanel id="modalPanelDetalhesAgenda" autosized="true" shadowOpacity="true" zindex="98"
                             showWhenRendered="#{agendaIndividualControle.apresentarModalListaAgendadas}" width="660"
                             height="80" onshow="focusAt('fecharButtonB');">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Lista de Alunos"></h:outputText>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formModalPanelDetalhesAgenda" prependId="false" ajaxSubmit="true">
                    <rich:dataTable value="#{agendaIndividualControle.listaAgendaModal}" var="item" columns="6"
                                    style="border-width: 1px;" width="100%">
                        <rich:column style="border-width: 1px;">
                            <f:facet name="header" >
                                <h:outputText value="Nome do Cliente" />
                            </f:facet>
                            <h:outputText value="#{item.clienteVO.pessoa.nome}" />
                        </rich:column>
                        <rich:column rendered="#{agendaIndividualControle.colaborador}"
                                     style="border-width: 1px;">
                            <f:facet name="header" >
                                <h:outputText value="Ambiente" />
                            </f:facet>
                            <h:outputText value="#{item.ambienteVO.descricao}" />
                        </rich:column>
                        <rich:column rendered="#{agendaIndividualControle.ambiente}"
                                     style="border-width: 1px;">
                            <f:facet name="header" >
                                <h:outputText value="Colaborador" />
                            </f:facet>
                            <h:outputText value="#{item.colaboradorVO.pessoa.nome}" />
                        </rich:column>
                        <rich:column style="border-width: 1px;">
                            <f:facet name="header" >
                                <h:outputText value="Serviço" />
                            </f:facet>
                            <h:outputText value="#{item.produtoVO.descricao}" />
                        </rich:column>
                        <rich:column style="border-width: 1px;">
                            <f:facet name="header" >
                                <h:outputText value="Tipo de Horário" />
                            </f:facet>
                            <h:outputText value="#{item.tipoHorarioVO.descricao}" />
                        </rich:column>
                        <rich:column style="border-width: 1px;">
                            <a4j:commandButton
                                image="#{context}/imagens/estudio/editar.png"
                                reRender="panelAgendaAluno, modalPanelErro"
                                value="Editar" action="#{agendaIndividualControle.acaoEditarAgenda}" >
                                <f:setPropertyActionListener value="#{item}" target="#{agendaIndividualControle.agendaSelecionada}" />
                            </a4j:commandButton>
                        </rich:column>
                        <rich:column style="border-width: 1px;">
                            <a4j:commandButton
                                image="#{context}/imagens/estudio/excluir.png"
                                reRender="modalPanelDetalhesAgenda,panelAutorizacaoFuncionalidade"
                                action="#{agendaIndividualControle.acaoFecharToolTip}"
                                value="Excluir">
                                <f:setPropertyActionListener value="#{item}" target="#{agendaIndividualControle.agendaSelecionada}"/>
                            </a4j:commandButton>
                        </rich:column >
                        <f:facet name="footer">
                            <h:outputText value="Total: #{fn:length(agendaIndividualControle.listaAgendaModal)}" />
                        </f:facet>
                    </rich:dataTable>
                    <rich:spacer height="05px"/>
                    <h:panelGrid style="position: relative; float:right; " columns="2">
                        <rich:column rendered="#{agendaIndividualControle.podeAgendarMais}">
                            <a4j:commandButton
                                image="#{context}/imagens/estudio/novo.png"
                                id="panelEditar-novaAgenda" value="Novo" title="Adicionar nova Agenda"
                                action="#{agendaIndividualControle.acaoNovaAgenda}" reRender="modalPanelDetalhesAgenda, panelAgendaAluno, modalPanelErro">
                            </a4j:commandButton>
                        </rich:column>
                        <rich:column>
                            <a4j:commandButton
                                image="#{context}/imagens/estudio/fechar.png"
                                id="fecharButtonB"
                                value="Fechar"
                                title="Fechar" ajaxSingle="true"
                                action="#{agendaIndividualControle.acaoFecharModalListaAgendadas}"
                                onclick="#{rich:component('modalPanelDetalhesAgenda')}.hide();" />
                        </rich:column>
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>           

            <rich:modalPanel id="panelAgendaAluno" autosized="true" shadowOpacity="true"
                             showWhenRendered="#{agendaIndividualControle.apresentarModalAgendaAluno}"
                             width="500" height="300" onshow="focusAt('panelAluno-matricula');">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Agenda Aluno"></h:outputText>

                    </h:panelGroup>
                </f:facet>
                <f:facet name="controls">
                    <h:panelGroup>
                        <a4j:form>
                            <a4j:commandButton  reRender="panelAgendaAluno" image="#{context}/imagens/close.png" id="agendaAluno-hide"
                                                action="#{agendaIndividualControle.acaoFecharModalAgendaAluno}" title="Fechar"/>
                        </a4j:form>
                    </h:panelGroup>
                </f:facet>
              <%--  <a4j:keepAlive beanName="agendaIndividualControle" /> --%>
                <h:form id="formPanelAgendaAluno" prependId="false">

                    <h:panelGrid columns="3" >
                        <h:outputText value="Matrícula*" style="position:relative; left:3px;" styleClass="texto_agenda"/>
                        <h:outputText value="Aluno*" style="position:relative; left:3px;" styleClass="texto_agenda"/>

                        <rich:spacer />
                        <h:panelGrid columns="2">
                            <h:inputText value="#{agendaIndividualControle.agendaSelecionada.clienteVO.codigoMatricula}"
                                         onblur="blurinput(this);"
                                         id="panelAluno-matricula"
                                         disabled="#{!agendaIndividualControle.novaAgenda}"
                                         onfocus="focusinput(this);"
                                         styleClass="form"
                                         style="#{!agendaIndividualControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666; background-image: none;' : ''}"
                                         onkeydown="return tabOnEnter(event, 'panelAluno-nome');"
                                         size="9">
                                <a4j:support event="onchange" oncomplete="focusAt('panelAluno-servico');"
                                             action="#{agendaIndividualControle.acaoProcurarCliente}"
                                             reRender="panelAluno-nome, panelAluno-ClienteSuggestion, imagem1, panelAluno-matricula, botaoContatoAlunoEstudio, botaoClienteAlunoEstudio"/>
                            </h:inputText>
                        </h:panelGrid>
                        <h:panelGrid columns="2">
                            <h:inputText 
                                value="#{agendaIndividualControle.agendaSelecionada.clienteVO.pessoa.nome}"
                                disabled="#{!agendaIndividualControle.novaAgenda}"
                                onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                id="panelAluno-nome"
                                styleClass="form"
                                onkeydown="return tabOnEnter(event, 'panelAluno-servico');"
                                style="width:250px; #{!agendaIndividualControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666; background-image: none;' : ''}"/>
                            <rich:suggestionbox id="panelAluno-ClienteSuggestion" for="panelAluno-nome"
                                                width="250"
                                                suggestionAction="#{agendaIndividualControle.listarClientesSuggest}"
                                                var="item" fetchValue="#{item.pessoa.nome}"
                                                nothingLabel="Nenhum dado encontrado" status="statusHora" >
                                <h:column>
                                    <h:outputText value="#{item.pessoa.nome}"/>
                                </h:column>
                                <a4j:support event="onselect" action="#{agendaIndividualControle.montarHistoricoBVCliente}" reRender="panelAluno-matricula,imagem1, botaoContatoAlunoEstudio, botaoClienteAlunoEstudio">
                                    <f:setPropertyActionListener target="#{agendaIndividualControle.agendaSelecionada.clienteVO}" value="#{item}" />
                                </a4j:support>
                            </rich:suggestionbox>
                        </h:panelGrid>
                        <h:panelGroup id="botaoClienteAlunoEstudio">
                            <a4j:commandLink actionListener="#{ClienteControle.abrirClienteDoEstudio}"
                                             oncomplete="#{ClienteControle.msgAlert}"
                                             rendered="#{agendaIndividualControle.agendaSelecionada.clienteVO.codigo > 0}">
                                <h:graphicImage url="#{context}/imagens/botaoVisualizar.png" title="Visualizar o Cliente"
                                                height="18px"
                                                width="18px"
                                                style="border:none;">
                                </h:graphicImage>
                                <f:attribute value="#{agendaIndividualControle.agendaSelecionada.clienteVO.codigo}" name="codigoCliente"/>
                            </a4j:commandLink>

                            <a4j:commandLink id="historicoBVCliente" rendered="#{agendaIndividualControle.exibirHistoricoBV}"
                                             style="font-size: 23px;color:rgba(56, 53, 53, 0.65);"
                                             title="Visualizar Boletim Visita"
                                             onclick="abrirPopup('#{context}/questionarioClienteCRMForm.jsp', 'HistoricoBVCliente', 1000, 650);">
                                <i class="fa-icon-time"/>
                            </a4j:commandLink>
                        </h:panelGroup>

                    </h:panelGrid>

                    <h:panelGrid columns="2" style="width:500px">
                        <a4j:mediaOutput element="img" id="imagem1"  style="width:90px;height:130px"  cacheable="false" session="true"
                                         rendered="#{!SuperControle.fotosNaNuvem}" 
                                         createContent="#{agendaIndividualControle.paintFoto}"  value="#{ImagemData}" mimeType="image/jpeg" >

                        </a4j:mediaOutput>
                        <h:graphicImage rendered="#{SuperControle.fotosNaNuvem}" 
                                        width="120" height="150"                                        
                                        style="width:120px;height:150px"
                                        url="#{agendaIndividualControle.paintFotoDaNuvem}"/>
                        <h:panelGrid columns="2" id="panelAlunoAgenda">
                            <h:outputLabel  id="lb-servico"  styleClass="classLabel texto_agenda" value="Serviço*"/>
                            <h:outputLabel id="lbl-status" styleClass="classLabel texto_agenda" value="Status:"/>

                            <h:panelGroup>
                                <h:inputText id="itemServicos"  size="30"
                                             onblur="blurinput(this);"  styleClass="form"
                                             disabled="#{agendaIndividualControle.agendaSelecionada.codigo > 0}"
                                             value="#{agendaIndividualControle.agendaSelecionada.produtoVO.descricao}"
                                             title="#{agendaIndividualControle.agendaSelecionada.produtoVO.observacao}"                                 
                                             onkeydown="bloquearCtrlJ()">
                                </h:inputText>

                                <rich:suggestionbox height="200" width="200"
                                                    for="itemServicos"
                                                    suggestionAction="#{agendaIndividualControle.executarAutocompleteConsultaProduto}"
                                                    minChars="1" rowClasses="20"
                                                    status="statusHora" immediate="true"
                                                    nothingLabel="Nenhum Serviço encontrado !" 
                                                    var="result" id="suggestionNomeServicos">

                                    <a4j:support event="onselect" focus="panelAluno-status" reRender="panelAlunoAgenda"  action="#{agendaIndividualControle.selecionarServicoSuggestionBox}"/>

                                    <h:column>
                                        <h:outputText value="#{result.descricao}" title="#{result.observacao}"/>
                                    </h:column>

                                </rich:suggestionbox>
                            </h:panelGroup>
                            <h:selectOneMenu
                                id="panelAluno-status"
                                disabled="#{agendaIndividualControle.bloquearStatus}"
                                onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                styleClass="form"
                                onkeydown="return tabOnEnter(event, 'panelAluno-horario');"
                                style="width:140px; #{agendaIndividualControle.bloquearStatus ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                value="#{agendaIndividualControle.agendaSelecionada.status}"
                                title="Status" >
                                <f:selectItems value="#{agendaIndividualControle.buscarListaStatus}" />
                                <a4j:support event="onchange" reRender="panelAlunoAgenda"/>
                            </h:selectOneMenu>

                            <h:selectOneMenu
                                id="panelAluno-servico-generico"
                                style="width:200px;"
                                onblur="blurinput(this);"
                                onfocus="focusinput(this);"
                                onkeydown="return tabOnEnter(event, 'panelAluno-horario');"
                                rendered="#{!empty agendaIndividualControle.servicoGenericoCombo && !agendaIndividualControle.novaAgenda}"
                                value="#{agendaIndividualControle.agendaSelecionada.produtoGenericoEscolhidoVO.codigo}"
                                title="Serviço"
                                styleClass="form">
                                <f:selectItem itemLabel="Selecione um Serviço!" />
                                <f:selectItems value="#{agendaIndividualControle.servicoGenericoCombo}"  />
                                <a4j:support event="onchange" />
                            </h:selectOneMenu >
                            <h:outputText value="" rendered="#{!empty agendaIndividualControle.servicoGenericoCombo && !agendaIndividualControle.novaAgenda}"/>

                            <h:outputText value="Profissional*:" styleClass="texto_agenda"/>
                            <h:outputText  value="Profissional Indicação:" styleClass="texto_agenda" />

                            <h:selectOneMenu
                                  style="width:200px; #{!agendaIndividualControle.verificarPreenchimentoParaProfissional
                                  || agendaIndividualControle.desabilitaStatus
                                  || !agendaIndividualControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                    value="#{agendaIndividualControle.agendaSelecionada.colaboradorVO}"
                                    id="listaProfissionalModal"
                                    onblur="blurinput(this);"
                                    onfocus="focusinput(this);"
                                    styleClass="form"
                                    onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                    converter="colaboradorConverter"
                                    title="Profissional">
                                <f:selectItem itemLabel="Selecione um Profissional!"  />
                                <f:selectItems  id="selectItemProfissional" value="#{agendaIndividualControle.profissionalCombo}"/>
                                <a4j:support event="onchange" reRender="panelAluno-horario"
                                             action="#{agendaIndividualControle.listarHorarios}"/>
                            </h:selectOneMenu>

                            <h:selectOneMenu style="width:200px; #{!agendaIndividualControle.verificarPreenchimentoParaProfissional
                                                       || agendaIndividualControle.desabilitaStatus
                                                       || !agendaIndividualControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                             value="#{agendaIndividualControle.agendaSelecionada.colaboradorIndicacaoVO}"
                                             id="listaProfissionalIndicacaoModal"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             converter="colaboradorConverter"
                                             onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                             title="Profissional Indicação">
                                <f:selectItem itemLabel="Selecione um Profissional!" />
                                <f:selectItems  id="selectItemProfissionalIndicacao" value="#{agendaIndividualControle.profissionalIndicacaoCombo}"/>
                            </h:selectOneMenu>

                            <h:outputText  value="Ambiente*:" styleClass="texto_agenda"  />
                            <h:outputText  value="" styleClass=""  />

                            <h:selectOneMenu
                                   style="width:200px; #{!agendaIndividualControle.verificarPreenchimentoParaAmbiente
                                  || agendaIndividualControle.desabilitaStatus
                                  || !agendaIndividualControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                    value="#{agendaIndividualControle.agendaSelecionada.ambienteVO}"
                                    id="listaAmbienteModal"
                                    onblur="blurinput(this);"
                                    onfocus="focusinput(this);"
                                    styleClass="form"
                                    onkeydown="return tabOnEnter(event, 'tipoHorario');"
                                    converter="ambienteConverter"
                                    title="Ambiente">
                                <f:selectItem itemLabel="Selecione um Ambiente!"  />
                                <f:selectItems  id="selectItemAmbiente" value="#{agendaIndividualControle.ambienteCombo}"/>
                                <a4j:support event="onchange" reRender="panelAlunoAgenda"   action="#{agendaIndividualControle.listarHorarios}"/>
                            </h:selectOneMenu>
                            <h:outputText  value="" styleClass="" />

                            <h:outputLabel id="lbl-data" styleClass="classLabel texto_agenda" value="Data da Aula*:"/>
                            <h:outputLabel id="lb-tipoHorario"  style="width:200px" styleClass="classLabel texto_agenda" value="Tipo Horário*:"/>

                            <rich:calendar
                                    locale="pt/BR"
                                    disabled="#{agendaIndividualControle.desabilitaStatus}"
                                    inputSize="10"
                                    inputStyle="#{agendaIndividualControle.desabilitaStatus ? 'background-color: #D2D2D2; color: #666666; background-image: none;' : ''}"
                                    inputClass="form"
                                    oninputblur="blurinput(this);"
                                    oninputfocus="focusinput(this);"
                                    oninputchange="return validar_Data(this.id);"
                                    oninputkeypress="return mascara(this, '99/99/9999', event);"
                                    oninputkeyup="return tabOnEnter(event, 'panelAluno-horario');"
                                    datePattern="dd/MM/yyyy"
                                    enableManualInput="true"
                                    zindex="2"
                                    showWeeksBar="false"
                                    value="#{agendaIndividualControle.agendaSelecionada.dataAula}"
                                    id="panelAluno-dataAula"
                                    popup="true" ajaxSingle="true">
                                <a4j:support event="onchanged"
                                             reRender="selectItemProfissional, listaProfissionalModal, selectItemAmbiente, listaAmbienteModal, panelAluno-profissionalSuggestion, panelAluno-horario"
                                             action="#{agendaIndividualControle.mudancaData}"
                                             ajaxSingle="true"/>
                            </rich:calendar>
                            <h:selectOneMenu
                                    id="tipoHorario"
                                    onblur="blurinput(this);"
                                    onfocus="focusinput(this);"
                                    styleClass="form"
                                    onkeydown="return tabOnEnter(event, 'panelAluno-observacao');"
                                    style="width:200px; #{agendaIndividualControle.desabilitaStatus || !agendaIndividualControle.novaAgenda ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                    value="#{agendaIndividualControle.agendaSelecionada.tipoHorarioVO.codigo}"
                                    title="Tipo de Horário">
                                <f:selectItem itemLabel="Selecione um horário!" itemValue="-1"/>
                                <f:selectItems value="#{agendaIndividualControle.buscarListaTipoHorarios}"/>
                            </h:selectOneMenu>

                            <h:outputLabel rendered="#{agendaIndividualControle.apresentarBotaoRetirarHorario!='S'}"  id="lb-hora"  styleClass="classLabel texto_agenda"  value="Hora*:">
                                <rich:spacer width="25px"/>
                                <a4j:commandButton id="addHorario"
                                                   rendered="#{!(!agendaIndividualControle.verificarPreenchimentoParaHoras || agendaIndividualControle.desabilitaStatus)
                                                               && (agendaIndividualControle.apresentarBotaoRetirarHorario!='S' && agendaIndividualControle.apresentarBotaoRetirarHorario!='')}"
                                                   action="#{agendaIndividualControle.adicionarHorario}" title="Adicionar horário com intervalo diferente de uma hora"
                                                   image="#{context}/imagens/estudio/adicionar_mais.png" reRender="formPanelAgendaAluno"/>
                            </h:outputLabel>
                            <h:panelGroup rendered="#{agendaIndividualControle.apresentarBotaoRetirarHorario=='S'}">
                                <h:outputLabel id="lb-horainicial"
                                               styleClass="classLabel texto_agenda"
                                               value="Hora Inicial:"/>
                                <rich:spacer width="10px"/>
                                <h:outputLabel id="lb-horafinal"
                                               styleClass="classLabel texto_agenda"
                                               value="Hora Final:"/>
                            </h:panelGroup>
                            <rich:spacer />

                            <h:selectOneMenu
                                    id="panelAluno-horario"
                                    rendered="#{agendaIndividualControle.apresentarBotaoRetirarHorario=='' || agendaIndividualControle.apresentarBotaoRetirarHorario=='N'}"
                                    disabled="#{!agendaIndividualControle.verificarPreenchimentoParaHoras ||!agendaIndividualControle.novaAgenda}"
                                    immediate="true"
                                    onblur="blurinput(this);"
                                    onfocus="focusinput(this);"
                                    styleClass="form"
                                    onkeydown="return tabOnEnter(event, 'panelAluno-salvar');"
                                    style="width:140px; #{!agendaIndividualControle.verificarPreenchimentoParaHoras || agendaIndividualControle.desabilitaStatus ? 'background-color: #D2D2D2; color: #666666;' : ''}"
                                    value="#{agendaIndividualControle.timeSelecionado}"
                                    title="Horário" >
                                <f:selectItem itemLabel="Selecione um horário!" itemValue="-1"/>
                                <f:selectItems value="#{agendaIndividualControle.selectItemHorarios}" />
                            </h:selectOneMenu>
                            <h:panelGroup id="panelAlunoAddHorario">
                                <h:inputText rendered="#{agendaIndividualControle.apresentarBotaoRetirarHorario=='S'}"
                                             disabled="#{!agendaIndividualControle.verificarPreenchimentoParaHoras || agendaIndividualControle.desabilitaStatus}"
                                             id="horarioInicial" size="10" maxlength="5"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{agendaIndividualControle.timeHorarioInicial}" />
                                <h:inputText rendered="#{agendaIndividualControle.apresentarBotaoRetirarHorario=='S'}"
                                             disabled="#{!agendaIndividualControle.verificarPreenchimentoParaHoras || agendaIndividualControle.desabilitaStatus}"
                                             id="horarioFinal" size="10" maxlength="5"
                                             onblur="blurinput(this);"
                                             onfocus="focusinput(this);"
                                             styleClass="form"
                                             value="#{agendaIndividualControle.timeHorarioFinal}"/>
                                <a4j:commandButton id="retHorario" rendered="#{agendaIndividualControle.apresentarBotaoRetirarHorario=='S'}"
                                                   disabled="#{!agendaIndividualControle.verificarPreenchimentoParaHoras || agendaIndividualControle.desabilitaStatus}"
                                                   reRender="formPanelAgendaAluno"
                                                   action="#{agendaIndividualControle.retirarHorario}"
                                                   image="#{context}/imagens/estudio/adicionar_menos.png"/>
                            </h:panelGroup>

                            <rich:spacer rendered="#{!agendaIndividualControle.novaAgenda}" />

                            <h:outputLabel id="lb-observacoes" style="width:150px" styleClass="classLabel texto_agenda"  value="Observações:"/>
                            <h:outputLabel id="lb-hora-desc"   styleClass="classLabel texto_agenda"   value="60min"/>
                            <h:inputTextarea
                                onblur="blurinput(this);"
                                id="panelAluno-observacao"
                                onfocus="focusinput(this);"
                                styleClass="form"
                                onkeyup="this.value = this.value.substring(0, 100);"
                                onkeydown="return tabOnEnter(event, 'panelAluno-status');"
                                value="#{agendaIndividualControle.agendaSelecionada.observacao}"
                                style="width:200px; max-width: 200px; height:50px; max-height: 30px;">
                            </h:inputTextarea>

                            <h:panelGrid columns="1">
                                <h:panelGrid columns="2" style="float:right;">
                                    <h:panelGroup id="botaoContatoAlunoEstudio">
                                        <a4j:commandButton
                                                actionListener="#{HistoricoContatoControle.abrirContatoDoEstudio}"
                                                oncomplete="#{HistoricoContatoControle.msgAlert}"
                                                value="Realizar Contato"
                                                image="#{context}/imagens/estudio/realizar_contato.png"
                                                rendered="#{agendaIndividualControle.agendaSelecionada.clienteVO.codigo > 0}">
                                            <f:attribute
                                                    value="#{agendaIndividualControle.agendaSelecionada.clienteVO.codigo}"
                                                    name="codigoCliente"/>
                                        </a4j:commandButton>
                                    </h:panelGroup>

                                    <a4j:commandButton
                                            image="#{context}/imagens/estudio/incluir.png"
                                            id="panelAluno-salvar"
                                            disabled="#{agendaIndividualControle.bloquearStatus}"
                                            value="Salvar" action="#{agendaIndividualControle.acaoSalvarAgenda}"
                                            reRender="modalPanelSucesso
                                    #{agendaIndividualControle.apresentarModalAgendaAluno ? ',  modalPanelErro' : ', agendaGeral, panelAgendaAluno, modalPanelErro, modalPanelDetalhesAgenda'}"/>
                                </h:panelGrid>

                            </h:panelGrid>

                        </h:panelGrid>
                    </h:panelGrid>
                </h:form>
            </rich:modalPanel>

            <rich:modalPanel id="modalPanelSucesso" autosized="true" shadowOpacity="true"
                             showWhenRendered="#{agendaIndividualControle.apresentarModalSucesso}" width="450"
                             height="80" onshow="focusAt('okButton');">
                <f:facet name="header">
                    <h:panelGroup>
                        <h:outputText value="Informação do Agendamento"/>
                    </h:panelGroup>
                </f:facet>
                <a4j:form id="formModalPanelSucesso" prependId="false">
                    <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada" >

                        <h:panelGrid columns="1" width="100%">
                            <h:outputText  styleClass="mensagem"  value="#{agendaIndividualControle.mensagem}" escape="false"/>
                        </h:panelGrid>
                    </h:panelGrid>
                    <rich:spacer height="05px"/>
                    <h:panelGrid style="position: relative; float:right; ">
                        <a4j:commandButton
                            image="#{context}/imagens/estudio/confirmar.png"
                            id="okButton"
                            value="Ok"
                            title="Ok"
                            action="#{agendaIndividualControle.acaoFecharModalSucesso}" reRender="modalPanelSucesso" />
                    </h:panelGrid>
                </a4j:form>
            </rich:modalPanel>            

            <%@include file="/includes/autorizacao/include_autorizacao_funcionalidade.jsp" %>
        </f:view>
    </body>
</html>
