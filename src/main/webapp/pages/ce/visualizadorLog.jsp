<%@include file="includes/include_imports.jsp" %>
<f:loadBundle var="msg_bt" basename="propriedades.Botoes"/>
<f:loadBundle var="msg_aplic" basename="propriedades.Aplicacao"/>
<f:loadBundle var="msg" basename="propriedades.Mensagens"/>
<c:set var="moduloSession" value="1" scope="session" />

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
    <h:form id="form">
        <html>
        	<!-- Inclui o elemento HEAD da p�gina -->
            <%@include file="includes/include_head.jsp" %>
            
            <body>
                <table width="100%" height="100%" border="0" cellpadding="0" cellspacing="0">
		        	<!-- Topo e menu superior -->
		            <%@include file="includes/include_topo.jsp" %>
		            
                	<tr>
                        <td align="left" valign="top" class="bglateral">
                            <table width="100%" height="100%" align="center" border="0" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td width="206" align="center" valign="top" class="bglateraltop" style="padding-top:6px;">
                                    	<!-- Inclui menu lateral -->
                                    	<%@include file="includes/include_box_menulateral.jsp" %>
                                    </td>
                                    <td align="left" valign="top" style="padding:7px 15px 0 20px;">
										
										<div style="clear:both;">
                                    
                                    		
                                    
											<table width="98%" border="0" align="left" cellpadding="0" cellspacing="0"
													class="text" style="margin-right: 30px; margin-bottom: 20px;">
												<tr>
													<td width="19" height="50" align="left" valign="top">
														<img src="${contexto}/images/box_centro_top_left.gif" width="19" height="50"></td>
													<td align="left" valign="top" background="${contexto}/images/box_centro_top.gif"
															class="tituloboxcentro" style="padding: 11px 0 0 0;">
														<h:outputText value="#{CElabels['menu.consulta.Log']}"/></td>
													<td width="19" align="left" valign="top">
														<img src="${contexto}/images/box_centro_top_right.gif" width="19" height="50"></td>
												</tr>
												<tr>
													<td align="left" valign="top" background="${contexto}/images/box_centro_left.gif">
														<img src="${contexto}/images/shim.gif"></td>
													<td align="left" valign="top" bgcolor="#ffffff" style="padding: 15px 15px 5px 15px;">
														
														&nbsp;
													
     												<br />
     												
               
        		<table id="tableParcelas" style="width: 100%;"><tr><td align="center">
        		
        		
        		   <h:panelGrid columns="11" footerClass="colunaCentralizada" width="100%">
			                    <h:outputText styleClass="tituloCampos" value="#{msg.msg_consultar_por}"/>
			                    <h:selectOneMenu styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" id="consulta" value="#{LogControle.controleConsulta.campoConsulta}">
			                        <f:selectItems value="#{LogControle.tipoConsultaCombo}" />
			                    </h:selectOneMenu>
			                    <h:inputText id="valorConsulta" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{LogControle.controleConsulta.valorConsulta}"/>
			                    E&nbsp;
			                    <h:selectOneMenu styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" id="consulta2" value="#{LogControle.controleConsulta.campoConsulta2}">
			                        <f:selectItems value="#{LogControle.tipoConsultaCombo}" />
			                    </h:selectOneMenu>
			                    <h:inputText id="valorConsulta2" styleClass="form" onblur="blurinput(this);"  onfocus="focusinput(this);" value="#{LogControle.controleConsulta.valorConsulta2}"/>
								
			                  
			                    <h:panelGroup>
			                    	In�cio:&nbsp;
			                    </h:panelGroup>
			                    <rich:calendar styleClass="form" 
										value="#{LogControle.controleConsulta.inicio}" verticalOffset="-80" horizontalOffset="100"
										datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" 
										oninputblur="blurinput(this);" oninputfocus="focusinput(this);" style="position: absolute; top: -90px;"
										oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this, '99/99/9999', event);"
										enableManualInput="true" zindex="2" showWeeksBar="false" />
								<h:panelGroup>
			                    	Fim:&nbsp;
			                    </h:panelGroup>
			                    <rich:calendar styleClass="form" 
										value="#{LogControle.controleConsulta.fim}" 
										datePattern="dd/MM/yyyy" inputSize="10" inputClass="form" 
										oninputblur="blurinput(this);" oninputfocus="focusinput(this);" style="position: absolute; top: -90px;"
										oninputchange="return validar_Data(this.id);" oninputkeypress="return mascara(this, '99/99/9999', event);"
										enableManualInput="true" zindex="2" showWeeksBar="false" />	
								  
			                    <a4j:commandButton type="submit" styleClass="botoes" value="#{msg_bt.btn_consultar}" 
								                    reRender="items, painelPaginacao"  
								                    actionListener="#{LogControle.consultarPaginadoListener}" 
								                    image="../../imagens/botoesCE/buscar.png" alt="#{msg.msg_consultar_dados}" accesskey="2">
								                        <f:attribute name="paginaInicial" value="paginaInicial"/>
			                    </a4j:commandButton>			
                </h:panelGrid>
        		
                 <h:dataTable id="items" width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                             value="#{LogControle.listaConsulta}" rendered="#{LogControle.apresentarResultadoConsulta}" rows="10" var="controleLog">
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_entidade}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="entidade" value="#{controleLog.nomeEntidade}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_operacao}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="operacao" value="#{controleLog.operacao} #{controleLog.nomeEntidade_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_nomeCampo}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="nomeCampo" value="#{controleLog.nomeCampo}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_dataHora}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="dataAlteracao" value="#{controleLog.dataHoraAlteracao_Apresentar}"/>
                    </h:column>
                    <h:column>
                        <f:facet name="header">
                            <h:outputText value="#{msg_aplic.prt_Log_responsavel}"/>
                        </f:facet>
                        <a4j:commandLink action="#{LogControle.visualizarLog}" reRender="formLog2, formLog" oncomplete="Richfaces.showModalPanel('panelLog')" id="responsavel" value="#{controleLog.responsavelAlteracao}"/>
                    </h:column>
                </h:dataTable>

                <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
                    <h:panelGroup id="painelPaginacao" rendered="#{LogControle.confPaginacao.existePaginacao}">
                        <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarPrimeiro}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagInicial" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarAnterior}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagAnterior" />
                        </a4j:commandLink>
                        <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{LogControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
                        <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarPosterior}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagPosterior" />
                        </a4j:commandLink>
                        <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="items, paginaAtual, painelPaginacao" rendered="#{LogControle.confPaginacao.apresentarUltimo}" actionListener="#{LogControle.consultarPaginadoListener}">
                            <f:attribute name="pagNavegacao" value="pagFinal" />
                        </a4j:commandLink>
                        <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{LogControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
                    </h:panelGroup>
                </h:panelGrid>

            
                </td></tr></table>
                
                
				<br/>

                <h:panelGrid id="mensagensLogModal" columns="1" width="100%" styleClass="tabMensagens">
                    <h:panelGrid columns="1" width="100%">
                        <h:outputText styleClass="mensagem"  value="#{LogControle.mensagem}"/>
                        <h:outputText styleClass="mensagemDetalhada" value="#{LogControle.mensagemDetalhada}"/>
                    </h:panelGrid>
                </h:panelGrid>
            
											           
													</td>
													<td align="left" valign="top" background="${contexto}/images/box_centro_right.gif">
														<img src="${contexto}/images/shim.gif"></td>
												</tr>
												<tr>
													<td height="20" align="left" valign="top">
														<img src="${contexto}/images/box_centro_bottom_left.gif" width="19" height="20"></td>
													<td align="left" valign="top" background="${contexto}/images/box_centro_bottom.gif">
														<img src="${contexto}/images/shim.gif"></td>
													<td align="left" valign="top">
														<img src="${contexto}/images/box_centro_bottom_right.gif" width="19" height="20"></td>
												</tr>
											</table>
                                    
                               			</div>
                                
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="93" align="left" valign="top" class="bgrodape"><jsp:include page="/include_rodape.jsp" flush="true" /></td>
                    </tr>
                </table>
            	<%@include file="includes/include_focus.jsp" %>
            </body>
			
        </html>
    </h:form>
    <rich:modalPanel id="panelLog" autosized="true" shadowOpacity="false" width="600" height="300">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value=" #{msg_aplic.prt_Log_tituloForm}">
                </h:outputText>
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelink6"/>
                <rich:componentControl for="panelLog" attachTo="hidelink6" operation="hide" event="onclick"/>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formLog2" ajaxSubmit="true">
            <h:panelGrid columns="2" styleClass="tabForm" width="100%">

                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_operacao}"/>
                <h:inputTextarea id="operacao" rows="2" cols="60" styleClass="campos" readonly="true" value="#{LogControle.logVO.operacao} #{LogControle.logVO.nomeEntidade_Apresentar}" />
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_nomeCampo}"/>
                <h:inputText id="nomeCampo" size="40" styleClass="campos" readonly="true" value="#{LogControle.logVO.nomeCampo}" />
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAnterior}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAnterior" value="#{LogControle.logVO.valorCampoAnterior}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_valorCampoAlterado}"/>
                <h:inputTextarea rows="4" styleClass="campos" readonly="true" cols="70" id="valorCampoAlterado" value="#{LogControle.logVO.valorCampoAlterado}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_responsavel}"/>
                <h:outputText id="responsavel" styleClass="tituloCampos" value="#{LogControle.logVO.responsavelAlteracao}"/>
                <h:outputText styleClass="tituloCampos" value="#{msg_aplic.prt_Log_dataHora}"/>
                <h:outputText id="dataAlteracao" styleClass="tituloCampos" value="#{LogControle.logVO.dataAlteracao_Apresentar} �s #{LogControle.logVO.horaAlteracao_Apresentar}"/>

            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
 
	
</f:view>

