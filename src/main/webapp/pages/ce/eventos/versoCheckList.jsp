<%@include file="../includes/include_imports.jsp" %>
<html>
<head>
<link rel="shortcut icon" href="${contexto}/favicon.ico" >    
<title>Central de Eventos - Pacto Solu&ccedil;&otilde;es</title>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta name="title" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es" />
<meta name="description" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es" />
<meta name="keywords" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es"/>
<meta name="geo.region" content="BR" />
<meta name="author" content="Zillyon Web - Pacto Solu&ccedil;&otilde;es" />
<meta name="language" content="pt-br" />
<link href="${contexto}/css/checklist.css" rel="stylesheet" type="text/css">
<script>
	window.onload = function() {
		var numeroLinhas = 13;
		var tamanhoLista = document.getElementById('tamanhoLista').value;
		
		if (tamanhoLista < numeroLinhas) {
			var linhaEmBranco = '<tr>';
			for (var i = 0; i < 4; i++) {
				linhaEmBranco += '<td class="camposTabela">&nbsp;</td>';
			}
			linhaEmBranco += '</tr>';

			

			var numBens = numeroLinhas - tamanhoLista;
			var tabela = document.getElementById('tabelaBens');

			for (var i = 0; i < numBens; i++) {
				tabela.innerHTML += linhaEmBranco;
			}
			
		}
	};
</script>
</head>
<body>

<f:view>
    <jsp:include page="/includes/include_carregando_ripple.jsp" flush="true"/>
<h:form>
<table  cellpadding="2" width="1000px">
<tr>
<td>
<h:commandLink
		onclick="window.print();return false;">
		<h:graphicImage value="/imagens/botoesCE/imprimir.png" alt="#{CElabels['operacoes.imprimir']}" style="border: 0px; width: 65;"/>
</h:commandLink>
</td>
</tr>
<tr>
<td>

<center>
<c:if test="${EmissaoDocumentosControle.nrAmbientes gt 1}">
    <label class="checkTituloGeral"><strong>Evento <h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.situacao.descricao}"/> para os ambientes <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.descricaoAmbientes}"/> [VERSO]</strong></label>
    </c:if>
    <c:if test="${EmissaoDocumentosControle.nrAmbientes eq 1}">
    <label class="checkTituloGeral"><strong>Evento <h:outputText value="#{EmissaoDocumentosControle.eventoInteresse.situacao.descricao}"/> para o ambiente <h:outputText value="#{EmissaoDocumentosControle.negociacaoEvento.descricaoAmbientes}"/> [VERSO]</strong></label>
    </c:if>
</center>

</td>
</tr>

</table>
<!-- TABLE GERAL -->
<table width="1000px">
<!-- --------------- COLUNA 1 ------------------------- -->
<tr><td valign="top"><center>
<label class="checkTituloGeral"><strong>UTENS�LIOS</strong></label>
</center>
<table id="tabelaBens"  border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <th width="150px" nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Material:</th>
        <th  width="60px" nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Solicitado</th>
        <th  width="60px" nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Abastec.</th>
        <th   width="80px" nowrap="nowrap" bgcolor="#CCCCCC" class="campos">Total</th>
      </tr>
      
      <c:forEach var="utensilio" varStatus="index" items="${EmissaoDocumentosControle.negociacaoEvento.utensilios}">
	      <tr>
	        <td class="camposTabela">${utensilio.descricaoProdutoLocacao}</td>
	        <td class="camposTabela">${utensilio.quantidade}</td>
	        <td class="camposTabela">&nbsp;</td>
	        <td class="camposTabela">${utensilio.valorMonetario}</td>
	      </tr>
	      <c:if test="${index.last}">
	      	<c:set var="tamanhoLista" value="${index.index + 1}" />
	      </c:if>
	  </c:forEach>
	  
    </table>
    <br/>
<table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
<tr><td class="camposPequeno" width="60%">
Data da Confer�ncia: ____/____/_______
</td>
<td class="camposPequeno" width="20%">
Visto Func.
</td>
<td class="camposPequeno" width="20%">
Visto Cliente
</td>
</tr>
<tr><td width="60%" class="camposPequeno">
Func. Respons�vel: __________________
</td>
<td width="20%">
&nbsp;&nbsp;
</td>
<td width="20%">
&nbsp;&nbsp;
</td>
</tr>
</table>   
<br/>
 <table width="100%" border="1" cellpadding="2" cellspacing="0" class="tabela">
      <tr>
        <td width="51%"><span class="camposTabela">Futebol 7:</span></td>
        <td width="49%"><span class="camposTabela">Hor�rio: das: __:__ as __:__</span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">Monitor:</span></td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><span class="camposTabela">Assinatura:</span></td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><span class="camposTabela">Arena:</span></td>
        <td><span class="camposTabela">Hor�rio: das: __:__ as __:__</span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">Monitor:</span></td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><span class="camposTabela">Assinatura:</span></td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><span class="camposTabela">Cama El�stica</span></td>
        <td><span class="camposTabela">Hor�rio: das: __:__ as __:__</span></td>
      </tr>
      <tr>
        <td><span class="camposTabela">Monitor:</span></td>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <td><span class="camposTabela">Assinatura:</span></td>
        <td>&nbsp;</td>
      </tr>
    </table> 
    <br/>
     
</td>


<!-- --------------- COLUNA 2 ------------------------- -->
<td  valign="top">
<!-- CHECK LIST GERAL -->
<center><label class="checkTituloGeral"><strong>CHECK-LIST GERAL</strong></label></center>
<!-- SAL�O DE FESTAS -->
<table border="1" cellpadding="2" cellspacing="0" class="tabela">
<!-- CABE�ALHO-->
<tr><th width="150px">Sal�o de Festas</th>
<th width="55px">Visto</th></tr>
<!-- LINHA -->
<tr><td>Ar-Condicionado</td>
	<td>&nbsp;</td></tr>
<!-- LINHA -->
<tr><td>Portas e janelas</td>
	<td>  &nbsp; </td></tr>
<!-- LINHA -->
<tr><td>Piso</td>
	<td>  &nbsp; </td></tr>
<!-- LINHA -->
<tr><td>Ilumina��o</td>
	<td>  &nbsp; </td></tr>
	<!-- LINHA -->
<tr><td>�rea externa</td>
	<td> &nbsp;  </td></tr>
	<!-- LINHA -->
<tr><td>Seguran�as</td>
	<td> &nbsp;  </td></tr>
	<!-- LINHA -->
<tr><td>Ramais internos</td>
	<td>&nbsp;   </td></tr>

</table>

<br/>

<!-- BANHEIROS -->

<table border="1" cellpadding="2" cellspacing="0" class="tabela">
<!-- CABE�ALHO-->
<tr><th width="150px">Banheiros</th>
<th width="55px">Visto</th></tr>
<!-- LINHA -->
<tr><td>Papel Higi�nico</td>
	<td> &nbsp;  </td></tr>
<!-- LINHA -->
<tr><td>Papel toalha</td>
	<td>&nbsp; </td></tr>
<!-- LINHA -->
<tr><td>Sabonete</td>
	<td> &nbsp; </td></tr>
	<!-- LINHA -->
<tr><td>Ilumina��o</td>
	<td>&nbsp;   </td></tr>
	<!-- LINHA -->
<tr><td>Vaso Sanit�rio</td>
	<td>&nbsp;   </td></tr>
<tr><td>&nbsp; </td>
	<td>&nbsp;   </td></tr>
</table>
<br/>

<!-- cozinha -->

<table border="1" cellpadding="2" cellspacing="0" class="tabela">
<!-- CABE�ALHO-->
<tr><th width="150px">Cozinha</th>
<th width="55px">Visto</th></tr>
<!-- LINHA -->
<tr><td>Piso</td>
	<td> &nbsp;  </td></tr>
<!-- LINHA -->
<tr><td>Freezer</td>
	<td> &nbsp;  </td></tr>
<!-- LINHA -->
<tr><td>Microondas</td>
	<td>  &nbsp; </td></tr>
	<!-- LINHA -->
<tr><td>Fog�o</td>
	<td> &nbsp;  </td></tr>
	<!-- LINHA -->
<tr><td>Pia</td>
	<td> &nbsp;  </td></tr>
<tr><td>&nbsp; </td>
	<td>&nbsp;   </td></tr>
</table>
</td>



<!-- --------------- COLUNA 3 ------------------------- -->
<td  valign="top">
<center><label class="checkTituloGeral"><strong>SEGURAN�AS</strong></label></center>
<!-- SEGURAN�AS  -->
<table border="1" cellpadding="2" cellspacing="0" class="tabela">
<tr>
<td  width="390px" class="camposPequeno">
<center><label><strong>CONTROLE CHEGADA SEGURAN�AS</strong></label></center>
1-___:___ Nome: __________________ Assinatura: ____________________ <br/>
2-___:___ Nome: __________________ Assinatura: ____________________ <br/>
3-___:___ Nome: __________________ Assinatura: ____________________ <br/>
4-___:___ Nome: __________________ Assinatura: ____________________ <br/>
<br/>
<center><label><strong>CONTROLE SA�DA SEGURAN�AS</strong></label></center>
1-___:___ Nome: __________________ Assinatura: ____________________ <br/>
2-___:___ Nome: __________________ Assinatura: ____________________ <br/>
3-___:___ Nome: __________________ Assinatura: ____________________ <br/>
4-___:___ Nome: __________________ Assinatura: ____________________ <br/>
</td>
</tr>
</table>
<br/>
<table border="1" cellpadding="2" cellspacing="0" class="tabela">
<tr>
<td  width="390px" class="camposPequeno">
<center><label><strong>CONTROLE CHEGADA STAR CLEAN</strong></label></center>
1-___:___ Nome: __________________ Assinatura: ____________________ <br/>

<br/>
<center><label><strong>CONTROLE SAIDA STAR CLEAN</strong></label></center>
1-___:___ Nome: __________________ Assinatura: ____________________ <br/>

<br/>

</td>
</tr>
</table>
<br/>
<table width="100%" border="1" cellpadding="0" cellspacing="0" class="tabela">
          <tr>
            <td class="camposTabela" scope="col">Observa��o do Evento: <br/> <h:outputText escape="false" value="#{EmissaoDocumentosControle.eventoInteresse.observacao}"/>
            </td>
          </tr>
          
		          
        </table>
        <br/>
<table width="100%" border="1" cellpadding="0" cellspacing="0" class="tabela">
          <tr>
            <td class="camposTabela" scope="col">Obs.:</td>
          </tr>
          <tr>
            <th scope="row">&nbsp;</th>
          </tr>
          <tr>
            <th scope="row">&nbsp;</th>
          </tr>
          
        </table>
        <br/>
        <table width="100%" border="1" cellpadding="0" cellspacing="0" class="tabela">
          <tr>
            <th scope="col"><span class="campos">NSU</span></th>
          </tr>
          <tr>
            <th scope="row">&nbsp;</th>
          </tr>
          <tr>
            <th scope="row">&nbsp;</th>
          </tr>
        </table>
        <br/>
         
</td></tr>



</table>
<input type="hidden" id="tamanhoLista" value="${tamanhoLista}" />
</h:form>
</f:view>
</body>
</html>