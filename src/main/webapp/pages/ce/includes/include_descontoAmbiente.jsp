  
    <rich:modalPanel id="panelSenhaDescontoAmbiente" autosized="true" shadowOpacity="true" width="450" height="200" onshow="document.getElementById('formSenhaDescontoAmbiente:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.ambiente.desconto']}"/>
                
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
              <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkSenhaDescontoAmbiente"/>
                <rich:componentControl for="panelSenhaDescontoAmbiente" attachTo="hidelinkSenhaDescontoAmbiente" operation="hide"  event="onclick">
				</rich:componentControl>
              </h:panelGroup>
        </f:facet>
        <a4j:form id="formSenhaDescontoAmbiente">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{CElabels['entidade.ambiente.desconto']}"/>
                </h:panelGrid>
                <!--  DADOS DO LOGIN -->
                <h:panelGrid id="panelConfimacao" rendered="#{!OrcamentoDetalhadoControle.descontoLiberado }" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="C�digo:" />
                        <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px" value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.codigo}">
                            <a4j:support event="onchange" focus="formUsuarioSenha:senha" action="#{OrcamentoDetalhadoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usu�rio:" />
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text"  value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.senha}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <!--  DADOS DO DESCONTO -->
                <h:panelGrid id="panelDesconto" rendered="#{OrcamentoDetalhadoControle.descontoLiberado }" columns="2" width="100%" rowClasses="linhaImpar,linhaPar" columnClasses="colunaDireita, colunaEsquerda">
                    
                       <h:outputText value="#{CElabels['entidade.ambiente']} :" />
                       <h:outputText styleClass="text" style="margin-left:6px" value="#{OrcamentoDetalhadoControle.ambienteDesconto.descricaoAmbiente}" />
                       <h:outputText value="#{CElabels['entidade.desconto.tipo']} :" />
                       <h:panelGroup>
                       <h:selectOneMenu onblur="blurinput(this);" onfocus="focusinput(this);"
                    		onchange="limparMensagem('tipoDescontoAmbiente');" styleClass="form"
							id="tiposDesconto" value="#{OrcamentoDetalhadoControle.ambienteDesconto.tipoDesconto}">
							<f:selectItem itemValue="" itemLabel="#{CElabels['operacoes.selecione']}"/>
							<f:selectItems value="#{OrcamentoDetalhadoControle.tiposDesconto}" />
						</h:selectOneMenu>
						<div id="divObg-tipoDescontoAmbiente" class="mensagemObrigatorio"></div>
						</h:panelGroup>	
						<h:outputText value="#{CElabels['menu.cadastros.perfisEventos.descontos']}: " />
						<h:panelGroup>
						<h:inputText id="valorDesconto" onblur="blurinput(this);" onfocus="focusinput(this);"
									 onkeypress="return(currencyFormat(this,'.',',',event));" onchange="limparMensagem('valorDescontoAmbiente');"
									 styleClass="form" maxlength="14" value="#{OrcamentoDetalhadoControle.ambienteDesconto.descontoFormatado}" />
						<div id="divObg-valorDescontoAmbiente" class="mensagemObrigatorio"></div>
						</h:panelGroup>			 
                </h:panelGrid>
            <!-- -------------------------------------------------------------------------------------------------- -->    
                <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                 <!-- BOTAO DE CONFIRMAR DO LOGIN E SENHA -->             
                <a4j:commandButton rendered="#{!OrcamentoDetalhadoControle.descontoLiberado }" 
                		value="#{msg_bt.btn_confirmar}" image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
                		action="#{OrcamentoDetalhadoControle.liberarDesconto}"
                		reRender="formSenhaDescontoAmbiente"/>
                <!-- BOTAO DE CONFIRMAR DO DESCONTO -->
                <h:panelGroup>		
               	<a4j:commandButton rendered="#{OrcamentoDetalhadoControle.descontoLiberado}"
               			value="#{msg_bt.btn_confirmar}" image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
               			onclick="if(!validarDescontoAmbiente()){return false;};"
                		action="#{OrcamentoDetalhadoControle.confirmarDescontoAmbiente}"
                		oncomplete="Richfaces.hideModalPanel('panelSenhaDescontoAmbiente')" 
                		reRender="formSenhaDescontoAmbiente, detalhamentoNegociacao,ambientes"
                		actionListener="#{OrcamentoDetalhadoControle.autorizacao}">
                		
                		<!-- funcao.desconto ambiente -->
                   		<f:attribute name="funcao" value="112"/>    
                		</a4j:commandButton>
               	</h:panelGroup>
            </h:panelGrid>
        </a4j:form>
        <script>
        function validarDescontoAmbiente() {
				limparMensagem('tipoDescontoAmbiente');
				limparMensagem('valorDescontoAmbiente');
	   			
				var tipo = document.getElementById('formSenhaDescontoAmbiente:tiposDesconto');
				var valor = document.getElementById('formSenhaDescontoAmbiente:valorDesconto');
				var validade = true;
				if (tipo == null || tipo.value == null || tipo.value == "") {
				exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.tipoDescontoOrcamento\']}"/>', 'tipoDescontoAmbiente');
				validade = false;
			}
				if (valor == null || valor.value == null || valor.value == "") {
				exibirMensagem('<h:outputText value="#{Mensagens[\'campoObrigatorio.valorDesconto\']}"/>', 'valorDescontoAmbiente');
				validade = false;
			}
				return validade;

			}
        
        </script>
    </rich:modalPanel>
