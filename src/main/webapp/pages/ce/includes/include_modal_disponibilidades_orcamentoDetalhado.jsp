
<script type="text/javascript">
    <!--
    function preencherHiddenChamarBotao(idBotao, idHidden, valorHidden){
        var hidden = document.getElementById(idHidden);
        var botao = document.getElementById(idBotao);
        hidden.value = valorHidden;
        botao.click();
    }
    //-->
</script>

<rich:modalPanel id="panelDisponibilidade" autosized="true" shadowOpacity="true" width="450" height="250">
    <f:facet name="header">
        <h:panelGroup>
            <h:outputText value="#{CElabels['menu.operacoesCE.disponibilidade']}"/>
        </h:panelGroup>
    </f:facet>
    <f:facet name="controls">
        <h:panelGroup>
            <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkDisponibilidade"/>
            <rich:componentControl for="panelDisponibilidade" attachTo="hidelinkDisponibilidade" operation="hide" event="onclick"/>
        </h:panelGroup>
    </f:facet>

    <h:form id="formDisponibilidade">
        <a4j:commandButton id="botaoSetarHorarioParaAmbiente" reRender="ambientes, panelHorasData" action="#{ConsultaEventosControle.setarHorariosParaAmbiente}" style="visibility: hidden;"/>
        <h:inputHidden id="reservaSelecionadoParaAmbiente" value="#{ConsultaEventosControle.horariosSelecionadosParaAmbiente}" />

        <h:panelGrid columns="1" width="100%" columnClasses="colunaCentralizada">
            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.ambiente']}:" />&nbsp;
                <h:outputText value='#{ConsultaEventosControle.resultadoConsulta[ConsultaEventosControle.indexAmbiente]["0"]["1"]}' />
            </h:panelGroup>

            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.data']}:" />&nbsp;
                <h:outputText value='#{OrcamentoDetalhadoControle.negociacaoEvento.dataEventoFormatada}' />
            </h:panelGroup>

            <h:panelGroup>
                <h:outputText value="#{CElabels['entidade.horario']}:" />&nbsp;
                <h:outputText id="horarioSelecionado"/>
            </h:panelGroup>

            <h:panelGroup>
                <rich:dataTable width="100%" headerClass="consulta" rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
                                value="#{ConsultaEventosControle.disponibilidadeTO.reservas}" var="reserva"
                                style="cursor: pointer;"
                                onRowMouseOver="backgroundAntigo = this.style.background; this.style.background = '#D7D7D7';"
                                onRowMouseOut="this.style.background = backgroundAntigo;"
                                onRowClick="preencherHiddenChamarBotao('formDisponibilidade:botaoSetarHorarioParaAmbiente','formDisponibilidade:reservaSelecionadoParaAmbiente','#{reserva.horarioInicialFormatado} - #{reserva.horarioFinalExibicaoFormatado}');Richfaces.hideModalPanel('panelDisponibilidade');">
                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.horario']}"/>
                        </f:facet>
                        <h:outputText value="#{reserva.horarioInicialFormatado} - #{reserva.horarioFinalExibicaoFormatado}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.nome']}"/>
                        </f:facet>
                        <h:outputText value="#{reserva.nome}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.tipo']}"/>
                        </f:facet>
                        <h:outputText value="#{reserva.tipo.descricao}" />
                    </rich:column>

                    <rich:column>
                        <f:facet name="header">
                            <h:outputText value="#{CElabels['entidade.situacao']}"/>
                        </f:facet>
                        <h:outputText value="#{reserva.situacao.descricao}" />
                    </rich:column>
                </rich:dataTable>
            </h:panelGroup>

        </h:panelGrid>

    </h:form>
</rich:modalPanel>
