    <rich:modalPanel id="panelSenhaValorServico" autosized="true" shadowOpacity="true" width="450" height="250" onshow="document.getElementById('formSenhaValorServico:senha').focus()">
        <f:facet name="header">
            <h:panelGroup>
                <h:outputText value="#{CElabels['operacoes.editar.valor.servico']}"/>
                
            </h:panelGroup>
        </f:facet>
        <f:facet name="controls">
            <h:panelGroup>
                <h:graphicImage value="/imagens/close.png" style="cursor:pointer" id="hidelinkSenhaValorServico"/>
                <rich:componentControl for="panelSenhaValorServico" attachTo="hidelinkSenhaValorServico" operation="hide"  event="onclick">

                </rich:componentControl>
            </h:panelGroup>
        </f:facet>
        <a4j:form id="formSenhaValorServico">
            <h:panelGrid columns="1"  width="100%" columnClasses="colunaCentralizada">
                <h:panelGrid columns="1" style="height:25px; background-image:url('../../../imagens/fundoBarraTopo.png');background-repeat: repeat-x;" columnClasses="colunaCentralizada" width="100%">
                    <h:outputText styleClass="tituloFormulario" value="#{CElabels['operacoes.editar.valor.servico']}"/>
                </h:panelGrid>
                <h:panelGrid id="panelConfimacao" columns="1" width="100%" columnClasses="colunaEsquerda" styleClass="tabForm">
                    <h:panelGroup>
                        <h:inputText  style="opacity:0;height: 0px;" size="5" maxlength="7" />
                        <h:inputSecret  size="14" maxlength="64" style="opacity:0;height: 0px;"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="C�digo:" />
                        <h:inputText id="codigoUsuario" size="3" maxlength="100" style="margin-left:5px" value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.codigo}">
                            <a4j:support event="onchange" focus="formUsuarioSenha:senha" action="#{OrcamentoDetalhadoControle.consultarResponsavel}" reRender="panelConfimacao, mensagem"/>
                        </h:inputText>
                        <h:inputText id="autoCompleteHidden" size="3" maxlength="10" style="margin-left:6px;opacity:0;" value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.username}"/>
                    </h:panelGroup>
                    <h:panelGroup >
                        <h:outputText styleClass="text"  value="Usu�rio:" />
                        <h:outputText styleClass="text" style="margin-left:6px"
                                      value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.username}" />
                    </h:panelGroup>
                    <h:panelGroup>
                        <h:outputText styleClass="text"  value="Senha:"/>
                        <h:inputSecret autocomplete="off" id="senha" size="14" maxlength="64" style="margin-left:8px"
                                       value="#{OrcamentoDetalhadoControle.negEvContrato.responsavelContrato.senha}"/>
                    </h:panelGroup>
                </h:panelGrid>
                <h:outputText id="mensagem" styleClass="mensagemDetalhada"
                              value="#{OrcamentoDetalhadoControle.mensagemDetalhada}"/>
                <a4j:commandButton value="#{msg_bt.btn_confirmar}" image="/imagens/botoesCE/confirmar.png" alt="#{msg.msg_gravar_dados}"
                		action="#{OrcamentoDetalhadoControle.autorizarAlteracaoValorServico}"
                		oncomplete="Richfaces.hideModalPanel('panelSenhaValorServico')" reRender="detalhamentoNegociacao, servicos"
                		actionListener="#{OrcamentoDetalhadoControle.autorizacao}">
                		<!-- funcao.valor servico extra -->
				<f:attribute name="funcao" value="116"/>
				</a4j:commandButton>
               
            </h:panelGrid>
        </a4j:form>
    </rich:modalPanel>
