
<%@page contentType="text/html"%>
<%@page pageEncoding="ISO-8859-1"%>
<%@taglib prefix="f" uri="http://java.sun.com/jsf/core"%>
<%@taglib prefix="h" uri="http://java.sun.com/jsf/html"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@taglib prefix="a4j" uri="https://ajax4jsf.dev.java.net/ajax"%>
<f:loadBundle var="msg_menu" basename="propriedades.Menu"/>
<!-- inicio box -->
<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">
        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa-icon-list"></i> Financeiro
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.estornoRecibo}">
            <a4j:commandLink value="#{CElabels['menu.consulta.financeiro.estornoRecibo']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="ESTORNO_RECIBO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-estornar-o-recibo-de-pagamento-de-um-colaborador/"
                          title="Clique e saiba mais: Estorno de Recibo" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.movimentoContaCorrenteCliente}">
            <a4j:commandLink value="Movimento CC Cliente"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="MOVIMENTO_CC_CLIENTE_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-lancar-credito-ou-debito-na-conta-corrente-do-aluno/"
                          title="Clique e saiba mais: Movimento da Conta Corrente do Cliente" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.formaPagamento}">

            <a4j:commandLink value="#{CElabels['menu.consulta.financeiro.formasPagamento']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="FORMA_PAGAMENTO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-cadastrar-uma-forma-de-pagamento/"
                          title="Clique e saiba mais: Formas de Pagamento" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem" rendered="#{LoginControle.permissaoAcessoMenuVO.operadoraCartao}">

            <a4j:commandLink value="#{CElabels['menu.consulta.financeiro.operadorasCartao']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="OPERADORA_CARTAO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWiki}Cadastros:Config._Financeiras:Operadora_de_Cartão"
                          title="Clique e saiba mais: Operadora de Cartão" target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>

        </h:panelGroup>

    </h:panelGroup>
</h:panelGroup>