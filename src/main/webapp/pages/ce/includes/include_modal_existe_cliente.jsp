<%@include file="../includes/include_imports.jsp" %>

<rich:modalPanel id="panelCadastroSimplificadoCliente" autosized="true"
	shadowOpacity="true"
	showWhenRendered="#{CadastroInicialControle.exibir['modal']}"
	width="550" height="250"
	onshow="document.getElementById('formCadastroSimplificadoCliente:nomeCadastroSimplificadoCliente').focus();">

	<f:facet name="header">
		<h:panelGroup>
			<h:outputText
				value="Consultar Cliente #{CadastroInicialControle.exibir['modalCliente']}"/>
		</h:panelGroup>
	</f:facet>
	<f:facet name="controls">
		<h:panelGroup>
			<h:graphicImage value="/imagens/close.png" style="cursor:pointer"
				id="hidelinkCadastroSimplificadoCliente" />
			<rich:componentControl for="panelCadastroSimplificadoCliente"
				attachTo="hidelinkCadastroSimplificadoCliente" operation="hide"
				event="onclick" />
		</h:panelGroup>
	</f:facet>

	<a4j:form id="formCadastroSimplificadoCliente" ajaxSubmit="true">

		<h:panelGrid columns="1" width="100%">

			<h:panelGrid columns="2" rowClasses="linhaImpar, linhaPar"
				columnClasses="classEsquerda, classDireita" width="100%">

				<h:outputText value="Nome Completo " />
				<h:panelGroup>
					<h:inputText id="nomeCadastroSimplificadoCliente" size="50"
						maxlength="50" onblur="blurinput(this);"
						onfocus="focusinput(this);" styleClass="form"
						value="#{CadastroInicialControle.parametro.nomeCliente}" />
				</h:panelGroup>
				
				<h:outputText value="Telefone " />
				<h:panelGroup>
					<h:inputText id="telefoneConsulta" size="50"
						onblur="if(!validarFonePesquisa(this)){this.value='';};"
						maxlength="50"
						onkeypress="return mascara(this, '(99)999999999', event);"
						onfocus="focusinput(this);" styleClass="form"
						value="#{CadastroInicialControle.parametro.telefone}" />
				</h:panelGroup>

				<h:outputText value="CPF " />
				<h:panelGroup>
					<h:inputText id="cpf" size="15" maxlength="15"
						onblur="blurinput(this);" onfocus="focusinput(this);"
						styleClass="form" value="#{CadastroInicialControle.parametro.pessoa.cfp}"
						onkeypress="return mascara(this, '999.999.999-99', event);"/>
				</h:panelGroup>
				<div id="mensagemBusca" class="mensagemObrigatorio">
				</div>
			</h:panelGrid>

			<rich:dataTable id="itens" width="100%" headerClass="consulta"
				rowClasses="linhaImpar, linhaPar" columnClasses="colunaAlinhamento"
				value="#{CadastroInicialControle.listaInteressados}"
				rendered="#{CadastroInicialControle.apresentarResultadoConsulta}"
				rows="#{CadastroInicialControle.nrPagina}" var="interessado">
				
				<h:column>
					<f:facet name="header">
						<h:outputText  value="#{CElabels['entidade.pessoa.nomeCompleto']}" />
					</f:facet>
					<a4j:commandLink  action="#{CadastroInicialControle.escolhe}"
						reRender="panelCadastroSimplificadoCliente, panelDadosBasicos,evt,panelMensagens"
						value="#{interessado.pessoa.nome}" title="#{msg.msg_editar_dados}"
						styleClass="botoes" />
				</h:column>
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="#{CElabels['entidade.interessado.status']}" />
					</f:facet>
					<h:outputText value="#{interessado.status}" />
				</h:column>
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="Telefones" />
					</f:facet>
					<h:outputText escape="false" value="#{interessado.telefones}" />
				</h:column>
				
				<h:column>
					<f:facet name="header">
						<h:outputText value="Eventos" />
					</f:facet>
					<h:outputText escape="false" value="#{interessado.nomesEventos}" />
				</h:column>
			</rich:dataTable>
			<h:panelGrid rendered="#{CadastroInicialControle.apresentarResultadoConsulta}" columns="1"
                     columnClasses="colunaCentralizada" width="100%">
         <h:panelGroup>
            
<h:panelGrid columns="1"
                     columnClasses="colunaCentralizada" width="100%" >
	        
		<a4j:outputPanel id="painelPaginacao"> 
																	<h:panelGroup id="painelPaginacaoManual" rendered="#{CadastroInicialControle.confPaginacao.paginarBanco}">
																	
											                            <a4j:commandLink id="pagiInicial" styleClass="tituloCampos" value="  <<  " reRender="itens,paginaatual,painelPaginacaoManual" rendered="#{CadastroInicialControle.confPaginacao.apresentarPrimeiro}" actionListener="#{CadastroInicialControle.consultarPaginadoListener}">
											                            	<f:attribute name="pagNavegacao" value="pagInicial" />							                            	
											                            </a4j:commandLink>
											                            <a4j:commandLink id="pagiAnterior" styleClass="tituloCampos" value="  <  " reRender="itens,painelPaginacaoManual" rendered="#{CadastroInicialControle.confPaginacao.apresentarAnterior}" actionListener="#{CadastroInicialControle.consultarPaginadoListener}">
											                            	<f:attribute name="pagNavegacao" value="pagAnterior" />
											                            </a4j:commandLink>
											                            <h:outputText id="paginaAtual" styleClass="tituloCampos" value="#{msg_aplic.prt_msg_pagina} #{CadastroInicialControle.confPaginacao.paginaAtualDeTodas}" rendered="true"/>
											                            <a4j:commandLink id="pagiPosterior" styleClass="tituloCampos" value="  >  " reRender="itens,painelPaginacaoManual" rendered="#{CadastroInicialControle.confPaginacao.apresentarPosterior}" actionListener="#{CadastroInicialControle.consultarPaginadoListener}">
											                            	<f:attribute name="pagNavegacao" value="pagPosterior" />
											                            </a4j:commandLink>
											                            <a4j:commandLink id="pagiFinal" styleClass="tituloCampos" value="  >>  " reRender="itens,painelPaginacaoManual" rendered="#{CadastroInicialControle.confPaginacao.apresentarUltimo}" actionListener="#{CadastroInicialControle.consultarPaginadoListener}">
											                            	<f:attribute name="pagNavegacao" value="pagFinal" />
											                            </a4j:commandLink>
											                            
											                            <h:outputText id="totalItens" styleClass="tituloCampos" value=" [#{msg_aplic.prt_msg_itens} #{CadastroInicialControle.confPaginacao.numeroTotalItens}]" rendered="true"/>
										                            
										                            </h:panelGroup>
										                            
							                        			</a4j:outputPanel>


       </h:panelGrid>




       </h:panelGroup>
       </h:panelGrid>
		<h:panelGrid id="panelMensagem" columns="1" width="100%">
				<h:panelGrid columns="2" width="100%">
					<h:outputText id="msg" styleClass="mensagem"
						value="#{CadastroInicialControle.mensagem}" />
					<h:outputText styleClass="mensagemDetalhada"
						value="#{CadastroInicialControle.mensagemDetalhada}" />
						</h:panelGrid>
				
			</h:panelGrid>
			</h:panelGrid>
			<table width="100%" border="0" align="center" cellpadding="0"
				cellspacing="0">
				<tr>
					<td width="10"><img
						src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
						height="69"></td>
					<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
						<td align="center">
							<a4j:commandButton
								onclick="if(!validarBusca()){return false;};"
								action="#{CadastroInicialControle.consultaClienteInteressado}"
								reRender="panelCadastroSimplificadoCliente,panelDadosBasicos,panelMensagens,evt"
								value="#{msg_bt.btn_editar}"
								image="/imagens/botoesCE/buscar.png"
								title="#{msg.msg_editar_dados}" styleClass="botoes" />
						</td>
						<td align="center">		
								
								<a4j:commandLink
								rendered="#{CadastroInicialControle.ativarBotaoNovo && !CadastroInicialControle.alterarNomeInteressado}"
								action="#{CadastroInicialControle.fechaModal}"
								reRender="panelCadastroSimplificadoCliente, panelDadosBasicos,panelMensagens,evt">
								<h:graphicImage value="/imagens/botoesCE/btnNovo.png" style="border: 0px;"/>
								</a4j:commandLink>
								<a4j:commandButton
								rendered="#{CadastroInicialControle.ativarBotaoNovo && CadastroInicialControle.alterarNomeInteressado}"
								action="#{CadastroInicialControle.habilitaAlteracaoNome}"
								image="/imagens/botoesCE/alterar_nome.png"
								value="#{CElabels['menu.operacoesCE.alterarNomeInteressado']}"
								reRender="panelCadastroSimplificadoCliente, panelDadosBasicos,panelMensagens,evt"
								>
								
								</a4j:commandButton>
						</td>		
								

						</tr>
					</table>
					</td>
					<td width="10"><img
						src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
						height="69"></td>
				</tr>
			</table>
	</a4j:form>
	<script>
	function validarFonePesquisa(telefone)
    {
		limparMensagemBusca();
        if (telefone.value.match('^\\(?(\\d{2})\\)[- ]?$')){
	         return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{4})$')){
       	 return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{4})[- ]?(\\d{4})$')){
       	 return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{8})$')){
            return true;
        }
        if (telefone.value.match('^\\(?(\\d{2})\\)?[- ]?(\\d{9})$')){
            return true;
        }
        exibirMensagemBusca('<h:outputText value="Formato do telefone deve ser (XX), (XX)XXXX, (XX)XXXXXXXX ou (XX)XXXXXXXXX."/>');
        return false;
    }
	function exibirMensagemBusca(mensagem) {
		var divObgMensagem = document.getElementById('mensagemBusca');
		divObgMensagem.innerHTML = mensagem;
	}
	function validarBusca() {
		
		var nome = document.getElementById('formCadastroSimplificadoCliente:nomeCadastroSimplificadoCliente').value;
		var tel = document.getElementById('formCadastroSimplificadoCliente:telefoneConsulta').value;
		var cpf = document.getElementById('formCadastroSimplificadoCliente:cpf').value;
		var validade = true;
		
		
		if(nome == "" || nome == null ){
			if(tel == "" || tel == null){
				if (cpf == "" || cpf == null){
			        exibirMensagemBusca('<h:outputText value="#{CElabels[\'operacoes.consultaCliente\']}"/>');
				validade = false;
			}
			}
		}
		return validade;
	}
	function limparMensagemBusca() {
		var divObgMensagem = document.getElementById('mensagemBusca');
		divObgMensagem.innerHTML = "";
	}
	
	
	</script>
</rich:modalPanel>
