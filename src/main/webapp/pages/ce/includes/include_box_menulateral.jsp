<%@include file="../includes/include_imports.jsp" %>



<h:panelGroup layout="block" styleClass="menuLateral">
    <h:panelGroup layout="block" styleClass="grupoMenuLateral">

        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa fa-file-text-o"></i> Cadastros
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.operacoesCE.cadastroInicial']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{CadastroInicialControle.abrirCadastroInicialNovo}">
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Cadastros:Cadastro_Inicial"
                          title="Clique e saiba mais: Cadastro Inicial"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">

        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa fa-wrench"></i> Opera��es
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.operacoesCE.operacoes.simularOrcamento']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="SIMULAR_ORCAMENTO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Opera��es:Or�amento_Detalhado"
                          title="Clique e saiba mais: Simular Or�amento"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.operacoesCE.listaProspects']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="LISTA_PROSPECTS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Consultas:ListaProspects"
                          title="Clique e saiba mais: Lista de Prospects"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.operacoesCE.agendaVisita']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="AGENDA_VISISTA" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Opera��es:AgendaVisita"
                          title="Clique e saiba mais: Agenda Visita"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['operacoes.conversas']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CONVERSAS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Conversas:Conversas"
                          title="Clique e saiba mais: Conversas"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

    </h:panelGroup>

    <h:panelGroup layout="block" styleClass="grupoMenuLateral">

        <h:panelGroup layout="block" styleClass="grupoMenuTitulo">
            <i class="fa fa-search"></i> Consultas
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.operacoesCE.disponibilidade']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="AGENDA_EVENTOS" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Consultas:AgendadeEventos"
                          title="Clique e saiba mais: Agenda de Eventos"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.caixaAberto']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="CAIXA_EM_ABERTO_CE" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlBaseConhecimento}como-receber-uma-parcela-do-aluno-no-caixa-em-aberto/"
                          title="Clique e saiba mais: Caixa em Aberto"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="#{CElabels['menu.operacoesCE.pesquisa']}"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="PESQUISA_GERAL" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}Consultas:Pesquisa_Geral"
                          title="Clique e saiba mais: Pesquisa Geral"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

        <h:panelGroup layout="block" styleClass="grupoMenuItem">

            <a4j:commandLink value="Gest�o de cr�dito"
                             styleClass="titulo3 linkFuncionalidade"
                             action="#{FuncionalidadeControle.abrirComCasoNavegacao}"
                             actionListener="#{FuncionalidadeControle.prepararFuncionalidade}"
                             oncomplete="#{FuncionalidadeControle.abrirPopUp}">
                <f:attribute name="funcionalidade" value="GESTAO_CREDITO" />
            </a4j:commandLink>

            <h:outputLink styleClass="linkWiki"
                          value="#{SuperControle.urlWikiCE}"
                          title="Clique e saiba mais: Gest�o de Cr�dito"
                          target="_blank">
                <i class="fa-icon-question-sign" style="font-size: 18px"></i>
            </h:outputLink>
        </h:panelGroup>

    </h:panelGroup>

</h:panelGroup>
