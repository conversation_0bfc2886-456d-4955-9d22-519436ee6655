<%@include file="../includes/include_imports.jsp" %><table width="100%">
	<tr>
		<td>
		<table width="100%" border="0" align="center" cellpadding="0"
			cellspacing="0">
			<tr>
				<td width="10"><img
					src="${contexto}/imagens/agenda_imgs/menu_esq.jpg" width="10"
					height="69"></td>
				<td background="${contexto}/imagens/agenda_imgs/menu_fundo.jpg">
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td align="center">
							<a4j:commandLink id="estorno"
							action="#{CancelamentoEventoControle.confirmarEstornar}" oncomplete="#{CancelamentoEventoControle.msgAlert}"
							reRender="panelSenhaCancelamento">
							<h:graphicImage value="/imagens/botoesCE/estorno.png"
								style="border: 0px;"
								alt="Estorno"/>
							</a4j:commandLink></td>
							
						<td align="center"><a4j:commandLink id="desistencia"
															action="#{CancelamentoEventoControle.confirmarNaoEstornar}" oncomplete="#{CancelamentoEventoControle.msgAlert}"
							reRender="panelSenhaCancelamento">
							<h:graphicImage value="/imagens/bt_desistencia.jpg"
								style="border: 0px;"
								alt="Desist�ncia"/>
							</a4j:commandLink></td>
							
						<td align="center"><h:commandLink id="voltar"
							actionListener="#{CadastroInicialControle.selCadastroInicialListener}"
							action="#{CadastroInicialControle.abrirDetalhamento}">
							<h:graphicImage value="/imagens/bt_voltar.jpg"
								style="border: 0px;"
								alt="Voltar"/>
							<f:attribute name="codigoEventoInteresse"
								value="#{CancelamentoEventoControle.evento.codigo}" />
						</h:commandLink></td>

					</tr>
				</table>
				</td>
				<td width="10"><img
					src="${contexto}/imagens/agenda_imgs/menu_dir.jpg" width="10"
					height="69"></td>
			</tr>
		</table>

		</td>
	</tr>
<tr>
<td>
<h:panelGrid rowClasses="linhaImpar, linhaPar"
			columnClasses="classEsquerda, classDireita"
			width="100%" columns="2">
			<h:outputText value="#{CElabels['entidade.dataInteresse']}:" />
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos" id="dataInicio" 
					value="#{CancelamentoEventoControle.evento.dataFormatada}" />
				<div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:outputText value="#{CElabels['entidade.evento.nome']}:" />
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos"  
					value="#{CancelamentoEventoControle.evento.nomeEvento}" id="aaa"></h:outputText>
				<div id="divObg-dataInicio" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText value="#{CElabels['entidade.cliente.nome']}:" />
			</h:panelGroup>
			<h:panelGroup>
				<h:outputText styleClass="tituloCampos"  
					value="#{CancelamentoEventoControle.evento.interessado.nomeCliente}"
					id="nomeCliente"></h:outputText>
				<div id="divObg-nomeCliente" class="mensagemObrigatorio"></div>
			</h:panelGroup>
			</h:panelGrid>
</td>
</tr>
	<tr>
		<td>
		<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
			<tr>
				<td width="100%">
				<c:if test="${not empty CancelamentoEventoControle.parcelas }">
				<br/>
				<h:panelGroup>
					<img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
						<h:outputText value="#{CElabels['operacoes.parcelasRelacionadas']}:"/>
						<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
				</h:panelGroup>
				<br/>
				<rich:dataTable var="movParcelaVO" value="#{CancelamentoEventoControle.parcelas}" width="100%">
										
										<rich:column>
										<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.descricao']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.descricao}" />
													</center>
											</rich:column>


											<rich:column>
											<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.dataRegistro']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.aulaAvulsaDiariaVO.dataRegistro_Apresentar}" />
													</center>
											</rich:column>

											<rich:column>
											<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.vencimento']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.dataVencimento_Apresentar}" />
													</center>
											</rich:column>

											<rich:column>
												<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.valor']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.valorParcelaNumerico}">
													
												</h:outputText>
												</center>
											</rich:column>
											<rich:column>
												<f:facet name="header">
											<h:outputText value="#{CElabels['entidade.situacao']}" />
												</f:facet>
												<center>
												<h:outputText
													value="#{movParcelaVO.situacao_Apresentar}">
												</h:outputText>
												</center>
											</rich:column>
										</rich:dataTable>
				</c:if>
				<c:if test="${empty CancelamentoEventoControle.parcelas }">
				<br/>
				<h:panelGroup>
					<img src="${contexto}/images/arrow2.gif" width="16" height="16" style="vertical-align:middle;margin-right:6px;">
						<h:outputText value="#{CElabels['operacoes.naoExistemParcelas']}"/>
						<div class="sep" style="margin:4px 0 5px 0;"><img src="${contexto}/images/shim.gif"></div>
				</h:panelGroup>
				<br/>
				
				</c:if>
				</td>
			</tr>	
		</table>
		</td>
	</tr>
	<tr>
		<td>
	<h:panelGrid id="mensagens" columns="1" width="100%"
					>
					<h:panelGrid columns="3" width="100%">
						<h:panelGrid columns="1" width="100%">
							
								<h:outputText value=" " />
							
						</h:panelGrid>
						<h:panelGrid columns="1" width="100%">
							<h:outputText styleClass="mensagem"
								value="#{CancelamentoEventoControle.mensagem}" />
							<h:outputText styleClass="mensagemDetalhada"
								value="#{CancelamentoEventoControle.mensagemDetalhada}" />
						</h:panelGrid>
					</h:panelGrid>
				</h:panelGrid>
		</td>
	</tr>


</table>
